package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.AdderList
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.client.UpdaterList
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.HealthInstitutionNegotiationModel
import br.com.alice.data.layer.services.HealthInstitutionNegotiationModelDataService.FieldOptions
import br.com.alice.data.layer.services.HealthInstitutionNegotiationModelDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface HealthInstitutionNegotiationModelDataService : Service,
    Finder<FieldOptions, OrderingOptions, HealthInstitutionNegotiationModel>,
    Counter<FieldOptions, OrderingOptions, HealthInstitutionNegotiationModel>,
    Getter<HealthInstitutionNegotiationModel>,
    Adder<HealthInstitutionNegotiationModel>,
    AdderList<HealthInstitutionNegotiationModel>,
    Updater<HealthInstitutionNegotiationModel>,
    UpdaterList<HealthInstitutionNegotiationModel> {

    override val namespace: String
        get() = "eita"

    override val serviceName: String
        get() = "healthcare_institution_negotiation"

    class IdField : Field.UUIDField(HealthInstitutionNegotiationModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun isNotNul() = Predicate.isNotNull(this)
    }

    class CodeField : Field.TextField(HealthInstitutionNegotiationModel::code) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
        fun notInList(value: List<String>) = Predicate.notInList(this, value)
    }

    class TableTypeField : Field.TextField(HealthInstitutionNegotiationModel::tableType) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class DescriptionField : Field.TextField(HealthInstitutionNegotiationModel::description) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class HealthcareResourceIdField : Field.UUIDField(HealthInstitutionNegotiationModel::healthcareResourceId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ProviderUnitGroupIdField : Field.UUIDField(HealthInstitutionNegotiationModel::providerUnitGroupId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ValidAfterField : Field.DateField(HealthInstitutionNegotiationModel::validAfter) {
        fun less(value: LocalDate) = Predicate.less(this, value)
        fun lessEq(value: LocalDate) = Predicate.lessEq(this, value)
    }

    class ValidBeforeField : Field.DateField(HealthInstitutionNegotiationModel::validBefore) {
        fun greater(value: LocalDate) = Predicate.greater(this, value)
        fun greaterEq(value: LocalDate) = Predicate.greaterEq(this, value)
        fun isNull() = Predicate.isNull(this)
    }

    class UpdatedAt : Field.DateTimeField(HealthInstitutionNegotiationModel::updatedAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
    }

    class CreatedAt : Field.DateTimeField(HealthInstitutionNegotiationModel::createdAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
    }

    class SearchTokens : Field.TextField(HealthInstitutionNegotiationModel::searchTokens) {
        fun search(value: String) = Predicate.search(this, value)
    }

    class ExternalId : Field.TextField(HealthInstitutionNegotiationModel::externalId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class ActiveHealthcareResource : Field.BooleanField(HealthInstitutionNegotiationModel::activeHealthcareResource)

    class FieldOptions {
        val id = IdField()
        val code = CodeField()
        val tableType = TableTypeField()
        val healthcareResourceId = HealthcareResourceIdField()
        val providerUnitGroupId = ProviderUnitGroupIdField()
        val validAfter = ValidAfterField()
        val validBefore = ValidBeforeField()
        val searchTokens = SearchTokens()
        val activeHealthcareResource = ActiveHealthcareResource()
        val externalId = ExternalId()
    }

    class OrderingOptions {
        val updatedAt = UpdatedAt()
        val createdAt = CreatedAt()
        val description = DescriptionField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<HealthInstitutionNegotiationModel, Throwable>
    override suspend fun add(model: HealthInstitutionNegotiationModel): Result<HealthInstitutionNegotiationModel, Throwable>
    override suspend fun addList(models: List<HealthInstitutionNegotiationModel>): Result<List<HealthInstitutionNegotiationModel>, Throwable>
    override suspend fun update(model: HealthInstitutionNegotiationModel): Result<HealthInstitutionNegotiationModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<HealthInstitutionNegotiationModel>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun updateList(
        models: List<HealthInstitutionNegotiationModel>,
        returnOnFailure: Boolean
    ): Result<List<HealthInstitutionNegotiationModel>, Throwable>

}
