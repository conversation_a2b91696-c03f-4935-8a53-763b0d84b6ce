package br.com.alice.atlas.service

import br.com.alice.atlas.model.Accredited
import br.com.alice.atlas.model.AccreditedCategory
import br.com.alice.atlas.services.AccreditedNetworkFlagshipService
import br.com.alice.atlas.services.AccreditedService
import br.com.alice.atlas.services.FlagshipsAccreditedLogicService
import br.com.alice.atlas.services.FlagshipsAccreditedLogicServiceImpl
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.SiteAccreditedNetworkFlagship
import br.com.alice.data.layer.models.SiteAccreditedNetworkFlagshipData
import br.com.alice.data.layer.models.SiteAccreditedNetworkFlagshipTypes
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.isSuccess
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class FlagshipsAccreditedLogicServiceTest: MockedTestHelper() {
    private val accreditedNetworkFlagshipService: AccreditedNetworkFlagshipService = mockk()
    private val accreditedService: AccreditedService = mockk()

    private val flagshipsAccreditedLogicService: FlagshipsAccreditedLogicService = FlagshipsAccreditedLogicServiceImpl(
        accreditedNetworkFlagshipService,
        accreditedService,
    )
    private val placeId = "testPlaceId"
    private val providerUnitIdHospital = RangeUUID.generate()
    private val providerUnitIdLaboratory = RangeUUID.generate()
    private val flagships = SiteAccreditedNetworkFlagship(
        googlePlaceId = placeId,
        data = listOf(
            SiteAccreditedNetworkFlagshipData(
                providerUnitId = providerUnitIdHospital,
                type = SiteAccreditedNetworkFlagshipTypes.HOSPITAL,
            ),
            SiteAccreditedNetworkFlagshipData(
                providerUnitId = providerUnitIdLaboratory,
                type = SiteAccreditedNetworkFlagshipTypes.LABORATORY,
            )
        )
    )
    private val accredited = listOf(
        Accredited(
            referencedId = RangeUUID.generate(),
            name = "Hospital 2",
            id = RangeUUID.generate(),
            category = AccreditedCategory.HOSPITAL,
        ),
        Accredited(
            referencedId = RangeUUID.generate(),
            name = "Laboratory 2",
            id = RangeUUID.generate(),
            category = AccreditedCategory.LABORATORY,
        )
    )
    private val accreditedFlagship = listOf(
        Accredited(
            referencedId = providerUnitIdHospital,
            name = "Hospital",
            id = RangeUUID.generate(),
            category = AccreditedCategory.HOSPITAL,
        ),
        Accredited(
            referencedId = providerUnitIdLaboratory,
            name = "Laboratory",
            id = RangeUUID.generate(),
            category = AccreditedCategory.LABORATORY,
        )
    )

    @Test
    fun `#getFlagships with flagship service enabled and place id not null`() = runBlocking {
        val required = accreditedFlagship + accredited

        coEvery {
            accreditedNetworkFlagshipService.getFlagship(placeId)
        } returns flagships.success()
        coEvery {
            accreditedService.getByReferencedIds(any())
        } returns accreditedFlagship.success()

        val result = flagshipsAccreditedLogicService.getFlagships(
            accredited,
            placeId,
            true,
        )

        assertTrue(result.isSuccess())
        assertEquals(required, result.get())

        coVerifyOnce { accreditedNetworkFlagshipService.getFlagship(any()) }
        coVerifyOnce { accreditedService.getByReferencedIds(any()) }
    }

    @Test
    fun `#getFlagships with place id null and not default search`() = runBlocking {
        val result = flagshipsAccreditedLogicService.getFlagships(
            accredited,
            null,
        )

        assertTrue(result.isSuccess())
        assertEquals(accredited , result.get())

        coVerifyNone { accreditedNetworkFlagshipService.getFlagship(any()) }
        coVerifyNone { accreditedService.getByReferencedIds(any()) }
    }

    @Test
    fun `#getFlagships with flagship service throwing an error`() = runBlocking {
        coEvery {
            accreditedNetworkFlagshipService.getFlagship(placeId)
        } returns NotFoundException().failure()

        val result = flagshipsAccreditedLogicService.getFlagships(
            accredited,
            placeId,
            true,
        )

        assertTrue(result.isSuccess())
        assertEquals(accredited, result.get())

        coVerifyOnce { accreditedNetworkFlagshipService.getFlagship(any()) }
        coVerifyNone { accreditedService.getByReferencedIds(any()) }
    }
}
