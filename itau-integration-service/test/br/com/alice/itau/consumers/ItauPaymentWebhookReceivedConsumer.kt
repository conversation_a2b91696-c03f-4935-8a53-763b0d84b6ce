package br.com.alice.itau.consumers

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.itau.events.ItauBoletoPaymentWebhookReceivedEvent
import br.com.alice.itau.events.ItauBoletoPaymentWebhookReceivedEventPayload
import br.com.alice.itau.events.ItauPixPaymentWebhookReceivedEvent
import br.com.alice.itau.events.ItauPixPaymentWebhookReceivedEventPayload
import br.com.alice.itau.services.internal.ItauPaymentService
import br.com.alice.moneyin.event.AcquirerPaymentWebhookReceivedEvent
import com.github.kittinunf.result.failure
import com.google.gson.Gson
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class ItauPixWebhookReceivedConsumerTest : ConsumerTest() {

    private val kafkaProducerService: KafkaProducerService = mockk()
    private val itauPaymentService: ItauPaymentService = mockk()
    private val consumer = ItauPaymentWebhookReceivedConsumer(kafkaProducerService, itauPaymentService)

    private val pixPayload = ItauPixPaymentWebhookReceivedEventPayload(
        endToEndId = "E18236120202503291358s042c6d342d",
        pixId = "344e422b6c1e4e90b91fe37cf38189e8",
        amountPaid = "0.03",
        paidAt = "2025-03-29T13:58:12.095Z",
        pixKey = "34266553000102",
        responsibleInfo = "Alice",
    )

    private val boletoPayload = ItauBoletoPaymentWebhookReceivedEventPayload(
        boletoId = "2d2fce4a-4dd1-41bd-ad04-6d4ca6024a70",
        ourNumber = "00000034",
        amountPaid = "0.50",
        amountReceived = "0.50",
        paidAt = "2025-05-11",
        paidType = "06",
        personType = "F",
        cpf = "14337864601",
        cnpj = null,
        payerName = "Teste Integração Itau Teste Integração Itau",
        notificationDate = "2025-05-11",
        notificationTime = "18:56",
    )

    @Test
    fun `Consuming pix event - itauPayment not found`() = runBlocking {
        val event = ItauPixPaymentWebhookReceivedEvent(payload = pixPayload)

        coEvery { itauPaymentService.getByPixId(pixPayload.pixId) } returns NotFoundException().failure()

        consumer.emitAcquirerPaymentWebhookReceivedEventForPix(event)

        coVerifyOnce { itauPaymentService.getByPixId(any()) }
    }

    @Test
    fun `successfully consuming pix event`() = runBlocking {
        val event = ItauPixPaymentWebhookReceivedEvent(payload = pixPayload)
        val itauPayment = TestModelFactory.buildItauPayment()
        val updated = itauPayment.copy(paidResponse = Gson().toJson(event.payload))

        coEvery { itauPaymentService.getByPixId(pixPayload.pixId) } returns itauPayment
        coEvery { itauPaymentService.update(updated) } returns updated
        coEvery {
            kafkaProducerService.produce(match {
                it.name == AcquirerPaymentWebhookReceivedEvent.name
            })
        } returns mockk()

        consumer.emitAcquirerPaymentWebhookReceivedEventForPix(event)

        coVerifyOnce { itauPaymentService.getByPixId(any()) }
        coVerifyOnce { itauPaymentService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `Consuming boleto event - itauPayment not found`() = runBlocking {
        val event = ItauBoletoPaymentWebhookReceivedEvent(payload = boletoPayload)

        coEvery { itauPaymentService.getByBoletoId(boletoPayload.boletoId) } returns NotFoundException().failure()

        consumer.emitAcquirerPaymentWebhookReceivedEventForBoleto(event)

        coVerifyOnce { itauPaymentService.getByBoletoId(any()) }
    }

    @Test
    fun `successfully consuming boleto event`() = runBlocking {
        val event = ItauBoletoPaymentWebhookReceivedEvent(payload = boletoPayload)
        val itauPayment = TestModelFactory.buildItauPayment()
        val updated = itauPayment.copy(paidResponse = Gson().toJson(event.payload))

        coEvery { itauPaymentService.getByBoletoId(boletoPayload.boletoId) } returns itauPayment
        coEvery { itauPaymentService.update(updated) } returns updated
        coEvery {
            kafkaProducerService.produce(match {
                it.name == AcquirerPaymentWebhookReceivedEvent.name
            })
        } returns mockk()

        consumer.emitAcquirerPaymentWebhookReceivedEventForBoleto(event)

        coVerifyOnce { itauPaymentService.getByBoletoId(any()) }
        coVerifyOnce { itauPaymentService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

}
