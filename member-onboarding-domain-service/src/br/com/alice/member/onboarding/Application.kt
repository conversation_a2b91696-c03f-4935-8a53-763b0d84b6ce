package br.com.alice.member.onboarding

import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.business.ioc.BusinessDomainClientModule
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.data.layer.MEMBER_ONBOARDING_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.healthlogic.ioc.HealthLogicDomainClientModule
import br.com.alice.healthplan.ioc.HealthPlanDomainClientModule
import br.com.alice.marauders.map.ioc.MaraudersMapDomainClientModule
import br.com.alice.member.onboarding.client.MemberOnboardingActionService
import br.com.alice.member.onboarding.client.MemberOnboardingCheckpointService
import br.com.alice.member.onboarding.client.MemberOnboardingEligibilityService
import br.com.alice.member.onboarding.client.MemberOnboardingProgressService
import br.com.alice.member.onboarding.client.MemberOnboardingService
import br.com.alice.member.onboarding.client.MemberOnboardingStepService
import br.com.alice.member.onboarding.client.MemberOnboardingStratificationService
import br.com.alice.member.onboarding.client.MemberOnboardingTemplateService
import br.com.alice.member.onboarding.controllers.MemberOnboardingBackfillController
import br.com.alice.member.onboarding.ioc.DataLayerServiceModule
import br.com.alice.member.onboarding.ioc.ServiceModule
import br.com.alice.member.onboarding.routes.apiRoutes
import br.com.alice.member.onboarding.routes.kafkaRoutes
import br.com.alice.member.onboarding.routes.recurringRoutes
import br.com.alice.member.onboarding.services.MemberOnboardingActionServiceImpl
import br.com.alice.member.onboarding.services.MemberOnboardingCheckpointServiceImpl
import br.com.alice.member.onboarding.services.MemberOnboardingEligibilityServiceImpl
import br.com.alice.member.onboarding.services.MemberOnboardingProgressServiceImpl
import br.com.alice.member.onboarding.services.MemberOnboardingServiceImpl
import br.com.alice.member.onboarding.services.MemberOnboardingStepServiceImpl
import br.com.alice.member.onboarding.services.MemberOnboardingStratificationServiceImpl
import br.com.alice.member.onboarding.services.MemberOnboardingTemplateServiceImpl
import br.com.alice.member.onboarding.services.internal.AliceInfoABTestService
import br.com.alice.member.onboarding.services.internal.MemberOnboardingCreationService
import br.com.alice.membership.ioc.MembershipClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.sales_channel.ioc.SalesChannelDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import com.typesafe.config.ConfigFactory
import io.ktor.server.application.Application
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    val dependencyInjectionModules = listOf(
        DataLayerServiceModule,
        FeatureConfigDomainClientModule,
        HealthPlanDomainClientModule,
        KafkaProducerModule,
        HealthLogicDomainClientModule,
        MaraudersMapDomainClientModule,
        MembershipClientModule,
        PersonDomainClientModule,
        StaffDomainClientModule,
        ServiceModule,
        BusinessDomainClientModule,
        SalesChannelDomainClientModule,
        ClinicalAccountDomainClientModule,

        module(createdAtStart = true) {
            // Configuration
            single { config }

            //Load services
            loadServiceServers("br.com.alice.member.onboarding.services")

            // Exposed Services
            single<MemberOnboardingTemplateService> { MemberOnboardingTemplateServiceImpl(get(), get(), get(), get()) }
            single<MemberOnboardingStepService> { MemberOnboardingStepServiceImpl(get()) }
            single<MemberOnboardingStratificationService> {
                MemberOnboardingStratificationServiceImpl(
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get()
                )
            }
            single<MemberOnboardingActionService> { MemberOnboardingActionServiceImpl(get()) }
            single<MemberOnboardingEligibilityService> { MemberOnboardingEligibilityServiceImpl(get()) }
            single<MemberOnboardingCheckpointService> { MemberOnboardingCheckpointServiceImpl(get()) }
            single<MemberOnboardingProgressService> {
                MemberOnboardingProgressServiceImpl(
                    get(),
                    get(),
                    get(),
                    get(),
                    get()
                )
            }
            single<MemberOnboardingService> { MemberOnboardingServiceImpl(get(), get(), get(), get(), get(), get()) }

            // Internal Services
            single { AliceInfoABTestService(get(), get()) }
            single { MemberOnboardingCreationService(get(), get(), get(), get(), get(), get(), get(), get()) }

            //controllers
            single { MemberOnboardingBackfillController(get()) }

            // Kafka Consumers
        }
    )
}

@JvmOverloads
fun Application.module(dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules) {
    setupDomainService(dependencyInjectionModules) {
        authenticationBootstrap()

        routing {
            application.attributes.put(PolicyRootServiceKey, MEMBER_ONBOARDING_ROOT_SERVICE_NAME)
            recurringRoutes()
            apiRoutes()
        }

        kafkaConsumer(startRoutesSync = true) {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }

        featureConfigBootstrap(
            FeatureNamespace.ALICE_APP,
            FeatureNamespace.ONBOARDING,
            FeatureNamespace.MEMBERSHIP,
            FeatureNamespace.MEMBER_ONBOARDING
        )
    }

}
