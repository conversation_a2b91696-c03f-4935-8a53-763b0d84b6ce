package br.com.alice.api.backoffice.transfers.execIndicator

import br.com.alice.api.backoffice.transfers.FriendlyEnumResponse
import br.com.alice.common.core.Status
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePricingStatus
import br.com.alice.exec.indicator.models.SecondaryResourcesTransport
import java.time.LocalDate
import java.util.UUID

data class HealthSpecialistResourceBundleCreateRequest(
    val primaryTuss: String,
    val secondaryResources: List<SecondaryResourcesTransport>? = emptyList(),
    val executionAmount: Int = 1,
    val executionEnvironment: HealthSpecialistProcedureExecutionEnvironment = HealthSpecialistProcedureExecutionEnvironment.DOES_NOT_APPLY,
    val aliceDescription: String,
    val status: Status,
    val serviceType: HealthSpecialistResourceBundleServiceType,
    var aliceCode: String? = null,
) {
    init {
        aliceCode = aliceCode?.takeIf { it.isNotBlank() }
    }
}

data class SecondaryResourcesResponseTransport(
    val id: UUID,
    val code: String,
    val tableType: String,
    val tussCode: String,
    val description: String,
)

data class HealthSpecialistResourceBundleResponse(
    val id: UUID,
    val primaryTuss: String,
    val secondaryResources: List<SecondaryResourcesResponseTransport>,
    val executionAmount: Int,
    val executionEnvironment: HealthSpecialistProcedureExecutionEnvironment,
    val aliceDescription: String,
    val aliceCode: String,
    val status: Status,
    val serviceType: HealthSpecialistResourceBundleServiceType,
)

data class HealthSpecialistResourceBundleIndexResponse(
    val id: UUID,
    val aliceCode: String,
    val primaryTuss: String,
    val aliceDescription: String,
    var type: FriendlyEnumResponse<HealthSpecialistResourceBundleType>,
    val serviceType: FriendlyEnumResponse<HealthSpecialistResourceBundleServiceType>,
    val status: Status,
    val pricingStatus: FriendlyEnumResponse<HealthSpecialistResourceBundlePricingStatus>,
    val specialtiesText: String,
)

enum class HealthSpecialistResourceBundleType(val description: String) {
    BUNDLE("Pacote"),
    SINGLE("Unitário")
}

data class HealthSpecialistResourceBundleUpdateRequest(
    val secondaryResources: List<SecondaryResourcesTransport>? = emptyList(),
    val executionAmount: Int,
    val executionEnvironment: HealthSpecialistProcedureExecutionEnvironment,
    val aliceDescription: String,
    val status: Status,
    val serviceType: HealthSpecialistResourceBundleServiceType,
)

data class ResourceBundleSpecialtyBffResponse(
    val id: UUID,
    val name: String,
    val isTherapy: Boolean,
    val pricingStatus: FriendlyEnumResponse<HealthSpecialistResourceBundlePricingStatus>,
    val currentBeginAt: LocalDate?,
    val currentEndAt: LocalDate?,
    val hasScheduledPriceChange: Boolean,
    val medicalSpecialtyId: UUID,
)
