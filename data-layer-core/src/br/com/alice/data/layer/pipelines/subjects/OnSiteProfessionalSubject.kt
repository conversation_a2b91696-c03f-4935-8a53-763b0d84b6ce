package br.com.alice.data.layer.pipelines.subjects

import br.com.alice.common.core.Role
import br.com.alice.data.layer.models.StaffModel
import java.util.UUID

open class OnSiteProfessionalSubject(
    id: UUID,
    role: Role,
) : HealthProfessionalSubject(
    id = id,
    role = role,
    memberInPortfolio = true
) {
    constructor(staff: StaffModel) : this(
        id = staff.id,
        role = staff.role
    )
}
