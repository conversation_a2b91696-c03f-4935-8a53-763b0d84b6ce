package br.com.alice.nullvs.services.internals

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.redis.GenericCache
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.nullvs.exceptions.DependsOnModelException
import br.com.alice.nullvs.exceptions.ManySubcontractsToContractException
import br.com.alice.nullvs.exceptions.NoSubcontractForCompanyException
import br.com.alice.nullvs.exceptions.NullTotvsContractException
import br.com.alice.nullvs.exceptions.NullTotvsSubContractException
import br.com.alice.nullvs.logics.CacheHelper
import br.com.alice.nullvs.models.company.CompanyInfo
import io.mockk.Called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import kotlin.reflect.KClass
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CompanyInfoCacheServiceTest {
    private val cache: GenericCache = mockk()
    private val companyContractService: CompanyContractService = mockk()
    private val companyService: CompanyService = mockk()
    private val companySubContractService: CompanySubContractService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()

    private val service = CompanyInfoCacheService(
        cache,
        companyContractService,
        companyService,
        companySubContractService,
        beneficiaryService
    )

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    private fun withValidCache(
        key: String,
        value: CompanyInfo,
        expirationTime: Long = CacheHelper.FIVE_MINUTES_IN_SECONDS,
        block: suspend () -> Unit
    ) = runBlocking {
        coEvery { cache.get(key, CompanyInfo::class, any(), expirationTime, any()) } returns value

        block()
    }

    private fun withInvalidCache(
        key: String,
        expirationTime: Long = CacheHelper.FIVE_MINUTES_IN_SECONDS,
        type: KClass<*> = CompanyInfo::class,
        block: suspend () -> Unit
    ) = runBlocking<Unit> {
        coEvery { cache.get(key, type, any(), expirationTime, any()) } coAnswers {
            arg<suspend () -> CompanyInfo>(4).invoke()
        }

        block()
    }

    private val contract = TestModelFactory.buildCompanyContract(externalId = "00001")
    private val company = TestModelFactory.buildCompany(contractIds = listOf(contract.id))
    private val subContract =
        TestModelFactory.buildCompanySubContract(externalId = "0001", contractId = contract.id, companyId = company.id)
    private val beneficiary =
        TestModelFactory.buildBeneficiary(companyId = company.id, companySubContractId = subContract.id)

    val cacheKey = CacheHelper.getCompanyInfoBySubContractIdKey(subContract.id)

    @Test
    fun `should get the company info`() =
        withInvalidCache(cacheKey) {
            coEvery { companyService.get(company.id) } returns company
            coEvery { companySubContractService.get(subContract.id) } returns subContract
            coEvery { companyContractService.get(contract.id) } returns contract

            val expected = CompanyInfo(
                companyId = company.id,
                cnpj = company.cnpj,
                contractId = contract.id,
                contractNumber = contract.externalId,
                subContractId = subContract.id,
                subcontractNumber = subContract.externalId,
                hasCompanyProductPriceListing = false,
            )

            val result = service.getByBeneficiary(beneficiary)

            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce {
                cache.get(cacheKey, CompanyInfo::class, any(), CacheHelper.FIVE_MINUTES_IN_SECONDS, any())
                companyService.get(company.id)
                companySubContractService.get(subContract.id)
                companyContractService.get(contract.id)
            }

            coVerifyNone { beneficiaryService wasNot Called }
        }

    @Test
    fun `should get the company info by cache`() =
        withValidCache(
            cacheKey, CompanyInfo(
                companyId = company.id,
                cnpj = company.cnpj,
                contractNumber = contract.externalId,
                subcontractNumber = subContract.externalId,
                hasCompanyProductPriceListing = false,
            )
        ) {
            val expected = CompanyInfo(
                companyId = company.id,
                cnpj = company.cnpj,
                contractNumber = contract.externalId,
                subcontractNumber = subContract.externalId,
                hasCompanyProductPriceListing = false,
            )

            val result = service.getByBeneficiary(beneficiary)

            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce {
                cache.get(cacheKey, CompanyInfo::class, any(), CacheHelper.FIVE_MINUTES_IN_SECONDS, any())
            }

            coVerifyNone {
                companyService.get(any())
                companySubContractService.get(any())
                companyContractService.get(any())
                beneficiaryService wasNot Called
            }
        }

    @Test
    fun `should try to infer the subcontract to the beneficiary when it does not belong to any`() = runBlocking<Unit> {
        val beneficiary = beneficiary.copy(companySubContractId = null)
        val beneficiaryUpdated = beneficiary.copy(companySubContractId = subContract.id)

        coEvery { companyService.get(company.id) } returns company
        coEvery { companySubContractService.findByCompanyId(company.id) } returns listOf(subContract)
        coEvery { companyContractService.get(contract.id) } returns contract
        coEvery { beneficiaryService.update(beneficiaryUpdated) } returns beneficiary

        val expected = CompanyInfo(
            companyId = company.id,
            cnpj = company.cnpj,
            contractId = contract.id,
            contractNumber = contract.externalId,
            subContractId = subContract.id,
            subcontractNumber = subContract.externalId,
            hasCompanyProductPriceListing = false,
        )

        val result = service.getByBeneficiary(beneficiary)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce {
            companyService.get(company.id)
            companySubContractService.findByCompanyId(company.id)
            companyContractService.get(contract.id)
            beneficiaryService.update(beneficiaryUpdated)
        }

        coVerifyNone {
            companySubContractService.get(any())
            cache wasNot Called
        }
    }

    @Test
    fun `should throw an exception when trying to infer the subcontract to the beneficiary but no one is found`() =
        runBlocking<Unit> {
            val beneficiary = beneficiary.copy(companySubContractId = null)
            val beneficiaryUpdated = beneficiary.copy(companySubContractId = subContract.id)

            coEvery { companyService.get(company.id) } returns company
            coEvery { companySubContractService.findByCompanyId(company.id) } returns emptyList()
            coEvery { companyContractService.get(contract.id) } returns contract
            coEvery { beneficiaryService.update(beneficiaryUpdated) } returns beneficiary

            val result = service.getByBeneficiary(beneficiary)

            assertThat(result).isFailureOfType(NoSubcontractForCompanyException::class)

            coVerifyOnce {
                companyService.get(company.id)
                companySubContractService.findByCompanyId(company.id)
            }

            coVerifyNone {
                companySubContractService.get(any())
                companyContractService wasNot Called
                beneficiaryService wasNot Called
                cache wasNot Called
            }
        }

    @Test
    fun `should throw an exception when trying to infer the subcontract to the beneficiary but many of them is found`() =
        runBlocking<Unit> {
            val beneficiary = beneficiary.copy(companySubContractId = null)
            val beneficiaryUpdated = beneficiary.copy(companySubContractId = subContract.id)

            coEvery { companyService.get(company.id) } returns company
            coEvery { companySubContractService.findByCompanyId(company.id) } returns listOf(
                subContract,
                TestModelFactory.buildCompanySubContract(),
            )
            coEvery { companyContractService.get(contract.id) } returns contract
            coEvery { beneficiaryService.update(beneficiaryUpdated) } returns beneficiary

            val result = service.getByBeneficiary(beneficiary)

            assertThat(result).isFailureOfType(ManySubcontractsToContractException::class)

            coVerifyOnce {
                companyService.get(company.id)
                companySubContractService.findByCompanyId(company.id)
            }

            coVerifyNone {
                companySubContractService.get(any())
                companyContractService wasNot Called
                beneficiaryService wasNot Called
                cache wasNot Called
            }
        }

    @Test
    fun `should throw an exception when the contract number is null`() =
        withValidCache(
            cacheKey, CompanyInfo(
                companyId = company.id,
                cnpj = company.cnpj,
                contractNumber = null,
                subcontractNumber = null,
                hasCompanyProductPriceListing = false,
            )
        ) {
            val result = service.getByBeneficiary(beneficiary)

            assertThat(result).isFailureOfType(NullTotvsContractException::class)

            coVerifyOnce {
                cache.get(cacheKey, CompanyInfo::class, any(), CacheHelper.FIVE_MINUTES_IN_SECONDS, any())
            }

            coVerifyNone { beneficiaryService wasNot Called }
        }

    @Test
    fun `should throw an exception when the subcontract number is null`() =
        withValidCache(
            cacheKey, CompanyInfo(
                companyId = company.id,
                cnpj = company.cnpj,
                contractNumber = "00001",
                subcontractNumber = null,
                hasCompanyProductPriceListing = false,
            )
        ) {
            val result = service.getByBeneficiary(beneficiary)

            assertThat(result).isFailureOfType(NullTotvsSubContractException::class)

            coVerifyOnce {
                cache.get(cacheKey, CompanyInfo::class, any(), CacheHelper.FIVE_MINUTES_IN_SECONDS, any())
            }

            coVerifyNone { beneficiaryService wasNot Called }
        }

    @Test
    fun `should throw an exception when the contract number is null but the contract id is not`() =
        withInvalidCache(cacheKey) {
            val contract = contract.copy(externalId = null)

            coEvery { companyService.get(company.id) } returns company
            coEvery { companySubContractService.get(subContract.id) } returns subContract
            coEvery { companyContractService.get(contract.id) } returns contract

            val result = service.getByBeneficiary(beneficiary)

            assertThat(result).isFailureOfType(DependsOnModelException::class)

            coVerifyOnce {
                companyService.get(company.id)
                companySubContractService.get(subContract.id)
                companyContractService.get(contract.id)
                cache.get(cacheKey, CompanyInfo::class, any(), CacheHelper.FIVE_MINUTES_IN_SECONDS, any())
            }

            coVerifyNone { beneficiaryService wasNot Called }
        }

    @Test
    fun `should throw an exception when the subcontract number is null but the subcontract id is not`() =
        withInvalidCache(cacheKey) {
            val subContract = subContract.copy(externalId = null)

            coEvery { companyService.get(company.id) } returns company
            coEvery { companySubContractService.get(subContract.id) } returns subContract
            coEvery { companyContractService.get(contract.id) } returns contract

            val result = service.getByBeneficiary(beneficiary)

            assertThat(result).isFailureOfType(DependsOnModelException::class)

            coVerifyOnce {
                cache.get(cacheKey, CompanyInfo::class, any(), CacheHelper.FIVE_MINUTES_IN_SECONDS, any())
                companyService.get(company.id)
                companySubContractService.get(subContract.id)
                companyContractService.get(contract.id)
            }

            coVerifyNone { beneficiaryService wasNot Called }
        }

    @Nested
    inner class Invalidate {
        @Test
        fun `should invalidate the cache`() = runBlocking {

            coEvery {
                cache.invalidateKey(CacheHelper.getCompanyInfoBySubContractIdKey(subContract.id))
            } returns 1

            val result = service.invalidate(subContract.id)

            assertThat(result).isSuccessWithData(1)

            coVerifyOnce {
                cache.invalidateKey(CacheHelper.getCompanyInfoBySubContractIdKey(subContract.id))
            }
        }
    }
}
