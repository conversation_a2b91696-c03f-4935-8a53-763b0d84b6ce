package br.com.alice.data.layer.policies

import br.com.alice.data.layer.HEALTH_CARE_OPS_ENVIRONMENT_SUBSCRIBERS
import br.com.alice.data.layer.HEALTH_CARE_OPS_ROOT_SERVICE_NAME
import br.com.alice.data.layer.HEALTH_CARE_OPS_WEBHOOKS_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.AppointmentScheduleType.HEALTH_DECLARATION
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.policies.features.*
import br.com.alice.data.layer.subjects.Unauthenticated

val healthCareOpsPolicySet = policySet {

    match("at health-care-ops-subscribers", { rootService.name == HEALTH_CARE_OPS_ENVIRONMENT_SUBSCRIBERS }) {
        match("can view", { action is View }) {
            match("any PersonModel") { resource is PersonModel }
        }
        match("can view and create", { action is View || action is Create }) {
            match("any EmailCommunicationModel") { resource is EmailCommunicationModel }
        }
    }

    match("at health-care-ops", { rootService.name == HEALTH_CARE_OPS_ROOT_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            match("can view", { action is View }) {
                match("StaffModel attempting to login with") { staffAttemptingToLoginWith() }

                match("an Lead with email") {
                    subject is Unauthenticated &&
                            resource is Lead &&
                            resource.email == subject.key
                }

                match("a PersonModel with nationalId") {
                    subject is Unauthenticated &&
                            resource is PersonModel &&
                            resource.nationalId == subject.key
                }
            }

            match("can update and create", { action is Update || action is Create }) {
                match("any Lead") { resource is Lead }
            }
        }

        match("B2B - Ops or Ops Role", { subject is StaffModel && (subject.isB2BOps() || subject.isOps()) }) {
            allows(PersonContractualRiskModel::class, View, Count)
            includes(viewHealthCareOpsHomePage)
            includes(viewNullvsIntegrationLog)
            includes(viewHealthCareOpsProfilePage)
            includes(viewAndUpdatePersonPreferences)
            includes(viewAndUpdateMember)
            includes(viewMemberProductChangeSchedule)
            includes(updateHealthCareOpsInsurancePortability)
            includes(createAndUpdateHealthCareOpsContract)
            includes(canChangeProduct)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityHealthInsurance)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityRequest)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityRequestFile)
            includes(updateHealthCareOpsPersonInfo)
            includes(processHealthCareOpsBackgroundCheck)
            includes(viewAndUpdateBeneficiary)
            includes(generateHealthCareOpsAccessCode)
            includes(updateHealthCareOpsMemberApp)
            includes(cancelHealthCareOpsMember)
            includes(healthCareOpsAnonymizeLead)
            includes(viewInvoicePayments)
            includes(createInvoicePayments)
            includes(viewAndUpdateHealthCareOpsPriceListing)
            includes(deleteCompanyProductPriceListing)
            includes(viewHealthCareOpsLeadPage)
            includes(viewHealthCareOpsSuperAppPage)
            includes(viewUpdateAndCreatePersonBillingAccountableParty)
            includes(viewAnyMemberDocuments)
            includes(viewAndUpdateAnyContractFiles)
            includes(viewAndUpdateAnyInsurancePortabilityFile)
            includes(viewCreateUpdateAndDeleteLegalGuardianAssociation)
            includes(createViewUpdateCassiMember)
            includes(viewAndUpdateCompany)
            includes(viewAndUpdateCompanyStaff)
            includes(viewAndUpdateOngoingCompanyDeal)
            includes(viewSalesFirm)
            includes(canReactivateMember)
            includes(viewBeneficiaryCompiledView)
            includes(viewAndUpdateMemberContract)
            includes(viewCreateAndUpdateMemberTelegramTracking)
            includes(viewUpdateAndCreateBillingAccountableParty)
            includes(reactivateMember)
        }

        match("Ops Role", { subject is StaffModel && subject.isOps() }) {
            match("can view", { action is View }) {
                match("any StaffModel") { resource is StaffModel }
                match("any Health Declaration") { resource is HealthDeclaration }
                match("any Product") { resource is ProductModel }
                match("any Product Bundle") { resource is ProductBundleModel }
                match("any Provider") { resource is ProviderModel }
                match("any DeviceModel") { resource is DeviceModel }
            }
            match("can update and create", { action is Update || action is Create }) {
                match("any MemberModel Registration") { resource is MemberRegistrationModel }
            }

            match(
                "can view and update and create",
                { action is View || action is Count || action is Update || action is Create }) {
                match("any PersonModel Onboarding") { resource is PersonOnboardingModel }
                match("any PersonModel Registration") { resource is PersonRegistrationModel }
                match("any MemberModel") { resource is MemberModel }
                match("any PersonModel") { resource is PersonModel }
                match("any Lead") { resource is Lead }
                match("any PersonModel Login") { resource is PersonLoginModel }
                match("any Onboarding Contract") { resource is OnboardingContractModel }
                match("any Onboarding Background Check") { resource is OnboardingBackgroundCheckModel }
                match("any Health Declaration Schedule") { resource is AppointmentScheduleModel && resource.type == HEALTH_DECLARATION }
                match("any Product Order") { resource is ProductOrderModel }
                match("any PromoCodeModel") { resource is PromoCodeModel }
                match("any Insurance Portability Request") { resource is InsurancePortabilityRequestModel }
                match("any Insurance Portability Request File") { resourceIs(InsurancePortabilityRequestFileModel::class) }
                match("any Person Preferences") { resource is PersonPreferencesModel }
                match("any Product PriceListing") { resource is ProductPriceListingModel }
                match("any PriceListing") { resource is PriceListingModel }
                match("any MemberProductPrice") { resource is MemberProductPriceModel }
                match("any BillingAccountableParty") { resource is BillingAccountablePartyModel }
                match("any PersonBillingAccountableParty") { resource is PersonBillingAccountablePartyModel }
                match("any LegalGuardianInfoTemp") { resource is LegalGuardianInfoTempModel }
                match("any LegalGuardianAssociation") { resource is LegalGuardianAssociationModel }
            }

            match("can delete", { action is Delete }) {
                match("any PriceListing") { resourceIs(PriceListingModel::class) }
            }

            match("can count", { action is Count }) {
                match("any Product PriceListing") { resourceIs(ProductPriceListingModel::class) }
                match("any PersonModel") { resourceIs(PersonModel::class) }
            }

            includes(changeUserProduct)
            includes(viewNullvsIntegrationLog)
            includes(viewMemberProductChangeSchedule)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityHealthInsurance)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityRequest)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityRequestFile)
            includes(viewCreateUpdateAndDeleteLegalGuardianAssociation)
            includes(createRequestChangeContactInfo)
            includes(createViewUpdateCassiMember)
            includes(viewInvoicePayments)
            includes(createInvoicePayments)
        }

        match("Costumer Experience OPs", { subject is StaffModel && subject.isCXOps() }) {
            includes(viewMemberProductChangeSchedule)
            includes(viewInvoicePayments)

            match("can view", { action is View }) {
                match("any StaffModel") { resource is StaffModel }
                match("any Product") { resource is ProductModel }
                match("any Product Bundle") { resource is ProductBundleModel }
                match("any Provider") { resource is ProviderModel }
                match("any Product Order") { resource is ProductOrderModel }
                match("any PersonModel") { resource is PersonModel }
                match("any DeviceModel") { resource is DeviceModel }
                match("any PersonModel Onboarding") { resource is PersonOnboardingModel }
                match("any PersonModel Registration") { resource is PersonRegistrationModel }
                match("any MemberModel") { resource is MemberModel }
                match("any PersonModel") { resource is PersonModel }
                match("any Lead") { resource is Lead }
                match("any PersonModel Login") { resource is PersonLoginModel }
                match("any Health Declaration Schedule") { resource is AppointmentScheduleModel && resource.type == HEALTH_DECLARATION }
                match("any Product PriceListing") { resource is ProductPriceListingModel }
                match("any PriceListing") { resource is PriceListingModel }
                match("any MemberProductPriceModel") { resource is MemberProductPriceModel }
                match("any Health Declaration") { resource is HealthDeclaration }
            }
        }

        match(
            "Navigator or Chief Digital Care Nurse or Quality Nurse",
            { subject is StaffModel && (subject.isNavigator() || subject.isChiefDigitalCareNurse() || subject.isQualityNurse()) }) {
            match("can view", { action is View }) {
                match("any Health Declaration Schedule") {
                    resource is AppointmentScheduleModel && resource.type == HEALTH_DECLARATION
                }
            }
            allows(AppointmentScheduleModel::class, View, Count, Create, Update)
            allows(BeneficiaryModel::class, View, Count)
            allows(BeneficiaryCompiledViewModel::class, View, Count)
            allows(BillingAccountablePartyModel::class, View, Count, Create, Update)
            allows(BolepixPaymentDetailModel::class, View, Count, Create, Update)
            allows(BoletoPaymentDetailModel::class, View, Count, Create, Update)
            allows(CassiMemberModel::class, View, Count, Create, Update)
            allows(CompanyModel::class, View, Count)
            allows(CompanyActivationFilesModel::class, View, Count)
            allows(CompanyContractModel::class, View, Count)
            allows(CompanySubContractModel::class, View, Count)
            allows(CompanyProductPriceListingModel::class, View, Count, Create, Update)
            allows(DeviceModel::class, View, Count)
            allows(HealthDeclaration::class, View, Count, Create, Update)
            allows(InsurancePortabilityHealthInsuranceModel::class, View, Count, Create, Update, Delete)
            allows(InsurancePortabilityRequestModel::class, View, Count, Create, Update, Delete)
            allows(InsurancePortabilityRequestFileModel::class, View, Count, Create, Update, Delete)
            allows(InvoiceItemModel::class, View, Count, Create, Update)
            allows(InvoiceLiquidationModel::class, View, Count, Create, Update)
            allows(InvoicePaymentModel::class, View, Count, Create, Update)
            allows(ResourceSignTokenModel::class, View, Count, Create, Update)
            allows(CancelPaymentOnAcquirerScheduleModel::class, View, Count, Create, Update)
            allows(Lead::class, View, Count, Create, Update)
            allows(LegalGuardianAssociationModel::class, View, Count, Create, Update, Delete)
            allows(LegalGuardianInfoTempModel::class, View, Count, Create, Update, Delete)
            allows(MemberModel::class, View, Count, Create, Update)
            allows(MemberLifeCycleEventsModel::class, View, Count, Create, Update)
            allows(MemberInvoiceModel::class, View, Count, Create, Update)
            allows(MemberInvoiceGroupModel::class, View, Count, Create, Update)
            allows(MemberProductChangeScheduleModel::class, View, Count)
            allows(PreActivationPaymentModel::class, View, Count, Create, Update)
            allows(MemberProductPriceModel::class, View, Count)
            allows(MemberProductPriceAdjustmentModel::class, View, Count)
            allows(MemberRegistration::class, View, Count, Create, Update)
            allows(OnboardingBackgroundCheckModel::class, View, Count)
            allows(OnboardingContractModel::class, View, Count)
            allows(OngoingCompanyDeal::class, View, Count)
            allows(PaymentDetailModel::class, View, Count, Create, Update)
            allows(PersonModel::class, View, Count, Create, Update)
            allows(PersonBillingAccountablePartyModel::class, View, Count, Create, Update)
            allows(PersonLoginModel::class, View, Count, Create, Update)
            allows(PersonOnboardingModel::class, View, Count, Create, Update)
            allows(PersonPreferencesModel::class, View, Count, Create, Update)
            allows(PersonRegistrationModel::class, View, Count, Create, Update)
            allows(PixPaymentDetailModel::class, View, Count, Create, Update)
            allows(PriceListingModel::class, View, Count, Create, Update)
            allows(ProductModel::class, View, Count, Create, Update)
            allows(ProductBundleModel::class, View, Count)
            allows(ProductOrderModel::class, View, Count, Create, Update)
            allows(ProductPriceAdjustmentModel::class, View, Count, Create, Update)
            allows(ProductPriceListingModel::class, View, Count, Create, Update)
            allows(ProviderModel::class, View, Count)
            allows(GenericFileVault::class, View, Count)
            allows(StaffModel::class, View, Count)
            allows(SimpleCreditCardPaymentDetailModel::class, View, Count, Create, Update)
            allows(UpdatedPersonContactInfoTempModel::class, View, Count, Create, Update)
        }

        match("Risk Nurse", { subject is StaffModel && subject.isRiskNurse() }) {
            allows(AppointmentScheduleModel::class, View, Count)
            allows(BeneficiaryModel::class, View, Count)
            allows(BeneficiaryCompiledViewModel::class, View, Count)
            allows(BillingAccountablePartyModel::class, View, Count, Create, Update)
            allows(BoletoPaymentDetailModel::class, View, Count)
            allows(BolepixPaymentDetailModel::class, View, Count)
            allows(BillingAccountablePartyModel::class, View, Count)
            allows(CassiMemberModel::class, View, Count)
            allows(CompanyModel::class, View, Count)
            allows(CompanyActivationFilesModel::class, View, Count)
            allows(CompanyContractModel::class, View, Count)
            allows(CompanySubContractModel::class, View, Count)
            allows(DeviceModel::class, View, Count)
            allows(HealthDeclaration::class, View, Count, Create, Update)
            allows(InsurancePortabilityHealthInsuranceModel::class, View, Count, Create, Update, Delete)
            allows(InsurancePortabilityRequestModel::class, View, Count, Create, Update, Delete)
            allows(InsurancePortabilityRequestFileModel::class, View, Count, Create, Update, Delete)
            allows(Lead::class, View, Count)
            allows(LegalGuardianAssociationModel::class, View, Count, Create, Update, Delete)
            allows(LegalGuardianInfoTempModel::class, View, Count, Create, Update, Delete)
            allows(MemberModel::class, View, Count)
            allows(MemberProductPriceModel::class, View, Count)
            allows(MemberProductPriceAdjustmentModel::class, View, Count)
            allows(MemberRegistration::class, View, Count)
            allows(OnboardingBackgroundCheckModel::class, View, Count)
            allows(OnboardingContractModel::class, View, Count)
            allows(OngoingCompanyDeal::class, View, Count)
            allows(PaymentDetailModel::class, View, Count, Create, Update)
            allows(PersonModel::class, View, Count)
            allows(PersonBillingAccountablePartyModel::class, View, Count)
            allows(PersonLoginModel::class, View, Count)
            allows(PersonOnboardingModel::class, View, Count)
            allows(PersonRegistrationModel::class, View, Count)
            allows(PersonPreferencesModel::class, View, Count)
            allows(PixPaymentDetailModel::class, View, Count)
            allows(PriceListingModel::class, View, Count)
            allows(ProductModel::class, View, Count)
            allows(ProductBundleModel::class, View, Count)
            allows(ProductOrderModel::class, View, Count)
            allows(ProductPriceAdjustmentModel::class, View, Count)
            allows(ProductPriceListingModel::class, View, Count)
            allows(PromoCodeModel::class, View, Count, Create, Update)
            allows(ProviderModel::class, View, Count)
            allows(GenericFileVault::class, View, Count)
            allows(SimpleCreditCardPaymentDetailModel::class, View, Count)
            allows(StaffModel::class, View, Count)
            allows(UpdatedPersonContactInfoTempModel::class, View, Count, Create, Update)

            match("can update and create", { action is Update || action is Create }) {
                match("any Health Declaration Schedule") { resource is AppointmentScheduleModel && resource.type == HEALTH_DECLARATION }
            }

            includes(viewMemberContract)
            includes(viewInvoicePayments)
        }

        match("Health Ops - Multi, Lead", {
            subject is StaffModel && (
                    subject.isHealthOps() ||
                            subject.isHealthOpsMulti()
                    )
        }) {
            match("can view", { action is View }) {
                match("any StaffModel") { resource is StaffModel }
                match("any Product") { resource is ProductModel }
                match("any Product Bundle") { resource is ProductBundleModel }
                match("any Provider") { resource is ProviderModel }
                match("any Product Order") { resource is ProductOrderModel }
                match("any PersonModel") { resource is PersonModel }
                match("any DeviceModel") { resource is DeviceModel }
                match("any PersonModel Onboarding") { resource is PersonOnboardingModel }
                match("any PersonModel Registration") { resource is PersonRegistrationModel }
                match("any MemberModel") { resource is MemberModel }
                match("any PersonModel") { resource is PersonModel }
                match("any Lead") { resource is Lead }
                match("any PersonModel Login") { resource is PersonLoginModel }
                match("any Health Declaration Schedule") { resource is AppointmentScheduleModel && resource.type == HEALTH_DECLARATION }
                match("any Product PriceListing") { resource is ProductPriceListingModel }
                match("any PriceListing") { resource is PriceListingModel }
                match("any MemberProductPriceModel") { resource is MemberProductPriceModel }
                match("any Health Declaration") { resource is HealthDeclaration }
            }

            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("any PersonModel Registration") { resource is PersonRegistrationModel }
                match("any MemberModel") { resource is MemberModel }
                match("any PersonModel") { resource is PersonModel }
                match("any Lead") { resource is Lead }
                match("any PersonModel Login") { resource is PersonLoginModel }
            }

            includes(viewAndUpdateOutcomeConf)
            includes(viewAndUpdateHealthDemandMonitoring)
            includes(createViewUpdateCassiMember)
            includes(viewInvoicePayments)
        }

        match("Product Tech Health", { subject is StaffModel && subject.isProductTechHealth() }) {
            includes(viewPersonContractualRisk)
            includes(createPersonContractualRisk)
            includes(viewAndUpdatePersonHealthConditionContractualRisk)
            includes(viewAndUpdateAndCreateMaco)
            includes(viewAndUpdateStandardCost)
        }

        match("Product and Tech", { subject is StaffModel && subject.isProductTech() }) {
            match("can view", { action is View }) {
                match("any StaffModel") { resource is StaffModel }
                match("any BeneficiaryHubspot") { resource is BeneficiaryHubspotModel }
                match("any BeneficiaryCompiledView") { resource is BeneficiaryCompiledViewModel }
                match("any Product") { resource is ProductModel }
                match("any Product Bundle") { resource is ProductBundleModel }
                match("any Provider") { resource is ProviderModel }
                match("any PromoCodeModel") { resource is PromoCodeModel }
                match("any ProviderUnit") { resource is ProviderUnitModel }
                match("any StructuredAddress") { resource is StructuredAddress }
                match("any ContactModel") { resource is ContactModel }
                match("any Onboarding Contract") { resource is OnboardingContractModel }
                match("any ProductPriceAdjustment") { resource is ProductPriceAdjustmentModel }
                match("any MemberProductPriceAdjustment") { resource is MemberProductPriceAdjustmentModel }
                match("any Insurance Portability Request") { resource is InsurancePortabilityRequestModel }
                match("any Insurance Portability Request File") { resource is InsurancePortabilityRequestFileModel }
                match("any Sales Firm") { resource is SalesFirm }
                match("any Sales Firm StaffModel") { resource is SalesFirmStaff }
            }

            match(
                "can view and update and create",
                { action is View || action is Count || action is Update || action is Create }) {
                match("Test Person") { testPerson() }
                match("Test Member") { testPersonResource(MemberModel::class) }
                match("Test Person Health Declaration") { testPersonResource(HealthDeclaration::class) }
                match("Test Person Registration") { testPersonResource(PersonRegistrationModel::class) }
                match("Test Person Onboarding") { testPersonResource(PersonOnboardingModel::class) }
                match("Test Background Check") { testPersonResource(OnboardingBackgroundCheckModel::class) }
                match("Test person Product Order") { testPersonResource(ProductOrderModel::class) }
                match("Test Person Onboarding Contract") { testPersonResource(OnboardingContractModel::class) }
                match("Test Insurance Portability Request") { testPersonResource(InsurancePortabilityRequestModel::class) }
                match("Test Insurance Portability Request File") {
                    testPersonResource(
                        InsurancePortabilityRequestFileModel::class
                    )
                }
                match("Test Appointment Schedule") { testPersonResource(AppointmentScheduleModel::class) }
                match("Test Person Login") { testPersonResource(PersonLoginModel::class) }
                match("Test Member Invoice") { testPersonResource(MemberInvoiceModel::class) }
                match("Test Member Invoice Group") { testPersonResource(MemberInvoiceGroupModel::class) }
                match("Test Pre Activation Payment") { testPersonResource(PreActivationPaymentModel::class) }
                match("any Person Onboarding") { resource is PersonOnboardingModel }
                match("any Person Registration") { resource is PersonRegistrationModel }
                match("any Member") { resource is MemberModel }
                match("any Person") { resource is PersonModel }
                match("any Lead") { resource is Lead }
                match("any PersonModel Login") { resource is PersonLoginModel }
                match("any Onboarding Contract") { resource is OnboardingContractModel }
                match("any Onboarding Background Check") { resource is OnboardingBackgroundCheckModel }
                match("any Health Declaration Schedule") { resource is AppointmentScheduleModel && resource.type == HEALTH_DECLARATION }
                match("any Product Order") { resource is ProductOrderModel }
                match("any PromoCodeModel") { resource is PromoCodeModel }
                match("any Insurance Portability Request") { resource is InsurancePortabilityRequestModel }
                match("any Insurance Portability Request File") { resource is InsurancePortabilityRequestFileModel }
                match("any BillingAccountableParty") { resource is BillingAccountablePartyModel }
                match("any PersonBillingAccountableParty") { resource is PersonBillingAccountablePartyModel }
                match("any Person Preferences") { resource is PersonPreferencesModel }
                match("any Product PriceListing") { resource is ProductPriceListingModel }
                match("any PriceListing") { resource is PriceListingModel }
                match("any MemberProductPrice") { resource is MemberProductPriceModel }
                match("any LegalGuardianInfoTemp") { resource is LegalGuardianInfoTempModel }
                match("any LegalGuardianAssociation") { resource is LegalGuardianAssociationModel }
                match("any Ongoing Company Deal") { resource is OngoingCompanyDeal }
            }

            match("can delete", { action is Delete }) {
                match("any PriceListing") { resourceIs(PriceListingModel::class) }
            }

            match("can count", { action is Count }) {
                match("any Product PriceListing") { resourceIs(ProductPriceListingModel::class) }
                allows(PersonModel::class, Count)
            }

            includes(viewNullvsIntegrationLog)
            includes(viewAndUpdateMemberContract)
            includes(viewCreateUpdateAndDeleteLegalGuardianAssociation)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityHealthInsurance)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityRequest)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityRequestFile)
            includes(createViewUpdateCompanyContractAndSubContract)
            includes(viewAndCreateAndUpdateCompanyActivationFiles)
            includes(viewAndCreateFileVaultCompanyActivationFiles)
            includes(viewAndCountItsOwnCompanyInvoices)
            includes(viewAndUpdateBeneficiary)
            includes(viewAndUpdateHealthCareOpsPriceListing)
            includes(deleteCompanyProductPriceListing)
            includes(viewAddress)
            includes(viewAppointmentSchedule)
            includes(createRequestChangeContactInfo)
            includes(createViewUpdateCassiMember)
            includes(viewBeneficiaryCompiledView)
            includes(viewMemberProductChangeSchedule)
            includes(viewCreateAndUpdateMemberTelegramTracking)
            includes(viewUpdateAndCreateBillingAccountableParty)
            includes(viewInvoicePayments)
            includes(createInvoicePayments)
            includes(viewAndUpdateCompany)
            includes(reactivateMember)
        }

        match("All Staffs", { subject is StaffModel }) {
            includes(viewBeneficiary)
            includes(viewCompany)
            includes(viewCompanyActivationFiles)
            includes(viewOngoingCompanyDeal)
            includes(viewSalesFirm)
        }

        match("Member OPS", { subject is StaffModel && subject.isMemberOps() }) {
            match("can view", { action is View }) {
                match("any OnboardingBackgroundCheck") { resource is OnboardingBackgroundCheckModel }
                match("any StaffModel") { resource is StaffModel }
                match("any Product") { resource is ProductModel }
                match("any Product Bundle") { resource is ProductBundleModel }
                match("any Provider") { resource is ProviderModel }
                match("any Product Order") { resource is ProductOrderModel }
                match("any PersonModel") { resource is PersonModel }
                match("any DeviceModel") { resource is DeviceModel }
                match("any PersonModel Onboarding") { resource is PersonOnboardingModel }
                match("any PersonModel Registration") { resource is PersonRegistrationModel }
                match("any MemberModel") { resource is MemberModel }
                match("any PersonModel") { resource is PersonModel }
                match("any Lead") { resource is Lead }
                match("any PersonModel Login") { resource is PersonLoginModel }
                match("any Product PriceListing") { resource is ProductPriceListingModel }
                match("any PriceListing") { resource is PriceListingModel }
                match("any MemberProductPrice") { resource is MemberProductPriceModel }
                match("any AppointmentScheduleModel") { resource is AppointmentScheduleModel }
                match("any Health Declaration") { resource is HealthDeclaration }
            }

            match(
                "can view and update and create",
                { action is View || action is Count || action is Update || action is Create }) {
                match("any PersonModel Onboarding") { resource is PersonOnboardingModel }
                match("any PersonModel Registration") { resource is PersonRegistrationModel }
                match("any MemberModel") { resource is MemberModel }
                match("any PersonModel") { resource is PersonModel }
                match("any Lead") { resource is Lead }
                match("any PersonModel Login") { resource is PersonLoginModel }
                match("any Product Order") { resource is ProductOrderModel }
                match("any PromoCodeModel") { resource is PromoCodeModel }
                match("any PersonModel Preferences") { resource is PersonPreferencesModel }
                match("any Product PriceListing") { resource is ProductPriceListingModel }
                match("any PriceListing") { resource is PriceListingModel }
                match("any MemberProductPrice") { resource is MemberProductPriceModel }
                match("any BillingAccountableParty") { resource is BillingAccountablePartyModel }
                match("any PersonBillingAccountableParty") { resource is PersonBillingAccountablePartyModel }
                match("any ProductPriceAdjustment") { resource is ProductPriceAdjustmentModel }
                match("any MemberProductPriceAdjustmentModel") { resource is MemberProductPriceAdjustmentModel }
                match("any Onboarding Contract") { resource is OnboardingContractModel }
            }

            match("can send invites", { action is Update || action is Create }) {
                match("any PersonModel") { resource is PersonModel }
                match("any Lead") { resource is Lead }
                match("any PersonModel Login") { resource is PersonLoginModel }
            }
            match("can delete", { action is Delete }) {
                match("any PriceListing") { resourceIs(PriceListingModel::class) }
                match("any ProductPriceAdjustment") { resource is ProductPriceAdjustmentModel }

            }

            match("can count", { action is Count }) {
                match("any Product PriceListing") { resourceIs(ProductPriceListingModel::class) }
            }

            includes(viewCreateUpdateAndDeleteLegalGuardianAssociation)
            includes(changeUserProduct)
            includes(viewMemberProductChangeSchedule)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityHealthInsurance)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityRequest)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityRequestFile)
            includes(viewAddress)
            includes(viewAppointmentSchedule)
            includes(createRequestChangeContactInfo)
            includes(createViewUpdateCassiMember)
            includes(viewCompany)
            includes(canReactivateMember)
            includes(viewInvoicePayments)
            includes(createInvoicePayments)
        }

        match("Chief Risk or Med Ex", { subject is StaffModel && (subject.isChiefRisk() || subject.isMedEx()) }) {
            allows(AppointmentScheduleModel::class, View, Count)
            allows(BeneficiaryModel::class, View, Count)
            allows(BeneficiaryCompiledViewModel::class, View, Count)
            allows(BillingAccountablePartyModel::class, View, Count, Create, Update)
            allows(BoletoPaymentDetailModel::class, View, Count)
            allows(BolepixPaymentDetailModel::class, View, Count)
            allows(BillingAccountablePartyModel::class, View, Count, Create, Update)
            allows(CassiMemberModel::class, View, Count, Create, Update)
            allows(CompanyModel::class, View, Count)
            allows(CompanyActivationFilesModel::class, View, Count)
            allows(CompanyContractModel::class, View, Count)
            allows(CompanyProductPriceListingModel::class, View, Count)
            allows(CompanySubContractModel::class, View, Count)
            allows(DeviceModel::class, View, Count)
            allows(HealthDeclaration::class, View, Count, Create, Update)
            allows(InsurancePortabilityHealthInsuranceModel::class, View, Count, Create, Update, Delete)
            allows(InsurancePortabilityRequestModel::class, View, Count, Create, Update, Delete)
            allows(InsurancePortabilityRequestFileModel::class, View, Count, Create, Update, Delete)
            allows(Lead::class, View, Count, Create, Update)
            allows(LegalGuardianAssociationModel::class, View, Count, Create, Update, Delete)
            allows(LegalGuardianInfoTempModel::class, View, Count, Create, Update, Delete)
            allows(MemberModel::class, View, Count, Create, Update)
            allows(MemberProductPriceModel::class, View, Count, Create, Update)
            allows(MemberProductPriceAdjustmentModel::class, View, Count)
            allows(MemberRegistration::class, View, Count, Create, Update)
            allows(OnboardingBackgroundCheckModel::class, View, Count, Create, Update)
            allows(OnboardingContractModel::class, View, Count, Create, Update)
            allows(OngoingCompanyDeal::class, View, Count)
            allows(PaymentDetailModel::class, View, Count, Create, Update)
            allows(PersonModel::class, View, Count, Create, Update)
            allows(PersonBillingAccountablePartyModel::class, View, Count, Create, Update)
            allows(PersonLoginModel::class, View, Count, Create, Update)
            allows(PersonOnboardingModel::class, View, Count, Create, Update)
            allows(PersonRegistrationModel::class, View, Count, Create, Update)
            allows(PersonPreferencesModel::class, View, Count, Create, Update)
            allows(PixPaymentDetailModel::class, View, Count)
            allows(PriceListingModel::class, View, Count, Create, Update)
            allows(ProductModel::class, View, Count)
            allows(ProductBundleModel::class, View, Count)
            allows(ProductOrderModel::class, View, Count, Create, Update)
            allows(ProductPriceAdjustmentModel::class, View, Count)
            allows(ProductPriceListingModel::class, View, Count, Create, Update)
            allows(PromoCodeModel::class, View, Count, Create, Update)
            allows(ProviderModel::class, View, Count)
            allows(GenericFileVault::class, View, Count)
            allows(SimpleCreditCardPaymentDetailModel::class, View, Count)
            allows(StaffModel::class, View, Count)
            allows(UpdatedPersonContactInfoTempModel::class, View, Count, Create, Update)

            match("can update and create", { action is Update || action is Create }) {
                match("any Health Declaration Schedule") { resource is AppointmentScheduleModel && resource.type == HEALTH_DECLARATION }
            }

            includes(viewCompanyPage)
            includes(viewInvoicePayments)
            includes(viewMemberProductChangeSchedule)
            includes(viewMemberContract)
            includes(viewPersonContractualRisk)
            includes(viewAndUpdateAndCreateMaco)
            includes(viewAndUpdatePersonHealthConditionContractualRisk)
            includes(viewAndUpdateStandardCost)
            includes(createPersonContractualRisk)
        }

        match("Med and Enf Risk", { subject is StaffModel && (subject.isMedRisk() || subject.isRiskNurse()) }) {
            match("can view", { action is View }) {
                match("any PersonModel") { resource is PersonModel }
                match("any MemberModel") { resource is MemberModel }
                match("any Product Order") { resource is ProductOrderModel }
                match("any PersonModel Onboarding") { resource is PersonOnboardingModel }
                match("any PersonModel Registration") { resource is PersonRegistrationModel }
                match("any Lead") { resource is Lead }
                match("any PersonModel Login") { resource is PersonLoginModel }
                match("any Onboarding Contract") { resource is OnboardingContractModel }
                match("any Onboarding Background Check") { resource is OnboardingBackgroundCheckModel }
                match("any Health Declaration Schedule") { resource is AppointmentScheduleModel && resource.type == HEALTH_DECLARATION }
                match("any Insurance Portability Request") { resource is InsurancePortabilityRequestModel }
                match("any Insurance Portability Request File") { resource is InsurancePortabilityRequestFileModel }
                match("any Person Preferences") { resource is PersonPreferencesModel }
                match("any MemberProductPrice") { resource is MemberProductPriceModel }
                match("any Product") { resource is ProductModel }
                match("any ProductBundle") { resource is ProductBundleModel }
                match("any Product PriceListing") { resource is ProductPriceListingModel }
                match("any PriceListing") { resource is PriceListingModel }
                match("any Provider") { resource is ProviderModel }
            }

            match("can view & update and create", { action is View || action is Update || action is Create }) {
                match("any Health Declaration") { resource is HealthDeclaration }
                match("any Appointment Schedule") { resource is AppointmentScheduleModel }
            }

            includes(viewPersonContractualRisk)
            includes(createPersonContractualRisk)
            includes(viewAndUpdateAndCreateMaco)
            includes(viewAndUpdatePersonHealthConditionContractualRisk)
            includes(viewAndUpdateStandardCost)
            includes(viewCompanyProductPriceListing)
        }

        match("Finance OPS", { subject is StaffModel && subject.isFinOps() }) {
            match("can view", { action is View }) {
                match("any StaffModel") { resource is StaffModel }
                match("any Product") { resource is ProductModel }
                match("any Product Bundle") { resource is ProductBundleModel }
                match("any Provider") { resource is ProviderModel }
                match("any Product Order") { resource is ProductOrderModel }
                match("any PersonModel") { resource is PersonModel }
                match("any DeviceModel") { resource is DeviceModel }
                match("any PersonModel Onboarding") { resource is PersonOnboardingModel }
                match("any PersonModel Registration") { resource is PersonRegistrationModel }
                match("any MemberModel") { resource is MemberModel }
                match("any PersonModel") { resource is PersonModel }
                match("any Lead") { resource is Lead }
                match("any PersonModel Login") { resource is PersonLoginModel }
                match("any Product PriceListing") { resource is ProductPriceListingModel }
                match("any PriceListing") { resource is PriceListingModel }
                match("any MemberProductPriceModel") { resource is MemberProductPriceModel }
            }

            includes(viewInvoicePayments)
        }

        match(
            "Insurance Ops - Health Institution Ops and Insurance Ops - Community Success",
            { subject is StaffModel && (subject.isInsuranceOpsHealthInstitutionOps() || subject.isInsuranceOpsCommunitySuccess()) }) {
            match("can view", { action is View }) {
                match("any StaffModel") { resource is StaffModel }
                match("any Product") { resource is ProductModel }
                match("any Product Bundle") { resource is ProductBundleModel }
                match("any Provider") { resource is ProviderModel }
                match("any Product Order") { resource is ProductOrderModel }
                match("any PersonModel") { resource is PersonModel }
                match("any DeviceModel") { resource is DeviceModel }
                match("any PersonModel Onboarding") { resource is PersonOnboardingModel }
                match("any PersonModel Registration") { resource is PersonRegistrationModel }
                match("any MemberModel") { resource is MemberModel }
                match("any PersonModel") { resource is PersonModel }
                match("any PersonInternalReference") { resource is PersonInternalReference }
                match("any Lead") { resource is Lead }
                match("any PersonModel Login") { resource is PersonLoginModel }
                match("any Health Declaration") { resource is HealthDeclaration }
                match("any Product Price Adjustment") { resource is ProductPriceAdjustmentModel }
                match("any Onboarding Contract") { resource is OnboardingContractModel }
                match("any Insurance Portability Request") { resource is InsurancePortabilityRequestModel }
                match("any Insurance Portability Request File") { resource is InsurancePortabilityRequestFileModel }
                match("any Onboarding Background Check") { resource is OnboardingBackgroundCheckModel }
                match("any Appointment Schedule") { resource is AppointmentScheduleModel }
            }

            match(
                "can view and update and create",
                { action is View || action is Update || action is Create || action is Count }) {
                match("any Product PriceListing") { resource is ProductPriceListingModel }
                match("any PriceListing") { resource is PriceListingModel }
                match("any MemberProductPriceModel") { resource is MemberProductPriceModel }
                includes(createProducts)
            }
        }

        match("Health Community", { subject is StaffModel && subject.isHealthCommunity() }) {
            match("can view", { action is View }) {
                match("any StaffModel") { resource is StaffModel }
                match("any Product") { resource is ProductModel }
                match("any Product Bundle") { resource is ProductBundleModel }
                match("any Provider") { resource is ProviderModel }
                match("any Product Order") { resource is ProductOrderModel }
                match("any PersonModel") { resource is PersonModel }
                match("any DeviceModel") { resource is DeviceModel }
                match("any PersonModel Onboarding") { resource is PersonOnboardingModel }
                match("any PersonModel Registration") { resource is PersonRegistrationModel }
                match("any MemberModel") { resource is MemberModel }
                match("any PersonModel") { resource is PersonModel }
                match("any PersonInternalReference") { resource is PersonInternalReference }
                match("any Lead") { resource is Lead }
                match("any PersonModel Login") { resource is PersonLoginModel }
                match("any Product PriceListing") { resource is ProductPriceListingModel }
                match("any PriceListing") { resource is PriceListingModel }
                match("any MemberProductPriceModel") { resource is MemberProductPriceModel }
                match("any Health Declaration") { resource is HealthDeclaration }
            }
        }
    }

    match("at healthcare-ops-webhooks", { rootService.name == HEALTH_CARE_OPS_WEBHOOKS_ROOT_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            match("can send invites", { action is Update || action is View || action is Create }) {
                match("any PersonModel") {
                    resource is PersonModel &&
                            subject is Unauthenticated &&
                            resource.nationalId == subject.key
                }

                match("any Lead") {
                    resource is Lead &&
                            subject is Unauthenticated &&
                            resource.nationalId == subject.key
                }

                match("any PersonModel Login") {
                    resource is PersonLoginModel &&
                            subject is Unauthenticated &&
                            resource.nationalId == subject.key
                }

                match("any Health Declaration") { resource is HealthDeclaration }
                match("any PersonModel Onboarding") { resource is PersonOnboardingModel }
                match("any PersonModel Registration") { resource is PersonRegistrationModel }
                match("any PromoCodeModel") { resource is PromoCodeModel }
            }
        }
    }

}
