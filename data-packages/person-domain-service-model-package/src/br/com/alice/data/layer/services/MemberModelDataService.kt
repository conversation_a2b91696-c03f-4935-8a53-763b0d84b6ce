package br.com.alice.data.layer.services

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.mapFirst
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.client.UpdaterList
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.not
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.data.layer.models.MemberModel
import br.com.alice.data.layer.models.MemberStatus
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface MemberModelDataService : Service,
    Finder<MemberModelDataService.FieldOptions, MemberModelDataService.OrderingOptions, MemberModel>,
    Counter<MemberModelDataService.FieldOptions, MemberModelDataService.OrderingOptions, MemberModel>,
    Updater<MemberModel>,
    UpdaterList<MemberModel>,
    Getter<MemberModel>,
    Adder<MemberModel> {

    override val namespace: String
        get() = "member"
    override val serviceName: String
        get() = "member"

    class IdField : Field.UUIDField(MemberModel::id) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ArchivedField : Field.BooleanField(MemberModel::archived)

    class PersonIdField : Field.TableIdField(MemberModel::personId) {
        fun inList(value: List<PersonId>) = Predicate.inList(this, value)
    }

    class ParentMemberField : Field.UUIDField(MemberModel::parentMember) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<PersonId>) = Predicate.inList(this, value)
    }

    class StatusField : Field.TextField(MemberModel::status) {
        fun eq(value: MemberStatus) = Predicate.eq(this, value)
        fun inList(value: List<MemberStatus>) = Predicate.inList(this, value)
    }

    class BrandField : Field.TextField(MemberModel::brand) {
        fun eq(value: Brand) = Predicate.eq(this, value)
    }

    class ActivationDate : Field.DateTimeField(MemberModel::activationDate) {
        fun isNull() = Predicate.isNull(this)
        fun isNotNull() = not(Predicate.isNull(this))
        fun greaterEq(date: LocalDateTime) = Predicate.greaterEq(this, date)
        fun lessEq(date: LocalDateTime) = Predicate.lessEq(this, date)
    }
    class ProductIdField : Field.JsonbField(MemberModel::selectedProduct) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: UUID) = Predicate.jsonSearch(this, "{\"id\":\"$value\"}")
    }

    class CreatedAt : Field.DateTimeField(MemberModel::createdAt) {
        fun greaterEq(date: LocalDateTime) = Predicate.greaterEq(this, date)
        fun lessEq(date: LocalDateTime) = Predicate.lessEq(this, date)
    }

    class CanceledAt : Field.DateTimeField(MemberModel::canceledAt) {
        fun isNull() = Predicate.isNull(this)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class BeneficiaryIdField : Field.UUIDField(MemberModel::beneficiaryId) {
        fun isNull() = Predicate.isNull(this)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class ParentBeneficiaryField : Field.JsonbField(MemberModel::beneficiary) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: UUID) = Predicate.jsonSearch(this, "{\"parent_beneficiary\":\"${value}\"}")
    }

    class BeneficiaryTypeField : Field.JsonbField(MemberModel::beneficiary) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: BeneficiaryType) = Predicate.jsonSearch(this, "{\"type\":\"${value}\"}")
    }

    class CompanyIdField : Field.UUIDField(MemberModel::companyId) {
        fun isNull() = Predicate.isNull(this)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class CompanySubContractIdField : Field.UUIDField(MemberModel::companySubContractId) {
        fun isNull() = Predicate.isNull(this)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class ParentPersonField : Field.TableIdField(MemberModel::parentPerson)

    class FieldOptions {
        val id = IdField()
        val personId = PersonIdField()
        val archived = ArchivedField()
        val status = StatusField()
        val activationDate = ActivationDate()
        val brand = BrandField()
        val productId = ProductIdField()
        val createdAt = CreatedAt()
        val canceledAt = CanceledAt()
        val parentMember = ParentMemberField()
        val beneficiaryId = BeneficiaryIdField()
        val companyId = CompanyIdField()
        val companySubContractId = CompanySubContractIdField()
        val parentBeneficiary = ParentBeneficiaryField()
        val parentPersonId = ParentPersonField()
        val beneficiaryType = BeneficiaryTypeField()
    }

    class OrderingOptions {
        val id = IdField()
        val activationDate = ActivationDate()
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    suspend fun findByPerson(personId: String) = find {
        where { this.personId.eq(personId).and(this.archived.eq(false)) }
    }.mapFirst()

    suspend fun findByPersonAndStatus(personId: PersonId, status: MemberStatus) =
        findByPersonAndStatuses(personId, listOf(status))

    suspend fun findByPersonAndStatuses(personId: PersonId, status: List<MemberStatus>) = find {
        where {
            this.personId.eq(personId)
                .and(this.archived.eq(false))
                .and(this.status.inList(status))
        }
    }

    suspend fun findByPersonsAndStatus(personIds: List<PersonId>, status: MemberStatus) = find {
        where {
            this.personId.inList(personIds)
                .and(this.archived.eq(false))
                .and(this.status.eq(status))
        }
    }
    
    suspend fun findByPersonsAndStatuses(personIds: List<PersonId>, status: List<MemberStatus>) = find {
        where {
            this.personId.inList(personIds)
                .and(this.archived.eq(false))
                .and(this.status.inList(status))
        }
    }

    suspend fun findFirstByPerson(personId: PersonId) = findOneOrNull {
        where {
            this.personId.eq(personId)
        }.orderBy { activationDate }.sortOrder { asc }
    }

    suspend fun findIdsByStatus(status: MemberStatus) = find {
        where {
            this.status.eq(status)
                .and(this.archived.eq(false))
        }
    }.get().asSequence().map { it.id }.success()

    override suspend fun add(model: MemberModel): Result<MemberModel, Throwable>

    override suspend fun findByQuery(query: Query): Result<List<MemberModel>, Throwable>

    override suspend fun update(model: MemberModel): Result<MemberModel, Throwable>
    override suspend fun updateList(models: List<MemberModel>, returnOnFailure: Boolean): Result<List<MemberModel>, Throwable>
    override suspend fun get(id: UUID): Result<MemberModel, Throwable>

    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>

    suspend fun findByBrandAndStatus(status: MemberStatus, brand: Brand, limit: Int, offset: Int) = find {
        where {
            this.status.eq(status) and
                    this.brand.eq(brand)
        }.limit { limit }.offset { offset }
    }

    suspend fun findOneByBeneficiaryId(beneficiaryId: UUID) = findOne {
        where {
            this.beneficiaryId.eq(beneficiaryId) and this.archived.eq(false)
        }
    }

    suspend fun findByBeneficiaryIds(beneficiaryIds: List<UUID>) = find {
        where {
            this.beneficiaryId.inList(beneficiaryIds) and this.archived.eq(false)
        }
    }

    // FIXME: range should be mandatory
    suspend fun findByCompanyId(companyId: UUID, range: IntRange?) = find {
        where {
            this.companyId.eq(companyId) and this.archived.eq(false)
        }.apply { range?.let { addSortAndPagination(this, range) } }
    }

    suspend fun findByCompanyIds(companyIds: List<UUID>, range: IntRange) = find {
        addSortAndPagination(
            where {
                this.companyId.inList(companyIds) and this.archived.eq(false)
            }, range
        )
    }

    // FIXME: range should be mandatory
    suspend fun findByCompanySubContractId(companySubContractId: UUID, range: IntRange?) = find {
            where {
                this.companySubContractId.eq(companySubContractId) and this.archived.eq(false)
            }.apply { range?.let { addSortAndPagination(this, range) } }
    }

    suspend fun findByCompanyIdAndPersonIds(companyId: UUID, personIds: List<PersonId>) = find {
        where {
            this.companyId.eq(companyId) and
                    this.personId.inList(personIds) and
                    this.archived.eq(false)
        }
    }

    suspend fun findByParentPersonIdAndStatuses(parentPersonId: PersonId, statuses: List<MemberStatus>) = find {
        where {
            this.parentPersonId.eq(parentPersonId) and
                    this.status.inList(statuses) and
                    this.archived.eq(false)
        }
    }

    suspend fun findByParentBeneficiaryId(parentBeneficiaryId: UUID) = find {
        where {
            this.parentBeneficiary.eq(parentBeneficiaryId) and this.archived.eq(false)
        }
    }

    suspend fun countByBeneficiaryIds(beneficiaryIds: List<UUID>) = count {
        where {
            this.beneficiaryId.inList(beneficiaryIds) and this.archived.eq(false)
        }
    }

    @OptIn(WithFilterPredicateUsage::class)
    suspend fun findByFilter(companyId: UUID?, beneficiaryParentId: UUID?, status: MemberStatus?, range: IntRange) = find {
        addSortAndPagination(
            where {
                this.archived.eq(false)
                    .withFilter(status) { this.status.eq(it) }
                    .withFilter(companyId) { this.companyId.eq(it) }
                    .withFilter(beneficiaryParentId) { this.parentBeneficiary.eq(it) }!!
            }, range
        )
    }

    suspend fun findByCancellationDate(startDate: LocalDate, endDate: LocalDate, range: IntRange) = find {
        addSortAndPagination(
            where {
                this.canceledAt.greaterEq(startDate.atStartOfDay()) and
                        this.canceledAt.lessEq(endDate.atStartOfDay()) and
                        this.archived.eq(false)
            }, range
        )
    }

    @OptIn(WithFilterPredicateUsage::class)
    suspend fun countByFilter(companyId: UUID?, status: MemberStatus?, parentBeneficiaryId: UUID?) = count {
        where {
            this.archived.eq(false)
                .withFilter(companyId) { this.companyId.eq(it) }
                .withFilter(status) { this.status.eq(it) }
                .withFilter(parentBeneficiaryId) { this.parentBeneficiary.eq(it) }!!
        }
    }

    @OptIn(WithFilterPredicateUsage::class)
    suspend fun countByCompanyIdAndStatus(
        companyId: UUID,
        status: MemberStatus,
        type: BeneficiaryType? = null,
        withNoPendingCancellation: Boolean = false
    ) = count {
        where {
            this.archived.eq(false) and this.companyId.eq(companyId) and this.status.eq(MemberStatus.ACTIVE)
                .withFilter(type) { this.beneficiaryType.eq(it) }
                .withFilter(takeIf { withNoPendingCancellation }) { this.canceledAt.isNull() }!!
        }
    }

    private fun addSortAndPagination(query: QueryBuilder<FieldOptions, OrderingOptions>, range: IntRange) = query
        .orderBy { createdAt }
        .sortOrder { asc }
        .offset { range.first }
        .limit { range.last }
}
