# App Content Domain Service

[![Coverage](https://sonarcloud.io/api/project_badges/measure?project=mono%3Aapp-content-domain-service&metric=coverage&token=fffcae26ceabe0657c2307d505beca15e0230bfb)](https://sonarcloud.io/summary/new_code?id=mono%3Aapp-content-domain-service)

[![Code Smells](https://sonarcloud.io/api/project_badges/measure?project=mono%3Aapp-content-domain-service&metric=code_smells&token=fffcae26ceabe0657c2307d505beca15e0230bfb)](https://sonarcloud.io/summary/new_code?id=mono%3Aapp-content-domain-service)

[![Duplicated Lines (%)](https://sonarcloud.io/api/project_badges/measure?project=mono%3Aapp-content-domain-service&metric=duplicated_lines_density&token=fffcae26ceabe0657c2307d505beca15e0230bfb)](https://sonarcloud.io/summary/new_code?id=mono%3Aapp-content-domain-service)

[![Bugs](https://sonarcloud.io/api/project_badges/measure?project=mono%3Aapp-content-domain-service&metric=bugs&token=fffcae26ceabe0657c2307d505beca15e0230bfb)](https://sonarcloud.io/summary/new_code?id=mono%3Aapp-content-domain-service)

### Responsible Team
Platform

- Find us on ``#eng-gestao-de-saude`` on Slack ;)
- [Platform Notion](https://www.notion.so/alicehealth/Platform-Team-b789312d8f064ce0a599010450ea22d8)

### Events Consumed

| Event Name                 | Why is consumed here?                                                                                         | Idempotency strategy                                                                    |
|----------------------------|---------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------|
| `HealthFormCompletedEvent` | To mark an `AppContentScreenDetail` which applies a `HealthForm` on the home screen as FINISHED on its status | Checking which `personId` and `questionnaireKey` the `AppContentScreenDetail` refers to |

### Feature Flags

| Feature Flag Name | Why is used? |
|-------------------|--------------|
|                   |              |
