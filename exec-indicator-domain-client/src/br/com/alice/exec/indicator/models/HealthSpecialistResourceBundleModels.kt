package br.com.alice.exec.indicator.models

import br.com.alice.common.core.Status
import br.com.alice.data.layer.models.AppointmentRecommendationLevel
import br.com.alice.data.layer.models.CSVPricingUpdateError
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.HealthSpecialistResourceBundle
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.data.layer.models.ResourceBundleSpecialty
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPrice
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdate
import br.com.alice.data.layer.models.Staff
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class HealthSpecialistResourceBundlePatchRequest(
    val aliceDescription: String? = null,
    val status: Status? = null,
    val medicalSpecialtyIds: List<UUID>? = null,
)

data class HealthSpecialistResourceBundlePatchResponse(
    val id: UUID,
    val secondaryResources: List<SecondaryResourcesTransport>,
    val executionAmount: Int,
    val executionEnvironment: HealthSpecialistProcedureExecutionEnvironment,
    val aliceDescription: String,
    val status: Status,
    val serviceType: HealthSpecialistResourceBundleServiceType,
    val medicalSpecialtyIds: List<UUID> = emptyList(),
)

data class SecondaryResourcesTransport(val id: UUID)

data class HealthSpecialistResourceBundlePaginatedResponse(
    val total: Int,
    val items: List<HealthSpecialistResourceBundleWithPricingData>
)

enum class HealthSpecialistResourceBundlePricingStatus(val description: String) {
    PRICED("Precificado"),
    PENDING("Possui pendências")
}

data class HealthSpecialistResourceBundleWithPricingData(
    val healthSpecialistResourceBundle: HealthSpecialistResourceBundle,
    val medicalSpecialtyIds: List<UUID> = emptyList(),
    val pricingStatus: HealthSpecialistResourceBundlePricingStatus,
    val specialtiesCount: Int,
    val allSpecialtiesCount: Int,
)

data class ResourceBundleSpecialtyResponse(
    val id: UUID,
    val name: String,
    val isTherapy: Boolean,
    val pricingStatus: HealthSpecialistResourceBundlePricingStatus,
    val currentBeginAt: LocalDate?,
    val currentEndAt: LocalDate?,
    val hasScheduledPriceChange: Boolean,
    val medicalSpecialtyId: UUID,
)

data class ResourceBundleSpecialtiesWithCount(
    val count: Int,
    val specialties: List<ResourceBundleSpecialtyResponse>,
)

data class PricingForHealthSpecialistResourceBundleWithCountResponse(
    val count: Int,
    val items: List<PricingForHealthSpecialistResourceBundleResponse>
)

data class PricingForHealthSpecialistResourceBundleResponse(
    val healthSpecialistResourceBundleId: UUID,
    val primaryTuss: String,
    val aliceCode: String,
    val description: String,
    val serviceType: String,
    val pendingNumber: Int,
    val medicalSpecialties: List<PricingForHealthSpecialistMedicalSpecialtyResponse>,
)

data class PricingForHealthSpecialistMedicalSpecialtyResponse(
    val medicalSpecialtyId: UUID,
    val description: String,
    val prices: List<ResourceBundleSpecialtyPrice>,
    val pendingNumber: Int,
    val beginAt: LocalDate?,
    val changeBeginAt: LocalDate?
)

data class PricingForHealthSpecialistRequestFilters(
    val query: String? = null,
    val medicalSpecialtyIds: List<UUID>? = null,
    val status: PricingStatus? = null,
    val serviceTypes: List<HealthSpecialistResourceBundleServiceType>? = null,
)

data class ProcessingResourceBundleSpecialtyPricingUpdateResponse(
    val isProcessing: Boolean,
    val resourceBundleSpecialtyPricingUpdate: ResourceBundleSpecialtyPricingUpdate? = null,
)

data class ResourceBundleSpecialtyAggregateCount(
    val count: Int,
    val response: List<ResourceBundleSpecialtyAggregate>,
)

data class ResourceSuggestedProcedure(
    val suggestedProcedure: List<ResourceBundleSpecialtyAggregate>,
    val procedureDefault: ResourceBundleSpecialtyAggregate?
)

data class ResourceBundleSpecialtyAggregate(
    val id: UUID,
    val appointmentRecommendationLevel: AppointmentRecommendationLevel,
    val status: Status,
    val medicalSpecialtyId: UUID,
    val primaryTuss: String,
    val code: String,
    val description: String,
    val healthSpecialistResourceBundleStatus: Status,
)

data class AppointmentRecommendationBondRequest(
    val suggestedProcedure: List<UUID>,
    val procedureDefault: UUID? = null,
)

data class UpdateSuggestedProcedure(
    val procedureToRemoveBond: List<ResourceBundleSpecialty>,
    val procedureToAddBond: List<ResourceBundleSpecialty>,
)

data class ResourceBundleSpecialtyPricingUpdateHistoryWithCount(
    val count: Int,
    val items: List<ResourceBundleSpecialtyPricingUpdateHistoryItem>,
)

data class ResourceBundleSpecialtyPricingUpdateHistoryItem(
    val id: UUID,
    val fileName: String,
    val fileUrl: String?,
    val createdByStaff: Staff?,
    val processingAt: LocalDateTime? = null,
    val completedAt: LocalDateTime? = null,
    val rowsCount: Int,
    val failedRowsCount: Int,
    val failedRowsErrors: List<CSVPricingUpdateError> = emptyList(),
    val parsingError: String? = null,
    val pricesBeginAt: LocalDate,
    val createdAt: LocalDateTime,
)
