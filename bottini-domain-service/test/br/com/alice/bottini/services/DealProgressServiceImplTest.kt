package br.com.alice.bottini.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.communication.crm.sales.b2c.Deal
import br.com.alice.communication.crm.sales.b2c.DealResult
import br.com.alice.communication.crm.sales.b2c.DealStage
import br.com.alice.communication.crm.sales.b2c.SalesCrmPipeline
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.DealSalesInfo
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonSalesInfo
import br.com.alice.data.layer.models.ProductType
import br.com.alice.membership.client.PromoCodeService
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.person.client.PersonService
import br.com.alice.person.model.events.PersonPayload
import br.com.alice.product.client.ProductService
import br.com.alice.schedule.model.events.AppointmentSchedulePayload
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class DealProgressServiceImplTest {

    private val salesCrmPipeline: SalesCrmPipeline = mockk()
    private val personSalesService: PersonSalesService = mockk()
    private val onboardingService: OnboardingService = mockk()
    private val dealSalesInfoService: DealSalesInfoService = mockk()
    private val productService: ProductService = mockk()
    private val personService: PersonService = mockk()
    private val promoCodeService: PromoCodeService = mockk()

    private val dealProgressService = DealProgressServiceImpl(
        salesCrmPipeline,
        personSalesService,
        onboardingService,
        dealSalesInfoService,
        productService,
        promoCodeService,
        personService
    )

    private val leadId = RangeUUID.generate()
    private val person = TestModelFactory.buildPerson(opportunityId = RangeUUID.generate(), leadId = leadId)
    private val personSalesInfo = PersonSalesInfo(
        leadId = leadId,
        personId = person.id,
        hubspotDealId = RangeUUID.generate().toString()
    )
    private val product = TestModelFactory.buildProduct()
    private val productOrder = TestModelFactory.buildProductOrder(product = product, personId = person.id)
    private val insurancePortabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(person.id)
    private val notFinishedOnboarding = TestModelFactory.buildPersonOnboarding(
        personId = person.id,
        currentPhase = OnboardingPhase.SHOPPING
    )

    @Test
    fun `#registerFirstAccessOnDeal should upsert deal with first access date when receive FirstAccessEvent with disabled SalesCrmPipeline`() = runBlocking {
        val personLogin = TestModelFactory.buildPersonLogin(personId = person.id)
        val deal = TestModelFactory.buildDealSalesInfo(hubspotDealId = "teste")

        coEvery { personService.get(personLogin.personId) } returns person.success()
        coEvery { dealSalesInfoService.findByLastOpportunityId(any()) } returns deal
        coEvery { onboardingService.findByPerson(person.id) } returns notFinishedOnboarding.success()

        val response = dealProgressService.registerFirstAccessOnDeal(personLogin)

        ResultAssert.assertThat(response).isSuccess()

        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { dealSalesInfoService.findByLastOpportunityId(any()) }
        coVerifyOnce { onboardingService.findByPerson(any()) }
        coVerifyNone { salesCrmPipeline.updateDeal(any(), any()) }
    }

    @Test
    fun `#registerFirstAccessOnDeal should not upsert deal when person is duquesa`() = runBlocking {
        val personLogin = TestModelFactory.buildPersonLogin(personId = person.id)

        coEvery {
            personService.get(personLogin.personId)
        } returns person.copy(tags = listOf(Person.DUQUESA_TAG)).success()

        val response = dealProgressService.registerFirstAccessOnDeal(personLogin)

        ResultAssert.assertThat(response).isSuccess()

        coVerifyOnce { personService.get(any()) }
        coVerifyNone { dealSalesInfoService.findByLastOpportunityId(any()) }
        coVerifyNone { salesCrmPipeline.updateDeal(any(), any()) }
    }

    @Test
    fun `#moveDealStageOnShoppingFinished should ignore Sales CRM integration when deal was not found`() =
        runBlocking<Unit> {
            coEvery { personService.get(person.id) } returns person.success()
            coEvery { personSalesService.findByLeadId(person.leadId!!) } returns null
            coEvery { dealSalesInfoService.findByLastOpportunityId(any()) } returns null
            coEvery { productService.getProduct(productOrder.productId) } returns product.success()
            coEvery { onboardingService.findByPerson(person.id) } returns notFinishedOnboarding.success()

            val response = dealProgressService.moveDealStageOnShoppingFinished(productOrder, null)
            ResultAssert.assertThat(response).isSuccess()

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { personSalesService.findByLeadId(any()) }
            coVerifyOnce { dealSalesInfoService.findByLastOpportunityId(any()) }
            coVerifyOnce { productService.getProduct(any()) }
            coVerifyOnce { onboardingService.findByPerson(any()) }
        }

    @Test
    fun `#moveDealStageOnShoppingFinished should not update any information when the onboarding has already finished`() =
        runBlocking {
            val onboarding = TestModelFactory.buildPersonOnboarding(
                personId = productOrder.personId,
                currentPhase = OnboardingPhase.FINISHED
            )

            coEvery { onboardingService.findByPerson(productOrder.personId) } returns onboarding.success()
            coEvery { productService.getProduct(productOrder.productId) } returns product.success()

            val response = dealProgressService.moveDealStageOnShoppingFinished(productOrder, null)
            ResultAssert.assertThat(response).isSuccess()

            coVerifyOnce { onboardingService.findByPerson(any()) }
            coVerifyOnce { productService.getProduct(any()) }
            coVerifyNone { salesCrmPipeline.updateDeal(any(), any()) }
        }

    @Test
    fun `#moveDealStageOnShoppingFinished should update a deal when a person has not finished onboarding`() =
        runBlocking {
            coEvery { productService.getProduct(productOrder.productId) } returns product.success()
            coEvery { onboardingService.findByPerson(person.id) } returns notFinishedOnboarding.success()

            assertHubspotDealMove(DealStage.SHOPPING_FINISHED) {
                dealProgressService.moveDealStageOnShoppingFinished(productOrder, null)
            }

            coVerifyOnce { productService.getProduct(productOrder.productId) }
            coVerifyOnce { onboardingService.findByPerson(person.id) }

        }

    @Test
    fun `#moveDealStageOnShoppingFinished should return 200 OK and move CRM stage to shopping`() = runBlocking<Unit> {
        coEvery { productService.getProduct(productOrder.productId) } returns product.success()
        coEvery { onboardingService.findByPerson(person.id) } returns notFinishedOnboarding.success()

        assertHubspotDealMove(DealStage.SHOPPING_FINISHED) {
            dealProgressService.moveDealStageOnShoppingFinished(productOrder, null)
        }

        coVerifyOnce { productService.getProduct(any()) }
        coVerifyOnce { onboardingService.findByPerson(any()) }

    }

    @Test
    fun `#moveDealStageOnContractStarted should move a Deal to contract_received stage`() = runBlocking<Unit> {
        coEvery { onboardingService.findByPerson(person.id) } returns notFinishedOnboarding.success()

        assertHubspotDealMove(DealStage.CONTRACT_RECEIVED) {
            dealProgressService.moveDealStageOnContractStarted(person.id)
        }

        coVerifyOnce { onboardingService.findByPerson(any()) }
    }

    @Test
    fun `#moveDealOnPortabilitySubmitted should move a Deal to waiting_portability stage`() = runBlocking<Unit> {
        coEvery { onboardingService.findByPerson(person.id) } returns notFinishedOnboarding.success()

        assertHubspotDealMove(DealStage.WAITING_PORTABILITY) {
            dealProgressService.moveDealOnPortabilitySubmitted(insurancePortabilityRequest)
        }

        coVerifyOnce { onboardingService.findByPerson(any()) }
    }

    @Test
    fun `#moveDealOnPortabilityDecline should move a Deal to shopping_finished stage`() = runBlocking<Unit> {
        coEvery { onboardingService.findByPerson(person.id) } returns notFinishedOnboarding.success()

        assertHubspotDealMove(DealStage.SHOPPING_FINISHED) {
            dealProgressService.moveDealOnPortabilityDecline(insurancePortabilityRequest)
        }

        coVerifyOnce { onboardingService.findByPerson(any()) }
    }

    @Test
    fun `#moveDealOnPortabilityInProgress should move a Deal to WAITING_FOR_PORTABILITY_INFO stage`() = runBlocking<Unit> {
        coEvery { onboardingService.findByPerson(person.id) } returns notFinishedOnboarding.success()

        assertHubspotDealMove(DealStage.WAITING_PORTABILITY_INFO) {
            dealProgressService.moveDealOnPortabilityInProgress(insurancePortabilityRequest)
        }

        coVerifyOnce { onboardingService.findByPerson(any()) }
    }

    @Test
    fun `#moveDealStageOnRegistrationFinished should return 200 OK and move CRM stage to shopping`() = runBlocking<Unit> {
        coEvery { onboardingService.findByPerson(person.id) } returns notFinishedOnboarding.success()

        assertHubspotDealMove(DealStage.HEALTH_DECLARATION_APPOINTMENT) {
            dealProgressService.moveDealStageOnRegistrationFinished(person.id)
        }

        coVerifyOnce { onboardingService.findByPerson(any()) }
    }

    @Test
    fun `#moveDealOnAppointmentCreated should return 200 OK and move CRM stage to health declaration appointment`() = runBlocking<Unit> {
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(person.id)

        coEvery { onboardingService.findByPerson(person.id) } returns notFinishedOnboarding.success()

        assertHubspotDealMove(DealStage.SCHEDULED_HEALTH_DECLARATION_APPOINTMENT) {
            dealProgressService.moveDealOnAppointmentCreated(appointmentSchedule, person.id)
        }

        coVerifyOnce { onboardingService.findByPerson(any()) }

    }

    @Test
    fun `#moveDealOnAppointmentCreated should return 200 OK and dont move to a new stage when a appointment is not health declaration`() = runBlocking {
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(
            personId = person.id,
            type = AppointmentScheduleType.IMMERSION
        )

        val response = dealProgressService.moveDealOnAppointmentCreated(appointmentSchedule, person.id)
        ResultAssert.assertThat(response).isSuccess()

        coVerifyNone { salesCrmPipeline.updateDeal(any(), any()) }
    }


    @Test
    fun `#moveDealOnInvoiceSent should return 200 OK and move CRM stage to invoice sent`() = runBlocking<Unit> {
        coEvery { onboardingService.findByPerson(person.id) } returns notFinishedOnboarding.success()

        assertHubspotDealMove(DealStage.INVOICE_SENT) {
            dealProgressService.moveDealOnInvoiceSent(person.id)
        }

        coVerifyOnce { onboardingService.findByPerson(any()) }
    }

    @Test
    fun `#moveDealOnMemberActivated should return 200 OK and move CRM stage to member activated`() = runBlocking<Unit> {
        val member = TestModelFactory.buildMember(person.id)

        coEvery { onboardingService.findByPerson(person.id) } returns notFinishedOnboarding.success()

        assertHubspotDealMove(DealStage.MEMBER_ACTIVATED, true) {
            dealProgressService.moveDealOnMemberActivated(member)
        }

        coVerifyOnce { onboardingService.findByPerson(any()) }
    }

    @Test
    fun `#moveDealOnMemberActivated should return 200 OK and move CRM stage to LOST_LEAD_B2C`() = runBlocking<Unit> {
        val member = TestModelFactory.buildMember(personId = person.id, productType = ProductType.B2B)

        coEvery { onboardingService.findByPerson(person.id) } returns notFinishedOnboarding.success()


        assertHubspotDealMove(DealStage.LOST_LEAD_B2C, true) {
            dealProgressService.moveDealOnMemberActivated(member)
        }

        coVerifyOnce { onboardingService.findByPerson(any()) }
    }

    @Test
    fun `#moveDealOnAppointmentCancellation should return 200 OK and movCRM stage of health declaration appointment no show with disabled SalesCrmPipeline`() = runBlocking<Unit> {
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(person.id)
        val schedule = appointmentSchedule.convertTo(AppointmentSchedulePayload::class)
        val notFinishedOnboarding = TestModelFactory.buildPersonOnboarding(
            personId = person.id,
            currentPhase = OnboardingPhase.SHOPPING
        )

        val newDealSalesInfo =
            DealSalesInfo(
                personId = person.id,
                lastOpportunityId = person.opportunityId,
                hubspotDealId = personSalesInfo.hubspotDealId
            )
        val newPersonSalesInfo = personSalesInfo.copy(hubspotDealId = null)

        coEvery {
            onboardingService.findByPerson(person.id)
        } returns notFinishedOnboarding.success()
        coEvery { personSalesService.findByLeadId(person.leadId!!) } returns personSalesInfo
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { dealSalesInfoService.findByLastOpportunityId(person.opportunityId!!) } returns null
        coEvery {
            dealSalesInfoService.add(
                match { it.personId == newDealSalesInfo.personId && it.lastOpportunityId == person.opportunityId && it.hubspotDealId == newDealSalesInfo.hubspotDealId })
        } returns newDealSalesInfo.success()
        coEvery { personSalesService.update(newPersonSalesInfo) } returns newPersonSalesInfo.success()

        dealProgressService.moveDealToNoShow(schedule)

        coVerifyOnce { onboardingService.findByPerson(any()) }
        coVerifyOnce { personSalesService.findByLeadId(any()) }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { dealSalesInfoService.findByLastOpportunityId(any()) }
        coVerifyOnce { dealSalesInfoService.add(any()) }
        coVerifyOnce { personSalesService.update(any()) }
        coVerifyNone { salesCrmPipeline.updateDeal(any(), any()) }
    }

    private fun mountPersonChanged(): Pair<Deal, DealSalesInfo> {
        val dealSalesInfo =
            DealSalesInfo(
                simulationId = RangeUUID.generate(),
                lastOpportunityId = person.opportunityId,
                hubspotDealId = "899"
            )
        coEvery { dealSalesInfoService.findByLastOpportunityId(dealSalesInfo.lastOpportunityId!!) }

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { dealSalesInfoService.findByLastOpportunityId(person.opportunityId!!) } returns dealSalesInfo
        coEvery { salesCrmPipeline.updateDeal(any(), any()) } returns DealResult("id")

        val deal = Deal(
            email = person.email,
            dateOfBirth = person.dateOfBirth,
            nationalId = person.nationalId,
            name = "${person.firstName} ${person.lastName}",
            firstName = person.firstName,
            lastName = person.lastName,
            phoneNumber = person.phoneNumber,
            isInternal = person.tags?.contains("internal")
        )

        coEvery { salesCrmPipeline.updateDeal(dealSalesInfo.hubspotDealId!!, deal) } returns DealResult("id")

        return Pair(deal, dealSalesInfo)

    }

    private suspend fun assertHubspotDealMove(
        expectedStage: DealStage,
        withDealSalesInfo: Boolean? = false,
        block: suspend () -> Unit
    ) {
        var hubspotDealId = personSalesInfo.hubspotDealId!!
        if (withDealSalesInfo == true) {
            val dealSalesInfo =
                DealSalesInfo(
                    simulationId = RangeUUID.generate(),
                    lastOpportunityId = person.opportunityId,
                    hubspotDealId = "899"
                )
            hubspotDealId = dealSalesInfo.hubspotDealId!!
            coEvery { dealSalesInfoService.findByLastOpportunityId(dealSalesInfo.lastOpportunityId!!) } returns dealSalesInfo
        } else {
            val newDealSalesInfo =
                DealSalesInfo(
                    personId = person.id,
                    lastOpportunityId = person.opportunityId,
                    hubspotDealId = personSalesInfo.hubspotDealId
                )
            val newPersonSalesInfo = personSalesInfo.copy(hubspotDealId = null)
            coEvery { dealSalesInfoService.findByLastOpportunityId(person.opportunityId!!) } returns null
            coEvery { personSalesService.findByLeadId(person.leadId!!) } returns personSalesInfo
            coEvery {
                dealSalesInfoService.add(
                    match { it.personId == newDealSalesInfo.personId && it.lastOpportunityId == person.opportunityId && it.hubspotDealId == newDealSalesInfo.hubspotDealId })
            } returns newDealSalesInfo.success()
            coEvery { personSalesService.update(newPersonSalesInfo) } returns newPersonSalesInfo.success()
        }

        coEvery { personService.get(person.id) } returns person.success()

        block()

        if (withDealSalesInfo == true) {
            coVerifyOnce { dealSalesInfoService.findByLastOpportunityId(any()) }
        } else {
            coVerifyOnce { dealSalesInfoService.findByLastOpportunityId(any()) }
            coVerifyOnce { personSalesService.findByLeadId(any()) }
            coVerifyOnce { dealSalesInfoService.add(any()) }
            coVerifyOnce { personSalesService.update(any()) }
        }

        coVerifyOnce { personService.get(any()) }
    }
}
