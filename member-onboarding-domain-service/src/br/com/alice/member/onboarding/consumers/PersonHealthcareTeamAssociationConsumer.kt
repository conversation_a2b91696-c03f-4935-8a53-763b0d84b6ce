package br.com.alice.member.onboarding.consumers

import br.com.alice.clinicalaccount.event.PersonHealthcareTeamAssociationUpdatedEvent
import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus.COMPLETED
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.COVER_MFC
import br.com.alice.member.onboarding.client.MemberOnboardingService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success

class PersonHealthcareTeamAssociationConsumer(
    private val memberOnboardingService: MemberOnboardingService
) : Spannable, Consumer() {

    suspend fun completeCoverMfcOnboardingStep(event: PersonHealthcareTeamAssociationUpdatedEvent) =
        withSubscribersEnvironment {
            span("completeCoverMfcOnboardingStep") { span ->
                span.setAttribute("health_care_team_id", event.payload.newHealthcareTeamId.toString())

                event.payload.newPersonClinicalAccount?.let { personClinicalAccount ->
                    memberOnboardingService.getByPersonId(personClinicalAccount.personId).flatMap { memberOnboarding ->
                        span.setAttribute("member_onboarding_id", memberOnboarding.id.toString())
                        span.setAttribute("member_onboarding_steps", memberOnboarding.steps.toString())

                        if (!memberOnboarding.isCoverMFCStepCompleted()) {
                            memberOnboardingService.updateStepStatus(
                                memberOnboardingId = memberOnboarding.id,
                                stepType = COVER_MFC,
                                stepStatus = COMPLETED
                            )
                        } else false.success()
                    }
                } ?: false.success()
            }
        }
}
