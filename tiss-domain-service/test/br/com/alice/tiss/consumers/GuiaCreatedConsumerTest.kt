package br.com.alice.tiss.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.events.GuiaCreatedEvent
import br.com.alice.exec.indicator.events.GuiaCreatedPayload
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.exec.indicator.models.RequesterData
import br.com.alice.tiss.consumers.converters.GuiaCreatedRequestBuilder
import br.com.alice.tiss.models.CreateGuiaRequest
import br.com.alice.tiss.services.internal.GuiaService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GuiaCreatedConsumerTest : ConsumerTest() {
    @AfterTest
    fun setup() = clearAllMocks()

    private val guiaService: GuiaService = mockk()
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService = mockk()
    private val builder: GuiaCreatedRequestBuilder = mockk()
    private val totvsGuiaService = mockk<TotvsGuiaService>()

    private val consumer = GuiaCreatedConsumer(guiaService, builder, mvAuthorizedProcedureService, totvsGuiaService)

    val totvsGuia = TestModelFactory.buildTotvsGuia()

    private val payload = GuiaCreatedPayload(
        totvsGuia = totvsGuia,
        procedures = emptyList(),
        beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        origin = GuiaOrigin.EITA
    )
    private val createGuiaHospitalizationRequest = CreateGuiaRequest(
        procedures = listOf(
            GuiaProcedure(table = "98", code = "123", quantity = 1, description = ""),
            GuiaProcedure(table = "98", code = "545", quantity = 1, description = "")
        ),
        requester = RequesterData(name = "Alice Operadora", providerCode = "00000000000"),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE,
            phoneNumber = "(11) 1111-1111",
        ),
        guiaNumber = "983732412324",
        requestDate = LocalDate.now(),
        beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        ansCode = "1234",
        authorizationType = "1",
        attendanceCharacter = "1",
        origin = GuiaOrigin.EHR,
        attendanceRegime = "1",
        accidentIndication = "1",
    )

    private val event = GuiaCreatedEvent(payload)
    private val eventWithExecutor = GuiaCreatedEvent(payload.copy(executorCnpj = "123"))

    private val mvAuthorizedProcedureExecuted = TestModelFactory.buildMvAuthorizedProcedure(
        guiaExecutionCode = "123",
        status = MvAuthorizedProcedureStatus.EXECUTED
    )

    private val mvAuthorizedProcedureNotExecuted = TestModelFactory.buildMvAuthorizedProcedure(
        status = MvAuthorizedProcedureStatus.AUTHORIZED
    )

    @Test
    fun `#handle GuiaCreatedEvent should process event`() = runBlocking {
        coEvery {
            guiaService.createGuiaExam(any())
        } returns true.success()

        coEvery {
            builder.build(any(), any(), any(), any(), any())
        } returns createGuiaHospitalizationRequest.success()

        coEvery {
            totvsGuiaService.get(totvsGuia.id)
        } returns totvsGuia.success()

        val result = consumer.handle(event)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { guiaService.createGuiaExam(any()) }
    }

    @Test
    fun `#handle GuiaCreatedEvent should avoid processing event when guide already has external code`() = runBlocking {
        coEvery {
            builder.build(any(), any(), any(), any(), any())
        } returns createGuiaHospitalizationRequest.success()

        coEvery {
            totvsGuiaService.get(totvsGuia.id)
        } returns totvsGuia.copy(externalCode = "hasExternalCode").success()

        val result = consumer.handle(event)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyNone { guiaService.createGuiaExam(any()) }
    }

    @Test
    fun `#handle GuiaCreatedEvent should process event when is a execution`() = runBlocking {

        coEvery {
            guiaService.createGuiaExam(any())
        } returns true.success()

        coEvery {
            builder.build(any(), any(), any(), any(), any())
        } returns createGuiaHospitalizationRequest.success()

        coEvery {
            mvAuthorizedProcedureService.findByTotvsGuiaId(any())
        } returns listOf(mvAuthorizedProcedureNotExecuted).success()

        val result = consumer.handle(eventWithExecutor)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { guiaService.createGuiaExam(any()) }
        coVerifyOnce { mvAuthorizedProcedureService.findByTotvsGuiaId(any()) }
    }

    @Test
    fun `#handle GuiaCreatedEvent should not process event when is a execution and there is already an execution for guide and procedure being executed is already executed`() = runBlocking {
        coEvery {
            mvAuthorizedProcedureService.findByTotvsGuiaId(any())
        } returns listOf(mvAuthorizedProcedureExecuted, mvAuthorizedProcedureNotExecuted).success()

        val event = GuiaCreatedEvent(payload.copy(
            executorCnpj = "123",
            procedures = listOf(mvAuthorizedProcedureExecuted)
        ))

        val result = consumer.handle(event)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyNone { guiaService.createGuiaExam(any()) }
        coVerifyOnce { mvAuthorizedProcedureService.findByTotvsGuiaId(any()) }
    }

    @Test
    fun `#handle GuiaCreatedEvent should process event when is a execution and there is already an execution for guide and procedure being executed is not executed`() = runBlocking {
        coEvery {
            mvAuthorizedProcedureService.findByTotvsGuiaId(any())
        } returns listOf(mvAuthorizedProcedureExecuted, mvAuthorizedProcedureNotExecuted).success()

        coEvery {
            guiaService.createGuiaExam(any())
        } returns true.success()

        coEvery {
            builder.build(any(), any(), any(), any(), any())
        } returns createGuiaHospitalizationRequest.success()

        val event = GuiaCreatedEvent(payload.copy(
            executorCnpj = "123",
            procedures = listOf(mvAuthorizedProcedureNotExecuted)
        ))

        val result = consumer.handle(event)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { guiaService.createGuiaExam(any()) }
        coVerifyOnce { mvAuthorizedProcedureService.findByTotvsGuiaId(any()) }
    }
}
