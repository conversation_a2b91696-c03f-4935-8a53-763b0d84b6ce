package br.com.alice.data.layer.policies

import br.com.alice.data.layer.MEMBER_WANNABE_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.subjects.Unauthenticated

val memberWannabePolicySet = policySet {
    match("at member-wannabe", { rootService.name == MEMBER_WANNABE_API_ROOT_SERVICE_NAME }) {
        match("guest user", { subject is Unauthenticated }) {
            match("can count", { action is Count }) {
                match("any Person") { resourceIs(PersonModel::class) }
                match("any StaffModel") { resourceIs(StaffModel::class) }
                match("any HealthProfessionalModel") { resourceIs(HealthProfessionalModel::class) }
                match("any HealthCommunitySpecialistModel") { resourceIs(HealthCommunitySpecialistModel::class) }
                match("any ProviderUnit") { resourceIs(ProviderUnitModel::class) }
                match("any StructuredAddress") { resourceIs(StructuredAddress::class) }
                match("any ContactModel") { resource is ContactModel }
                match("any Provider") { resourceIs(ProviderModel::class) }
                match("any ProductRecommendation") { resourceIs(ProductRecommendation::class)}
                match("any ConsolidatedAccreditedNetwork") { resourceIs(ConsolidatedAccreditedNetwork::class)}
            }
            match("can view", { action is View }) {
                match("any Product") { resource is ProductModel }
                match("any Person Sales") { resource is PersonSalesInfo }
                match("any Product Bundle") { resource is ProductBundleModel }
                match("any PromoCodeModel") { resource is PromoCodeModel }
                match("any public FeatureConfigModel") { resource is FeatureConfigModel && resource.isPublic }
                match("any Product Price Listing") { resource is ProductPriceListingModel }
                match("any Price Listing") { resource is PriceListingModel }
                match("any Provider") { resource is ProviderModel }
                match("any ProviderUnit") { resource is ProviderUnitModel }
                match("any StructuredAddress") { resource is StructuredAddress }
                match("any ContactModel") { resource is ContactModel }
                match("any ProviderUnitGroup") { resource is ProviderUnitGroupModel }
                match("any StaffModel") { resource is StaffModel }
                match("any HealthProfessionalModel") { resourceIs(HealthProfessionalModel::class) }
                match("any MedicalSpecialty") { resource is MedicalSpecialtyModel }
                match("any HealthcareTeamModel") { resource is HealthcareTeamModel }
                match("any HealthCommunitySpecialistModel") { resource is HealthCommunitySpecialistModel }
                match("any ProductRecommendation") { resource is ProductRecommendation }
                match("any CoveredGeoRegion") { resource is CoveredGeoRegionModel }
                match("any ConsolidatedAccreditedNetwork") { resource is ConsolidatedAccreditedNetwork }
            }

            match("can view and update and create", { action is Update || action is View || action is Create }) {
                match("any PersonModel") { resource is PersonModel }

                match("any PersonModel Login") { resource is PersonLoginModel }

                match("any Product Order") { resource is ProductOrderModel }
                match("any PersonModel Onboarding") { resource is PersonOnboardingModel }

                match("any Lead") { resource is Lead }
                match("any Health Declaration") { resource is HealthDeclaration }
                match("any Person Onboarding") { resource is PersonOnboardingModel }
                match("any Person Registration") { resource is PersonRegistrationModel }
                match("any Insurance Portability") { resource is InsurancePortabilityRequestModel }
                match("any HealthProductSimulation") { resource is HealthProductSimulation }
                match("any ShoppingCartModel") { resource is ShoppingCartModel }
                match("any Opportunity") { resource is Opportunity }
                match("any HealthProductSimulationGroup") { resource is HealthProductSimulationGroup }
                match("any UpdatedPersonContactInfoTempModel") { resource is UpdatedPersonContactInfoTempModel }
                match("any ZipcodeAddress") { resource is ZipcodeAddress }
                match("any CoveredGeoRegion") { resource is CoveredGeoRegionModel }
            }
            allows(VicProductOption::class, View, Create, Update, Count, Delete)
            allows(SiteAccreditedNetwork::class, View, Count)
            allows(SiteAccreditedNetworkProvider::class, View, Count)
            allows(SiteAccreditedNetworkAddress::class, View, Count)
            allows(SiteAccreditedNetworkCategory::class, View, Count)
            allows(SiteAccreditedNetworkFlagship::class, View, Count)
        }
    }
}
