package br.com.alice.nullvs.consumers.client

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.nullvs.consumers.ConsumerTest
import br.com.alice.nullvs.events.NullvsClientActivatedEvent
import br.com.alice.nullvs.events.NullvsClientWebhookReceivedEvent
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.Method
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.Status
import br.com.alice.nullvs.models.NullvsClientWebhookReceived
import br.com.alice.nullvs.models.TotvsStatus
import br.com.alice.nullvs.services.internals.NullvsIntegrationLogService
import br.com.alice.nullvs.services.internals.NullvsIntegrationRecordService
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.BeforeTest
import kotlin.test.Test

class NullvsClientWebhookReceivedConsumerTest : ConsumerTest() {
    private val nullvsIntegrationLogService: NullvsIntegrationLogService = mockk()
    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val consumer =
        NullvsClientWebhookReceivedConsumer(
            nullvsIntegrationLogService,
            nullvsIntegrationRecordService,
            kafkaProducerService,
        )

    val nullvsClientWebhookReceived = NullvsClientWebhookReceived(
        idSoc = "1",
        batchId = "00001",
        status = TotvsStatus.SUCCESS,
        posBatchPayload = 1,
        type = BatchType.CREATE,
        dateStamp = "2023-01-01T00:00:00",
        payload = NullvsClientWebhookReceived.Payload(
            internalModelType = InternalModelType.BILLING_ACCOUNTABLE_PARTY,
            internalId = RangeUUID.generate(),
            clientCode = "123",
            store = "123",
            cgc = "123",
            clientName = "Cliente nome",
        )
    )

    @BeforeTest
    fun setup() {
        super.before()
        mockkObject(TotvsIntegrationMetric)
    }

    @Nested
    inner class ProcessIntegratedClientAtTotvs {
        @Test
        fun `#should add nullvsIntegrationRecord and update nullvsIntegrationLog as expected when totvsStatus is success`() =
            runBlocking {
                val webhook = nullvsClientWebhookReceived.copy(status = TotvsStatus.SUCCESS)
                val event = NullvsClientWebhookReceivedEvent(webhook)
                val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(
                    batchType = BatchType.CREATE,
                    internalModelName = InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                )
                val nullvsIntegrationRecord = webhook.toNullvsIntegrationRecord(nullvsIntegrationLog.internalId)
                val updateNullvsIntegrationLog = nullvsIntegrationLog.copy(status = LogStatus.FINISHED)

                coEvery {
                    kafkaProducerService.produce(match<NullvsClientActivatedEvent> {
                        it.payload.billingAccountablePartyId == webhook.payload.internalId!!
                                && it.payload.externalId == webhook.payload.clientCode!!
                    })
                } returns mockk()

                coEvery {
                    nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                        any(),
                        any(),
                        any(),
                    )
                } returns nullvsIntegrationLog
                coEvery { nullvsIntegrationRecordService.add(any()) } returns nullvsIntegrationRecord
                coEvery { nullvsIntegrationLogService.update(any()) } returns updateNullvsIntegrationLog

                val result = consumer.processIntegratedClientAtTotvs(event)

                ResultAssert.assertThat(result).isSuccessWithData(updateNullvsIntegrationLog)

                coVerifyOnce {
                    nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                        webhook.batchId,
                        webhook.idSoc,
                        webhook.posBatchPayload,
                    )
                }
                coVerifyOnce {
                    nullvsIntegrationRecordService.add(match {
                        it.internalId == nullvsIntegrationRecord.internalId && it.internalModelName == nullvsIntegrationRecord.internalModelName
                    })
                    kafkaProducerService.produce(match<NullvsClientActivatedEvent> {
                        it.payload.billingAccountablePartyId == webhook.payload.internalId!!
                                && it.payload.externalId == webhook.payload.clientCode!!
                    })
                }
                coVerifyOnce { nullvsIntegrationLogService.update(updateNullvsIntegrationLog) }
                coVerifyOnce { TotvsIntegrationMetric.countNullvsWebhookConsumer(Method.CLIENT, Status.SUCCESS) }
            }

        @Test
        fun `#should add nullvsIntegrationRecord and update nullvsIntegrationLog as expected when totvsStatus is success - nullvs log not exists`() =
            runBlocking {
                val webhook = nullvsClientWebhookReceived.copy(status = TotvsStatus.SUCCESS)
                val event = NullvsClientWebhookReceivedEvent(webhook)
                val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(
                    batchType = BatchType.CREATE,
                    internalModelName = InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                )
                val nullvsIntegrationRecord = webhook.toNullvsIntegrationRecord(nullvsIntegrationLog.internalId)
                val updateNullvsIntegrationLog = nullvsIntegrationLog.copy(status = LogStatus.FINISHED)

                coEvery {
                    nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                        any(),
                        any(),
                        any(),
                    )
                } returns NotFoundException("")

                coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsIntegrationLog
                coEvery { nullvsIntegrationRecordService.add(any()) } returns nullvsIntegrationRecord
                coEvery { nullvsIntegrationLogService.update(any()) } returns updateNullvsIntegrationLog

                coEvery {
                    kafkaProducerService.produce(match<NullvsClientActivatedEvent> {
                        it.payload.billingAccountablePartyId == webhook.payload.internalId!!
                                && it.payload.externalId == webhook.payload.clientCode!!
                    })
                } returns mockk()

                val result = consumer.processIntegratedClientAtTotvs(event)

                ResultAssert.assertThat(result).isSuccessWithData(true)

                coVerifyOnce {
                    nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                        webhook.batchId,
                        webhook.idSoc,
                        webhook.posBatchPayload,
                    )

                    kafkaProducerService.produce(match<NullvsClientActivatedEvent> {
                        it.payload.billingAccountablePartyId == webhook.payload.internalId!!
                                && it.payload.externalId == webhook.payload.clientCode!!
                    })

                    nullvsIntegrationLogService.add(any())

                    nullvsIntegrationRecordService.add(match {
                        it.internalId == nullvsIntegrationRecord.internalId && it.internalModelName == nullvsIntegrationRecord.internalModelName
                    })

                    nullvsIntegrationLogService.update(updateNullvsIntegrationLog)

                    TotvsIntegrationMetric.countNullvsWebhookConsumer(Method.CLIENT, Status.SUCCESS)
                }
            }

        @Test
        fun `#should add nullvsIntegrationRecord and update nullvsIntegrationLog as expected when totvsStatus is feilure - nullvs log not exists`() =
            runBlocking {
                val webhook = nullvsClientWebhookReceived.copy(status = TotvsStatus.FAILURE)
                val event = NullvsClientWebhookReceivedEvent(webhook)
                val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(batchType = BatchType.CREATE)
                val nullvsIntegrationRecord = webhook.toNullvsIntegrationRecord(nullvsIntegrationLog.internalId)
                val updateNullvsIntegrationLog = nullvsIntegrationLog.copy(status = LogStatus.FINISHED)

                coEvery {
                    nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                        any(),
                        any(),
                        any(),
                    )
                } returns NotFoundException("")

                val result = consumer.processIntegratedClientAtTotvs(event)

                ResultAssert.assertThat(result).isSuccessWithData(true)

                coVerifyOnce {
                    nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                        webhook.batchId,
                        webhook.idSoc,
                        webhook.posBatchPayload,
                    )

                    TotvsIntegrationMetric.countNullvsWebhookConsumer(Method.CLIENT, Status.FAILURE)
                }

                coVerifyNone {
                    nullvsIntegrationLogService.add(any())

                    nullvsIntegrationRecordService.add(match {
                        it.internalId == nullvsIntegrationRecord.internalId && it.internalModelName == nullvsIntegrationRecord.internalModelName
                    })

                    nullvsIntegrationLogService.update(updateNullvsIntegrationLog)
                    kafkaProducerService.produce(any<NullvsClientActivatedEvent>())
                }

            }

        @Test
        fun `#should add nullvsIntegrationRecord and update nullvsIntegrationLog as expected when totvsStatus is success even when record is already created`() =
            runBlocking {
                val webhook = nullvsClientWebhookReceived.copy(status = TotvsStatus.SUCCESS)
                val event = NullvsClientWebhookReceivedEvent(webhook)
                val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(
                    batchType = BatchType.CREATE,
                    internalModelName = InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                )
                val nullvsIntegrationRecord = webhook.toNullvsIntegrationRecord(nullvsIntegrationLog.internalId)
                val updateNullvsIntegrationLog = nullvsIntegrationLog.copy(status = LogStatus.FINISHED)

                coEvery {
                    nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                        any(),
                        any(),
                        any(),
                    )
                } returns nullvsIntegrationLog
                coEvery { nullvsIntegrationRecordService.add(any()) } returns DuplicatedItemException("")
                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        any()
                    )
                } returns nullvsIntegrationRecord
                coEvery { nullvsIntegrationLogService.update(any()) } returns updateNullvsIntegrationLog
                coEvery {
                    kafkaProducerService.produce(match<NullvsClientActivatedEvent> {
                        it.payload.billingAccountablePartyId == webhook.payload.internalId!!
                                && it.payload.externalId == webhook.payload.clientCode!!
                    })
                } returns mockk()

                val result = consumer.processIntegratedClientAtTotvs(event)

                ResultAssert.assertThat(result).isSuccessWithData(updateNullvsIntegrationLog)

                coVerifyOnce {
                    nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                        webhook.batchId,
                        webhook.idSoc,
                        webhook.posBatchPayload,
                    )
                    kafkaProducerService.produce(match<NullvsClientActivatedEvent> {
                        it.payload.billingAccountablePartyId == webhook.payload.internalId!!
                                && it.payload.externalId == webhook.payload.clientCode!!
                    })
                }
                coVerifyOnce {
                    nullvsIntegrationRecordService.add(match {
                        it.internalId == nullvsIntegrationRecord.internalId && it.internalModelName == nullvsIntegrationRecord.internalModelName
                    })
                }
                coVerifyOnce {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        nullvsIntegrationRecord.internalId,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY
                    )
                }
                coVerifyOnce { nullvsIntegrationLogService.update(updateNullvsIntegrationLog) }
                coVerifyOnce { TotvsIntegrationMetric.countNullvsWebhookConsumer(Method.CLIENT, Status.SUCCESS) }
            }

        @Test
        fun `#should update nullvsIntegrationLog as expected when totvsStatus is failure`() =
            runBlocking {
                val webhook = nullvsClientWebhookReceived.copy(status = TotvsStatus.FAILURE)
                val event = NullvsClientWebhookReceivedEvent(webhook)
                val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(batchType = BatchType.CREATE)
                val updateNullvsIntegrationLog = nullvsIntegrationLog.copy(status = LogStatus.FAILURE)

                coEvery {
                    nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                        any(),
                        any(),
                        any(),
                    )
                } returns nullvsIntegrationLog
                coEvery { nullvsIntegrationLogService.update(any()) } returns updateNullvsIntegrationLog

                val result = consumer.processIntegratedClientAtTotvs(event)

                ResultAssert.assertThat(result).isSuccessWithData(updateNullvsIntegrationLog)

                coVerifyOnce {
                    nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                        webhook.batchId,
                        webhook.idSoc,
                        webhook.posBatchPayload,
                    )
                }
                coVerifyOnce { nullvsIntegrationLogService.update(updateNullvsIntegrationLog) }
                coVerifyOnce { TotvsIntegrationMetric.countNullvsWebhookConsumer(Method.CLIENT, Status.SUCCESS) }
            }

    }

    companion object {
        @JvmStatic
        fun updateNullvsIntegrationLogDueToTotvsStatus() = listOf(
            arrayOf(TotvsStatus.SUCCESS, LogStatus.FINISHED),
            arrayOf(TotvsStatus.FAILURE, LogStatus.FAILURE),
        )
    }

    @ParameterizedTest(name = "should update nullvsIntegrationLog to {1} as expected when totvsStatus is {0}")
    @MethodSource("updateNullvsIntegrationLogDueToTotvsStatus")
    fun `#should update nullvsIntegrationLog as expected when totvsStatus is success or failure`(
        totvsStatus: TotvsStatus,
        logStatus: LogStatus
    ) =
        runBlocking {
            val webhook = nullvsClientWebhookReceived.copy(status = totvsStatus)
            val event = NullvsClientWebhookReceivedEvent(webhook)
            val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(batchType = BatchType.UPDATE)
            val updateNullvsIntegrationLog = nullvsIntegrationLog.copy(status = logStatus)

            coEvery {
                nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                    any(),
                    any(),
                    any(),
                )
            } returns nullvsIntegrationLog

            coEvery { nullvsIntegrationLogService.update(any()) } returns updateNullvsIntegrationLog

            val result = consumer.processIntegratedClientAtTotvs(event)

            ResultAssert.assertThat(result).isSuccessWithData(updateNullvsIntegrationLog)

            coVerifyOnce {
                nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                    webhook.batchId,
                    webhook.idSoc,
                    webhook.posBatchPayload,
                )
            }
            coVerifyOnce { nullvsIntegrationLogService.update(updateNullvsIntegrationLog) }
            coVerifyOnce { TotvsIntegrationMetric.countNullvsWebhookConsumer(Method.CLIENT, Status.SUCCESS) }
        }
}
