package br.com.alice.api.healthcareops.usecases

import br.com.alice.api.healthcareops.converters.business.CompanyConverter.toCompanyInvoiceResponse
import br.com.alice.api.healthcareops.models.CompanyInvoiceResponse
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PortalUrlGeneratorService
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class GetCompanyInvoicesUseCase(
    private val companyService: CompanyService,
    private val memberInvoiceGroupService: MemberInvoiceGroupService,
    private val invoicePaymentService: InvoicePaymentService,
    private val billingAccountablePartyService: BillingAccountablePartyService,
    private val companySubcontractService: CompanySubContractService,
    private val companyContractService: CompanyContractService,
    private val portalUrlGeneratorService: PortalUrlGeneratorService
) {

    suspend fun getByBillingAccountableParty(companyId: UUID, billingAccountablePartyId: UUID): CompanyInvoiceResponse =
        coroutineScope {
            val companyDeferred = async { companyService.get(companyId) }

            val invoiceGroupsDeferred = async {
                memberInvoiceGroupService.getByBillingAccountablePartyId(billingAccountablePartyId)
            }
            val invoicePaymentsDeferred = async {
                invoicePaymentService.listInvoicePaymentsByBillingAccountablePartyId(
                    billingAccountablePartyId,
                    withPaymentDetails = true
                )
                    .map { invoicePayments ->
                        invoicePayments.filter { it.invoiceGroupId != null }
                    }
            }
            val billingAccountablePartyDeferred =
                async { billingAccountablePartyService.get(billingAccountablePartyId) }

            val invoiceGroups = invoiceGroupsDeferred.await().get()
            val billingAccountableParty = billingAccountablePartyDeferred.await().get()
            val invoicePayments = invoicePaymentsDeferred.await().get()
            val company = companyDeferred.await().get()
            val invoicePaymentsWithPortalUrlsDeferred =
                async { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(invoicePayments).get() }

            val invoicePaymentsWithPortalUrls = invoicePaymentsWithPortalUrlsDeferred.await()

            return@coroutineScope company.toCompanyInvoiceResponse(
                billingAccountableParty,
                invoiceGroups,
                invoicePayments,
                invoicePaymentsWithPortalUrls
            )
        }

    suspend fun getBySubcontract(companyId: UUID, subcontractId: UUID): CompanyInvoiceResponse = coroutineScope {

        val subcontract = companySubcontractService.get(subcontractId).get()
        val invoiceGroups = memberInvoiceGroupService.getBySubcontractId(subcontractId).get()

        val invoicePaymentsDeferred = async {
            invoicePaymentService.getByInvoiceGroupIds(
                invoiceGroups.map { it.id },
                withPaymentDetails = true
            )
                .map { invoicePayments ->
                    invoicePayments.filter { it.invoiceGroupId != null }
                }
        }
        val billingAccountablePartyDeferred =
            async {
                subcontract.billingAccountablePartyId?.let {
                    billingAccountablePartyService.get(it)
                } ?: getBillingAccountablePartyByContract(subcontract)
            }
        val companyDeferred = async { companyService.get(companyId) }

        val billingAccountableParty = billingAccountablePartyDeferred.await()?.get()
            ?: throw NotFoundException("BillingAccountableParty not found for subcontractId: $subcontractId or contractId: ${subcontract.contractId}")
        val invoicePayments = invoicePaymentsDeferred.await().get()
        val company = companyDeferred.await().get()

        val invoicePaymentsWithPortalUrlsDeferred =
            async { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(invoicePayments).get() }

        val invoicePaymentsWithPortalUrls = invoicePaymentsWithPortalUrlsDeferred.await()

        return@coroutineScope company.toCompanyInvoiceResponse(
            billingAccountableParty,
            invoiceGroups,
            invoicePayments,
            invoicePaymentsWithPortalUrls
        )
    }

    private suspend fun getBillingAccountablePartyByContract(companySubcontract: CompanySubContract) =
        companyContractService.get(companySubcontract.contractId)
            .map { companyContract ->
                companyContract.billingAccountablePartyId?.let { billingAccountablePartyId ->
                    billingAccountablePartyService.get(billingAccountablePartyId)
                }
            }.get()
}

