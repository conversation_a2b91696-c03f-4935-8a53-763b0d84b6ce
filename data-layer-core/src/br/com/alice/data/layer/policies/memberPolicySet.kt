package br.com.alice.data.layer.policies

import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.models.ExternalHealthInformation
import br.com.alice.common.models.HealthInformation
import br.com.alice.data.layer.MEMBERSHIP_ENVIRONMENT_BACKFILL
import br.com.alice.data.layer.MEMBERSHIP_SUBSCRIBERS_ROOT_SERVICE_NAME
import br.com.alice.data.layer.MEMBERSHIP_WEBHOOKS_ROOT_SERVICE_NAME
import br.com.alice.data.layer.MEMBER_API_SUBSCRIBERS_ROOT_SERVICE_NAME
import br.com.alice.data.layer.MEMBER_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.pipelines.subjects.PersonSubject
import br.com.alice.data.layer.policies.features.accessScreening
import br.com.alice.data.layer.policies.features.notifyChannelNewMessage
import br.com.alice.data.layer.policies.features.refund
import br.com.alice.data.layer.policies.features.viewBeneficiary
import br.com.alice.data.layer.policies.features.viewCompany
import br.com.alice.data.layer.policies.features.viewPersonWithKey
import br.com.alice.data.layer.subjects.Unauthenticated

val memberPolicySet = policySet {
    // applied to member-api and membership-domain-service,
    // because MEMBER_ROOT_SERVICE_NAME has the same value than MEMBERSHIP_ROOT_SERVICE_NAME
    match("at member-api", { rootService.name == MEMBER_ROOT_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            can(View) {
                resources(
                    InvoicePaymentModel::class,
                    BolepixPaymentDetailModel::class,
                    BoletoPaymentDetailModel::class,
                    PixPaymentDetailModel::class,
                    SimpleCreditCardPaymentDetailModel::class,
                    PaymentDetailModel::class,
                    BillingAccountablePartyModel::class
                )

            }

            match("can count", { action is Count }) {
                match("any PersonModel") { resourceIs(PersonModel::class) }
            }
            match("can view", { action is View }) {
                match("PersonModel doing login") {
                    resource is PersonModel &&
                            subject is Unauthenticated &&
                            (resource.id.toString() == subject.key || resource.nationalId == subject.key)
                }
                match("Her membership") {
                    resource is MemberModel &&
                            subject is Unauthenticated &&
                            subject.key.toPersonId() == resource.personId
                }

                match("Any Beneficiary") {
                    resource is BeneficiaryModel && subject is Unauthenticated
                }

                match("Any BeneficiaryOnboarding") {
                    resource is BeneficiaryOnboardingModel && subject is Unauthenticated
                }

                match("Any UpdateAppRuleModel") {
                    resource is UpdateAppRuleModel && subject is Unauthenticated
                }

                match("Any BeneficiaryOnboardingPhase") {
                    resource is BeneficiaryOnboardingPhaseModel && subject is Unauthenticated
                }

                match("her DeviceModel") {
                    resource is DeviceModel &&
                            subject is Unauthenticated &&
                            resource.personId.toString() == subject.key
                }

                match("any Lead") { resource is Lead }
                match("any Opportunity") { resource is Opportunity }
                match("any HealthProductSimulation") { resource is HealthProductSimulation }
                match("any MemberProductChangeScheduleModel") { resource is MemberProductChangeScheduleModel }

                includes(viewPersonWithKey)
                includes(notifyChannelNewMessage)
            }
            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("any LegalGuardianInfoTemp") { resourceIs(LegalGuardianInfoTempModel::class) }

                match("PersonModel login attempting to login with") {
                    resource is PersonLoginModel &&
                            subject is Unauthenticated &&
                            (resource.nationalId == subject.key ||
                                    resource.email == subject.key || resource.personId.toString() == subject.key)
                }
                match("her PersonModel Internal Reference") {
                    resource is PersonInternalReference &&
                            subject is Unauthenticated &&
                            resource.personId.toString() == subject.key
                }
                match("any PersonModel Onboarding") {
                    resource is PersonOnboardingModel &&
                            subject is Unauthenticated
                }
                match("her PersonModel") {
                    resource is PersonModel &&
                            subject is Unauthenticated &&
                            (resource.nationalId == subject.key
                                    || resource.email == subject.key || resource.id.toString() == subject.key)
                }
                match("her HealthDeclaration") {
                    resource is HealthDeclaration &&
                            subject is Unauthenticated &&
                            resource.personId.toString() == subject.key
                }
                match("her PersonRegistrationModel") {
                    resource is PersonRegistrationModel &&
                            subject is Unauthenticated &&
                            resource.personId.toString() == subject.key
                }
            }

            allows(ZendeskExternalReference::class, View)
        }

        match("PersonModel", { subject is PersonSubject }) {
            allows(AppointmentScheduleOptionModel::class, Count)
            allows(MemberOnboardingCheckpoint::class, View, Count, Create, Update)
            allows(GenerateExternalAttendancePa::class, View, Count, Create, Update)
            allows(AppointmentScheduleModel::class, Count, View)
            allows(AppContentScreenDetail::class, Count)
            allows(HealthCommunitySpecialistModel::class, Count)
            allows(MemberOnboarding::class, View, Count, Create, Update)
            allows(HealthConditionGroup::class, View)
            allows(BeneficiaryModel::class, Count)
            allows(PersonClinicalAccount::class, View, Create, Update)
            includes(accessScreening)
            match("can view", { action is View }) {
                match("her own PersonModel") { herOwn(PersonModel::class) }
                match("her own MemberModel") { herOwn(MemberModel::class) }
                match("her dependent PersonModel") { herDependent(PersonModel::class) }
                match("her parent PersonModel") {
                    resource is PersonModel && subject is PersonSubject && resource.id == subject.parentPerson?.id
                }
                match("her parent MemberModel") {
                    resource is MemberModel && subject is PersonSubject && resource.personId == subject.parentPerson?.id
                }
                match("her legal guardian PersonModel") { herLegalGuardian(PersonModel::class) }
                match("her dependent MemberModel") { herDependent(MemberModel::class) }
                match("her own Health Informations") { herOwn(HealthInformation::class) }
                match("her own Scheduled Appointments") { herOwn(AppointmentScheduleModel::class) }
                match("her own Appointment Coordination") { herOwn(AppointmentCoordination::class) }
                match("her own Invoices") { herOwn(MemberInvoiceModel::class) }
                match("her own PersonClinicalAccount") { herOwn(PersonClinicalAccount::class) }
                match("her LaboratoryTestResultModel") { herOwn(LaboratoryTestResultModel::class) }
                match("her External Health Information") { herOwn(ExternalHealthInformation::class) }
                match("her own health Events ") { herOwn(PersonHealthEvent::class) }
                match("her own CassiMember") { herOwn(CassiMemberModel::class) }
                match("her own TestResultFeedback") { herOwn(TestResultFeedback::class) }
                match("her own AliceTestResultBundle") { herOwn(AliceTestResultBundle::class) }
                match("her own Risk") { herOwn(Risk::class) }
                match("any Invoice Payment") { resource is InvoicePaymentModel }
                match("any Boleto Payment Detail") { resource is BoletoPaymentDetailModel }
                match("any Simple Credit Card Payment Detail") { resource is SimpleCreditCardPaymentDetailModel }
                match("any Pix Payment Detail") { resource is PixPaymentDetailModel }
                match("any Bolepix Payment Detail") { resource is BolepixPaymentDetailModel }
                match("any InvoiceLiquidation") { resource is InvoiceLiquidationModel }
                match("any ChannelFup") { resource is ChannelFup }
                match("any PromoCodeModel") { resource is PromoCodeModel }
                match("any Provider") { resource is ProviderModel }
                match("any ProviderUnit") { resource is ProviderUnitModel }
                match("any ConsolidatedAccreditedNetwork") { resource is ConsolidatedAccreditedNetwork }
                match("any ConsolidatedRating") { resource is ConsolidatedRating }
                match("any StructuredAddress") { resource is StructuredAddress }
                match("any ContactModel") { resource is ContactModel }
                match("any HealthCommunitySpecialistModel") { resource is HealthCommunitySpecialistModel }
                match("any HealthcareTeamModel") { resource is HealthcareTeamModel }
                match("any StaffModel") { resource is StaffModel }
                match("any HealthProfessionalModel") { resource is HealthProfessionalModel }
                match("any CassiSpecialist") { resource is CassiSpecialistModel }
                match("any ProviderUnitTestCode") { resource is ProviderUnitTestCodeModel }
                match("any MedicalSpecialty") { resource is MedicalSpecialtyModel }
                match("any Product") { resource is ProductModel }
                match("any Product Bundle") { resource is ProductBundleModel }
                match("any ProviderTestCode") { resource is ProviderTestCodeModel }
                match("any Provider") { resource is ProviderModel }
                match("any ProviderUnit") { resource is ProviderUnitModel }
                match("any StructuredAddress") { resource is StructuredAddress }
                match("any ContactModel") { resource is ContactModel }
                match("any Alice Agora Working Hours") { resource is AliceAgoraWorkingHours }
                match("any Appointment Schedule Options") { resource is AppointmentScheduleOptionModel }
                match("any Health Goal") { resource is HealthGoalModel }
                match("any TestCode") { resource is TestCodeModel }
                match("any TestPreparationModel") { resource is TestPreparationModel }
                match("any Health Form") { resource is HealthForm }
                match("any Health Form Section") { resource is HealthFormSection }
                match("any Health Form Question") { resource is HealthFormQuestion }
                match("any Healthcare Team Recommendation") { resource is HealthcareTeamRecommendationModel }
                match("any Healthcare Team Recommendation Rule") { resource is HealthcareTeamRecommendationRuleModel }
                match("any Healthcare Team Recommendation Rule To Recommendation") { resource is HealthcareTeamRecommendationRuleToRecommendationModel }
                match("any FaqGroupModel") { resource is FaqGroupModel }
                match("any FaqContentModel") { resource is FaqContentModel }
                match("any HealthCondition") { resource is HealthCondition }
                match("any PriceListing") { resource is PriceListingModel }
                match("any ProductPriceListing") { resource is ProductPriceListingModel }
                match("any MemberProductPrice") { resource is MemberProductPriceModel }
                match("any BillingAccountableParty") { resource is BillingAccountablePartyModel }
                match("any HealthMeasurementTypeModel") { resource is HealthMeasurementTypeModel }
                match("any FeatureConfigModel") { resource is FeatureConfigModel }
                match("any InsurancePortabilityHealthInsurance") { resource is InsurancePortabilityHealthInsuranceModel }
                match("any Company") { resource is CompanyModel }
                match("any Beneficiary") { resource is BeneficiaryModel }
                match("any BeneficiaryOnboarding") { resource is BeneficiaryOnboardingModel }
                match("any BeneficiaryOnboardingPhase") { resource is BeneficiaryOnboardingPhaseModel }
                match("any StaffScheduleModel") { resource is StaffScheduleModel }
                match("any ExternalCalendarEventModel") { resource is ExternalCalendarEventModel }
                match("any HealthProductSimulation") { resource is HealthProductSimulation }
                match("any AppointmentScheduleEventTypeModel") { resource is AppointmentScheduleEventTypeModel }
                match("any EventTypeProviderUnitModel") { resource is EventTypeProviderUnitModel }
                match("any AppointmentScheduleEventTypeDateExceptionModel") { resource is AppointmentScheduleEventTypeDateExceptionModel }
                match("any LegalGuardianInfoTemp") { resource is LegalGuardianInfoTempModel }
                match("any MemberOnboardingTemplate") { resource is MemberOnboardingTemplate }
                match("any MemberOnboardingAction") { resource is MemberOnboardingAction }
                match("any MemberOnboardingStep") { resource is MemberOnboardingStep }
                match("any UpdateAppRuleModel") { resource is UpdateAppRuleModel }
                match("her own MemberOnboardingCheckpoint") { herOwn(MemberOnboardingCheckpoint::class) }
                match("any OutcomeConf") { resource is OutcomeConf }
                match("any PersonPreferencesModel") { resource is PersonPreferencesModel }
                match("any PersonBillingAccountableParty") { resource is PersonBillingAccountablePartyModel }
                match("any InvoicePayment") { resource is InvoicePaymentModel }
                match("any MemberInvoice") { resource is MemberInvoiceModel }
                match("any EmergencyRecommendation") { resource is EmergencyRecommendation }
                match("any CoPaymentCostInfo") { resource is CoPaymentCostInfoModel }
                match("any RefundCostInfo") { resource is RefundCostInfoModel }
                match("any CompanyRefundCostInfo") { resource is CompanyRefundCostInfoModel }
                match("any GenericFileVault") { resource is GenericFileVault }
                match("any HealthcareResource") { resource is HealthcareResourceModel }
                match("any CsatTemplate") { resource is CsatTemplate }
                match("any Health Plan Task Template") { resource is HealthPlanTaskTemplate }
                match("any AIAssistant") { resource is AIAssistant }
                match("her own TotvsGuia") {
                    resource is TotvsGuiaModel && subject is PersonSubject && resource.personId == subject.id
                }
                match("her own MvAuthorizedProcedure") { herOwn(MvAuthorizedProcedureModel::class) }
                match("her own ExternalAppointmentScheduleModel") { herOwn(ExternalAppointmentScheduleModel::class) }
                match("MemberInvoiceGroup for which he is responsible") {
                    resource is MemberInvoiceGroupModel &&
                            subject is PersonSubject &&
                            (resource.billingAccountablePartyId == subject.billingAccountablePartyId)
                }
                match("her own SeviceScriptNavigationGroup") { herOwn(ServiceScriptNavigationGroup::class) }
                match("her own PersonGracePeriod") { herOwn(PersonGracePeriod::class) }
            }
            match("can view and create", { action is View || action is Create }) {
                match("her own File") { herOwn(FileVault::class) }
                match("her dependents File") { herDependent(FileVault::class) }
                match("her own Channel") { herOwn(Channel::class) }
            }
            match("can create and update", { action is Create || action is Update }) {
                match("any LegalGuardianInfoTemp") { resource is LegalGuardianInfoTempModel }
                match("Invoice Payment for which he is responsible") {
                    resource is InvoicePaymentModel &&
                            subject is PersonSubject &&
                            (resource.billingAccountablePartyId == subject.billingAccountablePartyId)
                }
                match("her own Health Events") {
                    resource is PersonHealthEvent &&
                            subject is PersonSubject &&
                            resource.staffId == null &&
                            (resource.personId == subject.id)
                }
            }
            match("can view and update", { action is View || action is Update }) {
                match("any LegalGuardianInfoTemp") { resource is LegalGuardianInfoTempModel }
                match("her own VideoCall") { herOwn(VideoCall::class) }
                match("her own MemberContract") { herOwn(MemberContractModel::class) }
                match("her own MemberContractTermModel") { herOwn(MemberContractTermModel::class) }
                match("her dependent MemberContract") { herDependent(MemberContractModel::class) }
                match("her dependent MemberContractTermModel") { herDependent(MemberContractTermModel::class) }
                match("her own AppContentScreenDetail") { herOwn(AppContentScreenDetail::class) }
                match("her own AppointmentScheduleCheckInModel") { herOwn(AppointmentScheduleCheckInModel::class) }
            }
            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("herself") { herself() }
                match("her own PersonModel tasks") { herOwn(PersonTaskModel::class) }
                match("her own DeviceModel") { herOwn(DeviceModel::class) }
                match("her own Membership") { herOwn(MemberModel::class) }
                match("her own Health Declaration") { herOwn(HealthDeclaration::class) }
                match("her dependent Health Declaration") { herDependent(HealthDeclaration::class) }
                match("her Health Plan Task Group") { herOwn(HealthPlanTaskGroup::class) }
                match("her Health Plan Tasks") { herOwn(HealthPlanTask::class) }
                match("her Action Plan Tasks") { herOwn(ActionPlanTask::class) }
                match("her Demand Action Plans") { herOwn(DemandActionPlan::class) }
                match("her own PersonModel Internal Reference") { herOwn(PersonInternalReference::class) }
                match("her own PersonModel Onboarding") { herOwn(PersonOnboardingModel::class) }
                match("her own PersonModel Registration") { herOwn(PersonRegistrationModel::class) }
                match("her own Product Order") { herOwn(ProductOrderModel::class) }
                match("her own Onboarding Contract") { herOwn(OnboardingContractModel::class) }
                match("her own Health Goals") { herOwn(PersonHealthGoalModel::class) }
                match("her own Healthcare Recommendation") { herOwn(PersonHealthcareTeamRecommendationModel::class) }
                match("her own TestResultFileModel") { herOwn(TestResultFileModel::class) }
                match("her own Portability Request") { herOwn(InsurancePortabilityRequestModel::class) }
                match("her own Portability Request File") { herOwn(InsurancePortabilityRequestFileModel::class) }
                match("her own LegalGuardianInfoTemp") { herOwn(LegalGuardianInfoTempModel::class) }
                match("her Shopping Cart") {
                    resource is ShoppingCartModel &&
                            subject is PersonSubject &&
                            resource.leadId == subject.person.leadId
                }
                match("her own Health Form Question Answer") { herOwn(HealthFormQuestionAnswer::class) }
                match("her own Health Form Answer Group") { herOwn(HealthFormAnswerGroup::class) }
                match("her own Health Plan") { herOwn(HealthPlan::class) }
                match("her own Clinical Background") { herOwn(ClinicalBackground::class) }
                match("her own Health Measurement") { herOwn(HealthMeasurementModel::class) }
                match("her FaqFeedbackModel") {
                    resource is FaqFeedbackModel &&
                            subject is PersonSubject &&
                            (resource.personId == null || resource.personId == subject.id)
                }
                match("her own Additional Information") { herOwn(PersonAdditionalInfoModel::class) }
                match("any opportunity") { resource is Opportunity }
                match("her appointment schedules") { herOwn(AppointmentScheduleModel::class) }
                match("her Lead") {
                    resource is Lead && subject is PersonSubject &&
                            (resource.id == subject.person.leadId || resource.email == subject.person.email)
                }
                match("her own PersonDocumentsUploadModel") { herOwn(PersonDocumentsUploadModel::class) }
                match("her own PersonBenefitModel") { herOwn(PersonBenefitModel::class) }
                match("her own PersonCalendlyModel") { herOwn(PersonCalendlyModel::class) }
                match("her own trackAbPerson") { herOwn(TrackPersonABModel::class) }
                match("her own AppointmentScheduleModel") { herOwn(AppointmentScheduleModel::class) }
                match("any BeneficiaryOnboarding") { resource is BeneficiaryOnboardingModel }
                match("any BeneficiaryOnboardingPhase") { resource is BeneficiaryOnboardingPhaseModel }
                match("her own FollowUpHistory") { herOwn(FollowUpHistory::class) }
                match("her own MemberOnboardingCheckpoint") { herOwn(MemberOnboardingCheckpoint::class) }
                match("her own PersonEligibilityDuquesa") { herOwn(PersonEligibilityDuquesa::class) }
                includes(refund)
                match("her own Csat") { herOwn(Csat::class) }
            }
            match("can count", { action is Count }) {
                match("any Health Form Section") { resourceIs(HealthFormSection::class) }
                match("any Health Form Question") { resourceIs(HealthFormQuestion::class) }
                match("any Health Form Question Answer") { resourceIs(HealthFormQuestionAnswer::class) }
                match("any LegalGuardianInfoTemp") { resourceIs(LegalGuardianInfoTempModel::class) }
                match("any ClinicalOutcomeRecord") { resourceIs(ClinicalOutcomeRecord::class) }
                match("her Action Plan Tasks") { resourceIs(ActionPlanTask::class) }
                match("her Demand Action Plans") { resourceIs(DemandActionPlan::class) }
            }
            match("can create", { action is Create }) {
                match("any LegalGuardianInfoTemp") { resourceIs(LegalGuardianInfoTempModel::class) }
                match("any AbTestSpecialistRecommendation") { resource is AbTestSpecialistRecommendation }
            }
            match("can delete", { action is Delete }) {
                match("her own Health Form Question Answer") { herOwn(HealthFormQuestionAnswer::class) }
            }
            can(View, Create, Update) {
                match("her own PersonIdentityValidationModel") { herOwn(PersonIdentityValidationModel::class) }
            }
            can(View, Create, Update, Delete) {
                match("her own AccreditedNetworkFavorite") { herOwn(AccreditedNetworkFavorite::class) }
            }
        }
    }

    // applied to member-api
    match("at member-api-subscribers", { rootService.name == MEMBER_API_SUBSCRIBERS_ROOT_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(viewBeneficiary)
            allows(ActionPlanTask::class, Count, View)
            match("can view", { action is View }) {
                match("any UpdateAppRuleModel") { resourceIs(UpdateAppRuleModel::class) }
                match("any OngoingCompanyDeal") { resourceIs(OngoingCompanyDeal::class) }
                match("any DeviceModel") { resourceIs(DeviceModel::class) }
            }
            match("can view, create and update", { action is View || action is Create || action is Update }) {
                match("any MemberOnboarding") { resourceIs(MemberOnboarding::class) }
            }
        }
    }

    // applied to member-api
    match("at member-api-background-process", { rootService.name == MEMBER_API_SUBSCRIBERS_ROOT_SERVICE_NAME }) {
        allows(Risk::class, View)
        allows(PersonModel::class, View)
        allows(MemberOnboardingTemplate::class, View)
        allows(MemberModel::class, View)
        allows(HealthDeclaration::class, View)
    }

    // applied to member-api and membership-domain-service
    match("at membership-domain-service webhooks", { rootService.name == MEMBERSHIP_WEBHOOKS_ROOT_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            match("can view", { action is View }) {
                match("any PersonModel") { resource is PersonModel }
            }

            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("any Onboarding Background Check") { resource is OnboardingBackgroundCheckModel }
            }

            match("can view and update update", { action is View || action is Update || action is Create }) {
                match("any PersonModel Identity Validation") { resource is PersonIdentityValidationModel }
            }

            match("can view and delete", { action is View || action is Delete }) {
                match("any identity_validation image") {
                    resource is FileVault &&
                            resource.domain == "person" &&
                            resource.namespace == "identity_validation"
                }
            }
        }
    }

    // applied to membership-domain-service
    match(
        "at membership-domain-service subscribers",
        { rootService.name == MEMBERSHIP_SUBSCRIBERS_ROOT_SERVICE_NAME }) {

        allows(HealthPlanTaskStatusHistoryModel::class, Create)

        includes(viewBeneficiary)

        allows(AppointmentScheduleEventTypeModel::class, View)
        allows(EventTypeProviderUnitModel::class, View)
        allows(MedicalSpecialtyModel::class, View)
        allows(ZendeskExternalReference::class, View)

        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(viewCompany)
        }

        match("PersonModel", { subject is PersonModel }) {
            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("her own Health Declaration") { herOwn(HealthDeclaration::class) }
                match("her dependent Health Declaration") { herDependent(HealthDeclaration::class) }
            }
        }
        match("can view", { action is View }) {
            match("her DeviceModel") {
                resource is DeviceModel &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }

            match("her Health Declaration") {
                resource is HealthDeclaration &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }

            match("her PersonLoginModel") {
                resource is PersonLoginModel &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }

            match("her PersonInternalReference") {
                resource is PersonInternalReference &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }

            match("any Promo Code") {
                resource is PromoCodeModel
            }

            match("her Appointment Schedule") {
                resource is AppointmentScheduleModel &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }

            match("any Lead") { resource is Lead }
            match("any Product") { resource is ProductModel }
            match("any ProductBundle") { resource is ProductBundleModel }
            match("any PriceListing") { resource is PriceListingModel }
            match("any ProductPriceListing") { resource is ProductPriceListingModel }
            match("any StaffModel") { resource is StaffModel }
            match("any HealthProductSimulation") { resource is HealthProductSimulation }
            match("any HealthProductSimulationGroup") { resource is HealthProductSimulationGroup }
            match("any HealthCondition") { resource is HealthCondition }
            match("any ProductPriceAdjustment") { resource is ProductPriceAdjustmentModel }
            match("any MemberModel") { resource is MemberModel }

            match("her ClinicalBackground") {
                resource is ClinicalBackground &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }

            match("her Appointment") {
                resource is Appointment &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }
        }

        match("can view and update and create", { action is Update || action is View || action is Create }) {
            match("her Onboarding Contract") {
                resource is OnboardingContractModel &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }

            match("her Promo Code") {
                resource is PromoCodeModel &&
                        subject is Unauthenticated &&
                        resource.ownerPersonId.toString() == subject.key
            }

            match("her PersonModel Onboarding") { resource is PersonOnboardingModel }

            match("her MV MemberModel Registration") {
                resource is MemberRegistrationModel &&
                        subject is Unauthenticated &&
                        resource.personId == subject.key
            }

            match("any PersonModel") { resource is PersonModel }
            match("any PersonModel Sales") { resource is PersonSalesInfo }
            match("any Deal Sales") { resource is DealSalesInfo }
            match("any MemberProductPriceModel") { resource is MemberProductPriceModel }
            match("any MemberProductPriceAdjustmentModel") { resource is MemberProductPriceAdjustmentModel }
            match("any CassiMember") { resource is CassiMemberModel }
            match("any MemberAccreditedNetworkTrackerModel") { resource is MemberAccreditedNetworkTrackerModel }

            match("any Onboarding Background Check") {
                resource is OnboardingBackgroundCheckModel &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }

            match("her MemberModel") {
                resource is MemberModel &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }

            match("her PersonRegistrationModel") {
                resource is PersonRegistrationModel &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }

            match("her PersonLoginModel") {
                resource is PersonLoginModel &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }

            match("her Product Order") {
                resource is ProductOrderModel &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }
            match("her Health Declaration") {
                resource is HealthDeclaration &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }
            match("her InsurancePortabilityRequest") {
                resource is InsurancePortabilityRequestModel &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }
            match("her InsurancePortabilityRequestFile") {
                resource is InsurancePortabilityRequestFileModel &&
                        subject is Unauthenticated &&
                        resource.personId.toString() == subject.key
            }
            match("can view and create", { action is View || action is Create }) {
                match("any term document") {
                    resource is FileVault &&
                            resource.domain == "member" &&
                            resource.namespace == "term" &&
                            subject is Unauthenticated
                }
            }
            match("can view and update and create", { action is View || action is Create }) {
                match("any MemberContractTermModel") {
                    resource is MemberContractTermModel &&
                            subject is Unauthenticated
                }
            }

            //TODO: This policy is broken due to missing && on condition, just keeping until migration is done to map resources
            match("can view and update", { action is View || action is Update }) {
                match("any member documents") {
                    resource is FileVault &&
                            resource.domain == "member" &&
                            resource.namespace == "documents"
                    subject is Unauthenticated
                }
            }
        }
    }

    // applied to membership-domain-service
    at(MEMBERSHIP_ENVIRONMENT_BACKFILL) {
        who(Unauthenticated::class) {
            can(View) { resources(PersonModel::class) }
            can(Create, View) {
                resources(
                    HealthDeclaration::class,
                    PersonRegistrationModel::class,
                    PersonOnboardingModel::class
                )
            }
            can(Update, View) { resources(PromoCodeModel::class, PersonOnboardingModel::class) }
        }
    }

    describe("Policy for backfill selectedProduct.type field") {
        // applied to membership-domain-service
        at(MEMBERSHIP_ENVIRONMENT_BACKFILL) {
            who(Unauthenticated::class) {
                can(Update) {
                    resources(MemberModel::class)
                }
                can(View, Count) {
                    resources(ProductModel::class, MemberModel::class)
                }
            }
        }
    }

    describe("Policy for backfill selectedProduct.type field of OnboadingContract") {
        // applied to membership-domain-service
        at(MEMBERSHIP_ENVIRONMENT_BACKFILL) {
            who(Unauthenticated::class) {
                can(Update) {
                    resources(OnboardingContractModel::class)
                }
                can(View, Count) {
                    resources(ProductModel::class, OnboardingContractModel::class)
                }
            }
        }
    }

    describe("Policy for backfill update person") {
        // applied to membership-domain-service
        at(MEMBERSHIP_ENVIRONMENT_BACKFILL) {
            who(Unauthenticated::class) {
                can(View, Count, Update) {
                    resources(PersonModel::class)
                }
            }
        }
    }

    describe("Policy for backfill create contract document") {
        // applied to membership-domain-service
        at(MEMBERSHIP_ENVIRONMENT_BACKFILL) {
            who(Unauthenticated::class) {
                can(Update) {
                    resources(OnboardingContractModel::class)
                }
                can(View, Count) {
                    resources(
                        PersonModel::class,
                        PersonOnboardingModel::class,
                        ProductModel::class,
                        OnboardingContractModel::class,
                        ProductOrderModel::class,
                        ProductBundleModel::class,
                        ProductPriceListingModel::class,
                        PriceListingModel::class,
                        ProviderModel::class,
                        InsurancePortabilityRequestModel::class,
                        HealthDeclaration::class
                    )
                }
            }
        }
    }

    describe("Policy for backfill force member activation") {
        // applied to membership-domain-service
        at(MEMBERSHIP_ENVIRONMENT_BACKFILL) {
            who(Unauthenticated::class) {
                can(View, Count) {
                    resources(
                        BeneficiaryModel::class,
                        CompanyModel::class,
                        CompanyContractModel::class,
                    )
                }
            }
        }
    }

    describe("Policy for backfill get contract terms") {
        // applied to membership-domain-service
        at(MEMBERSHIP_ENVIRONMENT_BACKFILL) {
            who(Unauthenticated::class) {
                can(View, Count) {
                    resources(
                        MemberContractModel::class,
                        MemberContractTermModel::class,
                    )
                    match("any contract terms") {
                        resource is FileVault &&
                                resource.domain == "member" &&
                                resource.namespace == "term"
                        subject is Unauthenticated
                    }
                }
            }
        }
    }

    describe("Policy for backfill update beneficiary subcontract") {
        // applied to membership-domain-service
        at(MEMBERSHIP_ENVIRONMENT_BACKFILL) {
            who(Unauthenticated::class) {
                can(View, Count, Update) {
                    resources(
                        BeneficiaryModel::class,
                    )
                }
            }
        }
    }

    describe("Policy for backfill copy CPTs") {
        // applied to membership-domain-service
        at(MEMBERSHIP_ENVIRONMENT_BACKFILL) {
            who(Unauthenticated::class) {
                can(View, Count) {
                    resources(
                        PersonGracePeriod::class,
                        HealthDeclaration::class,
                        HealthCondition::class
                    )
                }

                can(Update) {
                    resources(HealthDeclaration::class)
                }
            }
        }
    }
}
