package br.com.alice.exec.indicator.service

import br.com.alice.common.MvUtil
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthInstitutionNegotiation
import br.com.alice.data.layer.services.HealthInstitutionNegotiationModelDataService
import br.com.alice.exec.indicator.client.HealthInstitutionNegotationFilters
import br.com.alice.exec.indicator.client.HealthInstitutionNegotiationService
import br.com.alice.exec.indicator.client.HealthcareBundleService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.converters.modelConverters.toTransport
import br.com.alice.exec.indicator.exceptions.ProviderUnitGroupNotFoundByProviderUnit
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.provider.client.ProviderUnitGroupService
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import java.time.LocalDate
import java.util.UUID

class HealthInstitutionNegotiationServiceImpl(
    private val data: HealthInstitutionNegotiationModelDataService,
    private val healthcareBundleService: HealthcareBundleService,
    private val providerUnitService: ProviderUnitService,
    private val providerUnitGroupService: ProviderUnitGroupService
) : HealthInstitutionNegotiationService {

    companion object {
        const val DEFAULT_BUNDLE_TABLE = "98"
    }

    override suspend fun get(id: UUID) = data.get(id).map {
        it.toTransport()
    }

    override suspend fun getByExternalId(externalId: String) = useReadDatabase {
        data.findOne { where { fieldOptions.externalId.eq(externalId) } }.map {
            it.toTransport()
        }
    }

    override suspend fun add(model: HealthInstitutionNegotiation) =
        this.upsert(model) { data.add(it.toModel()).map {
            it.toTransport()
        }.get() }

    override suspend fun update(model: HealthInstitutionNegotiation) =
        this.upsert(model) { data.update(it.toModel()).map {
            it.toTransport()
        }.get() }

    private suspend fun upsert(
        model: HealthInstitutionNegotiation,
        action: suspend (HealthInstitutionNegotiation) -> HealthInstitutionNegotiation
    ) = coResultOf<HealthInstitutionNegotiation, Throwable> {
        if (model.tableType == DEFAULT_BUNDLE_TABLE) {
            val bundle = healthcareBundleService
                .getByCodeAndProviderUnitGroup(model.code, model.providerUnitGroupId)
                .getOrNullIfNotFound()

            action(model.copy(bundlePrimaryTuss = bundle?.primaryTuss ?: model.bundlePrimaryTuss))
        } else action(model)
    }

    override suspend fun addMany(models: List<HealthInstitutionNegotiation>) =
        coResultOf<List<HealthInstitutionNegotiation>, Throwable> { models.pmap { this.add(it).get() } }

    override suspend fun updateMany(models: List<HealthInstitutionNegotiation>) =
        coResultOf<List<HealthInstitutionNegotiation>, Throwable> { models.pmap { this.update(it).get() } }

    override suspend fun findByHealthcareResourceId(healthcareResourceId: UUID) = useReadDatabase {
        data.find { where { this.healthcareResourceId.eq(healthcareResourceId) } }.map {
            it.toTransport()
        }
    }

    override suspend fun list(filters: HealthInstitutionNegotationFilters) = useReadDatabase {
        logger.info("HealthInstitutionNegotiationServiceImpl::list", "filters" to filters)
        filters.build().flatMap {
            data.find {
                where { predicate(it) }
                    .orderBy { description }
                    .sortOrder { asc }
                    .offset { filters.offset }
                    .limit { filters.limit }
            }.map {
                it.toTransport()
            }
        }
    }

    @OptIn(OrPredicateUsage::class)
    override suspend fun findByCodeAndProviderUnitGroup(
        code: String,
        providerUnitGroupId: UUID,
    ) = useReadDatabase {
        data.find {
            where {
                this.code.eq(code)
                    .and(this.providerUnitGroupId.eq(providerUnitGroupId))
                    .and(this.validAfter.lessEq(LocalDate.now()))
                    .and(scope(this.validBefore.isNull().or(this.validBefore.greater(LocalDate.now()))))
            }
        }.map {
            it.toTransport()
        }
    }

    private fun predicate(filters: HealthInstitutionNegotationFilters) =
        HealthInstitutionNegotiationModelDataService.FieldOptions().activeHealthcareResource.eq(true)
            .andQueryPredicate(filters.query)
            .andProviderUnitGroupIdPredicate(filters.providerUnitGroupId)
            .andValidAfterPredicate(filters.validAfter)
            .andValidBeforePredicate(filters.validBefore)
            .andFilterPSCodes(filters.guiaType)

    private fun Predicate.andQueryPredicate(query: String?): Predicate {
        logger.info(
            "HealthInstitutionNegotiationServiceImpl::list::andQueryPredicate",
            "query" to query
        )
        if (query.isNullOrBlank()) return this
        return this.and(HealthInstitutionNegotiationModelDataService.FieldOptions().searchTokens.search(query))
    }

    private fun Predicate.andProviderUnitGroupIdPredicate(providerUnitGroupId: UUID?): Predicate {
        logger.info(
            "HealthInstitutionNegotiationServiceImpl::list::andProviderUnitGroupIdPredicate",
            "providerUnitGroupId" to providerUnitGroupId
        )
        if (providerUnitGroupId == null) return this
        return this.and(
            HealthInstitutionNegotiationModelDataService.FieldOptions().providerUnitGroupId.eq(providerUnitGroupId)
        )
    }

    private fun Predicate.andValidAfterPredicate(validAfter: LocalDate?): Predicate {
        logger.info(
            "HealthInstitutionNegotiationServiceImpl::list::andValidAfterPredicate",
            "validAfter" to validAfter
        )
        if (validAfter == null) return this.and(
            HealthInstitutionNegotiationModelDataService.FieldOptions().validAfter.lessEq(LocalDate.now())
        )

        return this.and(HealthInstitutionNegotiationModelDataService.FieldOptions().validAfter.lessEq(validAfter))
    }

    @OptIn(OrPredicateUsage::class)
    private fun Predicate.andValidBeforePredicate(validBefore: LocalDate?): Predicate {
        logger.info(
            "HealthInstitutionNegotiationServiceImpl::list::andValidBeforePredicate",
            "validBefore" to validBefore
        )
        if (validBefore == null) {
            return this.and(
                scope(
                    HealthInstitutionNegotiationModelDataService.FieldOptions().validBefore.greater(LocalDate.now())
                        .or(HealthInstitutionNegotiationModelDataService.FieldOptions().validBefore.isNull())
                )
            )
        }
        return this.and(HealthInstitutionNegotiationModelDataService.FieldOptions().validBefore.greater(validBefore))
    }

    private fun Predicate.andFilterPSCodes(searchGuiaType: MvUtil.TISS?): Predicate {
        logger.info(
            "HealthInstitutionNegotiationServiceImpl::list::andFilterPSCodes",
            "searchGuiaType" to searchGuiaType
        )

        val emergencyCodes = FeatureService.getList(
            namespace = FeatureNamespace.EXEC_INDICATOR,
            key = "emergency_room_procedures",
            defaultValue = listOf("10101039", "98450001", "98450002", "98450003", "98450005")
        )

        if (searchGuiaType != MvUtil.TISS.PS) {
            return this.and(HealthInstitutionNegotiationModelDataService.FieldOptions().code.notInList(emergencyCodes))
        }

        return this.and(HealthInstitutionNegotiationModelDataService.FieldOptions().code.inList(emergencyCodes))
    }

    private suspend fun HealthInstitutionNegotationFilters.build() =
        coResultOf<HealthInstitutionNegotationFilters, Throwable> {
            if (providerUnitId == null) return@coResultOf this
            val providerUnit = providerUnitService.get(providerUnitId!!).get()
            val providerUnitGroupId = providerUnit.providerUnitGroupId
                ?: providerUnit.cnpj?.let { providerUnitGroupService.getByCnpj(it).get().id }
                ?: throw ProviderUnitGroupNotFoundByProviderUnit(providerUnitId!!)

            logger.info(
                "HealthInstitutionNegotiationServiceImpl::list::build",
                "providerUnitId" to providerUnitId,
                "providerUnitGroupId" to providerUnitGroupId
            )

            this.copy(providerUnitGroupId = providerUnitGroupId)
        }
}
