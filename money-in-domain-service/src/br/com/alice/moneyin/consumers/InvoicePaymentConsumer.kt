package br.com.alice.moneyin.consumers

import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.foldNotFound
import br.com.alice.common.extensions.money
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.moneyin.client.InvoiceLiquidationService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PreActivationPaymentService
import br.com.alice.moneyin.communication.InvoiceMailer
import br.com.alice.moneyin.event.InvoicePaymentApprovedEvent
import br.com.alice.moneyin.event.InvoicePaymentCreatedEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.math.BigDecimal

class InvoicePaymentConsumer(
    private val invoicesService: InvoicesService,
    private val paymentService: InvoicePaymentService,
    private val memberInvoiceGroupService: MemberInvoiceGroupService,
    private val invoiceLiquidationService: InvoiceLiquidationService,
    private val preActivationPaymentService: PreActivationPaymentService,
    private val invoiceMailer: InvoiceMailer
) : Consumer() {

    suspend fun sendEmailToNotifyPayment(event: InvoicePaymentApprovedEvent) = withSubscribersEnvironment {
        logger.info("InvoicePaymentApprovedEvent received - sendPinpoint", "event" to event)

        val invoicePayment = event.payload

        invoiceMailer.sendInvoicePaidEmail(invoicePayment)
            .then {
                logger.info("Email for invoice paid sent successfully", "invoice_payment_id" to invoicePayment.id)
            }
            .thenError {
                logger.error(
                    "Fail during invoice paid email sent",
                    "invoice_payment_id" to invoicePayment.id
                )
            }
    }

    suspend fun closeInvoice(event: InvoicePaymentApprovedEvent) = withSubscribersEnvironment {
        logger.info("InvoicePaymentApprovedEvent received - closeInvoice", "event" to event)

        val invoicePayment = event.payload
        val memberInvoices = invoicesService.findInvoicesByIds(invoicePayment.memberInvoiceIds).get()

        areInvoicesPaid(memberInvoices, invoicePayment).map { isInvoicePaid ->
            if (isInvoicePaid) {
                memberInvoices.map {
                    invoicesService.markAsPaid(it.id, invoicePayment.approvedAt)
                }
            }
        }
    }

    suspend fun emitMemberInvoiceGroup(event: InvoicePaymentApprovedEvent) = withSubscribersEnvironment {
        logger.info("InvoicePaymentApprovedEvent received - emitMemberInvoiceGroup", "event" to event)

        val invoicePayment = event.payload
        val memberInvoiceGroupId = invoicePayment.invoiceGroupId
        if (memberInvoiceGroupId != null)
            memberInvoiceGroupService.get(memberInvoiceGroupId)
                .flatMap { memberInvoiceGroupService.markAsPaid(it, invoicePayment) }
        else
            true.success()
    }

    suspend fun emitInvoiceLiquidation(event: InvoicePaymentApprovedEvent) = withSubscribersEnvironment {
        logger.info("InvoicePaymentApprovedEvent received - emitInvoiceLiquidationGroup", "event" to event)

        val invoicePayment = event.payload
        val invoiceLiquidationId = invoicePayment.invoiceLiquidationId
        invoiceLiquidationId?.let { id ->
            invoiceLiquidationService.get(id)
                .flatMap { invoiceLiquidationService.markAsPaid(it, invoicePayment) }
        } ?: true.success()
    }

    suspend fun emitInvoicePreActivationPayment(event: InvoicePaymentApprovedEvent) = withSubscribersEnvironment {
        logger.info("InvoicePaymentApprovedEvent received - emitInvoicePreActivationPayment", "event" to event)

        val invoicePayment = event.payload
        val preActivationPaymentId = invoicePayment.preActivationPaymentId
        preActivationPaymentId?.let { id ->
            preActivationPaymentService.get(id)
                .flatMap { preActivationPaymentService.markAsPaid(it, invoicePayment) }
                .thenError {
                    logger.error(
                        "InvoicePaymentConsumer::emitInvoicePreActivationPayment - something was wrong when tried to mark this payment as paid",
                        "pre_activation_payment_id" to id,
                        "billing_accountable_party_id" to invoicePayment.billingAccountablePartyId,
                        "error_message" to it.message,
                        "error" to it,
                    )
                }
        } ?: true.success()
    }

    private suspend fun areInvoicesPaid(
        invoices: List<MemberInvoice>,
        payment: InvoicePayment
    ): Result<Boolean, Throwable> {
        if (areInvoicesPaid(invoices, payment.amount)) return true.success()

        return invoices
            .flatMap { invoice -> paymentService.listInvoicePayments(invoice.id).get() }
            .distinctBy { it.id }
            .filter { it.isApproved }
            .sumOf { it.amount }
            .let { paidAmount -> areInvoicesPaid(invoices, paidAmount).success() }
    }

    private fun areInvoicesPaid(invoices: List<MemberInvoice>, paidAmount: BigDecimal): Boolean =
        paidAmount.money >= invoices.sumOf { it.totalAmount }.money

    suspend fun associateMemberInvoiceGroup(event: InvoicePaymentCreatedEvent) = withSubscribersEnvironment {
        val invoicePayment = event.payload.invoicePayment

        if (invoicePayment.invoiceGroupId == null && invoicePayment.invoiceLiquidationId == null && invoicePayment.memberInvoiceId != null) {
            logger.info(
                "Trying to associate a member invoice group to invoice payment",
                "invoice_payment_id" to invoicePayment.id,
                "member_invoice_id" to invoicePayment.memberInvoiceId,
            )

            memberInvoiceGroupService.getByMemberInvoices(invoicePayment.memberInvoiceId!!)
                .flatMap { memberInvoiceGroup ->
                    logger.info(
                        "A member invoice group is found",
                        "member_invoice_group_id" to memberInvoiceGroup.id
                    )

                    paymentService.update(
                        invoicePayment.associateGroup(memberInvoiceGroup)
                    )
                        .then { logger.info("The invoice payment was updated to its member invoice group") }
                        .coFoldNotFound {
                            paymentService.get(invoicePayment.id, withPaymentDetails = false)
                                .flatMap {
                                    paymentService.update(
                                        it.associateGroup(memberInvoiceGroup)
                                    )
                                }
                        }
                }.foldNotFound {
                    logger.info(
                        "A member invoice group is not exists",
                        "invoice_payment_id" to invoicePayment.id,
                        "member_invoice_id" to invoicePayment.memberInvoiceId,
                    )

                    false.success()
                }
        } else {
            true.success()
        }
    }

}
