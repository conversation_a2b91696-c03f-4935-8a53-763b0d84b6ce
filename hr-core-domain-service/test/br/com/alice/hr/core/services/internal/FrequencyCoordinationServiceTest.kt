package br.com.alice.hr.core.services.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InternalServiceErrorException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.hr.core.clients.interfaces.DataAPIClient
import br.com.alice.hr.core.clients.FrequencyCoordinationDataResponse
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals

class FrequencyCoordinationServiceTest {
    private val dataAPIClient: DataAPIClient = mockk()

    private val frequencyCoordinationDataService = FrequencyCoordinationDataService(
        dataAPIClient,
    )
    private val companyId = RangeUUID.generate()
    private val frequencyCoordinationDataResponse = FrequencyCoordinationDataResponse(
        coordinationRate = 2.5F,
        frequency = FrequencyCoordinationDataResponse.Frequency(
            aa = FrequencyCoordinationDataResponse.FrequencyItem(
                totalEvents = 1,
                uniqueMembers = 2,
                historic = listOf(
                    FrequencyCoordinationDataResponse.Historic(
                        value = 1.0F,
                        benchmark = 2.0F,
                        period = "2021-01-02",
                        activeMembers = 10,
                    )
                )
            ),
            exams = FrequencyCoordinationDataResponse.FrequencyItem(
                totalEvents = 1,
                uniqueMembers = 2,
                historic = listOf(
                    FrequencyCoordinationDataResponse.Historic(
                        value = 1.0F,
                        benchmark = 2.0F,
                        period = "2021-01-02",
                        activeMembers = 10,
                    )
                )
            ),
            familyDoctor = FrequencyCoordinationDataResponse.FrequencyItem(
                totalEvents = 1,
                uniqueMembers = 2,
                historic = listOf(
                    FrequencyCoordinationDataResponse.Historic(
                        value = 1.0F,
                        benchmark = 2.0F,
                        period = "2021-01-02",
                        activeMembers = 10,
                    )
                )
            ),
            therapies = FrequencyCoordinationDataResponse.FrequencyItem(
                totalEvents = 1,
                uniqueMembers = 2,
                historic = listOf(
                    FrequencyCoordinationDataResponse.Historic(
                        value = 1.0F,
                        benchmark = 2.0F,
                        period = "2021-01-02",
                        activeMembers = 10,
                    )
                )
            ),
            specialist = FrequencyCoordinationDataResponse.FrequencyItem(
                totalEvents = 1,
                uniqueMembers = 2,
                historic = listOf(
                    FrequencyCoordinationDataResponse.Historic(
                        value = 1.0F,
                        benchmark = 2.0F,
                        period = "2021-01-02",
                        activeMembers = 10,
                    )
                )
            ),
        )
    )

    @Test
    fun `#should get from api successfully`() = runBlocking {
        coEvery {
            dataAPIClient.getFrequencyCoordinationByCompany(companyId)
        } returns frequencyCoordinationDataResponse

        val result = frequencyCoordinationDataService.getFrequencyCoordinationData(companyId)

        assertEquals(frequencyCoordinationDataResponse, result.get())

        coVerifyOnce { dataAPIClient.getFrequencyCoordinationByCompany(any()) }
    }

    @Test
    fun `#should raise the exception when an error occurs`(): Unit = runBlocking {
        coEvery {
            dataAPIClient.getFrequencyCoordinationByCompany(companyId)
        } throws InternalServiceErrorException("Internal Service Error")

        val result = frequencyCoordinationDataService.getFrequencyCoordinationData(companyId)
        assertThat(result).isFailureOfType(InternalServiceErrorException::class)
    }
}
