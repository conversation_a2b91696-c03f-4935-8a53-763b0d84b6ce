package br.com.alice.api.backoffice.mappers.execIndicator

import br.com.alice.api.backoffice.transfers.execIndicator.HealthcareResourceResponse
import br.com.alice.data.layer.helpers.TestModelFactory
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class HealthcareResourceOutputMapperTest {

    private val resource = TestModelFactory.buildHealthcareResource()

    @Test
    fun `toResponse return correctly`() {
        val expected = HealthcareResourceResponse(
            id = resource.id,
            code = resource.code,
            description = resource.description,
            tableType = resource.tableType
        )

        val response = HealthcareResourceOutputMapper.toResponse(resource)

        assertThat(response).isEqualTo(expected)
    }
}
