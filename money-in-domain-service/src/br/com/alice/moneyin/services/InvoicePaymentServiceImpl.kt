package br.com.alice.moneyin.services

import br.com.alice.common.PaymentMethod
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.money
import br.com.alice.common.core.extensions.plusSafe
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapEach
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.mapFirst
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.isPersonId
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.BillingAccountableParty
import br.com.alice.data.layer.models.BoletoPaymentDetail
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoiceLiquidation
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.InvoicePaymentModel
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.InvoicePaymentSource
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.data.layer.models.PaymentDetail
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.models.PreActivationPayment
import br.com.alice.data.layer.models.PreActivationPaymentType
import br.com.alice.data.layer.services.InvoicePaymentModelDataService
import br.com.alice.data.layer.services.MemberInvoiceGroupModelDataService
import br.com.alice.data.layer.services.MemberInvoiceModelDataService
import br.com.alice.moneyin.builder.InvoicePaymentBuilder
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.CancelPaymentOnAcquirerScheduleService
import br.com.alice.moneyin.client.InvalidAmountException
import br.com.alice.moneyin.client.InvoicePaymentAlreadyApprovedException
import br.com.alice.moneyin.client.InvoicePaymentAmountNotAllowed
import br.com.alice.moneyin.client.InvoicePaymentCanceledException
import br.com.alice.moneyin.client.InvoicePaymentDetailsNotFoundException
import br.com.alice.moneyin.client.InvoicePaymentHasNoMemberInvoice
import br.com.alice.moneyin.client.InvoicePaymentInvalidAmountPaid
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.MemberInvoiceNotFoundException
import br.com.alice.moneyin.client.MemberInvoiceNotOpenedException
import br.com.alice.moneyin.client.PendingInvoicePaymentException
import br.com.alice.moneyin.converters.enrichDetailForCreation
import br.com.alice.moneyin.converters.toModel
import br.com.alice.moneyin.converters.toTransport
import br.com.alice.moneyin.event.CreateExternalPaymentRequestEvent
import br.com.alice.moneyin.event.ExternalInvoiceCreatedEvent
import br.com.alice.moneyin.event.InvoicePaymentApprovedEvent
import br.com.alice.moneyin.event.InvoicePaymentCanceledEvent
import br.com.alice.moneyin.event.InvoicePaymentCreatedEvent
import br.com.alice.moneyin.event.PaymentDetailCreatedEvent
import br.com.alice.moneyin.metrics.Metrics
import br.com.alice.moneyin.model.InvalidPaymentMethodAndReasonException
import br.com.alice.moneyin.model.PaymentValidationException
import br.com.alice.moneyin.services.internal.AcquirerOrchestratorService
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class InvoicePaymentServiceImpl(
    private val invoicePaymentDataService: InvoicePaymentModelDataService,
    private val memberInvoiceGroupDataService: MemberInvoiceGroupModelDataService,
    private val kafkaProducerService: KafkaProducerService,
    private val paymentDetailService: PaymentDetailService,
    private val paymentService: PaymentService,
    private val memberInvoiceDataService: MemberInvoiceModelDataService,
    private val billingAccountablePartyService: BillingAccountablePartyService,
    private val acquirerOrchestratorService: AcquirerOrchestratorService,
    private val memberService: MemberService,
    private val cancelPaymentOnAcquirerScheduleService: CancelPaymentOnAcquirerScheduleService,
    private val moneyInResourceSignTokenServiceImpl: MoneyInResourceSignTokenServiceImpl
) : InvoicePaymentService {

    override suspend fun get(paymentId: UUID, withPaymentDetails: Boolean) = invoicePaymentDataService.get(paymentId)
        .flatMap { it.withPaymentDetails(withPaymentDetails) }
        .map { it.toTransport() }

    override suspend fun getLastByCompanyId(companyId: UUID, withPaymentDetails: Boolean) =
        memberInvoiceGroupDataService.find {
            where { this.companyId.eq(companyId) }
                .orderByList({ listOf(this.referenceDate, this.createdAt) }, { listOf(desc, desc) })
                .limit { 1 }
        }.mapFirst().flatMap { memberInvoiceGroup ->
            getLastByInvoiceGroupId(memberInvoiceGroup.id, withPaymentDetails)
        }

    private suspend fun InvoicePaymentModel.withPaymentDetails(withPaymentDetails: Boolean) =
        if (withPaymentDetails) paymentDetailService.getPaymentDetail(this.toTransport())
            .map { this.withPaymentDetail(it.toModel()) }
        else this.success()

    override suspend fun getByExternalId(
        externalId: String,
        withPaymentDetails: Boolean
    ): Result<InvoicePayment, Throwable> =
        invoicePaymentDataService.findOne { where { this.externalId.eq(externalId) } }
            .flatMap { it.withPaymentDetails(withPaymentDetails) }
            .map { it.toTransport() }

    override suspend fun getByExternalIdAndSource(
        externalId: String,
        source: InvoicePaymentSource,
        withPaymentDetails: Boolean
    ): Result<InvoicePayment, Throwable> =
        invoicePaymentDataService.findOne { where { this.externalId.eq(externalId) and this.source.eq(source) } }
            .flatMap { it.withPaymentDetails(withPaymentDetails) }
            .map { it.toTransport() }

    override suspend fun getLastByInvoiceGroupId(
        memberInvoiceGroupId: UUID,
        withPaymentDetails: Boolean
    ): Result<InvoicePayment, Throwable> =
        invoicePaymentDataService.find {
            where {
                this.invoiceGroupId.eq(memberInvoiceGroupId)
            }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
        }.mapFirst().flatMap { it.withPaymentDetails(withPaymentDetails) }
            .map { it.toTransport() }


    override suspend fun getByInvoiceGroupIds(
        memberInvoiceGroupIds: List<UUID>,
        withPaymentDetails: Boolean
    ): Result<List<InvoicePayment>, Throwable> {
        val invoicePayments =
            invoicePaymentDataService.find {
                where {
                    this.invoiceGroupId.inList(memberInvoiceGroupIds)
                }
            }.mapEach { it.toTransport() }
        return if (withPaymentDetails) listInvoicePaymentsWithDetails(invoicePayments) else invoicePayments
    }

    override suspend fun getByInvoiceLiquidationIds(
        invoiceLiquidationIds: List<UUID>,
        withPaymentDetails: Boolean
    ): Result<List<InvoicePayment>, Throwable> {
        val invoicePayments =
            invoicePaymentDataService.find {
                where {
                    this.invoiceLiquidationId.inList(invoiceLiquidationIds)
                }
            }.mapEach { it.toTransport() }
        return if (withPaymentDetails) listInvoicePaymentsWithDetails(invoicePayments) else invoicePayments
    }

    override suspend fun getByInvoicePreActivationPayment(
        preActivationPaymentIds: List<UUID>,
        withPaymentDetails: Boolean
    ): Result<List<InvoicePayment>, Throwable> {
        val invoicePayments =
            invoicePaymentDataService.find {
                where {
                    this.preActivationPaymentId.inList(preActivationPaymentIds)
                }
            }.mapEach { it.toTransport() }
        return if (withPaymentDetails) listInvoicePaymentsWithDetails(invoicePayments) else invoicePayments
    }

    override suspend fun approve(
        paymentId: UUID,
        approvedAt: LocalDateTime?,
        amountPaid: BigDecimal?,
        interest: BigDecimal?,
        fine: BigDecimal?,
        approvedByStaffId: UUID?,
    ): Result<InvoicePayment, Throwable> =
        invoicePaymentDataService.get(paymentId).flatMap { invoicePayment ->
            if (invoicePayment.isApproved) return invoicePayment.toTransport().success()
                .then {
                    logger.info(
                        "InvoicePaymentServiceImpl::approve: the invoice payment is already approved",
                        "id" to it.id,
                        "approved_at" to it.approvedAt,
                        "total_amount" to it.amount,
                        "amount_paid" to amountPaid,
                    )
                }

            amountPaid?.let {
                if (it < invoicePayment.amount) {
                    return@flatMap InvoicePaymentInvalidAmountPaid(it, invoicePayment.amount).failure()
                }
            }


            val finalApprovedAt = approvedAt ?: LocalDateTime.now()
            val approvedPayment = invoicePayment.approve(finalApprovedAt, amountPaid, fine, interest, approvedByStaffId)

            updateInvoicePayment(approvedPayment)
        }

    private suspend fun updateInvoicePayment(invoicePayment: InvoicePaymentModel) =
        invoicePaymentDataService.update(invoicePayment)
            .then { logger.info("invoice payment approved", "invoice_payment" to it) }
            .map { it.toTransport() }
            .then { kafkaProducerService.produce(InvoicePaymentApprovedEvent(it)) }
            .then { Metrics.Payment.invoicePaymentStatusChanged(it) }

    override suspend fun decline(paymentId: UUID): Result<InvoicePayment, Throwable> =
        invoicePaymentDataService.get(paymentId).flatMap { payment ->
            if (payment.isDeclined) return payment.toTransport().success()
            if (payment.isCanceled) return InvoicePaymentCanceledException(payment.id).failure()
            if (payment.isApproved) return InvoicePaymentAlreadyApprovedException(paymentId).failure()

            val declinedPayment = payment.decline()

            invoicePaymentDataService.update(declinedPayment)
                .then { logger.info("invoice payment declined", "invoice_payment" to it) }
                .map { it.toTransport() }
                .then { Metrics.Payment.invoicePaymentStatusChanged(it) }
        }

    override suspend fun secondCopy(paymentId: UUID, personId: PersonId): Result<Boolean, Throwable> =
        coroutineScope {
            cancel(paymentId, CancellationReason.CANCELED_BY_REISSUE).flatMap { payment ->
                val memberInvoicesDeferred = async { findMemberInvoicesByIds(payment.memberInvoiceIds).get() }
                val now = LocalDate.now().atEndOfTheDay()

                val memberInvoiceGroupDeferred =
                    if (payment.invoiceGroupId != null) async {
                        memberInvoiceGroupDataService.get(payment.invoiceGroupId!!)
                            .getOrNullIfNotFound()
                    } else null

                val memberInvoices = memberInvoicesDeferred.await()
                val memberInvoiceGroup = memberInvoiceGroupDeferred?.await()

                val billingAccountablePartyDeferred =
                    async { billingAccountablePartyService.getCurrent(personId).get() }

                val paymentReason = if (memberInvoices.first().type == MemberInvoiceType.FIRST_PAYMENT)
                    PaymentReason.FIRST_PAYMENT
                else if (memberInvoices.first().type != null)
                    PaymentReason.valueOf(memberInvoices.first().type!!.toPaymentReason().name)
                else PaymentReason.REGULAR_PAYMENT

                val rightReason =
                    payment.reason ?: memberInvoiceGroup?.type?.toPaymentReason()
                        ?.let { PaymentReason.valueOf(it.name) } ?: paymentReason

                createInvoicePaymentForMemberInvoices(
                    InvoicePaymentService.CreateInvoicePaymentForMemberInvoicesPayload(
                        payment.method,
                        memberInvoices.map { it.toTransport() },
                        now.plusDays(3L),
                        billingAccountablePartyDeferred.await(),
                        rightReason,
                        memberInvoiceGroup?.toTransport(),
                        personId,
                        InvoicePaymentOrigin.ISSUED_BY_MEMBER
                    )
                ).map { true }
            }
        }

    private fun canCancelPaymentImmediately(
        payment: InvoicePaymentModel,
    ): Boolean {
        if (payment.source != InvoicePaymentSource.ITAU) {
            return true
        }

        val isDay0 = payment.createdAt.toLocalDate() == LocalDate.now()
        val hasBoleto = payment.method == PaymentMethod.BOLETO || payment.method == PaymentMethod.BOLEPIX

        return !hasBoleto && !isDay0
    }

    override suspend fun cancel(paymentId: UUID, reason: CancellationReason): Result<InvoicePayment, Throwable> =
        invoicePaymentDataService.get(paymentId).flatMap { payment ->
            if (payment.isCanceled) return payment.toTransport().success()
            if (payment.isApproved) return InvoicePaymentAlreadyApprovedException(paymentId).failure()

            payment.externalId?.let {
                try {
                    if (canCancelPaymentImmediately(payment)) {
                        acquirerOrchestratorService.cancel(payment.toTransport()).get()
                    } else {
                        cancelPaymentOnAcquirerScheduleService.scheduleCancelPaymentOnAcquirer(payment.id)
                    }
                } catch (ex: Exception) {
                    logger.error("Error to cancel payment on acquirer", "error_message" to ex.message.toString())
                }
            }

            val canceledPayment = payment.cancel(reason)

            invoicePaymentDataService.update(canceledPayment)
                .map { it.toTransport() }
                .then {
                    logger.info("invoice payment canceled", "invoice_payment" to it)
                    kafkaProducerService.produce(InvoicePaymentCanceledEvent(it))
                }
                .then { Metrics.Payment.invoicePaymentStatusChanged(it) }
        }

    override suspend fun cancelByInvoiceGroupId(
        memberInvoiceGroupId: UUID,
        reason: CancellationReason
    ): Result<List<InvoicePayment>, Throwable> =
        findByMemberInvoiceGroupId(memberInvoiceGroupId)
            .flatMapEach { payment ->
                logger.info("cancelByInvoiceGroupId - payment about to be canceled", "invoice_payment" to payment)
                this.cancel(payment.id, reason)
            }
            .then {
                logger.info(
                    "cancelByInvoiceGroupId - payments canceled successfully",
                    "invoice_group_id" to memberInvoiceGroupId
                )
            }
            .thenError { logger.info("cancelByInvoiceGroupId - payments not canceled", "ex" to it) }

    override suspend fun cancelByPreActivationPaymentId(
        preActivationPaymentId: UUID,
        reason: CancellationReason
    ): Result<List<InvoicePayment>, Throwable> =
        findByPreActivationPaymentId(preActivationPaymentId)
            .flatMapEach { payment ->
                logger.info(
                    "cancelByPreActivationPaymentId - payment about to be canceled",
                    "invoice_payment_id" to payment.id
                )
                this.cancel(payment.id, reason)
            }
            .then {
                logger.info(
                    "cancelByPreActivationPaymentId - payments canceled successfully",
                    "pre_activation_payment_id" to preActivationPaymentId
                )
            }
            .thenError { logger.info("cancelByPreActivationPaymentId - payments not canceled", "ex" to it) }

    override suspend fun cancelByInvoiceLiquidationId(
        invoiceLiquidationId: UUID,
        reason: CancellationReason
    ): Result<List<InvoicePayment>, Throwable> =
        invoicePaymentDataService.find {
            where { this.invoiceLiquidationId.eq(invoiceLiquidationId) }
        }.flatMapEach { payment ->
            logger.info("cancelByInvoiceLiquidationId - payment about to be canceled", "invoice_payment" to payment)
            this.cancel(payment.id, reason)
        }.then {
            logger.info(
                "cancelByInvoiceLiquidationId - payments canceled successfully",
                "invoice_liquidation_id" to invoiceLiquidationId
            )
        }.thenError { logger.info("cancelByInvoiceLiquidationId - payments not canceled", "ex" to it) }


    private suspend fun findByMemberInvoiceGroupId(memberInvoiceGroupId: UUID) =
        invoicePaymentDataService.find { where { this.invoiceGroupId.eq(memberInvoiceGroupId) } }

    private suspend fun findByPreActivationPaymentId(preActivationPaymentId: UUID) =
        invoicePaymentDataService.find { where { this.preActivationPaymentId.eq(preActivationPaymentId) } }

    override suspend fun fail(paymentId: UUID, failReason: String): Result<InvoicePayment, Throwable> =
        invoicePaymentDataService.get(paymentId).flatMap { payment ->
            if (payment.isFailed) return payment.toTransport().success()
            if (payment.isApproved) return InvoicePaymentAlreadyApprovedException(paymentId).failure()

            val failedPayment = payment.fail(failReason)

            invoicePaymentDataService.update(failedPayment)
                .then { logger.info("invoice payment failed", "invoice_payment" to it) }
                .then { Metrics.Payment.invoicePaymentStatusChanged(it.toTransport()) }
        }.map { it.toTransport() }

    override suspend fun expire(paymentId: UUID): Result<InvoicePayment, Throwable> =
        invoicePaymentDataService.get(paymentId).flatMap { payment ->
            if (payment.isExpired || payment.isApproved) return payment.toTransport().success()
            invoicePaymentDataService.update(payment.expire())
                .then { logger.info("invoice payment expired", "invoice_payment" to it) }
                .then { Metrics.Payment.invoicePaymentStatusChanged(it.toTransport()) }
        }.map { it.toTransport() }

    private suspend fun withDetails(invoicePayment: InvoicePayment): InvoicePayment {
        val paymentDetail = paymentDetailService.getPaymentDetail(invoicePayment).getOrNullIfNotFound()
        return invoicePayment.withPaymentDetail(paymentDetail)
    }

    override suspend fun listInvoicePayments(
        memberInvoiceId: UUID,
        withPaymentDetails: Boolean
    ): Result<List<InvoicePayment>, Throwable> {
        return listInvoicePaymentsByInvoicesIds(listOf(memberInvoiceId), withPaymentDetails)
    }

    override suspend fun listInvoicePaymentsByInvoicesIds(
        memberInvoiceIds: List<UUID>,
        withPaymentDetails: Boolean
    ): Result<List<InvoicePayment>, Throwable> {
        val invoicePayments =
            invoicePaymentDataService.find { where { this.memberInvoiceIds.containsAny(memberInvoiceIds) } }
        val invoicePaymentTransports = invoicePayments.mapEach { it.toTransport() }
        return if (withPaymentDetails) listInvoicePaymentsWithDetails(invoicePaymentTransports) else invoicePaymentTransports
    }

    override suspend fun listInvoicePaymentsById(invoicePaymentIds: List<UUID>): Result<List<InvoicePayment>, Throwable> =
        invoicePaymentDataService.find { where { this.id.inList(invoicePaymentIds) } }
            .mapEach { it.toTransport() }

    override suspend fun getPendingInvoicePayment(
        memberInvoiceId: UUID,
        method: PaymentMethod,
        withPaymentDetails: Boolean
    ): Result<InvoicePayment, Throwable> =
        listInvoicePayments(memberInvoiceId, withPaymentDetails)
            .flatMap { payment ->
                payment.firstOrNull { it.method == method && it.isPending }?.success()
                    ?: NotFoundException("There is no pending payment for invoice '$memberInvoiceId' and method '$method'").failure()
            }

    override suspend fun createInvoicePayment(
        invoicePayment: InvoicePayment,
        member: Member,
    ): Result<InvoicePayment, Throwable> {
        logger.info(
            "Creating invoicePayment",
            "invoice_payment" to invoicePayment,
            "reason" to invoicePayment.reason,
            "member" to member,
        )

        val billingAccountableParty =
            invoicePayment.billingAccountablePartyId?.let { billingAccountablePartyService.get(it).get() }
                ?: billingAccountablePartyService.getCurrentOrCreateForPerson(member.personId)
                    .get() // TODO: Remove it after deploy, billing accountable party id will be required when creating a new payment

        return validateAndAdjustCreation(invoicePayment)
            .flatMap { createInvoicePayment(it, billingAccountableParty, member) }
    }

    override suspend fun createInvoicePaymentForMemberInvoices(
        payload: InvoicePaymentService.CreateInvoicePaymentForMemberInvoicesPayload
    ): Result<InvoicePayment, Throwable> {
        val paymentMethod = payload.paymentMethod
        val memberInvoices = payload.memberInvoices
        val dueDate = payload.dueDate
        val billingAccountableParty = payload.billingAccountableParty
        val origin = payload.origin
        val paymentUrl = payload.paymentUrl
        val memberInvoiceGroup = payload.memberInvoiceGroup
        val reason = payload.reason
        val personId = payload.personId
        val externalId = payload.externalId
        val sendEmail = payload.sendEmail
        val syncProcess = payload.syncProcess

        logger.info(
            "Creating invoicePayment for invoices",
            "invoices_total" to memberInvoices.size,
            "due_date" to dueDate,
            "billing_accountable_party" to billingAccountableParty,
            "reason" to reason
        )

        val rightReason = memberInvoiceGroup?.type?.toPaymentReason() ?: reason

        val invoicePayment = InvoicePaymentBuilder.buildPendingInvoicePaymentWithDetails(
            paymentMethod,
            memberInvoices,
            dueDate,
            rightReason,
            billingAccountableParty,
            memberInvoiceGroup?.totalAmount,
            origin,
            paymentUrl,
        ).copy(
            invoiceGroupId = memberInvoiceGroup?.id,
            externalId = externalId,
            sendEmail = sendEmail
        )

        return createPaymentForInvoicePayments(
            invoicePayment,
            memberInvoices,
            billingAccountableParty,
            personId,
            syncProcess
        )
    }

    private suspend fun findMemberInvoicesByIds(ids: List<UUID>) = memberInvoiceDataService.find {
        where { this.id.inList(ids) }
    }

    private suspend fun findMemberInvoicesByGroupId(id: UUID) = memberInvoiceDataService.find {
        where { this.memberInvoiceGroupId.eq(id) }
    }

    override suspend fun findInvoicePaymentInCreatedAtRangeAndBillingAccountablePartyId(
        startDate: LocalDateTime,
        endDate: LocalDateTime,
        billingAccountablePartyId: UUID
    ): Result<List<InvoicePayment>, Throwable> {
        return invoicePaymentDataService.find {
            where {
                this.createdAt.greaterEq(startDate).and(this.createdAt.lessEq(endDate))
                    .and(this.billingAccountablePartyId.eq(billingAccountablePartyId.toString()))
            }
        }.mapEach { it.toTransport() }
    }

    override suspend fun createInvoicePaymentForInvoiceGroup(
        memberInvoiceGroup: MemberInvoiceGroup,
        paymentMethod: PaymentMethod,
        reason: PaymentReason,
        origin: InvoicePaymentOrigin,
        dueDate: LocalDate?,
        syncProcess: Boolean?
    ): Result<InvoicePayment, Throwable> = coroutineScope {

        logger.info("Creating invoicePayment with memberInvoiceGroup", "member_invoice_group" to memberInvoiceGroup)

        val memberInvoicesDeferred = async {
            if (memberInvoiceGroup.memberInvoiceIds.isEmpty())
                findMemberInvoicesByGroupId(memberInvoiceGroup.id).then {
                    logger.info("Find by group id")

                    val memberInvoiceGroup = memberInvoiceGroup.copy(
                        memberInvoiceIds = it.map { memberInvoice -> memberInvoice.id }
                    )

                    memberInvoiceGroupDataService.update(memberInvoiceGroup.toModel())
                }
            else
                findMemberInvoicesByIds(memberInvoiceGroup.memberInvoiceIds)
        }

        val billingAccountablePartyDeferred =
            async { billingAccountablePartyService.get(memberInvoiceGroup.billingAccountablePartyId).get() }

        val rightReason = memberInvoiceGroup.type?.toPaymentReason() ?: reason

        val memberInvoices = memberInvoicesDeferred.await().get()
        val billingAccountableParty = billingAccountablePartyDeferred.await()

        val dueDate = dueDate ?: memberInvoiceGroup.dueDate

        createInvoicePaymentForMemberInvoices(
            InvoicePaymentService.CreateInvoicePaymentForMemberInvoicesPayload(
                paymentMethod,
                memberInvoices.map { it.toTransport() },
                dueDate.atStartOfDay(),
                billingAccountableParty,
                rightReason,
                memberInvoiceGroup,
                origin = origin,
                syncProcess = syncProcess
            ),
        )
    }

    override suspend fun createFromMemberInvoiceGroup(
        memberInvoiceGroup: MemberInvoiceGroup,
        paymentMethod: PaymentMethod,
        reason: PaymentReason,
        origin: InvoicePaymentOrigin,
        dueDate: LocalDate?,
        sendEmail: Boolean,
        copyMemberInvoiceIds: Boolean,
        syncProcess: Boolean
    ): Result<InvoicePayment, Throwable> {
        logger.info(
            "Creating invoicePayment from MemberInvoiceGroup",
            "member_invoice_group_id" to memberInvoiceGroup.id,
            "member_invoice_group_total" to memberInvoiceGroup.totalAmount,
            "member_invoice_group_date_reference" to memberInvoiceGroup.referenceDate,
        )

        if (memberInvoiceGroup.totalAmount == null || memberInvoiceGroup.totalAmount!! <= 0.toBigDecimal()) {
            return InvalidAmountException("Invalid invoice amount: the value must be positive").failure()
        }

        val billingAccountableParty =
            billingAccountablePartyService.get(memberInvoiceGroup.billingAccountablePartyId).get()

        val rightReason = memberInvoiceGroup.type?.toPaymentReason() ?: reason

        val dueDate = dueDate ?: memberInvoiceGroup.dueDate

        logger.info(
            "Creating invoicePayment for invoices",
            "due_date" to dueDate,
            "billing_accountable_party" to billingAccountableParty,
            "reason" to reason
        )

        val invoicePayment = InvoicePaymentBuilder.buildPendingInvoicePaymentWithDetails(
            paymentMethod,
            emptyList(),
            dueDate.atStartOfDay(),
            rightReason,
            billingAccountableParty,
            memberInvoiceGroup.totalAmount,
            origin,
            paymentUrl = null,
        ).copy(
            invoiceGroupId = memberInvoiceGroup.id,
            sendEmail = sendEmail,
            memberInvoiceIds = if (copyMemberInvoiceIds) memberInvoiceGroup.memberInvoiceIds else emptyList()
        )

        return createInvoicePaymentWithoutMember(invoicePayment, billingAccountableParty, syncProcess)
            .coFoldDuplicated {
                logger.error(
                    "InvoicePayment pending already exists for this invoice group",
                    "member_invoice_group_id" to memberInvoiceGroup.id,

                    )
                getByInvoiceGroupIds(listOf(memberInvoiceGroup.id), true).mapFirst()
            }
            .thenError { exception ->
                logger.error(
                    "Error creating InvoicePayment",
                    "invoice_payment" to invoicePayment,
                    "payment_reason" to invoicePayment.reason,
                    "exception" to exception.message,
                    "stack_trace" to exception.stackTrace.first().toString()
                )
            }
    }

    override suspend fun createFromPreActivationPayment(
        preActivationPayment: PreActivationPayment,
        paymentMethod: PaymentMethod,
        origin: InvoicePaymentOrigin,
        dueDate: LocalDate?,
        sendEmail: Boolean,
        copyMemberInvoiceIds: Boolean,
        syncProcess: Boolean
    ): Result<InvoicePayment, Throwable> {
        logger.info(
            "Creating invoicePayment from PreActivationPayment",
            "pre_activation_payment_id" to preActivationPayment.id,
            "pre_activation_payment_total" to preActivationPayment.totalAmount,
            "pre_activation_payment_date_reference" to preActivationPayment.referenceDate,
        )

        if (preActivationPayment.totalAmount <= 0.toBigDecimal()) {
            return InvalidAmountException("Invalid pre activation payment amount: the value must be positive").failure()
        }

        val billingAccountableParty =
            billingAccountablePartyService.get(preActivationPayment.billingAccountablePartyId).get()

        val rightReason =
            if (preActivationPayment.type == PreActivationPaymentType.B2B) PaymentReason.B2B_PRE_ACTIVATION_PAYMENT else PaymentReason.B2C_PRE_ACTIVATION_PAYMENT

        val dueDate = dueDate ?: preActivationPayment.dueDate

        logger.info(
            "Creating invoicePayment for invoices",
            "due_date" to dueDate,
            "billing_accountable_party" to billingAccountableParty,
            "reason" to rightReason
        )

        val invoicePayment = InvoicePaymentBuilder.buildPendingInvoicePaymentWithDetails(
            paymentMethod,
            emptyList(),
            dueDate.atStartOfDay(),
            rightReason,
            billingAccountableParty,
            preActivationPayment.totalAmount,
            origin,
            paymentUrl = null,
        ).copy(
            preActivationPaymentId = preActivationPayment.id,
            sendEmail = sendEmail,
            memberInvoiceIds = if (copyMemberInvoiceIds) preActivationPayment.memberInvoiceIds else emptyList()
        )

        return createInvoicePaymentWithoutMember(invoicePayment, billingAccountableParty, syncProcess)
            .coFoldDuplicated {
                logger.error(
                    "InvoicePayment pending already exists for this invoice group",
                    "pre_activation_payment_id" to preActivationPayment.id,

                    )

                getByInvoicePreActivationPayment(listOf(preActivationPayment.id), true).mapFirst()
            }
            .thenError { exception ->
                logger.error(
                    "Error creating InvoicePayment",
                    "invoice_payment" to invoicePayment,
                    "payment_reason" to invoicePayment.reason,
                    "exception" to exception.message,
                    "stack_trace" to exception.stackTrace.first().toString()
                )
            }
    }

    private suspend fun createPaymentForInvoicePayments(
        invoicePayment: InvoicePayment,
        memberInvoices: List<MemberInvoice>,
        billingAccountableParty: BillingAccountableParty,
        personId: PersonId? = null,
        syncProcess: Boolean? = false
    ): Result<InvoicePayment, Throwable> {
        logger.info(
            "Creating invoicePayment for memberInvoices",
            "reason" to invoicePayment.reason,
            "invoice_payment" to invoicePayment,
            "member_invoices" to memberInvoices,
            "billing_accountable_party" to billingAccountableParty,
        )

        val alreadyPendingPayment = getPendingInvoicePaymentByInvoiceIds(memberInvoices.map { it.id })
        if (alreadyPendingPayment != null)
            return withDetails(alreadyPendingPayment.toTransport()).success()


        return getMemberForFirstPayment(invoicePayment.reason!!, memberInvoices)
            .flatMap { createInvoicePayment(invoicePayment, billingAccountableParty, it, personId, syncProcess) }
    }

    private suspend fun getMemberForFirstPayment(
        reason: PaymentReason,
        memberInvoices: List<MemberInvoice>
    ) = if (reason == PaymentReason.FIRST_PAYMENT) {
        memberService.get(memberInvoices.first().memberId)
    } else {
        Result.of { null }
    }

    private suspend fun getPendingInvoicePaymentByInvoiceIds(invoiceIds: List<UUID>) =
        invoicePaymentDataService.findOneOrNull {
            where {
                this.memberInvoiceIds.containsAny(invoiceIds).and(this.status.eq(InvoicePaymentStatus.PENDING))
            }
        }

    private suspend fun getPendingInvoicePaymentByInvoiceLiquidationId(invoiceLiquidationId: UUID) =
        invoicePaymentDataService.findOneOrNull {
            where {
                this.invoiceLiquidationId.eq(invoiceLiquidationId)
                    .and(this.status.eq(InvoicePaymentStatus.PENDING))
            }
        }

    private suspend fun createInvoicePayment(
        invoicePayment: InvoicePayment,
        billingAccountableParty: BillingAccountableParty,
        member: Member?,
        personId: PersonId? = null,
        syncProcess: Boolean? = false
    ): Result<InvoicePayment, Throwable> {
        val paymentDetail = invoicePayment.paymentDetail
        logger.info(
            "Creating invoicePayment",
            "invoice_payment" to invoicePayment,
            "billing_accountable_party" to billingAccountableParty,
            "reason" to invoicePayment.reason,
            "member" to member,
        )

        return add(invoicePayment, member)
            .flatMap {
                if (syncProcess == true) {
                    createExternalPaymentForInvoicePayment(
                        it.withPaymentDetail(paymentDetail?.toModel()).toTransport(),
                        billingAccountableParty,
                        personId
                    )
                } else {
                    requestExternalPaymentCreation(
                        it.withPaymentDetail(paymentDetail?.toModel()).toTransport(),
                        billingAccountableParty,
                        personId
                    )
                }
            }
            .thenError { exception ->
                logger.error(
                    "Error creating InvoicePayment",
                    "member_id" to member?.id,
                    "invoice_payment" to invoicePayment,
                    "payment_reason" to invoicePayment.reason,
                    "exception" to exception.message,
                    "stack_trace" to exception.stackTrace.first().toString()
                )
            }
    }

    private suspend fun createInvoicePaymentWithoutMember(
        invoicePayment: InvoicePayment,
        billingAccountableParty: BillingAccountableParty,
        syncProcess: Boolean
    ): Result<InvoicePayment, Throwable> {
        val paymentDetail = invoicePayment.paymentDetail
        logger.info(
            "Creating invoicePayment",
            "invoice_payment" to invoicePayment,
            "billing_accountable_party" to billingAccountableParty,
            "reason" to invoicePayment.reason,
        )

        return add(invoicePayment)
            .flatMap {
                if (syncProcess) {
                    createExternalPaymentForInvoicePayment(
                        it.withPaymentDetail(paymentDetail?.toModel()).toTransport(),
                        billingAccountableParty
                    )
                } else {
                    requestExternalPaymentCreation(
                        it.withPaymentDetail(paymentDetail?.toModel()).toTransport(),
                        billingAccountableParty
                    )
                }
            }
            .thenError { exception ->
                logger.error(
                    "Error creating InvoicePayment",
                    "invoice_payment" to invoicePayment,
                    "payment_reason" to invoicePayment.reason,
                    "exception" to exception.message,
                    "stack_trace" to exception.stackTrace.first().toString()
                )
            }
    }

    private suspend fun add(
        invoicePayment: InvoicePayment,
        member: Member? = null,
    ) = invoicePaymentDataService.add(
        invoicePayment.toModel().copy(
            source = acquirerOrchestratorService.getDefaultPaymentSource(invoicePayment.billingAccountablePartyId!!)
        )
    )
        .andThen { moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(it.id) }
        .then {
            kafkaProducerService.produce(
                InvoicePaymentCreatedEvent(
                    it.toTransport(),
                    member,
                    invoicePayment.reason!!,
                )
            )
        }
        .then { Metrics.Payment.invoicePaymentCreated(it.toTransport()) }
        .then { logger.info("InvoicePayment created", "invoice_payment" to it) }

    private suspend fun requestExternalPaymentCreation(
        invoicePayment: InvoicePayment,
        billingAccountableParty: BillingAccountableParty,
        personId: PersonId? = null
    ): Result<InvoicePayment, Throwable> {
        return invoicePayment.paymentDetail?.let { paymentDetail ->
            logger.info(
                "Producing message to create external Payment async",
                "invoice_payment" to invoicePayment,
                "payment_detail" to paymentDetail,
                "billing_accountable_party" to billingAccountableParty,
            )
            val event = CreateExternalPaymentRequestEvent(
                invoicePayment.withPaymentDetail(paymentDetail),
                billingAccountableParty,
                personId
            )
            coResultOf<ProducerResult, Throwable> {
                kafkaProducerService.produce(event, billingAccountableParty.id.toString())
            }.map { invoicePayment.withPaymentDetail(paymentDetail) }
        } ?: run {
            logger.warn(
                "No paymentDetail information found for invoicePayment. External payment won't be created",
                "invoice_payment" to invoicePayment,
                "billing_accountable_party_id" to billingAccountableParty.id,
            )
            invoicePayment.success()
        }
    }

    private suspend fun createPaymentDetail(invoicePayment: InvoicePayment): Result<PaymentDetail, Throwable> =
        invoicePayment.paymentDetail?.let { paymentDetail ->
            logger.info("Producing to kafka a ExternalInvoiceCreatedEvent")
            coResultOf<ProducerResult, Throwable> {
                kafkaProducerService.produce(ExternalInvoiceCreatedEvent(invoicePayment))
            }.map { paymentDetail }
        } ?: run {
            logger.error(
                "No paymentDetail information found for invoicePayment. External payment won't be created",
                "invoice_payment" to invoicePayment,
            )
            InvoicePaymentDetailsNotFoundException(invoicePayment.id).failure()
        }

    override suspend fun reissueBoleto(
        invoicePayment: InvoicePayment,
        billingAccountableParty: BillingAccountableParty,
        dueDate: LocalDateTime
    ): Result<InvoicePayment, Throwable> {
        if (invoicePayment.externalId != null) return invoicePayment.success()

        val detail = BoletoPaymentDetail(
            paymentId = invoicePayment.id,
            dueDate = dueDate,
        )

        return invoicePayment.withPaymentDetail(detail).success()
            .flatMap { requestExternalPaymentCreation(invoicePayment, billingAccountableParty) }
    }

    override suspend fun listInvoicePaymentsByBillingAccountablePartyId(
        billingAccountablePartyId: UUID,
        withPaymentDetails: Boolean
    ): Result<List<InvoicePayment>, Throwable> {
        val invoicePayments = invoicePaymentDataService.find {
            where {
                this.billingAccountablePartyId.eq(billingAccountablePartyId.toString())
            }
        }
        val invoicePaymentTransports = invoicePayments.mapEach { it.toTransport() }
        return if (withPaymentDetails) listInvoicePaymentsWithDetails(invoicePaymentTransports) else invoicePaymentTransports
    }

    override suspend fun createPaymentForLiquidation(
        invoiceLiquidation: InvoiceLiquidation,
        paymentMethod: PaymentMethod,
        reason: PaymentReason,
        origin: InvoicePaymentOrigin,
        dueDate: LocalDate?
    ): Result<InvoicePayment, Throwable> {
        if (reason != PaymentReason.B2B_LIQUIDATION && reason != PaymentReason.B2C_LIQUIDATION)
            return InvalidPaymentMethodAndReasonException("Invalid payment reason for liquidation").failure()

        val alreadyPendingPayment = getPendingInvoicePaymentByInvoiceLiquidationId(invoiceLiquidation.id)
        if (alreadyPendingPayment != null)
            return withDetails(alreadyPendingPayment.toTransport()).success()

        val billingAccountableParty =
            billingAccountablePartyService.get(invoiceLiquidation.billingAccountablePartyId).get()

        val sendEmail = invoiceLiquidation.installment == 1

        return InvoicePaymentBuilder.buildInvoicePaymentWithDetailsForLiquidation(
            paymentMethod,
            dueDate?.atStartOfDay() ?: invoiceLiquidation.dueDate.atStartOfDay(),
            reason,
            billingAccountableParty,
            invoiceLiquidation.amount,
            origin,
            sendEmail
        ).copy(invoiceLiquidationId = invoiceLiquidation.id)
            .let {
                createInvoicePaymentWithoutMember(it, billingAccountableParty, false)
            }
    }

    override suspend fun listInvoicePaymentsByBillingAccountablePartyIds(
        billingAccountablePartyIds: List<UUID>,
        withPaymentDetails: Boolean
    ): Result<List<InvoicePayment>, Throwable> {
        val invoicePayments = invoicePaymentDataService.find {
            where {
                this.billingAccountablePartyId.inList(billingAccountablePartyIds.map { it.toString() })
            }
        }
        val invoicePaymentTransports = invoicePayments.mapEach { it.toTransport() }
        return if (withPaymentDetails) listInvoicePaymentsWithDetails(invoicePaymentTransports) else invoicePaymentTransports
    }

    override suspend fun findForApprovedWithoutAmountPaidDefined(range: IntRange) =
        invoicePaymentDataService.find {
            where {
                status.eq(InvoicePaymentStatus.APPROVED) and amountPaid.isNull()
            }.limit { range.count() }.offset { range.first }
        }.flatMap { listInvoicePaymentsWithDetails(it.map { it.toTransport() }.success()) }

    override suspend fun update(invoicePayment: InvoicePayment): Result<InvoicePayment, Throwable> =
        invoicePaymentDataService.update(invoicePayment.toModel())
            .then { logger.info("invoicePayment updated", "invoicePayment" to invoicePayment) }
            .thenError { logger.error("update - invoicePayment failed", "exception" to it) }
            .map { it.toTransport() }

    private suspend fun validateAndAdjustCreation(
        invoicePayment: InvoicePayment,
    ): Result<InvoicePayment, Throwable> {
        if (invoicePayment.memberInvoiceId == null) return InvoicePaymentHasNoMemberInvoice("This InvoicePayment: ${invoicePayment.id} has no member invoice associate it").failure()

        val memberInvoiceId = invoicePayment.memberInvoiceId!!

        val memberInvoice = memberInvoiceDataService.get(memberInvoiceId).then {
            logger.info("MemberInvoice found", it)
        }.getOrNullIfNotFound()
            ?: return MemberInvoiceNotFoundException(memberInvoiceId.toString()).failure()

        if (memberInvoice.status != InvoiceStatus.OPEN)
            return MemberInvoiceNotOpenedException("Invoice ${memberInvoice.id} is not open yet").failure()

        if (memberInvoice.totalAmount.money != invoicePayment.amount)
            return InvoicePaymentAmountNotAllowed(invoicePayment.amount, memberInvoice.id).failure()

        val invoicePayments =
            listInvoicePayments(memberInvoiceId, withPaymentDetails = false).getOrNullIfNotFound()

        if (invoicePayments != null) {
            val hasPendingPayment = invoicePayments.any { it.isPending }

            if (hasPendingPayment)
                return PendingInvoicePaymentException(memberInvoiceId).failure()
        }

        val reason =
            memberInvoice.type?.toPaymentReason()?.let { PaymentReason.valueOf(it.name) } ?: invoicePayment.reason

        return invoicePayment.copy(reason = reason).success()
    }

    override suspend fun createExternalPaymentForInvoicePayment(
        invoicePayment: InvoicePayment,
        billingAccountableParty: BillingAccountableParty,
        personId: PersonId?
    ): Result<InvoicePayment, Throwable> {
        return invoicePayment.paymentDetail?.let {
            invoicePayment.dueDateFromDetail?.let {
                if (it.toLocalDate().isBefore(LocalDate.now())) return PaymentValidationException(
                    "A data de vencimento não pode estar no passado",
                    "invalid_payment_response",
                ).failure()
            }

            createExternalPayment(invoicePayment, billingAccountableParty, personId)
                .map { it.toTransport() }
        } ?: run {
            logger.error(
                "No paymentDetail information found for invoicePayment. External payment won't be created",
                "invoice_payment" to invoicePayment,
                "billing_accountable_party_id" to billingAccountableParty.id,
            )
            InvoicePaymentDetailsNotFoundException(invoicePayment.id).failure()
        }
    }

    private suspend fun createExternalPayment(
        invoicePayment: InvoicePayment,
        billingAccountableParty: BillingAccountableParty,
        personId: PersonId? = null,
    ): Result<InvoicePaymentModel, Throwable> = issueInvoice(invoicePayment, billingAccountableParty)
        .then {
            if (invoicePayment.method == PaymentMethod.BOLETO) {
                val event = PaymentDetailCreatedEvent(
                    personId
                )

                kafkaProducerService.produce(event, personId.toString())
            }
        }.then { createPaymentDetail(it.toTransport()) }

    private suspend fun issueInvoice(
        invoicePayment: InvoicePayment,
        billingAccountableParty: BillingAccountableParty,
    ): Result<InvoicePaymentModel, Throwable> {
        logger.info(
            "Start issuing ${invoicePayment.method} with external partner",
            "invoice_payment" to invoicePayment,
            "payment_detail" to invoicePayment.paymentDetail,
            "billing_accountable_party_id" to billingAccountableParty.id
        )

        return paymentService.issueInvoicePayment(invoicePayment, billingAccountableParty)
            .flatMapPair { updateWithExternalId(invoicePayment, it.id) }
            .map { (invoicePayment, response) -> response.enrichDetailForCreation(invoicePayment) }
    }

    private suspend fun updateWithExternalId(invoicePayment: InvoicePayment, externalId: String) =
        invoicePaymentDataService.get(invoicePayment.id)
            .flatMap { invoicePaymentDataService.update(it.copy(externalId = externalId)) }
            .map { invoicePaymentUpdated -> invoicePaymentUpdated.withPaymentDetail(invoicePayment.toModel().paymentDetail) }
            .then {
                logger.info(
                    "InvoicePayment updated with externalId",
                    "invoice_payment" to invoicePayment,
                )
            }

    private suspend fun listInvoicePaymentsWithDetails(invoicePayments: Result<List<InvoicePayment>, Throwable>): Result<List<InvoicePayment>, Throwable> =
        coroutineScope {
            val invoicePaymentsIds =
                invoicePayments.mapEach { it.id }.get().filterNot { it.isPersonId() }
            val boletoDetailsDeferred =
                async {
                    paymentDetailService.getPaymentDetails(invoicePaymentsIds, PaymentMethod.BOLETO)
                        .getOrNullIfNotFound()
                }

            val pixDetailsDeferred =
                async {
                    paymentDetailService.getPaymentDetails(invoicePaymentsIds, PaymentMethod.PIX)
                        .getOrNullIfNotFound()
                }

            val simpleCreditCardDetailsDeferred =
                async {
                    paymentDetailService.getPaymentDetails(invoicePaymentsIds, PaymentMethod.SIMPLE_CREDIT_CARD)
                        .getOrNullIfNotFound()
                }

            val bolepixDetailsDeferred =
                async {
                    paymentDetailService.getPaymentDetails(invoicePaymentsIds, PaymentMethod.BOLEPIX)
                        .getOrNullIfNotFound()
                }

            val boletoDetails = boletoDetailsDeferred.await()
            val pixDetails = pixDetailsDeferred.await()
            val bolepixDetails = bolepixDetailsDeferred.await()
            val simpleCreditCardDetails = simpleCreditCardDetailsDeferred.await()

            invoicePayments
                .mapEach { invoicePayment ->
                    val paymentBoletoDetails = boletoDetails?.filter { it.paymentId == invoicePayment.id }
                    val paymentPixDetails = pixDetails?.filter { it.paymentId == invoicePayment.id }
                    val paymentBolepixDetails = bolepixDetails?.filter { it.paymentId == invoicePayment.id }
                    val paymentSimpleCreditCardDetails =
                        simpleCreditCardDetails?.filter { it.paymentId == invoicePayment.id }

                    val paymentDetails = paymentBoletoDetails
                        .plusSafe(paymentPixDetails)
                        .plusSafe(paymentBolepixDetails)
                        .plusSafe(paymentSimpleCreditCardDetails)
                        .firstOrNull()

                    invoicePayment.withPaymentDetail(paymentDetails)
                }
        }

}
