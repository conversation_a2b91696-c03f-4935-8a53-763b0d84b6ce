package br.com.alice.app.content.ioc

import br.com.alice.app.content.SERVICE_NAME
import br.com.alice.app.content.client.AliceScreensService
import br.com.alice.app.content.client.AppContentABTestService
import br.com.alice.app.content.client.AppContentScreenDetailService
import br.com.alice.app.content.client.AppointmentScheduleEventService
import br.com.alice.app.content.client.BottomTabsService
import br.com.alice.app.content.client.DuquesaScreensService
import br.com.alice.app.content.client.PlanDetailsMenuService
import br.com.alice.app.content.client.screens.*
import br.com.alice.app.content.client.sections.DrawerSectionService
import br.com.alice.app.content.consumers.ActionPlanTaskUpsertedConsumer
import br.com.alice.app.content.consumers.HealthFormFinishedConsumer
import br.com.alice.app.content.consumers.HealthScoreResultConsumer
import br.com.alice.app.content.consumers.MemberOnboardingDroppedConsumer
import br.com.alice.app.content.consumers.MemberOnboardingFinishedConsumer
import br.com.alice.app.content.consumers.TestResultFeedbackCreatedConsumer
import br.com.alice.app.content.consumers.TestResultFeedbackOpenedConsumer
import br.com.alice.app.content.controllers.InternalFeatureController
import br.com.alice.app.content.controllers.RecurringController
import br.com.alice.app.content.controllers.ScreenDetailController
import br.com.alice.app.content.services.AppContentABTestServiceImpl
import br.com.alice.app.content.services.AppContentScreenDetailServiceImpl
import br.com.alice.app.content.services.AppointmentScheduleEventServiceImpl
import br.com.alice.app.content.services.BottomTabsServiceImpl
import br.com.alice.app.content.services.CopayDetailsScreenServiceImpl
import br.com.alice.app.content.services.CopayScreenServiceImpl
import br.com.alice.app.content.services.DrawerSectionServiceImpl
import br.com.alice.app.content.services.PlanDetailsMenuServiceImpl
import br.com.alice.app.content.services.TestCodesService
import br.com.alice.app.content.services.device.DeviceVersionServiceImpl
import br.com.alice.app.content.services.drawer.CopayDrawerServiceImpl
import br.com.alice.app.content.services.drawer.DuquesaHomeDrawerServiceImpl
import br.com.alice.app.content.services.drawer.ExplorerDrawerServiceImpl
import br.com.alice.app.content.services.drawer.MainMenuDrawerServiceImpl
import br.com.alice.app.content.services.drawer.PlanDetailsDrawerServiceImpl
import br.com.alice.app.content.services.drawer.RedesignUnifiedHealthDrawerServiceImpl
import br.com.alice.app.content.services.drawer.UnifiedHealthDrawerServiceImpl
import br.com.alice.app.content.services.router.AliceScreensServiceImpl
import br.com.alice.app.content.services.router.DuquesaScreensServiceImpl
import br.com.alice.app.content.services.screens.*
import br.com.alice.app.content.services.screens.health.HealthAllDemandsScreenEmptyStateServiceImpl
import br.com.alice.app.content.services.screens.health.HealthProfessionalRecommendationScreenService
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.redis.CacheFactory
import org.koin.dsl.module

val ServiceModule = module(createdAtStart = true) {

    val cache = CacheFactory.newInstance("app-content-domain-service")

    // Servers
    loadServiceServers("br.com.alice.app.content.services")

    // Controllers
    single { HealthController(SERVICE_NAME) }
    single { RecurringController(get(), get(), get()) }
    single { ScreenDetailController(get()) }
    single { InternalFeatureController(get()) }

    // Kafka Consumers
    single { HealthFormFinishedConsumer(get(), get()) }
    single { MemberOnboardingDroppedConsumer(get()) }
    single { MemberOnboardingFinishedConsumer(get()) }
    single { HealthScoreResultConsumer(get()) }
    single { ActionPlanTaskUpsertedConsumer(get(), get()) }
    single { TestResultFeedbackCreatedConsumer(get()) }
    single { TestResultFeedbackOpenedConsumer(get()) }

    // Services
    single<BottomTabsService> { BottomTabsServiceImpl(get()) }
    single<AliceScreensService> {
        AliceScreensServiceImpl(
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
        )
    }
    single<DuquesaScreensService> { DuquesaScreensServiceImpl(get(), get(), get(), get()) }
    single<AliceNowScreenService> { AliceNowScreenServiceImpl(get(), get()) }
    single<UnifiedHomeScreenService> { UnifiedHomeScreenServiceImpl(get(), get(), get(), get(), get(), get()) }
    single<AppointmentHubScreenService> { AppointmentHubScreenServiceImpl(get<AppointmentScheduleEventService>() as AppointmentScheduleEventServiceImpl, get(), get()) }
    single<MainMenuService> { MainMenuServiceImpl() }
    single<HealthScreenService> {
        HealthScreenServiceImpl(
            get(),
            get(),
            get()
        )
    }
    single<DemandScreenService> { DemandScreenServiceImpl(get(), get()) }
    single<AppContentScreenDetailService> { AppContentScreenDetailServiceImpl(get(), get()) }
    single<DrawerSectionService> { DrawerSectionServiceImpl(get(), get(), get(), get(), get(), get(), get()) }
    single<AccreditedNetworkMenuService> { AccreditedNetworkMenuServiceImpl(get(), get()) }
    single<CopayScreenService> { CopayScreenServiceImpl(get()) }
    single<CopayDetailsScreenService> { CopayDetailsScreenServiceImpl() }
    single<OmbudsmanScreenService> { OmbudsmanScreenServiceImpl() }

    single<DuquesaHomeScreenService> { DuquesaHomeScreenServiceImpl(get(), get(), get(), get(), get()) }
    single<DuquesaServiceScreenService> { DuquesaServiceScreenServiceImpl(get(), get()) }
    single<DuquesaMainMenuScreenService> { DuquesaMainMenuScreenServiceImpl() }
    single<TestResultScreenService> { TestResultScreenServiceImpl() }
    single<ExplorerScreenService> { ExplorerScreenServiceImpl() }
    single<RedesignUnifiedHealthScreenService> {
        RedesignUnifiedHealthScreenServiceImpl(
            get(),
            get(),
            get(),
            get()
        )
    }
    single<RedesignHealthPlanScreenService> {
        RedesignHealthPlanScreenServiceImpl(
            get(),
            get(),
            get(),
            get(),
            get(),
            get()
        )
    }
    single<MemberProfileScreenService> { MemberProfileScreenServiceImpl() }

    single<ScreeningScreenService> { ScreeningScreenServiceImpl(get(), get()) }
    single<AppContentABTestService> { AppContentABTestServiceImpl(get(), get()) }
    single<MemberOnboardingScreeService> { MemberOnboardingScreeServiceImpl(get()) }
    single<AppointmentScheduleEventService> { AppointmentScheduleEventServiceImpl(get(), get(), get(), get(), get()) }
    single<PlanDetailsMenuService> { PlanDetailsMenuServiceImpl(get(), get(), get(), get()) }

    // Internal Services
    single { CopayDrawerServiceImpl(get(), get(), get()) }
    single { MainMenuDrawerServiceImpl(get()) }
    single { DuquesaHomeDrawerServiceImpl() }
    single { UnifiedHealthDrawerServiceImpl() }
    single { DeviceVersionServiceImpl(get()) }
    single { HealthAllDemandsScreenEmptyStateServiceImpl(get()) }
    single { RedesignUnifiedHealthDrawerServiceImpl() }
    single { TestCodesService(get(), get(), get(), cache) }
    single { TaskListServiceImpl(get(), get(), get()) }
    single { ExplorerDrawerServiceImpl(get()) }
    single { PlanDetailsDrawerServiceImpl(get()) }
    single { HealthProfessionalRecommendationScreenService(get(), get(), get(), get(), get(), cache) }
}
