{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "CassiMemberModel", "Generic<PERSON><PERSON><PERSON><PERSON>", "PersonBillingAccountablePartyModel", "HealthDeclaration", "InsurancePortabilityRequestModel", "PersonGracePeriod", "HealthCondition", "ProductBundleModel", "ProviderModel", "ProviderUnitModel", "StructuredAddress", "ContactModel", "HealthCommunitySpecialistModel", "HealthcareResourceGroupAssociationModel", "FileVault", "ProviderUnitGroupModel", "HealthcareResourceGroupModel", "TotvsGuiaModel", "HealthProfessionalModel", "HealthProfessionalOpsProfileModel"], "actions": ["view", "count"]}, {"resources": ["TrackPersonABModel"], "actions": ["view", "create"]}, {"resources": ["NullvsIntegrationLogModel", "MemberInvoiceGroupModel", "InvoiceLiquidationModel", "MemberInvoiceModel", "InvoicePaymentModel", "CancelPaymentOnAcquirerScheduleModel", "ResourceSignTokenModel", "ItauPaymentModel", "PaymentDetailModel", "BoletoPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "PixPaymentDetailModel", "BolepixPaymentDetailModel", "InvoiceItemModel", "CompanyContractModel", "CompanySubContractModel", "BillingAccountablePartyModel", "MemberProductPriceModel"], "actions": ["view", "create", "count", "update"]}, {"resources": ["CompanyModel", "PersonModel", "Beneficiary<PERSON><PERSON>l"], "actions": ["view", "update", "count"]}, {"resources": ["MemberLifeCycleEventsModel", "HealthcareResourceModel", "HealthcareBundleModel", "ProductPriceListingModel", "PriceListingModel", "ProductModel", "MemberModel", "CompanyProductPriceListingModel", "NationalReceiptModel", "HealthInstitutionNegotiationModel", "EitaNullvsIntegrationRecordModel", "EitaNullvsIntegrationLogModel"], "actions": ["view", "create", "update"]}, {"resources": ["NullvsIntegrationRecordModel"], "actions": ["view", "create", "count", "update", "delete"]}]}]}