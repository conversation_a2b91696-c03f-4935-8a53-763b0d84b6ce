package br.com.alice.business.consumers

import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyProductConfigurationService
import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.converters.CompanyConverter.toCompanyActivationFilesInput
import br.com.alice.business.events.CognitoCompanyUpsertRequestedEvent
import br.com.alice.business.events.CognitoCompanyUpsertedEvent
import br.com.alice.business.events.CompanyActivationFilesRequestedEvent
import br.com.alice.business.events.CompanyUpsertedEvent
import br.com.alice.business.events.CompanyWasUpdatedOnHubspotAfterCognitoIsPopulatedEvent
import br.com.alice.business.model.CognitoCompanyMemberRequest
import br.com.alice.business.model.CognitoCompanyRequest
import br.com.alice.business.model.CognitoFile
import br.com.alice.business.model.CompanySalesDetails
import br.com.alice.business.model.toCompany
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.normalizeCnpjWithoutMask
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.communication.crm.hubspot.b2b.HubspotSalesFunnelPipelineImpl.Companion.backgroundCheckStageId
import br.com.alice.communication.crm.hubspot.b2b.HubspotSalesFunnelPipelineImpl.Companion.pipelineIdBrokerInternal
import br.com.alice.communication.crm.hubspot.b2b.client.DealResponse
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotCompanyDeal
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotDealSearchResponse
import br.com.alice.communication.crm.sales.b2b.BusinessCompanyDeal
import br.com.alice.communication.crm.sales.b2b.BusinessSalesCrmPipeline
import br.com.alice.communication.crm.sales.b2b.DealResult
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyActivationFileType.PROOF_OF_PAYMENT
import br.com.alice.data.layer.models.CompanyActivationFileType.PROOF_OF_PLAN
import br.com.alice.data.layer.models.CompanyAddress
import br.com.alice.data.layer.models.CompanyBusinessUnit
import br.com.alice.data.layer.models.CompanyContractType
import br.com.alice.data.layer.models.CompanyProductType
import br.com.alice.data.layer.models.CompanySize
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.CompanySubContractStatus
import br.com.alice.data.layer.models.ConfigurationVersion
import br.com.alice.data.layer.models.DealStage
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.PriceAdjustmentType
import br.com.alice.data.layer.models.ProductPriceListingConfiguration
import br.com.alice.nullvs.models.NatureCode
import br.com.alice.product.client.PriceListingService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertFailsWith

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CompanyConsumerTest : ConsumerTest() {

    private val companyService: CompanyService = mockk()
    private val companyProductConfigurationService: CompanyProductConfigurationService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val businessSalesCrmPipeline: BusinessSalesCrmPipeline = mockk()
    private val companyContractService: CompanyContractService = mockk()
    private val companySubContractService: CompanySubContractService = mockk()
    private val companyProductPriceListingService: CompanyProductPriceListingService = mockk()
    private val productService: ProductService = mockk()
    private val priceListingService: PriceListingService = mockk()

    private val consumer = CompanyConsumer(
        companyService,
        companyProductConfigurationService,
        kafkaProducerService,
        businessSalesCrmPipeline,
        companyContractService,
        companySubContractService,
        companyProductPriceListingService,
        productService,
        priceListingService
    )
    private val companySalesDetails = CompanySalesDetails(
        sellerName = "Alice",
        salesAgentDocument = "202.149.300-84",
        salesAgentPhoneNumber = "11-*********",
        salesAgentSupervisorNationalId = "202.149.300-84"
    )
    private val cognitoCompanyRequest = CognitoCompanyRequest(
        id = "2",
        name = "Acme",
        legalName = "Acme Company",
        cnpj = "08.337.157/0001-19",
        email = "<EMAIL>",
        phoneNumber = "11-*********",
        rangeOfBeneficiaries = "2 a 5",
        contractType = "Livre adesão",
        contractStartedAt = "2023-10-01",
        beneficiariesCountAtDayZero = "10",
        microOrIndividualCompany = "MEI",
        billingAccountablePartyEmail = "<EMAIL>",
        billingAccountablePartyPhoneNumber = "11-*********",
        members = emptyList(),
        fgtsFiles = emptyList(),
        workRegisterBookletFiles = emptyList(),
        broker = companySalesDetails.sellerName,
        agentNationalId = companySalesDetails.salesAgentDocument,
        agentPhoneNumber = companySalesDetails.salesAgentPhoneNumber,
        agentSupervisorNationalId = companySalesDetails.salesAgentSupervisorNationalId
    )
    private val cognitoCompanyUpsertRequestedEvent = CognitoCompanyUpsertRequestedEvent(cognitoCompanyRequest)
    private val availableProductIds = listOf(
        RangeUUID.generate(),
        RangeUUID.generate()
    )
    private val defaultProductId = availableProductIds.firstOrNull() ?: RangeUUID.generate()

    private val companyContract = TestModelFactory.buildCompanyContract()
    private val company = TestModelFactory.buildCompany(
        name = cognitoCompanyRequest.name,
        legalName = cognitoCompanyRequest.legalName,
        cnpj = cognitoCompanyRequest.cnpj.normalizeCnpjWithoutMask(),
        address = CompanyAddress(
            postalCode = "",
            State = "",
            city = "",
            street = "",
            number = 0
        ),
        email = cognitoCompanyRequest.email!!,
        phoneNumber = cognitoCompanyRequest.phoneNumber.orEmpty(),
        availableProducts = emptyList(),
        defaultProductId = defaultProductId,
        contractStartedAt = LocalDateTime.of(2023, 10, 1, 0, 0),
        beneficiariesCountAtDayZero = 10,
        defaultFlowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW,
        priceAdjustmentType = PriceAdjustmentType.POOL_SMALL_COMPANY,
        contractIds = listOf(companyContract.id)
    )
    private val companySubContract = TestModelFactory.buildCompanySubContract()

    private val companyUpsertedEvent = CompanyUpsertedEvent(company)
    private val companyProductConfiguration = TestModelFactory.buildCompanyProductConfiguration(
        companyProductType = CompanyProductType.MICRO_2_5_OPTIONAL,
        availableProductIds = availableProductIds,
    )
    private val companyAddress = CompanyAddress(
        postalCode = "",
        State = "",
        city = "",
        street = "",
        number = 0
    )

    private val producerResult: ProducerResult = mockk()

    private val membersWithFiles = listOf(
        CognitoCompanyMemberRequest(
            fullName = "MARIA DA SILVA",
            nationalId = "",
            email = cognitoCompanyRequest.email,
            phone = cognitoCompanyRequest.phoneNumber.orEmpty(),
            biologicalSex = "Masculino",
            dateOfBirth = "01/01/2000",
            mothersName = "Alice",
            nationalDocumentationFiles = listOf(
                CognitoFile(
                    fileName = "file.pdf",
                    contentType = "application/pdf",
                    fileAddress = "https://www.internet.com/file"
                ),
                CognitoFile(
                    fileName = "file.pdf",
                    contentType = "application/pdf",
                    fileAddress = "https://www.internet.com/file"
                )
            )
        ),
        CognitoCompanyMemberRequest(
            fullName = "JOSE PINHEIRO",
            nationalId = "",
            email = cognitoCompanyRequest.email,
            phone = cognitoCompanyRequest.phoneNumber.orEmpty(),
            biologicalSex = "Masculino",
            dateOfBirth = "01/01/2000",
            mothersName = "Alice",
            nationalDocumentationFiles = listOf(
                CognitoFile(
                    fileName = "file.pdf",
                    contentType = "application/pdf",
                    fileAddress = "https://www.internet.com/file"
                ),
                CognitoFile(
                    fileName = "file.pdf",
                    contentType = "application/pdf",
                    fileAddress = "https://www.internet.com/file"
                )
            )
        )
    )

    private val cognitoCompanyRequestWithFiles = cognitoCompanyRequest.copy(
        fgtsFiles = listOf(
            CognitoFile(
                fileName = "file.pdf",
                contentType = "application/pdf",
                fileAddress = "https://www.internet.com/file"
            )
        ),
        workRegisterBookletFiles = listOf(
            CognitoFile(
                fileName = "file.pdf",
                contentType = "application/pdf",
                fileAddress = "https://www.internet.com/file"
            )
        ),
        members = membersWithFiles
    )

    private val eventWithFiles = CognitoCompanyUpsertRequestedEvent(cognitoCompanyRequestWithFiles)

    @BeforeTest
    fun setup() {
        super.before()

        coEvery { companyService.findByEmails(any()) } returns listOf(company)
        coEvery { kafkaProducerService.produce(any<CognitoCompanyUpsertedEvent>()) } returns producerResult
    }

    @Test
    fun `#handleCognitoCompanyUpsertRequested should create and return company`() =
        mockRangeUuidAndDateTime { uuid, localDateTime ->
            runBlocking {
                val priceListings = listOf(
                    TestModelFactory.buildPriceListing(),
                    TestModelFactory.buildPriceListing(),
                    TestModelFactory.buildPriceListing(),
                )
                val productId = RangeUUID.generate()
                val billingAccountablePartyId = RangeUUID.generate()

                val products = listOf(
                    TestModelFactory.buildProduct(id = availableProductIds[0], ansNumber = "12345")
                        .copy(priceListing = priceListings[0]),
                    TestModelFactory.buildProduct(ansNumber = "1234").copy(priceListing = priceListings[1]),
                    TestModelFactory.buildProduct(id = productId, ansNumber = "123")
                        .copy(priceListing = priceListings[2]),
                )

                val companyProductPriceListing = listOf(
                    TestModelFactory.buildCompanyProductPriceListing(
                        productId = availableProductIds[0],
                        priceListingId = priceListings[0].id,
                    ),
                    TestModelFactory.buildCompanyProductPriceListing(
                        productId = products[1].id,
                        priceListingId = priceListings[1].id,
                    ),
                )

                val subcontractToBeAdded = CompanySubContract(
                    id = uuid,
                    title = "Subcontrato ${company.cnpj}",
                    billingAccountablePartyId = company.billingAccountablePartyId,
                    companyId = company.id,
                    contractId = company.contractIds.first(),
                    isProRata = cognitoCompanyRequest.isProRata,
                    flexBenefit = true,
                    hasEmployeesAbroad = company.hasEmployeesAbroad ?: true,
                    defaultProductId = company.defaultProductId,
                    availableProducts = company.availableProducts,
                    defaultFlowType = company.defaultFlowType,
                    nature = NatureCode.B2B_ALICE.value,
                    dueDate = 15,
                    isBillingLevel = true,
                    paymentType = cognitoCompanyRequest.paymentType,
                    hasRetroactiveCharge = cognitoCompanyRequest.hasRetroactiveCharge,
                    billingGroup = cognitoCompanyRequest.billingGroup?.code,
                    status = CompanySubContractStatus.DRAFT,
                    createdAt = localDateTime
                )

                coEvery {
                    companyProductConfigurationService.findByCompanyProductTypeAndConfigurationVersion(
                        CompanyProductType.MICRO_2_5_OPTIONAL,
                        ConfigurationVersion.STABLE
                    )
                } returns companyProductConfiguration.copy(
                    productPriceListIds = listOf(
                        ProductPriceListingConfiguration(
                            productId = availableProductIds[0],
                            priceListingId = priceListings[0].id,
                        ),
                        ProductPriceListingConfiguration(
                            productId = products[1].id,
                            priceListingId = priceListings[1].id,
                        ),
                        ProductPriceListingConfiguration(
                            productId = products[2].id,
                            priceListingId = priceListings[2].id,
                        )
                    )
                )

                coEvery { productService.findByIds(any(), any()) } returns products
                coEvery { priceListingService.getList(any()) } returns priceListings

                coEvery {
                    companyService.findByCnpj(cognitoCompanyRequest.cnpj.normalizeCnpjWithoutMask())
                } returns NotFoundException("not_found")

                val expectedCompanyModel = buildCompanyModel(
                    cognitoCompanyRequest = cognitoCompanyRequest,
                    alreadyExistentCompany = null,
                    companyAvailableProductIds = availableProductIds,
                    companyProductType = companyProductConfiguration.companyProductType,
                    contractType = CompanyContractType.MANDATORY,
                    companyBusinessUnit = null
                ).copy(billingAccountablePartyId = billingAccountablePartyId)

                coEvery {
                    companyService.createOrGetBillingAccountablePartyForCompany(
                        match {
                            it.address == expectedCompanyModel.address
                                    && it.name == expectedCompanyModel.name
                                    && it.legalName == expectedCompanyModel.legalName
                                    && it.cnpj == expectedCompanyModel.cnpj
                        },
                        cognitoCompanyRequest.billingAccountablePartyEmail
                    )
                } returns billingAccountablePartyId

                coEvery {
                    companyService.add(
                        company = match {
                            it.cnpj == expectedCompanyModel.cnpj
                                    && it.address == companyAddress
                        },
                        shouldIgnoreCheck = false,
                        billingAccountablePartyEmail = cognitoCompanyRequest.billingAccountablePartyEmail,
                        companySalesDetails = companySalesDetails
                    )
                } returns company

                coEvery { companyContractService.add(any(), any()) } returns companyContract
                coEvery {
                    companySubContractService.add(subcontractToBeAdded, false)
                } returns subcontractToBeAdded.success()

                coEvery { companyProductPriceListingService.findCurrentBySubContractId(any()) } returns emptyList()

                coEvery {
                    companyProductPriceListingService.addListToSubContract(
                        any(),
                        any()
                    )
                } returns companyProductPriceListing


                val result = consumer.handleCognitoCompanyUpsertRequested(cognitoCompanyUpsertRequestedEvent)
                assertThat(result.get()).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
                    .isEqualTo(company)

                coVerifyOnce {
                    companyService.add(
                        any(),
                        any(),
                        cognitoCompanyRequest.billingAccountablePartyEmail,
                        companySalesDetails = companySalesDetails
                    )
                }
                coVerifyOnce {
                    companyContractService.add(
                        match { it.accountableEmail == company.email },
                        sendEvent = false
                    )
                }
                coVerifyOnce { companySubContractService.add(match { it.companyId == company.id }, sendEvent = false) }
                coVerifyOnce {
                    companyProductPriceListingService.addListToSubContract(match {
                        (it[0].productId == companyProductPriceListing[0].productId) &&
                                (it[1].productId == companyProductPriceListing[1].productId)
                    }, subcontractToBeAdded)
                }
            }
        }

    @Test
    fun `#handleCognitoCompanyUpsertRequested should create and return company with company size`() = runBlocking {
        val priceListings = listOf(
            TestModelFactory.buildPriceListing(),
            TestModelFactory.buildPriceListing(),
            TestModelFactory.buildPriceListing(),
        )
        val productId = RangeUUID.generate()
        val billingAccountablePartyId = RangeUUID.generate()

        val products = listOf(
            TestModelFactory.buildProduct(id = availableProductIds[0], ansNumber = "12345")
                .copy(priceListing = priceListings[0]),
            TestModelFactory.buildProduct(ansNumber = "1234").copy(priceListing = priceListings[1]),
            TestModelFactory.buildProduct(id = productId, ansNumber = "123").copy(priceListing = priceListings[2]),
        )

        val companyProductPriceListing = listOf(
            TestModelFactory.buildCompanyProductPriceListing(
                productId = availableProductIds[0],
                priceListingId = priceListings[0].id,
            ),
            TestModelFactory.buildCompanyProductPriceListing(
                productId = products[1].id,
                priceListingId = priceListings[1].id,
            ),
        )

        coEvery {
            companyProductConfigurationService.findByCompanyProductTypeAndConfigurationVersion(
                CompanyProductType.MICRO_2_5_OPTIONAL,
                ConfigurationVersion.STABLE
            )
        } returns companyProductConfiguration.copy(
            productPriceListIds = listOf(
                ProductPriceListingConfiguration(
                    productId = availableProductIds[0],
                    priceListingId = priceListings[0].id,
                ),
                ProductPriceListingConfiguration(
                    productId = products[1].id,
                    priceListingId = priceListings[1].id,
                ),
                ProductPriceListingConfiguration(
                    productId = products[2].id,
                    priceListingId = priceListings[2].id,
                )
            )
        )

        coEvery { productService.findByIds(any(), any()) } returns products
        coEvery { priceListingService.getList(any()) } returns priceListings

        coEvery {
            companyService.findByCnpj(cognitoCompanyRequest.cnpj.normalizeCnpjWithoutMask())
        } returns NotFoundException("not_found")

        val expectedCompanyModel = buildCompanyModel(
            cognitoCompanyRequest = cognitoCompanyRequest,
            alreadyExistentCompany = null,
            companyAvailableProductIds = availableProductIds,
            companyProductType = companyProductConfiguration.companyProductType,
            contractType = CompanyContractType.MANDATORY,
            companyBusinessUnit = null
        ).copy(billingAccountablePartyId = billingAccountablePartyId)

        coEvery {
            companyService.createOrGetBillingAccountablePartyForCompany(
                match {
                    it.address == expectedCompanyModel.address
                            && it.name == expectedCompanyModel.name
                            && it.legalName == expectedCompanyModel.legalName
                            && it.cnpj == expectedCompanyModel.cnpj
                },
                cognitoCompanyRequest.billingAccountablePartyEmail
            )
        } returns billingAccountablePartyId
        coEvery {
            companyService.add(
                company = match {
                    it.cnpj == expectedCompanyModel.cnpj
                            && it.address == companyAddress
                            && it.companySize == CompanySize.MLA
                },
                shouldIgnoreCheck = false,
                billingAccountablePartyEmail = cognitoCompanyRequest.billingAccountablePartyEmail,
                companySalesDetails = companySalesDetails
            )
        } returns company.copy()

        coEvery { companyContractService.add(any(), any()) } returns companyContract
        coEvery { companySubContractService.add(any(), any()) } returns companySubContract

        coEvery { companyProductPriceListingService.findCurrentBySubContractId(any()) } returns emptyList()

        coEvery {
            companyProductPriceListingService.addListToSubContract(
                any(),
                any()
            )
        } returns companyProductPriceListing

        val event = CognitoCompanyUpsertRequestedEvent(
            cognitoCompanyRequest.copy(totalEmployees = 50)
        )

        val result = consumer.handleCognitoCompanyUpsertRequested(event)
        assertThat(result.get()).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
            .isEqualTo(company)

        coVerifyOnce {
            companyService.add(
                any(),
                any(),
                cognitoCompanyRequest.billingAccountablePartyEmail,
                companySalesDetails = companySalesDetails
            )
        }
        coVerifyOnce { companyService.createOrGetBillingAccountablePartyForCompany(any(), any()) }
        coVerifyOnce { companyContractService.add(match { it.accountableEmail == company.email }, sendEvent = false) }
        coVerifyOnce { companySubContractService.add(match { it.companyId == company.id }, sendEvent = false) }
        coVerifyOnce {
            companyProductPriceListingService.addListToSubContract(match {
                (it[0].productId == companyProductPriceListing[0].productId) &&
                        (it[1].productId == companyProductPriceListing[1].productId)
            }, companySubContract)
        }
    }

    @Test
    fun `#handleCognitoCompanyUpsertRequested should update and return company (with older contract reference)`() =
        runBlocking {
            coEvery {
                companyProductConfigurationService.findByCompanyProductTypeAndConfigurationVersion(
                    CompanyProductType.MICRO_2_5_OPTIONAL,
                    ConfigurationVersion.STABLE
                )
            } returns companyProductConfiguration
            coEvery {
                companyService.findByCnpj(cognitoCompanyRequest.cnpj.normalizeCnpjWithoutMask())
            } returns company
            coEvery { companySubContractService.findByCompanyId(any()) } returns listOf(companySubContract)

            val expectedCompanyModel = buildCompanyModel(
                cognitoCompanyRequest = cognitoCompanyRequest,
                alreadyExistentCompany = company,
                companyAvailableProductIds = availableProductIds,
                companyProductType = companyProductConfiguration.companyProductType,
                contractType = CompanyContractType.OPTIONAL,
                companyBusinessUnit = CompanyBusinessUnit.CORRETORES
            )
            val expectedResult = company.copy(availableProducts = availableProductIds)
            coEvery {
                companyService.update(
                    company = expectedCompanyModel,
                    shouldIgnoreCheck = false,
                    companySalesDetails = companySalesDetails
                )
            } returns expectedResult

            val result = consumer.handleCognitoCompanyUpsertRequested(cognitoCompanyUpsertRequestedEvent)
            assertThat(result.get()).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
                .isEqualTo(expectedResult)

            coVerifyOnce { companyService.update(any(), any(), any()) }
        }

    @Test
    fun `#handleCognitoCompanyUpsertRequested should update and return company (with older contract reference) with TRANSITORY`() =
        runBlocking {
            withFeatureFlag(
                FeatureNamespace.BUSINESS,
                "company_configuration_product_current_configuration_version",
                "TRANSITORY"
            ) {
                coEvery {
                    companyProductConfigurationService.findByCompanyProductTypeAndConfigurationVersion(
                        CompanyProductType.MICRO_2_OPTIONAL,
                        ConfigurationVersion.TRANSITORY
                    )
                } returns companyProductConfiguration
                coEvery {
                    companyService.findByCnpj(cognitoCompanyRequest.cnpj.normalizeCnpjWithoutMask())
                } returns company
                coEvery { companySubContractService.findByCompanyId(any()) } returns listOf(companySubContract)

                val expectedCompanyModel = buildCompanyModel(
                    cognitoCompanyRequest = cognitoCompanyRequest,
                    alreadyExistentCompany = company,
                    companyAvailableProductIds = availableProductIds,
                    companyProductType = companyProductConfiguration.companyProductType,
                    contractType = CompanyContractType.OPTIONAL,
                    companyBusinessUnit = CompanyBusinessUnit.CORRETORES
                )
                val expectedResult = company.copy(availableProducts = availableProductIds)
                coEvery {
                    companyService.update(
                        company = expectedCompanyModel,
                        shouldIgnoreCheck = false,
                        companySalesDetails = companySalesDetails
                    )
                } returns expectedResult

                val newEvent =
                    CognitoCompanyUpsertRequestedEvent(cognitoCompanyRequest.copy(rangeOfBeneficiaries = "2"))

                val result = consumer.handleCognitoCompanyUpsertRequested(newEvent)
                assertThat(result.get()).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
                    .isEqualTo(expectedResult)

                coVerifyOnce { companyService.update(any(), any(), any()) }
            }
        }

    @Test
    fun `#handleCognitoCompanyUpsertRequested should update and return company (without contract reference)`() =
        runBlocking {
            coEvery {
                companyProductConfigurationService.findByCompanyProductTypeAndConfigurationVersion(
                    CompanyProductType.MICRO_2_5_OPTIONAL,
                    ConfigurationVersion.STABLE
                )
            } returns companyProductConfiguration
            coEvery {
                companyService.findByCnpj(cognitoCompanyRequest.cnpj.normalizeCnpjWithoutMask())
            } returns company.copy(contractIds = emptyList())
            coEvery { companySubContractService.findByCompanyId(any()) } returns listOf(companySubContract)

            coEvery { companyContractService.add(any(), any()) } returns companyContract

            val expectedCompanyModel = buildCompanyModel(
                cognitoCompanyRequest = cognitoCompanyRequest,
                alreadyExistentCompany = company,
                companyAvailableProductIds = availableProductIds,
                companyProductType = companyProductConfiguration.companyProductType,
                contractType = CompanyContractType.OPTIONAL,
                companyBusinessUnit = CompanyBusinessUnit.CORRETORES
            ).copy(contractIds = listOf(companyContract.id))
            val expectedResult = company.copy(availableProducts = availableProductIds)

            coEvery {
                companyService.update(
                    company = expectedCompanyModel,
                    shouldIgnoreCheck = false,
                    companySalesDetails = companySalesDetails,
                )
            } returns expectedResult

            val result = consumer.handleCognitoCompanyUpsertRequested(cognitoCompanyUpsertRequestedEvent)
            assertThat(result.get()).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
                .isEqualTo(expectedResult)

            coVerifyOnce { companyService.update(any(), any(), any()) }
            coVerifyOnce { companyContractService.add(any(), any()) }
        }

    @Test
    fun `#handleCognitoCompanyUpsertRequested should recreate cppls when subcontract is not synced`() =
        runBlocking {
            val priceListings = listOf(
                TestModelFactory.buildPriceListing(),
                TestModelFactory.buildPriceListing(),
            )
            val companyProductConfigurationWithPriceListing = companyProductConfiguration.copy(
                productPriceListIds = listOf(
                    ProductPriceListingConfiguration(
                        productId = availableProductIds[0],
                        priceListingId = priceListings[0].id
                    ),
                    ProductPriceListingConfiguration(
                        productId = availableProductIds[1],
                        priceListingId = priceListings[1].id
                    )
                )
            )
            val subContractNotSynced = companySubContract.copy(externalId = null)
            val products = listOf(
                TestModelFactory.buildProduct(id = availableProductIds[0], ansNumber = "12345"),
                TestModelFactory.buildProduct(id = availableProductIds[1], ansNumber = "1234")
            )
            val currentCppls = listOf(
                TestModelFactory.buildCompanyProductPriceListing(),
                TestModelFactory.buildCompanyProductPriceListing()
            )
            val newCppls = listOf(
                TestModelFactory.buildCompanyProductPriceListing(
                    priceListingId = priceListings[0].id,
                    productId = availableProductIds[0]
                ),
                TestModelFactory.buildCompanyProductPriceListing(
                    priceListingId = priceListings[1].id,
                    productId = availableProductIds[1]
                )
            )


            coEvery {
                companyProductConfigurationService.findByCompanyProductTypeAndConfigurationVersion(
                    CompanyProductType.MICRO_2_5_OPTIONAL,
                    ConfigurationVersion.STABLE
                )
            } returns companyProductConfigurationWithPriceListing
            coEvery {
                companyService.findByCnpj(cognitoCompanyRequest.cnpj.normalizeCnpjWithoutMask())
            } returns company.copy(contractIds = emptyList())
            coEvery { companySubContractService.findByCompanyId(any()) } returns listOf(subContractNotSynced)
            coEvery { companyProductPriceListingService.findCurrentBySubContractId(subContractNotSynced.id) } returns currentCppls
            coEvery { companyProductPriceListingService.clearFromSubContract(subContractNotSynced.id) } returns listOf(
                true,
                true
            )

            coEvery {
                productService.findByIds(
                    availableProductIds,
                    findOptions = ProductService.FindOptions(withPriceListing = false, withBundles = false)
                )
            } returns products

            coEvery {
                priceListingService.getList(priceListings.map { it.id })
            } returns priceListings

            coEvery {
                companyProductPriceListingService.addListToSubContract(
                    any(),
                    subContractNotSynced
                )
            } returns newCppls
            coEvery { companyContractService.add(any(), any()) } returns companyContract

            val expectedCompanyModel = buildCompanyModel(
                cognitoCompanyRequest = cognitoCompanyRequest,
                alreadyExistentCompany = company,
                companyAvailableProductIds = availableProductIds,
                companyProductType = companyProductConfiguration.companyProductType,
                contractType = CompanyContractType.OPTIONAL,
                companyBusinessUnit = CompanyBusinessUnit.CORRETORES
            ).copy(contractIds = listOf(companyContract.id))
            val expectedResult = company.copy(availableProducts = availableProductIds)

            coEvery {
                companyService.update(
                    company = expectedCompanyModel,
                    shouldIgnoreCheck = false,
                    companySalesDetails = companySalesDetails,
                )
            } returns expectedResult

            val result = consumer.handleCognitoCompanyUpsertRequested(cognitoCompanyUpsertRequestedEvent)
            assertThat(result.get()).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
                .isEqualTo(expectedResult)

            coVerifyOnce { companyService.update(any(), any(), any()) }
            coVerifyOnce { companyContractService.add(any(), any()) }
        }

    @Test
    fun `#handleCognitoCompanyUpsertRequested should do nothing when find company by cnpj throw unhandled error`() =
        runBlocking {
            coEvery {
                companyProductConfigurationService.findByCompanyProductTypeAndConfigurationVersion(
                    CompanyProductType.MICRO_2_5_OPTIONAL,
                    ConfigurationVersion.STABLE
                )
            } returns companyProductConfiguration
            coEvery {
                companyService.findByCnpj(cognitoCompanyRequest.cnpj.normalizeCnpjWithoutMask())
            } returns Exception()

            assertFailsWith<Exception> {
                consumer.handleCognitoCompanyUpsertRequested(
                    cognitoCompanyUpsertRequestedEvent
                )
            }

            coVerifyNone { companyService.update(any()) }
            coVerifyNone { companyService.add(any()) }
        }

    private fun buildCompanyModel(
        cognitoCompanyRequest: CognitoCompanyRequest,
        alreadyExistentCompany: Company?,
        companyAvailableProductIds: List<UUID>,
        companyProductType: CompanyProductType,
        contractType: CompanyContractType,
        companyBusinessUnit: CompanyBusinessUnit?
    ) =
        cognitoCompanyRequest.toCompany(
            alreadyExistentCompany = alreadyExistentCompany,
            companyAddress = companyAddress,
            availableProductIds = companyAvailableProductIds,
            companyProductType = companyProductType,
            contractType = contractType,
            companyBusinessUnit = companyBusinessUnit
        )

    @Test
    fun `#handleCognitoCompanyUpsertRequested should create and return company and files`() = runBlocking {
        coEvery {
            companyProductConfigurationService.findByCompanyProductTypeAndConfigurationVersion(
                CompanyProductType.MICRO_2_5_OPTIONAL,
                ConfigurationVersion.STABLE
            )
        } returns companyProductConfiguration
        coEvery {
            companyService.findByCnpj(cognitoCompanyRequestWithFiles.cnpj.normalizeCnpjWithoutMask())
        } returns NotFoundException("not_found")

        val billingAccountablePartyId = RangeUUID.generate()

        val expectedCompanyModel = buildCompanyModel(
            cognitoCompanyRequest = eventWithFiles.payload.cognitoCompanyRequest,
            alreadyExistentCompany = null,
            companyAvailableProductIds = availableProductIds,
            companyProductType = companyProductConfiguration.companyProductType,
            contractType = CompanyContractType.MANDATORY,
            companyBusinessUnit = null
        ).copy(billingAccountablePartyId = billingAccountablePartyId)

        coEvery {
            companyService.createOrGetBillingAccountablePartyForCompany(
                match {
                    it.address == expectedCompanyModel.address
                            && it.name == expectedCompanyModel.name
                            && it.legalName == expectedCompanyModel.legalName
                            && it.cnpj == expectedCompanyModel.cnpj
                },
                cognitoCompanyRequest.billingAccountablePartyEmail
            )
        } returns billingAccountablePartyId


        coEvery {
            companyService.add(
                company = match {
                    it.cnpj == expectedCompanyModel.cnpj
                            && it.address == companyAddress
                },
                shouldIgnoreCheck = false,
                billingAccountablePartyEmail = eventWithFiles.payload.cognitoCompanyRequest.billingAccountablePartyEmail,
                companySalesDetails = companySalesDetails
            )
        } returns company

        coEvery { companyContractService.add(any(), any()) } returns companyContract
        coEvery { companySubContractService.add(any(), any()) } returns companySubContract

        val result = consumer.handleCognitoCompanyUpsertRequested(eventWithFiles)
        assertThat(result.get()).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
            .isEqualTo(company)

        coVerifyOnce {
            companyService.add(
                any(),
                any(),
                eventWithFiles.payload.cognitoCompanyRequest.billingAccountablePartyEmail,
                companySalesDetails = companySalesDetails
            )
        }

        coVerify(exactly = 6) {
            kafkaProducerService.produce(match { it is CompanyActivationFilesRequestedEvent })
        }
        coVerifyOnce {
            companyContractService.add(
                match { it.accountableEmail == company.email },
                sendEvent = false
            )
        }
        coVerifyOnce { companySubContractService.add(match { it.companyId == company.id }, sendEvent = false) }

    }

    @Test
    fun `#handleCognitoCompanyUpsertRequested should create and return company and member files`() =
        runBlocking {
            coEvery {
                companyProductConfigurationService.findByCompanyProductTypeAndConfigurationVersion(
                    CompanyProductType.MICRO_2_5_OPTIONAL,
                    ConfigurationVersion.STABLE
                )
            } returns companyProductConfiguration
            coEvery {
                companyService.findByCnpj(cognitoCompanyRequestWithFiles.cnpj.normalizeCnpjWithoutMask())
            } returns NotFoundException("not_found")

            val billingAccountablePartyId = RangeUUID.generate()

            val expectedCompanyModel = buildCompanyModel(
                cognitoCompanyRequest = eventWithFiles.payload.cognitoCompanyRequest,
                alreadyExistentCompany = null,
                companyAvailableProductIds = availableProductIds,
                companyProductType = companyProductConfiguration.companyProductType,
                contractType = CompanyContractType.MANDATORY,
                companyBusinessUnit = null
            ).copy(billingAccountablePartyId = billingAccountablePartyId)

            coEvery {
                companyService.createOrGetBillingAccountablePartyForCompany(
                    match {
                        it.address == expectedCompanyModel.address
                                && it.name == expectedCompanyModel.name
                                && it.legalName == expectedCompanyModel.legalName
                                && it.cnpj == expectedCompanyModel.cnpj
                    },
                    cognitoCompanyRequest.billingAccountablePartyEmail
                )
            } returns billingAccountablePartyId


            coEvery {
                companyService.add(
                    company = match {
                        it.cnpj == expectedCompanyModel.cnpj
                                && it.address == companyAddress
                    },
                    shouldIgnoreCheck = false,
                    billingAccountablePartyEmail = eventWithFiles.payload.cognitoCompanyRequest.billingAccountablePartyEmail,
                    companySalesDetails = companySalesDetails
                )
            } returns company

            coEvery { companyContractService.add(any(), any()) } returns companyContract
            coEvery { companySubContractService.add(any(), any()) } returns companySubContract

            val result = consumer.handleCognitoCompanyUpsertRequested(eventWithFiles)
            assertThat(result.get()).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt")
                .isEqualTo(company)

            coVerifyOnce {
                companyService.add(
                    any(),
                    any(),
                    eventWithFiles.payload.cognitoCompanyRequest.billingAccountablePartyEmail,
                    companySalesDetails = companySalesDetails
                )
            }

            coVerify(exactly = 6) {
                kafkaProducerService.produce(match { it is CompanyActivationFilesRequestedEvent })
            }
            coVerifyOnce {
                companyContractService.add(
                    match { it.accountableEmail == company.email },
                    sendEvent = false
                )
            }
            coVerifyOnce { companySubContractService.add(match { it.companyId == company.id }, sendEvent = false) }
        }
    

    @Test
    fun `#handleCognitoCompanyUpsertRequested should trigger documents and drop flow when request doesn't have company info`() =
        runBlocking {
            val cognitoCompanyRequest = CognitoCompanyRequest(
                id = "2",
                name = "Acme",
                legalName = "Acme Company",
                cnpj = "08.337.157/0001-19",
                proofOfPayment = listOf(CognitoFile("proofOfPayment", "", "")),
                proofOfPlan = listOf(CognitoFile("proofOfPlan", "", "")),
                members = listOf(
                    CognitoCompanyMemberRequest(
                        fullName = "MARIA DA SILVA",
                        nationalId = "",
                        biologicalSex = "",
                        dateOfBirth = "",
                        proofOfPayment = listOf(CognitoFile("proofOfPaymentMember", "", "")),
                        proofOfPlan = listOf(CognitoFile("proofOfPlanMember", "", "")),
                    ),
                )
            )

            val cognitoCompanyUpsertRequestedEvent = CognitoCompanyUpsertRequestedEvent(cognitoCompanyRequest)

            coEvery { companyService.findByCnpj(cognitoCompanyRequest.cnpj.normalizeCnpjWithoutMask()) } returns company
            coEvery {
                kafkaProducerService.produce(
                    CompanyActivationFilesRequestedEvent(
                        cognitoCompanyRequest.proofOfPlan.toCompanyActivationFilesInput(
                            fileType = PROOF_OF_PLAN,
                            companyId = company.id
                        ).first()
                    )
                )
            } returns producerResult
            coEvery {
                kafkaProducerService.produce(
                    CompanyActivationFilesRequestedEvent(
                        cognitoCompanyRequest.proofOfPayment.toCompanyActivationFilesInput(
                            fileType = PROOF_OF_PAYMENT,
                            companyId = company.id
                        ).first()
                    )
                )
            } returns producerResult
            coEvery {
                kafkaProducerService.produce(
                    CompanyActivationFilesRequestedEvent(
                        cognitoCompanyRequest.members!!.first().proofOfPlan.toCompanyActivationFilesInput(
                            fileType = PROOF_OF_PLAN,
                            companyId = company.id,
                            memberName = cognitoCompanyRequest.members!!.first().fullName
                        ).first()
                    )
                )
            } returns producerResult
            coEvery {
                kafkaProducerService.produce(
                    CompanyActivationFilesRequestedEvent(
                        cognitoCompanyRequest.members!!.first().proofOfPayment.toCompanyActivationFilesInput(
                            fileType = PROOF_OF_PAYMENT,
                            companyId = company.id,
                            memberName = cognitoCompanyRequest.members!!.first().fullName
                        ).first()
                    )
                )
            } returns producerResult

            val result = consumer.handleCognitoCompanyUpsertRequested(cognitoCompanyUpsertRequestedEvent)
            assertThat(result).isSuccessWithData(listOf(producerResult, producerResult, producerResult, producerResult))

            coVerifyOnce { companyService.findByCnpj(any()) }
            coVerify(exactly = 4) { kafkaProducerService.produce(any()) }
            confirmVerified(
                companyService,
                companyProductConfigurationService,
                kafkaProducerService,
                businessSalesCrmPipeline,
                companyContractService,
                companySubContractService,
                companyProductPriceListingService,
                productService
            )
        }

    @Test
    fun `#upsertCompanyDeal should update deal and return deal response`() = runBlocking {
        val companyDealToUpdate = BusinessCompanyDeal(
            companyId = company.id.toString(),
            cnpj = company.cnpj,
        )
        val hubspotCompanyDeal = HubspotCompanyDeal(
            pipeline = pipelineIdBrokerInternal,
            dealstage = "*********",
            b2b_company_id_aos = companyDealToUpdate.companyId,
            empresa_cnpj = companyDealToUpdate.cnpj,
            dealname = companyDealToUpdate.dealName,
            deal_email = companyDealToUpdate.dealEmail,
            deal_phone = companyDealToUpdate.dealPhone,
            zip_code = companyDealToUpdate.addressPostalCode,
            n3p_corretora = companyDealToUpdate.broker,
            n3p__cpf_do_corretor = companyDealToUpdate.salesAgentDocument
        )
        val deals = listOf(
            DealResponse(
                id = "id",
                properties = hubspotCompanyDeal
            )
        )
        val dealResult = DealResult(id = "deal_response_id")
        val expectedResponse = listOf(dealResult)

        coEvery {
            businessSalesCrmPipeline.searchDealByCnpj(company.cnpj)
        } returns HubspotDealSearchResponse(total = 1, results = deals)
        coEvery {
            businessSalesCrmPipeline.updateCompanyDeal(
                dealId = "id",
                companyDealToUpdate
            )
        } returns dealResult
        coEvery {
            kafkaProducerService.produce(match { it is CompanyWasUpdatedOnHubspotAfterCognitoIsPopulatedEvent })
        } returns mockk()

        val result = consumer.upsertCompanyDeal(companyUpsertedEvent)
        assertThat(result).isSuccessWithData(expectedResponse)

        coVerifyOnce {
            kafkaProducerService.produce(any())
        }
    }

    @Test
    fun `#upsertCompanyDeal should not update deal since stage is not one of the stages that should update`() =
        runBlocking {
            val companyDealToUpdate = BusinessCompanyDeal(
                companyId = company.id.toString(),
                pipeline = pipelineIdBrokerInternal,
                dealStage = backgroundCheckStageId,
                cnpj = company.cnpj,
            )
            val hubspotCompanyDeal = HubspotCompanyDeal(
                pipeline = pipelineIdBrokerInternal,
                dealstage = DealStage.LOST_LEAD.name,
                b2b_company_id_aos = companyDealToUpdate.companyId,
                empresa_cnpj = companyDealToUpdate.cnpj,
                dealname = companyDealToUpdate.dealName,
                deal_email = companyDealToUpdate.dealEmail,
                deal_phone = companyDealToUpdate.dealPhone,
                zip_code = companyDealToUpdate.addressPostalCode,
                n3p_corretora = companyDealToUpdate.broker,
                n3p__cpf_do_corretor = companyDealToUpdate.salesAgentDocument
            )
            val deals = listOf(
                DealResponse(
                    id = "id",
                    properties = hubspotCompanyDeal
                )
            )
            val dealResult = DealResult(id = "deal_response_id")

            coEvery {
                businessSalesCrmPipeline.searchDealByCnpj(company.cnpj)
            } returns HubspotDealSearchResponse(total = 1, results = deals)
            coEvery {
                businessSalesCrmPipeline.updateCompanyDeal(
                    dealId = "id",
                    companyDealToUpdate
                )
            } returns dealResult

            val result = consumer.upsertCompanyDeal(companyUpsertedEvent)
            assertThat(result).isSuccessWithData(emptyList<Any>())

            coVerifyNone {
                kafkaProducerService.produce(any())
            }
        }

}
