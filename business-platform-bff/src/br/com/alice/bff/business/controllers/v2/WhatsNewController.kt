package br.com.alice.bff.business.controllers.v2

import br.com.alice.authentication.currentEmail
import br.com.alice.bff.business.services.CompanyTraits
import br.com.alice.bff.business.services.ModuleService
import br.com.alice.bff.business.services.Modules
import br.com.alice.bff.business.services.ProductCachedService
import br.com.alice.business.client.BeneficiaryCompiledViewFilters
import br.com.alice.business.client.BeneficiaryCompiledViewService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanyStaffService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.foldResponse
import br.com.alice.data.layer.models.BeneficiaryViewInsuranceStatus
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyStaff
import br.com.alice.data.layer.models.CompanyStaffAccessLevel
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Product
import br.com.alice.featureconfig.core.FeatureService
import com.github.kittinunf.result.getOrElse
import java.time.LocalDate

/*
    * This controller is responsible for returning the announcements that should be displayed in the "What's New" section.
    * THIS WHOLE CLASS WILL BE DELETED
 */
class WhatsNewController(
    private val companyStaffService: CompanyStaffService,
    private val companyService: CompanyService,
    private val moduleService: ModuleService,
    private val beneficiaryViewService: BeneficiaryCompiledViewService,
    private val subContractService: CompanySubContractService,
    private val productCachedService: ProductCachedService,
): Controller() {
    companion object {
        private val FINANCE_ANNOUNCEMENTS = listOf(
            WhatsNewAnnouncements.PRODUCT_ULTRA,
            WhatsNewAnnouncements.TOP_OF_MIND
        )
    }

    suspend fun getWhatsNewAnnouncements(): Response =
        coResultOf<List<WhatsNewAnnouncements>, Throwable> {
            val staff = companyStaffService.getLatestByEmail(currentEmail()).get()
            val company = companyService.get(staff.companyId).get()

            val newsList = listOfNotNull(
                WhatsNewAnnouncements.PRODUCT_ULTRA.takeIf { shouldDisplayUltra(company) },
                WhatsNewAnnouncements.FEATURE_MAGENTA_TAB.takeIf { isMagentaTabEnabled(company) },
                WhatsNewAnnouncements.FEATURE_UTILIZATION_JOURNEY_TAB.takeIf { isUtilizationJourneyTabEnabled(company) },
                WhatsNewAnnouncements.SETTINGS_FEATURE.takeIf { shouldDisplaySettingsNews(company) },
                WhatsNewAnnouncements.TOP_OF_MIND.takeIf { LocalDate.now().isBefore(LocalDate.of(2025, 10, 1)) }
            ).let { restrictAccordingToAccessLevel(staff, it) }

            return@coResultOf newsList
        }.foldResponse()

    private fun restrictAccordingToAccessLevel(staff: CompanyStaff, announcements: List<WhatsNewAnnouncements>): List<WhatsNewAnnouncements> =
        when(staff.accessLevel) {
            CompanyStaffAccessLevel.FINANCE -> announcements.filter { FINANCE_ANNOUNCEMENTS.contains(it) }
            else -> announcements
        }

    private suspend fun shouldDisplayUltra(company: Company): Boolean
        = isProductUltraEnabled() && hasEnoughMembers(company) && isNewPortfolio(company)

    private fun isNotFinance(staff: CompanyStaff) =
        staff.accessLevel != CompanyStaffAccessLevel.FINANCE

    private suspend fun hasEnoughMembers(company: Company): Boolean =
        beneficiaryViewService.countByFilters(
            BeneficiaryCompiledViewFilters(
                companyId = company.id,
                insuranceStatus = listOf(
                    BeneficiaryViewInsuranceStatus.ACTIVE,
                    BeneficiaryViewInsuranceStatus.ACTIVE_WITH_ISSUES,
                    BeneficiaryViewInsuranceStatus.ACTIVATING,
                    BeneficiaryViewInsuranceStatus.PENDING_ACTIVATION,
                    BeneficiaryViewInsuranceStatus.CANCELING
                ),
                range = IntRange(0, 0)
            )
        ).getOrElse { 0 } >= 35

    private suspend fun isNewPortfolio(company: Company): Boolean {
        val subContract = subContractService.findByCompanyId(company.id)
            .getOrElse { emptyList() }
            .firstOrNull()

        if (subContract == null) return false

        val products = productCachedService.listProductsByCompanyAndSubcontract(company.id, subContract.id)

        return products.all {
            isProductInNewPortfolio(it)
        }
    }

    private fun isProductUltraEnabled() =
        FeatureService.get(FeatureNamespace.BUSINESS, "SHOW_ULTRA_MODAL", false)


    private suspend fun isMagentaTabEnabled(company: Company) =
        moduleService.getActiveModules(
            CompanyTraits(
                userMail = currentEmail(),
                companySize = company.companySize,
                companyCnpj = company.cnpj,
                companyId = company.id
            )
        ).contains(Modules.DATA_MODULE_MAGENTA_TAB)

    private suspend fun isUtilizationJourneyTabEnabled(company: Company): Boolean =
        moduleService.getActiveModules(
            CompanyTraits(
                userMail = currentEmail(),
                companySize = company.companySize,
                companyCnpj = company.cnpj,
                companyId = company.id
            )
        ).contains(Modules.DATA_MODULE_JOURNEY_TAB)

    private fun isProductInNewPortfolio(product: Product): Boolean {
        val newPortfolioNames = listOf("Exclusivo", "Conforto", "Equilíbrio", "Ultra", "Alice 144")
        val productNames = listOfNotNull(
            product.title,
            product.displayName,
            product.complementName
        )
        return newPortfolioNames.any { name ->
            productNames.any { it.contains(name, ignoreCase = true) }
        }
    }

    private fun hasSettingsModuleEnabled() =
        FeatureService.get(FeatureNamespace.BUSINESS, "is_settings_feature_enabled", false)

    private suspend fun hasSettingsModuleEnabled(company: Company): Boolean =
        moduleService.getActiveModules(
            CompanyTraits(
                userMail = currentEmail(),
                companySize = company.companySize,
                companyCnpj = company.cnpj,
                companyId = company.id
            )
        ).contains(Modules.SETTINGS_MODULE)

    private suspend fun shouldDisplaySettingsNews(company: Company): Boolean
            = hasSettingsModuleEnabled() && hasSettingsModuleEnabled(company)

}

enum class WhatsNewAnnouncements {
    PRODUCT_ULTRA,
    FEATURE_MAGENTA_TAB,
    FEATURE_UTILIZATION_JOURNEY_TAB,
    SETTINGS_FEATURE,
    TOP_OF_MIND
}
