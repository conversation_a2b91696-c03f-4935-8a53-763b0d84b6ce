package br.com.alice.business.consumers

import br.com.alice.business.client.CompanyService
import br.com.alice.business.converters.B2bBatchInvoiceReportConverter
import br.com.alice.business.converters.B2bBatchInvoiceReportConverter.toB2bBatchInvoiceReport
import br.com.alice.business.services.internal.B2bBatchInvoiceReportService
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.helpers.verifyNone
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.moneyin.event.MemberInvoiceGeneratedEvent
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class MemberInvoiceGeneratedConsumerTest : ConsumerTest() {
    private val companyService: CompanyService = mockk()
    private val b2bBatchInvoiceReportService: B2bBatchInvoiceReportService = mockk()
    private val consumer = MemberInvoiceGeneratedConsumer(
        companyService,
        b2bBatchInvoiceReportService,
    )

    @BeforeTest
    fun setup() {
        super.before()
        mockkObject(B2bBatchInvoiceReportConverter)
    }

    @AfterTest
    override fun clear() {
        super.clear()
        unmockkAll()
    }

    @Test
    fun `#storeReport - should save report for company's memberInvoice`() = runBlocking {
        val memberInvoice = TestModelFactory.buildMemberInvoice()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val event = MemberInvoiceGeneratedEvent(memberInvoice, billingAccountableParty)
        val company = TestModelFactory.buildCompany()
        val b2bBatchInvoiceReport = TestModelFactory.buildB2bBatchInvoiceReport(
            memberId = memberInvoice.memberId,
            billingAccountablePartyId = billingAccountableParty.id,
            totalAmount = memberInvoice.totalAmount
        )

        coEvery { companyService.findByBillingAccountablePartyId(any()) } returns company
        every { memberInvoice.toB2bBatchInvoiceReport(any(), any()) } returns b2bBatchInvoiceReport
        coEvery { b2bBatchInvoiceReportService.add(any()) } returns b2bBatchInvoiceReport

        val result = consumer.storeReport(event)
        assertThat(result).isSuccess()

        coVerifyOnce { b2bBatchInvoiceReportService.add(b2bBatchInvoiceReport) }
    }

    @Test
    fun `#storeReport - should ignore event when billingAccountableParty is not a company`() = runBlocking {
        val memberInvoice = TestModelFactory.buildMemberInvoice()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val event = MemberInvoiceGeneratedEvent(memberInvoice, billingAccountableParty)

        coEvery { companyService.findByBillingAccountablePartyId(any()) } returns NotFoundException()

        val result = consumer.storeReport(event)
        assertThat(result).isSuccess()

        verifyNone { memberInvoice.toB2bBatchInvoiceReport(any(), any()) }
        coVerifyNone { b2bBatchInvoiceReportService.add(any()) }
    }

    @Test
    fun `#storeReport - should ignore when report is already stored`() = runBlocking {
        val memberInvoice = TestModelFactory.buildMemberInvoice()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val event = MemberInvoiceGeneratedEvent(memberInvoice, billingAccountableParty)
        val company = TestModelFactory.buildCompany()
        val b2bBatchInvoiceReport = TestModelFactory.buildB2bBatchInvoiceReport(
            memberId = memberInvoice.memberId,
            billingAccountablePartyId = billingAccountableParty.id,
            totalAmount = memberInvoice.totalAmount
        )

        coEvery { companyService.findByBillingAccountablePartyId(any()) } returns company
        every { memberInvoice.toB2bBatchInvoiceReport(any(), any()) } returns b2bBatchInvoiceReport
        coEvery { b2bBatchInvoiceReportService.add(any()) } returns DuplicatedItemException("")

        val result = consumer.storeReport(event)
        assertThat(result).isSuccess()

        coVerifyOnce { b2bBatchInvoiceReportService.add(b2bBatchInvoiceReport) }
    }
}
