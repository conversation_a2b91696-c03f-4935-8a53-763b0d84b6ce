package br.com.alice.moneyin.services.internal

import br.com.alice.common.PaymentMethod
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.InvoicePaymentSource
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.moneyin.client.AcquirerPaymentService
import br.com.alice.moneyin.models.AcquirerCreatePaymentResponse
import br.com.alice.moneyin.models.AcquirerGetPaymentResponse
import br.com.alice.moneyin.models.PaymentRequestInput
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import java.util.UUID

class AcquirerOrchestratorService(
    private val iuguPaymentService: IuguPaymentService,
    private val itauPaymentService: ItauPaymentServiceProxy,
) {

    fun getDefaultPaymentSource(billingAccountablePartyId: UUID): InvoicePaymentSource {
        val shouldUseItau = FeatureService.inList(
            FeatureNamespace.MONEY_IN,
            "billing-accountable-to-use-itau",
            billingAccountablePartyId.toString()
        )

        if (shouldUseItau) {
            return InvoicePaymentSource.ITAU
        }

        val defaultPaymentSource = FeatureService.get(
            FeatureNamespace.MONEY_IN,
            "default-payment-source",
            InvoicePaymentSource.IUGU.name
        ).let { InvoicePaymentSource.valueOf(it) }


        return defaultPaymentSource
    }


    private fun getAcquirerForSource(source: InvoicePaymentSource?): AcquirerPaymentService {
        return when (source) {
            InvoicePaymentSource.IUGU -> iuguPaymentService
            InvoicePaymentSource.ADYEN -> throw IllegalArgumentException("Not supported anymore")
            InvoicePaymentSource.ITAU -> itauPaymentService
            null -> throw IllegalArgumentException("Source not found")
        }
    }

    suspend fun create(requestInput: PaymentRequestInput): Result<AcquirerCreatePaymentResponse, Throwable> {
        if (requestInput.method == PaymentMethod.SIMPLE_CREDIT_CARD) {
            return IllegalArgumentException("Simple credit card not supported").failure()
        }
        val source = getDefaultPaymentSource(requestInput.payer.id)
        val acquirerService = getAcquirerForSource(source)
        return acquirerService.create(requestInput)
    }

    suspend fun get(invoicePayment: InvoicePayment): Result<AcquirerGetPaymentResponse, Throwable> {
        if (invoicePayment.externalId == null) return IllegalArgumentException("ExternalId not found").failure()
        val acquirerService = getAcquirerForSource(invoicePayment.source)
        return acquirerService.get(invoicePayment.externalId!!)
    }

    suspend fun cancel(invoicePayment: InvoicePayment): Result<Boolean, Throwable> {
        if (invoicePayment.externalId == null) return IllegalArgumentException("ExternalId not found").failure()
        val acquirerService = getAcquirerForSource(invoicePayment.source)
        return acquirerService.cancel(invoicePayment.externalId!!)
    }

}
