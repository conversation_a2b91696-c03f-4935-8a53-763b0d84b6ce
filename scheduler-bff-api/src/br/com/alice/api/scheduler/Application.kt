package br.com.alice.api.scheduler

import br.com.alice.api.scheduler.controllers.AppointmentScheduleController
import br.com.alice.api.scheduler.controllers.AuthController
import br.com.alice.api.scheduler.controllers.BackfillController
import br.com.alice.api.scheduler.controllers.DebugController
import br.com.alice.api.scheduler.controllers.ExternalCalendarEventController
import br.com.alice.api.scheduler.controllers.GoogleCalendarController
import br.com.alice.api.scheduler.controllers.MainController
import br.com.alice.api.scheduler.controllers.MemberController
import br.com.alice.api.scheduler.controllers.ProviderController
import br.com.alice.api.scheduler.controllers.v1.AppointmentScheduleEventTypeController
import br.com.alice.api.scheduler.controllers.v1.SchedulePreferenceController
import br.com.alice.api.scheduler.controllers.v1.StaffScheduleController
import br.com.alice.api.scheduler.routes.apiRoutes
import br.com.alice.api.scheduler.routes.webhookRoutes
import br.com.alice.api.scheduler.services.AuthService
import br.com.alice.api.scheduler.webhooks.CalendlyWebhookReceiver
import br.com.alice.api.scheduler.webhooks.GoogleCalendarWebhookReceiver
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupBffApi
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.data.layer.SCHEDULER_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.healthplan.ioc.HealthPlanDomainClientModule
import br.com.alice.membership.ioc.MembershipClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.provider.ioc.ProviderDomainClientModule
import br.com.alice.schedule.ioc.AppointmentScheduleDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import com.typesafe.config.ConfigFactory
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.server.application.Application
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.plugins.cors.routing.CORS
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module
import java.time.Duration

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    fun dependencyInjectionModules() = listOf(
        AppointmentScheduleDomainClientModule,
        ClinicalAccountDomainClientModule,
        StaffDomainClientModule,
        KafkaProducerModule,
        FeatureConfigDomainClientModule,
        MembershipClientModule,
        ProviderDomainClientModule,
        PersonDomainClientModule,
        HealthPlanDomainClientModule,

        module(createdAtStart = true) {
            single { config }

            // Services
            single { AuthService(get()) }

            // Controllers
            single { AppointmentScheduleController(get(), get(), get(), get()) }
            single { AppointmentScheduleEventTypeController(get(), get(), get(), get(), get(), get(), get()) }
            single { AuthController(get(), get(), get(), get()) }
            single { BackfillController(get(), get(), get()) }
            single { ExternalCalendarEventController(get()) }
            single { GoogleCalendarController(get(), get(), get(), get(), get(), get()) }
            single { HealthController(SCHEDULER_ROOT_SERVICE_NAME) }
            single { MainController(get(), get(), get()) }
            single { MemberController(get(), get()) }
            single { SchedulePreferenceController(get(), get(), get()) }
            single { StaffScheduleController(get(), get(), get(), get()) }
            single { DebugController(get(), get(), get(), get(), get(), get(), get()) }
            single { ProviderController(get()) }

            // Webhooks
            single { CalendlyWebhookReceiver(get()) }
            single { GoogleCalendarWebhookReceiver(get()) }
        }
    )
}

@JvmOverloads
fun Application.module(
    dependencyInjectionModules: List<Module> =
        ApplicationModule.dependencyInjectionModules()
) {
    setupBffApi(dependencyInjectionModules) {
        install(CORS) {
            allowMethod(HttpMethod.Get)
            allowMethod(HttpMethod.Post)
            allowMethod(HttpMethod.Put)
            allowMethod(HttpMethod.Delete)
            allowMethod(HttpMethod.Options)
            allowHeader(HttpHeaders.Range)
            allowHeader(HttpHeaders.Authorization)
            allowHeader(HttpHeaders.ContentType)
            allowHeader(HttpHeaders.ContentRange)
            allowHeadersPrefixed("X-Datadog-")
            allowHeader("traceparent")
            exposeHeader(HttpHeaders.ContentRange)
            // TODO: be more restrict on hosts
            anyHost()
            maxAgeInSeconds = Duration.ofDays(7).seconds
        }

        routing {
            application.attributes.put(PolicyRootServiceKey, SCHEDULER_ROOT_SERVICE_NAME)

            apiRoutes()
            webhookRoutes()
        }

        featureConfigBootstrap(FeatureNamespace.SCHEDULE)
    }
}
