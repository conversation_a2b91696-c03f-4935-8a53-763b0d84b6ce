package br.com.alice.ehr.consumers

import br.com.alice.appointment.event.AppointmentCompletedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.Appointment.ReferenceLinkModel.HEALTH_PLAN_TASK
import br.com.alice.data.layer.models.Appointment.ReferenceLinkModel.SCHEDULE
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.AppointmentType
import br.com.alice.data.layer.models.HealthPlanTaskStatus.ACTIVE
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DONE
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.HealthPlanTaskType.EMERGENCY
import br.com.alice.data.layer.models.HealthPlanTaskType.PRESCRIPTION
import br.com.alice.data.layer.models.HealthPlanTaskType.REFERRAL
import br.com.alice.data.layer.models.HealthPlanTaskType.TEST_REQUEST
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel
import br.com.alice.data.layer.models.PersonHealthEventStatus
import br.com.alice.data.layer.models.TaskSourceType.SYSTEM
import br.com.alice.data.layer.models.TaskUpdatedBy
import br.com.alice.data.layer.models.copy
import br.com.alice.ehr.client.CounterReferralService
import br.com.alice.ehr.event.EmergencyExecutedEvent
import br.com.alice.ehr.event.TestRequestExecutedEvent
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.model.events.AppointmentScheduleCancelledEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import br.com.alice.secondary.attention.events.CounterReferralCreatedEvent
import br.com.alice.wanda.event.PersonHealthEventUpdatedEvent
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import java.time.LocalDateTime
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class AutoCompleteTaskConsumerTest : ConsumerTest() {

    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val appointmentScheduleService: AppointmentScheduleService = mockk()
    private val counterReferralService: CounterReferralService = mockk()
    private val autoCompleteTaskConsumer = AutoCompleteTaskConsumer(
        healthPlanTaskService,
        appointmentScheduleService,
        counterReferralService
    )

    private val task =
        TestModelFactory.buildHealthPlanTask().copy(status = ACTIVE, finishedBy = null, finishedAt = null)

    private val counterReferral =
        TestModelFactory.buildCounterReferral().copy(referralId = task.id, personId = task.personId)

    private val referralEvent = CounterReferralCreatedEvent(counterReferral)

    private val appointment =
        TestModelFactory.buildAppointment(personId = task.personId)
            .copy(
                referencedLinks = listOf(
                    Appointment.ReferencedLink(
                        id = task.id,
                        model = HEALTH_PLAN_TASK
                    )
                )
            )

    private val appointmentCompletedEvent = AppointmentCompletedEvent(appointment)

    private val schedule = TestModelFactory.buildAppointmentSchedule(personId = task.personId)
    private val personHealthEvent = TestModelFactory.buildPersonHealthEvent(personId = task.personId)

    @Test
    fun `#handleScheduleCreatedEvent should update task and return the updated task`() = runBlocking {
        val taskToUpdate = task
        val modelUpdated = task.copy(
            status = DONE,
            finishedBy = TaskUpdatedBy(source = SYSTEM),
            finishedAt = LocalDateTime.now()
        )

        val scheduleEvent = AppointmentScheduleCreatedEvent(
            person = TestModelFactory.buildPerson(personId = task.personId),
            appointmentSchedule = TestModelFactory.buildAppointmentSchedule(
                taskId = task.id,
                staffId = null
            )
        )

        coEvery {
            healthPlanTaskService.get(scheduleEvent.payload.appointmentSchedule.healthPlanTaskId!!)
        } returns task.success()

        coEvery { healthPlanTaskService.completeTask(taskToUpdate) } returns modelUpdated.success()

        val result = autoCompleteTaskConsumer.handleScheduleCreatedEvent(scheduleEvent)

        assertThat(result).isSuccessWithData(modelUpdated)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyOnce { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handleTestRequest should update task and return the updated task`() = runBlocking {
        val taskId = RangeUUID.generate()

        val event = TestRequestExecutedEvent(
            taskId, task.personId
        )
        val taskToUpdate = task.copy(id = taskId, type = TEST_REQUEST)
        val modelUpdated = task.copy(
            status = DONE,
            finishedBy = TaskUpdatedBy(source = SYSTEM),
            finishedAt = LocalDateTime.now()
        )

        coEvery {
            healthPlanTaskService.get(taskId)
        } returns taskToUpdate.success()

        coEvery { healthPlanTaskService.completeTask(taskToUpdate) } returns modelUpdated.success()

        val result = autoCompleteTaskConsumer.handleTestRequest(event)
        assertThat(result).isSuccessWithData(modelUpdated)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyOnce { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handlePs should update task and return the updated task`() = runBlocking {
        val taskId = RangeUUID.generate()

        val event = EmergencyExecutedEvent(
            taskId, task.personId
        )
        val taskToUpdate = task.copy(id = taskId, type = EMERGENCY)
        val modelUpdated = task.copy(
            status = DONE,
            finishedBy = TaskUpdatedBy(source = SYSTEM),
            finishedAt = LocalDateTime.now()
        )

        coEvery {
            healthPlanTaskService.get(taskId)
        } returns taskToUpdate.success()

        coEvery { healthPlanTaskService.completeTask(taskToUpdate) } returns modelUpdated.success()

        val result = autoCompleteTaskConsumer.handlePs(event)
        assertThat(result).isSuccessWithData(modelUpdated)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyOnce { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handleScheduleCreatedEvent return task if type is not equals to expected`() = runBlocking {
        val scheduleEvent = AppointmentScheduleCreatedEvent(
            person = TestModelFactory.buildPerson(personId = task.personId),
            appointmentSchedule = TestModelFactory.buildAppointmentSchedule(
                taskId = task.id,
                staffId = null
            )
        )
        val diffStatusTask = task.copy(type = TEST_REQUEST)

        coEvery {
            healthPlanTaskService.get(scheduleEvent.payload.appointmentSchedule.healthPlanTaskId!!)
        } returns diffStatusTask.success()

        val result = autoCompleteTaskConsumer.handleScheduleCreatedEvent(scheduleEvent)

        assertThat(result).isSuccessWithData(diffStatusTask)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyNone { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handleScheduleCreatedEvent does nothing if does not have task related`() = runBlocking {
        val scheduleEvent = AppointmentScheduleCreatedEvent(
            person = TestModelFactory.buildPerson(personId = task.personId),
            appointmentSchedule = TestModelFactory.buildAppointmentSchedule(
                taskId = null,
                staffId = null,
            )
        )

        val result = autoCompleteTaskConsumer.handleScheduleCreatedEvent(scheduleEvent)

        assertThat(result).isSuccessWithData(false)

        coVerifyNone { healthPlanTaskService.get(any()) }
        coVerifyNone { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handleScheduleCreatedEvent does nothing if already have a Staff related`() = runBlocking {
        val scheduleEvent = AppointmentScheduleCreatedEvent(
            person = TestModelFactory.buildPerson(personId = task.personId),
            appointmentSchedule = TestModelFactory.buildAppointmentSchedule(
                taskId = task.id,
                staffId = RangeUUID.generate(),
            )
        )

        coEvery { healthPlanTaskService.get(task.id) } returns task.success()

        val result = autoCompleteTaskConsumer.handleScheduleCreatedEvent(scheduleEvent)

        assertThat(result).isSuccessWithData(false)

        coVerifyNone { healthPlanTaskService.get(any()) }
        coVerifyNone { healthPlanTaskService.activateTask(any()) }
    }

    @Test
    fun `#handleScheduleCanceledEvent does nothing if does not have task related`() = runBlocking {
        val cancelledScheduleEvent = AppointmentScheduleCancelledEvent(
            AppointmentSchedule(
                eventId = "someEventId",
                eventName = "some event name",
                healthPlanTaskId = null,
                healthcareTeamId = RangeUUID.generate(),
                location = null,
                personId = task.personId,
                staffId = RangeUUID.generate(),
                startTime = LocalDateTime.now().plusDays(2),
                status = AppointmentScheduleStatus.CANCELED,
                type = AppointmentScheduleType.HEALTH_DECLARATION,
                version = 0,
                eventUuid = null
            )
        )

        val result = autoCompleteTaskConsumer.handleScheduleCanceledEvent(cancelledScheduleEvent)
        assertThat(result).isSuccessWithData(false)

        coVerifyNone { healthPlanTaskService.get(any()) }
        coVerifyNone { healthPlanTaskService.activateTask(any()) }
    }

    @Test
    fun `#handleAppointmentCompletedEvent does nothing if does not have task related`() = runBlocking {
        val event = appointmentCompletedEvent.copy(appointment.copy(referencedLinks = emptyList()))

        val result = autoCompleteTaskConsumer.handleAppointmentCompletedEvent(event)
        assertThat(result).isSuccess()

        coVerify { healthPlanTaskService wasNot called }
    }
    @Test
    fun `#handleAppointmentCompletedEvent does nothing when type is counter referral`() = runBlocking {
        val event = appointmentCompletedEvent.copy(
            appointment.copy(
                referencedLinks = listOf(
                    Appointment.ReferencedLink(
                        id = task.id,
                        model = HEALTH_PLAN_TASK
                    )
                ),
                type = AppointmentType.COUNTER_REFERRAL
            )
        )

        val result = autoCompleteTaskConsumer.handleAppointmentCompletedEvent(event)
        assertThat(result).isSuccessWithData(false)

        coVerify { healthPlanTaskService wasNot called }
    }


    @Test
    fun `#handleAppointmentCompletedEvent updates task when health plan related`() = runBlocking {
        val modelUpdated = task.copy(
            status = DONE,
            finishedBy = TaskUpdatedBy(source = SYSTEM),
            finishedAt = LocalDateTime.now()
        )

        coEvery { healthPlanTaskService.get(task.id) } returns task.success()
        coEvery { healthPlanTaskService.completeTask(task) } returns modelUpdated.success()

        val result = autoCompleteTaskConsumer.handleAppointmentCompletedEvent(appointmentCompletedEvent)
        assertThat(result).isSuccess()

        coVerifyOnce { healthPlanTaskService.get(task.id) }
        coVerifyOnce { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handleAppointmentCompletedEvent updates task when schedule related`() = runBlocking {
        val event = appointmentCompletedEvent.copy(
            appointment.copy(
                referencedLinks = listOf(
                    Appointment.ReferencedLink(
                        id = schedule.id,
                        model = SCHEDULE
                    )
                )
            )
        )
        val schedule = schedule.copy(healthPlanTaskId = task.id)
        val modelUpdated = task.copy(
            status = DONE,
            finishedBy = TaskUpdatedBy(source = SYSTEM),
            finishedAt = LocalDateTime.now()
        )

        coEvery { appointmentScheduleService.get(schedule.id) } returns schedule.success()
        coEvery { healthPlanTaskService.get(task.id) } returns task.success()
        coEvery { healthPlanTaskService.completeTask(task) } returns modelUpdated.success()

        val result = autoCompleteTaskConsumer.handleAppointmentCompletedEvent(event)
        assertThat(result).isSuccess()

        coVerifyOnce { appointmentScheduleService.get(schedule.id) }
        coVerifyOnce { healthPlanTaskService.get(task.id) }
        coVerifyOnce { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handleAppointmentCompletedEvent does nothing if schedule does not have task id`() = runBlocking {
        val event = appointmentCompletedEvent.copy(
            appointment.copy(
                referencedLinks = listOf(
                    Appointment.ReferencedLink(
                        id = schedule.id,
                        model = SCHEDULE
                    )
                )
            )
        )

        coEvery { appointmentScheduleService.get(schedule.id) } returns schedule.success()

        val result = autoCompleteTaskConsumer.handleAppointmentCompletedEvent(event)
        assertThat(result).isSuccess()

        coVerifyOnce { appointmentScheduleService.get(schedule.id) }
        coVerify { healthPlanTaskService wasNot called }
    }

    @Test
    fun `#handlePersonHealthEventUpdate - should return true when referenced model class not belong`() = runBlocking {
        val personHealthEventModelClass =
            personHealthEvent.copy(referencedModelClass = PersonHealthEventReferenceModel.REFERRAL)
        val event = PersonHealthEventUpdatedEvent(personHealthEventModelClass)

        val result = autoCompleteTaskConsumer.handlePersonHealthEventUpdate(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyNone { healthPlanTaskService.get(any()) }
        coVerifyNone { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handlePersonHealthEventUpdate - should return true when referenced model class is null`() = runBlocking {
        val personHealthEventModelClass = personHealthEvent.copy(referencedModelClass = null)
        val event = PersonHealthEventUpdatedEvent(personHealthEventModelClass)

        val result = autoCompleteTaskConsumer.handlePersonHealthEventUpdate(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyNone { healthPlanTaskService.get(any()) }
        coVerifyNone { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handlePersonHealthEventUpdate - should return true when staffId is null`() = runBlocking {
        val personHealthEventModelClass = personHealthEvent.copy(staffId = null)
        val event = PersonHealthEventUpdatedEvent(personHealthEventModelClass)

        val result = autoCompleteTaskConsumer.handlePersonHealthEventUpdate(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyNone { healthPlanTaskService.get(any()) }
        coVerifyNone { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handlePersonHealthEventUpdate - should return true when status of person not belong`() = runBlocking {
        val personHealthEventStatus = personHealthEvent.copy(
            referencedModelClass = PersonHealthEventReferenceModel.PRESCRIPTION,
            status = PersonHealthEventStatus.NOT_STARTED
        )
        val event = PersonHealthEventUpdatedEvent(personHealthEventStatus)

        val result = autoCompleteTaskConsumer.handlePersonHealthEventUpdate(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyNone { healthPlanTaskService.get(any()) }
        coVerifyNone { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handlePersonHealthEventUpdate - should return true when person health event is FINISHED`() = runBlocking {
        val personHealthEventFinished = personHealthEvent.copy(
            referencedModelClass = PersonHealthEventReferenceModel.PRESCRIPTION,
            status = PersonHealthEventStatus.FINISHED,
            referencedModelId = task.id.toString(),
            staffId = RangeUUID.generate()
        )
        val taskPrescription = task.copy(type = PRESCRIPTION)

        coEvery { healthPlanTaskService.get(taskPrescription.id) } returns taskPrescription.success()
        coEvery { healthPlanTaskService.completeTask(taskPrescription) } returns taskPrescription.success()

        val event = PersonHealthEventUpdatedEvent(personHealthEventFinished)

        val result = autoCompleteTaskConsumer.handlePersonHealthEventUpdate(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyOnce { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handlePersonHealthEventUpdate - should return true when person health event is CANCELLED`() = runBlocking {
        val personHealthEventFinished = personHealthEvent.copy(
            referencedModelClass = PersonHealthEventReferenceModel.PRESCRIPTION,
            status = PersonHealthEventStatus.FINISHED,
            referencedModelId = task.id.toString(),
            staffId = RangeUUID.generate()
        )
        val taskPrescription = task.copy(type = PRESCRIPTION)

        coEvery { healthPlanTaskService.get(taskPrescription.id) } returns taskPrescription.success()
        coEvery { healthPlanTaskService.completeTask(taskPrescription) } returns taskPrescription.success()

        val event = PersonHealthEventUpdatedEvent(personHealthEventFinished)

        val result = autoCompleteTaskConsumer.handlePersonHealthEventUpdate(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyOnce { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handleAppointmentCompletedEvent updates referral task with multiple sessions when all sessions are completed`() =
        runBlocking {

            val referralWithSessions = task.copy(
                content = mapOf("sessionsQuantity" to 2)
            )

            val modelUpdated = referralWithSessions.copy(
                status = DONE,
                finishedBy = TaskUpdatedBy(source = SYSTEM),
                finishedAt = LocalDateTime.now()
            )

            coEvery { healthPlanTaskService.get(referralWithSessions.id) } returns referralWithSessions.success()
            coEvery { healthPlanTaskService.completeTask(referralWithSessions) } returns modelUpdated.success()

            val result = autoCompleteTaskConsumer.handleAppointmentCompletedEvent(appointmentCompletedEvent)
            assertThat(result).isSuccess()

            coVerifyOnce { healthPlanTaskService.get(task.id) }
            coVerifyOnce { healthPlanTaskService.completeTask(any()) }
        }

    @Test
    fun `#handleReferralEvent should update task id sessions quantity or follow up max quantity is null`() =
        runBlocking {
            val task = TestModelFactory.buildHealthPlanTaskReferral(
                sessionsQuantity = null,
                followUpMaxQuantity = null
            )

            coEvery {
                healthPlanTaskService.get(counterReferral.referralId!!)
            } returns task.success()

            coEvery {
                healthPlanTaskService.completeTask(task)
            } returns task.success()

            val result = autoCompleteTaskConsumer.handleReferralEvent(referralEvent)
            assertThat(result).isSuccessWithData(task)

            coVerifyOnce { healthPlanTaskService.get(any()) }
            coVerifyOnce { healthPlanTaskService.completeTask(any()) }
        }

    @Test
    fun `#handleReferralEvent should update task if sessions quantity is one`() = runBlocking {
        val task = TestModelFactory.buildHealthPlanTaskReferral(
            sessionsQuantity = 1
        )

        coEvery {
            healthPlanTaskService.get(counterReferral.referralId!!)
        } returns task.success()

        coEvery {
            healthPlanTaskService.completeTask(task)
        } returns task.success()

        val result = autoCompleteTaskConsumer.handleReferralEvent(referralEvent)
        assertThat(result).isSuccessWithData(task)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyOnce { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handleReferralEvent should update task if follow up max quantity is one`() = runBlocking {
        val task = TestModelFactory.buildHealthPlanTaskReferral(
            followUpMaxQuantity = 1
        )

        coEvery {
            healthPlanTaskService.get(counterReferral.referralId!!)
        } returns task.success()

        coEvery {
            healthPlanTaskService.completeTask(task)
        } returns task.success()

        val result = autoCompleteTaskConsumer.handleReferralEvent(referralEvent)
        assertThat(result).isSuccessWithData(task)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyOnce { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handleReferralEvent should update task if sessions quantity is two and second counter referral`() =
        runBlocking {
            val taskToUpdate = TestModelFactory.buildHealthPlanTaskReferral(
                sessionsQuantity = 2
            )

            coEvery {
                healthPlanTaskService.get(counterReferral.referralId!!)
            } returns taskToUpdate.success()

            coEvery {
                counterReferralService.listByReferral(taskToUpdate.id)
            } returns listOf(counterReferral, counterReferral).success()

            coEvery {
                healthPlanTaskService.completeTask(taskToUpdate)
            } returns taskToUpdate.success()

            val result = autoCompleteTaskConsumer.handleReferralEvent(referralEvent)
            assertThat(result).isSuccessWithData(taskToUpdate)

            coVerifyOnce { healthPlanTaskService.get(any()) }
            coVerifyOnce { healthPlanTaskService.completeTask(any()) }
        }

    @Test
    fun `#handleReferralEvent should update task if follow up max quantity is two and second counter referral`() =
        runBlocking {
            val taskToUpdate = TestModelFactory.buildHealthPlanTaskReferral(
                followUpMaxQuantity = 2
            )

            coEvery {
                healthPlanTaskService.get(counterReferral.referralId!!)
            } returns taskToUpdate.success()

            coEvery {
                counterReferralService.listByReferral(taskToUpdate.id)
            } returns listOf(counterReferral, counterReferral).success()

            coEvery {
                healthPlanTaskService.completeTask(taskToUpdate)
            } returns taskToUpdate.success()

            val result = autoCompleteTaskConsumer.handleReferralEvent(referralEvent)
            assertThat(result).isSuccessWithData(taskToUpdate)

            coVerifyOnce { healthPlanTaskService.get(any()) }
            coVerifyOnce { healthPlanTaskService.completeTask(any()) }
        }

    @Test
    fun `#handleReferralEvent should not update task if sessions quantity is two and first counter referral`() =
        runBlocking {
            val task = TestModelFactory.buildHealthPlanTaskReferral(
                sessionsQuantity = 2
            )

            coEvery {
                healthPlanTaskService.get(counterReferral.referralId!!)
            } returns task.success()

            coEvery {
                counterReferralService.listByReferral(task.id)
            } returns listOf(counterReferral).success()


            val result = autoCompleteTaskConsumer.handleReferralEvent(referralEvent)
            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { healthPlanTaskService.get(any()) }
            coVerifyOnce { counterReferralService.listByReferral(any()) }
            coVerifyNone { healthPlanTaskService.completeTask(any()) }
        }

    @Test
    fun `#handleReferralEvent should not update task if follow up max quantity is two and first counter referral`() =
        runBlocking {
            val task = TestModelFactory.buildHealthPlanTaskReferral(
                followUpMaxQuantity = 2
            )

            coEvery {
                healthPlanTaskService.get(counterReferral.referralId!!)
            } returns task.success()

            coEvery {
                counterReferralService.listByReferral(task.id)
            } returns listOf(counterReferral).success()


            val result = autoCompleteTaskConsumer.handleReferralEvent(referralEvent)
            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { healthPlanTaskService.get(any()) }
            coVerifyOnce { counterReferralService.listByReferral(any()) }
            coVerifyNone { healthPlanTaskService.completeTask(any()) }
        }

    @Test
    fun `#handleReferralEvent should update task if its follow up`() = runBlocking {
        val task = TestModelFactory.buildHealthPlanTask(
            type = HealthPlanTaskType.FOLLOW_UP_REQUEST,
            groupId = RangeUUID.generate(),
            status = ACTIVE,
            title = "Retorno com Joaozinho"
        )
        val counterReferral = TestModelFactory.buildCounterReferral().copy(
            referralId = task.id,
            personId = task.personId
        )

        coEvery {
            healthPlanTaskService.get(counterReferral.referralId!!)
        } returns task.success()

        coEvery {
            healthPlanTaskService.completeTask(task)
        } returns task.success()

        val result = autoCompleteTaskConsumer.handleReferralEvent(CounterReferralCreatedEvent(counterReferral))
        assertThat(result).isSuccessWithData(task)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyOnce { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handleReferralEvent should validate event when referral id is null return false`() = runBlocking {
        val event = CounterReferralCreatedEvent(counterReferral.copy(referralId = null))

        val result = autoCompleteTaskConsumer.handleReferralEvent(event)
        assertThat(result).isSuccessWithData(false)

        coVerify { healthPlanTaskService wasNot called }
    }

    @Test
    fun `#handleReferralEvent should not update task if status already DONE`() = runBlocking {
        val modelDone = TestModelFactory.buildHealthPlanTaskReferral()
            .copy(
                status = DONE,
                finishedBy = TaskUpdatedBy(source = SYSTEM)
            )

        coEvery {
            healthPlanTaskService.get(counterReferral.referralId!!)
        } returns modelDone.success()

        val result = autoCompleteTaskConsumer.handleReferralEvent(referralEvent)
        assertThat(result).isSuccessWithData(modelDone)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyNone { healthPlanTaskService.updateTask(any()) }
    }

    @Test
    fun `#handleReferralEvent should return error when throw exception on get`() = runBlocking {
        coEvery {
            healthPlanTaskService.get(counterReferral.referralId!!)
        } returns NotFoundException().failure()

        val result = autoCompleteTaskConsumer.handleReferralEvent(referralEvent)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyNone { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handleReferralEvent should return error when throw exception on update`() = runBlocking {
        val taskToUpdate = task.copy(type = REFERRAL)
        coEvery {
            healthPlanTaskService.get(counterReferral.referralId!!)
        } returns taskToUpdate.success()

        coEvery {
            healthPlanTaskService.completeTask(any())
        } returns Exception("").failure()

        val result = autoCompleteTaskConsumer.handleReferralEvent(referralEvent)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyOnce { healthPlanTaskService.completeTask(any()) }
    }

    @Test
    fun `#handleReferralEvent return false when task not found`() = runBlocking {
        coEvery {
            healthPlanTaskService.get(any())
        } returns NotFoundException().failure()

        val result = autoCompleteTaskConsumer.handleReferralEvent(referralEvent)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyNone { healthPlanTaskService.completeTask(any()) }
    }
}
