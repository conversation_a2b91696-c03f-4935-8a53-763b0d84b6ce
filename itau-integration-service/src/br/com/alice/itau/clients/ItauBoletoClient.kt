package br.com.alice.itau.clients

import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.common.serialization.gson
import br.com.alice.itau.ItauBoletoConfig
import br.com.alice.itau.ServiceConfig
import br.com.alice.itau.clients.models.ItauBoletoCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoCreateResponse
import br.com.alice.itau.clients.models.ItauBoletoGetResponse
import br.com.alice.itau.clients.models.ItauErrorException
import br.com.alice.itau.clients.models.ItauErrorResponse
import br.com.alice.itau.clients.models.ItauPixCancelRequest
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.plugins.ResponseException
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.patch
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.contentType
import io.opentelemetry.api.trace.Span
import io.opentelemetry.api.trace.StatusCode

class ItauBoletoClient(
    private val itauAuthClient: ItauAuthClient,
    private val client: HttpClient = ItauClientHelper.itauDefaultHttpClient(),
) : Spannable {
    open val config: ItauBoletoConfig = ServiceConfig.itauBoletoConfig

    suspend fun create(request: ItauBoletoCreateRequest): Result<ItauBoletoCreateResponse, Throwable> {
        val accessToken = itauAuthClient.getAccessToken().get()
        logger.info("Creating boleto with request: ${gson.toJson(request)}")
        return span("post") { span ->
            val url = config.baseUrl
            span.setAttribute("url", url)
            try {
                val response = client.post(url) {
                    contentType(ContentType.Application.Json)
                    header(HttpHeaders.Authorization, "Bearer $accessToken")
                    header("x-itau-apikey", config.apiKey)
                    setBody(request)
                }
                span.setStatus(StatusCode.OK)
                response.body<ItauBoletoCreateResponse>().success()
            } catch (e: Exception) {
                handleError("createBoleto", e, span).failure()
            }
        }
    }

    suspend fun get(ourNumber: String): Result<ItauBoletoGetResponse, Throwable> {
        val accessToken = itauAuthClient.getAccessToken().get()
        val aliceBankAccount = config.aliceAccountKey
        return span("get") { span ->
            val url =
                "${config.baseUrl}/?id_beneficiario=$aliceBankAccount&codigo_carteira=109&nosso_numero=$ourNumber&view=specific"
            span.setAttribute("url", url)
            try {
                val response = client.get(url) {
                    contentType(ContentType.Application.Json)
                    header(HttpHeaders.Authorization, "Bearer $accessToken")
                    header("x-itau-apikey", config.apiKey)
                }
                span.setStatus(StatusCode.OK)
                val itauBoletoGetResponse = response.body<ItauBoletoGetResponse>()
                if (itauBoletoGetResponse.data.isEmpty()) {
                    return@span ItauErrorException(
                        "Boleto not found",
                        HttpStatusCode.NotFound
                    ).failure()
                }
                itauBoletoGetResponse.success()
            } catch (e: Exception) {
                handleError("getBoleto", e, span).failure()
            }
        }
    }

    suspend fun cancel(ourNumber: String): Result<Boolean, Throwable> {
        val accessToken = itauAuthClient.getAccessToken().get()
        val id = config.aliceAccountKey + "109" + ourNumber
        return span("cancel") { span ->
            val url = "${config.baseUrl}/$id/baixa"
            span.setAttribute("url", url)
            try {
                client.patch(url) {
                    contentType(ContentType.Application.Json)
                    header(HttpHeaders.Authorization, "Bearer $accessToken")
                    header("x-itau-apikey", config.apiKey)
                    setBody(ItauPixCancelRequest())
                }
                span.setStatus(StatusCode.OK)
                true.success()
            } catch (e: Exception) {
                handleError("cancelBoleto", e, span).failure()
            }
        }
    }

    private suspend fun handleError(requestName: String, e: Exception, span: Span): Exception {
        val (error, statusCode) = handleException(e, requestName, span)
        return ItauErrorException(error.message, statusCode)
    }

    private suspend fun handleException(
        e: Exception,
        requestName: String,
        span: Span
    ): Pair<ItauErrorResponse, HttpStatusCode> {
        logger.error("ItauBoletoClient:: error executing $requestName", e)
        span.recordException(e)
        return when (e) {
            is ResponseException -> handleResponseException(e) //400 + 500 family
            else -> throw e
        }
    }

    private suspend fun handleResponseException(re: ResponseException): Pair<ItauErrorResponse, HttpStatusCode> {
        try {
            return gson.fromJson(re.response.bodyAsText(), ItauErrorResponse::class.java) to re.response.status
        } catch (e: Exception) {
            logger.error("ItauBoletoClient::post : error parsing Itau error response", e.message)
            throw e
        }
    }
}
