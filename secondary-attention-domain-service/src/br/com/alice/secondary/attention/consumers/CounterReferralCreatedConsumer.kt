package br.com.alice.secondary.attention.consumers

import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.NextStepSpecialistType
import br.com.alice.data.layer.models.copy
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.secondary.attention.events.CounterReferralCreatedEvent
import br.com.alice.secondary.attention.events.HealthPlanTaskReferralsHandleEvent
import br.com.alice.secondary.attention.events.TaskReferralsEventHandleType
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class CounterReferralCreatedConsumer(
    private val healthPlanTaskService: HealthPlanTaskService,
    private val kafkaProducerService: KafkaProducerService,
) : Consumer() {

    suspend fun handleHealthPlanTasks(event: CounterReferralCreatedEvent) = withSubscribersEnvironment {
        val counterReferral = event.payload.counterReferral
        if (counterReferral.nextStepSpecialistInfo?.type == NextStepSpecialistType.DISCHARGED) {
            counterReferral.referralId?.let { referralId ->
                healthPlanTaskService.get(referralId).map { task ->
                    healthPlanTaskService.updateTask(
                        task.copy(
                            status = HealthPlanTaskStatus.DONE
                        )
                    )
                }.thenError {
                    logger.info(
                        "Could not find task by referralId",
                        "referralId" to counterReferral.referralId,
                        "error" to it
                    )
                }
            }
        }
        return@withSubscribersEnvironment true.success()
    }

    suspend fun dispatchHealthPlanTaskReferrals(event: CounterReferralCreatedEvent) = withSubscribersEnvironment {
        val counterReferral = event.payload.counterReferral

        logger.info(
            "CounterReferralCreatedConsumer#dispatchHealthPlanTaskReferrals begin",
            "counter_referral_id" to counterReferral.id,
            "counter_referral_referral_id" to counterReferral.referralId,
        )
        if (counterReferral.referralId == null)
            return@withSubscribersEnvironment true.success()

        val healthPlanTask = healthPlanTaskService.get(counterReferral.referralId!!).get()

        val payload = HealthPlanTaskReferralsHandleEvent(
            TaskReferralsEventHandleType.INCREMENT_COUNTER_REFERRAL,
            healthPlanTask,
            counterReferral
        )
        kafkaProducerService.produce(payload, healthPlanTask.id.toString())

        true.success()
    }
}
