package br.com.alice.data.layer.pipelines.subjects

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import org.junit.jupiter.api.Assertions.assertEquals
import kotlin.test.Test

class HealthOpsSubjectTest {

    @Test
    fun `should create a HealthOpsSubject from StaffModel`() {
        val id = RangeUUID.generate()
        val role = Role.HEALTH_OPS
        val staffModel = DataLayerTestModelFactory.buildStaff(id = id, role = role)

        val healthOpsSubject = HealthOpsSubject(staffModel)

        assertEquals(id, healthOpsSubject.id)
        assertEquals(role, healthOpsSubject.role)
    }
}
