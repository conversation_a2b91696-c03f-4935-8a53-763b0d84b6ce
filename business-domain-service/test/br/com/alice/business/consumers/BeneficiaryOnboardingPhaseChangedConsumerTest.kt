package br.com.alice.business.consumers

import br.com.alice.business.client.BeneficiaryOnboardingService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.communication.Mailer
import br.com.alice.business.converters.model.toModel
import br.com.alice.business.events.BeneficiaryOnboardingFinishedEvent
import br.com.alice.business.events.BeneficiaryOnboardingPhaseChangedEvent
import br.com.alice.business.events.CompanyBeneficiariesVideoCallFinishedEvent
import br.com.alice.business.exceptions.BeneficiaryActivationValidationException
import br.com.alice.business.metrics.BeneficiaryMetric
import br.com.alice.business.services.internal.BeneficiaryHubspotService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.communication.crm.sales.b2b.BusinessDeal
import br.com.alice.communication.crm.sales.b2b.BusinessDealStage
import br.com.alice.communication.crm.sales.b2b.BusinessSalesCrmPipeline
import br.com.alice.communication.crm.sales.b2b.DealResult
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.data.layer.services.BeneficiaryOnboardingPhaseModelDataService
import br.com.alice.membership.client.ContractGenerator
import br.com.alice.membership.client.onboarding.HealthDeclarationForm
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class BeneficiaryOnboardingPhaseChangedConsumerTest : ConsumerTest() {
    private val beneficiaryHubspotService: BeneficiaryHubspotService = mockk()
    private val businessSalesCrmPipeline: BusinessSalesCrmPipeline = mockk()
    private val personService: PersonService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val beneficiaryOnboardingService: BeneficiaryOnboardingService = mockk()
    private val beneficiaryOnboardingPhaseDataService: BeneficiaryOnboardingPhaseModelDataService = mockk()
    private val healthDeclarationForm: HealthDeclarationForm = mockk()
    private val memberService: MemberService = mockk()
    private val mailer: Mailer = mockk()
    private val contractGenerator: ContractGenerator = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val consumer =
        BeneficiaryOnboardingPhaseChangedConsumer(
            beneficiaryHubspotService,
            businessSalesCrmPipeline,
            personService,
            beneficiaryService,
            beneficiaryOnboardingService,
            beneficiaryOnboardingPhaseDataService,
            healthDeclarationForm,
            memberService,
            mailer,
            contractGenerator,
            kafkaProducerService
        )

    @BeforeTest
    fun setup() {
        mockkObject(logger)
        mockkObject(BeneficiaryMetric)
    }

    @AfterTest
    override fun clear() {
        super.clear()
        unmockkAll()
    }

    @Test
    fun `#updateHubspotDeal should get beneficiaryHubspot and update hubspot deal from event`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val beneficiaryOnboardingId = RangeUUID.generate()
        val newPhase = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW
        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiaryId)
        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            beneficiaryOnboardingId = beneficiaryOnboardingId,
            phase = newPhase,
        )
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            id = beneficiaryOnboardingId,
            beneficiaryId = beneficiaryId,
            phases = listOf(beneficiaryOnboardingPhase),
        )
        val beneficiary = TestModelFactory.buildBeneficiary(id = beneficiaryId, canceledAt = null)
        val businessDeal = BusinessDeal(stage = BusinessDealStage.valueOf(newPhase.name))
        val event = BeneficiaryOnboardingPhaseChangedEvent(beneficiaryId, beneficiaryOnboardingPhase)
        val dealResult = DealResult("123")

        coEvery {
            beneficiaryHubspotService.getOrCreateByPersonAndCompanyId(
                any(),
                any(),
                any()
            )
        } returns beneficiaryHubspot
        coEvery { beneficiaryService.get(any()) } returns beneficiary
        coEvery { businessSalesCrmPipeline.updateDeal(any(), any()) } returns dealResult
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding

        val result = consumer.updateHubspotDeal(event)

        assertThat(result).isSuccessWithData(dealResult)

        coVerifyOnce {
            beneficiaryHubspotService.getOrCreateByPersonAndCompanyId(
                beneficiaryId,
                beneficiary.personId,
                beneficiary.companyId
            )
        }
        coVerifyOnce { beneficiaryService.get(beneficiaryId) }
        coVerifyOnce { businessSalesCrmPipeline.updateDeal(beneficiaryHubspot.externalDealId, businessDeal) }
        coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(beneficiaryId) }
    }

    @Test
    fun `#updateHubspotDeal should skipMessage when Beneficiary have canceledAt isBeforeEq today`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val newPhase = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW
        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(phase = newPhase)
        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiaryId)
        val beneficiary =
            TestModelFactory.buildBeneficiary(id = beneficiaryId, canceledAt = LocalDateTime.now().minusDays(1))
        val event = BeneficiaryOnboardingPhaseChangedEvent(beneficiaryId, beneficiaryOnboardingPhase)

        coEvery {
            beneficiaryHubspotService.getOrCreateByPersonAndCompanyId(
                any(),
                any(),
                any()
            )
        } returns beneficiaryHubspot
        coEvery { beneficiaryService.get(any()) } returns beneficiary

        val result = consumer.updateHubspotDeal(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce {
            beneficiaryHubspotService.getOrCreateByPersonAndCompanyId(
                beneficiaryId,
                beneficiary.personId,
                beneficiary.companyId
            )
        }
        coVerifyOnce { beneficiaryService.get(beneficiaryId) }
        coVerifyNone { businessSalesCrmPipeline.updateDeal(any(), any()) }
    }

    @Test
    fun `#updateHubspotDeal should update hubspot deal to specific stage according beneficiary phase type`() =
        runBlocking {
            val beneficiaryId = RangeUUID.generate()
            val beneficiaryOnboardingId = RangeUUID.generate()
            val newPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT
            val newBusinessDealStage = BusinessDealStage.HEALTH_DECLARATION_APPOINTMENT_NO_SHOW_OR_CANCELED

            val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiaryId)
            val beneficiaryOnboardingPreviousPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
                beneficiaryOnboardingId = beneficiaryOnboardingId,
                phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT_SCHEDULED,
                transactedAt = LocalDateTime.now().minusDays(1),
            )
            val beneficiaryOnboardingCurrentPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
                beneficiaryOnboardingId = beneficiaryOnboardingId,
                phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT,
                transactedAt = LocalDateTime.now(),
            )
            val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
                id = beneficiaryOnboardingId,
                beneficiaryId = beneficiaryId,
                phases = listOf(beneficiaryOnboardingPreviousPhase, beneficiaryOnboardingCurrentPhase),
            )
            val beneficiary = TestModelFactory.buildBeneficiary(id = beneficiaryId, canceledAt = null)
            val businessDeal = BusinessDeal(stage = BusinessDealStage.valueOf(newBusinessDealStage.name))
            val event = BeneficiaryOnboardingPhaseChangedEvent(beneficiaryId, beneficiaryOnboardingCurrentPhase)
            val dealResult = DealResult("123")

            coEvery {
                beneficiaryHubspotService.getOrCreateByPersonAndCompanyId(
                    any(),
                    any(),
                    any()
                )
            } returns beneficiaryHubspot
            coEvery { beneficiaryService.get(any()) } returns beneficiary
            coEvery { businessSalesCrmPipeline.updateDeal(any(), any()) } returns dealResult
            coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding

            val result = consumer.updateHubspotDeal(event)

            assertThat(result).isSuccessWithData(dealResult)

            coVerifyOnce {
                beneficiaryHubspotService.getOrCreateByPersonAndCompanyId(
                    beneficiaryId,
                    beneficiary.personId,
                    beneficiary.companyId
                )
            }
            coVerifyOnce { beneficiaryService.get(beneficiaryId) }
            coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(beneficiaryId) }
            coVerifyOnce { businessSalesCrmPipeline.updateDeal(beneficiaryHubspot.externalDealId, businessDeal) }
        }

    @Test
    fun `#handleHealthDeclarationAppointment should send email when phase is HEALTH_DECLARATION_APPOINTMENT and flowType is FULL_RISK_FLOW`() =
        runBlocking {
            val person = TestModelFactory.buildPerson()
            val member = TestModelFactory.buildMember(personId = person.id)

            val beneficiary = TestModelFactory.buildBeneficiary(
                memberId = member.id,
                onboarding = TestModelFactory.buildBeneficiaryOnboarding(flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW)
            )
            val newPhaseType = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT
            val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(phase = newPhaseType)


            val event = BeneficiaryOnboardingPhaseChangedEvent(
                beneficiaryId = beneficiary.id,
                newPhase = beneficiaryOnboardingPhase,
            )

            coEvery {
                beneficiaryService.get(
                    beneficiary.id,
                    findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
                )
            } returns beneficiary
            coEvery { memberService.get(beneficiary.memberId) } returns member
            coEvery { personService.get(member.personId) } returns person
            coEvery {
                mailer.sendHealthDeclarationAppointmentEmail(
                    person.contactName,
                    person.email,
                    beneficiary.onboarding?.flowType!!
                )
            } returns EmailReceipt("id")

            val result = consumer.handleHealthDeclarationAppointment(event)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce {
                beneficiaryService.get(
                    beneficiary.id,
                    findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
                )
            }
            coVerifyOnce { personService.get(member.personId) }
            coVerifyOnce {
                mailer.sendHealthDeclarationAppointmentEmail(
                    person.contactName,
                    person.email,
                    beneficiary.onboarding?.flowType!!
                )
            }
        }

    @Test
    fun `#handleHealthDeclarationAppointment should send email when phase is HEALTH_DECLARATION_APPOINTMENT and flowType is PARTIAL_RISK_FLOW`() =
        runBlocking {
            val person = TestModelFactory.buildPerson()
            val member = TestModelFactory.buildMember(personId = person.id)

            val beneficiary = TestModelFactory.buildBeneficiary(
                memberId = member.id,
                onboarding = TestModelFactory.buildBeneficiaryOnboarding(flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW)
            )
            val phaseType = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT
            val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(phase = phaseType)

            val event = BeneficiaryOnboardingPhaseChangedEvent(
                beneficiaryId = beneficiary.id,
                newPhase = beneficiaryOnboardingPhase,
            )

            coEvery {
                beneficiaryService.get(
                    beneficiary.id,
                    findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
                )
            } returns beneficiary
            coEvery { memberService.get(beneficiary.memberId) } returns member
            coEvery { personService.get(member.personId) } returns person
            coEvery {
                mailer.sendHealthDeclarationAppointmentEmail(
                    person.contactName,
                    person.email,
                    beneficiary.onboarding?.flowType!!
                )
            } returns EmailReceipt("id")

            val result = consumer.handleHealthDeclarationAppointment(event)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce {
                beneficiaryService.get(
                    beneficiary.id,
                    findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
                )
            }
            coVerifyOnce { personService.get(member.personId) }
            coVerifyOnce {
                mailer.sendHealthDeclarationAppointmentEmail(
                    person.contactName,
                    person.email,
                    beneficiary.onboarding?.flowType!!
                )
            }
        }

    @Test
    fun `#handleHealthDeclarationAppointment should ignore event if phase is not HEALTH_DECLARATION_APPOINTMENT`() =
        runBlocking {
            val person = TestModelFactory.buildPerson()
            val member = TestModelFactory.buildMember(personId = person.id)

            val beneficiary = TestModelFactory.buildBeneficiary(
                memberId = member.id,
                onboarding = TestModelFactory.buildBeneficiaryOnboarding()
            )
            val newPhaseType = BeneficiaryOnboardingPhaseType.values().toList()
                .minus(BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT).random()

            val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(phase = newPhaseType)
            val event = BeneficiaryOnboardingPhaseChangedEvent(
                beneficiaryId = beneficiary.id,
                newPhase = beneficiaryOnboardingPhase,
            )

            val result = consumer.handleHealthDeclarationAppointment(event)
            assertThat(result).isSuccessWithData(true)

            coVerifyNone { beneficiaryService.get(any(), any()) }
            coVerifyNone { personService.get(any()) }
            coVerifyNone { mailer.sendHealthDeclarationAppointmentEmail(any(), any(), any()) }
        }

    @Test
    fun `#logInfo should get info and log data`() = runBlocking {
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()

        val beneficiary = TestModelFactory.buildBeneficiary(
            memberId = member.id,
            onboarding = beneficiaryOnboarding,
        )

        val newPhaseType = BeneficiaryOnboardingPhaseType.values().toList()
            .minus(BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT).random()

        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(phase = newPhaseType)

        val event = BeneficiaryOnboardingPhaseChangedEvent(
            beneficiaryId = beneficiary.id,
            newPhase = beneficiaryOnboardingPhase,
        )

        coEvery {
            beneficiaryService.get(
                event.payload.beneficiaryId,
                findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
            )
        } returns beneficiary
        coEvery { memberService.get(beneficiary.memberId) } returns member
        coEvery { personService.get(member.personId) } returns person

        val result = consumer.logInfo(event)

        assertThat(result).isSuccessWithData(Unit)

        coVerifyOnce {
            logger.info(
                "beneficiary_onboarding_phase_changed",
                "national_code_identifier" to person.nationalId,
                "flow_type" to beneficiary.onboarding!!.flowType,
                "activation_date" to beneficiary.activatedAt,
                "new_phase" to event.payload.newPhase.phase,
                "beneficiary_id" to event.payload.beneficiaryId
            )
        }


        coVerifyOnce {
            beneficiaryService.get(
                event.payload.beneficiaryId,
                findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
            )
        }
        coVerifyOnce { memberService.get(beneficiary.memberId) }
        coVerifyOnce { personService.get(member.personId) }
    }

    companion object {
        @JvmStatic
        fun beneficiaryPhasesDependingOnCptsAndAge() = listOf(
            arrayOf(
                BeneficiaryOnboardingPhaseType.SEND_CPTS,
                BeneficiaryOnboardingPhaseType.SEND_CPTS,
                true,
                16,
                1,
                0,
                ParentBeneficiaryRelationType.STEPCHILD,
            ),
            arrayOf(
                BeneficiaryOnboardingPhaseType.SEND_CPTS,
                BeneficiaryOnboardingPhaseType.CPTS_CONFIRMATION,
                true,
                16,
                1,
                1,
                ParentBeneficiaryRelationType.SON_DAUGHTER_IN_LAW
            ),
            arrayOf(
                BeneficiaryOnboardingPhaseType.SEND_CPTS,
                BeneficiaryOnboardingPhaseType.CPTS_CONFIRMATION,
                true,
                16,
                1,
                1,
                ParentBeneficiaryRelationType.CHILD
            ),
            arrayOf(
                BeneficiaryOnboardingPhaseType.SEND_CPTS,
                BeneficiaryOnboardingPhaseType.CPTS_CONFIRMATION,
                true,
                33,
                1,
                1,
                null,
            ),
            arrayOf(
                BeneficiaryOnboardingPhaseType.SEND_CPTS,
                BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE,
                false,
                28,
                2,
                1,
                null,
            ),
        )
    }

    @ParameterizedTest(name = "should move from phase {0} to {1}, for person which age is {3} and that CPTs is {2}")
    @MethodSource("beneficiaryPhasesDependingOnCptsAndAge")
    fun `#handlePhaseChangeAfterHealthDeclarationAnswered should move from one phase to another depending on age and if member has CPTs`(
        newPhaseType: BeneficiaryOnboardingPhaseType,
        nextPhase: BeneficiaryOnboardingPhaseType,
        hasCpts: Boolean,
        age: Long,
        maxSteps: Int,
        movePhaseIsExecuted: Int,
        parentBeneficiaryRelationType: ParentBeneficiaryRelationType?,
    ) = runBlocking {
        val person = TestModelFactory.buildPerson(dateOfBirth = LocalDateTime.now().minusYears(age))
        val beneficiaryId = RangeUUID.generate()
        val member = TestModelFactory.buildMember(personId = person.id)
        val beneficiary = TestModelFactory.buildBeneficiary(
            id = beneficiaryId,
            personId = person.id,
            memberId = member.id,
            onboarding = TestModelFactory.buildBeneficiaryOnboarding(),
            parentBeneficiaryRelationType = parentBeneficiaryRelationType
        )
        val healthDeclaration = TestModelFactory.buildHealthDeclaration(personId = person.id)
        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(phase = newPhaseType)
        val event = BeneficiaryOnboardingPhaseChangedEvent(
            beneficiaryId = beneficiaryId,
            newPhase = beneficiaryOnboardingPhase
        )

        coEvery { beneficiaryService.get(event.payload.beneficiaryId) } returns beneficiary
        coEvery { beneficiaryService.findByMemberId(any()) } returns beneficiary
        coEvery { healthDeclarationForm.hasCpts(member.id) } returns hasCpts
        coEvery { healthDeclarationForm.findByPerson(any()) } returns healthDeclaration
        coEvery {
            beneficiaryOnboardingService.moveToPhase(
                beneficiaryId,
                nextPhase, event.eventDate, maxSteps
            )
        } returns beneficiaryOnboardingPhase
        coEvery { personService.get(beneficiary.personId) } returns person
        coEvery { memberService.get(member.id) } returns member

        val result = consumer.handlePhaseChangeAfterHealthDeclarationAnswered(event)
        assertThat(result).isSuccessWithData(true)

        coVerify(exactly = movePhaseIsExecuted) {
            beneficiaryOnboardingService.moveToPhase(
                beneficiary.id,
                nextPhase,
                event.eventDate,
                maxSteps,
            )

        }
    }

    @Test
    fun `#handlePhaseChangeAfterHealthDeclarationAnswered should trigger the alert for sending responsibility term when in SEND_CPTS for minor person`() =
        runBlocking {
            val beneficiaryId = RangeUUID.generate()
            val person = TestModelFactory.buildPerson(dateOfBirth = LocalDateTime.now().minusYears(16))
            val sendCptsPhase = BeneficiaryOnboardingPhaseType.SEND_CPTS

            val beneficiary = TestModelFactory.buildBeneficiary(
                id = beneficiaryId,
                personId = person.id,
                onboarding = TestModelFactory.buildBeneficiaryOnboarding()
            )

            val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(phase = sendCptsPhase)
            val event = BeneficiaryOnboardingPhaseChangedEvent(
                beneficiaryId = beneficiaryId,
                newPhase = beneficiaryOnboardingPhase,
            )

            coEvery { beneficiaryService.get(event.payload.beneficiaryId) } returns beneficiary
            coEvery { personService.get(beneficiary.personId) } returns person

            val result = consumer.handlePhaseChangeAfterHealthDeclarationAnswered(event)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce {
                logger.info(
                    "Alert to send responsibility term for minor person",
                    "person_id" to person.id.toString(),
                    "national_code_identifier" to person.nationalId,
                )
            }

            coVerifyOnce { personService.get(beneficiary.personId) }
            coVerifyOnce { beneficiaryService.get(event.payload.beneficiaryId) }
        }


    @Test
    fun `#generateHealthDeclarationContract should create the Health Declaration contract once member got into the phase CONTRACT_SIGNATURE`() =
        runBlocking {
            val beneficiaryId = RangeUUID.generate()
            val person = TestModelFactory.buildPerson()
            val member = TestModelFactory.buildMember(personId = person.id)
            val contractSignaturePhase = BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE

            val beneficiary = TestModelFactory.buildBeneficiary(
                id = beneficiaryId,
                memberId = member.id,
                personId = person.id,
                onboarding = TestModelFactory.buildBeneficiaryOnboarding()
            )

            val beneficiaryOnboardingPhase =
                TestModelFactory.buildBeneficiaryOnboardingPhase(phase = contractSignaturePhase)
            val event = BeneficiaryOnboardingPhaseChangedEvent(
                beneficiaryId = beneficiaryId,
                newPhase = beneficiaryOnboardingPhase,
            )

            val memberContractId = RangeUUID.generate()
            val memberContractTerm =
                TestModelFactory.buildMemberContractTerm(memberId = member.id, memberContractId = memberContractId)
            val memberContract = TestModelFactory.buildMemberContract(terms = listOf(memberContractTerm))

            coEvery { beneficiaryService.get(beneficiaryId) } returns beneficiary
            coEvery { memberService.get(member.id) } returns member
            coEvery { contractGenerator.generateContractB2B(member, beneficiary) } returns memberContract

            val result = consumer.generateContractB2B(event)
            assertThat(result).isSuccessWithData(memberContract)

            coVerifyOnce { beneficiaryService.get(event.payload.beneficiaryId) }
            coVerifyOnce { contractGenerator.generateContractB2B(member, beneficiary) }
        }

    @Test
    fun `#generateHealthDeclarationContract should be metric the exceptions`() =
        runBlocking {
            val beneficiaryId = RangeUUID.generate()
            val person = TestModelFactory.buildPerson()
            val member = TestModelFactory.buildMember(personId = person.id)
            val contractSignaturePhase = BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE

            val beneficiary = TestModelFactory.buildBeneficiary(
                id = beneficiaryId,
                memberId = member.id,
                personId = person.id,
                onboarding = TestModelFactory.buildBeneficiaryOnboarding()
            )

            val beneficiaryOnboardingPhase =
                TestModelFactory.buildBeneficiaryOnboardingPhase(phase = contractSignaturePhase)
            val event = BeneficiaryOnboardingPhaseChangedEvent(
                beneficiaryId = beneficiaryId,
                newPhase = beneficiaryOnboardingPhase,
            )

            coEvery { beneficiaryService.get(beneficiaryId) } returns beneficiary
            coEvery { memberService.get(member.id) } returns member
            coEvery { contractGenerator.generateContractB2B(member, beneficiary) } returns Exception("")
            coEvery { personService.get(person.id) } returns person

            val result = consumer.generateContractB2B(event)
            assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce { beneficiaryService.get(event.payload.beneficiaryId) }
            coVerifyOnce { contractGenerator.generateContractB2B(member, beneficiary) }
            coVerifyOnce { BeneficiaryMetric.failedToCreateHealthDeclarationContract(person) }

        }

    @Test
    fun `#generateHealthDeclarationContract should trigger metrics when an exception occurs while generating the contract`() =
        runBlocking {
            val beneficiaryId = RangeUUID.generate()
            val person = TestModelFactory.buildPerson()
            val member = TestModelFactory.buildMember(personId = person.id)
            val contractSignaturePhase = BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE

            val beneficiary = TestModelFactory.buildBeneficiary(
                id = beneficiaryId,
                memberId = member.id,
                personId = person.id,
                onboarding = TestModelFactory.buildBeneficiaryOnboarding()
            )

            val beneficiaryOnboardingPhase =
                TestModelFactory.buildBeneficiaryOnboardingPhase(phase = contractSignaturePhase)
            val event = BeneficiaryOnboardingPhaseChangedEvent(
                beneficiaryId = beneficiaryId,
                newPhase = beneficiaryOnboardingPhase,
            )

            coEvery { beneficiaryService.get(beneficiaryId) } returns beneficiary
            coEvery { memberService.get(member.id) } returns member
            coEvery { contractGenerator.generateContractB2B(member, beneficiary) } returns DuplicatedItemException("")

            val result = consumer.generateContractB2B(event)
            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { beneficiaryService.get(event.payload.beneficiaryId) }
            coVerifyOnce { contractGenerator.generateContractB2B(member, beneficiary) }
            coVerifyNone { BeneficiaryMetric.failedToCreateHealthDeclarationContract(any()) }

        }

    @Test
    fun `#updatePreviousPhase should update finishedAt field of the previous phase`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val sendCptsPhase = BeneficiaryOnboardingPhaseType.SEND_CPTS
        val beneficiaryOnboardingId = RangeUUID.generate()
        val beneficiaryOnboardingPreviousPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.WAITING_CPTS_APPLICATION,
            beneficiaryOnboardingId = beneficiaryOnboardingId,
            transactedAt = LocalDateTime.now().minusDays(2),
        )
        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = sendCptsPhase,
            beneficiaryOnboardingId = beneficiaryOnboardingId,
            transactedAt = LocalDateTime.now().minusDays(1),
        )
        val beneficiaryOnboardingPreviousPhaseUpdated =
            beneficiaryOnboardingPreviousPhase.copy(finishedAt = beneficiaryOnboardingPhase.transactedAt).toModel()

        val event = BeneficiaryOnboardingPhaseChangedEvent(
            beneficiaryId = beneficiaryId,
            newPhase = beneficiaryOnboardingPhase,
        )

        coEvery { beneficiaryOnboardingService.getPreviousPhase(event.payload.newPhase) } returns beneficiaryOnboardingPreviousPhase
        coEvery { beneficiaryOnboardingPhaseDataService.update(beneficiaryOnboardingPreviousPhaseUpdated) } returns beneficiaryOnboardingPreviousPhaseUpdated

        val result = consumer.updatePreviousPhase(event)
        assertThat(result).isSuccessWithData(beneficiaryOnboardingPreviousPhaseUpdated)

        coVerifyOnce { beneficiaryOnboardingService.getPreviousPhase(event.payload.newPhase) }
        coVerifyOnce { beneficiaryOnboardingPhaseDataService.update(beneficiaryOnboardingPreviousPhaseUpdated) }
    }

    @Test
    fun `#activeBeneficiaryIfOnLastPhase should active member if beneficiary went to last phase`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary()
        val activatedBeneficiary = beneficiary.copy(memberStatus = MemberStatus.ACTIVE)
        val beneficiaryOnboardingPhase =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.FINISHED)

        coEvery { beneficiaryService.activateBeneficiary(beneficiary.id) } returns activatedBeneficiary

        val event = BeneficiaryOnboardingPhaseChangedEvent(beneficiary.id, beneficiaryOnboardingPhase)

        val result = consumer.activeBeneficiaryIfOnLastPhase(event)
        assertThat(result).isSuccessWithData(activatedBeneficiary)

        coVerifyOnce { beneficiaryService.activateBeneficiary(beneficiary.id) }
    }

    @Test
    fun `#activeBeneficiaryIfOnLastPhase shouldnt activate if beneficiary is in early phase`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary()
        val beneficiaryOnboardingPhase =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE)

        val event = BeneficiaryOnboardingPhaseChangedEvent(beneficiary.id, beneficiaryOnboardingPhase)

        val result = consumer.activeBeneficiaryIfOnLastPhase(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyNone { beneficiaryService.activateBeneficiary(beneficiary.id) }
    }

    @Test
    fun `#activeBeneficiaryIfOnLastPhase should active member if beneficiary is on WAITING_FOR_REVIEW and move to finished`() =
        runBlocking {
            val beneficiary = TestModelFactory.buildBeneficiary()
            val activatedBeneficiary = beneficiary.copy(memberStatus = MemberStatus.ACTIVE)
            val beneficiaryOnboardingPhase =
                TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW)
            val newBeneficiaryOnboardingPhase =
                TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.FINISHED)

            coEvery { beneficiaryService.activateBeneficiary(beneficiary.id) } returns beneficiary.copy(memberStatus = MemberStatus.ACTIVE)
            coEvery { beneficiaryOnboardingService.moveToNextPhase(beneficiary.id) } returns newBeneficiaryOnboardingPhase

            val event = BeneficiaryOnboardingPhaseChangedEvent(beneficiary.id, beneficiaryOnboardingPhase)

            val result = consumer.activeBeneficiaryIfOnLastPhase(event)
            assertThat(result).isSuccessWithData(activatedBeneficiary)

            coVerifyOnce { beneficiaryService.activateBeneficiary(beneficiary.id) }
            coVerifyOnce { beneficiaryOnboardingService.moveToNextPhase(beneficiary.id) }
        }

    @Test
    fun `#activeBeneficiaryIfOnLastPhase should active member if beneficiary is on REGISTRATION and does not move to finished`() =
        runBlocking {
            val beneficiary = TestModelFactory.buildBeneficiary()
            val activatedBeneficiary = beneficiary.copy(memberStatus = MemberStatus.ACTIVE)
            val beneficiaryOnboardingPhase =
                TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.REGISTRATION)
            val newBeneficiaryOnboardingPhase =
                TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.FINISHED)

            coEvery { beneficiaryService.activateBeneficiary(beneficiary.id) } returns beneficiary.copy(memberStatus = MemberStatus.ACTIVE)

            val event = BeneficiaryOnboardingPhaseChangedEvent(beneficiary.id, beneficiaryOnboardingPhase)

            val result = consumer.activeBeneficiaryIfOnLastPhase(event)
            assertThat(result).isSuccessWithData(activatedBeneficiary)

            coVerifyOnce { beneficiaryService.activateBeneficiary(beneficiary.id) }
            coVerifyNone { beneficiaryOnboardingService.moveToNextPhase(any()) }
        }


    @Test
    fun `#activeBeneficiaryIfOnLastPhase should return true if error on member activation`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary()
        val beneficiaryOnboardingPhase =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW)

        coEvery { beneficiaryService.activateBeneficiary(beneficiary.id) } returns beneficiary.copy(memberStatus = MemberStatus.ACTIVE)
        coEvery { beneficiaryOnboardingService.moveToNextPhase(beneficiary.id) } returns BeneficiaryActivationValidationException(
            beneficiary
        ).failure()

        val event = BeneficiaryOnboardingPhaseChangedEvent(beneficiary.id, beneficiaryOnboardingPhase)

        val result = consumer.activeBeneficiaryIfOnLastPhase(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { beneficiaryService.activateBeneficiary(beneficiary.id) }
        coVerifyOnce { beneficiaryOnboardingService.moveToNextPhase(beneficiary.id) }
    }

    @Test
    fun `#handleProcessFinished works properly when onboarding is on last phase`() = runBlocking {
        val beneficiaryOnboardingId = RangeUUID.generate()
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
            id = beneficiaryOnboardingId,
            phases = listOf(
                TestModelFactory.buildBeneficiaryOnboardingPhase
                    (
                    beneficiaryOnboardingId = beneficiaryOnboardingId,
                    phase = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW,
                    transactedAt = LocalDateTime.now(),
                )
            )
        )
        val beneficiaryOnboardingPhaseChangedEvent = BeneficiaryOnboardingPhaseChangedEvent(
            beneficiaryId = beneficiaryOnboarding.beneficiaryId,
            newPhase = beneficiaryOnboarding.phases.last()
        )

        val beneficiaryOnboardingFinishedEvent = BeneficiaryOnboardingFinishedEvent(
            beneficiaryId = beneficiaryOnboarding.beneficiaryId
        )

        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        consumer.handleProcessFinished(beneficiaryOnboardingPhaseChangedEvent)

        coVerifyOnce {
            kafkaProducerService.produce(match<BeneficiaryOnboardingFinishedEvent> {
                it.payload.beneficiaryId == beneficiaryOnboardingFinishedEvent.payload.beneficiaryId
            })
        }
    }

    @Nested
    inner class HandleBeneficiaryVideoCallFinished {

        @Test
        fun `should produce an event when the whole company finish the video call onboarding phase`() =
            runBlocking<Unit> {
                val person = TestModelFactory.buildPerson()
                val secondPerson = TestModelFactory.buildPerson()
                val member = TestModelFactory.buildMember(personId = person.id)
                val secondMember = TestModelFactory.buildMember(personId = secondPerson.id)

                val company = TestModelFactory.buildCompany()
                val beneficiary = TestModelFactory.buildBeneficiary(
                    memberId = member.id,
                    companyId = company.id,
                    personId = person.id,
                )
                val secondBeneficiary =
                    TestModelFactory.buildBeneficiary(
                        memberId = secondMember.id,
                        companyId = company.id,
                        personId = secondPerson.id,
                    )
                val currentPhase = BeneficiaryOnboardingPhaseType.WAITING_CPTS_APPLICATION
                val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(phase = currentPhase)

                val secondBeneficiaryOnboardingPhase =
                    TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.CPTS_CONFIRMATION)
                val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
                    beneficiaryId = beneficiary.id,
                    phases = listOf(secondBeneficiaryOnboardingPhase)
                )
                val secondBeneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
                    beneficiaryId = secondBeneficiary.id,
                    phases = listOf(beneficiaryOnboardingPhase)
                )

                val event = BeneficiaryOnboardingPhaseChangedEvent(
                    beneficiaryId = beneficiary.id,
                    newPhase = beneficiaryOnboardingPhase,
                )

                coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary
                coEvery { beneficiaryService.findByCompanyId(beneficiary.companyId) } returns listOf(
                    beneficiary,
                    secondBeneficiary
                )
                coEvery {
                    beneficiaryOnboardingService.findByBeneficiaryIds(
                        listOf(
                            beneficiary.id,
                            secondBeneficiary.id
                        )
                    )
                } returns listOf(beneficiaryOnboarding, secondBeneficiaryOnboarding)
                coEvery {
                    memberService.getCurrentsByPersonIds(
                        listOf(
                            member.personId,
                            secondMember.personId
                        )
                    )
                } returns listOf(member, secondMember)

                coEvery {
                    kafkaProducerService.produce(match<CompanyBeneficiariesVideoCallFinishedEvent> {
                        it.payload.companyId == company.id
                    })
                } returns mockk()

                val result = consumer.handleBeneficiaryVideoCallFinished(event)

                assertThat(result).isSuccess()

                coVerifyOnce {
                    beneficiaryService.get(beneficiary.id)
                    beneficiaryService.findByCompanyId(beneficiary.companyId)
                    beneficiaryOnboardingService.findByBeneficiaryIds(
                        listOf(
                            beneficiary.id,
                            secondBeneficiary.id
                        )
                    )
                    memberService.getCurrentsByPersonIds(
                        listOf(
                            member.personId,
                            secondMember.personId
                        )
                    )
                    kafkaProducerService.produce(match<CompanyBeneficiariesVideoCallFinishedEvent> {
                        it.payload.companyId == company.id
                    })
                }
            }

        @Test
        fun `should not produce an event when at least one member from company needs to finish the video call onboarding phase`() =
            runBlocking<Unit> {
                val person = TestModelFactory.buildPerson()
                val secondPerson = TestModelFactory.buildPerson()
                val member = TestModelFactory.buildMember(personId = person.id)
                val secondMember = TestModelFactory.buildMember(personId = secondPerson.id)

                val company = TestModelFactory.buildCompany()
                val beneficiary = TestModelFactory.buildBeneficiary(
                    memberId = member.id,
                    companyId = company.id,
                    personId = person.id,
                )
                val secondBeneficiary =
                    TestModelFactory.buildBeneficiary(
                        memberId = secondMember.id,
                        companyId = company.id,
                        personId = secondPerson.id,
                    )
                val currentPhase = BeneficiaryOnboardingPhaseType.WAITING_CPTS_APPLICATION
                val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(phase = currentPhase)

                val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
                    beneficiaryId = beneficiary.id,
                    phases = listOf(beneficiaryOnboardingPhase)
                )

                val secondBeneficiaryOnboardingPhase =
                    TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD)
                val secondBeneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
                    beneficiaryId = secondBeneficiary.id,
                    phases = listOf(secondBeneficiaryOnboardingPhase)
                )

                val event = BeneficiaryOnboardingPhaseChangedEvent(
                    beneficiaryId = beneficiary.id,
                    newPhase = beneficiaryOnboardingPhase,
                )

                coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary
                coEvery { beneficiaryService.findByCompanyId(beneficiary.companyId) } returns listOf(
                    beneficiary,
                    secondBeneficiary
                )
                coEvery {
                    beneficiaryOnboardingService.findByBeneficiaryIds(
                        listOf(
                            beneficiary.id,
                            secondBeneficiary.id
                        )
                    )
                } returns listOf(beneficiaryOnboarding, secondBeneficiaryOnboarding)
                coEvery {
                    memberService.getCurrentsByPersonIds(
                        listOf(
                            member.personId,
                            secondMember.personId
                        )
                    )
                } returns listOf(member, secondMember)

                val result = consumer.handleBeneficiaryVideoCallFinished(event)

                assertThat(result).isSuccess()

                coVerifyOnce {
                    beneficiaryService.get(beneficiary.id)
                    beneficiaryService.findByCompanyId(beneficiary.companyId)
                    beneficiaryOnboardingService.findByBeneficiaryIds(
                        listOf(
                            beneficiary.id,
                            secondBeneficiary.id
                        )
                    )
                    memberService.getCurrentsByPersonIds(
                        listOf(
                            member.personId,
                            secondMember.personId
                        )
                    )
                }

                coVerifyNone {
                    kafkaProducerService.produce(any())
                }
            }
    }

    @Nested
    inner class HandleCptsConfirmation {
        @Test
        fun `#should move to CONTRACT_SIGNATURE when the current phase is CPTS_CONFIRMATION and the beneficiary does not have CPTS`() =
            runBlocking {
                val person = TestModelFactory.buildPerson()
                val member = TestModelFactory.buildMember(personId = person.id)

                val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id)
                val currentPhase = BeneficiaryOnboardingPhaseType.CPTS_CONFIRMATION
                val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(phase = currentPhase)

                val event = BeneficiaryOnboardingPhaseChangedEvent(
                    beneficiaryId = beneficiary.id,
                    newPhase = beneficiaryOnboardingPhase,
                )

                coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary
                coEvery { healthDeclarationForm.hasCpts(member.id) } returns false
                coEvery {
                    beneficiaryOnboardingService.moveToPhase(
                        beneficiary.id,
                        BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE, event.eventDate, 1
                    )
                } returns beneficiaryOnboardingPhase

                val result = consumer.handleCptsConfirmation(event)

                assertThat(result).isSuccessWithData(beneficiaryOnboardingPhase)

                coVerifyOnce {
                    beneficiaryService.get(beneficiary.id)
                    healthDeclarationForm.hasCpts(member.id)
                    beneficiaryOnboardingService.moveToPhase(
                        beneficiary.id,
                        BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE, event.eventDate, 1
                    )
                }
            }

        @Test
        fun `#should not move to CONTRACT_SIGNATURE when the current phase is CPTS_CONFIRMATION and the beneficiary has CPTS`() =
            runBlocking {
                val person = TestModelFactory.buildPerson()
                val member = TestModelFactory.buildMember(personId = person.id)

                val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id)
                val currentPhase = BeneficiaryOnboardingPhaseType.CPTS_CONFIRMATION
                val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(phase = currentPhase)

                val event = BeneficiaryOnboardingPhaseChangedEvent(
                    beneficiaryId = beneficiary.id,
                    newPhase = beneficiaryOnboardingPhase,
                )

                coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary

                coEvery { healthDeclarationForm.hasCpts(member.id) } returns true

                val result = consumer.handleCptsConfirmation(event)

                assertThat(result).isSuccessWithData(true)

                coVerifyOnce {
                    beneficiaryService.get(beneficiary.id)
                    healthDeclarationForm.hasCpts(member.id)
                }
                coVerifyNone {
                    beneficiaryOnboardingService.moveToPhase(any(), any(), any(), any())
                }
            }

        @Test
        fun `#should ignore event if phase is not CPTS_CONFIRMATION`() =
            runBlocking {
                val beneficiary = TestModelFactory.buildBeneficiary()
                val currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT

                val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(phase = currentPhase)

                val event = BeneficiaryOnboardingPhaseChangedEvent(
                    beneficiaryId = beneficiary.id,
                    newPhase = beneficiaryOnboardingPhase,
                )

                val result = consumer.handleCptsConfirmation(event)
                assertThat(result).isSuccessWithData(true)

                coVerifyNone {
                    beneficiaryService.get(any())
                    healthDeclarationForm.hasCpts(any())
                    beneficiaryOnboardingService.moveToPhase(any(), any(), any(), any())
                }
            }
    }

}
