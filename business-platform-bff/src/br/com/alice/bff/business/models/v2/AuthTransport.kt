package br.com.alice.bff.business.models.v2

import br.com.alice.bff.business.services.Modules
import br.com.alice.data.layer.models.CompanySize
import br.com.alice.data.layer.models.CompanyStaffAccessLevel
import java.util.UUID

data class SendMagicLinkRequest(val email: String, val url: String)
data class SignInRequest(val idToken: String)

enum class StaffType {
    COMPANY;
}

data class StaffResponse(
    val id: UUID,
    var name: String,
    var email: String,
    var accessLevel: CompanyStaffAccessLevel,
    var type: StaffType,
    val company: CompanyResponse? = null,
    val helpDesk: HelpDeskTransport? = null,
    val enabledModules: List<Modules> = emptyList(),
)

data class CompanyResponse(
    val id: UUID,
    var name: String,
    var legalName: String,
    var companySize: CompanySize? = null,
    val hasBeneficiaries: Boolean,
)

data class HelpDeskTransport(
    val url: String
)
