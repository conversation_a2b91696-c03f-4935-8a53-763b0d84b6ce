package br.com.alice.app.content.model

import br.com.alice.app.content.model.section.SectionDataBase
import java.time.Duration

data class Section(
    val id: String,
    val type: SectionType,
    val data: SectionDataBase,
    val minAppVersion: String,
    val position: Int = 0,
    val cacheDuration: Duration? = null,
    val requiredSections: List<String>? = null,
)

data class Image(
    val url: String? = null,
    val type: ImageType? = null,
    val size: ImageSize? = null,
)

data class TabBarItem(
    val content: String,
    val action: RemoteAction,
    val icon: String? = null,
    val count: Int? = null,
)

enum class TabBarVariant {
    ICONS,
    TEXTS,
    FILTERS,
}

enum class BackgroundColor {
    VIOLET,
    YELLOW,
    ORANGE,
    BLUE,
    GREEN,
    FUCHSIA,
    LIME,
    MAGENTA,
    BACKGROUND_DEFAULT,
}

enum class Alignment {
    RIGHT,
    CENTER,
    LEFT,
    RIGHT_BOTTOM,
    RIGHT_MIDDLE,
    RIGHT_TOP
}

enum class ImageType {
    STATIC,
    STATIC_FULL_WIDTH
}

enum class ImageSize {
    SMALL,
    MEDIUM,
    REGULAR,
    BIG
}

enum class SectionTextLayout {
    H1,
    H2,
    H3,
    TEXT,
    TITLE_X_LARGE_HIGHLIGHT,
    TITLE_LARGE_HIGHLIGHT,
    TITLE_MEDIUM_HIGHLIGHT,
    TITLE_SMALL_HIGHLIGHT,
    TITLE_SMALL,
    BODY_MEDIUM,
    BODY_LARGE,
    LABEL_LARGE,
    DISPLAY_MEDIUM,
}

enum class SectionOrientation {
    VERTICAL,
    HORIZONTAL
}

enum class SectionType(val key: String, val minAppVersion: String? = null) {
    BRIDGE_SECTION("bridge_section", "3.26.0"),
    BUTTON_SECTION("button_section", "2.75.0"),
    CALENDAR_SECTION("calendar_section", "3.25.1"),
    CALLOUT_SECTION("callout_section", "3.17.0"),
    CARD_SECTION("card_section", "2.70.0"),
    CARD_SECTION_MAIN("card_section", "4.21.0"),
    CHAT_INPUT_SECTION("chat_input_section", "3.30.0"),
    CHECKBOX_SECTION("checkbox_section", "3.17.0"),
    GROUPED_COLUMN_SECTION("grouped_column_section"),
    GRID_ITEM_SECTION("grid_item_section", "4.0.0"),
    GRID_SECTION("grid_section", "4.0.0"),
    IMAGE_SECTION("image_section", "2.75.0"),
    LINK_SECTION("link_section", "3.26.0"),
    LIST_CARD_SECTION("list_card_section", "2.70.0"),
    LIST_MENU_SECTION("list_menu_section", "2.75.0"),
    MENU_SECTION("menu_section", "2.75.0"),
    MODULE_SECTION("ModuleSection"),
    PILL_RESPONSE_SECTION("pill_response_section", "3.14.1"),
    QUESTIONNAIRE_SECTION("questionnaire_section"),
    SHEET_SECTION("sheet_section", "2.75.0"),
    TAB_BAR_SECTION("tab_bar_section", "3.17.0"),
    TEXT_SECTION("text_section", "2.75.0"),
    INFINITE_SCROLL_SECTION("infinite_scroll_section", "3.54.0"),
    SHORTCUT_SECTION("shortcut_section", "3.66.0"),
    ALICE_AGORA_MAIN_SECTION("alice_agora_main_section", "4.5.0"),
    STACKED_CAROUSEL_SECTION("stacked_carousel_section", "4.13.0"),
    ACTION_CARD_SECTION("action_card_section", "4.20.0"),
    ACTIVATION_VIDEO_CALL_SECTION("activation_video_call_section", "4.20.0"),
    HEALTH_PROFESSIONAL_INFO_SECTION("health_professional_info_section", "4.33.0"),
    LARGE_LIST_CARD_SECTION("large_list_card_section", "4.33.0")
}

enum class SectionPadding {
    P0, P1, P2, P3, P6, P7,
}

enum class PillResponseSelectionAlgorithm {
    MULTIPLE,
}

data class PillOption(
    val id: String,
    val title: String,
    val body: String? = null,
    val accessory: String? = null,
)

data class CalloutAction(
    val type: CalloutType? = CalloutType.NAVIGATION,
    val label: String? = null,
    val onClickAction: RemoteAction? = null
)

enum class CalloutType {
    NONE,
    OPT_OUT,
    NAVIGATION
}

enum class CalloutVariant {
    WARNING,
    HIGHLIGHT,
    TUTORIAL,
    INFORMATION,
    MESSAGE
}

data class OnStatusChange(
    val type: ActionType = ActionType.VALIDATE
)

enum class MenuVariant {
    SIMPLE,
    INTERMEDIATE,
    DENSE,
    DEMAND,
    DEMAND_TASK,
    EXPANDABLE
}

data class Tag(
    val icon: String? = null,
    val text: String? = null,
    val colorScheme: TagColorScheme = TagColorScheme.GRAY,
) {
    companion object {
        fun openTag() = Tag(text = "Em aberto", colorScheme = TagColorScheme.GRAY)
        fun concludedTag() = Tag(text = "Concluído", colorScheme = TagColorScheme.GREEN)
        fun archivedTag() = Tag(text = "Arquivado", colorScheme = TagColorScheme.YELLOW)
        fun expiredTagWithoutDate() = Tag(text = "Expirado", colorScheme = TagColorScheme.YELLOW)
        fun scheduledTag() = Tag(text = "Agendado", colorScheme = TagColorScheme.MAGENTA)
        fun inProgressTag() = Tag(text = "Em processo", colorScheme = TagColorScheme.MAGENTA)
        fun inTreatmentTag() = Tag(text = "Em tratamento", colorScheme = TagColorScheme.MAGENTA)
    }
}

enum class TagColorScheme {
    RED,
    MAGENTA,
    VIOLET,
    BLUE,
    GREEN,
    YELLOW,
    ORANGE,
    GRAY,
}

data class Caption(
    val text: String,
    val icon: String? = null,
)

enum class BridgeRuleOnAction {
    LOADED,
    PRESSED,
    CONTENT_SELECTED,
}

enum class BridgeRuleThenProperty {
    ENABLED,
    TEXT,
}

data class BridgeRule(
    val onAction: BridgeRuleOn,
    val then: BridgeRuleThen,
)

data class BridgeRuleOn(
    val key: String,
    val action: BridgeRuleOnAction,
)

data class BridgeRuleThen(
    val make: String,
    val property: BridgeRuleThenProperty,
    val value: Any,
)

enum class CalendarMode {
    DAY,
    YEAR
}

enum class CalendarAvailableDays {
    ALL,
    WEEKDAYS
}

data class Padding(val horizontal: Double, val vertical: Double)
