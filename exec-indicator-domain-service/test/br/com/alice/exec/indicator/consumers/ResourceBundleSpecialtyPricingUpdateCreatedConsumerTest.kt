package br.com.alice.exec.indicator.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.exec.indicator.events.ResourceBundleSpecialtyPricingUpdateCreatedEvent
import br.com.alice.exec.indicator.service.internal.ProcessPricingCSVService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ResourceBundleSpecialtyPricingUpdateCreatedConsumerTest : ConsumerTest() {
    private val processPricingCSVService: ProcessPricingCSVService = mockk()
    private val resourceBundleSpecialtyPricingUpdateCreatedConsumer = ResourceBundleSpecialtyPricingUpdateCreatedConsumer(
        processPricingCSVService
    )

    @Test
    fun `processResourceBundleSpecialtyPricingUpdateCreated should process the event successfully`() = runBlocking<Unit> {
        val resourceBundleSpecialtyPricingUpdateId = RangeUUID.generate()
        val resourceBundleSpecialtyPricingUpdate = TestModelFactory.buildResourceBundleSpecialtyPricingUpdate()

        val event = ResourceBundleSpecialtyPricingUpdateCreatedEvent(
            resourceBundleSpecialtyPricingUpdateId
        )

        coEvery {
            processPricingCSVService.process(resourceBundleSpecialtyPricingUpdateId)
        } returns resourceBundleSpecialtyPricingUpdate.success()

        val result = resourceBundleSpecialtyPricingUpdateCreatedConsumer.processResourceBundleSpecialtyPricingUpdateCreated(event)

        assertThat(result).isSuccessWithData(resourceBundleSpecialtyPricingUpdate)

        coVerifyOnce {
            processPricingCSVService.process(resourceBundleSpecialtyPricingUpdateId)
        }
    }
}
