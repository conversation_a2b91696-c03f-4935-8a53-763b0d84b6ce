package br.com.alice.api.sherlock.controllers

import br.com.alice.api.sherlock.ControllerTestHelper
import br.com.alice.api.sherlock.model.ChannelByCSVQueryRequest
import br.com.alice.api.sherlock.model.ChannelQueryRequest
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelsResult
import br.com.alice.data.layer.models.QueryStatus
import br.com.alice.sherlock.client.ChannelResponse
import br.com.alice.sherlock.client.ChannelsAuditService
import br.com.alice.sherlock.client.ChannelsResultStaffService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class ChannelsAuditControllerTest : ControllerTestHelper() {
    private val staffService: StaffService = mockk()
    private val channelsResultStaffService: ChannelsResultStaffService = mockk()
    private val channelsAuditService: ChannelsAuditService = mockk()

    private val channelsAuditController = ChannelsAuditController(
        staffService,
        channelsResultStaffService,
        channelsAuditService
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        clearAllMocks()
        module.single { channelsAuditController }
    }

    private val request = ChannelQueryRequest(
        channelIds = listOf("channel-id"),
        reason = "tentando ver msg passadas",
        personInternalCode = "XVVVK"
    )

    private val expectedDTO = ChannelsResult(
        staffId = staff.id,
        id = RangeUUID.generate(),
        reason = request.reason,
        personInternalCode = request.personInternalCode,
        channelIds = request.channelIds,
        status = QueryStatus.QUEUED
    )

    @Test
    fun `#channels should return OK with channels result`() {
        val requestBody = request.copy(personInternalCode = "${request.personInternalCode} ")

        coEvery {
            channelsResultStaffService.addChannelsResult(
                staffId = staff.id,
                personInternalCode = request.personInternalCode,
                reason = request.reason,
                channelIds = request.channelIds
            )
        } returns expectedDTO.success()

        authenticatedAs(idToken, authorizedStaff) {
            post("/channels", body = requestBody) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedDTO)
            }
        }
    }

    @Test
    fun `#channelsCSV should return OK with channels result`() {
        coEvery {
            channelsResultStaffService.addChannelsResult(
                staffId = staff.id,
                personInternalCode = null,
                reason = request.reason,
                channelIds = request.channelIds
            )
        } returns expectedDTO.success()

        authenticatedAs(idToken, authorizedStaff) {
            post("/channels/by_list", body = ChannelByCSVQueryRequest(
                channelIds = request.channelIds.joinToString(","),
                reason = request.reason
            )) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedDTO)
            }
        }
    }

    @Test
    fun `#channels should return Bad Request when channels is empty`() {
        authenticatedAs(idToken, authorizedStaff) {
            post("/channels", body = request.copy(channelIds = emptyList())) { response ->
                ResponseAssert.assertThat(response).isBadRequestWithErrorCode("missing_params_values")
            }
        }
    }

    @Test
    fun `#channels should return Bad Request when channels has over MAX ids`() {
        authenticatedAs(idToken, authorizedStaff) {
            post("/channels", body = request.copy(
                channelIds = List(ChannelsAuditController.MAX_CHANNEL_BATCH_SIZE + 1) { n -> "channel-id$n" }
            )) { response ->
                ResponseAssert.assertThat(response).isBadRequestWithErrorCode("too_many_params")
            }
        }
    }

    @Test
    fun `#channels should return Bad Request when reason is blank`() {
        authenticatedAs(idToken, authorizedStaff) {
            post("/channels", body = request.copy(reason = "")) { response ->
                ResponseAssert.assertThat(response).isBadRequestWithErrorCode("missing_params_values")
            }
        }
    }

    @Test
    fun `#getChannels should return Ok with channels`() {
        val personInternalCode = "XVVKB"

        val channel = ChannelResponse(
            "balbalbalblabl",
            "canal bla bla",
            ChannelStatus.ARCHIVED,
            LocalDateTime.now()
        )

        coEvery {
            channelsAuditService.getChannels(
                personInternalCode,
                false
            )
        } returns listOf(channel).success()

        authenticatedAs(idToken, authorizedStaff) {
            get("/channels/$personInternalCode ") { response ->
                ResponseAssert.assertThat(response).isOKWithData(listOf(channel))
            }
        }
    }

}
