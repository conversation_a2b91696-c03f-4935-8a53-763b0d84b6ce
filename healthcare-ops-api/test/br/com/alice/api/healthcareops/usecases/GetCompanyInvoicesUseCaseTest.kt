package br.com.alice.api.healthcareops.usecases

import br.com.alice.api.healthcareops.models.CompanyBillingAccountableParty
import br.com.alice.api.healthcareops.models.CompanyInvoiceResponse
import br.com.alice.api.healthcareops.models.CompanyMemberInvoiceGroupInfo
import br.com.alice.api.healthcareops.models.CompanyMemberInvoiceGroupPayment
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InvoicePaymentSource
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PortalUrlGeneratorService
import br.com.alice.moneyin.models.InvoicePaymentWithPortalUrl
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

class GetCompanyInvoicesUseCaseTest {

    private val companyService: CompanyService = mockk()
    private val memberInvoiceGroupService: MemberInvoiceGroupService = mockk()
    private val invoicePaymentService: InvoicePaymentService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val companySubcontractService: CompanySubContractService = mockk()
    private val companyContractService: CompanyContractService = mockk()
    private val portalUrlGeneratorService: PortalUrlGeneratorService = mockk()

    private val getCompanyInvoicesUseCase = GetCompanyInvoicesUseCase(
        companyService,
        memberInvoiceGroupService,
        invoicePaymentService,
        billingAccountablePartyService,
        companySubcontractService,
        companyContractService,
        portalUrlGeneratorService
    )

    @Test
    fun `#getByBillingAccountableParty should return companyInvoices by billing Accountable Party`() = runBlocking {
        val product = TestModelFactory.buildProduct()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val company = TestModelFactory.buildCompany(
            availableProducts = listOf(product.id),
            defaultProductId = product.id,
            billingAccountablePartyId = billingAccountableParty.id
        )

        val memberInvoiceGroup1 =
            TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)
        val memberInvoiceGroup2 =
            TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)

        val invoicePayment1 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
        val invoicePayment2 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
        val invoicePayment3 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup2.id)

        val expected = CompanyInvoiceResponse(
            id = company.id,
            name = company.name,
            legalName = company.legalName,
            cnpj = company.cnpj,
            email = company.email,
            phoneNumber = company.phoneNumber,
            address = company.address,
            billingAccountableParty = CompanyBillingAccountableParty(
                id = billingAccountableParty.id,
                firstName = billingAccountableParty.firstName,
                lastName = billingAccountableParty.lastName,
                type = billingAccountableParty.type,
                nationalId = billingAccountableParty.nationalId,
                email = billingAccountableParty.email,
            ),
            memberInvoiceGroup = listOf(
                CompanyMemberInvoiceGroupInfo(
                    id = memberInvoiceGroup1.id,
                    externalId = memberInvoiceGroup1.externalId!!,
                    billingAccountablePartyId = memberInvoiceGroup1.billingAccountablePartyId,
                    referenceDate = memberInvoiceGroup1.referenceDate,
                    dueDate = memberInvoiceGroup1.dueDate,
                    status = memberInvoiceGroup1.status,
                    payments = listOf(
                        CompanyMemberInvoiceGroupPayment(
                            id = invoicePayment1.id,
                            totalAmount = invoicePayment1.amount,
                            status = invoicePayment1.status,
                            invoiceGroupId = invoicePayment1.invoiceGroupId,
                            paymentDetail = invoicePayment1.paymentDetail,
                            createdAt = invoicePayment1.createdAt,
                            canceledReason = invoicePayment1.canceledReason,
                            approvedAt = invoicePayment1.approvedAt,
                            reason = invoicePayment1.reason,
                            method = invoicePayment1.method,
                            externalId = invoicePayment1.externalId
                        ),
                        CompanyMemberInvoiceGroupPayment(
                            id = invoicePayment2.id,
                            totalAmount = invoicePayment2.amount,
                            status = invoicePayment2.status,
                            invoiceGroupId = invoicePayment2.invoiceGroupId,
                            paymentDetail = invoicePayment2.paymentDetail,
                            createdAt = invoicePayment2.createdAt,
                            canceledReason = invoicePayment2.canceledReason,
                            approvedAt = invoicePayment2.approvedAt,
                            reason = invoicePayment2.reason,
                            method = invoicePayment2.method,
                            externalId = invoicePayment2.externalId
                        ),
                    )
                ),
                CompanyMemberInvoiceGroupInfo(
                    id = memberInvoiceGroup2.id,
                    externalId = memberInvoiceGroup2.externalId!!,
                    billingAccountablePartyId = memberInvoiceGroup2.billingAccountablePartyId,
                    referenceDate = memberInvoiceGroup2.referenceDate,
                    dueDate = memberInvoiceGroup2.dueDate,
                    status = memberInvoiceGroup2.status,
                    payments = listOf(
                        CompanyMemberInvoiceGroupPayment(
                            id = invoicePayment3.id,
                            totalAmount = invoicePayment3.amount,
                            status = invoicePayment3.status,
                            invoiceGroupId = invoicePayment3.invoiceGroupId,
                            paymentDetail = invoicePayment3.paymentDetail,
                            createdAt = invoicePayment3.createdAt,
                            canceledReason = invoicePayment3.canceledReason,
                            approvedAt = invoicePayment3.approvedAt,
                            reason = invoicePayment3.reason,
                            method = invoicePayment3.method,
                            externalId = invoicePayment3.externalId
                        ),
                    )
                )
            )
        )

        val invoicePayments = listOf(
            invoicePayment1, invoicePayment2, invoicePayment3
        )

        coEvery { companyService.get(any()) } returns company
        coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
        coEvery { memberInvoiceGroupService.getByBillingAccountablePartyId(any()) } returns listOf(
            memberInvoiceGroup1,
            memberInvoiceGroup2
        )
        coEvery { invoicePaymentService.listInvoicePaymentsByBillingAccountablePartyId(any(), any()) } returns listOf(
            invoicePayment1, invoicePayment2, invoicePayment3
        )
        coEvery { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(invoicePayments) } returns emptyList()

        val result =
            getCompanyInvoicesUseCase.getByBillingAccountableParty(company.id, company.billingAccountablePartyId!!)

        assertEquals(expected.billingAccountableParty.id, result.billingAccountableParty.id)
        assertEquals(expected.cnpj, result.cnpj)
        assertEquals(expected.email, result.email)
        assertEquals(
            expected.memberInvoiceGroup.first().payments.first().totalAmount,
            result.memberInvoiceGroup.first().payments.first().totalAmount
        )

        coVerifyOnce { companyService.get(company.id) }
        coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
        coVerifyOnce { memberInvoiceGroupService.getByBillingAccountablePartyId(company.billingAccountablePartyId!!) }
        coVerifyOnce {
            invoicePaymentService.listInvoicePaymentsByBillingAccountablePartyId(
                company.billingAccountablePartyId!!,
                true
            )
        }
    }

    @Test
    fun `#getByBillingAccountableParty should return companyInvoices by billing Accountable Party with portalUrl for source Itau`() =
        runBlocking {
            val product = TestModelFactory.buildProduct()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val company = TestModelFactory.buildCompany(
                availableProducts = listOf(product.id),
                defaultProductId = product.id,
                billingAccountablePartyId = billingAccountableParty.id
            )

            val memberInvoiceGroup1 =
                TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)
            val memberInvoiceGroup2 =
                TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)

            val invoicePayment1 = TestModelFactory.buildInvoicePayment(
                invoiceGroupId = memberInvoiceGroup1.id,
                source = InvoicePaymentSource.ITAU
            )
            val invoicePayment2 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
            val invoicePayment3 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup2.id)

            val expected = CompanyInvoiceResponse(
                id = company.id,
                name = company.name,
                legalName = company.legalName,
                cnpj = company.cnpj,
                email = company.email,
                phoneNumber = company.phoneNumber,
                address = company.address,
                billingAccountableParty = CompanyBillingAccountableParty(
                    id = billingAccountableParty.id,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    type = billingAccountableParty.type,
                    nationalId = billingAccountableParty.nationalId,
                    email = billingAccountableParty.email,
                ),
                memberInvoiceGroup = listOf(
                    CompanyMemberInvoiceGroupInfo(
                        id = memberInvoiceGroup1.id,
                        externalId = memberInvoiceGroup1.externalId!!,
                        billingAccountablePartyId = memberInvoiceGroup1.billingAccountablePartyId,
                        referenceDate = memberInvoiceGroup1.referenceDate,
                        dueDate = memberInvoiceGroup1.dueDate,
                        status = memberInvoiceGroup1.status,
                        payments = listOf(
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment1.id,
                                totalAmount = invoicePayment1.amount,
                                status = invoicePayment1.status,
                                invoiceGroupId = invoicePayment1.invoiceGroupId,
                                paymentDetail = invoicePayment1.paymentDetail,
                                createdAt = invoicePayment1.createdAt,
                                canceledReason = invoicePayment1.canceledReason,
                                approvedAt = invoicePayment1.approvedAt,
                                reason = invoicePayment1.reason,
                                method = invoicePayment1.method,
                                externalId = invoicePayment1.externalId,
                                portalUrl = "portalUrl"
                            ),
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment2.id,
                                totalAmount = invoicePayment2.amount,
                                status = invoicePayment2.status,
                                invoiceGroupId = invoicePayment2.invoiceGroupId,
                                paymentDetail = invoicePayment2.paymentDetail,
                                createdAt = invoicePayment2.createdAt,
                                canceledReason = invoicePayment2.canceledReason,
                                approvedAt = invoicePayment2.approvedAt,
                                reason = invoicePayment2.reason,
                                method = invoicePayment2.method,
                                externalId = invoicePayment2.externalId
                            ),
                        ),
                    ),
                    CompanyMemberInvoiceGroupInfo(
                        id = memberInvoiceGroup2.id,
                        externalId = memberInvoiceGroup2.externalId!!,
                        billingAccountablePartyId = memberInvoiceGroup2.billingAccountablePartyId,
                        referenceDate = memberInvoiceGroup2.referenceDate,
                        dueDate = memberInvoiceGroup2.dueDate,
                        status = memberInvoiceGroup2.status,
                        payments = listOf(
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment3.id,
                                totalAmount = invoicePayment3.amount,
                                status = invoicePayment3.status,
                                invoiceGroupId = invoicePayment3.invoiceGroupId,
                                paymentDetail = invoicePayment3.paymentDetail,
                                createdAt = invoicePayment3.createdAt,
                                canceledReason = invoicePayment3.canceledReason,
                                approvedAt = invoicePayment3.approvedAt,
                                reason = invoicePayment3.reason,
                                method = invoicePayment3.method,
                                externalId = invoicePayment3.externalId
                            ),
                        )
                    )
                )
            )

            val invoicePayments = listOf(
                invoicePayment1, invoicePayment2, invoicePayment3
            )
            val invoicePaymentWithPortalUrl = listOf(
                InvoicePaymentWithPortalUrl(
                    invoicePayment1.id,
                    "portalUrl"
                )
            )

            coEvery { companyService.get(any()) } returns company
            coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
            coEvery { memberInvoiceGroupService.getByBillingAccountablePartyId(any()) } returns listOf(
                memberInvoiceGroup1,
                memberInvoiceGroup2
            )
            coEvery {
                invoicePaymentService.listInvoicePaymentsByBillingAccountablePartyId(
                    any(),
                    any()
                )
            } returns listOf(
                invoicePayment1, invoicePayment2, invoicePayment3
            )
            coEvery { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(invoicePayments) } returns invoicePaymentWithPortalUrl

            val result =
                getCompanyInvoicesUseCase.getByBillingAccountableParty(company.id, company.billingAccountablePartyId!!)

            assertEquals(expected.billingAccountableParty.id, result.billingAccountableParty.id)
            assertEquals(expected.cnpj, result.cnpj)
            assertEquals(expected.email, result.email)
            assertEquals(
                expected.memberInvoiceGroup.first().payments.first().totalAmount,
                result.memberInvoiceGroup.first().payments.first().totalAmount
            )
            assertEquals(expected.memberInvoiceGroup.first().payments.first().portalUrl, "portalUrl")

            coVerifyOnce { companyService.get(company.id) }
            coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
            coVerifyOnce { memberInvoiceGroupService.getByBillingAccountablePartyId(company.billingAccountablePartyId!!) }
            coVerifyOnce {
                invoicePaymentService.listInvoicePaymentsByBillingAccountablePartyId(
                    company.billingAccountablePartyId!!,
                    true
                )
            }
            coVerifyOnce {
                portalUrlGeneratorService.mountPortalUrlForInvoicePayments(any())
            }
        }

    @Test
    fun `#getByCompanySubcontract - should return CompanyInvoiceResponse by a subcontract`() = runBlocking {
        val product = TestModelFactory.buildProduct()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val company = TestModelFactory.buildCompany(
            availableProducts = listOf(product.id),
            defaultProductId = product.id,
            billingAccountablePartyId = billingAccountableParty.id
        )
        val companySubcontract = TestModelFactory.buildCompanySubContract(
            companyId = company.id,
            billingAccountablePartyId = billingAccountableParty.id
        )

        val memberInvoiceGroup1 =
            TestModelFactory.buildMemberInvoiceGroup(
                billingAccountablePartyId = company.billingAccountablePartyId!!,
                type = MemberInvoiceType.B2B_FIRST_PAYMENT,
            )
        val memberInvoiceGroup2 =
            TestModelFactory.buildMemberInvoiceGroup(
                billingAccountablePartyId = company.billingAccountablePartyId!!,
                type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
            )

        val invoicePayment1 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
        val invoicePayment2 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
        val invoicePayment3 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup2.id)

        val expected = CompanyInvoiceResponse(
            id = company.id,
            name = company.name,
            legalName = company.legalName,
            cnpj = company.cnpj,
            email = company.email,
            phoneNumber = company.phoneNumber,
            address = company.address,
            billingAccountableParty = CompanyBillingAccountableParty(
                id = billingAccountableParty.id,
                firstName = billingAccountableParty.firstName,
                lastName = billingAccountableParty.lastName,
                type = billingAccountableParty.type,
                nationalId = billingAccountableParty.nationalId,
                email = billingAccountableParty.email,
            ),
            memberInvoiceGroup = listOf(
                CompanyMemberInvoiceGroupInfo(
                    id = memberInvoiceGroup1.id,
                    externalId = memberInvoiceGroup1.externalId!!,
                    billingAccountablePartyId = memberInvoiceGroup1.billingAccountablePartyId,
                    referenceDate = memberInvoiceGroup1.referenceDate,
                    dueDate = memberInvoiceGroup1.dueDate,
                    status = memberInvoiceGroup1.status,
                    type = memberInvoiceGroup1.type,
                    payments = listOf(
                        CompanyMemberInvoiceGroupPayment(
                            id = invoicePayment1.id,
                            totalAmount = invoicePayment1.amount,
                            status = invoicePayment1.status,
                            invoiceGroupId = invoicePayment1.invoiceGroupId,
                            paymentDetail = invoicePayment1.paymentDetail,
                            createdAt = invoicePayment1.createdAt,
                            canceledReason = invoicePayment1.canceledReason,
                            approvedAt = invoicePayment1.approvedAt,
                            reason = invoicePayment1.reason,
                            method = invoicePayment1.method,
                            externalId = invoicePayment1.externalId
                        ),
                        CompanyMemberInvoiceGroupPayment(
                            id = invoicePayment2.id,
                            totalAmount = invoicePayment2.amount,
                            status = invoicePayment2.status,
                            invoiceGroupId = invoicePayment2.invoiceGroupId,
                            paymentDetail = invoicePayment2.paymentDetail,
                            createdAt = invoicePayment2.createdAt,
                            canceledReason = invoicePayment2.canceledReason,
                            approvedAt = invoicePayment2.approvedAt,
                            reason = invoicePayment2.reason,
                            method = invoicePayment2.method,
                            externalId = invoicePayment2.externalId
                        ),
                    )
                ),
                CompanyMemberInvoiceGroupInfo(
                    id = memberInvoiceGroup2.id,
                    externalId = memberInvoiceGroup2.externalId!!,
                    billingAccountablePartyId = memberInvoiceGroup2.billingAccountablePartyId,
                    referenceDate = memberInvoiceGroup2.referenceDate,
                    dueDate = memberInvoiceGroup2.dueDate,
                    status = memberInvoiceGroup2.status,
                    type = memberInvoiceGroup2.type,
                    payments = listOf(
                        CompanyMemberInvoiceGroupPayment(
                            id = invoicePayment3.id,
                            totalAmount = invoicePayment3.amount,
                            status = invoicePayment3.status,
                            invoiceGroupId = invoicePayment3.invoiceGroupId,
                            paymentDetail = invoicePayment3.paymentDetail,
                            createdAt = invoicePayment3.createdAt,
                            canceledReason = invoicePayment3.canceledReason,
                            approvedAt = invoicePayment3.approvedAt,
                            reason = invoicePayment3.reason,
                            method = invoicePayment3.method,
                            externalId = invoicePayment3.externalId
                        ),
                    )
                )
            )
        )

        val invoicePayments = listOf(
            invoicePayment1, invoicePayment2, invoicePayment3
        )

        coEvery { companyService.get(any()) } returns company
        coEvery { companySubcontractService.get(any()) } returns companySubcontract
        coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
        coEvery { memberInvoiceGroupService.getBySubcontractId(any()) } returns listOf(
            memberInvoiceGroup1,
            memberInvoiceGroup2
        )
        coEvery { invoicePaymentService.getByInvoiceGroupIds(any(), any()) } returns listOf(
            invoicePayment1, invoicePayment2, invoicePayment3
        )
        coEvery { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(invoicePayments) } returns emptyList()

        val result = getCompanyInvoicesUseCase.getBySubcontract(company.id, companySubcontract.id)

        assertEquals(expected.billingAccountableParty.id, result.billingAccountableParty.id)
        assertEquals(expected.cnpj, result.cnpj)
        assertEquals(expected.email, result.email)
        assertEquals(
            expected.memberInvoiceGroup.first().payments.first().totalAmount,
            result.memberInvoiceGroup.first().payments.first().totalAmount
        )

        coVerifyOnce { companyService.get(company.id) }
        coVerifyOnce { companySubcontractService.get(companySubcontract.id) }
        coVerifyOnce { billingAccountablePartyService.get(companySubcontract.billingAccountablePartyId!!) }
        coVerifyOnce { memberInvoiceGroupService.getBySubcontractId(companySubcontract.id) }
        coVerifyOnce {
            invoicePaymentService.getByInvoiceGroupIds(
                listOf(memberInvoiceGroup1.id, memberInvoiceGroup2.id),
                true
            )
        }
        coVerifyOnce { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(any()) }

        coVerifyNone { companyContractService.get(any()) }
    }

    @Test
    fun `#getByCompanySubcontract - should return CompanyInvoiceResponse by a subcontract and return portalUrl when source is ITAU`() =
        runBlocking {
            val product = TestModelFactory.buildProduct()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val company = TestModelFactory.buildCompany(
                availableProducts = listOf(product.id),
                defaultProductId = product.id,
                billingAccountablePartyId = billingAccountableParty.id
            )
            val companySubcontract = TestModelFactory.buildCompanySubContract(
                companyId = company.id,
                billingAccountablePartyId = billingAccountableParty.id
            )

            val memberInvoiceGroup1 =
                TestModelFactory.buildMemberInvoiceGroup(
                    billingAccountablePartyId = company.billingAccountablePartyId!!,
                    type = MemberInvoiceType.B2B_FIRST_PAYMENT,
                )
            val memberInvoiceGroup2 =
                TestModelFactory.buildMemberInvoiceGroup(
                    billingAccountablePartyId = company.billingAccountablePartyId!!,
                    type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
                )

            val invoicePayment1 = TestModelFactory.buildInvoicePayment(
                invoiceGroupId = memberInvoiceGroup1.id,
                source = InvoicePaymentSource.ITAU
            )
            val invoicePayment2 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
            val invoicePayment3 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup2.id)

            val expected = CompanyInvoiceResponse(
                id = company.id,
                name = company.name,
                legalName = company.legalName,
                cnpj = company.cnpj,
                email = company.email,
                phoneNumber = company.phoneNumber,
                address = company.address,
                billingAccountableParty = CompanyBillingAccountableParty(
                    id = billingAccountableParty.id,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    type = billingAccountableParty.type,
                    nationalId = billingAccountableParty.nationalId,
                    email = billingAccountableParty.email,
                ),
                memberInvoiceGroup = listOf(
                    CompanyMemberInvoiceGroupInfo(
                        id = memberInvoiceGroup1.id,
                        externalId = memberInvoiceGroup1.externalId!!,
                        billingAccountablePartyId = memberInvoiceGroup1.billingAccountablePartyId,
                        referenceDate = memberInvoiceGroup1.referenceDate,
                        dueDate = memberInvoiceGroup1.dueDate,
                        status = memberInvoiceGroup1.status,
                        type = memberInvoiceGroup1.type,
                        payments = listOf(
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment1.id,
                                totalAmount = invoicePayment1.amount,
                                status = invoicePayment1.status,
                                invoiceGroupId = invoicePayment1.invoiceGroupId,
                                paymentDetail = invoicePayment1.paymentDetail,
                                createdAt = invoicePayment1.createdAt,
                                canceledReason = invoicePayment1.canceledReason,
                                approvedAt = invoicePayment1.approvedAt,
                                reason = invoicePayment1.reason,
                                method = invoicePayment1.method,
                                externalId = invoicePayment1.externalId,
                                portalUrl = "portalUrl"
                            ),
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment2.id,
                                totalAmount = invoicePayment2.amount,
                                status = invoicePayment2.status,
                                invoiceGroupId = invoicePayment2.invoiceGroupId,
                                paymentDetail = invoicePayment2.paymentDetail,
                                createdAt = invoicePayment2.createdAt,
                                canceledReason = invoicePayment2.canceledReason,
                                approvedAt = invoicePayment2.approvedAt,
                                reason = invoicePayment2.reason,
                                method = invoicePayment2.method,
                                externalId = invoicePayment2.externalId
                            ),
                        )
                    ),
                    CompanyMemberInvoiceGroupInfo(
                        id = memberInvoiceGroup2.id,
                        externalId = memberInvoiceGroup2.externalId!!,
                        billingAccountablePartyId = memberInvoiceGroup2.billingAccountablePartyId,
                        referenceDate = memberInvoiceGroup2.referenceDate,
                        dueDate = memberInvoiceGroup2.dueDate,
                        status = memberInvoiceGroup2.status,
                        type = memberInvoiceGroup2.type,
                        payments = listOf(
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment3.id,
                                totalAmount = invoicePayment3.amount,
                                status = invoicePayment3.status,
                                invoiceGroupId = invoicePayment3.invoiceGroupId,
                                paymentDetail = invoicePayment3.paymentDetail,
                                createdAt = invoicePayment3.createdAt,
                                canceledReason = invoicePayment3.canceledReason,
                                approvedAt = invoicePayment3.approvedAt,
                                reason = invoicePayment3.reason,
                                method = invoicePayment3.method,
                                externalId = invoicePayment3.externalId
                            ),
                        )
                    )
                )
            )

            val invoicePayments = listOf(
                invoicePayment1, invoicePayment2, invoicePayment3
            )
            val invoicePaymentsWithPortalUrl = listOf(
                InvoicePaymentWithPortalUrl(
                    invoicePayment1.id,
                    "portalUrl"
                )
            )

            coEvery { companyService.get(any()) } returns company
            coEvery { companySubcontractService.get(any()) } returns companySubcontract
            coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
            coEvery { memberInvoiceGroupService.getBySubcontractId(any()) } returns listOf(
                memberInvoiceGroup1,
                memberInvoiceGroup2
            )
            coEvery { invoicePaymentService.getByInvoiceGroupIds(any(), any()) } returns listOf(
                invoicePayment1, invoicePayment2, invoicePayment3
            )
            coEvery { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(invoicePayments) } returns invoicePaymentsWithPortalUrl

            val result = getCompanyInvoicesUseCase.getBySubcontract(company.id, companySubcontract.id)

            assertEquals(expected.billingAccountableParty.id, result.billingAccountableParty.id)
            assertEquals(expected.cnpj, result.cnpj)
            assertEquals(expected.email, result.email)
            assertEquals(
                expected.memberInvoiceGroup.first().payments.first().totalAmount,
                result.memberInvoiceGroup.first().payments.first().totalAmount
            )

            coVerifyOnce { companyService.get(company.id) }
            coVerifyOnce { companySubcontractService.get(companySubcontract.id) }
            coVerifyOnce { billingAccountablePartyService.get(companySubcontract.billingAccountablePartyId!!) }
            coVerifyOnce { memberInvoiceGroupService.getBySubcontractId(companySubcontract.id) }
            coVerifyOnce {
                invoicePaymentService.getByInvoiceGroupIds(
                    listOf(memberInvoiceGroup1.id, memberInvoiceGroup2.id),
                    true
                )
            }
            coVerifyOnce {
                portalUrlGeneratorService.mountPortalUrlForInvoicePayments(any())
            }

            coVerifyNone { companyContractService.get(any()) }
        }

    @Test
    fun `#getByCompanySubcontract - should return CompanyInvoiceResponse by a subcontract getting billingAccountableParty by contract`() =
        runBlocking {
            val product = TestModelFactory.buildProduct()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val company = TestModelFactory.buildCompany(
                availableProducts = listOf(product.id),
                defaultProductId = product.id,
                billingAccountablePartyId = billingAccountableParty.id
            )
            val companySubcontract =
                TestModelFactory.buildCompanySubContract(companyId = company.id, billingAccountablePartyId = null)
            val companyContract = TestModelFactory.buildCompanyContract(
                id = companySubcontract.contractId,
                billingAccountablePartyId = billingAccountableParty.id
            )
            val memberInvoiceGroup1 =
                TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)
            val memberInvoiceGroup2 =
                TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)

            val invoicePayment1 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
            val invoicePayment2 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
            val invoicePayment3 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup2.id)

            val expected = CompanyInvoiceResponse(
                id = company.id,
                name = company.name,
                legalName = company.legalName,
                cnpj = company.cnpj,
                email = company.email,
                phoneNumber = company.phoneNumber,
                address = company.address,
                billingAccountableParty = CompanyBillingAccountableParty(
                    id = billingAccountableParty.id,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    type = billingAccountableParty.type,
                    nationalId = billingAccountableParty.nationalId,
                    email = billingAccountableParty.email,
                ),
                memberInvoiceGroup = listOf(
                    CompanyMemberInvoiceGroupInfo(
                        id = memberInvoiceGroup1.id,
                        externalId = memberInvoiceGroup1.externalId!!,
                        billingAccountablePartyId = memberInvoiceGroup1.billingAccountablePartyId,
                        referenceDate = memberInvoiceGroup1.referenceDate,
                        dueDate = memberInvoiceGroup1.dueDate,
                        status = memberInvoiceGroup1.status,
                        payments = listOf(
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment1.id,
                                totalAmount = invoicePayment1.amount,
                                status = invoicePayment1.status,
                                invoiceGroupId = invoicePayment1.invoiceGroupId,
                                paymentDetail = invoicePayment1.paymentDetail,
                                createdAt = invoicePayment1.createdAt,
                                canceledReason = invoicePayment1.canceledReason,
                                approvedAt = invoicePayment1.approvedAt,
                                reason = invoicePayment1.reason,
                                method = invoicePayment1.method,
                                externalId = invoicePayment1.externalId
                            ),
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment2.id,
                                totalAmount = invoicePayment2.amount,
                                status = invoicePayment2.status,
                                invoiceGroupId = invoicePayment2.invoiceGroupId,
                                paymentDetail = invoicePayment2.paymentDetail,
                                createdAt = invoicePayment2.createdAt,
                                canceledReason = invoicePayment2.canceledReason,
                                approvedAt = invoicePayment2.approvedAt,
                                reason = invoicePayment2.reason,
                                method = invoicePayment2.method,
                                externalId = invoicePayment2.externalId
                            ),
                        )
                    ),
                    CompanyMemberInvoiceGroupInfo(
                        id = memberInvoiceGroup2.id,
                        externalId = memberInvoiceGroup2.externalId!!,
                        billingAccountablePartyId = memberInvoiceGroup2.billingAccountablePartyId,
                        referenceDate = memberInvoiceGroup2.referenceDate,
                        dueDate = memberInvoiceGroup2.dueDate,
                        status = memberInvoiceGroup2.status,
                        payments = listOf(
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment3.id,
                                totalAmount = invoicePayment3.amount,
                                status = invoicePayment3.status,
                                invoiceGroupId = invoicePayment3.invoiceGroupId,
                                paymentDetail = invoicePayment3.paymentDetail,
                                createdAt = invoicePayment3.createdAt,
                                canceledReason = invoicePayment3.canceledReason,
                                approvedAt = invoicePayment3.approvedAt,
                                reason = invoicePayment3.reason,
                                method = invoicePayment3.method,
                                externalId = invoicePayment3.externalId
                            ),
                        )
                    )
                )
            )

            val invoicePayments = listOf(
                invoicePayment1, invoicePayment2, invoicePayment3
            )

            coEvery { companyService.get(any()) } returns company
            coEvery { companySubcontractService.get(any()) } returns companySubcontract
            coEvery { companyContractService.get(companySubcontract.contractId) } returns companyContract
            coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
            coEvery { memberInvoiceGroupService.getBySubcontractId(any()) } returns listOf(
                memberInvoiceGroup1,
                memberInvoiceGroup2
            )
            coEvery { invoicePaymentService.getByInvoiceGroupIds(any(), any()) } returns listOf(
                invoicePayment1, invoicePayment2, invoicePayment3
            )
            coEvery { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(invoicePayments) } returns emptyList()

            val result = getCompanyInvoicesUseCase.getBySubcontract(company.id, companySubcontract.id)

            assertEquals(expected.billingAccountableParty.id, result.billingAccountableParty.id)
            assertEquals(expected.cnpj, result.cnpj)
            assertEquals(expected.email, result.email)
            assertEquals(
                expected.memberInvoiceGroup.first().payments.first().totalAmount,
                result.memberInvoiceGroup.first().payments.first().totalAmount
            )

            coVerifyOnce { companyService.get(company.id) }
            coVerifyOnce { companySubcontractService.get(companySubcontract.id) }
            coVerifyOnce { billingAccountablePartyService.get(companyContract.billingAccountablePartyId!!) }
            coVerifyOnce { memberInvoiceGroupService.getBySubcontractId(companySubcontract.id) }
            coVerifyOnce {
                invoicePaymentService.getByInvoiceGroupIds(
                    listOf(memberInvoiceGroup1.id, memberInvoiceGroup2.id),
                    true
                )
            }
            coVerifyOnce { companyContractService.get(companySubcontract.contractId) }
            coVerifyOnce { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(any()) }
        }

    @Test
    fun `#getByCompanySubcontract - should fail if any BillingAccountParty is found`() = runBlocking {
        val product = TestModelFactory.buildProduct()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val company = TestModelFactory.buildCompany(
            availableProducts = listOf(product.id),
            defaultProductId = product.id,
            billingAccountablePartyId = billingAccountableParty.id
        )
        val companySubcontract =
            TestModelFactory.buildCompanySubContract(companyId = company.id, billingAccountablePartyId = null)
        val companyContract =
            TestModelFactory.buildCompanyContract(id = companySubcontract.contractId, billingAccountablePartyId = null)
        val memberInvoiceGroup1 =
            TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)
        val memberInvoiceGroup2 =
            TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)

        val invoicePayment1 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
        val invoicePayment2 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
        val invoicePayment3 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup2.id)


        coEvery { companyService.get(any()) } returns company
        coEvery { companySubcontractService.get(any()) } returns companySubcontract
        coEvery { companyContractService.get(companySubcontract.contractId) } returns companyContract
        coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
        coEvery { memberInvoiceGroupService.getBySubcontractId(any()) } returns listOf(
            memberInvoiceGroup1,
            memberInvoiceGroup2
        )
        coEvery { invoicePaymentService.getByInvoiceGroupIds(any(), any()) } returns listOf(
            invoicePayment1, invoicePayment2, invoicePayment3
        )

        assertFailsWith<NotFoundException> {
            getCompanyInvoicesUseCase.getBySubcontract(company.id, companySubcontract.id)
        }

        coVerifyOnce { companyService.get(company.id) }
        coVerifyOnce { companySubcontractService.get(companySubcontract.id) }
        coVerifyOnce { memberInvoiceGroupService.getBySubcontractId(companySubcontract.id) }
        coVerifyOnce {
            invoicePaymentService.getByInvoiceGroupIds(
                listOf(memberInvoiceGroup1.id, memberInvoiceGroup2.id),
                true
            )
        }
        coVerifyOnce { companyContractService.get(companySubcontract.contractId) }
        coVerifyNone { billingAccountablePartyService.get(any()) }
    }


}
