package br.com.alice.api.backoffice.controllers.featureConfig

import br.com.alice.api.backoffice.ControllerTestHelper
import br.com.alice.api.backoffice.controllers.execIndicator.HealthcareResourceController
import br.com.alice.api.backoffice.transfers.execIndicator.HealthcareResourceResponse
import br.com.alice.common.data.dsl.matchers.BFFResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.exec.indicator.client.HealthcareResourceFilters
import br.com.alice.exec.indicator.client.HealthcareResourceListWithCount
import br.com.alice.exec.indicator.client.HealthcareResourceService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthcareResourceControllerTest : ControllerTestHelper() {

    private val healthcareResourceService: HealthcareResourceService = mockk()
    private val healthcareResourceController = HealthcareResourceController(
        healthcareResourceService
    )

    private val healthcareResource = TestModelFactory.buildHealthcareResource()

    @BeforeTest
    override fun setup() {
        clearAllMocks()
        super.setup()
        module.single { healthcareResourceController }
    }

    @Test
    fun `#create should add new health specialist resource bundle`() = runBlocking {
        val expectedResponse = listOf(
            HealthcareResourceResponse(
                code = healthcareResource.code,
                description = healthcareResource.description,
                id = healthcareResource.id,
                tableType = healthcareResource.tableType
            )
        )

        coEvery { healthcareResourceService.list(
            searchTerm = "algum texto",
            range = 0..10,
            filters = HealthcareResourceFilters(
                tableType = listOf(HealthcareResourceService.DEFAULT_PRIMARY_TUSS_TABLE)
            )
        ) } returns HealthcareResourceListWithCount(
            list = listOf(healthcareResource),
            count = 1
        ).success()

        authenticatedAs(idToken, staff) {
            get("/healthcareResource/tussResources?filter={q:\"algum texto\"}") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            healthcareResourceService.list(any(), any(), any())
        }
    }
}
