package br.com.alice.api.ops.converters

import br.com.alice.api.ops.models.AddressRequest
import br.com.alice.api.ops.models.ClinicalStaffResponse
import br.com.alice.api.ops.models.ClinicalStaffWithAddresses
import br.com.alice.common.RangeUUID
import br.com.alice.common.models.Gender
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Council
import br.com.alice.data.layer.models.PhoneNumber
import br.com.alice.data.layer.models.Qualification
import br.com.alice.data.layer.models.SpecialistAppointmentType
import br.com.alice.data.layer.models.SpecialistStatus
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class ClinicalStaffResponseConverterTest {

    @Test
    fun `convert should convert ClinicalStaffWithAddresses to ClinicalStaffResponse`() {
        val healthProfessionalId = RangeUUID.generate()
        val staffId = RangeUUID.generate()
        val specialtyId = RangeUUID.generate()
        val subSpecialtyId = RangeUUID.generate()
        val providerUnitId = RangeUUID.generate()

        val address = TestModelFactory.buildStructuredAddress(
            id = RangeUUID.generate(),
            street = "Rua Teste",
            number = "123",
            complement = "Apto 45",
            neighborhood = "Bairro Teste",
            state = "SP",
            city = "São Paulo",
            zipcode = "01234-567",
            active = true,
            latitude = "-23.5503099",
            longitude = "-46.6342009"
        )

        val healthProfessional = TestModelFactory.buildHealthProfessional(
            id = healthProfessionalId,
            staffId = staffId,
            name = "Dr. Teste",
            email = "<EMAIL>",
            gender = Gender.MALE,
            specialtyId = specialtyId,
            subSpecialtyIds = listOf(subSpecialtyId),
            council = Council("12345", State.SP),
            qualifications = listOf(Qualification.ISO_9001),
            providerUnitIds = listOf(providerUnitId),
            appointmentTypes = listOf(SpecialistAppointmentType.REMOTE),
            urlSlug = "dr-teste",
            imageUrl = "https://example.com/image.jpg",
            education = listOf("Faculdade de Medicina"),
            tier = SpecialistTier.EXPERT,
        ).copy(
            phones = listOf(PhoneNumber(phone = "12345678901")),
            curiosity = "Gosta de correr",
            scheduleAvailabilityDays = 5
        )

        val source = ClinicalStaffWithAddresses(
            healthProfessional = healthProfessional,
            addresses = listOf(address)
        )

        val result = ClinicalStaffResponseConverter.convert(source)

        val expected = ClinicalStaffResponse(
            id = healthProfessionalId,
            name = "Dr. Teste",
            gender = Gender.MALE,
            specialtyId = specialtyId,
            subSpecialtyIds = listOf(subSpecialtyId),
            email = "<EMAIL>",
            councilState = "SP",
            councilNumber = "12345",
            phones = listOf(PhoneNumber(phone = "12345678901")),
            qualifications = listOf(Qualification.ISO_9001),
            imageUrl = "https://example.com/image.jpg",
            education = listOf("Faculdade de Medicina"),
            tier = SpecialistTier.EXPERT,
            providerUnitIds = listOf(providerUnitId),
            scheduleAvailabilityDays = 5,
            appointmentTypes = listOf(SpecialistAppointmentType.REMOTE),
            curiosity = "Gosta de correr",
            showOnApp = true,
            status = SpecialistStatus.ACTIVE,
            urlSlug = "dr-teste",
            addressList = listOf(
                AddressRequest(
                    id = address.id,
                    street = address.street,
                    number = address.number,
                    complement = address.complement,
                    neighborhood = address.neighborhood,
                    state = address.state,
                    city = address.city,
                    zipcode = address.zipcode,
                    label = address.label,
                    active = address.active,
                    latitude = address.latitude,
                    longitude = address.longitude
                )
            ),
            staffId = staffId
        )

        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expected)
    }

    @Test
    fun `convert should handle null addresses`() {
        val healthProfessionalId = RangeUUID.generate()
        val staffId = RangeUUID.generate()

        val healthProfessional = TestModelFactory.buildHealthProfessional(
            id = healthProfessionalId,
            staffId = staffId,
            name = "Dr. Teste",
            email = "<EMAIL>",
            gender = Gender.MALE,
            council = Council("12345", State.SP),
        )

        val source = ClinicalStaffWithAddresses(
            healthProfessional = healthProfessional,
            addresses = null
        )

        val result = ClinicalStaffResponseConverter.convert(source)

        assertThat(result.addressList).isEmpty()
    }

    @Test
    fun `convert should handle empty addresses list`() {
        val healthProfessionalId = RangeUUID.generate()
        val staffId = RangeUUID.generate()

        val healthProfessional = TestModelFactory.buildHealthProfessional(
            id = healthProfessionalId,
            staffId = staffId,
            name = "Dr. Teste",
            email = "<EMAIL>",
            gender = Gender.MALE,
            council = Council("12345", State.SP),
        )

        val source = ClinicalStaffWithAddresses(
            healthProfessional = healthProfessional,
            addresses = emptyList()
        )

        val result = ClinicalStaffResponseConverter.convert(source)

        assertThat(result.addressList).isEmpty()
    }
}
