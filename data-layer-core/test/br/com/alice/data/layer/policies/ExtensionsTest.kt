package br.com.alice.data.layer.policies

import br.com.alice.authentication.RootService
import br.com.alice.common.models.HealthInformation
import br.com.alice.data.layer.MEMBERSHIP_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.AuthorizationRequest
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CassiMember
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.FinancialDataModel
import br.com.alice.data.layer.models.MemberContract
import br.com.alice.data.layer.models.MemberModel
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.models.StaffSignTokenModel
import br.com.alice.data.layer.pipelines.subjects.PersonSubject
import br.com.alice.data.layer.pipelines.subjects.StaffSubject
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class ExtensionsTest {

    @Test
    fun `#herDependent should return false when resource is PersonModel and not her dependent`() {
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
        )

        val resource = DataLayerTestModelFactory.buildPerson()
        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herDependent(PersonModel::class)
        assertThat(result).isFalse()
    }

    @Test
    fun `#herDependent should return true when resource is PersonModel and her dependent`() {
        val resource = DataLayerTestModelFactory.buildPerson()
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
            dependentPersons = listOf(resource),
        )

        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herDependent(PersonModel::class)
        assertThat(result).isTrue()
    }

    @Test
    fun `#herDependent should return false when resource is PersonReference and not her dependent`() {
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
        )

        val resource = DataLayerTestModelFactory.buildMember()
        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herDependent(MemberModel::class)
        assertThat(result).isFalse()
    }

    @Test
    fun `#herDependent should return true when resource is PersonReference and her dependent`() {
        val dependentPerson = DataLayerTestModelFactory.buildPerson()
        val resource = DataLayerTestModelFactory.buildMember(personId = dependentPerson.id)
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
            dependentPersons = listOf(dependentPerson),
        )


        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herDependent(MemberModel::class)
        assertThat(result).isTrue()
    }

    @Test
    fun `#herDependent should return false when resource is not PersonReference`() {
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
        )

        val resource = TestModelFactory.buildCompany()
        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herDependent(Company::class)
        assertThat(result).isFalse()
    }

    @Test
    fun `#herOwn should return false when subject is not PersonSubject`() {
        val resource = DataLayerTestModelFactory.buildMember()
        val subject = DataLayerTestModelFactory.buildStaff()

        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herOwn(MemberModel::class)
        assertThat(result).isFalse()
    }

    @Test
    fun `#herOwn should return false when resource is not PersonReference nor MemberReference`() {
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
        )

        val resource = TestModelFactory.buildCompany()
        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herOwn(Company::class)
        assertThat(result).isFalse()
    }

    @Test
    fun `#herOwn should return false when resource is PersonReference but not expected sub class`() {
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
        )

        val resource = DataLayerTestModelFactory.buildMember()
        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herOwn(Company::class)
        assertThat(result).isFalse()
    }

    @Test
    fun `#herOwn should return false when resource is PersonReference but the personId does not match`() {
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
        )

        val resource = DataLayerTestModelFactory.buildMember()
        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herOwn(MemberModel::class)
        assertThat(result).isFalse()
    }

    @Test
    fun `#herOwn should return true when resource is PersonReference and the personId does match`() {
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
        )

        val resource = DataLayerTestModelFactory.buildMember(personId = subject.id)
        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herOwn(MemberModel::class)
        assertThat(result).isTrue()
    }

    @Test
    fun `#herOwn should return false when resource is MemberReference but not expected sub class`() {
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
        )

        val resource = TestModelFactory.buildCassiMember()
        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herOwn(Company::class)
        assertThat(result).isFalse()
    }

    @Test
    fun `#herOwn should return false when resource is MemberReference but subject does not have any member`() {
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
        )

        val resource = TestModelFactory.buildCassiMember()
        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herOwn(CassiMember::class)
        assertThat(result).isFalse()
    }

    @Test
    fun `#herOwn should return false when resource is MemberReference but the memberId does not match`() {
        val member = DataLayerTestModelFactory.buildMember()
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
            members = listOf(member)
        )

        val resource = TestModelFactory.buildCassiMember()
        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herOwn(CassiMember::class)
        assertThat(result).isFalse()
    }

    @Test
    fun `#herOwn should return true when resource is MemberReference and the memberId does match`() {
        val member = DataLayerTestModelFactory.buildMember()
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
            members = listOf(member)
        )

        val resource = TestModelFactory.buildCassiMember(memberId = member.id)
        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herOwn(CassiMember::class)
        assertThat(result).isTrue()
    }

    @Test
    fun `#herLegalGuardian should return true when resource is PersonModel and is subject legal guardian`() {
        val resource = DataLayerTestModelFactory.buildPerson()
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
            legalGuardianPersons = listOf(resource),
        )

        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herLegalGuardian(PersonModel::class)
        assertThat(result).isTrue
    }

    @Test
    fun `#herLegalGuardian should return false when resource is PersonModel and is not subject legal guardian`() {
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
        )

        val resource = DataLayerTestModelFactory.buildPerson()
        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herLegalGuardian(PersonModel::class)
        assertThat(result).isFalse
    }

    @Test
    fun `#herLegalGuardian should return false when resource is not PersonModel or FinancialData`() {
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
        )

        val resource = TestModelFactory.buildCompany()
        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herLegalGuardian(Company::class)
        assertThat(result).isFalse
    }

    @Test
    fun `#herLegalGuardian should return false when subject is not PersonSubject`() {
        val resource = DataLayerTestModelFactory.buildMember()
        val subject = DataLayerTestModelFactory.buildStaff()

        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herLegalGuardian(MemberModel::class)
        assertThat(result).isFalse
    }

    @Test
    fun `#herLegalGuardian should return true when resource is FinancialData and is subject legal guardian`() {
        val legalGuardian = DataLayerTestModelFactory.buildPerson()
        val resource = FinancialDataModel(
            personId = legalGuardian.id,
            bankCode = "001",
            bankName = null,
            bankAgency = "0001",
            accountNumber = "000001",
            nationalId = "***********",
            accountNickname = null,
            active = false
        )
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
            legalGuardianPersons = listOf(legalGuardian),
        )

        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herLegalGuardian(FinancialDataModel::class)
        assertThat(result).isTrue
    }

    @Test
    fun `#herOwn should return true when subject is StaffSubject and resource is her StaffReference`() {
        val staff = DataLayerTestModelFactory.buildStaff()
        val subject = StaffSubject(
            id = staff.id,
            role = staff.role,
        )
        val resource = DataLayerTestModelFactory.buildStaffSignToken(
            staffId = subject.id
        )

        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herOwn(StaffSignTokenModel::class)
        assertThat(result).isTrue
    }

    @Test
    fun `#herOwn should return true when subject is StaffSubject and resource is not StaffReference`() {
        val staff = DataLayerTestModelFactory.buildStaff()
        val subject = StaffSubject(
            id = staff.id,
            role = staff.role,
        )
        val resource = TestModelFactory.buildStaffSignToken(
            staffId = subject.id
        )

        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herOwn(HealthInformation::class)
        assertThat(result).isFalse
    }

    @Test
    fun `#herDependent should return false when resource is MemberReference and not her dependent`() {
        val person = DataLayerTestModelFactory.buildPerson()
        val member = DataLayerTestModelFactory.buildMember()
        val subject = PersonSubject(
            id = person.id,
            person = person,
            members = listOf(member),
            dependentMembers = emptyList()
        )
        val otherMember = DataLayerTestModelFactory.buildMember()

        val resource = TestModelFactory.buildMemberContract(memberId = otherMember.id)
        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herDependent(MemberContract::class)
        assertThat(result).isFalse()
    }

    @Test
    fun `#herDependent should return true when resource is MemberReference and her dependent`() {
        val dependentMember = DataLayerTestModelFactory.buildMember()
        val resource = TestModelFactory.buildMemberContract(memberId = dependentMember.id)
        val person = DataLayerTestModelFactory.buildPerson()
        val subject = PersonSubject(
            id = person.id,
            person = person,
            dependentMembers = listOf(dependentMember),
        )

        val request = AuthorizationRequest(subject, View, resource,  RootService(MEMBERSHIP_ROOT_SERVICE_NAME))

        val result = request.herDependent(MemberContract::class)
        assertThat(result).isTrue()
    }
}
