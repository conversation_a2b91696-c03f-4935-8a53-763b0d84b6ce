package br.com.alice.hr.core.ioc

import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.RunningMode
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.communication.email.EmailSender
import br.com.alice.communication.email.sender.EmailSenderClient
import br.com.alice.communication.email.sender.MockEmailClient
import br.com.alice.communication.email.sender.PinPointEmailClient
import br.com.alice.communication.email.template.EmailTemplateClient
import br.com.alice.communication.email.template.MockEmailTemplateClient
import br.com.alice.communication.email.template.PinPointEmailTemplateClient
import br.com.alice.hr.core.client.CompanyScoreMagentaService
import br.com.alice.hr.core.client.ConsolidatedHRCompanyReportService
import br.com.alice.hr.core.client.HrBeneficiaryService
import br.com.alice.hr.core.client.HrCompanyService
import br.com.alice.hr.core.client.HrCompanyStaffService
import br.com.alice.hr.core.communication.Mailer
import br.com.alice.hr.core.consumers.CalculateCompanyScoreMagentaConsumer
import br.com.alice.hr.core.consumers.CompanyReportConsumer
import br.com.alice.hr.core.consumers.NewCompanyStaffConsumer
import br.com.alice.hr.core.services.CompanyScoreMagentaServiceImpl
import br.com.alice.hr.core.services.ConsolidatedHRCompanyReportServiceImpl
import br.com.alice.hr.core.services.HrBeneficiaryServiceImpl
import br.com.alice.hr.core.services.HrCompanyServiceImpl
import br.com.alice.hr.core.services.HrCompanyStaffServiceImpl
import br.com.alice.hr.core.services.internal.FrequencyCoordinationDataService
import br.com.alice.hr.core.services.internal.PopulationDataService
import br.com.alice.hr.core.services.useCases.AddOrReactivateCompanyStaffUseCase
import br.com.alice.hr.core.services.useCases.CalculateCompanyScoreMagentaUseCase
import br.com.alice.hr.core.services.useCases.CreateBeneficiaryDependentUseCase
import br.com.alice.hr.core.services.useCases.GetBeneficiaryRelationTypeOptionsUseCase
import br.com.alice.hr.core.services.useCases.ListActiveCompanyStaffsUseCase
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig
import org.koin.dsl.module
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.pinpoint.PinpointClient

private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

val ServiceModule = module(createdAtStart = true) {
    // Configuration
    single { config }

    // Email clients
    when (BaseConfig.instance.runningMode) {
        RunningMode.PRODUCTION -> {
            single<EmailTemplateClient> { PinPointEmailTemplateClient(get()) }
            single<EmailSenderClient> { PinPointEmailClient(get()) }
        }
        else -> {
            single<EmailTemplateClient> { MockEmailTemplateClient }
            single<EmailSenderClient> { MockEmailClient }
        }
    }
    single<PinpointClient> { PinpointClient.builder().region(Region.US_EAST_1).build() }

    single { EmailSender(get(), get()) }

    // Services
    loadServiceServers("br.com.alice.hr.core.services")

    // Exposed services
    single<ConsolidatedHRCompanyReportService> { ConsolidatedHRCompanyReportServiceImpl(get()) }
    single<CompanyScoreMagentaService> { CompanyScoreMagentaServiceImpl(get()) }
    single<HrCompanyStaffService> { HrCompanyStaffServiceImpl(get(), get(), get(), get(), get()) }
    single<HrCompanyService> { HrCompanyServiceImpl(get(), get()) }
    single<HrBeneficiaryService> { HrBeneficiaryServiceImpl(get(), get(), get(), get(), get(), get()) }

    // internal services
    single<PopulationDataService> { PopulationDataService(get()) }
    single<FrequencyCoordinationDataService> { FrequencyCoordinationDataService(get()) }
    single<CalculateCompanyScoreMagentaUseCase> { CalculateCompanyScoreMagentaUseCase(get(), get(), get()) }
    single<AddOrReactivateCompanyStaffUseCase> { AddOrReactivateCompanyStaffUseCase(get()) }
    single<ListActiveCompanyStaffsUseCase> { ListActiveCompanyStaffsUseCase(get()) }
    single<GetBeneficiaryRelationTypeOptionsUseCase> { GetBeneficiaryRelationTypeOptionsUseCase(get()) }
    single<CreateBeneficiaryDependentUseCase> { CreateBeneficiaryDependentUseCase(get()) }
    single { Mailer(get()) }

    // Consumers
    single { CompanyReportConsumer(get(), get(), get()) }
    single { CalculateCompanyScoreMagentaConsumer(get(), get()) }
    single { NewCompanyStaffConsumer(get()) }
}
