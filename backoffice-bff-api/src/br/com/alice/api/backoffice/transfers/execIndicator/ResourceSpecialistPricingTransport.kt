package br.com.alice.api.backoffice.transfers.execIndicator

import br.com.alice.api.backoffice.transfers.FriendlyEnumResponse
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdateStatus
import java.util.UUID

data class DownloadCSVRequest(
    val resourceBundleSpecialtyIds: List<UUID> = emptyList()
)

data class EffectiveDatesResponse(
    val dates: List<String> = emptyList()
)

data class ResourceSpecialistPricingIndexResponse(
    val id: UUID,
    val filename: String,
    val downloadFileUrl: String?,
    val downloadFailedLinesFileUrl: String?,
    val createdAt: String,
    val createdBy: String,
    val status: FriendlyEnumResponse<ResourceBundleSpecialtyPricingUpdateStatus>,
    val processingDetails: ResourceSpecialistPricingProcessingDetailsResponse
)

data class ResourceSpecialistPricingProcessingDetailsResponse(
    val totalItems: Int,
    val errorItems: Int,
    val friendlyDescription: String
)
