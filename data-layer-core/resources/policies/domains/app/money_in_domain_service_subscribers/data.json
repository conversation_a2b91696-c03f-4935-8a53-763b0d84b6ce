{"rules": [{"conditions": [], "allow": [{"resources": ["MemberInvoiceModel", "InvoicePaymentModel", "ResourceSignTokenModel", "ItauPaymentModel", "CancelPaymentOnAcquirerScheduleModel", "PaymentDetailModel", "InvoiceItemModel", "MemberInvoiceGroupModel", "PreActivationPaymentModel", "MemberModel", "PersonModel", "ProductModel", "BillingAccountablePartyModel", "InvoiceLiquidationModel", "ProductBundleModel", "PriceListingModel", "ProductPriceListingModel", "PersonBillingAccountablePartyModel", "EmailCommunicationModel", "BolepixPaymentDetailModel", "PixPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "BoletoPaymentDetailModel"], "actions": ["view", "update", "create"]}, {"resources": ["MemberLightweightModel", "MemberProductPriceModel", "PersonPreferencesModel", "ProductPriceAdjustmentModel", "MemberProductPriceAdjustmentModel"], "actions": ["view"]}, {"resources": ["BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "CassiMemberModel", "Beneficiary<PERSON><PERSON>l"], "actions": ["view", "count"]}, {"resources": ["CompanyModel"], "actions": ["view", "count", "create", "update"]}, {"resources": ["TrackPersonABModel"], "actions": ["view", "create"]}]}]}