package br.com.alice.itau.services

import br.com.alice.common.PaymentMethod
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.itau.client.AcquirerItauPaymentService
import br.com.alice.itau.clients.ItauBolecodeClient
import br.com.alice.itau.clients.ItauBoletoClient
import br.com.alice.itau.clients.ItauPixClient
import br.com.alice.itau.converter.toAcquirerCreatePaymentResponse
import br.com.alice.itau.converter.toAcquirerGetPaymentResponse
import br.com.alice.itau.converter.toItauBolecodeCreateRequest
import br.com.alice.itau.converter.toItauBoletoCreateRequest
import br.com.alice.itau.converter.toItauPixCreateRequest
import br.com.alice.itau.converter.toOurNumberString
import br.com.alice.itau.services.internal.ItauPaymentService
import br.com.alice.moneyin.models.AcquirerCreatePaymentResponse
import br.com.alice.moneyin.models.AcquirerGetPaymentResponse
import br.com.alice.moneyin.models.PaymentRequestInput
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.map

class AcquirerItauPaymentServiceImpl(
    private val itauPixClient: ItauPixClient,
    private val itauBoletoClient: ItauBoletoClient,
    private val itauBolecodeClient: ItauBolecodeClient,
    private val itauPaymentService: ItauPaymentService,
) : AcquirerItauPaymentService {

    override suspend fun create(request: PaymentRequestInput): Result<AcquirerCreatePaymentResponse, Throwable> {
        val itauPayment = itauPaymentService.createOrGetByPaymentId(request.orderId).get()

        return when (request.method) {
            PaymentMethod.PIX -> itauPixClient.create(request.toItauPixCreateRequest())
                .map { it.toAcquirerCreatePaymentResponse(itauPayment) }

            PaymentMethod.BOLETO -> itauBoletoClient.create(
                request.toItauBoletoCreateRequest(
                    itauPayment
                )
            )
                .map { it.toAcquirerCreatePaymentResponse(itauPayment) }

            PaymentMethod.BOLEPIX -> itauBolecodeClient.create(
                request.toItauBolecodeCreateRequest(
                    itauPayment
                )
            )
                .map { it.data.toAcquirerCreatePaymentResponse(itauPayment) }

            PaymentMethod.SIMPLE_CREDIT_CARD -> InvalidArgumentException("Simple credit card is not supported").failure()
        }.andThen { (_, externalIds) ->
            itauPaymentService.update(
                itauPayment.copy(
                    pixId = externalIds.pixId,
                    boletoId = externalIds.boletoId,
                )
            )
        }.map { (acquirerResponse, _) -> acquirerResponse }
            .thenError {
                logger.info(
                    "Failed to create payment for orderId: ${request.orderId} with method: ${request.method}",
                    "exception" to it,
                )
            }
    }

    override suspend fun get(externalId: String): Result<AcquirerGetPaymentResponse, Throwable> {
        val itauPayment = itauPaymentService.get(externalId.toUUID()).get()
        if (itauPayment.boletoId != null) {
            return itauBoletoClient.get(itauPayment.ourNumber!!.toOurNumberString())
                .map { it.toAcquirerGetPaymentResponse() }
        }
        if (itauPayment.pixId != null) {
            return itauPixClient.get(itauPayment.pixId!!)
                .map { it.toAcquirerGetPaymentResponse() }
        }
        return NotFoundException("Payment not found").failure()
    }

    override suspend fun cancel(externalId: String): Result<Boolean, Throwable> {
        val itauPayment = itauPaymentService.get(externalId.toUUID()).get()
        if (itauPayment.boletoId != null) {
            return itauBoletoClient.cancel(itauPayment.ourNumber!!.toOurNumberString())
        }
        if (itauPayment.pixId != null) {
            return itauPixClient.cancel(itauPayment.pixId!!)
        }
        return NotFoundException("Payment not found").failure()
    }

}
