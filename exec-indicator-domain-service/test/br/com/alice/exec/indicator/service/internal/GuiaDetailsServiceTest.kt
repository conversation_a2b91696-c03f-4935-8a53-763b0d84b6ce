package br.com.alice.exec.indicator.service.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.GlossAuthorizationInfoData
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.data.layer.models.TotvsGuiaStatus
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.HospitalizationInfoService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.models.ChemotherapyData
import br.com.alice.exec.indicator.models.GenericGloss
import br.com.alice.exec.indicator.models.GuiaDetailsResponse
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.HospitalizationData
import br.com.alice.exec.indicator.models.OpmeData
import br.com.alice.exec.indicator.models.RadiotherapyData
import br.com.alice.exec.indicator.models.TotvsGuiaStatusResponse
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.VaultResponse
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GuiaDetailsServiceTest {

    private val healthcareResourceService: HealthcareResourceService = mockk()
    private val attachmentOpmeService: AttachmentOpmeService = mockk()
    private val hospitalizationInfoService: HospitalizationInfoService = mockk()
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService = mockk()
    private val attachmentChemotherapyService: AttachmentChemotherapyService = mockk()
    private val fileVaultActionService: FileVaultActionService = mockk()
    private val attachmentRadiotherapyService: AttachmentRadiotherapyService = mockk()

    private val service = GuiaDetailsService(
        healthcareResourceService,
        attachmentOpmeService,
        hospitalizationInfoService,
        mvAuthorizedProcedureService,
        attachmentChemotherapyService,
        fileVaultActionService,
        attachmentRadiotherapyService,
    )

    private val referenceTotvsGuia = TestModelFactory.buildTotvsGuia(
        medicalRequestFileIds = listOf(RangeUUID.generate()),
        status = TotvsGuiaStatus.UNAUTHORIZED
    )

    private val totvsGuia = TestModelFactory.buildTotvsGuia(
        medicalRequestFileIds = listOf(RangeUUID.generate()),
        referenceTotvsGuiaId = referenceTotvsGuia.id
    )

    private val guiaDetailsResponse = GuiaDetailsResponse(
        externalCode = totvsGuia.externalCode ?: totvsGuia.code,
        passcode = totvsGuia.passcode,
        personId = totvsGuia.personId,
        type = totvsGuia.type,
        status = TotvsGuiaStatusResponse.PROCESSING,
        requester = totvsGuia.requestedByProfessional,
        newBorn = totvsGuia.newBorn,
        files = emptyList(),
        hospitalizationData = null,
        opmeData = null,
        chemotherapyData = null,
        procedures = emptyList(),
        opmes = emptyList(),
        requestedDrugs = emptyList()
    )

    private val vaultResponses = listOf(
        VaultResponse(
            url = "url",
            id = totvsGuia.medicalRequestFileIds[0],
            type = "type",
            vaultUrl = "vaultUrl",
            fileName = "filename"
        ),
        VaultResponse(
            url = "url",
            id = referenceTotvsGuia.medicalRequestFileIds[0],
            type = "type",
            vaultUrl = "vaultUrl",
            fileName = "filename"
        )
    )

    @AfterTest
    fun clear() = clearAllMocks()

    @Test
    fun `#getOpmeData should return guia details successfully`() = runBlocking {
        val attachment = TestModelFactory.buildAttachmentOpme()

        val opmeData = OpmeData(
            technicalJustification = attachment.technicalJustification,
            materialSpecification = attachment.materialSpecification
        )

        coEvery {
            attachmentOpmeService.getByTotvsGuiaId(totvsGuia.id)
        } returns attachment.success()

        coEvery {
            fileVaultActionService.securedLinks(totvsGuia.medicalRequestFileIds)
        } returns vaultResponses.success()

        val result = service.getOpmeData(totvsGuia)
        ResultAssert.assertThat(result).isSuccessWithData(
            guiaDetailsResponse.copy(
                opmeData = opmeData,
                files = vaultResponses,
                opmes = attachment.requestedOpmes
            )
        )

        coVerifyOnce {
            attachmentOpmeService.getByTotvsGuiaId(totvsGuia.id)
            fileVaultActionService.securedLinks(totvsGuia.medicalRequestFileIds)
        }

    }

    @Test
    fun `#getChemotherapyData should return guia details successfully`() = runBlocking {
        val requestedDrugs = listOf(
            TestModelFactory.buildAttachmentChemotherapy().requestedDrugs[0].copy(
                gloss = listOf(GlossAuthorizationInfoData(code = "code", title = "title", description = "description"))
            )
        )
        val attachment = TestModelFactory.buildAttachmentChemotherapy(requestedDrugs = requestedDrugs)

        val mvAuthorizedProcedures = listOf(
            TestModelFactory.buildMvAuthorizedProcedure(
                totvsGuiaId = referenceTotvsGuia.id,
                gloss = listOf(
                    GlossAuthorizationInfoData(
                        code = "code",
                        description = "description",
                        title = "title",
                    )
                ),
                status = MvAuthorizedProcedureStatus.UNAUTHORIZED
            )
        )
        val healthcareResource = listOf(
            TestModelFactory.buildHealthcareResource(
                code = mvAuthorizedProcedures[0].procedureId!!
            )
        )

        coEvery {
            mvAuthorizedProcedureService.findByTotvsGuiaIds(
                listOf(totvsGuia.id, referenceTotvsGuia.id)
            )
        } returns mvAuthorizedProcedures.success()

        coEvery {
            healthcareResourceService.findByCodes(mvAuthorizedProcedures.mapNotNull { it.procedureId }, false)
        } returns healthcareResource.success()

        coEvery {
            attachmentChemotherapyService.getByTotvsGuiaId(totvsGuia.id)
        } returns attachment.success()

        coEvery {
            fileVaultActionService.securedLinks(
                totvsGuia.medicalRequestFileIds.plus(referenceTotvsGuia.medicalRequestFileIds)
            )
        } returns vaultResponses.success()

        val chemotherapyData = ChemotherapyData(
            cyclesQuantity = attachment.cyclesQuantity,
            currentCycle = attachment.currentCycle,
            currentCycleDays = attachment.currentCycleDays,
            cyclesInterval = attachment.cyclesInterval,
            oncologicalDiagnosis = attachment.chemotherapyOncologicalDiagnosis
        )

        val gloss = listOf(
            GenericGloss(
                code = mvAuthorizedProcedures[0].gloss!![0].code,
                title = mvAuthorizedProcedures[0].gloss!![0].title,
                description = mvAuthorizedProcedures[0].gloss!![0].description,
                procedureId = mvAuthorizedProcedures[0].procedureId!!
            ),
            GenericGloss(
                code = requestedDrugs[0].gloss[0].code,
                title = requestedDrugs[0].gloss[0].title,
                description = requestedDrugs[0].gloss[0].description,
                procedureId = requestedDrugs[0].drugsIdentification.code
            )
        )

        val guiaProcedures = listOf(
            GuiaProcedure(
                code = mvAuthorizedProcedures[0].procedureId!!,
                description = healthcareResource[0].description,
                quantity = mvAuthorizedProcedures[0].quantity,
                table = healthcareResource[0].tableType!!,
                status = mvAuthorizedProcedures[0].status
            )
        )

        val result = service.getChemotherapyData(totvsGuia, referenceTotvsGuia)
        ResultAssert.assertThat(result).isSuccessWithData(
            guiaDetailsResponse.copy(
                externalCode = referenceTotvsGuia.externalCode ?: referenceTotvsGuia.code,
                passcode = referenceTotvsGuia.passcode,
                personId = referenceTotvsGuia.personId,
                type = referenceTotvsGuia.type,
                status = TotvsGuiaStatusResponse.UNAUTHORIZED,
                requester = referenceTotvsGuia.requestedByProfessional,
                newBorn = referenceTotvsGuia.newBorn,
                chemotherapyData = chemotherapyData,
                files = vaultResponses,
                requestedDrugs = attachment.requestedDrugs,
                gloss = gloss,
                procedures = guiaProcedures
            )
        )

        coVerifyOnce {
            attachmentChemotherapyService.getByTotvsGuiaId(totvsGuia.id)
            healthcareResourceService.findByCodes(mvAuthorizedProcedures.mapNotNull { it.procedureId }, false)

            mvAuthorizedProcedureService.findByTotvsGuiaIds(
                listOf(totvsGuia.id, referenceTotvsGuia.id)
            )

            fileVaultActionService.securedLinks(
                totvsGuia.medicalRequestFileIds.plus(referenceTotvsGuia.medicalRequestFileIds)
            )
        }

    }

    @Test
    fun `#getHospitalizationDataAndProcedure should return guia details successfull with empty gloss because procedure is not unauthorized or pending`() = runBlocking {
        val hospitalizationInfo = TestModelFactory.buildHospitalizationInfo()
        val mvAuthorizedProcedures = listOf(
            TestModelFactory.buildMvAuthorizedProcedure(
                gloss = listOf(
                    GlossAuthorizationInfoData(
                        code = "code",
                        description = "description",
                        title = "title"
                    )
                )
            )
        )
        val healthcareResource = listOf(
            TestModelFactory.buildHealthcareResource(
                code = mvAuthorizedProcedures[0].procedureId!!
            )
        )

        coEvery {
            hospitalizationInfoService.getByTotvsGuiaId(totvsGuia.id)
        } returns hospitalizationInfo.success()

        coEvery {
            mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuia.id)
        } returns mvAuthorizedProcedures.success()

        coEvery {
            healthcareResourceService.findByCodes(mvAuthorizedProcedures.mapNotNull { it.procedureId }, false)
        } returns healthcareResource.success()

        coEvery {
            fileVaultActionService.securedLinks(totvsGuia.medicalRequestFileIds)
        } returns vaultResponses.success()

        val hospitalizationData = HospitalizationData(
            attendanceCharacter = hospitalizationInfo.attendanceCharacter,
            accidentIndication = hospitalizationInfo.accidentIndication,
            type = hospitalizationInfo.type,
            numberOfDays = hospitalizationInfo.numberOfDays,
            suggestedDate = hospitalizationInfo.suggestedDate,
            suggestedDischargeDate = hospitalizationInfo.suggestedDischargeDate,
            chemotherapyIndication = hospitalizationInfo.chemotherapyIndication,
            opmeIndication = hospitalizationInfo.opmeIndication,
            clinicalIndication = hospitalizationInfo.clinicalIndication,
            healthCondition = hospitalizationInfo.healthCondition
        )

        val guiaProcedures = listOf(
            GuiaProcedure(
                code = mvAuthorizedProcedures[0].procedureId!!,
                description = healthcareResource[0].description,
                quantity = mvAuthorizedProcedures[0].quantity,
                table = healthcareResource[0].tableType!!,
                status = mvAuthorizedProcedures[0].status
            )
        )

        val gloss = emptyList<GenericGloss>()

        val result = service.getHospitalizationDataAndProcedure(totvsGuia)
        ResultAssert.assertThat(result).isSuccessWithData(
            guiaDetailsResponse.copy(
                hospitalizationData = hospitalizationData,
                files = vaultResponses,
                procedures = guiaProcedures,
                gloss = gloss
            )
        )

        coVerifyOnce {
            hospitalizationInfoService.getByTotvsGuiaId(totvsGuia.id)
            healthcareResourceService.findByCodes(mvAuthorizedProcedures.mapNotNull { it.procedureId }, false)
            mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuia.id)
            fileVaultActionService.securedLinks(totvsGuia.medicalRequestFileIds)
        }
    }

    @Test
    fun `#getHospitalizationDataAndProcedure should return guia details successfull with gloss because procedure is unauthorized and totvs guia is unauthorized`() = runBlocking {
        val hospitalizationInfo = TestModelFactory.buildHospitalizationInfo()
        val mvAuthorizedProcedures = listOf(
            TestModelFactory.buildMvAuthorizedProcedure(
                gloss = listOf(
                    GlossAuthorizationInfoData(
                        code = "code",
                        description = "description",
                        title = "title",
                        observation = "observacao"
                    )
                ),
                status = MvAuthorizedProcedureStatus.UNAUTHORIZED
            )
        )
        val healthcareResource = listOf(
            TestModelFactory.buildHealthcareResource(
                code = mvAuthorizedProcedures[0].procedureId!!
            )
        )

        coEvery {
            hospitalizationInfoService.getByTotvsGuiaId(totvsGuia.id)
        } returns hospitalizationInfo.success()

        coEvery {
            mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuia.id)
        } returns mvAuthorizedProcedures.success()

        coEvery {
            healthcareResourceService.findByCodes(mvAuthorizedProcedures.mapNotNull { it.procedureId }, false)
        } returns healthcareResource.success()

        coEvery {
            fileVaultActionService.securedLinks(totvsGuia.medicalRequestFileIds)
        } returns vaultResponses.success()

        val hospitalizationData = HospitalizationData(
            attendanceCharacter = hospitalizationInfo.attendanceCharacter,
            accidentIndication = hospitalizationInfo.accidentIndication,
            type = hospitalizationInfo.type,
            numberOfDays = hospitalizationInfo.numberOfDays,
            suggestedDate = hospitalizationInfo.suggestedDate,
            suggestedDischargeDate = hospitalizationInfo.suggestedDischargeDate,
            chemotherapyIndication = hospitalizationInfo.chemotherapyIndication,
            opmeIndication = hospitalizationInfo.opmeIndication,
            clinicalIndication = hospitalizationInfo.clinicalIndication,
            healthCondition = hospitalizationInfo.healthCondition
        )

        val guiaProcedures = listOf(
            GuiaProcedure(
                code = mvAuthorizedProcedures[0].procedureId!!,
                description = healthcareResource[0].description,
                quantity = mvAuthorizedProcedures[0].quantity,
                table = healthcareResource[0].tableType!!,
                status = mvAuthorizedProcedures[0].status
            )
        )

        val gloss = listOf(
            GenericGloss(
                code = mvAuthorizedProcedures[0].gloss!![0].code,
                title = mvAuthorizedProcedures[0].gloss!![0].title,
                description = mvAuthorizedProcedures[0].gloss!![0].description,
                procedureId = mvAuthorizedProcedures[0].procedureId!!,
                observation = mvAuthorizedProcedures[0].gloss!![0].observation
            )
        )

        val result = service.getHospitalizationDataAndProcedure(totvsGuia.copy(status = TotvsGuiaStatus.UNAUTHORIZED))
        ResultAssert.assertThat(result).isSuccessWithData(
            guiaDetailsResponse.copy(
                hospitalizationData = hospitalizationData,
                files = vaultResponses,
                procedures = guiaProcedures,
                gloss = gloss,
                status = TotvsGuiaStatusResponse.UNAUTHORIZED
            )
        )

        coVerifyOnce {
            hospitalizationInfoService.getByTotvsGuiaId(totvsGuia.id)
            healthcareResourceService.findByCodes(mvAuthorizedProcedures.mapNotNull { it.procedureId }, false)
            mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuia.id)
            fileVaultActionService.securedLinks(totvsGuia.medicalRequestFileIds)
        }
    }

    @Test
    fun `#getHospitalizationDataAndProcedure should return guia details successfull with gloss because procedure is pending`() = runBlocking {
        val hospitalizationInfo = TestModelFactory.buildHospitalizationInfo()
        val mvAuthorizedProcedures = listOf(
            TestModelFactory.buildMvAuthorizedProcedure(
                gloss = listOf(
                    GlossAuthorizationInfoData(
                        code = "code",
                        description = "description",
                        title = "title",
                        observation = "observacao"
                    )
                ),
                status = MvAuthorizedProcedureStatus.PENDING
            )
        )
        val healthcareResource = listOf(
            TestModelFactory.buildHealthcareResource(
                code = mvAuthorizedProcedures[0].procedureId!!
            )
        )

        coEvery {
            hospitalizationInfoService.getByTotvsGuiaId(totvsGuia.id)
        } returns hospitalizationInfo.success()

        coEvery {
            mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuia.id)
        } returns mvAuthorizedProcedures.success()

        coEvery {
            healthcareResourceService.findByCodes(mvAuthorizedProcedures.mapNotNull { it.procedureId }, false)
        } returns healthcareResource.success()

        coEvery {
            fileVaultActionService.securedLinks(totvsGuia.medicalRequestFileIds)
        } returns vaultResponses.success()

        val hospitalizationData = HospitalizationData(
            attendanceCharacter = hospitalizationInfo.attendanceCharacter,
            accidentIndication = hospitalizationInfo.accidentIndication,
            type = hospitalizationInfo.type,
            numberOfDays = hospitalizationInfo.numberOfDays,
            suggestedDate = hospitalizationInfo.suggestedDate,
            suggestedDischargeDate = hospitalizationInfo.suggestedDischargeDate,
            chemotherapyIndication = hospitalizationInfo.chemotherapyIndication,
            opmeIndication = hospitalizationInfo.opmeIndication,
            clinicalIndication = hospitalizationInfo.clinicalIndication,
            healthCondition = hospitalizationInfo.healthCondition
        )

        val guiaProcedures = listOf(
            GuiaProcedure(
                code = mvAuthorizedProcedures[0].procedureId!!,
                description = healthcareResource[0].description,
                quantity = mvAuthorizedProcedures[0].quantity,
                table = healthcareResource[0].tableType!!,
                status = mvAuthorizedProcedures[0].status
            )
        )

        val gloss = listOf(
            GenericGloss(
                code = mvAuthorizedProcedures[0].gloss!![0].code,
                title = mvAuthorizedProcedures[0].gloss!![0].title,
                description = mvAuthorizedProcedures[0].gloss!![0].description,
                procedureId = mvAuthorizedProcedures[0].procedureId!!,
                observation = mvAuthorizedProcedures[0].gloss!![0].observation
            )
        )

        val result = service.getHospitalizationDataAndProcedure(totvsGuia.copy(status = TotvsGuiaStatus.UNAUTHORIZED))
        ResultAssert.assertThat(result).isSuccessWithData(
            guiaDetailsResponse.copy(
                hospitalizationData = hospitalizationData,
                files = vaultResponses,
                procedures = guiaProcedures,
                gloss = gloss,
                status = TotvsGuiaStatusResponse.UNAUTHORIZED
            )
        )

        coVerifyOnce {
            hospitalizationInfoService.getByTotvsGuiaId(totvsGuia.id)
            healthcareResourceService.findByCodes(mvAuthorizedProcedures.mapNotNull { it.procedureId }, false)
            mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuia.id)
            fileVaultActionService.securedLinks(totvsGuia.medicalRequestFileIds)
        }
    }

    @Test
    fun `#getRadiotherapyData should return guia details successfully`() = runBlocking {
        val attachment = TestModelFactory.buildAttachmentRadiotherapy()

        val mvAuthorizedProcedures = listOf(
            TestModelFactory.buildMvAuthorizedProcedure(
                totvsGuiaId = referenceTotvsGuia.id,
                gloss = listOf(
                    GlossAuthorizationInfoData(
                        code = "code",
                        description = "description",
                        title = "title"
                    )
                ),
                status = MvAuthorizedProcedureStatus.UNAUTHORIZED
            )
        )
        val healthcareResource = listOf(
            TestModelFactory.buildHealthcareResource(
                code = mvAuthorizedProcedures[0].procedureId!!
            )
        )

        coEvery {
            mvAuthorizedProcedureService.findByTotvsGuiaIds(listOf(totvsGuia.id, referenceTotvsGuia.id))
        } returns mvAuthorizedProcedures.success()

        coEvery {
            healthcareResourceService.findByCodes(mvAuthorizedProcedures.mapNotNull { it.procedureId }, false)
        } returns healthcareResource.success()

        coEvery { attachmentRadiotherapyService.getByTotvsGuiaId(totvsGuia.id) } returns attachment.success()

        coEvery {
            fileVaultActionService.securedLinks(
                totvsGuia.medicalRequestFileIds.plus(referenceTotvsGuia.medicalRequestFileIds)
            )
        } returns vaultResponses.success()

        val radiotherapyData = RadiotherapyData(
            oncologicalDiagnosis = attachment.oncologicalDiagnosisRadio,
            numberOfFields = attachment.fieldsQuantity,
            dailyDoses = attachment.fieldDose,
            gy = attachment.totalDose,
            numberOfDays = attachment.daysQuantity,
            estimatedStartAdministration = attachment.expectedStartDate,
        )

        val gloss = listOf(
            GenericGloss(
                code = mvAuthorizedProcedures[0].gloss!![0].code,
                title = mvAuthorizedProcedures[0].gloss!![0].title,
                description = mvAuthorizedProcedures[0].gloss!![0].description,
                procedureId = mvAuthorizedProcedures[0].procedureId!!
            )
        )

        val guiaProcedures = listOf(
            GuiaProcedure(
                code = mvAuthorizedProcedures[0].procedureId!!,
                description = healthcareResource[0].description,
                quantity = mvAuthorizedProcedures[0].quantity,
                table = healthcareResource[0].tableType!!,
                status = mvAuthorizedProcedures[0].status
            )
        )

        val result = service.getRadiotherapyData(totvsGuia, referenceTotvsGuia)
        ResultAssert.assertThat(result).isSuccessWithData(
            guiaDetailsResponse.copy(
                externalCode = referenceTotvsGuia.externalCode ?: referenceTotvsGuia.code,
                passcode = referenceTotvsGuia.passcode,
                personId = referenceTotvsGuia.personId,
                type = referenceTotvsGuia.type,
                status = TotvsGuiaStatusResponse.UNAUTHORIZED,
                requester = referenceTotvsGuia.requestedByProfessional,
                newBorn = referenceTotvsGuia.newBorn,
                radiotherapyData = radiotherapyData,
                files = vaultResponses,
                gloss = gloss,
                procedures = guiaProcedures
            )
        )

        coVerifyOnce {
            attachmentRadiotherapyService.getByTotvsGuiaId(totvsGuia.id)
            healthcareResourceService.findByCodes(mvAuthorizedProcedures.mapNotNull { it.procedureId }, false)

            mvAuthorizedProcedureService.findByTotvsGuiaIds(
                listOf(totvsGuia.id, referenceTotvsGuia.id)
            )

            fileVaultActionService.securedLinks(
                totvsGuia.medicalRequestFileIds.plus(referenceTotvsGuia.medicalRequestFileIds)
            )
        }

    }
}
