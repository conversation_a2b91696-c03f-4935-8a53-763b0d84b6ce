package br.com.alice.schedule.ioc

import br.com.alice.appointment.ioc.AppointmentDomainClientModule
import br.com.alice.business.ioc.BusinessDomainClientModule
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.RunningMode
import br.com.alice.common.ioc.NotificationModule
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.redis.CacheFactory
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotClient
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotClientImpl
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotClientLocal
import br.com.alice.communication.email.EmailSender
import br.com.alice.communication.email.sender.EmailSenderClient
import br.com.alice.communication.email.sender.MockEmailClient
import br.com.alice.communication.email.sender.SimpleEmailServiceClient
import br.com.alice.communication.email.template.EmailTemplateClient
import br.com.alice.communication.email.template.MockEmailTemplateClient
import br.com.alice.communication.email.template.SimpleEmailServiceTemplateClient
import br.com.alice.communication.ioc.CommunicationModule
import br.com.alice.communication.treble_whatsapp.TrebleWhatsAppClient
import br.com.alice.data.layer.services.*
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.healthcondition.ioc.HealthConditionDomainClientModule
import br.com.alice.healthlogic.ioc.HealthLogicDomainClientModule
import br.com.alice.healthplan.ioc.HealthPlanDomainClientModule
import br.com.alice.marauders.map.ioc.MaraudersMapDomainClientModule
import br.com.alice.membership.ioc.MembershipClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.provider.ioc.ProviderDomainClientModule
import br.com.alice.schedule.SERVICE_NAME
import br.com.alice.schedule.ServiceConfig
import br.com.alice.schedule.client.AppointmentScheduleCheckInService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeDateExceptionService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.AppointmentScheduleOptionService
import br.com.alice.schedule.client.AppointmentSchedulePreTriageService
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.client.EventTypeProviderUnitService
import br.com.alice.schedule.client.ExternalAppointmentScheduleService
import br.com.alice.schedule.client.ExternalCalendarEventService
import br.com.alice.schedule.client.ExternalCalendarRecurrentEventService
import br.com.alice.schedule.client.GoogleCalendarEventService
import br.com.alice.schedule.client.HealthConditionSchedulingService
import br.com.alice.schedule.client.LivanceService
import br.com.alice.schedule.client.PersonCalendlyService
import br.com.alice.schedule.client.ReferralSchedulingService
import br.com.alice.schedule.client.SchedulePreferenceService
import br.com.alice.schedule.client.StaffAvailabilityService
import br.com.alice.schedule.client.StaffSchedulePreferenceService
import br.com.alice.schedule.client.StaffScheduleService
import br.com.alice.schedule.communication.Mailer
import br.com.alice.schedule.consumers.*
import br.com.alice.schedule.controllers.BackfillController
import br.com.alice.schedule.controllers.GoogleCalendarController
import br.com.alice.schedule.controllers.InternalFeaturesController
import br.com.alice.schedule.controllers.RecurringController
import br.com.alice.schedule.integrations.calendly.CalendlyClient
import br.com.alice.schedule.integrations.calendly.CalendlyConfiguration
import br.com.alice.schedule.integrations.googlecalendar.GoogleCalendarApi
import br.com.alice.schedule.integrations.livance.LivanceClient
import br.com.alice.schedule.integrations.zoom.ZoomClient
import br.com.alice.schedule.services.AppointmentScheduleCheckInServiceImpl
import br.com.alice.schedule.services.AppointmentScheduleEventTypeDateExceptionServiceImpl
import br.com.alice.schedule.services.AppointmentScheduleEventTypeServiceImpl
import br.com.alice.schedule.services.AppointmentScheduleOptionServiceImpl
import br.com.alice.schedule.services.AppointmentSchedulePreTriageServiceImpl
import br.com.alice.schedule.services.AppointmentScheduleServiceImpl
import br.com.alice.schedule.services.EventTypeProviderUnitServiceImpl
import br.com.alice.schedule.services.ExternalAppointmentScheduleServiceImpl
import br.com.alice.schedule.services.ExternalCalendarEventServiceImpl
import br.com.alice.schedule.services.ExternalCalendarRecurrentEventServiceImpl
import br.com.alice.schedule.services.GoogleCalendarEventServiceImpl
import br.com.alice.schedule.services.HealthConditionSchedulingServiceImpl
import br.com.alice.schedule.services.LivanceServiceImpl
import br.com.alice.schedule.services.PersonCalendlyServiceImpl
import br.com.alice.schedule.services.ReferralSchedulingServiceImpl
import br.com.alice.schedule.services.SchedulePreferenceServiceImpl
import br.com.alice.schedule.services.StaffAvailabilityServiceImpl
import br.com.alice.schedule.services.StaffSchedulePreferenceServiceImpl
import br.com.alice.schedule.services.StaffScheduleServiceImpl
import br.com.alice.schedule.services.internal.AppointmentReminderService
import br.com.alice.schedule.services.internal.AppointmentScheduledNotifier
import br.com.alice.schedule.services.internal.ExternalAppointmentScheduleInternalService
import br.com.alice.schedule.services.internal.HealthResourcesStore
import br.com.alice.schedule.services.internal.IcalGenerator
import br.com.alice.schedule.services.internal.ImmersionAppointmentScheduleService
import br.com.alice.schedule.services.internal.MailerServiceImpl
import br.com.alice.schedule.services.internal.TrebleWhatsAppService
import br.com.alice.schedule.services.internal.ZoomService
import br.com.alice.staff.ioc.StaffDomainClientModule
import io.ktor.client.HttpClient
import io.ktor.client.engine.apache.Apache
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import org.koin.dsl.module
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.ses.SesClient

val ScheduleDomainModule = listOf(
    AppointmentScheduleDomainClientModule,
    ClinicalAccountDomainClientModule,
    FeatureConfigDomainClientModule,
    KafkaProducerModule,
    MaraudersMapDomainClientModule,
    MembershipClientModule,
    NotificationModule,
    PersonDomainClientModule,
    ProviderDomainClientModule,
    StaffDomainClientModule,
    HealthPlanDomainClientModule,
    HealthConditionDomainClientModule,
    HealthLogicDomainClientModule,
    CommunicationModule,
    BusinessDomainClientModule,
    AppointmentDomainClientModule,

    module(createdAtStart = true) {
        single {
            DefaultHttpClient({ install(ContentNegotiation) { simpleGson() } }, timeoutInMillis = 15_000)
        }

        when (BaseConfig.instance.runningMode) {
            RunningMode.PRODUCTION -> {
                single<SesClient> { SesClient.builder().region(Region.US_EAST_1).build() }
                single<EmailSenderClient> { SimpleEmailServiceClient(get()) }
                single<EmailTemplateClient> { SimpleEmailServiceTemplateClient(get()) }
            }

            else -> {
                single<EmailTemplateClient> { MockEmailTemplateClient }
                single<EmailSenderClient> { MockEmailClient }
            }
        }

        val cache = CacheFactory.newInstance("schedule-domain-service-cache")

        val httpClient: HttpClient = DefaultHttpClient(
            Apache.create {
                customizeClient {
                    setMaxConnTotal(2000)
                    setMaxConnPerRoute(200)
                }
            },
            {
                install(ContentNegotiation) {
                    gsonSnakeCase()
                }
            }, timeoutInMillis = 15_000
        )

        single<HubspotClient> {
            if (ServiceConfig.isProduction && ServiceConfig.environment() != RunningMode.TEST)
                HubspotClientImpl(
                    ServiceConfig.Crm.hubspotConfig(),
                    Apache.create()
                )
            else HubspotClientLocal()
        }

        single { CalendlyClient(CalendlyConfiguration(ServiceConfig.Calendly.config().restApiKey), httpClient) }
        single { ZoomClient(ServiceConfig.Zoom.config(), httpClient) }
        single { TrebleWhatsAppClient(ServiceConfig.Treble.config(), httpClient) }
        single { LivanceClient() }

        // Internal Services
        single { AppointmentReminderService(get()) }
        single { AppointmentScheduledNotifier(get(), get()) }
        single { EmailSender(get(), get()) }
        single { HealthResourcesStore(get(), get(), get(), get()) }
        single { IcalGenerator() }
        single { ImmersionAppointmentScheduleService(get(), get(), get()) }
        single { Mailer(get()) }
        single { MailerServiceImpl(get(), get()) }
        single { StaffScheduleServiceImpl(get(), get()) }
        single { TrebleWhatsAppService(get()) }
        single { ZoomService(get()) }
        single { ExternalAppointmentScheduleInternalService(get(), get()) }

        // External Services
        single<AppointmentScheduleEventTypeService> {
            AppointmentScheduleEventTypeServiceImpl(
                get(),
                get(),
                get<EventTypeProviderUnitService>() as EventTypeProviderUnitServiceImpl,
                get()
            )
        }
        single<AppointmentScheduleEventTypeDateExceptionService> {
            AppointmentScheduleEventTypeDateExceptionServiceImpl(get())
        }
        single<AppointmentScheduleOptionService> {
            AppointmentScheduleOptionServiceImpl(
                get(),
                get(),
                get(),
                get(),
                get<EventTypeProviderUnitService>() as EventTypeProviderUnitServiceImpl,
                get(),
                get<StaffScheduleService>() as StaffScheduleServiceImpl,
                get(),
                cache,
                get(),
                get()
            )
        }
        single<AppointmentSchedulePreTriageService> {
            AppointmentSchedulePreTriageServiceImpl(
                get(),
                get<AppointmentScheduleOptionService>() as AppointmentScheduleOptionServiceImpl,
                get(),
                get(),
                get(),
                get()
            )
        }
        single<AppointmentScheduleService> {
            AppointmentScheduleServiceImpl(
                get(),
                get(),
                get(),
                get(),
                get<SchedulePreferenceService>() as SchedulePreferenceServiceImpl,
                get(),
                get(),
                get()
            )
        }
        single<AppointmentScheduleCheckInService> { AppointmentScheduleCheckInServiceImpl(get()) }
        single<ExternalCalendarEventService> { ExternalCalendarEventServiceImpl(get(), get()) }
        single<ExternalCalendarRecurrentEventService> { ExternalCalendarRecurrentEventServiceImpl(get(), get()) }
        single<EventTypeProviderUnitService> { EventTypeProviderUnitServiceImpl(get(), get()) }
        single { GoogleCalendarApi() }
        single<GoogleCalendarEventService> {
            GoogleCalendarEventServiceImpl(
                get<ExternalCalendarEventService>() as ExternalCalendarEventServiceImpl,
                get<SchedulePreferenceService>() as SchedulePreferenceServiceImpl,
                get(),
                get(),
                get(),
                get(),
                get<ExternalCalendarRecurrentEventService>() as ExternalCalendarRecurrentEventServiceImpl,
                get(),
                get()
            )
        }
        single<HealthConditionSchedulingService> { HealthConditionSchedulingServiceImpl(get(), get()) }
        single<PersonCalendlyService> { PersonCalendlyServiceImpl(get()) }
        single<ReferralSchedulingService> {
            ReferralSchedulingServiceImpl(
                get(),
                get<AppointmentScheduleOptionService>() as AppointmentScheduleOptionServiceImpl
            )
        }
        single<SchedulePreferenceService> { SchedulePreferenceServiceImpl(get(), get(), get()) }
        single<StaffScheduleService> { StaffScheduleServiceImpl(get(), get()) }
        single<StaffSchedulePreferenceService> { StaffSchedulePreferenceServiceImpl(get()) }
        single<StaffAvailabilityService> {
            StaffAvailabilityServiceImpl(
                get(),
                get<AppointmentScheduleOptionService>() as AppointmentScheduleOptionServiceImpl,
                get(),
                get<AppointmentScheduleEventTypeDateExceptionService>() as AppointmentScheduleEventTypeDateExceptionServiceImpl,
                get<StaffScheduleService>() as StaffScheduleServiceImpl,
                get<ExternalCalendarEventService>() as ExternalCalendarEventServiceImpl,
                get(),
                get()
            )
        }
        single<ExternalAppointmentScheduleService>{ ExternalAppointmentScheduleServiceImpl(get(), get()) }
        single<LivanceService>{ LivanceServiceImpl(get(), get(), get(), get(), get()) }

        // Controllers
        single { HealthController(SERVICE_NAME) }
        single { RecurringController(get()) }
        single { GoogleCalendarController(get(), get()) }
        single {
            BackfillController(
                get(),
                get<GoogleCalendarEventService>() as GoogleCalendarEventServiceImpl,
                get<AppointmentScheduleService>() as AppointmentScheduleServiceImpl,
            )
        }
        single { InternalFeaturesController(get<GoogleCalendarEventService>() as GoogleCalendarEventServiceImpl) }

        // Subscribers
        single { SchedulerConsumer(get()) }
        single { HubspotConsumer(get(), get(), get(), get(), get(), get(), get()) }
        single { AppointmentScheduleCancelRequestedConsumer(get()) }
        single {
            GoogleCalendarEventsConsumer(
                get<GoogleCalendarEventService>() as GoogleCalendarEventServiceImpl,
                get<SchedulePreferenceService>() as SchedulePreferenceServiceImpl
            )
        }
        single { PersonHealthEventConsumer(get()) }
        single { CalendlyNotificationConsumer(get(), get(), get(), get(), get()) }
        single { StaffScheduleConsumer(get<GoogleCalendarEventService>() as GoogleCalendarEventServiceImpl, get()) }
        single {
            ExternalCalendarEventConsumer(
                get(),
                get(),
                get(),
                get()
            )
        }
        single { ExternalCalendarRecurrentEventConsumer(get()) }
        single { ZoomRefreshTokenUpdatedConsumer(get()) }
        single { AppointmentReminderConsumer(get()) }
        single {
            SchedulePreferenceConsumer(
                get(),
                get(),
                get<StaffSchedulePreferenceService>() as StaffSchedulePreferenceServiceImpl
            )
        }
        single {
            AppointmentScheduleEventTypeConsumer(
                get<AppointmentScheduleOptionService>() as AppointmentScheduleOptionServiceImpl,
                get(),
                get()
            )
        }
        single { AppointmentScheduleCancelledConsumer(get(), get(), get(), get(), get(), get()) }
        single { AppointmentCoordinationConsumer(get(), get(), get(), get(), get()) }
        single {
            AppointmentScheduleCancelConsumer(
                get<GoogleCalendarEventService>() as GoogleCalendarEventServiceImpl,
                get()
            )
        }
        single { AppointmentScheduleOptionConsumer(cache) }
        single {
            AppointmentScheduleNotificationConsumer(
                get(),
                get(),
                get(),
                get(),
                get(),
                get(),
                get(),
                get()
            )
        }
        single {
            StaffUpdatedConsumer(
                get(),
                get(),
                get(),
                get<StaffSchedulePreferenceService>() as StaffSchedulePreferenceServiceImpl,
                get<AppointmentScheduleOptionService>() as AppointmentScheduleOptionServiceImpl
            )
        }
        single {
            AppointmentScheduleCreatedConsumer(
                get(),
                get(),
                get<GoogleCalendarEventService>() as GoogleCalendarEventServiceImpl,
                get<ExternalCalendarEventService>() as ExternalCalendarEventServiceImpl,
                get(),
                get(),
                get(),
                get(),
                get(),
                get(),
                get()
            )
        }
        single { LivanceAppointmentScheduleConsumer(get(), get()) }

        // Data Services
        val invoker = DataLayerClientConfiguration.build()
        single<AppointmentScheduleModelDataService> { AppointmentScheduleModelDataServiceClient(invoker) }
        single<AppointmentScheduleCheckInModelDataService> { AppointmentScheduleCheckInModelDataServiceClient(invoker) }
        single<AppointmentScheduleOptionModelDataService> { AppointmentScheduleOptionModelDataServiceClient(invoker) }
        single<SchedulePreferenceModelDataService> { SchedulePreferenceModelDataServiceClient(invoker) }
        single<StaffScheduleModelDataService> { StaffScheduleModelDataServiceClient(invoker) }
        single<ExternalCalendarEventModelDataService> { ExternalCalendarEventModelDataServiceClient(invoker) }
        single<PersonCalendlyModelDataService> { PersonCalendlyModelDataServiceClient(invoker) }
        single<ExternalCalendarRecurrentEventModelDataService> { ExternalCalendarRecurrentEventModelDataServiceClient(invoker) }
        single<AppointmentScheduleEventTypeModelDataService> { AppointmentScheduleEventTypeModelDataServiceClient(invoker) }
        single<AppointmentReminderModelDataService> { AppointmentReminderModelDataServiceClient(invoker) }
        single<StaffSchedulePreferenceModelDataService> { StaffSchedulePreferenceModelDataServiceClient(invoker) }
        single<EventTypeProviderUnitModelDataService> { EventTypeProviderUnitModelDataServiceClient(invoker) }
        single<AppointmentScheduleEventTypeDateExceptionModelDataService> {
            AppointmentScheduleEventTypeDateExceptionModelDataServiceClient(invoker)
        }
        single<ExternalAppointmentScheduleModelDataService> { ExternalAppointmentScheduleModelDataServiceClient(invoker) }
    }
)
