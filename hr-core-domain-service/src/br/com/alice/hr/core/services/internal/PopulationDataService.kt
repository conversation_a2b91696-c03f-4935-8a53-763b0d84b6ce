package br.com.alice.hr.core.services.internal

import br.com.alice.common.extensions.coResultOf
import br.com.alice.hr.core.clients.interfaces.DataAPIClient
import br.com.alice.hr.core.clients.PopulationDataResponse
import java.util.UUID

class PopulationDataService(
    private val dataAPIClient: DataAPIClient,
) {
    suspend fun getPopulationData(companyId: UUID) =
        coResultOf<PopulationDataResponse, Throwable> {
            dataAPIClient.getPopulationDataByCompany(companyId)
        }
}
