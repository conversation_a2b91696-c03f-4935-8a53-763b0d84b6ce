package br.com.alice.api.backoffice.mappers.staff

import br.com.alice.common.mappers.CommonInputMapper
import br.com.alice.common.core.StaffType
import io.ktor.http.Parameters

object StaffFormInputMapper {
    fun getStaffType(queryParams: Parameters): StaffType =
        CommonInputMapper.getFilterParams<String>(queryParams, "type")
            ?.let { StaffType.valueOf(it) }
            ?: throw IllegalArgumentException("Staff type is required")

    data class StaffFilter(
        val searchToken: String?
    )

    fun toFilter(queryParams: Parameters): StaffFilter {
        val searchToken = CommonInputMapper.getFilterParams<String>(queryParams, "q")
        return StaffFilter(searchToken = searchToken)
    }
}