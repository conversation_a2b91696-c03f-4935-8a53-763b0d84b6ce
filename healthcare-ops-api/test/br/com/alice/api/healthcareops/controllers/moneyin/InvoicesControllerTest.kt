package br.com.alice.api.healthcareops.controllers.moneyin

import br.com.alice.api.healthcareops.api.RoutesTestHelper
import br.com.alice.api.healthcareops.controllers.PaymentAccountResponse
import br.com.alice.api.healthcareops.controllers.PaymentMemberResponse
import br.com.alice.api.healthcareops.converters.InvoiceItemsConverter
import br.com.alice.api.healthcareops.converters.MemberInvoiceResponseConverter
import br.com.alice.api.healthcareops.models.InvoiceCancellationRequest
import br.com.alice.api.healthcareops.models.IssueInvoiceRequest
import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.yearMonthFormatter
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ApprovePaymentResponse
import br.com.alice.data.layer.models.CancelPaymentRequest
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.CreateMemberInvoiceRequest
import br.com.alice.data.layer.models.CreatePaymentRequest
import br.com.alice.data.layer.models.CreatePaymentResponse
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.InvoicePaymentSource
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.MemberInvoiceResponse
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.data.layer.models.MemberInvoicesResponse
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.models.PaymentRequest
import br.com.alice.data.layer.models.withInvoicePayments
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.InvoiceAlreadyPaidException
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicePdfService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PortalUrlGeneratorService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import io.ktor.client.statement.bodyAsChannel
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.BeforeTest
import kotlin.test.Test

class InvoicesControllerTest : RoutesTestHelper() {

    private val invoicesService: InvoicesService = mockk()
    private val memberService: MemberService = mockk()
    private val personService: PersonService = mockk()
    private val invoicePaymentService: InvoicePaymentService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val memberInvoiceGroupService: MemberInvoiceGroupService = mockk()
    private val invoicePdfService: InvoicePdfService = mockk()
    private val portalUrlGeneratorService: PortalUrlGeneratorService = mockk()

    private val member = TestModelFactory.buildMember(personToBeAuthenticated.id)
    private val invoice = TestModelFactory.buildMemberInvoice(member)

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            InvoicesController(
                invoicesService,
                personService,
                memberService,
                invoicePaymentService,
                billingAccountablePartyService,
                memberInvoiceGroupService,
                invoicePdfService,
                portalUrlGeneratorService
            )
        }
    }

    @Test
    fun `#getPaymentsProfile should return 200 with expected response`() {
        val person = TestModelFactory.buildPerson()
        coEvery { personService.get(person.id) } returns person.success()

        val member = TestModelFactory.buildMember(personId = person.id)
        coEvery { memberService.findByPerson(person.id) } returns listOf(member).success()

        val expectedResponse = PaymentAccountResponse(
            id = person.id.toString(),
            fullName = person.fullRegisterName,
            email = person.email,
            members = listOf(
                PaymentMemberResponse(
                    id = member.id,
                    status = member.status,
                    statusHistory = member.statusHistory
                )
            )
        )

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/people/${person.id}/payments") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#cancel should return 200 OK when invoice was canceled successfully`() {
        coEvery { invoicesService.cancel(invoice.id, CancellationReason.INVALID) } returns Result.success(
            invoice.cancel(CancellationReason.INVALID)
        )

        val cancellationRequest = InvoiceCancellationRequest(CancellationReason.INVALID)

        authenticatedAs(token, personToBeAuthenticated) {
            put("/members/${member.id}/invoices/${invoice.id}/cancel", body = cancellationRequest) { response ->
                assertThat(response).isSuccessfulJson()
            }
        }
    }

    @Test
    fun `#cancel should return 200 OK when invoice was already paid`() {
        coEvery { invoicesService.cancel(invoice.id, CancellationReason.INVALID) } returns Result.failure(
            InvoiceAlreadyPaidException("Invoice ${invoice.id} is already paid")
        )

        val cancellationRequest = InvoiceCancellationRequest(CancellationReason.INVALID)

        authenticatedAs(token, personToBeAuthenticated) {
            put("/members/${member.id}/invoices/${invoice.id}/cancel", body = cancellationRequest) { response ->
                assertThat(response).isBadRequestWithErrorCode("invoice_already_paid")
            }
        }
    }

    @Test
    fun `#listInvoices should return 200 OK with expected data`() = mockLocalDateTime {
        val invoicePayment1 = TestModelFactory.buildInvoicePayment(
            source = InvoicePaymentSource.ITAU
        )
        val invoicePayment2 = TestModelFactory.buildInvoicePayment(
            source = InvoicePaymentSource.ITAU
        )
        val memberInvoiceWithPayments1 = TestModelFactory.buildMemberInvoiceWithPayments(
            member.id,
            invoicePayments = listOf(invoicePayment1)
        )
        val memberInvoiceWithPayments2 = TestModelFactory.buildMemberInvoiceWithPayments(
            member.id,
            invoicePayments = listOf(invoicePayment2)
        )

        val memberInvoices = listOf(memberInvoiceWithPayments1, memberInvoiceWithPayments2)
        val memberInvoicesResponse = MemberInvoiceResponseConverter.convert(memberInvoices)
        val invoicePaymentResponse1Expected =
            memberInvoicesResponse.memberInvoices.first().payments.first().copy(portalUrl = "portalUrl1")
        val invoicePaymentResponse2Expected =
            memberInvoicesResponse.memberInvoices.last().payments.first().copy(portalUrl = "portalUrl2")

        val expected = MemberInvoicesResponse(
            memberInvoices = listOf(
                MemberInvoiceResponse(
                    id = memberInvoiceWithPayments1.id.toString(),
                    referenceDate = memberInvoiceWithPayments1.referenceDate,
                    status = memberInvoiceWithPayments1.status,
                    paidAt = null,
                    totalAmount = memberInvoiceWithPayments1.totalAmount,
                    payments = listOf(invoicePaymentResponse1Expected),
                    dueDate = memberInvoiceWithPayments1.dueDate.toString(),
                    cancelledReason = null,
                    invoiceItems = memberInvoicesResponse.memberInvoices.first().invoiceItems
                ),
                MemberInvoiceResponse(
                    id = memberInvoiceWithPayments2.id.toString(),
                    referenceDate = memberInvoiceWithPayments2.referenceDate,
                    status = memberInvoiceWithPayments2.status,
                    paidAt = null,
                    totalAmount = memberInvoiceWithPayments2.totalAmount,
                    payments = listOf(invoicePaymentResponse2Expected),
                    dueDate = memberInvoiceWithPayments2.dueDate.toString(),
                    cancelledReason = null,
                    invoiceItems = memberInvoicesResponse.memberInvoices.last().invoiceItems
                )
            )
        )

        coEvery {
            invoicesService.listInvoices(member.id, true)
        } returns memberInvoices.success()

        coEvery {
            portalUrlGeneratorService.mountPortalUrl(
                invoicePayment1.id
            )
        } returns "portalUrl1"
        coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment2.id) } returns "portalUrl2"

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/members/${member.id}/invoices") { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        coVerifyOnce { invoicesService.listInvoices(any(), any()) }
        coVerify(exactly = 2) { portalUrlGeneratorService.mountPortalUrl(any()) }
    }

    @Nested
    inner class CreatePayment {
        @Test
        fun `#should return 200 OK with expected data`() {
            val invoiceId = RangeUUID.generate()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val payment = TestModelFactory.buildInvoicePayment(
                memberInvoiceIds = listOf(invoiceId),
                source = InvoicePaymentSource.IUGU,
                externalId = "1234",
                amount = BigDecimal("699.00"),
                billingAccountablePartyId = billingAccountableParty.id,
                reason = PaymentReason.REGULAR_PAYMENT,
                origin = InvoicePaymentOrigin.ISSUED_BY_STAFF,
            )
            val paymentRequest = PaymentRequest(
                method = payment.method,
                amount = payment.amount,
                dueDate = LocalDate.now().toString(),
                memberInvoiceId = invoiceId,
                externalId = "1234",
                reason = PaymentReason.REGULAR_PAYMENT,
            )
            val expected = CreatePaymentResponse(payment)

            coEvery { memberService.get(member.id) } returns member.success()
            coEvery { invoicesService.get(invoiceId) } returns invoice.success()
            coEvery { billingAccountablePartyService.getCurrentOrCreateForPerson(member.personId) } returns billingAccountableParty.success()
            coEvery {
                invoicePaymentService.createInvoicePayment(
                    match {
                        assertThat(it).usingRecursiveComparison()
                            .ignoringFields("id", "createdAt", "updatedAt", "paymentDetailString", "invoiceGroupId")
                            .isEqualTo(payment)
                        true
                    },
                    member
                )
            } returns payment.success()

            val request = CreatePaymentRequest(payment = paymentRequest)

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/members/${member.id}/invoices/$invoiceId/payments", request) { response ->
                    assertThat(response).isOK()

                    val data: CreatePaymentResponse = response.bodyAsJson()

                    assertThat(data.payment).usingRecursiveComparison().ignoringFields(
                        "id",
                        "createdAt",
                        "updatedAt",
                        "paymentDetailString"
                    ).isEqualTo(expected.payment)
                }
            }
        }

        @Test
        fun `#should return 200 OK with expected data created based on the member invoice group`() = runBlocking {
            val invoiceId = RangeUUID.generate()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val memberInvoiceGroup =
                TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = billingAccountableParty.id)
            val invoice = invoice.copy(memberInvoiceGroupId = memberInvoiceGroup.id, personId = member.personId)
            val payment = TestModelFactory.buildInvoicePayment(
                memberInvoiceIds = listOf(invoiceId),
                source = InvoicePaymentSource.IUGU,
                externalId = "1234",
                amount = BigDecimal("699.00"),
                billingAccountablePartyId = billingAccountableParty.id,
                reason = PaymentReason.REGULAR_PAYMENT,
            )
            val dueDate = LocalDate.now()
            val paymentRequest = PaymentRequest(
                method = payment.method,
                amount = payment.amount,
                dueDate = dueDate.toString(),
                memberInvoiceId = invoiceId,
                externalId = "1234",
                reason = PaymentReason.REGULAR_PAYMENT,
            )
            val invoices = listOf(invoice)

            val expected = CreatePaymentResponse(payment)

            coEvery { memberService.get(member.id) } returns member.success()
            coEvery { invoicesService.get(invoiceId) } returns invoice.success()
            coEvery { billingAccountablePartyService.get(billingAccountableParty.id) } returns billingAccountableParty.success()
            coEvery { memberInvoiceGroupService.get(memberInvoiceGroup.id) } returns memberInvoiceGroup.success()
            coEvery { invoicesService.listByMemberInvoiceGroupId(memberInvoiceGroup.id) } returns invoices.success()

            coEvery {
                invoicePaymentService.createInvoicePaymentForMemberInvoices(
                    InvoicePaymentService.CreateInvoicePaymentForMemberInvoicesPayload(
                        payment.method,
                        invoices,
                        dueDate = dueDate.atEndOfTheDay(),
                        billingAccountableParty,
                        PaymentReason.REGULAR_PAYMENT,
                        memberInvoiceGroup.copy(type = null),
                        member.personId,
                        InvoicePaymentOrigin.ISSUED_BY_STAFF,
                        externalId = payment.externalId
                    )
                )
            } returns payment.success()

            val request = CreatePaymentRequest(payment = paymentRequest)

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/members/${member.id}/invoices/$invoiceId/payments", request) { response ->
                    assertThat(response).isOK()

                    val data: CreatePaymentResponse = response.bodyAsJson()

                    assertThat(data.payment).usingRecursiveComparison().ignoringFields(
                        "id",
                        "createdAt",
                        "updatedAt",
                        "paymentDetailString"
                    ).isEqualTo(expected.payment)
                }
            }

            coVerifyNone {
                invoicePaymentService.createInvoicePayment(
                    any(),
                    member
                )
            }
        }

        @Test
        fun `#should return error if payment method is PIX and due date is setted for more than 5 days from now`() {
            val invoiceId = RangeUUID.generate()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val payment = TestModelFactory.buildInvoicePayment(
                memberInvoiceIds = listOf(invoiceId),
                source = InvoicePaymentSource.IUGU,
                externalId = "1234",
                amount = BigDecimal("699.00"),
                billingAccountablePartyId = billingAccountableParty.id,
                method = PaymentMethod.PIX,
            )
            val paymentRequest = PaymentRequest(
                method = payment.method,
                amount = payment.amount,
                dueDate = LocalDate.now().plusDays(6L).toString(),
                memberInvoiceId = invoiceId,
                externalId = "1234"
            )

            coEvery { memberService.get(member.id) } returns member.success()
            coEvery { invoicesService.get(invoiceId) } returns invoice.success()
            coEvery { billingAccountablePartyService.getCurrentOrCreateForPerson(member.personId) } returns billingAccountableParty.success()

            val request = CreatePaymentRequest(payment = paymentRequest)

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/members/${member.id}/invoices/$invoiceId/payments", request) { response ->
                    assertThat(response).isBadRequest()
                }
            }
        }
    }

    @Test
    fun `#approvePayment should return 200 OK with expected data`() {
        val invoiceId = RangeUUID.generate()
        val payment = TestModelFactory.buildInvoicePayment(memberInvoiceIds = listOf(invoiceId))

        coEvery { invoicePaymentService.approve(any(), approvedByStaffId = any()) } returns payment.success()

        val expectedResponse = ApprovePaymentResponse(payment)

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/invoices/payments/${payment.id}/approve") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { invoicePaymentService.approve(payment.id, approvedByStaffId = staffToBeAuthenticated.id) }
    }

    @Test
    fun `#cancelPayment should return 200 OK with expected data`() {
        val invoiceId = RangeUUID.generate()
        val reason = CancellationReason.INVALID
        val payment = TestModelFactory.buildInvoicePayment(memberInvoiceIds = listOf(invoiceId))

        coEvery { invoicePaymentService.cancel(payment.id, reason) } returns payment.success()

        val request = CancelPaymentRequest(reason = reason)
        val expectedResponse = ApprovePaymentResponse(payment)

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/invoices/payments/${payment.id}/cancel", request) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#createInvoice should return 200 OK with expected data`() {
        val memberInvoice = TestModelFactory.buildMemberInvoice(
            member = member
        )

        val request = CreateMemberInvoiceRequest(
            referenceDate = "2020-04",
            totalAmount = BigDecimal("900.89"),
            dueDate = "2021-03-03"
        )

        coEvery { memberService.get(member.id) } returns member.success()
        coEvery {
            invoicesService.createInvoice(match {
                it.memberId == member.id &&
                        it.totalAmount == request.totalAmount &&
                        it.referenceDate == LocalDate.parse("2020-04-01") &&
                        it.dueDate == LocalDate.parse("2021-03-03").atEndOfTheDay()
            })
        } returns memberInvoice.success()

        val expectedInvoiceItem = InvoiceItemsConverter.convert(memberInvoice.invoiceItems!!.first())

        val expected = MemberInvoiceResponse(
            id = memberInvoice.id.toString(),
            referenceDate = memberInvoice.referenceDate,
            status = memberInvoice.status,
            paidAt = null,
            totalAmount = memberInvoice.totalAmount,
            payments = emptyList(),
            dueDate = memberInvoice.dueDate.toString(),
            cancelledReason = null,
            invoiceItems = listOf(expectedInvoiceItem)
        )

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/members/${member.id}/invoices", request) { response ->
                assertThat(response).isOKWithData(expected)
            }
        }
    }

    @Test
    fun `#issueInvoice should return 200 OK with expected data and call service to issue invoice and create payment`() {
        val requestBody = IssueInvoiceRequest(
            referenceDate = "2021-02",
            dueDate = "2021-02-10",
            PaymentMethod.BOLETO
        )
        val amount = BigDecimal("100.00")
        val member = TestModelFactory.buildMember()
        val memberInvoice = TestModelFactory.buildMemberInvoice(member = member, totalAmount = amount)
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val payment = TestModelFactory.buildInvoicePayment(
            memberInvoiceIds = listOf(memberInvoice.id),
            source = InvoicePaymentSource.IUGU,
            amount = amount,
            billingAccountablePartyId = billingAccountableParty.id,
            method = requestBody.paymentMethod,
            status = InvoicePaymentStatus.PENDING,
            reason = PaymentReason.FIRST_PAYMENT,
            origin = InvoicePaymentOrigin.ISSUED_BY_STAFF,
        )
        coEvery {
            invoicesService.issueInvoice(
                member.id,
                LocalDate.parse(requestBody.referenceDate, yearMonthFormatter),
                LocalDate.parse(requestBody.dueDate).atEndOfTheDay(),
                PaymentMethod.BOLETO,
                withPayment = false,
                type = MemberInvoiceType.FIRST_PAYMENT,
            )
        } returns memberInvoice.success()

        coEvery { memberService.get(member.id) } returns member.success()
        coEvery { billingAccountablePartyService.getCurrentOrCreateForPerson(member.personId) } returns billingAccountableParty.success()
        coEvery {
            invoicePaymentService.createInvoicePayment(
                match {
                    assertThat(it).usingRecursiveComparison()
                        .ignoringFields("id", "createdAt", "updatedAt", "paymentDetailString", "invoiceGroupId")
                        .isEqualTo(payment)
                    true
                },
                member
            )
        } returns payment.success()

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/members/${member.id}/invoices/issue_invoice", requestBody) { response ->
                assertThat(response).isOKWithData(
                    MemberInvoiceResponseConverter.convert(
                        memberInvoice.withInvoicePayments(
                            listOf(payment)
                        )
                    )
                )
            }
        }
    }

    @Test
    fun `#downloadInvoicePdf should generate and download invoice bank slip successfully`() {

        val invoicePaymentId = RangeUUID.generate()
        val invoiceBytes = byteArrayOf(1, 2, 3, 4, 5)

        coEvery { invoicePdfService.generateInvoice(invoicePaymentId) } returns invoiceBytes

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/invoices/bank_slip/${invoicePaymentId}") { response ->
                assertThat(response).isOK()
                assertThat(response.bodyAsChannel()).isEqualTo(invoiceBytes)
            }
        }
    }

    @Test
    fun `#downloadInvoicePdf should generate and download secure invoice bank slip successfully`() {

        val invoicePaymentId = RangeUUID.generate()
        val invoiceBytes = byteArrayOf(1, 2, 3, 4, 5)

        coEvery { invoicePdfService.generateInvoice(invoicePaymentId, true) } returns invoiceBytes

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/invoices/bank_slip/${invoicePaymentId}/secure") { response ->
                assertThat(response).isOK()
                assertThat(response.bodyAsChannel()).isEqualTo(invoiceBytes)
            }
        }
    }

}
