package br.com.alice.provider.services

import br.com.alice.common.core.Status
import br.com.alice.common.core.Status.ACTIVE
import br.com.alice.common.core.extensions.isNotNullOrBlank
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.common.useReadDatabase
import br.com.alice.coverage.client.AddressService
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.StructuredAddress
import br.com.alice.data.layer.models.StructuredAddressReferenceModel
import br.com.alice.data.layer.services.ProviderUnitModelDataService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.provider.client.ProviderUnitFilter
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.provider.converters.modelConverters.toModel
import br.com.alice.provider.converters.modelConverters.toTransport
import br.com.alice.provider.model.ProviderUnitCreatedEvent
import br.com.alice.provider.model.ProviderUnitUpdatedEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class ProviderUnitServiceImpl(
    private val data: ProviderUnitModelDataService,
    private val addressService: AddressService,
    private val kafkaProducerService: KafkaProducerService,
) : ProviderUnitService {

    private fun getDefaultCasaAliceId() =
        FeatureService.get(FeatureNamespace.PROVIDER, "default_casa_alice_id", "67b70984-1cbb-4180-bc1a-e9717783946e")
            .toUUID()

    override suspend fun get(id: UUID) = useReadDatabase {
        data.get(id).map {
            it.copy(
                address = addressService.findByReferencedObject(
                    id,
                    StructuredAddressReferenceModel.PROVIDER_UNIT
                ).getOrNullIfNotFound()
            )
        }.map { it.toTransport() }
    }

    override suspend fun getActiveById(id: UUID): Result<ProviderUnit, Throwable> = useReadDatabase {
        data.findOne { where { this.id.eq(id) and this.status.eq(ACTIVE) } }.map {
            it.copy(
                address = addressService.findByReferencedObject(
                    id,
                    StructuredAddressReferenceModel.PROVIDER_UNIT
                ).getOrNullIfNotFound()
            )
        }.map { it.toTransport() }
    }

    override suspend fun getActiveAndShowOnScheduler(range: IntRange?, query: String?) =
        let {
            takeIf { range != null }?.let {
                data.find {
                    where {
                        this.status.eq(ACTIVE) and this.showOnScheduler.eq(true) and query?.let { q ->
                            searchTokens.search(q)
                        }
                    }
                        .orderBy { this.name }
                        .offset { range!!.first }
                        .limit { range!!.count() }
                }
            } ?: data.find {
                where {
                    this.status.eq(ACTIVE) and this.showOnScheduler.eq(true) and query?.let { q ->
                        searchTokens.search(q)
                    }
                }.orderBy { this.name }
            }
        }.mapEach {
            it.toTransport().copy(
                address = addressService.findByReferencedObject(
                    it.id,
                    StructuredAddressReferenceModel.PROVIDER_UNIT
                ).getOrNullIfNotFound()
            )
        }

    override suspend fun addWithAddress(
        unit: ProviderUnit,
        address: StructuredAddress
    ): Result<ProviderUnit, Throwable> = data.add(unit.toModel()).flatMap { providerUnit ->
        upsertAddress(providerUnit.toTransport(), address)
    }.then {
        kafkaProducerService.produce(ProviderUnitCreatedEvent(it))
    }

    override suspend fun updateList(request: List<ProviderUnit>) =
        data.updateList(request.map { it.toModel() }).then { providersUnit ->
            providersUnit.pmap {
                kafkaProducerService.produce(ProviderUnitUpdatedEvent(it.toTransport()))
            }
        }.mapEach {
            it.toTransport()
        }

    override suspend fun update(
        providerUnit: ProviderUnit,
        shouldPublishEvent: Boolean
    ): Result<ProviderUnit, Throwable> {
        return data.update(providerUnit.toModel()).then {
            if (shouldPublishEvent) {
                kafkaProducerService.produce(ProviderUnitUpdatedEvent(it.toTransport()))
            }
        }.map {
            it.toTransport()
        }
    }

    override suspend fun updateWithAddress(
        request: ProviderUnit,
        address: StructuredAddress
    ): Result<ProviderUnit, Throwable> = data.update(request.toModel()).flatMap { providerUnit ->
        upsertAddress(providerUnit.toTransport(), address)
    }.then {
        kafkaProducerService.produce(ProviderUnitUpdatedEvent(it))
    }

    override suspend fun getByName(providerName: String) = useReadDatabase {
        data.find { where { name.like(providerName).withStatus() } }.mapEach {
            it.toTransport()
        }
    }

    override suspend fun getByFilterWithRange(
        filter: ProviderUnitFilter,
        range: IntRange
    ): Result<List<ProviderUnit>, Throwable> {
        val (searchToken, status, types, ids) = filter
        return data.find {
            where {
                this.status.inList(status.ifEmpty { listOf(ACTIVE) }).withNameUsingLike(searchToken).withType(types)
                    .withId(ids)
            }.offset { range.first }
                .limit { range.count() }
                .orderBy { this.name }
        }.mapEach {
            it.toTransport()
        }
    }

    override suspend fun countByFilter(filter: ProviderUnitFilter): Result<Int, Throwable> {
        val (searchToken, status, types, ids) = filter
        return data.count {
            where {
                this.status.inList(status.ifEmpty { listOf(ACTIVE) }).withNameUsingLike(searchToken).withType(types)
                    .withId(ids)
            }
        }
    }

    override suspend fun getByType(providerUnitType: ProviderUnit.Type) = useReadDatabase {
        data.find { where { type.eq(providerUnitType).withStatus() } }.map { it.map { it.toTransport() }.sortByName() }
    }

    override suspend fun getByProviderIds(
        providerIds: List<UUID>,
        withAddress: Boolean
    ): Result<List<ProviderUnit>, Throwable> =
        useReadDatabase {
            data.find { where { this.providerId.inList(providerIds).withStatus() } }
                .flatMap { findAndAssociateAddress(it.map { it.toTransport() }, withAddress) }
        }

    override suspend fun getByProviderIdsPaginated(
        providerIds: List<UUID>,
        offset: Int,
        limit: Int
    ): Result<List<ProviderUnit>, Throwable> =
        useReadDatabase {
            data.find {
                where {
                    this.providerId.inList(providerIds)
                        .withStatus()
                }.offset { offset }.limit { limit }
            }.mapEach {
                it.toTransport()
            }
        }

    override suspend fun getByProviderIdsAndTypes(
        providerIds: List<UUID>,
        types: List<ProviderUnit.Type>,
        range: IntRange,
        searchTerm: String?,
        specialtyIdsFilter: List<UUID>?,
    ): Result<List<ProviderUnit>, Throwable> =
        useReadDatabase {
            data.find {
                where {
                    this.providerId.inList(providerIds) and
                            this.type.inList(types)
                                .withStatus()
                                .withName(searchTerm)
                                .withSpecialties(specialtyIdsFilter)
                }.orderBy { this.name }.offset { range.first }.limit { range.count() }
            }.mapEach {
                it.toTransport()
            }
        }

    override suspend fun getByProvidersTypeAndSpecialty(
        providerIds: List<UUID>,
        type: ProviderUnit.Type,
        specialtyIds: List<UUID>,
        subSpecialtyIds: List<UUID>?,
        brand: Brand?,
    ): Result<List<ProviderUnit>, Throwable> = useReadDatabase {
        data.find {
            where {
                this.type.eq(type)
                    .withStatus()
                    .withProviders(providerIds)
                    .withSpecialties(specialtyIds)
                    .withSubSpecialties(subSpecialtyIds)
                    .withBrand(brand)
            }.orderBy { this.name }
        }.mapEach {
            it.toTransport()
        }
    }

    override suspend fun getByProviderIdsAndSpecialties(
        providerIds: List<UUID>,
        specialtyIds: List<UUID>
    ): Result<List<ProviderUnit>, Throwable> = useReadDatabase {
        data.find {
            where {
                this.providerId.inList(providerIds)
                    .withStatus()
                    .withSpecialties(specialtyIds)
            }
        }.mapEach {
            it.toTransport()
        }
    }

    override suspend fun countByProviderIdsAndType(
        providerIds: List<UUID>,
        types: List<ProviderUnit.Type>,
        searchTerm: String?,
        specialtyIdsFilter: List<UUID>?,
    ): Result<Int, Throwable> =
        useReadDatabase {
            data.count {
                where {
                    this.providerId.inList(providerIds) and
                            this.type.inList(types)
                                .withStatus()
                                .withName(searchTerm)
                                .withSpecialties(specialtyIdsFilter)
                }
            }
        }


    override suspend fun getByProviderIdsWithTypes(
        providerIds: List<UUID>,
        types: List<ProviderUnit.Type>,
        withAddress: Boolean
    ): Result<List<ProviderUnit>, Throwable> =
        useReadDatabase {
            data.find { where { this.providerId.inList(providerIds).and(this.type.inList(types)).withStatus() } }
                .flatMap { findAndAssociateAddress(it.map { it.toTransport() }, withAddress) }
        }

    override suspend fun getByProviderIdsBrandWithTypes(
        providerIds: List<UUID>,
        brand: Brand,
        types: List<ProviderUnit.Type>,
        withAddress: Boolean
    ): Result<List<ProviderUnit>, Throwable> = useReadDatabase {
        data.find {
            where {
                this.providerId.inList(providerIds)
                    .withStatus()
                    .and(this.type.inList(types))
                    .and(this.brand.inList(listOf(brand, Brand.ALICE_DUQUESA)))
            }
        }.flatMap { findAndAssociateAddress(it.map { it.toTransport() }, withAddress) }
    }

    override suspend fun getByIds(list: List<UUID>, withAddress: Boolean) = useReadDatabase {
        if (withAddress) {
            getByIdsWithAddress(list).mapEach { it.toTransport() }
        } else {
            data.find { where { id.inList(list).withStatus() } }.mapEach { it.toTransport() }
        }
    }

    private suspend fun getByIdsWithAddress(unitIds: List<UUID>) = useReadDatabase {
        coroutineScope {
            val addressDeferred = async { addressService.findByReferencedModelIds(unitIds).get() }
            val unitsDeferred = async { data.find { where { id.inList(unitIds).withStatus() } } }
            val address = addressDeferred.await().associateBy { it.referencedModelId }
            unitsDeferred.await().mapEach {
                it.copy(address = address[it.id])
            }
        }
    }

    override suspend fun getDefaultCasaAlice() = useReadDatabase {
        data.findOne { where { id.eq(getDefaultCasaAliceId()) } }.map { it.toTransport() }
    }

    override suspend fun getByCnpjs(cnpjs: List<String>) = useReadDatabase {
        data.find { where { this.cnpj.inList(cnpjs).withStatus() } }.mapEach { it.toTransport() }
    }

    override suspend fun getByAdministrativeStaffId(administrativeStaffId: UUID, withAddress: Boolean) =
        useReadDatabase {
            data.find { where { this.administrativeStaff.contains(administrativeStaffId).withStatus() } }
                .flatMap { findAndAssociateAddress(it.map { it.toTransport() }, withAddress) }
        }

    override suspend fun getByClinicalStaffId(clinicalStaffId: UUID, withAddress: Boolean) = useReadDatabase {
        data.find { where { this.clinicalStaffId.contains(clinicalStaffId).withStatus() } }
            .flatMap { findAndAssociateAddress(it.map { it.toTransport() }, withAddress) }
    }

    override suspend fun getByGroupId(id: UUID): Result<List<ProviderUnit>, Throwable> = useReadDatabase {
        data.find { where { this.groupId.eq(id).withStatus() } }.mapEach { it.toTransport() }
    }

    override suspend fun getByCnpj(cnpj: String): Result<ProviderUnit, Throwable> = useReadDatabase {
        data.findOne { where { this.cnpj.eq(cnpj).withStatus() } }.map { it.toTransport() }
    }

    override suspend fun getWithEmptyCnes(offset: Int, limit: Int): Result<List<ProviderUnit>, Throwable> =
        useReadDatabase {
            data.find { where { this.cnes.isNull().withStatus() }.offset { offset }.limit { limit } }
                .mapEach { it.toTransport() }
        }

    override suspend fun getByUrlSlug(urlSlug: String, withAddress: Boolean): Result<ProviderUnit, Throwable> =
        useReadDatabase {
            data.findOne {
                where {
                    this.urlSlug.eq(urlSlug)
                }
            }.flatMap { findAndAssociateAddress(it.toTransport(), withAddress) }
        }


    @OptIn(OrPredicateUsage::class)
    override suspend fun findAllClinicalDuquesa(withAddress: Boolean): Result<List<ProviderUnit>, Throwable> =
        useReadDatabase {
            data.find {
                where {
                    this.type.eq(ProviderUnit.Type.CLINICAL).and(
                        scope(this.brand.eq(Brand.DUQUESA).or(this.brand.eq(Brand.ALICE_DUQUESA)))
                    ).withStatus()
                }
            }.flatMap { findAndAssociateAddress(it.map { it.toTransport() }, withAddress) }
        }

    override suspend fun existsClinicalStaffIds(staffId: UUID, type: ProviderUnit.Type): Result<Boolean, Throwable> =
        useReadDatabase {
            data.exists {
                where {
                    this.clinicalStaffId.contains(staffId) and this.type.eq(type)
                }
            }
        }


    private fun List<ProviderUnit>.sortByName() = this.sortedBy { it.name }
    private fun Predicate.withSpecialties(specialtyIds: List<UUID>?) =
        if (specialtyIds.isNotNullOrEmpty())
            this.and(ProviderUnitModelDataService.FieldOptions().medicalSpecialist.containsAnySpecialtyIds(specialtyIds!!))
        else this

    private fun Predicate.withSubSpecialties(subSpecialtyIds: List<UUID>?) =
        if (subSpecialtyIds.isNotNullOrEmpty())
            this.and(
                ProviderUnitModelDataService.FieldOptions().medicalSpecialist.containsAnySubSpecialtyIds(
                    subSpecialtyIds!!
                )
            )
        else this

    private fun Predicate.withBrand(brand: Brand?) =
        if (brand != null)
            this.and(ProviderUnitModelDataService.FieldOptions().brand.inList(listOf(brand, Brand.ALICE_DUQUESA)))
        else this

    private fun Predicate.withStatus(status: Status? = ACTIVE) =
        if (status != null)
            this.and(ProviderUnitModelDataService.FieldOptions().status.eq(status))
        else this

    private fun Predicate.withProviders(providerIds: List<UUID>) =
        if (providerIds.isNotNullOrEmpty())
            this.and(ProviderUnitModelDataService.FieldOptions().providerId.inList(providerIds))
        else this

    private fun Predicate.withName(searchTerm: String?) =
        if (searchTerm.isNotNullOrBlank()) this.and(ProviderUnitModelDataService.FieldOptions().name.search(searchTerm!!))
        else this

    private fun Predicate.withNameUsingLike(searchTerm: String?) =
        if (searchTerm.isNotNullOrBlank()) this.and(ProviderUnitModelDataService.FieldOptions().name.like(searchTerm!!))
        else this

    private fun Predicate.withType(types: List<ProviderUnit.Type>) =
        if (types.isNotEmpty()) this.and(ProviderUnitModelDataService.FieldOptions().type.inList(types))
        else this

    private fun Predicate.withId(ids: List<UUID>) =
        if (ids.isNotEmpty()) this.and(ProviderUnitModelDataService.FieldOptions().id.inList(ids))
        else this

    private suspend fun findAndAssociateAddress(
        units: List<ProviderUnit>,
        mustAssociateAddress: Boolean
    ): Result<List<ProviderUnit>, Throwable> =
        if (mustAssociateAddress) {
            addressService.findByReferencedModelIds(units.map { it.id }.distinct())
                .map { address ->
                    associateAddress(units, address)
                }
        } else {
            units.success()
        }

    private suspend fun findAndAssociateAddress(
        unit: ProviderUnit,
        mustAssociateAddress: Boolean
    ): Result<ProviderUnit, Throwable> =
        if (mustAssociateAddress) {
            addressService.findByReferencedObject(unit.id, StructuredAddressReferenceModel.PROVIDER_UNIT)
                .map {
                    unit.copy(address = it)
                }
        } else {
            unit.success()
        }

    private suspend fun associateAddress(
        units: List<ProviderUnit>,
        address: List<StructuredAddress>
    ): List<ProviderUnit> {
        val addressMap = address.associateBy { it.referencedModelId }
        return units.pmap { it.copy(address = addressMap[it.id]) }
    }

    private suspend fun upsertAddress(providerUnit: ProviderUnit, address: StructuredAddress) =
        addressService.upsert(
            address.copy(
                referencedModelId = providerUnit.id,
                referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT
            )
        ).map {
            providerUnit.copy(address = it)
        }
}
