package br.com.alice.moneyin.controllers

import br.com.alice.common.Response
import br.com.alice.common.asyncLayer
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coFoldException
import br.com.alice.common.extensions.flatMapEach
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.MONEY_IN_ENVIRONMENT_SUBSCRIBERS
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.moneyin.client.CancelPaymentOnAcquirerScheduleService
import br.com.alice.moneyin.client.InvoiceLiquidationService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.communication.InvoiceMailer
import br.com.alice.moneyin.event.ProcessInvoiceNearOverdueEvent
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.delay
import java.time.LocalDate
import java.time.LocalDateTime

const val ALLOW_BRAZE_NOTIFICATIONS_OF_INVOICES_DUE_DATE = "allow_braze_notifications_of_invoices_due_date"
const val SEND_INVOICE_LIQUIDATION_EMAIL_NEAR_TO_OVERDUE = "send_invoice_liquidation_email_near_to_overdue"

class RecurrentController(
    private val invoicesService: InvoicesService,
    private val kafkaProducerService: KafkaProducerService,
    private val invoiceLiquidationService: InvoiceLiquidationService,
    private val invoicePaymentService: InvoicePaymentService,
    private val invoiceMailer: InvoiceMailer,
    private val cancelPaymentOnAcquirerScheduleService: CancelPaymentOnAcquirerScheduleService
) : Controller() {

    suspend fun triggerInvoicesNearOverdueEvents(): Response =
        asyncLayer {
            withRootServicePolicy(MONEY_IN_ENVIRONMENT_SUBSCRIBERS) {
                withUnauthenticatedTokenWithKey(MONEY_IN_ENVIRONMENT_SUBSCRIBERS) {
                    if (isAllowedBrazeNotificationsOfInvoicesDueDate()) {
                        val initialDate = LocalDateTime.now().atEndOfTheDay()
                        val nearOverdueDate = initialDate.plusDays(1L)

                        logger.info(
                            "RecurrentController :: Retrieving invoices near overdue",
                            "initial_date" to initialDate,
                            "near_overdue_date" to nearOverdueDate
                        )

                        invoicesService
                            .listNearOverdueInvoices(
                                nearOverdueDate,
                                initialDate,
                                MemberInvoiceType.REGULAR_PAYMENT
                            ).map { invoices ->
                                val ungroupedInvoices =
                                    invoices.filter { invoice -> invoice.memberInvoiceGroupId == null }
                                val groupedInvoices =
                                    invoices.filter { invoice -> invoice.memberInvoiceGroupId != null }
                                        .groupBy { it.memberInvoiceGroupId }.map { it.value.first() }
                                logger.info(
                                    "RecurrentController :: Grouping invoices near overdue",
                                    "ungrouped_invoices_size" to ungroupedInvoices.size,
                                    "grouped_invoices_size" to groupedInvoices.size
                                )
                                ungroupedInvoices + groupedInvoices
                            }
                            .mapEach { invoice ->
                                logger.info(
                                    "RecurrentController :: Processing invoice near overdue event",
                                    "invoice_id" to invoice.id,
                                )
                                kafkaProducerService.produce(
                                    ProcessInvoiceNearOverdueEvent(invoice)
                                )
                            }.thenError {
                                logger.error(
                                    "RecurrentController :: Error during process",
                                    "error_message" to it.message,
                                )
                            }
                    } else {
                        logger.info("RecurrentController :: Braze notification sending flow for near overdue invoice is inactive")
                    }
                }
            }
            Response
        }

    private fun isAllowedBrazeNotificationsOfInvoicesDueDate() = FeatureService.get(
        FeatureNamespace.PAYMENTS,
        ALLOW_BRAZE_NOTIFICATIONS_OF_INVOICES_DUE_DATE,
        false
    )

    suspend fun sendInvoiceLiquidationEmailNearToOverdue(): Response =
        asyncLayer {
            withRootServicePolicy(MONEY_IN_ENVIRONMENT_SUBSCRIBERS) {
                withUnauthenticatedTokenWithKey(MONEY_IN_ENVIRONMENT_SUBSCRIBERS) {
                    if (shouldSendInvoiceLiquidationEmailNearToOverdue()) {
                        val nearOverdueDate = LocalDate.now().plusDays(10)

                        logger.info(
                            "RecurrentController :: Retrieving invoice liquidation near overdue",
                            "near_overdue_date" to nearOverdueDate
                        )

                        invoiceLiquidationService
                            .listInvoicesNearOverdue(
                                nearOverdueDate
                            ).flatMap { invoiceLiquidationList ->
                                invoicePaymentService.getByInvoiceLiquidationIds(
                                    invoiceLiquidationList.map { it.id },
                                    withPaymentDetails = true,
                                ).flatMap { invoicePayments ->
                                    invoicePayments.filter { it.sendEmail != true }.success()
                                }
                            }.flatMapEach { invoicePayment ->
                                val paymentReason =
                                    if (invoicePayment.reason?.isB2B() == false) PaymentReason.B2C_LIQUIDATION else PaymentReason.B2B_LIQUIDATION
                                invoiceMailer.send(
                                    invoicePayment,
                                    paymentReason,
                                    invoicePayment.billingAccountablePartyId
                                )
                                    .then {
                                        invoicePaymentService.update(invoicePayment.copy(sendEmail = true))
                                    }
                            }.thenError {
                                logger.error(
                                    "RecurrentController :: Error during process",
                                    "error" to it,
                                    "error_message" to it.message,
                                )
                            }
                    } else {
                        logger.info("RecurrentController :: Send invoice liquidation email near to overdue is inactive")
                    }
                }
            }
            Response
        }

    suspend fun cancelRequestedInvoicePayments(): Response =
        asyncLayer {
            withRootServicePolicy(MONEY_IN_ENVIRONMENT_SUBSCRIBERS) {
                withUnauthenticatedTokenWithKey(MONEY_IN_ENVIRONMENT_SUBSCRIBERS) {
                    val limit = 100
                    val yesterday = LocalDateTime.now().minusDays(1).atEndOfTheDay()
                    do {
                        val cancelScheduleList =
                            cancelPaymentOnAcquirerScheduleService.findInvoicePaymentsRequestedToCancel(
                                date = yesterday,
                                limit = limit
                            ).get()
                        cancelScheduleList.pmap { cancelSchedule ->
                            invoicePaymentService.get(cancelSchedule.invoicePaymentId, withPaymentDetails = false)
                                .flatMap {
                                    cancelPaymentOnAcquirerScheduleService.cancelPaymentOnAcquirer(
                                        cancelSchedule,
                                        it
                                    )
                                }
                                .coFoldException(Exception::class) {
                                    logger.error(
                                        "RecurrentController :: Error during process",
                                        "invoice_payment_id" to cancelSchedule.invoicePaymentId,
                                        "cancel_schedule_id" to cancelSchedule.id,
                                        "error" to it,
                                        "error_message" to it.message,
                                    )
                                    true.success()
                                }
                        }
                        delay(1000)
                    } while (cancelScheduleList.size == limit)
                }
            }
            Response
        }

    private fun shouldSendInvoiceLiquidationEmailNearToOverdue() = FeatureService.get(
        FeatureNamespace.PAYMENTS,
        SEND_INVOICE_LIQUIDATION_EMAIL_NEAR_TO_OVERDUE,
        false
    )
}
