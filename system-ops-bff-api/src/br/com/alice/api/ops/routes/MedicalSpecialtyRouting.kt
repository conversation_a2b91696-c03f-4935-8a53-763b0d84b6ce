package br.com.alice.api.ops.routes

import br.com.alice.api.ops.controllers.ClinicalStaffController
import br.com.alice.api.ops.controllers.MedicalSpecialtyController
import br.com.alice.common.coHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.medicalSpecialtyRoutes() {

    authenticate {
        val medicalSpecialtyController by inject<MedicalSpecialtyController>()
        route("medicalSpecialty") {
            get("/") {
                coHandler(medicalSpecialtyController::index)
            }
            get("/{id}") {
                coHandler("id", medicalSpecialtyController::getById)
            }
            post("/") {
                coHandler(medicalSpecialtyController::create)
            }
            put("/{id}") {
                co<PERSON>and<PERSON>("id", medicalSpecialtyController::update)
            }
        }
        route("specialties") {
            get("/") {
                coHandler(medicalSpecialtyController::getSpecialty)
            }
        }
        route("sub_specialties") {
            get("/") {
                coHandler(medicalSpecialtyController::getSubSpecialty)
            }
        }

        val clinicalStaffController by inject<ClinicalStaffController>()
        route("/clinical_staff") {
            get("/") {
                coHandler(clinicalStaffController::getClinicalStaffs)
            }
        }
    }
}
