package br.com.alice.data.layer.tables

import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

internal data class PersonHealthConditionContractualRiskTable(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonNonPiiToken,
    val healthConditionId: UUID,
    val staffId: UUID,
    val factor: Int,
    val baseRiskRating: Int,
    val finalRiskRating: Int,
    val reason: String,
    val suggestedMonthlyCost: BigDecimal? = null,
    val monthlyCost: BigDecimal? = null,
    val healthConditionDescription: String,
    val code: String,
    val codeType: Disease.Type,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<PersonHealthConditionContractualRiskTable>, PersonNonPiiReference {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
