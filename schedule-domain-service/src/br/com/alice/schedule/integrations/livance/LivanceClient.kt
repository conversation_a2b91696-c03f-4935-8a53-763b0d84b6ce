package br.com.alice.schedule.integrations.livance

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.service.serialization.gsonIdentity
import br.com.alice.schedule.ServiceConfig
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import io.ktor.client.HttpClient
import io.ktor.client.plugins.ResponseException
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.delete
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.opentelemetry.api.trace.Span
import java.nio.charset.Charset
import java.util.Base64

class LivanceClient(
    private val httpClient: HttpClient = DefaultHttpClient({
        install(ContentNegotiation) {
            gsonIdentity()
        }
    }, timeoutInMillis = 30_000),
) : Spannable {

    private val apiUrl = ServiceConfig.Livance.apiUrl()
    private val username = ServiceConfig.Livance.username()
    private val password = ServiceConfig.Livance.password()

    suspend fun createAppointment(request: CreateAppointmentRequest) = span("createAppointment") { span ->
        span.setAttribute("location_id", request.locationId)
        span.setAttribute("professional_id", request.professionalId)
        span.setAttribute("start_time", request.startTime)
        span.setAttribute("date", request.date)
        span.setAttribute("patient_name", request.patient.name)

        coResultOf<String, Throwable> {
            httpClient.post("$apiUrl/v1/appointments") {
                withHeaders()
                setBody(gsonIdentity.toJson(request))
            }.bodyAsText()
        }
            .recordResponse(span)
            .map { gsonIdentity.fromJson(it, CreateAppointmentResponse::class.java) }
            .recordResult(span)
    }

    suspend fun getAppointment(appointmentId: Long) = span("getAppointment") { span ->
        span.setAttribute("appointmentId", appointmentId)

        coResultOf<String, Throwable> {
            httpClient.get("$apiUrl/v1/appointments/$appointmentId") { withHeaders() }
                .bodyAsText()
        }
            .recordResponse(span)
            .map { gsonIdentity.fromJson(it, AppointmentResponse::class.java) }
            .recordResult(span)
    }

    suspend fun cancelAppointment(appointmentId: Long, reason: String? = null) = span("cancelAppointment") { span ->
        span.setAttribute("appointmentId", appointmentId)

        coResultOf<HttpStatusCode, Throwable> {
            httpClient.delete("$apiUrl/v1/appointments/$appointmentId") {
                withHeaders()
                setBody(gsonIdentity.toJson(CancelAppointmentRequest(reason)))
            }.status
        }
            .then { span.setAttribute("status_code", it) }
            .map { it == HttpStatusCode.OK }
            .recordResult(span)
    }

    suspend fun getLocations() = span("getLocations") { span ->
        coResultOf<String, Throwable> {
            httpClient.get("$apiUrl/v1/locations") { withHeaders() }
                .bodyAsText()
        }
            .recordResponse(span)
            .map { gsonIdentity.fromJson(it, Array<LocationResponse>::class.java).toList() }
            .recordResult(span)
    }

    suspend fun getProfessionals() = span("getProfessionals") { span ->
        coResultOf<String, Throwable> {
            httpClient.get("$apiUrl/v1/professionals") { withHeaders() }
                .bodyAsText()
        }
            .recordResponse(span)
            .map { gsonIdentity.fromJson(it, Array<ProfessionalResponse>::class.java).toList() }
            .recordResult(span)
    }

    private fun HttpRequestBuilder.withHeaders() {
        header(HttpHeaders.Authorization, "Basic ${getAuthKey(username, password)}")
        header(HttpHeaders.ContentType, ContentType.Application.Json)
    }

    private fun getAuthKey(username: String, password: String) =
        "${username}:${password}"
            .toByteArray(Charset.forName("UTF-8"))
            .let { key -> Base64.getEncoder().encodeToString(key) }

    private suspend fun Result<String, Throwable>.recordResponse(span: Span) =
        this.then { span.setAttribute("response", it) }
            .thenError { error ->
                if (error is ResponseException)
                    span.setAttribute("response_error_body", error.response.bodyAsText())
            }
}
