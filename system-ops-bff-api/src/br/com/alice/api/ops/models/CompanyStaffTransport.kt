package br.com.alice.api.ops.models

import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyStaffRole
import java.time.LocalDateTime
import java.util.UUID

data class CompanyStaffRequest(
    val companyId: UUID,
    val email: String,
    val firstName: String,
    val lastName: String,
)

data class CompanyStaffResponse(
    val id: UUID,
    val email: String?,
    val firstName: String?,
    val lastName: String?,
    val role: CompanyStaffRole,
    val archivedAt: LocalDateTime?,
    val company: Company,
)


data class CompanyStaffToUpdateRequest(
    val email: String? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    val role: CompanyStaffRole? = null,
    val archivedAt: LocalDateTime? = null,
)

data class AddressRequest(
    val id: UUID? = null,
    val street: String? = null,
    val number: String? = null,
    val complement: String? = null,
    val neighborhood: String? = null,
    val state: String? = null,
    val city: String? = null,
    val zipcode: String? = null,
    val label: String? = null,
    val active: Boolean = true,
    val latitude: String? = null,
    val longitude: String? = null,
)

