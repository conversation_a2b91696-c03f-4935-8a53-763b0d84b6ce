package br.com.alice.data.layer.services

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.localDateTimeComparator
import br.com.alice.common.extensions.mapFirst
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory.buildMember
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.helpers.TestTableFactory
import br.com.alice.data.layer.models.MemberBeneficiaryModel
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.repositories.JdbiRepositoryFactory
import br.com.alice.data.layer.tables.MemberTable
import br.com.alice.data.layer.tables.PersonTable
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class MemberModelDataServiceTest : DataServiceTestHelper() {

    private val repoFactory = JdbiRepositoryFactory(mainJdbi)
    private val personRepo = repoFactory.get(PersonTable::class)
    private val personTokenService = PersonTokenServiceImpl(tokenJdbi) as PersonTokenService
    private val memberModelDataService = MemberModelDataServiceClient(httpInvoker)
    private val memberRepo = repoFactory.get(MemberTable::class)

    private val personId = PersonId()
    private val personId2 = PersonId()
    private val personId3 = PersonId()

    private val personTable = TestTableFactory.buildPersonTable(personTokenService.createForPersonId(personId).get().personPiiToken)
    private val personTable2 = TestTableFactory.buildPersonTable(personTokenService.createForPersonId(personId2).get().personPiiToken)
    private val personTable3 = TestTableFactory.buildPersonTable(personTokenService.createForPersonId(personId3).get().personPiiToken)

    @BeforeTest
    fun setup() {
        memberRepo.truncate()
        personRepo.truncate()
        personRepo.add(personTable)
        personRepo.add(personTable2)
        personRepo.add(personTable3)
    }

    @Test
    fun `#findByPerson should find only not archived`() = runBlocking<Unit> {
        val activationDate = LocalDateTime.now()

        val member = buildMember(personId).copy(
            status = MemberStatus.ACTIVE,
            archived = false,
            activationDate = activationDate,
            beneficiary = MemberBeneficiaryModel(
                type = BeneficiaryType.EMPLOYEE,
                activatedAt = activationDate
            )
        )
        memberModelDataService.add(member)

        val member2 = buildMember(personId).copy(
            archived = true,
            activationDate = activationDate.plusMinutes(50),
            beneficiary = MemberBeneficiaryModel(
                type = BeneficiaryType.DEPENDENT,
                activatedAt = activationDate.plusMinutes(50)
            )
        )
        memberModelDataService.add(member2)

        val searchResult = memberModelDataService.findByPersonAndStatus(personId, MemberStatus.ACTIVE).mapFirst()
        assertThat(searchResult).isSuccessWithDataIgnoringGivenFields(member, "createdAt", "updatedAt", "updatedBy")

        val searchResult2 = memberModelDataService.findFirstByPerson(personId)
        Assertions.assertThat(searchResult2).usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
            .usingRecursiveComparison()
            .ignoringFields("createdAt", "updatedAt", "updatedBy")
            .isEqualTo(member)
    }

    @Test
    fun `#findByBrandAndStatus should find by brand and status`() = runBlocking {
        val activationDate = LocalDateTime.now()

        val member = buildMember(
            personId = personId,
            updatedBy = UpdatedBy(userType = "Unauthenticated", userId = "undefined", environmentName = "undefined")
        ).copy(status = MemberStatus.ACTIVE, archived = false, activationDate = activationDate, brand = Brand.DUQUESA)
        memberModelDataService.add(member)

        val member2 = buildMember(
            personId = personId,
            updatedBy = UpdatedBy(userType = "Unauthenticated", userId = "undefined", environmentName = "undefined")
        ).copy(status = MemberStatus.ACTIVE, activationDate = activationDate.plusMinutes(50))
        memberModelDataService.add(member2).get()

        val searchResult = memberModelDataService.findByBrandAndStatus(MemberStatus.ACTIVE, Brand.DUQUESA, 100, 0)
        assertThat(searchResult).isSuccessWithDataIgnoringGivenFields(listOf(member), "createdAt", "updatedAt")
    }

    @Test
    fun `#findByProductId should return members`() = runBlocking {
        val productId = RangeUUID.generate()

        val activationDate = LocalDateTime.now()

        val member1 = buildMember(
            personId = personId,
            status = MemberStatus.ACTIVE,
            activationDate = activationDate,
            productId = productId,
            updatedBy = UpdatedBy(userType = "Unauthenticated", userId = "undefined", environmentName = "undefined")
        )
        val member2 = buildMember(
            personId = personId2,
            status = MemberStatus.ACTIVE,
            activationDate = activationDate,
            productId = productId,
            updatedBy = UpdatedBy(userType = "Unauthenticated", userId = "undefined", environmentName = "undefined")
        )

        memberModelDataService.add(member1)
        memberModelDataService.add(member2)

        val searchResult = memberModelDataService.find {
            where {
                this.productId.eq(productId)
                    .and(this.status.eq(MemberStatus.ACTIVE))
                    .and(this.archived.eq(false))
            }
        }
        assertThat(searchResult).isSuccessWithDataIgnoringGivenFields(listOf(member1, member2), "createdAt", "updatedAt")
    }

    @Test
    fun `#findByCanceledAt should find by canceled at null`() = runBlocking {
        val activationDate = LocalDateTime.now()

        val member = buildMember(
            personId = personId,
            updatedBy = UpdatedBy(userType = "Unauthenticated", userId = "undefined", environmentName = "undefined")
        ).copy(status = MemberStatus.CANCELED, archived = false, activationDate = activationDate, canceledAt = null)

        memberModelDataService.add(member)

        val member2 = buildMember(
            personId = personId,
            updatedBy = UpdatedBy(userType = "Unauthenticated", userId = "undefined", environmentName = "undefined")
        ).copy(status = MemberStatus.CANCELED, activationDate = activationDate.plusMinutes(50), canceledAt = LocalDateTime.now())
        memberModelDataService.add(member2).get()

        val searchResult = memberModelDataService.find { where { canceledAt.isNull() }}
        assertThat(searchResult).isSuccessWithDataIgnoringGivenFields(listOf(member), "createdAt", "updatedAt")
    }

    @Test
    fun `#findOneByBeneficiaryId should return member`() = runBlocking {
        val member = buildMember(beneficiaryId = RangeUUID.generate(), personId = personId)
        val member2 = buildMember(beneficiaryId = RangeUUID.generate(), personId = personId)
        memberModelDataService.add(member)
        memberModelDataService.add(member2)

        assertThat(memberModelDataService.findOneByBeneficiaryId(member.beneficiaryId!!))
            .isSuccessWithDataIgnoringGivenFields(
                member,
                "createdAt",
                "updatedAt",
                "updatedBy"
            )
    }

    @Test
    fun `#findByBeneficiaryIds should return member`() = runBlocking {
        val member1 = buildMember(beneficiaryId = RangeUUID.generate(), personId = personId)
        val member2 = buildMember(beneficiaryId = RangeUUID.generate(), personId = personId)
        val member3 = buildMember(beneficiaryId = RangeUUID.generate(), personId = personId)
        memberModelDataService.add(member1)
        memberModelDataService.add(member2)
        memberModelDataService.add(member3)

        assertThat(memberModelDataService.findByBeneficiaryIds(
            listOf(member1.beneficiaryId!!, member2.beneficiaryId!!)
        )).isSuccessWithDataIgnoringGivenFields(
            listOf(member1, member2),
            "createdAt",
            "updatedAt",
            "updatedBy"
        )
    }

    @Test
    fun `#findByCompanyId should return members`() = runBlocking {
        val companyId = RangeUUID.generate()
        val member1 = buildMember(companyId = companyId, personId = personId)
        val member2 = buildMember(companyId = companyId, personId = personId)
        val member3 = buildMember(personId = personId)
        memberModelDataService.add(member1)
        memberModelDataService.add(member2)
        memberModelDataService.add(member3)

        assertThat(memberModelDataService.findByCompanyId(
            companyId, IntRange(0, 10)
        )).isSuccessWithDataIgnoringGivenFields(
            listOf(member1, member2),
            "createdAt",
            "updatedAt",
            "updatedBy"
        )
    }

    @Test
    fun `#findByCompanyIds should return members`() = runBlocking {
        val companyId = RangeUUID.generate()
        val member1 = buildMember(companyId = companyId, personId = personId)
        val member2 = buildMember(companyId = companyId, personId = personId)
        val member3 = buildMember(personId = personId)
        memberModelDataService.add(member1)
        memberModelDataService.add(member2)
        memberModelDataService.add(member3)

        assertThat(memberModelDataService.findByCompanyIds(
            listOf(companyId, member3.companyId!!), IntRange(0, 10)
        )).isSuccessWithDataIgnoringGivenFields(
            listOf(member1, member2, member3),
            "createdAt",
            "updatedAt",
            "updatedBy"
        )
    }

    @Test
    fun `#findByCompanySubContractId should return members`() = runBlocking {
        val companySubContractId = RangeUUID.generate()
        val member1 = buildMember(companySubContractId = companySubContractId, personId = personId)
        val member2 = buildMember(companySubContractId = companySubContractId, personId = personId)
        val member3 = buildMember(personId = personId)
        memberModelDataService.add(member1)
        memberModelDataService.add(member2)
        memberModelDataService.add(member3)

        assertThat(memberModelDataService.findByCompanySubContractId(
            companySubContractId, IntRange(0, 10)
        )).isSuccessWithDataIgnoringGivenFields(
            listOf(member1, member2),
            "createdAt",
            "updatedAt",
            "updatedBy"
        )
    }

    @Test
    fun `#findByCompanyIdAndPersonIds should return members`() = runBlocking {
        val companyId = RangeUUID.generate()
        val member1 = buildMember(companyId = companyId, personId = personId)
        val member2 = buildMember(companyId = companyId, personId = personId2)
        val member3 = buildMember(companyId = companyId, personId = personId3)
        val member4 = buildMember(personId = personId2)
        memberModelDataService.add(member1)
        memberModelDataService.add(member2)
        memberModelDataService.add(member3)
        memberModelDataService.add(member4)

        assertThat(memberModelDataService.findByCompanyIdAndPersonIds(
            companyId, listOf(personId, personId2)
        )).isSuccessWithDataIgnoringGivenFields(
            listOf(member1, member2),
            "createdAt",
            "updatedAt",
            "updatedBy"
        )
    }

    @Test
    fun `#findByParentPersonId should return members`() = runBlocking {
        val statuses = listOf(MemberStatus.ACTIVE, MemberStatus.PENDING)
        val member = buildMember(
            personId = personId,
            parentPerson = personId2,
            status = MemberStatus.ACTIVE,
        )
        val member2 = buildMember(
            personId = personId,
            parentPerson = personId3,
            status = MemberStatus.CANCELED,
        )
        memberModelDataService.add(member)
        memberModelDataService.add(member2)

        assertThat(memberModelDataService.findByParentPersonIdAndStatuses(personId2, statuses))
            .isSuccessWithDataIgnoringGivenFields(
                listOf(member),
                "createdAt",
                "updatedAt",
                "updatedBy"
            )
    }

    @Test
    fun `#findByParentBeneficiaryId should return members`() = runBlocking {
        val parentBeneficiaryId = RangeUUID.generate()
        val member = buildMember(
            personId = personId,
            beneficiary = MemberBeneficiaryModel(
                type = BeneficiaryType.EMPLOYEE,
                parentBeneficiary = parentBeneficiaryId,
                activatedAt = LocalDateTime.now()
            )
        )
        val member2 = buildMember(
            personId = personId,
            beneficiary = MemberBeneficiaryModel(
                type = BeneficiaryType.EMPLOYEE,
                parentBeneficiary = RangeUUID.generate(),
                activatedAt = LocalDateTime.now()
            )
        )
        memberModelDataService.add(member)
        memberModelDataService.add(member2)

        assertThat(memberModelDataService.findByParentBeneficiaryId(parentBeneficiaryId))
            .isSuccessWithDataIgnoringGivenFields(
                listOf(member),
                "createdAt",
                "updatedAt",
                "updatedBy"
            )
    }

    @Test
    fun `#countByBeneficiaryIds should return members count`() = runBlocking {
        val member = buildMember(personId = personId)
        val member2 = buildMember(personId = personId2)
        val member3 = buildMember(personId = personId3)
        memberModelDataService.add(member)
        memberModelDataService.add(member2)
        memberModelDataService.add(member3)

        assertThat(memberModelDataService.countByBeneficiaryIds(
            listOf(member.beneficiaryId!!, member2.beneficiaryId!!)
        )).isSuccessWithData(2)
    }

    @Test
    fun `#findBeneficiariesByFilter should return members`() = runBlocking {
        val companyId = RangeUUID.generate()
        val beneficiaryParentId = RangeUUID.generate()
        val memberStatus = MemberStatus.PENDING
        val member1 = buildMember(companyId = companyId, personId = personId, status = memberStatus,
            beneficiary = MemberBeneficiaryModel(
                parentBeneficiary = beneficiaryParentId,
                type = BeneficiaryType.EMPLOYEE,
                activatedAt = LocalDateTime.now()
            )
        )
        val member2 = buildMember(companyId = companyId, personId = personId2)
        val member3 = buildMember(personId = personId3, beneficiary = MemberBeneficiaryModel(
                parentBeneficiary = beneficiaryParentId,
                type = BeneficiaryType.EMPLOYEE,
                activatedAt = LocalDateTime.now()
            )
        )
        memberModelDataService.add(member1)
        memberModelDataService.add(member2)
        memberModelDataService.add(member3)

        assertThat(memberModelDataService.findByFilter(
            companyId = companyId,
            beneficiaryParentId = beneficiaryParentId,
            status = memberStatus,
            range = IntRange(0, 10)
        )).isSuccessWithDataIgnoringGivenFields(
            listOf(member1),
            "createdAt",
            "updatedAt",
            "updatedBy"
        )
    }

    @Test
    fun `#findByCancellationDate should return members`() = runBlocking {
        val member1 = buildMember(
            canceledAt = LocalDateTime.now().minusDays(2),
            personId = personId
        )
        val member2 = buildMember(
            canceledAt = LocalDateTime.now().plusDays(2),
            personId = personId2
        )
        val member3 = buildMember(
            canceledAt = LocalDateTime.now(),
            personId = personId3
        )

        memberModelDataService.add(member1)
        memberModelDataService.add(member2)
        memberModelDataService.add(member3)

        assertThat(memberModelDataService.findByCancellationDate(
            LocalDate.now().minusDays(1),
            LocalDate.now().plusDays(1),
            IntRange(0, 10)
        )).isSuccessWithDataIgnoringGivenFields(
            listOf(member3),
            "createdAt",
            "updatedAt",
            "updatedBy"
        )
    }

    @Test
    fun `#countByFilter should return number of members`() = runBlocking {
        val companyId = RangeUUID.generate()
        val parentBeneficiaryId = RangeUUID.generate()

        val member1 = buildMember(
            personId = personId,
            companyId = companyId,
            status = MemberStatus.PENDING,
            beneficiary = MemberBeneficiaryModel(
                type = BeneficiaryType.EMPLOYEE,
                activatedAt = LocalDateTime.now(),
                parentBeneficiary = parentBeneficiaryId
            )
        )
        val member2 = buildMember(
            personId = personId,
            companyId = companyId,
            status = MemberStatus.ACTIVE,
            beneficiary = MemberBeneficiaryModel(
                type = BeneficiaryType.EMPLOYEE,
                activatedAt = LocalDateTime.now(),
                parentBeneficiary = parentBeneficiaryId
            )
        )
        val member3 = buildMember(
            personId = personId,
            companyId = companyId,
            status = MemberStatus.PENDING,
        )
        val member4 = buildMember(
            personId = personId,
            status = MemberStatus.PENDING,
            beneficiary = MemberBeneficiaryModel(
                type = BeneficiaryType.EMPLOYEE,
                activatedAt = LocalDateTime.now(),
                parentBeneficiary = parentBeneficiaryId
            )
        )

        memberModelDataService.add(member1)
        memberModelDataService.add(member2)
        memberModelDataService.add(member3)
        memberModelDataService.add(member4)


        assertThat(memberModelDataService.countByFilter(
            companyId, null, null
        )).isSuccessWithData(3)
        assertThat(memberModelDataService.countByFilter(
            null, MemberStatus.PENDING, null
        )).isSuccessWithData(3)
        assertThat(memberModelDataService.countByFilter(
            null, null, parentBeneficiaryId
        )).isSuccessWithData(3)
        assertThat(memberModelDataService.countByFilter(
            null, MemberStatus.PENDING, parentBeneficiaryId
        )).isSuccessWithData(2)
        assertThat(memberModelDataService.countByFilter(
            companyId, MemberStatus.PENDING, null
        )).isSuccessWithData(2)
        assertThat(memberModelDataService.countByFilter(
            companyId, null, parentBeneficiaryId
        )).isSuccessWithData(2)
        assertThat(memberModelDataService.countByFilter(
            companyId, MemberStatus.PENDING, parentBeneficiaryId
        )).isSuccessWithData(1)
    }

    @Test
    fun `#countByCompanyIdAndStatus should return number of members`() = runBlocking {
        val companyId = RangeUUID.generate()

        val member1 = buildMember(
            personId = personId,
            status = MemberStatus.ACTIVE,
            canceledAt = null,
            companyId = companyId,
            beneficiary = MemberBeneficiaryModel(
                type = BeneficiaryType.EMPLOYEE,
                activatedAt = LocalDateTime.now()
            )
        )
        val member2 = buildMember(
            personId = personId,
            status = MemberStatus.PENDING,
            canceledAt = null,
            companyId = companyId,
        )
        val member3 = buildMember(
            personId = personId,
            status = MemberStatus.ACTIVE,
            canceledAt = LocalDateTime.now(),
            companyId = companyId
        )
        val member4 = buildMember(
            personId = personId,
            status = MemberStatus.ACTIVE,
            canceledAt = null,
            companyId = RangeUUID.generate()
        )

        memberModelDataService.add(member1)
        memberModelDataService.add(member2)
        memberModelDataService.add(member3)
        memberModelDataService.add(member4)

        assertThat(memberModelDataService.countByCompanyIdAndStatus(companyId, MemberStatus.ACTIVE, BeneficiaryType.EMPLOYEE))
            .isSuccessWithData(1)

    }
}
