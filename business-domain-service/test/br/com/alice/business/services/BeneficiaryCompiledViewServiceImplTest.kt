package br.com.alice.business.services

import br.com.alice.business.client.BeneficiaryCompiledViewFilters
import br.com.alice.business.client.BeneficiaryCompiledViewQuantities
import br.com.alice.business.client.BeneficiaryOnboardingService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.converters.model.toModel
import br.com.alice.business.converters.model.toTransport
import br.com.alice.business.exceptions.BeneficiaryIsArchived
import br.com.alice.business.metrics.BeneficiaryCompiledViewMetric
import br.com.alice.business.model.PaginatedList
import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.helpers.verifyNone
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryViewInsuranceStatus
import br.com.alice.data.layer.models.MemberProductChangeSchedule
import br.com.alice.data.layer.services.BeneficiaryCompiledViewModelDataService
import br.com.alice.membership.client.MemberProductChangeScheduleService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class BeneficiaryCompiledViewServiceImplTest {
    private val beneficiaryCompiledViewDataService: BeneficiaryCompiledViewModelDataService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val beneficiaryOnboardingService: BeneficiaryOnboardingService = mockk()
    private val personService: PersonService = mockk()
    private val personClinicalAccountService: PersonClinicalAccountService = mockk()
    private val memberService: MemberService = mockk()
    private val productService: ProductService = mockk()
    private val memberProductChangeScheduleService: MemberProductChangeScheduleService = mockk()

    private val beneficiaryCompiledViewService = BeneficiaryCompiledViewServiceImpl(
        beneficiaryCompiledViewDataService,
        beneficiaryService,
        memberService,
        personService,
        personClinicalAccountService,
        beneficiaryOnboardingService,
        productService,
        memberProductChangeScheduleService
    )

    @BeforeTest
    fun setup() {
        mockkObject(BeneficiaryCompiledViewMetric)
    }

    @AfterTest
    fun clear() {
        clearAllMocks()
        unmockkAll()
    }

    @Test
    fun `#deleteFromBeneficiaryId - delete if exists related to beneficiaryId`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()
        val beneficiaryCompiledView = TestModelFactory.buildBeneficiaryCompiledView().toModel()

        coEvery {
            beneficiaryCompiledViewDataService.findOne(queryEq { where { this.beneficiaryId.eq(beneficiaryId) } })
        } returns beneficiaryCompiledView
        coEvery { beneficiaryCompiledViewDataService.delete(beneficiaryCompiledView) } returns true

        val result = beneficiaryCompiledViewService.deleteFromBeneficiaryId(beneficiaryId)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { beneficiaryCompiledViewDataService.delete(beneficiaryCompiledView)  }
    }

    @Test
    fun `#deleteFromBeneficiaryId - ignore does not exist related to beneficiaryId`() = runBlocking {
        val beneficiaryId = RangeUUID.generate()

        coEvery {
            beneficiaryCompiledViewDataService.findOne(queryEq { where { this.beneficiaryId.eq(beneficiaryId) } })
        } returns NotFoundException()

        val result = beneficiaryCompiledViewService.deleteFromBeneficiaryId(beneficiaryId)
        assertThat(result).isSuccessWithData(false)

        coVerifyNone { beneficiaryCompiledViewDataService.delete(any()) }
    }

    @Test
    fun `#createOrUpdate should create a new row`(): Unit = runBlocking {
        val personId = PersonId()
        val person = TestModelFactory.buildPerson(personId)
        val member = TestModelFactory.buildMember(personId = person.id)
        val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id)
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()
        val personClinicalAccount = TestModelFactory.buildPersonClinicalAccount(personId = person.id)
        val initialProductId = RangeUUID.generate()
        val product = TestModelFactory.buildProduct(id = initialProductId)
        val beneficiaryCompiledView = TestModelFactory.buildBeneficiaryCompiledView(
            beneficiaryId = beneficiary.id,
            memberId = member.id,
            personId = person.id,
            companyId = beneficiary.companyId,
        ).toModel()
        val eventDate = LocalDateTime.now()

        coEvery { beneficiaryService.get(any()) } returns beneficiary
        coEvery { memberService.get(any()) } returns member
        coEvery { personService.get(any()) } returns person
        coEvery { personClinicalAccountService.getByPersonId(any()) } returns personClinicalAccount
        coEvery { productService.getProduct(any()) } returns product
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryCompiledViewDataService.add(any()) } returns beneficiaryCompiledView
        coEvery { beneficiaryCompiledViewDataService.getByBeneficiaryId(any()) } returns NotFoundException()
        coEvery {
            beneficiaryCompiledViewDataService.getByPersonIdAndCompanyId(
                any(),
                any()
            )
        } returns NotFoundException()
        coEvery { beneficiaryCompiledViewDataService.update(any()) } returns beneficiaryCompiledView
        coEvery { memberProductChangeScheduleService.getSchedulesByMemberAndStatus(any(), any()) } returns
                emptyList<MemberProductChangeSchedule>().success()

        val result = beneficiaryCompiledViewService.createOrUpdate(beneficiary.id, eventDate)

        assertThat(result).isSuccessWithData(beneficiaryCompiledView.toTransport())


        coVerifyOnce { beneficiaryService.get(beneficiary.id) }
        coVerifyOnce { memberService.get(beneficiary.memberId) }
        coVerifyOnce { personService.get(member.personId) }
        coVerifyOnce { personClinicalAccountService.getByPersonId(person.id) }
        coVerifyOnce { productService.getProduct(member.productId) }
        coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id) }
        coVerifyOnce { beneficiaryCompiledViewDataService.add(any()) }
        verifyOnce { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewCreated() }
        coVerifyNone { beneficiaryCompiledViewDataService.getByBeneficiaryId(any()) }
        coVerifyNone { beneficiaryCompiledViewDataService.getByPersonIdAndCompanyId(any(), any()) }
        coVerifyNone { beneficiaryCompiledViewDataService.update(any()) }
        verifyNone { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewUpdated() }
    }

    @Test
    fun `#createOrUpdate should create a new row with parent`(): Unit = runBlocking {
        val personId = PersonId()
        val person = TestModelFactory.buildPerson(personId)
        val parentPerson = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id, parentPerson = parentPerson.id)
        val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id, parentPerson = parentPerson.id)
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()
        val personClinicalAccount = TestModelFactory.buildPersonClinicalAccount(personId = person.id)
        val initialProductId = RangeUUID.generate()
        val product = TestModelFactory.buildProduct(id = initialProductId)
        val beneficiaryCompiledView = TestModelFactory.buildBeneficiaryCompiledView(
            beneficiaryId = beneficiary.id,
            memberId = member.id,
            personId = person.id,
            companyId = beneficiary.companyId,
            parentPerson = parentPerson
        ).toModel()
        val eventDate = LocalDateTime.now()

        coEvery { beneficiaryService.get(any()) } returns beneficiary
        coEvery { memberService.get(any()) } returns member
        coEvery { personService.get(person.id) } returns person
        coEvery { personService.get(parentPerson.id) } returns parentPerson
        coEvery { personClinicalAccountService.getByPersonId(any()) } returns personClinicalAccount
        coEvery { productService.getProduct(any()) } returns product
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryCompiledViewDataService.add(any()) } returns beneficiaryCompiledView
        coEvery { beneficiaryCompiledViewDataService.getByBeneficiaryId(any()) } returns NotFoundException()
        coEvery {
            beneficiaryCompiledViewDataService.getByPersonIdAndCompanyId(
                any(),
                any()
            )
        } returns NotFoundException()
        coEvery { beneficiaryCompiledViewDataService.update(any()) } returns beneficiaryCompiledView
        coEvery { memberProductChangeScheduleService.getSchedulesByMemberAndStatus(any(), any()) } returns emptyList<MemberProductChangeSchedule>().success()

        val result = beneficiaryCompiledViewService.createOrUpdate(beneficiary.id, eventDate)

        assertThat(result).isSuccessWithData(beneficiaryCompiledView.toTransport())


        coVerifyOnce { beneficiaryService.get(beneficiary.id) }
        coVerifyOnce { memberService.get(beneficiary.memberId) }
        coVerifyOnce { personService.get(member.personId) }
        coVerifyOnce { personService.get(parentPerson.id) }
        coVerifyOnce { personClinicalAccountService.getByPersonId(person.id) }
        coVerifyOnce { productService.getProduct(member.productId) }
        coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id) }
        coVerifyOnce { beneficiaryCompiledViewDataService.add(any()) }
        verifyOnce { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewCreated() }
        coVerifyNone { beneficiaryCompiledViewDataService.getByBeneficiaryId(any()) }
        coVerifyNone { beneficiaryCompiledViewDataService.getByPersonIdAndCompanyId(any(), any()) }
        coVerifyNone { beneficiaryCompiledViewDataService.update(any()) }
        verifyNone { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewUpdated() }
    }

    @Test
    fun `#createOrUpdate should update an existing row by beneficiary id`() = runBlocking {
        val personId = PersonId()
        val person = TestModelFactory.buildPerson(personId)
        val member = TestModelFactory.buildMember(personId = person.id)
        val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id)
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()
        val personClinicalAccount = TestModelFactory.buildPersonClinicalAccount(personId = person.id)
        val initialProductId = RangeUUID.generate()
        val product = TestModelFactory.buildProduct(id = initialProductId)
        val beneficiaryCompiledView = TestModelFactory.buildBeneficiaryCompiledView(
            beneficiaryId = beneficiary.id,
            memberId = member.id,
            viewUpdatedAt = LocalDateTime.now().minusDays(2)
        ).toModel()

        coEvery { beneficiaryService.get(any()) } returns beneficiary
        coEvery { memberService.get(any()) } returns member
        coEvery { personService.get(any()) } returns person
        coEvery { personClinicalAccountService.getByPersonId(any()) } returns personClinicalAccount
        coEvery { productService.getProduct(any()) } returns product
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryCompiledViewDataService.add(any()) } returns DuplicatedItemException("")
        coEvery { beneficiaryCompiledViewDataService.getByBeneficiaryId(any()) } returns beneficiaryCompiledView
        coEvery { beneficiaryCompiledViewDataService.update(any()) } returns beneficiaryCompiledView
        coEvery { memberProductChangeScheduleService.getSchedulesByMemberAndStatus(any(), any()) } returns emptyList<MemberProductChangeSchedule>().success()

        val result = beneficiaryCompiledViewService.createOrUpdate(beneficiary.id, LocalDateTime.now())

        assertThat(result).isSuccessWithData(beneficiaryCompiledView.toTransport())


        coVerifyOnce { beneficiaryService.get(beneficiary.id) }
        coVerifyOnce { memberService.get(beneficiary.memberId) }
        coVerifyOnce { personService.get(member.personId) }
        coVerifyOnce { personClinicalAccountService.getByPersonId(person.id) }
        coVerifyOnce { productService.getProduct(member.productId) }
        coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id) }
        coVerifyOnce { beneficiaryCompiledViewDataService.add(any()) }
        verifyNone { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewCreated() }
        coVerifyOnce { beneficiaryCompiledViewDataService.add(any()) }
        coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id) }
        coVerifyOnce { beneficiaryCompiledViewDataService.update(any()) }
        verifyOnce { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewUpdated() }
    }

    @Test
    fun `#createOrUpdate should update an existing row by company and person`() = runBlocking {
        val personId = PersonId()
        val person = TestModelFactory.buildPerson(personId)
        val member = TestModelFactory.buildMember(personId = person.id)
        val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id)
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()
        val personClinicalAccount = TestModelFactory.buildPersonClinicalAccount(personId = person.id)
        val initialProductId = RangeUUID.generate()
        val product = TestModelFactory.buildProduct(id = initialProductId)

        val otherMember = TestModelFactory.buildMember(personId = person.id)
        val otherBeneficiary = TestModelFactory.buildBeneficiary(memberId = otherMember.id)

        val otherBeneficiaryCompiledView = TestModelFactory.buildBeneficiaryCompiledView(
            beneficiaryId = otherBeneficiary.id,
            memberId = otherMember.id,
            viewUpdatedAt = LocalDateTime.now().minusDays(2),
            personId = person.id,
            companyId = beneficiary.companyId,
        ).toModel()

        coEvery { beneficiaryService.get(any()) } returns beneficiary
        coEvery { memberService.get(any()) } returns member
        coEvery { personService.get(any()) } returns person
        coEvery { personClinicalAccountService.getByPersonId(any()) } returns personClinicalAccount
        coEvery { productService.getProduct(any()) } returns product
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryCompiledViewDataService.add(any()) } returns DuplicatedItemException("")
        coEvery { beneficiaryCompiledViewDataService.getByBeneficiaryId(beneficiary.id) } returns NotFoundException()
        coEvery {
            beneficiaryCompiledViewDataService.getByPersonIdAndCompanyId(
                any(),
                any()
            )
        } returns otherBeneficiaryCompiledView
        coEvery { memberService.getCurrent(any()) } returns member
        coEvery { beneficiaryCompiledViewDataService.update(any()) } returns otherBeneficiaryCompiledView
        coEvery { memberProductChangeScheduleService.getSchedulesByMemberAndStatus(any(), any()) } returns emptyList<MemberProductChangeSchedule>().success()

        val result = beneficiaryCompiledViewService.createOrUpdate(beneficiary.id, LocalDateTime.now())

        assertThat(result).isSuccessWithData(otherBeneficiaryCompiledView.toTransport())

        coVerifyOnce { beneficiaryService.get(beneficiary.id) }
        coVerifyOnce { memberService.get(beneficiary.memberId) }
        coVerifyOnce { personService.get(member.personId) }
        coVerifyOnce { personClinicalAccountService.getByPersonId(person.id) }
        coVerifyOnce { productService.getProduct(member.productId) }
        coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id) }
        coVerifyOnce { beneficiaryCompiledViewDataService.add(any()) }
        verifyNone { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewCreated() }
        coVerifyOnce { beneficiaryCompiledViewDataService.getByBeneficiaryId(beneficiary.id) }
        coVerifyOnce {
            beneficiaryCompiledViewDataService.getByPersonIdAndCompanyId(
                otherBeneficiaryCompiledView.personId,
                otherBeneficiaryCompiledView.companyId
            )
        }
        coVerifyOnce { beneficiaryCompiledViewDataService.add(any()) }
        coVerifyOnce { beneficiaryCompiledViewDataService.update(any()) }
        verifyOnce { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewUpdated() }
    }

    @Test
    fun `#createOrUpdate should update an existing row with prioritized membership`() = runBlocking {
        val personId = PersonId()
        val person = TestModelFactory.buildPerson(personId)
        val member = TestModelFactory.buildMember(personId = person.id)
        val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id)
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()
        val personClinicalAccount = TestModelFactory.buildPersonClinicalAccount(personId = person.id)
        val initialProductId = RangeUUID.generate()
        val product = TestModelFactory.buildProduct(id = initialProductId)
        val currentMember = TestModelFactory.buildMember(personId = person.id)
        val currentBeneficiary = TestModelFactory.buildBeneficiary(
            memberId = currentMember.id,
            companyId = beneficiary.companyId
        )

        val existingBeneficiaryCompiledView = TestModelFactory.buildBeneficiaryCompiledView(
            memberId = member.id,
            viewUpdatedAt = LocalDateTime.now().minusDays(2),
            personId = person.id,
            companyId = beneficiary.companyId,
        ).toModel()

        val newBeneficiaryCompiledView = TestModelFactory.buildBeneficiaryCompiledView(
            beneficiaryId = currentBeneficiary.id,
            memberId = currentMember.id,
            viewUpdatedAt = LocalDateTime.now().minusDays(2),
            personId = person.id,
            companyId = beneficiary.companyId,
        ).toModel()

        coEvery { beneficiaryService.get(any()) } returns currentBeneficiary
        coEvery { memberService.get(any()) } returns currentMember
        coEvery { personService.get(any()) } returns person
        coEvery { personClinicalAccountService.getByPersonId(any()) } returns personClinicalAccount
        coEvery { productService.getProduct(any()) } returns product
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryCompiledViewDataService.add(any()) } returns DuplicatedItemException("")
        coEvery { beneficiaryCompiledViewDataService.getByBeneficiaryId(currentBeneficiary.id) } returns NotFoundException()
        coEvery {
            beneficiaryCompiledViewDataService.getByPersonIdAndCompanyId(
                any(),
                any()
            )
        } returns existingBeneficiaryCompiledView
        coEvery { memberService.getCurrent(any()) } returns currentMember
        coEvery { beneficiaryCompiledViewDataService.update(any()) } returns newBeneficiaryCompiledView
        coEvery { memberProductChangeScheduleService.getSchedulesByMemberAndStatus(any(), any()) } returns emptyList<MemberProductChangeSchedule>().success()

        val result = beneficiaryCompiledViewService.createOrUpdate(beneficiary.id, LocalDateTime.now())

        assertThat(result).isSuccessWithData(newBeneficiaryCompiledView.toTransport())

        coVerifyOnce { beneficiaryService.get(beneficiary.id) }
        coVerifyOnce { memberService.get(currentBeneficiary.memberId) }
        coVerifyOnce { personService.get(currentMember.personId) }
        coVerifyOnce { personClinicalAccountService.getByPersonId(person.id) }
        coVerifyOnce { productService.getProduct(currentMember.productId) }
        coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(currentBeneficiary.id) }
        coVerifyOnce { beneficiaryCompiledViewDataService.add(any()) }
        verifyNone { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewCreated() }
        coVerifyOnce { beneficiaryCompiledViewDataService.getByBeneficiaryId(currentBeneficiary.id) }
        coVerifyOnce {
            beneficiaryCompiledViewDataService.getByPersonIdAndCompanyId(
                existingBeneficiaryCompiledView.personId,
                existingBeneficiaryCompiledView.companyId
            )
        }
        coVerifyOnce { beneficiaryCompiledViewDataService.add(any()) }
        coVerifyOnce { beneficiaryCompiledViewDataService.update(any()) }
        verifyOnce { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewUpdated() }
    }

    @Test
    fun `#createOrUpdate should not update an existing row because of a later eventDate when different beneficiary`() =
        runBlocking {
            val personId = PersonId()
            val person = TestModelFactory.buildPerson(personId)
            val member = TestModelFactory.buildMember(personId = person.id)
            val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id)
            val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()
            val personClinicalAccount = TestModelFactory.buildPersonClinicalAccount(personId = person.id)
            val initialProductId = RangeUUID.generate()
            val product = TestModelFactory.buildProduct(id = initialProductId)
            val eventDate = LocalDateTime.now()
            val beneficiaryCompiledView = TestModelFactory.buildBeneficiaryCompiledView(
                companyId = beneficiary.companyId,
                personId = person.id,
                beneficiaryId = beneficiary.id,
                memberId = member.id,
                viewUpdatedAt = eventDate.minusDays(2)
            ).toModel()

            coEvery { beneficiaryService.get(any()) } returns beneficiary
            coEvery { memberService.get(any()) } returns member
            coEvery { personService.get(any()) } returns person
            coEvery { personClinicalAccountService.getByPersonId(any()) } returns personClinicalAccount
            coEvery { productService.getProduct(any()) } returns product
            coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding
            coEvery { beneficiaryCompiledViewDataService.add(any()) } returns DuplicatedItemException("")
            coEvery { beneficiaryCompiledViewDataService.getByBeneficiaryId(beneficiary.id) } returns beneficiaryCompiledView
            coEvery {
                beneficiaryCompiledViewDataService.getByPersonIdAndCompanyId(any(), any())
            } returns beneficiaryCompiledView
            coEvery { memberProductChangeScheduleService.getSchedulesByMemberAndStatus(any(), any()) } returns emptyList<MemberProductChangeSchedule>().success()
            coEvery { beneficiaryCompiledViewDataService.update(any()) } returns beneficiaryCompiledView

            val result = beneficiaryCompiledViewService.createOrUpdate(beneficiary.id, eventDate.minusDays(3))

            assertThat(result).isSuccessWithDataFields(
                beneficiaryCompiledView.toTransport(),
                "companyId",
                "personId",
                "beneficaryId",
                "memberId"
            )

            coVerifyOnce { beneficiaryService.get(beneficiary.id) }
            coVerifyOnce { memberService.get(beneficiary.memberId) }
            coVerifyOnce { personService.get(member.personId) }
            coVerifyOnce { personClinicalAccountService.getByPersonId(person.id) }
            coVerifyOnce { productService.getProduct(member.productId) }
            coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id) }
            coVerifyOnce { beneficiaryCompiledViewDataService.add(any()) }
            verifyNone { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewCreated() }
            coVerifyOnce { beneficiaryCompiledViewDataService.getByBeneficiaryId(beneficiary.id) }
            coVerifyNone { beneficiaryCompiledViewDataService.getByPersonIdAndCompanyId(any(), any()) }
            coVerifyNone { beneficiaryCompiledViewDataService.update(any()) }
            verifyNone { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewUpdated() }
        }

    @Test
    fun `#createOrUpdate should not update an existing row because of member mismatch`() = runBlocking {
        val personId = PersonId()
        val person = TestModelFactory.buildPerson(personId)
        val member = TestModelFactory.buildMember(personId = person.id)
        val differentMember = TestModelFactory.buildMember(personId = person.id)
        val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id)
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()
        val personClinicalAccount = TestModelFactory.buildPersonClinicalAccount(personId = person.id)
        val initialProductId = RangeUUID.generate()
        val product = TestModelFactory.buildProduct(id = initialProductId)

        val otherMember = TestModelFactory.buildMember(personId = person.id)
        val otherBeneficiary = TestModelFactory.buildBeneficiary(memberId = otherMember.id)

        val otherBeneficiaryCompiledView = TestModelFactory.buildBeneficiaryCompiledView(
            beneficiaryId = otherBeneficiary.id,
            memberId = otherMember.id,
            viewUpdatedAt = LocalDateTime.now().minusDays(2),
            personId = person.id,
            companyId = beneficiary.companyId,
        ).toModel()

        coEvery { beneficiaryService.get(any()) } returns beneficiary
        coEvery { memberService.get(any()) } returns member
        coEvery { personService.get(any()) } returns person
        coEvery { personClinicalAccountService.getByPersonId(any()) } returns personClinicalAccount
        coEvery { productService.getProduct(any()) } returns product
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryCompiledViewDataService.add(any()) } returns DuplicatedItemException("")
        coEvery { beneficiaryCompiledViewDataService.getByBeneficiaryId(beneficiary.id) } returns NotFoundException()
        coEvery {
            beneficiaryCompiledViewDataService.getByPersonIdAndCompanyId(
                any(),
                any()
            )
        } returns otherBeneficiaryCompiledView
        coEvery { memberService.getCurrent(any()) } returns differentMember
        coEvery { beneficiaryCompiledViewDataService.update(any()) } returns otherBeneficiaryCompiledView
        coEvery { memberProductChangeScheduleService.getSchedulesByMemberAndStatus(any(), any()) } returns
                emptyList<MemberProductChangeSchedule>().success()

        val result = beneficiaryCompiledViewService.createOrUpdate(beneficiary.id, LocalDateTime.now())

        assertThat(result).isSuccessWithData(otherBeneficiaryCompiledView.toTransport())

        coVerifyOnce { beneficiaryService.get(beneficiary.id) }
        coVerifyOnce { memberService.get(beneficiary.memberId) }
        coVerifyOnce { personService.get(member.personId) }
        coVerifyOnce { personClinicalAccountService.getByPersonId(person.id) }
        coVerifyOnce { productService.getProduct(member.productId) }
        coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id) }
        coVerifyOnce { beneficiaryCompiledViewDataService.add(any()) }
        verifyNone { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewCreated() }
        coVerifyOnce { beneficiaryCompiledViewDataService.getByBeneficiaryId(beneficiary.id) }
        coVerifyOnce {
            beneficiaryCompiledViewDataService.getByPersonIdAndCompanyId(
                otherBeneficiaryCompiledView.personId,
                otherBeneficiaryCompiledView.companyId
            )
        }
        coVerifyOnce { beneficiaryCompiledViewDataService.add(any()) }
        coVerifyNone { beneficiaryCompiledViewDataService.update(any()) }
        verifyNone { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewUpdated() }
    }

    @Test
    fun `#createOrUpdate should not create or update a new row due to beneficiary being archived`() = runBlocking {
        val personId = PersonId()
        val person = TestModelFactory.buildPerson(personId)
        val member = TestModelFactory.buildMember(personId = person.id)
        val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id, archived = true)
        val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()
        val personClinicalAccount = TestModelFactory.buildPersonClinicalAccount(personId = person.id)
        val initialProductId = RangeUUID.generate()
        val product = TestModelFactory.buildProduct(id = initialProductId)
        val beneficiaryCompiledView = TestModelFactory.buildBeneficiaryCompiledView(
            beneficiaryId = beneficiary.id,
            memberId = member.id,
            personId = person.id,
            companyId = beneficiary.companyId,
        ).toModel()
        val eventDate = LocalDateTime.now()

        coEvery { beneficiaryService.get(any()) } returns beneficiary
        coEvery { memberService.get(any()) } returns member
        coEvery { personService.get(any()) } returns person
        coEvery { personClinicalAccountService.getByPersonId(any()) } returns personClinicalAccount
        coEvery { productService.getProduct(any()) } returns product
        coEvery { beneficiaryOnboardingService.findByBeneficiaryId(any()) } returns beneficiaryOnboarding
        coEvery { beneficiaryCompiledViewDataService.add(any()) } returns beneficiaryCompiledView
        coEvery { beneficiaryCompiledViewDataService.getByBeneficiaryId(any()) } returns NotFoundException()
        coEvery {
            beneficiaryCompiledViewDataService.getByPersonIdAndCompanyId(
                any(),
                any()
            )
        } returns NotFoundException()
        coEvery { beneficiaryCompiledViewDataService.update(any()) } returns beneficiaryCompiledView

        val result = beneficiaryCompiledViewService.createOrUpdate(beneficiary.id, eventDate)

        assertThat(result).isFailureOfType(BeneficiaryIsArchived::class)

        coVerifyOnce { beneficiaryService.get(beneficiary.id) }
        coVerifyNone { memberService.get(beneficiary.memberId) }
        coVerifyNone { personService.get(member.personId) }
        coVerifyNone { personClinicalAccountService.getByPersonId(person.id) }
        coVerifyNone { productService.getProduct(member.productId) }
        coVerifyNone { beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id) }
        coVerifyNone { beneficiaryCompiledViewDataService.add(any()) }
        verifyNone { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewCreated() }
        coVerifyNone { beneficiaryCompiledViewDataService.getByBeneficiaryId(any()) }
        coVerifyNone { beneficiaryCompiledViewDataService.getByPersonIdAndCompanyId(any(), any()) }
        coVerifyNone { beneficiaryCompiledViewDataService.update(any()) }
        verifyNone { BeneficiaryCompiledViewMetric.countBeneficiaryCompiledViewUpdated() }
    }

    @Test
    fun `#getFiltered calls dataService filtering by company`() = runBlocking {
        val companyId = UUID.randomUUID()
        val beneficiaryCompiledViews = listOf(
            TestModelFactory.buildBeneficiaryCompiledView().toModel(),
            TestModelFactory.buildBeneficiaryCompiledView().toModel(),
        )

        val filter = BeneficiaryCompiledViewFilters(
            companyId = companyId,
            range = 0..9,
        )

        coEvery { beneficiaryCompiledViewDataService.count(any()) } returns beneficiaryCompiledViews.size.success()
        coEvery { beneficiaryCompiledViewDataService.find(any()) } returns beneficiaryCompiledViews.success()

        val result = beneficiaryCompiledViewService.getPaginated(filter)
        assertThat(result).isSuccessWithData(
            PaginatedList(
                beneficiaryCompiledViews.map { it.toTransport() },
                beneficiaryCompiledViews.size,
                filter.range
            )
        )

        coVerifyOnce { beneficiaryCompiledViewDataService.count(queryEq { where { this.companyId.eq(companyId) } }) }
        coVerifyOnce {
            beneficiaryCompiledViewDataService.find(queryEq { where {
                this.companyId.eq(companyId) }
                .offset { 0 }
                .limit { 10 }
                .orderBy { personName }
            })
        }
    }

    @Test
    fun `#statusQuantities should calls dataService`() = runBlocking {
        val companyId = UUID.randomUUID()
        val typeCount = listOf(CountByValues(listOf("EMPLOYEE"), 22))
        val statusCount = listOf(CountByValues(listOf("PENDING"), 23))
        val productCount = listOf(CountByValues(listOf("aabbccc"), 100))
        val onboardingPhaseCount = listOf(CountByValues(listOf("READY_TO_ONBOARD"), 100))
        val insuranceStatus = listOf(BeneficiaryViewInsuranceStatus.ACTIVE)

        coEvery { beneficiaryCompiledViewDataService.countGrouped(queryEq { where { this.companyId.eq(companyId) and this.insuranceStatus.inList(insuranceStatus) }.groupBy { listOf(this.beneficiaryType) } }) } returns typeCount.success()
        coEvery { beneficiaryCompiledViewDataService.countGrouped(queryEq { where { this.companyId.eq(companyId) and this.insuranceStatus.inList(insuranceStatus) }.groupBy { listOf(this.insuranceStatus) } }) } returns statusCount.success()
        coEvery { beneficiaryCompiledViewDataService.countGrouped(queryEq { where { this.companyId.eq(companyId) and this.insuranceStatus.inList(insuranceStatus) }.groupBy { listOf(this.productId) } }) } returns productCount.success()
        coEvery { beneficiaryCompiledViewDataService.countGrouped(queryEq { where { this.companyId.eq(companyId) and this.insuranceStatus.inList(insuranceStatus) }.groupBy { listOf(this.lastOnboardingPhase) } }) } returns onboardingPhaseCount.success()

        val result = beneficiaryCompiledViewService.quantitiesByCompanyAndStatus(companyId, insuranceStatus)
        assertThat(result).isSuccessWithData(BeneficiaryCompiledViewQuantities(
            beneficiaryType = typeCount,
            insuranceStatus = statusCount,
            productsId = productCount,
            onboardingPhase = onboardingPhaseCount
        ))

        coVerify(exactly = 4) { beneficiaryCompiledViewDataService.countGrouped(any()) }
    }
}
