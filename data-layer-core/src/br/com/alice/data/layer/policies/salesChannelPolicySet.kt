package br.com.alice.data.layer.policies


import br.com.alice.data.layer.BACKOFFICE_BFF_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.BOTTINI_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.SALES_CHANNEL_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.SALES_CHANNEL_DOMAIN_SERVICE_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.policies.features.viewBeneficiary
import br.com.alice.data.layer.policies.features.viewMember
import br.com.alice.data.layer.policies.features.viewMemberPrice
import br.com.alice.data.layer.subjects.Unauthenticated

val salesChannelPolicySet = policySet {
    at(SALES_CHANNEL_DOMAIN_SERVICE_ROOT_SERVICE_NAME) {
        who(Unauthenticated::class) {
            includes(viewUpdateCreateSalesFirm)
            includes(viewUpdateCreateSalesFirmStaff)
            includes(viewCreateUpdateSalesFirmAgentPartnership)
            includes(viewUpdateCreateOngoingCompanyDeal)
            includes(viewCompany)
            includes(viewCountUpdateSalesAgent)
            includes(createUpdateSalesAgent)
            includes(viewCreateUpdateHubspotTicket)
        }
        who(SalesFirmStaff::class) {
            includes(viewCountSalesFirmStaff)
            includes(viewCountOngoingCompanyDeal)
            includes(viewCompany)
        }
    }

    at(SALES_CHANNEL_API_ROOT_SERVICE_NAME) {
        who(Unauthenticated::class) {
            includes(viewCountSalesFirm)
            includes(viewCountSalesFirmStaff)
            includes(viewUpdateCreateOngoingCompanyDeal)
            includes(viewCountUpdateSalesAgent)
            includes(createUpdateSalesAgent)
            includes(viewHubspotTicket)
            includes(viewCompany)
        }
        who(SalesFirmStaff::class) {
            includes(viewCountSalesFirm)
            includes(viewCountSalesFirmStaff)
            includes(viewCountOngoingCompanyDeal)
            includes(viewBeneficiaryCompiledView)
            includes(viewAppointmentSchedule)
            includes(viewCompany)
            includes(viewBillingAccountableParty)
            includes(viewHubspotTicket)
            includes(salesViewInvoicePayments)
            includes(viewPerson)
            includes(viewProduct)
            includes(viewProductPriceListing)
            includes(viewPriceListing)
            includes(viewCountUpdateSalesAgent)
            includes(viewMember)
            includes(viewMemberPrice)
            includes(viewBeneficiary)
            includes(viewCreateUpdateSalesFirmAgentPartnership)
            includes(createUpdateSalesAgent)
        }
        who(SalesAgent::class) {
            includes(salesViewInvoicePayments)
            includes(viewAppointmentSchedule)
            includes(viewBeneficiaryCompiledView)
            includes(viewBillingAccountableParty)
            includes(viewCompany)
            includes(viewCountOngoingCompanyDeal)
            includes(viewCountSalesFirm)
            includes(viewCountSalesFirmStaff)
            includes(viewCountUpdateSalesAgent)
            includes(viewHubspotTicket)
            includes(viewPerson)
            includes(viewPriceListing)
            includes(viewProduct)
            includes(viewProductPriceListing)
            includes(viewMember)
            includes(viewMemberPrice)
            includes(viewBeneficiary)
        }
        match("Product and Tech", { subject is StaffModel && subject.isProductTech() }) {
            includes(viewCountUpdateSalesAgent)
            includes(createUpdateSalesAgent)
            includes(viewCreateUpdateSalesFirmAgentPartnership)
        }
    }

    at(BOTTINI_DOMAIN_ROOT_SERVICE_NAME) {
        who(Unauthenticated::class) {
            includes(viewCountSalesFirm)
            includes(viewCountSalesFirmStaff)
            includes(viewUpdateCreateOngoingCompanyDeal)
            includes(viewCountSalesAgent)
        }
    }

    at(BACKOFFICE_BFF_API_ROOT_SERVICE_NAME) {
        match("Product and Tech", { subject is StaffModel && subject.isProductTech() }) {
            includes(viewCountUpdateSalesAgent)
            includes(createUpdateSalesAgent)
        }
    }
}

val viewUpdateCreateSalesFirm = policySet {
    describe("can create, update and view") {
        can(View, Update, Create) {
            resources(SalesFirm::class)
        }
    }
}

val viewUpdateCreateSalesFirmStaff = policySet {
    describe("can create, update and view") {
        can(View, Update, Create) {
            resources(SalesFirmStaff::class)
        }
    }
}

val viewUpdateCreateOngoingCompanyDeal = policySet {
    describe("can create, update and view") {
        can(View, Update, Create) {
            resources(OngoingCompanyDeal::class)
        }
    }
}

val viewCountSalesFirm = policySet {
    describe("can view and count") {
        can(View, Count) {
            resources(SalesFirm::class)
        }
    }
}

val viewCountSalesFirmStaff = policySet {
    describe("can view and count") {
        can(View, Update, Create) {
            resources(SalesFirmStaff::class)
        }
    }
}

val viewCountOngoingCompanyDeal = policySet {
    describe("can view and count") {
        can(View, Count) {
            resources(OngoingCompanyDeal::class)
        }
    }
}

val salesViewInvoicePayments = policySet {
    describe("can view") {
        can(View) {
            resources(InvoicePaymentModel::class)
            resources(MemberInvoiceGroupModel::class)
            resources(PaymentDetailModel::class)
            resources(ResourceSignTokenModel::class)
        }
        can(View, Count) {
            resources(InvoiceLiquidationModel::class)
        }
    }
}


val viewBeneficiaryCompiledView = policySet {
    match("can view", { action is View || action is Count }) {
        match("any BeneficiaryCompiledView") { resourceIs(BeneficiaryCompiledViewModel::class) }
    }
}

val viewCompany = policySet {
    can(View, Count) {
        resources(
            CompanyModel::class,
            CompanyContractModel::class,
            CompanySubContractModel::class,
            GenericFileVault::class
        )
    }
}

val viewBillingAccountableParty = policySet {
    can(View, Count) {
        resources(BillingAccountablePartyModel::class)
    }
}

val viewHubspotTicket = policySet {
    can(View) {
        resources(HubspotTicket::class)
    }
}

val viewCountUpdateSalesAgent = policySet {
    can(View, Count, Update) {
        resources(SalesAgent::class)
    }
}

val viewCountSalesAgent = policySet {
    can(View, Count) {
        resources(SalesAgent::class)
    }
}

val createUpdateSalesAgent = policySet {
    describe("can create, update and view") {
        can(Update, Create) {
            resources(SalesAgent::class)
        }
    }
}

val viewCreateUpdateSalesFirmAgentPartnership = policySet {
    describe("can view, create and update") {
        can(View, Update, Create) {
            resources(SalesFirmAgentPartnership::class)
        }
    }
}

val viewCreateUpdateCountVicProductOption = policySet {
    describe("can create, update, view and count") {
        can(Update, Create, Count, View) {
            resources(VicProductOption::class)
        }
    }
}

val viewAppointmentSchedule = policySet {
    allows(AppointmentScheduleModel::class, View, Count)
}

val viewCreateUpdateHubspotTicket = policySet {
    describe("can view, create and update") {
        can(View, Create, Update) {
            resources(HubspotTicket::class)
        }
    }
}

val viewCreateUpdateCountProductGroups = policySet {
    describe("can create, update, view and count") {
        can(Update, Create, Count, View) {
            resources(ProductGroupModel::class)
        }
    }
}

val viewPerson = policySet {
    allows(PersonModel::class, View, Count)
}

val viewProduct = policySet {
    allows(ProductModel::class, View, Count)
}

val viewProductPriceListing = policySet {
    allows(ProductPriceListingModel::class, View, Count)
}

val viewPriceListing = policySet {
    allows(PriceListingModel::class, View, Count)
}
