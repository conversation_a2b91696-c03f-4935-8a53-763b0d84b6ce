package br.com.alice.moneyin.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InvoicePaymentSource
import br.com.alice.moneyin.ServiceConfig
import br.com.alice.moneyin.client.MoneyInResourceSignTokenService
import br.com.alice.moneyin.models.InvoicePaymentWithPortalUrl
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.spyk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class PortalUrlGeneratorServiceTest : MockedTestHelper() {
    private val moneyInResourceSignTokenService: MoneyInResourceSignTokenService = mockk()

    private val service = PortalUrlGeneratorServiceImpl(
        moneyInResourceSignTokenService
    )

    private val spyService = spyk(service)

    @Test
    fun `#mountPortalUrl should return a valid URL`() = runBlocking {
        val invoicePaymentId = RangeUUID.generate()
        val token = RangeUUID.generateUUIDv7()
        val expectedUrl = "${ServiceConfig.Payment.invoiceUrl()}/${invoicePaymentId}?token=${token}"

        coEvery {
            moneyInResourceSignTokenService.getSignTokenForMoneyInBff(invoicePaymentId)
        } returns token

        val result = service.mountPortalUrl(invoicePaymentId)

        ResultAssert.assertThat(result).isSuccessWithData(expectedUrl)

        coVerifyOnce { moneyInResourceSignTokenService.getSignTokenForMoneyInBff(any()) }
    }

    @Test
    fun `#mountPortalUrlForInvoicePayments should return a list with invoice payment and portal url`() = runBlocking {
        val invoicePayment1 = TestModelFactory.buildInvoicePayment(
            source = InvoicePaymentSource.ITAU
        )
        val invoicePayment2 = TestModelFactory.buildInvoicePayment(
            source = InvoicePaymentSource.ITAU
        )

        val invoicePayments = listOf(
            invoicePayment1,
            invoicePayment2
        )

        val expected = listOf(
            InvoicePaymentWithPortalUrl(
                invoicePayment1.id,
                "portalUrl1"
            ),
            InvoicePaymentWithPortalUrl(
                invoicePayment2.id,
                "portalUrl2"
            )
        )

        coEvery { spyService.mountPortalUrl(invoicePayment1.id) } returns "portalUrl1"
        coEvery { spyService.mountPortalUrl(invoicePayment2.id) } returns "portalUrl2"

        val result = spyService.mountPortalUrlForInvoicePayments(invoicePayments)

        ResultAssert.assertThat(result).isSuccessWithData(expected)

        coVerify(exactly = 2) { spyService.mountPortalUrl(any()) }
    }
}
