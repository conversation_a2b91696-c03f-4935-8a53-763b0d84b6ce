package br.com.alice.data.layer.policies

import br.com.alice.data.layer.EXEC_INDICATOR_BACKFILL_SERVICE_NAME
import br.com.alice.data.layer.EXEC_INDICATOR_RECURRING_SERVICE_NAME
import br.com.alice.data.layer.EXEC_INDICATOR_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.policies.features.*
import br.com.alice.data.layer.subjects.Unauthenticated

val execIndicatorPolicySet = policySet {
    match("at exec-indicator-domain-service", { rootService.name == EXEC_INDICATOR_ROOT_SERVICE_NAME }) {
        includes(sync)
        includes(execution)
        includes(updateExecutionGroupTag)
        includes(authorization)
        includes(createAuthorizationTicket)
        includes(createTicket)
        includes(testRequestCreateGuia)
        includes(createGuia)
        includes(createElibilityCheck)
        includes(eventinderSync)
        includes(rollbackProcedureExecution)
        includes(totvsUpsert)
        includes(canUpdateHealthcareBundleProviderName)
        includes(canViewCreateAndUpdateTotvsGuia)
        includes(canViewCreateAndUpdateAttachmentOpme)
        includes(canViewGlossAuthorizationInfo)
        includes(canViewHealthcareResourceGroupAssociation)
        includes(canViewCreateAndUpdateGuiaWithProcedures)
        includes(canViewAndUpdateHealthcareResource)
        includes(canViewFileVault)
        allows(HospitalizationInfoModel::class, View, Count, Create, Update)
        allows(HealthCondition::class, View, Count)
        allows(HealthcareBundleModel::class, View, Update)
        allows(AttachmentChemotherapyModel::class, View, Count, Create, Update)
        allows(NullvsIntegrationRecordModel::class, View)
        allows(AttachmentRadiotherapyModel::class, View, Count, Create, Update)
        allows(HealthInstitutionNegotiationModel::class, View, Create, Update)
        allows(HealthSpecialistResourceBundleModel::class, View, Count, Create, Update)
        allows(ResourceBundleSpecialtyModel::class, View, Count, Create, Update)
        allows(ResourceBundleSpecialtyPricingModel::class, View, Count, Create, Update)
        allows(Appointment::class, View)
        allows(AppointmentProcedureExecutedAliceCodeSourceModel::class, View, Create)
        allows(MagicNumbersModel::class, View, Update)
        allows(ResourceBundleSpecialtyPricingUpdateModel::class, View, Create, Update, Count)
        allows(GenericFileVault::class, View)
    }

    match("at exec-indicator-domain-service-recurring", { rootService.name == EXEC_INDICATOR_RECURRING_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(confirmExecution)
            includes(canViewStaffAndHealthProfessional)
        }
    }

    match(
        "at exec-indicator-domain-service dependents backfill",
        { rootService.name == EXEC_INDICATOR_BACKFILL_SERVICE_NAME }) {
        includes(canViewCreateAndUpdateTotvsGuia)
        allows(HealthcareBundleModel::class, View, Update)
        allows(ProviderUnitModel::class, View)
        allows(ProviderUnitGroupModel::class, View)
        allows(TussProcedureSpecialtyModel::class, View, Update, Create)
        allows(HealthSpecialistResourceBundleModel::class, View, Create, Update, Delete)
        allows(ResourceBundleSpecialtyModel::class, View, Create, Update, Delete)
        allows(ResourceBundleSpecialtyPricingModel::class, View, Create, Update, Delete)
        allows(HealthcareResourceModel::class, View)
        match("Unauthenticated", { subject is Unauthenticated }) {
            match("can view", { action is View }) {
                match("any MvAuthorizedProcedure") { resource is MvAuthorizedProcedureModel }
                match("any ExecutionGroup") { resource is ExecutionGroupModel }
                match("any HealthEvents") { resource is HealthEventsModel }
                match("any ExecIndicatorAuthorizer") { resource is ExecIndicatorAuthorizerModel }
                match("any TotvsGuia") { resource is TotvsGuiaModel }
                match("any HealthCommunitySpecialistModel") { resource is HealthCommunitySpecialistModel }
                match("any EligibilityCheck") { resource is EligibilityCheckModel }
            }
            match("can update", { action is Update }) {
                match("any ExecutionGroup") { resource is ExecutionGroupModel }
                match("any MvAuthorizedProcedure") { resource is MvAuthorizedProcedureModel }
                match("any EligibilityCheck") { resource is EligibilityCheckModel }
            }
            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("any GlossAuthorizationInfo") { resource is GlossAuthorizationInfoModel }
                match("any GuiaWithProcedures") { resource is GuiaWithProceduresModel }
            }
            match("can count", { action is Count }) {
                match("any ExecutionGroup") { resourceIs(ExecutionGroupModel::class) }
            }
            allows(HealthcareResourceGroupModel::class, View)
            allows(HealthcareResourceModel::class, View, Update)
            allows(HealthcareResourceGroupAssociationModel::class, View, Create, Update)
            match("can delete", { action is Delete }) {
                match("any MvAuthorizedProcedure") { resource is MvAuthorizedProcedureModel }
                match("any TotvsGuia") { resource is TotvsGuiaModel }
            }
        }
    }

}
