package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.models.Geography
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.data.layer.models.Nested
import br.com.alice.data.layer.models.Reference
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class BookTable(
    val name: String,
    val author: String,
    val isbn: String,
    val age: Int?,
    val available: Boolean,
    val genres: List<String>,
    val nested: Nested? = null,
    val jsonObjectArray: List<Reference> = emptyList(),
    val listOfMap: List<Map<String, Any?>> = emptyList(),
    val launchDate: LocalDate = LocalDate.now(),
    val personId: PersonPiiToken,
    val searchTokens: TsVector = TsVector(name.unaccent()),
    val someUuid: UUID = RangeUUID.generate(),
    val latitude: String? = null,
    val longitude: String? = null,
    val geoLocation: Geography? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val anonymized: Boolean = false
) : Table<BookTable>, Anonymizable {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
