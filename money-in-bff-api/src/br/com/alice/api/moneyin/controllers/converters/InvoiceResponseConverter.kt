package br.com.alice.api.moneyin.controllers.converters

import br.com.alice.api.moneyin.model.InvoiceItemResponse
import br.com.alice.api.moneyin.model.InvoicePaymentResponseV2
import br.com.alice.api.moneyin.model.InvoicePaymentStatusResponse
import br.com.alice.api.moneyin.model.InvoiceResponse
import br.com.alice.common.PaymentMethod
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.isAfterEq
import br.com.alice.common.core.extensions.isZero
import br.com.alice.common.core.extensions.toCNPJMask
import br.com.alice.common.core.extensions.toCPFMask
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.money
import br.com.alice.data.layer.models.BillingAccountableParty
import br.com.alice.data.layer.models.BolepixPaymentDetail
import br.com.alice.data.layer.models.BoletoPaymentDetail
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PixPaymentDetail
import br.com.alice.moneyin.converters.InvoiceBreakdownConverter
import br.com.alice.moneyin.helpers.BoletoAndPixHelpers
import java.time.LocalDate
import java.time.LocalDateTime

object InvoiceResponseConverter {
    fun convert(invoice: MemberInvoice, person: Person) =
        InvoiceResponse(
            firstName = person.firstName,
            lastName = person.lastName,
            nationalId = person.nationalId,
            amount = invoice.totalAmount.money,
            referenceDate = invoice.referenceDate,
            invoiceItems = buildBreakdown(invoice),
        )

    fun convertToV2(
        invoicePayment: InvoicePayment,
        memberInvoiceGroup: MemberInvoiceGroup?,
        billingAccountableParty: BillingAccountableParty
    ): InvoicePaymentResponseV2 {
        val pixAndBoletoInfo = BoletoAndPixHelpers.getPixAndBoletoInfo(invoicePayment)
            .getOrNullIfNotFound()

        val now = LocalDateTime.now()
        val dueDate = when (invoicePayment.method) {
            PaymentMethod.BOLETO -> (invoicePayment.paymentDetail as BoletoPaymentDetail).dueDate
            PaymentMethod.PIX -> (invoicePayment.paymentDetail as PixPaymentDetail).dueDate
            PaymentMethod.BOLEPIX -> (invoicePayment.paymentDetail as BolepixPaymentDetail).dueDate
            else -> null
        }

        val isOverdue =
            invoicePayment.isPending && dueDate != null && dueDate.atEndOfTheDay().isBefore(now)

        val statusResponse = if (isOverdue) InvoicePaymentStatusResponse.OVERDUE
        else InvoicePaymentStatusResponse.valueOf(invoicePayment.status.name)

        return InvoicePaymentResponseV2(
            id = invoicePayment.id,
            referenceDate = memberInvoiceGroup?.referenceDate ?: invoicePayment.createdAt.toLocalDate(),
            method = invoicePayment.method,
            amount = invoicePayment.amount.money,
            status = statusResponse,
            dueDate = dueDate?.toLocalDate(),
            approvedAt = invoicePayment.approvedAt?.toLocalDate(),
            emissionDate = invoicePayment.createdAt.toLocalDate(),
            billingAccountableName = billingAccountableParty.fullNameConsideringType,
            billingAccountableAddress = billingAccountableParty.address.toString(),
            billingAccountableNationalId = billingAccountableParty.nationalId.toCNPJMask().toCPFMask(),
            invoiceType = memberInvoiceGroup?.type,
            pixCopyAndPaste = pixAndBoletoInfo?.first?.copyAndPaste,
            pixQrCodeBase64 = pixAndBoletoInfo?.first?.qrCodeBase64,
            pixImageUrl = pixAndBoletoInfo?.first?.qrCodeImageUrl,
            boletoImageUrl = pixAndBoletoInfo?.second?.barcodeImageUrl,
            barcodeBase64 = pixAndBoletoInfo?.second?.barcodeBase64,
            barcodeWritableLine = pixAndBoletoInfo?.second?.digitableLine,
            receiverName = "ALICE",
            receiverNationalId = "34.266.553/0001-02",
            receiverAddress = " Av. Reboucas, 3535, São Paulo",
        )
    }

    private fun buildBreakdown(invoice: MemberInvoice): List<InvoiceItemResponse> {
        val breakdown = invoice.invoiceBreakdown?.let { breakdown ->
            InvoiceBreakdownConverter.convert(breakdown).map {
                InvoiceItemResponse(
                    amount = it.amount,
                    title = it.title
                )
            }
        } ?: emptyList()

        return breakdown.filterNot { it.amount.isZero() }
    }


}
