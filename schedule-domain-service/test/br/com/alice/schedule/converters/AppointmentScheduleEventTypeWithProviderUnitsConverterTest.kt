package br.com.alice.schedule.converters

import br.com.alice.common.core.extensions.toUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeLocation
import br.com.alice.data.layer.models.EventTypeProviderUnit
import br.com.alice.data.layer.models.Weekday
import br.com.alice.schedule.model.AppointmentScheduleEventTypeLocationAvailability
import br.com.alice.schedule.model.AppointmentScheduleEventTypeWithProviderUnits
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalTime
import kotlin.test.Test

class AppointmentScheduleEventTypeWithProviderUnitsConverterTest {

    private val appointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType()
    private val someProviderUnitId = "bc49e899-c95e-40d3-8f24-633541718d41".toUUID()
    private val anotherProviderUnitId = "74ce7665-4ccc-4a06-9c84-a22bc09100fa".toUUID()
    private val availabilityStartTime = LocalTime.parse("12:00")
    private val availabilityEndTime = LocalTime.parse("18:00")
    private val eventTypeProviderUnits = listOf(
        EventTypeProviderUnit(
            appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
            providerUnitId = someProviderUnitId,
            availabilityStartTime = availabilityStartTime,
            availabilityEndTime = availabilityEndTime
        ),
        EventTypeProviderUnit(
            appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
            providerUnitId = anotherProviderUnitId,
            availabilityStartTime = availabilityStartTime,
            availabilityEndTime = availabilityEndTime
        ),
        EventTypeProviderUnit(
            appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
            providerUnitId = null,
            availabilityStartTime = availabilityStartTime,
            availabilityEndTime = availabilityEndTime
        )
    )
    private val someProviderUnit = TestModelFactory.buildProviderUnit(id = someProviderUnitId)
    private val anotherProviderUnit = TestModelFactory.buildProviderUnit(id = anotherProviderUnitId)
    private val providerUnitMap = mapOf(someProviderUnitId to someProviderUnit, anotherProviderUnitId to anotherProviderUnit)
    private val appointmentScheduleEventTypeLocationsAvailability = listOf(
        AppointmentScheduleEventTypeLocationAvailability(
            providerUnitId = someProviderUnitId,
            type = AppointmentScheduleEventTypeLocation.ON_SITE,
            availableWeekDays = Weekday.values().toList(),
            availabilityStartTime = availabilityStartTime,
            availabilityEndTime = availabilityEndTime,
            providerUnitName = someProviderUnit.name
        ),
        AppointmentScheduleEventTypeLocationAvailability(
            providerUnitId = anotherProviderUnitId,
            type = AppointmentScheduleEventTypeLocation.ON_SITE,
            availableWeekDays = Weekday.values().toList(),
            availabilityStartTime = availabilityStartTime,
            availabilityEndTime = availabilityEndTime,
            providerUnitName = anotherProviderUnit.name
        ),
        AppointmentScheduleEventTypeLocationAvailability(
            providerUnitId = null,
            type = AppointmentScheduleEventTypeLocation.REMOTE,
            availableWeekDays = Weekday.values().toList(),
            availabilityStartTime = availabilityStartTime,
            availabilityEndTime = availabilityEndTime
        )
    )

    @Test
    fun `#convert should convert event type`() {
        val expected = AppointmentScheduleEventTypeWithProviderUnits(
            id = appointmentScheduleEventType.id,
            title = appointmentScheduleEventType.title,
            specialtyId = appointmentScheduleEventType.specialtyId,
            subSpecialtyIds = appointmentScheduleEventType.subSpecialtyIds,
            showOnApp = appointmentScheduleEventType.showOnApp,
            category = appointmentScheduleEventType.category,
            duration = appointmentScheduleEventType.duration,
            locationType = appointmentScheduleEventType.locationType,
            description = appointmentScheduleEventType.description,
            userType = appointmentScheduleEventType.userType,
            status = appointmentScheduleEventType.status,
            healthcareModelType = appointmentScheduleEventType.healthcareModelType,
            searchTokens = appointmentScheduleEventType.searchTokens,
            minimumTimeToScheduleBeforeAppointmentTime = appointmentScheduleEventType.minimumTimeToScheduleBeforeAppointmentTime,
            isMultiProfessionalReferral = appointmentScheduleEventType.isMultiProfessionalReferral,
            numberOfDaysFromNowToAllowScheduling = appointmentScheduleEventType.numberOfDaysFromNowToAllowScheduling,
            internalObservation = appointmentScheduleEventType.internalObservation,
            membersRisk = appointmentScheduleEventType.membersRisk,
            providerUnitIds = eventTypeProviderUnits.mapNotNull { it.providerUnitId },
            groupByType = appointmentScheduleEventType.groupByType,
            availableWeekDays = appointmentScheduleEventType.availableWeekDays,
            locations = appointmentScheduleEventTypeLocationsAvailability,
            updatedAt = appointmentScheduleEventType.updatedAt
        )
        val result = AppointmentScheduleEventTypeWithProviderUnitsConverter.convert(
            source = appointmentScheduleEventType,
            eventTypeProviderUnits = eventTypeProviderUnits,
            providerUnitMap
        )

        assertThat(result).isEqualTo(expected)
    }
}
