package br.com.alice.clinicalaccount.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.events.BeneficiaryOnboardingPhaseChangedEvent
import br.com.alice.business.events.MoveToPhaseEvent
import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryOnboarding
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhase
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.PersonTeamAssociation
import br.com.alice.data.layer.models.ProductType
import br.com.alice.member.onboarding.notifier.MemberOnboardingReadyToTeamAssociationEvent
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.sortinghat.client.PersonTeamAssociationService
import br.com.alice.sortinghat.client.RoutingClient
import br.com.alice.sortinghat.models.input.HealthcareTeamModel
import br.com.alice.sortinghat.models.output.HealthcareTeamOutputModel
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class MemberConsumerTest : ConsumerTest() {

    private val routingClient: RoutingClient = mockk()
    private val personClinicalAccountService: PersonClinicalAccountService = mockk()
    private val personTeamAssociationService: PersonTeamAssociationService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()

    private val memberConsumer = MemberConsumer(
        routingClient,
        personClinicalAccountService,
        personTeamAssociationService,
        beneficiaryService
    )

    private val personId = PersonId()
    private val healthcareTeamId = RangeUUID.generate()
    private val eventOnboarding = MemberOnboardingReadyToTeamAssociationEvent(personId)
    private val member = TestModelFactory.buildMember(personId = personId, productType = ProductType.B2B)
    private val eventMemberActivated = MemberActivatedEvent(member)
    private val routingModel = HealthcareTeamModel(
        id = personId.toString(),
        personId = personId
    )
    private val output = HealthcareTeamOutputModel(id = healthcareTeamId.toString())
    private val pca = TestModelFactory.buildPersonClinicalAccount()

    private val onboardingId = RangeUUID.generate()
    private val beneficiaryId = RangeUUID.generate()
    private val eventMoveToPhase = MoveToPhaseEvent(beneficiaryId, BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW)
    private val onboardingPhase = BeneficiaryOnboardingPhase(
        beneficiaryOnboardingId = onboardingId,
        phase = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW
    )
    private val onboarding = BeneficiaryOnboarding(
        id = onboardingId,
        beneficiaryId = beneficiaryId,
        flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
        initialProductId = RangeUUID.generate(),
        phases = listOf(onboardingPhase)
    )
    private val beneficiary = TestModelFactory.buildBeneficiary(
        id = beneficiaryId,
        memberStatus = MemberStatus.ACTIVE,
        personId = personId,
        onboarding = onboarding
    )
    private val onboardingPhaseChangedEvent = BeneficiaryOnboardingPhaseChangedEvent(beneficiaryId, onboardingPhase)

    @AfterTest
    fun confirm() = confirmVerified(
        routingClient,
        personClinicalAccountService,
        personTeamAssociationService,
        beneficiaryService
    )

    @Test
    fun `#processAssociationByOnboarding returns PCA after success routing`() = runBlocking {
        coEvery { routingClient.execute<HealthcareTeamOutputModel>(routingModel) } returns listOf(output).success()
        coEvery {
            personClinicalAccountService.associatePerson(
                personId = personId,
                healthcareTeamId = healthcareTeamId,
                multiStaffIds = emptyList()
            )
        } returns pca.success()

        val result = memberConsumer.processAssociationByOnboarding(eventOnboarding)
        assertThat(result).isSuccessWithData(pca)

        coVerifyOnce { routingClient.execute<HealthcareTeamOutputModel>(any()) }
        coVerifyOnce { personClinicalAccountService.associatePerson(any(), any(), any(), any()) }
    }

    @Test
    fun `#processAssociationByOnboarding - should return false if associate is off`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.CLINICAL_ACCOUNT,
            "should-execute-association",
            false
        ) {

            val result = memberConsumer.processAssociationByOnboarding(eventOnboarding)
            assertThat(result).isSuccessWithData(false)

            coVerifyNone {
                routingClient.execute<HealthcareTeamOutputModel>(any())
                personClinicalAccountService.associatePerson(any(), any(), any(), any())
            }
        }
    }


    @Test
    fun `#processAssociationByOnboarding returns error when an error occurs to routing`() = runBlocking {
        coEvery { routingClient.execute<HealthcareTeamOutputModel>(routingModel) } returns Exception().failure()

        val result = memberConsumer.processAssociationByOnboarding(eventOnboarding)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { routingClient.execute<HealthcareTeamOutputModel>(any()) }
        coVerify { personClinicalAccountService wasNot called }
    }

    @Test
    fun `#processAssociationByOnboarding returns error when an error occurs to create PCA`() = runBlocking {
        coEvery { routingClient.execute<HealthcareTeamOutputModel>(routingModel) } returns listOf(output).success()
        coEvery {
            personClinicalAccountService.associatePerson(
                personId = personId,
                healthcareTeamId = healthcareTeamId,
                multiStaffIds = emptyList()
            )
        } returns Exception().failure()

        val result = memberConsumer.processAssociationByOnboarding(eventOnboarding)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { routingClient.execute<HealthcareTeamOutputModel>(any()) }
        coVerifyOnce { personClinicalAccountService.associatePerson(any(), any(), any(), any()) }
    }

    @Test
    fun `#processAssociationByOnboarding returns error when routing returns empty list`() = runBlocking {
        coEvery {
            routingClient.execute<HealthcareTeamOutputModel>(routingModel)
        } returns emptyList<HealthcareTeamOutputModel>().success()

        val result = memberConsumer.processAssociationByOnboarding(eventOnboarding)
        assertThat(result).isFailureOfType(NoSuchElementException::class)

        coVerifyOnce { routingClient.execute<HealthcareTeamOutputModel>(any()) }
        coVerify { personClinicalAccountService wasNot called }
    }

    @Test
    fun `#processAssociationByMember returns PCA after success routing`() = runBlocking {
        coEvery {
            personTeamAssociationService.getByPersonId(personId, null)
        } returns mockk<PersonTeamAssociation>().success()
        coEvery { routingClient.execute<HealthcareTeamOutputModel>(routingModel) } returns listOf(output).success()
        coEvery {
            personClinicalAccountService.associatePerson(
                personId = personId,
                healthcareTeamId = healthcareTeamId,
                multiStaffIds = emptyList()
            )
        } returns pca.success()

        val result = memberConsumer.processAssociationByMember(eventMemberActivated)
        assertThat(result).isSuccessWithData(pca)

        coVerifyOnce { personTeamAssociationService.getByPersonId(any(), any()) }
        coVerifyOnce { routingClient.execute<HealthcareTeamOutputModel>(any()) }
        coVerifyOnce { personClinicalAccountService.associatePerson(any(), any(), any(), any()) }
    }

    @Test
    fun `#processAssociationByMember returns PCA after success routing when member is b2c`() = runBlocking {
        val eventMemberActivated = MemberActivatedEvent(TestModelFactory.buildMember(
            personId = personId,
            productType = ProductType.B2C
        ))

        coEvery {
            personTeamAssociationService.getByPersonId(personId, null)
        } returns mockk<PersonTeamAssociation>().success()
        coEvery { routingClient.execute<HealthcareTeamOutputModel>(routingModel) } returns listOf(output).success()
        coEvery {
            personClinicalAccountService.associatePerson(
                personId = personId,
                healthcareTeamId = healthcareTeamId,
                multiStaffIds = emptyList()
            )
        } returns pca.success()

        val result = memberConsumer.processAssociationByMember(eventMemberActivated)
        assertThat(result).isSuccessWithData(pca)

        coVerifyOnce { personTeamAssociationService.getByPersonId(any(), any()) }
        coVerifyOnce { routingClient.execute<HealthcareTeamOutputModel>(any()) }
        coVerifyOnce { personClinicalAccountService.associatePerson(any(), any(), any(), any()) }
        coVerify { beneficiaryService wasNot called }
    }

    @Test
    fun `#processAssociationByMember returns PCA after success routing when member is b2c and flag is enabled`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "should-use-onboarding-v2", true) {
                val eventMemberActivated = MemberActivatedEvent(
                    TestModelFactory.buildMember(
                        personId = personId,
                        productType = ProductType.B2C
                    )
                )

                coEvery {
                    personTeamAssociationService.getByPersonId(personId, null)
                } returns mockk<PersonTeamAssociation>().success()
                coEvery { routingClient.execute<HealthcareTeamOutputModel>(routingModel) } returns listOf(output).success()
                coEvery {
                    personClinicalAccountService.associatePerson(
                        personId = personId,
                        healthcareTeamId = healthcareTeamId,
                        multiStaffIds = emptyList()
                    )
                } returns pca.success()

                val result = memberConsumer.processAssociationByMember(eventMemberActivated)
                assertThat(result).isSuccessWithData(pca)

                coVerifyOnce { personTeamAssociationService.getByPersonId(any(), any()) }
                coVerifyOnce { routingClient.execute<HealthcareTeamOutputModel>(any()) }
                coVerifyOnce { personClinicalAccountService.associatePerson(any(), any(), any(), any()) }
                coVerify { beneficiaryService wasNot called }
            }
        }

    @Test
    fun `#processAssociationByMember returns error when an error occurs to routing`() = runBlocking {
        coEvery {
            personTeamAssociationService.getByPersonId(personId, null)
        } returns mockk<PersonTeamAssociation>().success()
        coEvery { routingClient.execute<HealthcareTeamOutputModel>(routingModel) } returns Exception().failure()

        val result = memberConsumer.processAssociationByMember(eventMemberActivated)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { personTeamAssociationService.getByPersonId(any(), any()) }
        coVerifyOnce { routingClient.execute<HealthcareTeamOutputModel>(any()) }
        coVerify { personClinicalAccountService wasNot called }
    }

    @Test
    fun `#processAssociationByMember returns error when an error occurs to create PCA`() = runBlocking {
        coEvery {
            personTeamAssociationService.getByPersonId(personId, null)
        } returns mockk<PersonTeamAssociation>().success()
        coEvery { routingClient.execute<HealthcareTeamOutputModel>(routingModel) } returns listOf(output).success()
        coEvery {
            personClinicalAccountService.associatePerson(
                personId = personId,
                healthcareTeamId = healthcareTeamId,
                multiStaffIds = emptyList()
            )
        } returns Exception().failure()

        val result = memberConsumer.processAssociationByMember(eventMemberActivated)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { personTeamAssociationService.getByPersonId(any(), any()) }
        coVerifyOnce { routingClient.execute<HealthcareTeamOutputModel>(any()) }
        coVerifyOnce { personClinicalAccountService.associatePerson(any(), any(), any(), any()) }
    }

    @Test
    fun `#processAssociationByMember returns error when routing returns empty list`() = runBlocking {
        coEvery {
            personTeamAssociationService.getByPersonId(personId, null)
        } returns mockk<PersonTeamAssociation>().success()
        coEvery {
            routingClient.execute<HealthcareTeamOutputModel>(routingModel)
        } returns emptyList<HealthcareTeamOutputModel>().success()

        val result = memberConsumer.processAssociationByMember(eventMemberActivated)
        assertThat(result).isFailureOfType(NoSuchElementException::class)

        coVerifyOnce { personTeamAssociationService.getByPersonId(any(), any()) }
        coVerifyOnce { routingClient.execute<HealthcareTeamOutputModel>(any()) }
        coVerify { personClinicalAccountService wasNot called }
    }

    @Test
    fun `#processAssociationByMember returns true when member doesn't have historical association`() = runBlocking {
        coEvery { personTeamAssociationService.getByPersonId(personId, null) } returns NotFoundException().failure()

        val result = memberConsumer.processAssociationByMember(eventMemberActivated)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { personTeamAssociationService.getByPersonId(any(), any()) }
        coVerify { routingClient wasNot called }
        coVerify { personClinicalAccountService wasNot called }
    }

    @Test
    fun `#processAssociationByMember returns PCA after success routing using new onboarding flow its active and valid beneficiary onboarding`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "should-use-onboarding-v2", true) {
                coEvery {
                    beneficiaryService.findByPersonId(
                        personId = personId,
                        findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
                    )
                } returns beneficiary.success()
                coEvery { routingClient.execute<HealthcareTeamOutputModel>(routingModel) } returns listOf(output).success()
                coEvery {
                    personClinicalAccountService.associatePerson(
                        personId = personId,
                        healthcareTeamId = healthcareTeamId,
                        multiStaffIds = emptyList()
                    )
                } returns pca.success()

                val result = memberConsumer.processAssociationByMember(eventMemberActivated)
                assertThat(result).isSuccessWithData(pca)

                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                coVerifyOnce { routingClient.execute<HealthcareTeamOutputModel>(any()) }
                coVerifyOnce { personClinicalAccountService.associatePerson(any(), any(), any(), any()) }
            }
        }

    @Test
    fun `#processAssociationByMember - should return false if off association`() =
        runBlocking {
            withFeatureFlags(
                FeatureNamespace.MEMBERSHIP to mapOf(
                    "should-use-onboarding-v2" to true
                ),
                FeatureNamespace.CLINICAL_ACCOUNT to mapOf(
                    "should-execute-association" to false
                )
            ) {
                coEvery {
                    beneficiaryService.findByPersonId(
                        personId = personId,
                        findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
                    )
                } returns beneficiary.success()

                val result = memberConsumer.processAssociationByMember(eventMemberActivated)
                assertThat(result).isSuccessWithData(false)

                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }

                coVerifyNone {
                    routingClient.execute<HealthcareTeamOutputModel>(any())
                    personClinicalAccountService.associatePerson(any(), any(), any(), any())
                }
            }

        }


    @Test
    fun `#processAssociationByMember returns PCA after success routing using new onboarding flow its active and current phase is finished`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "should-use-onboarding-v2", true) {
                val beneficiary = beneficiary.copy(
                    onboarding = beneficiary.onboarding!!.copy(
                        phases = listOf(
                            BeneficiaryOnboardingPhase(
                                beneficiaryOnboardingId = RangeUUID.generate(),
                                phase = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW
                            ),
                            BeneficiaryOnboardingPhase(
                                beneficiaryOnboardingId = RangeUUID.generate(),
                                phase = BeneficiaryOnboardingPhaseType.FINISHED,
                                transactedAt = LocalDateTime.now().plusMinutes(30)
                            )
                        )
                    )
                )
                coEvery {
                    beneficiaryService.findByPersonId(
                        personId = personId,
                        findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
                    )
                } returns beneficiary.success()
                coEvery { routingClient.execute<HealthcareTeamOutputModel>(routingModel) } returns listOf(output).success()
                coEvery {
                    personClinicalAccountService.associatePerson(
                        personId = personId,
                        healthcareTeamId = healthcareTeamId,
                        multiStaffIds = emptyList()
                    )
                } returns pca.success()

                val result = memberConsumer.processAssociationByMember(eventMemberActivated)
                assertThat(result).isSuccessWithData(pca)

                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                coVerifyOnce { routingClient.execute<HealthcareTeamOutputModel>(any()) }
                coVerifyOnce { personClinicalAccountService.associatePerson(any(), any(), any(), any()) }
            }
        }

    @Test
    fun `#processAssociationByMember do nothing when member has invalid beneficiary onboarding phase`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "should-use-onboarding-v2", true) {
                val beneficiary = beneficiary.copy(
                    onboarding = beneficiary.onboarding!!.copy(phases = emptyList())
                )

                coEvery {
                    beneficiaryService.findByPersonId(
                        personId = personId,
                        findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
                    )
                } returns beneficiary.success()

                val result = memberConsumer.processAssociationByMember(eventMemberActivated)
                assertThat(result).isSuccessWithData(false)

                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                coVerify { personTeamAssociationService wasNot called }
                coVerify { routingClient wasNot called }
                coVerify { personClinicalAccountService wasNot called }
            }
        }

    @Test
    fun `#processAssociationByMoveToPhase returns PCA after success routing using new onboarding flow its active and valid beneficiary onboarding`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "should-use-onboarding-v2", true) {
                coEvery {
                    beneficiaryService.get(beneficiaryId)
                } returns beneficiary.success()
                coEvery { routingClient.execute<HealthcareTeamOutputModel>(routingModel) } returns listOf(output).success()
                coEvery {
                    personClinicalAccountService.associatePerson(
                        personId = personId,
                        healthcareTeamId = healthcareTeamId,
                        multiStaffIds = emptyList()
                    )
                } returns pca.success()

                val result = memberConsumer.processAssociationByMoveToPhase(eventMoveToPhase)
                assertThat(result).isSuccessWithData(pca)

                coVerifyOnce { beneficiaryService.get(any()) }
                coVerifyOnce { routingClient.execute<HealthcareTeamOutputModel>(any()) }
                coVerifyOnce { personClinicalAccountService.associatePerson(any(), any(), any(), any()) }
            }
        }

    @Test
    fun `#processAssociationByMoveToPhase - should return false if associate is off`() =
        runBlocking {
            withFeatureFlags(
                FeatureNamespace.MEMBERSHIP to mapOf(
                    "should-use-onboarding-v2" to true
                ),
                FeatureNamespace.CLINICAL_ACCOUNT to mapOf(
                    "should-execute-association" to false
                )
            ) {
                coEvery {
                    beneficiaryService.get(beneficiaryId)
                } returns beneficiary.success()

                val result = memberConsumer.processAssociationByMoveToPhase(eventMoveToPhase)
                assertThat(result).isSuccessWithData(false)

                coVerifyOnce { beneficiaryService.get(any()) }

                coVerifyNone {
                    routingClient.execute<HealthcareTeamOutputModel>(any())
                    personClinicalAccountService.associatePerson(any(), any(), any(), any())
                }
            }
        }


    @Test
    fun `#processAssociationByMoveToPhase do nothing when phase is not WAITING_FOR_REVIEW`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "should-use-onboarding-v2", true) {
                val event = MoveToPhaseEvent(beneficiaryId, BeneficiaryOnboardingPhaseType.REGISTRATION)

                val result = memberConsumer.processAssociationByMoveToPhase(event)
                assertThat(result).isSuccessWithData(false)

                coVerify { beneficiaryService wasNot called  }
                coVerify { personTeamAssociationService wasNot called }
                coVerify { routingClient wasNot called }
                coVerify { personClinicalAccountService wasNot called }
            }
        }

    @Test
    fun `#processAssociationByMoveToPhase do nothing when member status is not active`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "should-use-onboarding-v2", true) {
                val beneficiary = beneficiary.copy(
                    memberStatus = MemberStatus.PENDING
                )

                coEvery {
                    beneficiaryService.get(beneficiaryId)
                } returns beneficiary.success()

                val result = memberConsumer.processAssociationByMoveToPhase(eventMoveToPhase)
                assertThat(result).isSuccessWithData(false)

                coVerifyOnce { beneficiaryService.get(any()) }
                coVerify { personTeamAssociationService wasNot called }
                coVerify { routingClient wasNot called }
                coVerify { personClinicalAccountService wasNot called }
            }
        }

    @Test
    fun `#processAssociationByMoveToPhase do nothing feature flag is disabled`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "should-use-onboarding-v2", false) {

                val result = memberConsumer.processAssociationByMoveToPhase(eventMoveToPhase)
                assertThat(result).isSuccessWithData(false)

                coVerify { beneficiaryService wasNot called }
                coVerify { personTeamAssociationService wasNot called }
                coVerify { routingClient wasNot called }
                coVerify { personClinicalAccountService wasNot called }
            }
        }


    @Test
    fun `#processAssociationByBeneficiaryOnboardingPhaseChanged returns PCA after success routing using new onboarding flow its active and valid beneficiary onboarding`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "should-use-onboarding-v2", true) {
                coEvery { beneficiaryService.get(beneficiaryId) } returns beneficiary.success()
                coEvery { routingClient.execute<HealthcareTeamOutputModel>(routingModel) } returns listOf(output).success()
                coEvery {
                    personClinicalAccountService.associatePerson(
                        personId = personId,
                        healthcareTeamId = healthcareTeamId,
                        multiStaffIds = emptyList()
                    )
                } returns pca.success()

                val result = memberConsumer.processAssociationByBeneficiaryOnboardingPhaseChanged(onboardingPhaseChangedEvent)
                assertThat(result).isSuccessWithData(pca)

                coVerifyOnce { beneficiaryService.get(any()) }
                coVerifyOnce { routingClient.execute<HealthcareTeamOutputModel>(any()) }
                coVerifyOnce { personClinicalAccountService.associatePerson(any(), any(), any(), any()) }
            }
        }

    @Test
    fun `#processAssociationByBeneficiaryOnboardingPhaseChanged - should return false if associate is off`() =
        runBlocking {
            withFeatureFlags(
                FeatureNamespace.MEMBERSHIP to mapOf(
                    "should-use-onboarding-v2" to true
                ),
                FeatureNamespace.CLINICAL_ACCOUNT to mapOf(
                    "should-execute-association" to false
                )
            ) {
                coEvery { beneficiaryService.get(beneficiaryId) } returns beneficiary.success()

                val result = memberConsumer.processAssociationByBeneficiaryOnboardingPhaseChanged(onboardingPhaseChangedEvent)
                assertThat(result).isSuccessWithData(false)

                coVerifyOnce { beneficiaryService.get(any()) }

                coVerifyNone {
                    routingClient.execute<HealthcareTeamOutputModel>(any())
                    personClinicalAccountService.associatePerson(any(), any(), any(), any())
                }
            }
        }


    @Test
    fun `#processAssociationByBeneficiaryOnboardingPhaseChanged do nothing when phase is not WAITING_FOR_REVIEW`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "should-use-onboarding-v2", true) {
                val onboardingPhase = onboardingPhase.copy(phase = BeneficiaryOnboardingPhaseType.REGISTRATION)
                val onboardingPhaseChangedEvent = BeneficiaryOnboardingPhaseChangedEvent(beneficiaryId, onboardingPhase)

                val result = memberConsumer.processAssociationByBeneficiaryOnboardingPhaseChanged(onboardingPhaseChangedEvent)
                assertThat(result).isSuccessWithData(false)

                coVerify { beneficiaryService wasNot called  }
                coVerify { personTeamAssociationService wasNot called }
                coVerify { routingClient wasNot called }
                coVerify { personClinicalAccountService wasNot called }
            }
        }

    @Test
    fun `#processAssociationByBeneficiaryOnboardingPhaseChanged do nothing when member status is not active`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "should-use-onboarding-v2", true) {
                val beneficiary = beneficiary.copy(
                    memberStatus = MemberStatus.PENDING
                )

                coEvery {
                    beneficiaryService.get(beneficiaryId)
                } returns beneficiary.success()

                val result = memberConsumer.processAssociationByBeneficiaryOnboardingPhaseChanged(onboardingPhaseChangedEvent)
                assertThat(result).isSuccessWithData(false)

                coVerifyOnce { beneficiaryService.get(any()) }
                coVerify { personTeamAssociationService wasNot called }
                coVerify { routingClient wasNot called }
                coVerify { personClinicalAccountService wasNot called }
            }
        }

    @Test
    fun `#processAssociationByBeneficiaryOnboardingPhaseChanged do nothing feature flag is disabled`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "should-use-onboarding-v2", false) {

                val result = memberConsumer.processAssociationByBeneficiaryOnboardingPhaseChanged(onboardingPhaseChangedEvent)
                assertThat(result).isSuccessWithData(false)

                coVerify { beneficiaryService wasNot called }
                coVerify { personTeamAssociationService wasNot called }
                coVerify { routingClient wasNot called }
                coVerify { personClinicalAccountService wasNot called }
            }
        }
}
