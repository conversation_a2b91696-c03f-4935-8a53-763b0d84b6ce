package br.com.alice.healthplan.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MemberOnboarding
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus
import br.com.alice.data.layer.models.MemberOnboardingFlowType
import br.com.alice.data.layer.models.MemberOnboardingReferencedLink
import br.com.alice.data.layer.models.MemberOnboardingReferencedLinkModel
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.Referral
import br.com.alice.data.layer.models.Step
import br.com.alice.healthplan.converters.HealthPlanTaskTransportConverter
import br.com.alice.healthplan.models.ReferralTransport
import br.com.alice.healthplan.services.internal.member_onboarding.MemberOnboardingTaskService
import br.com.alice.member.onboarding.client.MemberOnboardingService
import br.com.alice.member.onboarding.notifier.MemberOnboardingStepUpdatedEvent
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MemberOnboardingConsumerTest: ConsumerTest() {

    private val memberOnboardingTaskService: MemberOnboardingTaskService = mockk()
    private val memberOnboardingService: MemberOnboardingService = mockk()
    private val memberService: MemberService = mockk()

    private val memberOnboardingConsumer = MemberOnboardingConsumer(
        memberOnboardingTaskService,
        memberOnboardingService,
        memberService
    )

    private val staff = TestModelFactory.buildStaff()
    private val referral = TestModelFactory.buildHealthPlanTaskReferral(lastRequesterStaffId = staff.id)
    private val member = TestModelFactory.buildMember(status = MemberStatus.ACTIVE)
    private val transport = HealthPlanTaskTransportConverter.convert<Referral, ReferralTransport>(
        task = referral.specialize(),
        staffsById = mapOf(staff.id to staff),
        groups = emptyMap()
    )
    private val templateId = RangeUUID.generate()
    private val stepConclusion = Step(
        templateType = MemberOnboarding.MemberOnboardingStepType.CONCLUSION,
        status = MemberOnboardingStepStatus.BLOCKED
    )

    private val stepScoreMagenta = Step(
        templateType = MemberOnboarding.MemberOnboardingStepType.SCORE_MAGENTA,
        status = MemberOnboardingStepStatus.COMPLETED
    )

    private val stepCover = Step(
        templateType = MemberOnboarding.MemberOnboardingStepType.COVER,
        status = MemberOnboardingStepStatus.COMPLETED
    )
    private val stepCoverMfc = Step(
        templateType = MemberOnboarding.MemberOnboardingStepType.COVER_MFC,
        status = MemberOnboardingStepStatus.COMPLETED
    )
    private val memberOnboarding = TestModelFactory.buildMemberOnboarding(completed = true, steps = listOf(stepCoverMfc, stepScoreMagenta, stepCover, stepConclusion), referencedLinks = emptyList())
    private val referencedLinkTemplate = MemberOnboardingReferencedLink(
        id = templateId,
        model = MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK_TEMPLATE
    )
    private val referencedLinkTask = MemberOnboardingReferencedLink(
        id = transport.id!!,
        model = MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK
    )

    private fun createTaskFromUpdateFalseTestParameters() = listOf(
        arrayOf(
            "when already has HealthPlanTask",
            memberOnboarding.copy(
                referencedLinks = listOf(referencedLinkTask)
            )
        ),
        arrayOf(
            "when flow type is child and has not completed health declaration step",
            memberOnboarding.copy(
                steps = listOf(
                    stepCover.copy(status = MemberOnboardingStepStatus.PENDING),
                    stepConclusion
                )
            )
        ),
        arrayOf(
            "when flow type is child and has not completed cover mfc step",
            memberOnboarding.copy(
                steps = listOf(
                    stepCoverMfc.copy(status = MemberOnboardingStepStatus.PENDING),
                    stepConclusion
                )
            )
        ),
        arrayOf(
            "when flow type is adult and has not completed health declaration step",
            memberOnboarding.copy(
                flowType = MemberOnboardingFlowType.ADULT,
                steps = listOf(
                    stepCover.copy(status = MemberOnboardingStepStatus.PENDING),
                    stepScoreMagenta
                )
            )
        ),
        arrayOf(
            "when flow type is adult and has not completed cover mfc step",
            memberOnboarding.copy(
                flowType = MemberOnboardingFlowType.ADULT,
                steps = listOf(
                    stepCoverMfc.copy(status = MemberOnboardingStepStatus.PENDING),
                    stepCover,
                    stepScoreMagenta
                )
            )
        ),
        arrayOf(
            "when flow type is adult and has not completed score magenta step",
            memberOnboarding.copy(
                flowType = MemberOnboardingFlowType.ADULT,
                steps = listOf(
                    stepScoreMagenta.copy(status = MemberOnboardingStepStatus.PENDING),
                    stepCover
                )
            )
        ),
    )

    @ParameterizedTest(name = "{0}")
    @MethodSource("createTaskFromUpdateFalseTestParameters")
    fun `#createTaskFromUpdate should return false`(
        testName: String,
        memberOnboarding: MemberOnboarding
    ) = runBlocking {

        coEvery { memberOnboardingService.getByPersonId(memberOnboarding.personId) } returns memberOnboarding.success()

        val event = MemberOnboardingStepUpdatedEvent(memberOnboarding)

        val result = memberOnboardingConsumer.createTaskFromUpdate(event)

        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { memberOnboardingService.getByPersonId(any()) }
        coVerifyNone { memberService.getCurrent(any()) }
        coVerifyNone { memberOnboardingService.addReferencedLink(any(), any()) }
        coVerifyNone { memberOnboardingTaskService.getIfDontExistTaskByTemplate(any(), any()) }
        coVerifyNone { memberOnboardingTaskService.createTaskReferencedLink(any(), any()) }
    }

    @Test
    fun `#createTaskFromUpdate should return false if already has template but can't create task because member is not active`() = runBlocking {
        val memberOnboarding = memberOnboarding.copy(
            referencedLinks = listOf(referencedLinkTemplate)
        )
        val member = member.copy(status = MemberStatus.PENDING)

        coEvery { memberOnboardingService.getByPersonId(memberOnboarding.personId) } returns memberOnboarding.success()
        coEvery { memberService.getCurrent(memberOnboarding.personId) } returns member.success()

        val event = MemberOnboardingStepUpdatedEvent(memberOnboarding)

        val result = memberOnboardingConsumer.createTaskFromUpdate(event)

        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { memberOnboardingService.getByPersonId(any()) }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyNone { memberOnboardingTaskService.createTaskTemplateReferencedLink(any()) }
        coVerifyNone { memberOnboardingService.addReferencedLink(any(), any()) }
        coVerifyNone { memberOnboardingTaskService.getIfDontExistTaskByTemplate(any(), any()) }
        coVerifyNone { memberOnboardingTaskService.createTaskReferencedLink(any(), any()) }
    }

    @Test
    fun `#createTaskFromUpdate should return false if already has template but can't create task because is missing more than one step to complete`() = runBlocking {
        val memberOnboarding = memberOnboarding.copy(
            referencedLinks = listOf(referencedLinkTemplate),
            steps = listOf(
                Step(
                    templateType = MemberOnboarding.MemberOnboardingStepType.ALICE_INFO,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                stepScoreMagenta, stepCover, stepConclusion
            )
        )

        coEvery { memberOnboardingService.getByPersonId(memberOnboarding.personId) } returns memberOnboarding.success()
        coEvery { memberService.getCurrent(memberOnboarding.personId) } returns member.success()

        val event = MemberOnboardingStepUpdatedEvent(memberOnboarding)

        val result = memberOnboardingConsumer.createTaskFromUpdate(event)

        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { memberOnboardingService.getByPersonId(any()) }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyNone { memberOnboardingTaskService.createTaskTemplateReferencedLink(any()) }
        coVerifyNone { memberOnboardingService.addReferencedLink(any(), any()) }
        coVerifyNone { memberOnboardingTaskService.getIfDontExistTaskByTemplate(any(), any()) }
        coVerifyNone { memberOnboardingTaskService.createTaskReferencedLink(any(), any()) }
    }

    @Test
    fun `#createTaskFromUpdate should create template and add it in MemberOnboarding referencedLinks when does not have template yet and member is not active`() = runBlocking {
        val member = member.copy(status = MemberStatus.PENDING)
        val expected = memberOnboarding.copy(
            referencedLinks = listOf(referencedLinkTemplate)
        )

        coEvery { memberOnboardingService.getByPersonId(memberOnboarding.personId) } returns memberOnboarding.success()
        coEvery { memberService.getCurrent(memberOnboarding.personId) } returns member.success()
        coEvery { memberOnboardingTaskService.createTaskTemplateReferencedLink(memberOnboarding) } returns expected.referencedLinks
        coEvery {
            memberOnboardingService.addReferencedLink(memberOnboarding.id, expected.referencedLinks, shouldOverride = true)
        } returns expected.success()

        val event = MemberOnboardingStepUpdatedEvent(memberOnboarding)

        val result = memberOnboardingConsumer.createTaskFromUpdate(event)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { memberOnboardingService.getByPersonId(any()) }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { memberOnboardingTaskService.createTaskTemplateReferencedLink(any()) }
        coVerifyOnce { memberOnboardingService.addReferencedLink(any(), any(), any()) }
        coVerifyNone { memberOnboardingTaskService.getIfDontExistTaskByTemplate(any(), any()) }
        coVerifyNone { memberOnboardingTaskService.createTaskReferencedLink(any(), any()) }
    }

    @Test
    fun `#createTaskFromUpdate should create template and add it in MemberOnboarding referencedLinks when does not have template yet and is missing more than one step to complete`() = runBlocking {
        val memberOnboarding = memberOnboarding.copy(
            steps = listOf(
                Step(
                    templateType = MemberOnboarding.MemberOnboardingStepType.ALICE_INFO,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                stepScoreMagenta, stepCover, stepConclusion
            )
        )
        val expected = memberOnboarding.copy(
            referencedLinks = listOf(referencedLinkTemplate),
        )

        coEvery { memberOnboardingService.getByPersonId(memberOnboarding.personId) } returns memberOnboarding.success()
        coEvery { memberService.getCurrent(memberOnboarding.personId) } returns member.success()
        coEvery { memberOnboardingTaskService.createTaskTemplateReferencedLink(memberOnboarding) } returns expected.referencedLinks
        coEvery {
            memberOnboardingService.addReferencedLink(memberOnboarding.id, expected.referencedLinks, shouldOverride = true)
        } returns expected.success()

        val event = MemberOnboardingStepUpdatedEvent(memberOnboarding)

        val result = memberOnboardingConsumer.createTaskFromUpdate(event)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { memberOnboardingService.getByPersonId(any()) }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { memberOnboardingTaskService.createTaskTemplateReferencedLink(any()) }
        coVerifyOnce { memberOnboardingService.addReferencedLink(any(), any(), any()) }
        coVerifyNone { memberOnboardingTaskService.getIfDontExistTaskByTemplate(any(), any()) }
        coVerifyNone { memberOnboardingTaskService.createTaskReferencedLink(any(), any()) }
    }

    @Test
    fun `#createTaskFromUpdate should create template and task and add it in MemberOnboarding referencedLinks when does not have template yet`() = runBlocking {
        val expected = memberOnboarding.copy(
            referencedLinks = listOf(referencedLinkTemplate, referencedLinkTask),
        )

        coEvery { memberOnboardingService.getByPersonId(memberOnboarding.personId) } returns memberOnboarding.success()
        coEvery { memberService.getCurrent(memberOnboarding.personId) } returns member.success()
        coEvery { memberOnboardingTaskService.createTaskTemplateReferencedLink(memberOnboarding) } returns listOf(referencedLinkTemplate)
        coEvery { memberOnboardingTaskService.getIfDontExistTaskByTemplate(memberOnboarding.personId, listOf(templateId)) } returns listOf(templateId)
        coEvery { memberOnboardingTaskService.createTaskReferencedLink(memberOnboarding.personId, listOf(templateId)) } returns listOf(referencedLinkTask).success()
        coEvery {
            memberOnboardingService.addReferencedLink(memberOnboarding.id, expected.referencedLinks, shouldOverride = true)
        } returns expected.success()

        val event = MemberOnboardingStepUpdatedEvent(memberOnboarding)

        val result = memberOnboardingConsumer.createTaskFromUpdate(event)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { memberOnboardingService.getByPersonId(any()) }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { memberOnboardingTaskService.createTaskTemplateReferencedLink(any()) }
        coVerifyOnce { memberOnboardingTaskService.getIfDontExistTaskByTemplate(any(), any()) }
        coVerifyOnce { memberOnboardingTaskService.createTaskReferencedLink(any(), any()) }
        coVerifyOnce { memberOnboardingService.addReferencedLink(any(), any(), any()) }
    }

    @Test
    fun `#createTaskFromUpdate should only create task and add it in MemberOnboarding referencedLinks when already has template`() = runBlocking {
        val memberOnboarding = memberOnboarding.copy(
            referencedLinks = listOf(referencedLinkTemplate),
        )
        val expected = memberOnboarding.copy(
            referencedLinks = listOf(referencedLinkTemplate, referencedLinkTask)
        )

        coEvery { memberOnboardingService.getByPersonId(memberOnboarding.personId) } returns memberOnboarding.success()
        coEvery { memberService.getCurrent(memberOnboarding.personId) } returns member.success()
        coEvery { memberOnboardingTaskService.getIfDontExistTaskByTemplate(memberOnboarding.personId, listOf(templateId)) } returns listOf(templateId)
        coEvery { memberOnboardingTaskService.createTaskReferencedLink(memberOnboarding.personId, listOf(templateId)) } returns listOf(referencedLinkTask).success()
        coEvery {
            memberOnboardingService.addReferencedLink(memberOnboarding.id, expected.referencedLinks, shouldOverride = true)
        } returns expected.success()

        val event = MemberOnboardingStepUpdatedEvent(memberOnboarding)

        val result = memberOnboardingConsumer.createTaskFromUpdate(event)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { memberOnboardingService.getByPersonId(any()) }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { memberOnboardingTaskService.getIfDontExistTaskByTemplate(any(), any()) }
        coVerifyOnce { memberOnboardingTaskService.createTaskReferencedLink(any(), any()) }
        coVerifyOnce { memberOnboardingService.addReferencedLink(any(), any(), any()) }
        coVerifyNone { memberOnboardingTaskService.createTaskTemplateReferencedLink(any()) }
    }

    @Test
    fun `#createTaskFromUpdate should return false if already exist a task from this template`() = runBlocking {
        coEvery { memberOnboardingService.getByPersonId(memberOnboarding.personId) } returns memberOnboarding.success()
        coEvery { memberService.getCurrent(memberOnboarding.personId) } returns member.success()
        coEvery { memberOnboardingTaskService.createTaskTemplateReferencedLink(memberOnboarding) } returns listOf(referencedLinkTemplate)
        coEvery { memberOnboardingTaskService.getIfDontExistTaskByTemplate(memberOnboarding.personId, listOf(templateId)) } returns emptyList()

        val event = MemberOnboardingStepUpdatedEvent(memberOnboarding)

        val result = memberOnboardingConsumer.createTaskFromUpdate(event)

        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { memberOnboardingService.getByPersonId(any()) }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { memberOnboardingTaskService.createTaskTemplateReferencedLink(any()) }
        coVerifyOnce { memberOnboardingTaskService.getIfDontExistTaskByTemplate(any(), any()) }
        coVerifyNone { memberOnboardingTaskService.createTaskReferencedLink(any(), any()) }
        coVerifyNone { memberOnboardingService.addReferencedLink(any(), any(), any()) }
    }
}
