package br.com.alice.member.onboarding.routes

import br.com.alice.business.events.MoveToPhaseEvent
import br.com.alice.clinicalaccount.event.PersonHealthcareTeamAssociationUpdatedEvent
import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.internals.ConsumerJob
import br.com.alice.member.onboarding.consumers.MemberActivatedConsumer
import br.com.alice.member.onboarding.consumers.MemberOnboardingDroppedConsumer
import br.com.alice.member.onboarding.consumers.MoveToPhaseConsumer
import br.com.alice.member.onboarding.consumers.PersonHealthcareTeamAssociationConsumer
import br.com.alice.member.onboarding.notifier.MemberOnboardingDroppedEvent
import br.com.alice.person.model.events.MemberActivatedEvent

fun ConsumerJob.Configuration.kafkaRoutes() {

    val memberOnboardingDroopedConsumer by inject<MemberOnboardingDroppedConsumer>()
    consume(
        "trigger-team-association-event",
        MemberOnboardingDroppedEvent.name,
        memberOnboardingDroopedConsumer::triggerMemberOnboardingReadyToTeamAssociationEvent
    )

    val memberActivatedConsumer by inject<MemberActivatedConsumer>()
    consume(
        "update-member-onboarding-on-member-activation",
        MemberActivatedEvent.name,
        memberActivatedConsumer::updateMemberOnboarding
    )

    consume(
        "create-member-onboarding-for-brokers-on-member-activation",
        MemberActivatedEvent.name,
        memberActivatedConsumer::createOnboardingForMembersOfBrokers
    )

    val moveToPhaseConsumer by inject<MoveToPhaseConsumer>()
    consume(
        "create-member-onboarding-on-moving-phase",
        MoveToPhaseEvent.name,
        moveToPhaseConsumer::createOnboardingWhenWaitingForReview
    )

    val personHealthcareTeamAssociationConsumer by inject<PersonHealthcareTeamAssociationConsumer>()
    consume(
        "complete-cover-mfc-onboarding-step-from-person-health-care-team-association",
        PersonHealthcareTeamAssociationUpdatedEvent.name,
        personHealthcareTeamAssociationConsumer::completeCoverMfcOnboardingStep
    )
}
