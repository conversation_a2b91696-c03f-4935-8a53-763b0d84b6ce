package br.com.alice.bff.business.converters.v2

import br.com.alice.bff.business.models.v2.CompanyResponse
import br.com.alice.bff.business.models.v2.HelpDeskTransport
import br.com.alice.bff.business.models.v2.StaffResponse
import br.com.alice.bff.business.models.v2.StaffType
import br.com.alice.bff.business.services.Modules
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanySize
import br.com.alice.data.layer.models.CompanyStaff

var companyHelpDesk = HelpDeskTransport(
    url = "https://wa.me/5511978123260"
)

fun CompanyStaff.toResponse(
    company: Company,
    beneficiaryCount: Int,
    modules: List<Modules> = emptyList()
) = StaffResponse(
    id = this.id,
    name = this.fullName(),
    email = this.email,
    accessLevel = this.accessLevel,
    type = StaffType.COMPANY,
    company = CompanyResponse(
        id = company.id,
        name = company.name,
        legalName = company.legalName,
        companySize = company.companySize,
        hasBeneficiaries = beneficiaryCount > 0,
    ),
    helpDesk = if (company.companySize != null && company.companySize != CompanySize.MICRO) companyHelpDesk else null,
    enabledModules = modules
)
