package br.com.alice.secondary.attention.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.NextStepSpecialistInfo
import br.com.alice.data.layer.models.NextStepSpecialistType
import br.com.alice.data.layer.models.copy
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.secondary.attention.events.CounterReferralCreatedEvent
import br.com.alice.secondary.attention.events.HealthPlanTaskReferralsHandleEvent
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlin.test.Test
import kotlinx.coroutines.runBlocking

class CounterReferralCreatedConsumerTest : ConsumerTest() {

    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val consumer = CounterReferralCreatedConsumer(
        healthPlanTaskService,
        kafkaProducerService,
    )

    private val healthPlanTask = TestModelFactory.buildHealthPlanTaskReferral()
    private val counterReferral = TestModelFactory.buildCounterReferral(referralId = healthPlanTask.id)

    @Test
    fun `#handleHealthPlanTasks should finish task successfully`(): Unit = runBlocking {
        val task = TestModelFactory.buildHealthPlanTask()
        val counterReferral = TestModelFactory.buildCounterReferral(
            referralId = task.id,
        ).copy(
            nextStepSpecialistInfo = NextStepSpecialistInfo(
                type = NextStepSpecialistType.DISCHARGED
            )
        )

        coEvery {
            healthPlanTaskService.get(task.id)
        } returns task.success()
        coEvery {
            healthPlanTaskService.updateTask(
                task.copy(
                    status = HealthPlanTaskStatus.DONE
                )
            )
        } answers {
            firstArg<HealthPlanTask>().success()
        }

        val result = consumer.handleHealthPlanTasks(
            CounterReferralCreatedEvent(
                counterReferral
            )
        )

        assertThat(result).isSuccess()
        coVerify(exactly = 1) {
            healthPlanTaskService.updateTask(
                task.copy(
                    status = HealthPlanTaskStatus.DONE
                )
            )
        }
    }

    @Test
    fun `#handleHealthPlanTasks should not finish task`(): Unit = runBlocking {
        val task = TestModelFactory.buildHealthPlanTask()
        val counterReferral = TestModelFactory.buildCounterReferral(
            referralId = task.id
        ).copy(
            nextStepSpecialistInfo = NextStepSpecialistInfo(
                type = NextStepSpecialistType.FOLLOW_UP_APPOINTMENT
            )
        )

        val result = consumer.handleHealthPlanTasks(
            CounterReferralCreatedEvent(
                counterReferral
            )
        )

        assertThat(result).isSuccess()

        coVerify(exactly = 0) { healthPlanTaskService.updateTask(any()) }
        coVerify(exactly = 0) { healthPlanTaskService.get(any()) }
    }

    @Test
    fun `#dispatchHealthPlanTaskReferralsUpsert should receive and dispatch event`(): Unit = runBlocking {
        coEvery {
            kafkaProducerService.produce(any<HealthPlanTaskReferralsHandleEvent>(), healthPlanTask.id.toString())
        } returns mockk()

        coEvery {
            healthPlanTaskService.get(counterReferral.referralId!!)
        } returns healthPlanTask.success()

        val event = CounterReferralCreatedEvent(counterReferral)
        val result = consumer.dispatchHealthPlanTaskReferrals(event)
        assertThat(result).isSuccess()

        coVerifyOnce { kafkaProducerService.produce(any(), healthPlanTask.id.toString()) }
        coVerifyOnce { healthPlanTaskService.get(counterReferral.referralId!!) }
    }

    @Test
    fun `#dispatchHealthPlanTaskReferralsUpsert should receive and not dispatch if referral id is null`() =
        runBlocking {
            val event = CounterReferralCreatedEvent(counterReferral.copy(referralId = null))
            val result = consumer.dispatchHealthPlanTaskReferrals(event)
            assertThat(result).isSuccess()

            coVerifyNone { kafkaProducerService.produce(any(), healthPlanTask.id.toString()) }
            coVerifyNone { healthPlanTaskService.get(any()) }
        }

}
