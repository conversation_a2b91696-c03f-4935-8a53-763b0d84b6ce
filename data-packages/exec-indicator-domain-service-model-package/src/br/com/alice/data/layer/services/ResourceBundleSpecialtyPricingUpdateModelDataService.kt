package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdateModel
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface ResourceBundleSpecialtyPricingUpdateModelDataService :
    Service,
    Finder<ResourceBundleSpecialtyPricingUpdateModelDataService.FieldOptions, ResourceBundleSpecialtyPricingUpdateModelDataService.OrderingOptions, ResourceBundleSpecialtyPricingUpdateModel>,
    Getter<ResourceBundleSpecialtyPricingUpdateModel>,
    Adder<ResourceBundleSpecialtyPricingUpdateModel>,
    Updater<ResourceBundleSpecialtyPricingUpdateModel>,
    Counter<ResourceBundleSpecialtyPricingUpdateModelDataService.FieldOptions, ResourceBundleSpecialtyPricingUpdateModelDataService.OrderingOptions, ResourceBundleSpecialtyPricingUpdateModel> {

    override val namespace get() = "exec_indicator"
    override val serviceName get() = "resource_bundle_specialty_pricing_update"

    class Id : Field.UUIDField(ResourceBundleSpecialtyPricingUpdateModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class FileVaultId : Field.UUIDField(ResourceBundleSpecialtyPricingUpdateModel::fileVaultId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class CreatedAt : Field.DateTimeField(ResourceBundleSpecialtyPricingUpdateModel::createdAt) {
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
    }

    class ProcessingAt : Field.DateTimeField(ResourceBundleSpecialtyPricingUpdateModel::processingAt) {
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class CompletedAt : Field.DateTimeField(ResourceBundleSpecialtyPricingUpdateModel::completedAt) {
        fun isNull() = Predicate.isNull(this)
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class FailedRowsCount : Field.IntegerField(ResourceBundleSpecialtyPricingUpdateModel::failedRowsCount) {
        fun eq(value: Int) = Predicate.eq(this, value)
        fun greater(value: Int) = Predicate.greater(this, value)
    }

    class ParsingError : Field.TextField(ResourceBundleSpecialtyPricingUpdateModel::parsingError) {
        fun isNull() = Predicate.isNull(this)
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class FieldOptions {
        val id = Id()
        val fileVaultId = FileVaultId()
        val processingAt = ProcessingAt()
        val completedAt = CompletedAt()
        val failedRowsCount = FailedRowsCount()
        val parsingError = ParsingError()
        val createdAt = CreatedAt()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<ResourceBundleSpecialtyPricingUpdateModel>, Throwable>
    override suspend fun get(id: UUID): Result<ResourceBundleSpecialtyPricingUpdateModel, Throwable>
    override suspend fun add(model: ResourceBundleSpecialtyPricingUpdateModel): Result<ResourceBundleSpecialtyPricingUpdateModel, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun update(model: ResourceBundleSpecialtyPricingUpdateModel): Result<ResourceBundleSpecialtyPricingUpdateModel, Throwable>
}
