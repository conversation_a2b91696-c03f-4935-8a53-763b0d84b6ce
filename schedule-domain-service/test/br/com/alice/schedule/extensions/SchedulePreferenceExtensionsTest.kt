package br.com.alice.schedule.extensions

import br.com.alice.common.core.extensions.toUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class SchedulePreferenceExtensionsTest {

    private val staffId = "fd1cdb19-af48-4e01-a474-2aad7688d010".toUUID()

    @Test
    fun `shouldSkipQueryEvents should return true when hasStaffSchedules is false and isDoingFullSync is false`() {
        val schedulePreference = TestModelFactory.buildSchedulePreference(staffId = staffId).copy(
            hasStaffSchedules = false,
            isDoingFullSync = false
        )
        assertThat(schedulePreference.shouldSkipQueryEvents()).isTrue
    }

    @Test
    fun `shouldSkipQueryEvents should return false when hasStaffSchedules is true`() {
        val schedulePreference = TestModelFactory.buildSchedulePreference(staffId = staffId).copy(
            hasStaffSchedules = true,
            isDoingFullSync = false
        )
        assertThat(schedulePreference.shouldSkipQueryEvents()).isFalse
    }

    @Test
    fun `shouldSkipQueryEvents should return false when isDoingFullSync is true`() {
        val schedulePreference = TestModelFactory.buildSchedulePreference(staffId = staffId).copy(
            hasStaffSchedules = false,
            isDoingFullSync = true
        )
        assertThat(schedulePreference.shouldSkipQueryEvents()).isFalse
    }

    @Test
    fun `shouldSkipQueryEvents should return true when both hasStaffSchedules and isDoingFullSync are true`() {
        val schedulePreference = TestModelFactory.buildSchedulePreference(staffId = staffId).copy(
            hasStaffSchedules = true,
            isDoingFullSync = true
        )
        assertThat(schedulePreference.shouldSkipQueryEvents()).isFalse
    }

}
