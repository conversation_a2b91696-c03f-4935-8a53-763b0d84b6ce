package br.com.alice.data.layer.pipelines.subjects

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import org.junit.jupiter.api.Assertions.assertEquals
import kotlin.test.Test

class ProdTechHealthSubjectTest {

    @Test
    fun `should create a ProdTechHealthSubject from StaffModel`() {
        val id = RangeUUID.generate()
        val role = Role.PRODUCT_TECH
        val staffModel = DataLayerTestModelFactory.buildStaff(id = id, role = role)

        val prodTechHealthSubject = ProdTechHealthSubject(staffModel)

        assertEquals(id, prodTechHealthSubject.id)
        assertEquals(role, prodTechHealthSubject.role)
    }
}
