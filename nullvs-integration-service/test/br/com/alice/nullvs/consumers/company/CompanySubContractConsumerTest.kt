package br.com.alice.nullvs.consumers.company

import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.events.CompanySubContractCreatedEvent
import br.com.alice.business.events.CompanySubContractUpdatedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.common.kafka.internals.LocalProducer
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.NullvsIntegrationLog
import br.com.alice.moneyin.client.InvoiceItemService
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.consumers.ConsumerTest
import br.com.alice.nullvs.events.NullvsCompanySubContractActivatedEvent
import br.com.alice.nullvs.events.NullvsCompanySubContractBatchRequestEvent
import br.com.alice.nullvs.events.NullvsSyncCompanySubcontractRequestEvent
import br.com.alice.nullvs.exceptions.ContractDoesNotHaveStartAtDate
import br.com.alice.nullvs.exceptions.DependsOnModelException
import br.com.alice.nullvs.exceptions.ExternalClientNotFound
import br.com.alice.nullvs.exceptions.ExternalCompanyContractNotFound
import br.com.alice.nullvs.exceptions.NatureRequiredException
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.NatureCode
import br.com.alice.nullvs.models.company.CompanySubContractData
import br.com.alice.nullvs.models.company.ContractType
import br.com.alice.nullvs.models.company.NullvsCompanySubContractBatchRequest
import br.com.alice.nullvs.services.internals.CompanyInfoCacheService
import br.com.alice.nullvs.services.internals.TotvsCompanyIntegrationService
import br.com.alice.nullvs.services.internals.dependency.NullvsDependencyService
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.time.Month
import kotlin.test.BeforeTest

class CompanySubContractConsumerTest : ConsumerTest() {
    private val totvsCompanyIntegrationService: TotvsCompanyIntegrationService = mockk()
    private val contractService: CompanyContractService = mockk()
    private val invoiceItemService: InvoiceItemService = mockk()
    private val nullvsDependencyService: NullvsDependencyService = mockk()
    private val companyInfoCacheService: CompanyInfoCacheService = mockk()

    private val consumer =
        CompanySubContractConsumer(
            totvsCompanyIntegrationService,
            LocalProducer,
            contractService,
            invoiceItemService,
            nullvsDependencyService,
            companyInfoCacheService,
        )

    companion object {
        @JvmStatic
        fun exceptions() = listOf(
            ExternalCompanyContractNotFound(""),
            ExternalClientNotFound(""),
            NatureRequiredException(""),
            ContractDoesNotHaveStartAtDate("")
        )
    }

    @BeforeTest
    fun setup() {
        super.before()
        LocalProducer.clearMessages()
    }

    @Nested
    inner class CompanySubContractCreated {

        private val subcontract = TestModelFactory.buildCompanySubContract()
        private val event = CompanySubContractCreatedEvent(subcontract)
        private val meta = Meta(
            eventId = event.messageId,
            eventName = event.name,
            internalModelName = InternalModelType.SUBCONTRACT,
            internalId = subcontract.id,
            externalModelName = ExternalModelType.SUBCONTRACT,
            integratedAt = event.eventDate,
            originalTopic = CompanySubContractCreatedEvent.name,
            integrationEventName = NullvsCompanySubContractBatchRequestEvent.name,
        )

        val request = NullvsCompanySubContractBatchRequest(
            meta = meta,
            action = NullvsActionType.CREATE,
            subContractData = CompanySubContractData(
                number = null,
                isBillingLevel = subcontract.isBillingLevel,
                codeClient = "0001",
                dueDate = subcontract.dueDate,
                id = subcontract.id,
                groupCompany = "0001",
                contractType = ContractType.BUSINESS,
                hasProRata = true,
                billingGroup = "0004",
                startedAt = LocalDate.now(),
                cnpj = "*********00100",
                description = "Legal Company",
                shortDescription = "Company",
                contractId = subcontract.contractId,
                contractNumber = "00000001",
                readjustmentMonth = Month.JULY,
                nature = NatureCode.B2B_ALICE.value,
            )
        )

        @Test
        fun `#should produce NullvsCompanySubContractBatchRequestEvent`() = runBlocking {
            coEvery { totvsCompanyIntegrationService.createSubContract(meta, subcontract) } returns request

            val result = consumer.syncSubcontractCreated(event)

            ResultAssert.assertThat(result).isSuccessWithData(true)

            Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanySubContractBatchRequestEvent.name)).isTrue

            coVerifyOnce { totvsCompanyIntegrationService.createSubContract(meta, subcontract) }
        }

        @Test
        fun `#should ignore error in case of ExternalClientNotFound exception`() = runBlocking {
            coEvery {
                totvsCompanyIntegrationService.createSubContract(
                    meta,
                    subcontract
                )
            } returns ExternalClientNotFound(subcontract.id)

            val result = consumer.syncSubcontractCreated(event)

            ResultAssert.assertThat(result).isSuccessWithData(false)

            Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanySubContractBatchRequestEvent.name)).isFalse

            coVerifyOnce { totvsCompanyIntegrationService.createSubContract(meta, subcontract) }
        }

        @Test
        fun `#should ignore error in case of DependsOnModelException exception`() = runBlocking {
            coEvery {
                totvsCompanyIntegrationService.createSubContract(
                    meta,
                    subcontract
                )
            } returns DependsOnModelException(subcontract.id, InternalModelType.SUBCONTRACT)

            val result = consumer.syncSubcontractCreated(event)

            ResultAssert.assertThat(result).isSuccessWithData(false)

            Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanySubContractBatchRequestEvent.name)).isFalse

            coVerifyOnce { totvsCompanyIntegrationService.createSubContract(meta, subcontract) }
        }

        @ParameterizedTest(name = "should ignore error in case of exception {0}")
        @MethodSource("br.com.alice.nullvs.consumers.company.CompanySubContractConsumerTest#exceptions")
        fun `#should ignore error in case of exception`(exception: BadRequestException) = runBlocking {
            coEvery {
                totvsCompanyIntegrationService.createSubContract(
                    meta,
                    subcontract
                )
            } returns exception

            val result = consumer.syncSubcontractCreated(event)

            ResultAssert.assertThat(result).isSuccessWithData(false)
            Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanySubContractBatchRequestEvent.name)).isFalse

            coVerifyOnce { totvsCompanyIntegrationService.createSubContract(meta, subcontract) }
        }
    }

    @Nested
    inner class CompanySubContractUpdated {

        private val subcontract = TestModelFactory.buildCompanySubContract()
        private val event = CompanySubContractUpdatedEvent(subcontract)
        private val meta = Meta(
            eventId = event.messageId,
            eventName = event.name,
            internalModelName = InternalModelType.SUBCONTRACT,
            internalId = subcontract.id,
            externalModelName = ExternalModelType.SUBCONTRACT,
            integratedAt = event.eventDate,
            originalTopic = CompanySubContractUpdatedEvent.name,
            integrationEventName = NullvsCompanySubContractBatchRequestEvent.name,
        )

        val request = NullvsCompanySubContractBatchRequest(
            meta = meta,
            action = NullvsActionType.CREATE,
            subContractData = CompanySubContractData(
                number = null,
                isBillingLevel = subcontract.isBillingLevel,
                codeClient = "0001",
                dueDate = subcontract.dueDate,
                id = subcontract.id,
                groupCompany = "0001",
                contractType = ContractType.BUSINESS,
                hasProRata = true,
                billingGroup = "0004",
                startedAt = LocalDate.now(),
                cnpj = "*********00100",
                description = "Legal Company",
                shortDescription = "Company",
                contractId = subcontract.contractId,
                contractNumber = "00000001",
                readjustmentMonth = Month.JULY,
                nature = NatureCode.B2B_ALICE.value,
            )
        )

        @Test
        fun `#should produce NullvsCompanySubContractBatchRequestEvent`() = runBlocking {
            coEvery { totvsCompanyIntegrationService.updateSubContract(meta, subcontract) } returns request

            val result = consumer.syncSubcontractUpdated(event)

            ResultAssert.assertThat(result).isSuccessWithData(true)

            Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanySubContractBatchRequestEvent.name)).isTrue

            coVerifyOnce {
                totvsCompanyIntegrationService.updateSubContract(meta, subcontract)
            }
        }

        @Test
        fun `#should ignore error in case of ExternalClientNotFound exception`() = runBlocking {
            coEvery {
                totvsCompanyIntegrationService.updateSubContract(
                    meta,
                    subcontract
                )
            } returns ExternalClientNotFound(subcontract.id)

            val result = consumer.syncSubcontractUpdated(event)

            ResultAssert.assertThat(result).isSuccessWithData(false)

            Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanySubContractBatchRequestEvent.name)).isFalse

            coVerifyOnce {
                totvsCompanyIntegrationService.updateSubContract(meta, subcontract)
            }
        }

        @Test
        fun `#should ignore error in case of DependsOnModelException exception`() = runBlocking {
            coEvery {
                totvsCompanyIntegrationService.updateSubContract(
                    meta,
                    subcontract
                )
            } returns DependsOnModelException(subcontract.id, InternalModelType.SUBCONTRACT)

            val result = consumer.syncSubcontractUpdated(event)

            ResultAssert.assertThat(result).isSuccessWithData(false)

            Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanySubContractBatchRequestEvent.name)).isFalse

            coVerifyOnce {
                totvsCompanyIntegrationService.updateSubContract(meta, subcontract)
            }
        }

        @Test
        fun `#should ignore error in case of ExternalCompanyContractNotFound exception`() =
            runBlocking {
                coEvery {
                    totvsCompanyIntegrationService.updateSubContract(
                        meta,
                        subcontract
                    )
                } returns ExternalCompanyContractNotFound(subcontract.id)

                val result = consumer.syncSubcontractUpdated(event)

                ResultAssert.assertThat(result).isSuccessWithData(false)
                Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanySubContractBatchRequestEvent.name)).isFalse

                coVerifyOnce {
                    totvsCompanyIntegrationService.updateSubContract(meta, subcontract)
                }
            }
    }

    @Nested
    inner class SyncSubcontract {
        private val subcontract = TestModelFactory.buildCompanySubContract()
        private val event = NullvsSyncCompanySubcontractRequestEvent(subcontract, NullvsActionType.CREATE)
        private val meta = Meta(
            eventId = event.messageId,
            eventName = event.name,
            internalModelName = InternalModelType.SUBCONTRACT,
            internalId = subcontract.id,
            externalModelName = ExternalModelType.SUBCONTRACT,
            integratedAt = event.eventDate,
            originalTopic = "backfill-sync",
            integrationEventName = NullvsCompanySubContractBatchRequestEvent.name,
        )

        val request = NullvsCompanySubContractBatchRequest(
            meta = meta,
            action = NullvsActionType.CREATE,
            subContractData = CompanySubContractData(
                number = null,
                isBillingLevel = subcontract.isBillingLevel,
                codeClient = "0001",
                dueDate = subcontract.dueDate,
                id = subcontract.id,
                groupCompany = "0001",
                contractType = ContractType.BUSINESS,
                hasProRata = true,
                billingGroup = "0004",
                startedAt = LocalDate.now(),
                cnpj = "*********00100",
                description = "Legal Company",
                shortDescription = "Company",
                contractId = subcontract.contractId,
                contractNumber = "00000001",
                readjustmentMonth = Month.JULY,
                nature = NatureCode.B2B_ALICE.value,
            )
        )

        @Test
        fun `#should return an exception when something is wrong`() = runBlocking {
            coEvery {
                totvsCompanyIntegrationService.createSubContract(
                    meta,
                    subcontract,
                )
            } returns Exception("")

            val result = consumer.syncSubcontract(event)

            ResultAssert.assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce { totvsCompanyIntegrationService.createSubContract(meta, subcontract) }
        }

        @Test
        fun `#should return success when the DependsOnModelException is thrown`() = runBlocking {
            coEvery {
                totvsCompanyIntegrationService.createSubContract(
                    meta,
                    subcontract,
                )
            } returns DependsOnModelException(subcontract.id, InternalModelType.SUBCONTRACT)

            val result = consumer.syncSubcontract(event)

            ResultAssert.assertThat(result).isSuccess()

            coVerifyOnce { totvsCompanyIntegrationService.createSubContract(meta, subcontract) }
        }

        @Test
        fun `#should throw AutoRetryableException when DependsOnModelException is thrown and isFromDependencySystem is true`() =
            runBlocking {
                val event = NullvsSyncCompanySubcontractRequestEvent(
                    subcontract,
                    NullvsActionType.CREATE,
                    isFromDependencySystem = true
                )

                val meta = Meta(
                    eventId = event.messageId,
                    eventName = event.name,
                    internalModelName = InternalModelType.SUBCONTRACT,
                    internalId = subcontract.id,
                    externalModelName = ExternalModelType.SUBCONTRACT,
                    integratedAt = event.eventDate,
                    originalTopic = "backfill-sync",
                    integrationEventName = NullvsCompanySubContractBatchRequestEvent.name,
                )

                coEvery {
                    totvsCompanyIntegrationService.createSubContract(
                        meta,
                        subcontract,
                    )
                } returns DependsOnModelException(subcontract.id, InternalModelType.SUBCONTRACT)

                val result = consumer.syncSubcontract(event)

                ResultAssert.assertThat(result).isFailureOfType(AutoRetryableException::class)

                coVerifyOnce { totvsCompanyIntegrationService.createSubContract(meta, subcontract) }
            }

        @Test
        fun `#should return an exception when action is invalid`() = runBlocking {
            val invalidEvent =
                NullvsSyncCompanySubcontractRequestEvent(subcontract, action = NullvsActionType.UPDATE_PAYMENT)
            val result = consumer.syncSubcontract(invalidEvent)

            ResultAssert.assertThat(result).isFailureOfType(Exception::class)
        }

        @Test
        fun `#should produce NullvsSyncCompanySubcontractRequestEvent for create`() =
            runBlocking {
                coEvery {
                    totvsCompanyIntegrationService.createSubContract(
                        meta,
                        subcontract,
                    )
                } returns request

                val result = consumer.syncSubcontract(event)

                ResultAssert.assertThat(result).isSuccessWithData(true)

                Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanySubContractBatchRequestEvent.name)).isTrue

                coVerifyOnce {
                    totvsCompanyIntegrationService.createSubContract(
                        meta,
                        subcontract,
                    )
                }

            }

        @Test
        fun `#should produce NullvsCompanySubContractBatchRequestEvent for update`() =
            runBlocking {
                val updateEvent = NullvsSyncCompanySubcontractRequestEvent(subcontract, NullvsActionType.UPDATE)
                val updateMeta = meta.copy(eventId = updateEvent.messageId, integratedAt = updateEvent.eventDate)
                val requestUpdate =
                    request.copy(meta = updateMeta, action = NullvsActionType.UPDATE)

                coEvery {
                    totvsCompanyIntegrationService.updateSubContract(
                        updateMeta, subcontract,
                    )
                } returns requestUpdate

                val result = consumer.syncSubcontract(updateEvent)

                ResultAssert.assertThat(result).isSuccessWithData(true)

                Assertions.assertThat(LocalProducer.hasEvent(NullvsCompanySubContractBatchRequestEvent.name)).isTrue

                coVerifyOnce {
                    totvsCompanyIntegrationService.updateSubContract(
                        updateMeta, subcontract,
                    )
                }
            }
    }

    @Nested
    inner class SyncInvoiceItemFromSubcontract {
        private val subcontract = TestModelFactory.buildCompanySubContract()
        private val event = CompanySubContractUpdatedEvent(subcontract)

        @Test
        fun `#should return true when externalId is null`() = runBlocking {
            val subcontractNotActive = subcontract.copy(externalId = null)
            val event = CompanySubContractUpdatedEvent(subcontractNotActive)

            val result = consumer.syncInvoiceItemFromSubcontract(event)

            ResultAssert.assertThat(result).isSuccessWithData(true)
        }

        @Test
        fun `#should return true when LocalDate is before contract startedAt`() = runBlocking {
            val contract = TestModelFactory.buildCompanyContract(startedAt = LocalDate.now().plusDays(1))
            coEvery { contractService.get(subcontract.contractId) } returns contract

            val result = consumer.syncInvoiceItemFromSubcontract(event)

            ResultAssert.assertThat(result).isSuccessWithData(true)
        }

        @Test
        fun `#should return true when LocalDate is before contract startedAt is null`() = runBlocking {
            val contract = TestModelFactory.buildCompanyContract(startedAt = null)
            coEvery { contractService.get(subcontract.contractId) } returns contract

            val result = consumer.syncInvoiceItemFromSubcontract(event)

            ResultAssert.assertThat(result).isSuccessWithData(true)
        }

        @Test
        fun `#should produce InvoiceItemCreatedEvent`(): Unit = runBlocking {
            val contract = TestModelFactory.buildCompanyContract(startedAt = LocalDate.now().minusDays(1))
            coEvery { contractService.get(subcontract.contractId) } returns contract

            val invoiceItem = TestModelFactory.buildInvoiceItem()
            coEvery {
                invoiceItemService.listInvoiceItemsByCompanyAndSubcontract(
                    subcontract.companyId,
                    subcontract.id
                )
            } returns listOf(invoiceItem)

            val result = consumer.syncInvoiceItemFromSubcontract(event)

            ResultAssert.assertThat(result).isSuccessWithData(true)

            Assertions.assertThat(LocalProducer.hasEvent("invoice-item-created")).isTrue
        }
    }

    @Nested
    inner class ReprocessSubcontractDependencies {

        @Test
        fun `#should reprocess all dependents`() = runBlocking {
            val event = NullvsCompanySubContractActivatedEvent(
                RangeUUID.generate(),
                "*********",
                "0022",
                "00000001"
            )

            coEvery {
                companyInfoCacheService.invalidate(event.payload.companySubContractId)
            } returns 1

            coEvery {
                nullvsDependencyService.reprocessItsDependents(
                    event.payload.companySubContractId,
                    InternalModelType.SUBCONTRACT
                )
            } returns emptyList()

            val result = consumer.reprocessSubcontractDependencies(event)

            ResultAssert.assertThat(result).isSuccessWithData(emptyList<NullvsIntegrationLog>())

            coVerifyOnce {
                companyInfoCacheService.invalidate(event.payload.companySubContractId)
                nullvsDependencyService.reprocessItsDependents(
                    event.payload.companySubContractId,
                    InternalModelType.SUBCONTRACT
                )
            }
        }

        @Test
        fun `#should skip the consumer when the FF use_dependency_service is false`() = runBlocking {
            withFeatureFlag(FeatureNamespace.NULLVS, "use_dependency_service", false) {
                val event = NullvsCompanySubContractActivatedEvent(
                    RangeUUID.generate(),
                    "*********",
                    "0022",
                    "00000001"
                )

                val result = consumer.reprocessSubcontractDependencies(event)

                ResultAssert.assertThat(result).isSuccessWithData(true)

                coVerifyNone {
                    nullvsDependencyService.reprocessItsDependents(
                        any(),
                        any()
                    )
                }
            }
        }
    }
}
