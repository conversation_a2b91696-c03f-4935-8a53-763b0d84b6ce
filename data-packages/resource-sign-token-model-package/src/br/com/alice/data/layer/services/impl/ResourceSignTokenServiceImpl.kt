package br.com.alice.data.layer.services.impl

import br.com.alice.common.RangeUUID
import br.com.alice.common.UUIDv7
import br.com.alice.common.core.extensions.isBeforeEq
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.toLocalDateTime
import br.com.alice.data.layer.converter.toModel
import br.com.alice.data.layer.converter.toTransport
import br.com.alice.data.layer.models.ResourceSignToken
import br.com.alice.data.layer.models.ResourceSignTokenType
import br.com.alice.data.layer.services.ResourceSignTokenModelDataService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.Duration
import java.time.LocalDateTime
import java.util.UUID

class ResourceSignTokenServiceImpl(
    private val resourceSignTokenModelDataService: ResourceSignTokenModelDataService
) {

    suspend fun generateResourceSignToken(
        resourceId: String,
        resourceType: ResourceSignTokenType
    ): Result<ResourceSignToken, Throwable> = resourceSignTokenModelDataService.add(
        ResourceSignToken(
            signUuid = RangeUUID.generateUUIDv7(),
            resourceType = resourceType,
            resourceId = resourceId
        ).toModel()
    ).map {
        it.toTransport()
    }

    suspend fun isSignTokenValid(
        signUuid: UUIDv7,
        resource: ResourceSignTokenType,
        expirationDate: Duration,
        resourceId: String
    ): Result<Boolean, Throwable> = findSignTokenBySignUUID(signUuid, resource, resourceId).flatMap {
        if (it.isExpired(expirationDate)) {
            logger.error("Sign Token with id $signUuid is expired")
            return false.success()
        }

        true.success()
    }.coFoldNotFound {
        logger.error("Sign Token with id $signUuid not found")
        false.success()
    }

    suspend fun findSignTokenByResourceIdAndResourceType(
        resourceId: String,
        resourceType: ResourceSignTokenType
    ): Result<ResourceSignToken, Throwable> = resourceSignTokenModelDataService.findOne {
        where {
            this.resourceId.eq(resourceId) and this.resourceType.eq(resourceType)
        }
    }.map {
        it.toTransport()
    }

    private suspend fun findSignTokenBySignUUID(
        signUuid: UUIDv7,
        resource: ResourceSignTokenType,
        resourceId: String
    ): Result<ResourceSignToken, Throwable> = resourceSignTokenModelDataService.findOne {
        where {
            this.signUuid.eq(signUuid)
            this.resourceType.eq(resource)
            this.resourceId.eq(resourceId)
        }
    }.map {
        it.toTransport()
    }

    private fun ResourceSignToken.isExpired(expirationDate: Duration): Boolean {
        val localDateTime = this.signUuid.toLocalDateTime()
        return localDateTime.plus(expirationDate).isBeforeEq(LocalDateTime.now())
    }

    suspend fun softDeleteSignToken(id: UUID): Result<Boolean, Throwable> = resourceSignTokenModelDataService.get(id)
        .flatMap { resourceSignTokenModelDataService.softDelete(it) }

}
