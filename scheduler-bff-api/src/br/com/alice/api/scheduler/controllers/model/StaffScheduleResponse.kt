package br.com.alice.api.scheduler.controllers.model

import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.StaffScheduleType
import io.ktor.util.date.WeekDay
import java.time.LocalTime
import java.util.UUID

data class StaffScheduleResponse(
    val staffId: UUID,
    val id: UUID?,
    val startHour: LocalTime,
    var untilHour: LocalTime,
    val weekDay: WeekDay,
    val type: StaffScheduleType,
    val providerUnitId: UUID?,
    val alsoDigital: Boolean,
    val exceptionEventTypes: List<UUID>? = emptyList(),
)

data class StaffScheduleProviderResponse(
    val staffId: UUID,
    val id: UUID?,
    val startHour: LocalTime,
    var untilHour: LocalTime,
    val weekDay: WeekDay,
    val type: StaffScheduleType,
    val providerUnitId: UUID?,
    val providerUnitName: String?,
    val alsoDigital: <PERSON>olean,
    val exceptionEventTypes: List<UUID>? = emptyList(),
)

data class StaffSchedulesResponse(
    val staffSchedules: List<StaffScheduleProviderResponse>,
    val updatedAt: String?,
    val lastUpdatedBy: StaffSchedulesLastUpdatedBy?,
)

data class StaffSchedulesLastUpdatedBy(
    val id: UUID,
    val name: String,
)

data class BulkStaffScheduleUpsertResponse(
    val successfulUpsertedStaffSchedules: List<StaffScheduleResponse>,
    val unsuccessfulUpsertedStaffSchedules: List<StaffScheduleResponse>,
)

data class StaffScheduleRequest(
    val id: UUID? = null,
    val startHour: LocalTime,
    var untilHour: LocalTime,
    val weekDay: String,
    val type: StaffScheduleType? = StaffScheduleType.HAD,
    val providerUnitId: UUID? = null,
    val alsoDigital: Boolean,
    val exceptionEventTypes: List<UUID>? = emptyList(),
)

data class BulkStaffScheduleRequest(
    val staffScheduleRequests: List<StaffScheduleRequest> = emptyList()
)

data class StaffScheduleEventTypeAssociationRequest(
    val appointmentScheduleEventTypeId: UUID,
    val isDisassociation: Boolean,
)

fun StaffScheduleResponse.toProviderResponse(providerUnitMap: Map<UUID, ProviderUnit>) = StaffScheduleProviderResponse(
    staffId = staffId,
    id = id,
    type = type,
    exceptionEventTypes = exceptionEventTypes,
    weekDay = weekDay,
    startHour = startHour,
    untilHour = untilHour,
    providerUnitId = providerUnitId,
    providerUnitName = providerUnitId?.let { providerUnitMap[it]?.name },
    alsoDigital = alsoDigital
)
