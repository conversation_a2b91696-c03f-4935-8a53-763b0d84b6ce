package br.com.alice.data.layer.models

import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.logging.logger
import br.com.alice.common.serialization.gson
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class InvoicePayment(
    val amount: BigDecimal,
    val approvedAt: LocalDateTime? = null,
    val status: InvoicePaymentStatus = InvoicePaymentStatus.PENDING,
    val method: PaymentMethod = PaymentMethod.BOLETO,
    val reason: PaymentReason? = null,
    val canceledReason: CancellationReason? = null,
    val memberInvoiceIds: List<UUID>,
    val invoiceGroupId: UUID? = null,
    val externalId: String? = null,
    val source: InvoicePaymentSource? = null,
    val failReason: String? = null,
    val billingAccountablePartyId: UUID? = null,
    val origin: InvoicePaymentOrigin? = InvoicePaymentOrigin.UNDEFINED,
    val amountPaid: BigDecimal? = null,
    val fine: BigDecimal? = null,
    val interest: BigDecimal? = null,
    val approvedByStaffId: UUID? = null,
    val invoiceLiquidationId: UUID? = null,
    val preActivationPaymentId: UUID? = null,
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val paymentDetailString: String? = null,
    val sendEmail: Boolean? = null,
) {

    val memberInvoiceId get() = this.memberInvoiceIds.firstOrNull() // TODO: We shall refactor it once we review invoice <> payments rules, for now this will be kept just to maintain the same behavior we have so far.

    val dueDateFromDetail
        get() = when (paymentDetail?.method) {
            PaymentMethod.BOLETO -> (paymentDetail as BoletoPaymentDetail).dueDate
            PaymentMethod.BOLEPIX -> (paymentDetail as BolepixPaymentDetail).dueDate
            PaymentMethod.PIX -> (paymentDetail as PixPaymentDetail).dueDate
            else -> null
        }

    fun approve(
        approvedAt: LocalDateTime = LocalDateTime.now(),
        amountPaid: BigDecimal? = null,
        fine: BigDecimal? = null,
        interest: BigDecimal? = null,
        approvedByStaffId: UUID? = null,
    ) = copy(
        status = InvoicePaymentStatus.APPROVED,
        approvedAt = approvedAt,
        amountPaid = amountPaid,
        fine = fine,
        interest = interest,
        approvedByStaffId = approvedByStaffId
    )

    fun decline() = copy(
        status = InvoicePaymentStatus.DECLINED,
    )

    fun cancel(cancellationReason: CancellationReason) = copy(
        status = InvoicePaymentStatus.CANCELED,
        canceledReason = cancellationReason
    )

    fun fail(failReason: String) = copy(
        status = InvoicePaymentStatus.FAILED,
        failReason = failReason
    )

    fun expire() = copy(
        status = InvoicePaymentStatus.EXPIRED
    )

    fun associateGroup(memberInvoiceGroup: MemberInvoiceGroup) = copy(invoiceGroupId = memberInvoiceGroup.id)

    fun withPaymentDetail(paymentDetail: PaymentDetail?): InvoicePayment = paymentDetail?.let {
        if (it.paymentId != id)
            logger.warn(
                "PaymentDetail to include has different paymentId than this id. Changing that",
                "payment_detail_payment_id" to it.paymentId,
                "this_id" to id,
            )
        val paymentDetailStr = gson.toJson(it.withPaymentId(id))
        logger.info("payment_details", "detail" to paymentDetailStr)
        copy(paymentDetailString = paymentDetailStr)
    } ?: copy(paymentDetailString = null)

    val paymentDetail: PaymentDetail?
        get() = this.paymentDetailString?.let {
            when (method) {
                PaymentMethod.BOLETO -> gson.fromJson(it, BoletoPaymentDetail::class.java)
                PaymentMethod.SIMPLE_CREDIT_CARD -> gson.fromJson(it, SimpleCreditCardPaymentDetail::class.java)
                PaymentMethod.PIX -> gson.fromJson(it, PixPaymentDetail::class.java)
                PaymentMethod.BOLEPIX -> gson.fromJson(it, BolepixPaymentDetail::class.java)
            }
        }

    val isBoleto = method == PaymentMethod.BOLETO
    val isBolepix = method == PaymentMethod.BOLEPIX
    val isPix = method == PaymentMethod.PIX
    val isCreditCard = method == PaymentMethod.SIMPLE_CREDIT_CARD

    val isPending = status == InvoicePaymentStatus.PENDING
    val isCanceled = status == InvoicePaymentStatus.CANCELED
    val isApproved = approvedAt != null && status == InvoicePaymentStatus.APPROVED
    val isExpired = status == InvoicePaymentStatus.EXPIRED

    val isFirstPayment = reason == PaymentReason.B2B_FIRST_PAYMENT || reason == PaymentReason.FIRST_PAYMENT
}

enum class InvoicePaymentSource {
    ADYEN,
    ITAU,
    IUGU
}

enum class InvoicePaymentStatus {
    PENDING, APPROVED, DECLINED, CANCELED, FAILED, EXPIRED
}

enum class InvoicePaymentOrigin {
    UNDEFINED, ISSUED_BY_MEMBER, ISSUED_BY_STAFF, ISSUED_BY_NULLVS, ISSUED_BY_HEALTHCARE_OPS, ISSUED_BY_HR_PORTAL, ISSUED_BY_BACKFILL
}

enum class PaymentReason {
    FIRST_PAYMENT,
    REGULAR_PAYMENT,
    OVERDUE_PAYMENT,
    B2B_REGULAR_PAYMENT,
    B2B_FIRST_PAYMENT,
    B2B_LIQUIDATION,
    B2C_LIQUIDATION,
    B2B_PRE_ACTIVATION_PAYMENT,
    B2C_PRE_ACTIVATION_PAYMENT;


    fun isB2B() = when (this) {
        B2B_REGULAR_PAYMENT, B2B_FIRST_PAYMENT, B2B_LIQUIDATION, B2B_PRE_ACTIVATION_PAYMENT -> true
        else -> false
    }

    fun isLiquidation() = when (this) {
        B2B_LIQUIDATION, B2C_LIQUIDATION -> true
        else -> false
    }
}
