package br.com.alice.data.layer.pipelines.subjects

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import org.junit.jupiter.api.Assertions.assertEquals
import kotlin.test.Test

class ExternalHealthProfessionalSubjectTest {

    @Test
    fun testExternalHealthProfessionalSubject() {
        val id = RangeUUID.generate()
        val role = Role.PSYCHOLOGIST
        val memberInPortfolio = true

        val subject = ExternalHealthProfessionalSubject(id, role, memberInPortfolio)

        assertEquals(id, subject.id)
        assertEquals(role, subject.role)
        assertEquals(memberInPortfolio, subject.memberInPortfolio)
    }
}
