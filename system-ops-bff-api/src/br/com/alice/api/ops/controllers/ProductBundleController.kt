package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.converters.ProductBundleConverter
import br.com.alice.api.ops.converters.ProviderResponseConverter
import br.com.alice.api.ops.converters.SpecialistConverter
import br.com.alice.api.ops.models.ProductBundleRequest
import br.com.alice.common.ErrorResponse
import br.com.alice.common.Response
import br.com.alice.common.core.Status
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.then
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.ProductBundle
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.product.client.ProductBundleService
import br.com.alice.provider.client.ProviderFilter
import br.com.alice.provider.client.ProviderService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.map
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class ProductBundleController(
    private val productBundleService: ProductBundleService,
    private val providerService: ProviderService,
    private val healthProfessionalService: HealthProfessionalService,
) : ProductRouteController() {

    private fun buildSpecialistIdsProductBundle(request: ProductBundleRequest) =
        ProductBundle(
            name = request.name,
            specialistIds = request.specialistIds!!.map { it.id.toUUID() },
            type = request.type,
            imageUrl = request.imageUrl,
            priceScale = request.priceScale,
            active = request.active,
        )

    private fun buildExternalSpecialistIdsProductBundle(request: ProductBundleRequest) =
        ProductBundle(
            name = request.name,
            externalSpecialists = request.externalSpecialistIds!!.map { it.id.toUUID() },
            type = request.type,
            imageUrl = request.imageUrl,
            priceScale = request.priceScale,
            active = request.active,
        )

    private fun buildSpecialtyTiersProductBundle(request: ProductBundleRequest) =
        ProductBundle(
            name = request.name,
            specialtyTiers = request.specialtyTiers!!,
            type = request.type,
            imageUrl = request.imageUrl,
            priceScale = request.priceScale,
            active = request.active,
        )

    suspend fun create(request: ProductBundleRequest) = productPermission {
        if (request.specialistIds != null && request.providerIds != null) {
            return@productPermission Response(
                status = HttpStatusCode.BadRequest,
                message = ErrorResponse(
                    code = "product_bundle_with_provider_and_specialist_ids",
                    message = "Informe providerIds ou specialistIds. Não é permitido preencher ambos os campos."
                )
            )
        }

        when (request.type) {
            ProductBundleType.SPECIALITY_TIERS -> {
                val productBundle = buildSpecialtyTiersProductBundle(request)

                return@productPermission productBundleService.add(productBundle)
                    .map { ProductBundleConverter.convert(it) }
                    .foldResponse()
            }

            ProductBundleType.SPECIALIST -> {
                if (request.specialistIds == null) {
                    return@productPermission Response(
                        status = HttpStatusCode.BadRequest,
                        message = ErrorResponse(
                            code = "product_bundle_without_provider_and_specialist_ids",
                            message = "Um dos campos providerIds ou specialistIds deve ser informado."
                        )
                    )
                }
                val productBundle = buildSpecialistIdsProductBundle(request)

                return@productPermission productBundleService.add(productBundle)
                    .map { ProductBundleConverter.convert(it) }
                    .foldResponse()
            }

            ProductBundleType.CASSI_SPECIALIST -> {
                if (request.externalSpecialistIds == null) {
                    return@productPermission Response(
                        status = HttpStatusCode.BadRequest,
                        message = ErrorResponse(
                            code = "product_bundle_without_provider_and_external_specialist_id",
                            message = "O campo external_specialist_id deve ser informado."
                        )
                    )
                }

                val productBundle = buildExternalSpecialistIdsProductBundle(request)

                return@productPermission productBundleService.add(productBundle)
                    .map { ProductBundleConverter.convert(it) }
                    .foldResponse()
            }

            else -> {
                if (request.providerIds != null) {
                    val providerIds = request.providerIds.map { it.id.toUUID() }
                        .sortedWith { a, b ->
                            val orderA = request.providerIds.indexOfFirst { p -> p.id.toUUID() == a }
                            val orderB = request.providerIds.indexOfFirst { p -> p.id.toUUID() == b }
                            when {
                                orderA > orderB -> 1
                                orderA < orderB -> -1
                                else -> 0
                            }
                        }
                    val productBundle = ProductBundle(
                        name = request.name,
                        providerIds = providerIds,
                        type = request.type,
                        imageUrl = request.imageUrl,
                        priceScale = request.priceScale,
                        active = request.active,
                    )

                    return@productPermission productBundleService.add(productBundle)
                        .map { ProductBundleConverter.convert(it) }
                        .foldResponse()
                }

                return@productPermission Response(
                    status = HttpStatusCode.BadRequest,
                    message = ErrorResponse(
                        code = "product_bundle_without_provider_and_specialist_ids",
                        message = "Um dos campos providerIds ou specialistIds deve ser informado."
                    )
                )
            }
        }
    }

    suspend fun index(queryParams: Parameters): Response {
        val range = parseRange(queryParams)
        val ids: List<String>? = parseFilter(queryParams, "id")
        val filters = parseFilter(queryParams)
        val filterQuery = filters?.get("q") as String?

        val filteredElements = if (filterQuery != null || ids.isNotNullOrEmpty()) {
            return getFiltered(ids, filterQuery)
        } else {
            productBundleService.findByRange(range).get()
        }.map { ProductBundleConverter.convert(it) }
        val total = productBundleService.countAll().get()

        return Response(
            HttpStatusCode.OK,
            filteredElements,
            mapOf(HttpHeaders.ContentRange to total.toString())
        )
    }

    private suspend fun getFiltered(ids: List<String>?, filterQuery: String?): Response {
        val filteredElements = if (filterQuery != null) {
            productBundleService.searchProduct(filterQuery).get()
        } else {
            ids!!.map(String::toUUID)
                .let { productBundleService.findActivesByIds(it) }
                .get()
        }.map { ProductBundleConverter.convert(it) }

        return Response(
            HttpStatusCode.OK,
            filteredElements,
            mapOf(HttpHeaders.ContentRange to filteredElements.size.toString())
        )
    }

    suspend fun get(id: String) =
        productBundleService.get(id.toUUID())
            .map { bundle -> ProductBundleConverter.convert(bundle) }
            .foldResponse()

    suspend fun update(id: String, request: ProductBundleRequest) = productPermission {
        if (request.specialistIds != null && request.providerIds != null) {
            return@productPermission Response(
                status = HttpStatusCode.BadRequest,
                message = ErrorResponse(
                    code = "product_bundle_with_provider_and_specialist_ids",
                    message = "Informe providerIds ou specialistIds. Não é permitido preencher ambos os campos."
                )
            )
        } else if (request.specialtyTiers != null) {
            val oldBundle = productBundleService.get(id.toUUID()).get()
            val updatedBundle = oldBundle.copy(
                name = request.name,
                specialtyTiers = request.specialtyTiers,
                type = request.type,
                imageUrl = request.imageUrl,
                priceScale = request.priceScale,
                active = request.active,
            )

            return@productPermission productBundleService.update(
                model = updatedBundle,
                oldModel = oldBundle
            ).then { updated ->
                logger.info(
                    "Updated ${updated::class.simpleName}", "current_staff_id" to currentUserIdKey(),
                    "request" to request, "model" to updated
                )
            }.map { ProductBundleConverter.convert(it) }.foldResponse()
        } else if (request.specialistIds != null) {
            val oldBundle = productBundleService.get(id.toUUID()).get()
            val updatedBundle = oldBundle.copy(
                name = request.name,
                specialistIds = request.specialistIds.map { it.id.toUUID() },
                type = request.type,
                imageUrl = request.imageUrl,
                priceScale = request.priceScale,
                active = request.active,
            )

            return@productPermission productBundleService.update(
                model = updatedBundle,
                oldModel = oldBundle
            ).then { updated ->
                logger.info(
                    "Updated ${updated::class.simpleName}", "current_staff_id" to currentUserIdKey(),
                    "request" to request, "model" to updated
                )
            }.map { ProductBundleConverter.convert(it) }.foldResponse()
            // TODO: Remove condition after aos front is dealing only with providerIds
        } else if (request.providerIds != null) {
            val oldBundle = productBundleService.get(id.toUUID()).get()
            val prodviderIds = request.providerIds.map { it.id.toUUID() }
                .sortedWith { a, b ->
                    val orderA = request.providerIds.indexOfFirst { p -> p.id.toUUID() == a }
                    val orderB = request.providerIds.indexOfFirst { p -> p.id.toUUID() == b }
                    when {
                        orderA > orderB -> 1
                        orderA < orderB -> -1
                        else -> 0
                    }
                }

            val updatedBundle = oldBundle.copy(
                name = request.name,
                providerIds = prodviderIds,
                type = request.type,
                imageUrl = request.imageUrl,
                priceScale = request.priceScale,
                active = request.active,
            )

            return@productPermission productBundleService.update(
                model = updatedBundle,
                oldModel = oldBundle
            ).then { updated ->
                logger.info(
                    "Updated ${updated::class.simpleName}", "current_staff_id" to currentUserIdKey(),
                    "request" to request, "model" to updated
                )
            }.map { ProductBundleConverter.convert(it) }.foldResponse()
        } else if (request.externalSpecialistIds != null) {
            val oldBundle = productBundleService.get(id.toUUID()).get()
            val updatedBundle = oldBundle.copy(
                name = request.name,
                externalSpecialists = request.externalSpecialistIds.map { it.id.toUUID() },
                type = request.type,
                imageUrl = request.imageUrl,
                priceScale = request.priceScale,
                active = request.active,
            )

            return@productPermission productBundleService.update(
                model = updatedBundle,
                oldModel = oldBundle
            ).then { updated ->
                logger.info(
                    "Updated ${updated::class.simpleName}", "current_staff_id" to currentUserIdKey(),
                    "request" to request, "model" to updated
                )
            }.map { ProductBundleConverter.convert(it) }.foldResponse()
        }

        return@productPermission Response(
            status = HttpStatusCode.BadRequest,
            message = ErrorResponse(
                code = "product_bundle_without_provider_and_specialist_ids",
                message = "Um dos campos providerIds ou specialistIds deve ser informado."
            )
        )
    }

    suspend fun getProviders(queryParams: Parameters) = coroutineScope {
        val range = parseRange(queryParams)
        val filterQuery = parseFilter(queryParams)
        val namePrefix = filterQuery?.get("q")?.toString()
        val productBundleId = filterQuery?.get("productBundleId")?.toString() ?: ""

        val productBundle = productBundleService.get(productBundleId.toUUID()).get()
        val providersDef = async {
            providerService.getByFiltersWithRange(
                ProviderFilter(
                    ids = productBundle.providerIds,
                    searchToken = namePrefix,
                    status = listOf(Status.ACTIVE, Status.INACTIVE)
                ),
                range
            )
        }
        val totalOfProvidersDef = async {
            providerService.countByFilters(
                ProviderFilter(
                    ids = productBundle.providerIds,
                    searchToken = namePrefix,
                    status = listOf(Status.ACTIVE, Status.INACTIVE)
                )
            ).get()
        }

        val providers = providersDef.await()
        val totalOfProviders = totalOfProvidersDef.await()

        val response = providers.get().map { provider ->
            ProviderResponseConverter.convert(provider)
        }

        return@coroutineScope Response(
            HttpStatusCode.OK,
            response,
            mapOf(HttpHeaders.ContentRange to totalOfProviders.toString())
        )
    }

    suspend fun getSpecialistsOfBundle(queryParams: Parameters) = coroutineScope {
        val range = parseRange(queryParams)
        val filterQuery = parseFilter(queryParams)
        val namePrefix = filterQuery?.get("q")?.toString()
        val productBundleId = filterQuery?.get("productBundleId")?.toString() ?: ""

        val product = productBundleService.get(productBundleId.toUUID()).get()
        val specialistsDef = async {
            healthProfessionalService.searchByNameAndIdWithRange(range, namePrefix, product.specialistIds).get()
        }
        val totalOfSpecialistsDef = async {
            healthProfessionalService.countByNameAndId(namePrefix, product.specialistIds).get()
        }

        val specialists = specialistsDef.await()
        val totalOfSpecialists = totalOfSpecialistsDef.await()

        return@coroutineScope Response(
            HttpStatusCode.OK,
            specialists.map { SpecialistConverter.convert(it) },
            mapOf(HttpHeaders.ContentRange to totalOfSpecialists.toString())
        )
    }
}
