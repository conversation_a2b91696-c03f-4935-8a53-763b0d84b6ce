package br.com.alice.api.backoffice.controllers.staff

import br.com.alice.api.backoffice.ControllerTestHelper
import br.com.alice.api.backoffice.mappers.staff.StaffOutputMapper
import br.com.alice.api.backoffice.mappers.staff.StaffFormOutputMapper
import br.com.alice.api.backoffice.transfers.CouncilTypeResponse
import br.com.alice.api.backoffice.transfers.StaffFormResponse
import br.com.alice.common.models.CouncilType
import io.ktor.http.HttpStatusCode
import br.com.alice.api.backoffice.transfers.StaffRolesResponse
import br.com.alice.api.backoffice.transfers.StaffTiersResponse
import br.com.alice.api.backoffice.transfers.StaffScoreResponse
import br.com.alice.common.core.StaffType
import br.com.alice.common.data.dsl.matchers.BFFResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsBFFJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.MedicalSpecialtyType
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.staff.client.StaffService
import br.com.alice.api.backoffice.services.StaffBackofficeService
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.success
import io.ktor.http.Parameters
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test
import org.assertj.core.api.Assertions.assertThat
import java.util.UUID

class StaffControllerTest : ControllerTestHelper() {

    private val staffService: StaffService = mockk()
    private val staffBackofficeService: StaffBackofficeService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val staffController = StaffController(
        staffService,
        staffBackofficeService,
        medicalSpecialtyService,
        providerUnitService
    )

    private val staff1 = TestModelFactory.buildStaff()
    private val staffs = listOf(staff1)
    private val total = 2
    private val queryParams = Parameters.build {
        append("page", "1")
        append("pageSize", "10")
    }
    private val expectedResponse = StaffOutputMapper.toPaginatedResponse(staffs, total, queryParams)

    @BeforeTest
    override fun setup() {
        clearAllMocks()
        super.setup()
        module.single { staffController }
    }

    @BeforeTest
    fun confirmMocks() = confirmVerified(staffService, staffBackofficeService, medicalSpecialtyService, providerUnitService)

    @Test
    fun `#index returns Staffs found by range`() {
        coEvery { staffService.findByRange(IntRange(0, 9)) } returns staffs.success()
        coEvery { staffService.count() } returns total.success()

        authenticatedAs(idToken, staff) {
            get("/staff?filter={}&page=1&pageSize=10&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { staffService.findByRange(any()) }
        coVerifyOnce { staffService.count() }
    }

    @Test
    fun `#index returns Staffs found by term`() {
        coEvery { staffService.findByTokenAndRange("STAFF", range = IntRange(0, 9)) } returns staffs.success()
        coEvery { staffService.countActiveByToken("STAFF") } returns total.success()

        authenticatedAs(idToken, staff) {
            get("/staff?filter={\"q\":\"STAFF\"}&page=1&pageSize=10&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { staffService.findByTokenAndRange(any(), any()) }
        coVerifyOnce { staffService.countActiveByToken(any()) }
    }



    @Test
    fun `#buildFormByStaffType returns form for COMMUNITY_SPECIALIST`() {
        // Mock data
        val specialties = listOf(MedicalSpecialty(
            name = "Specialty 1",
            type = MedicalSpecialtyType.SPECIALTY,
            urlSlug = "specialty-1"
        ))
        val providerUnits = listOf(ProviderUnit(
            name = "Provider Unit 1",
            id = UUID.randomUUID(),
            type = ProviderUnit.Type.HOSPITAL,
            site = "",
            cnpj = "",
            phones = emptyList(),
            workingPeriods = emptyList(),
            qualifications = emptyList(),
            imageUrl = null,
            providerId = UUID.randomUUID()
        ))
        val staffRoles = listOf(StaffRolesResponse(id = "role1", name = "Role 1", value = "ROLE1"))
        val staffTiers = listOf(StaffTiersResponse(id = "tier1", name = "Tier 1", value = "TIER1"))
        val staffScore = listOf(StaffScoreResponse(id = "score1", name = "Score 1", value = "SCORE1"))
        val councilTypes = listOf(CouncilTypeResponse(id = 1, name = "CRM"))

        // Mock service calls
        coEvery { medicalSpecialtyService.getActivesByType(any(), any()) } returns specialties.success()
        coEvery { providerUnitService.getByFilterWithRange(any(), any()) } returns providerUnits.success()
        coEvery { staffBackofficeService.getStaffRoles(StaffType.COMMUNITY_SPECIALIST) } returns staffRoles
        coEvery { staffBackofficeService.getStaffTiers() } returns staffTiers
        coEvery { staffBackofficeService.getStaffScore() } returns staffScore
        coEvery { staffBackofficeService.getCouncilTypes() } returns councilTypes

        // Expected response
        val expectedResponse = StaffFormResponse(
            specialties = specialties,
            providerUnits = providerUnits,
            staffRoles = staffRoles,
            staffTiers = staffTiers,
            staffScore = staffScore,
            councilTypes = councilTypes
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"COMMUNITY_SPECIALIST\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNotNull
                assertThat(responseBody.providerUnits).isNotNull
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNotNull
                assertThat(responseBody.staffScore).isNotNull
                assertThat(responseBody.councilTypes).isNotNull
            }
        }

        // Verify service calls
        coVerifyOnce { medicalSpecialtyService.getActivesByType(any(), any()) }
        coVerifyOnce { providerUnitService.getByFilterWithRange(any(), any()) }
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.COMMUNITY_SPECIALIST) }
        coVerifyOnce { staffBackofficeService.getStaffTiers() }
        coVerifyOnce { staffBackofficeService.getStaffScore() }
        coVerifyOnce { staffBackofficeService.getCouncilTypes() }
    }

    @Test
    fun `#buildFormByStaffType returns form for PITAYA`() {
        // Mock data
        val staffRoles = listOf(StaffRolesResponse(id = "role1", name = "Role 1", value = "ROLE1"))

        // Mock service calls
        coEvery { staffBackofficeService.getStaffRoles(StaffType.PITAYA) } returns staffRoles

        // Expected response
        val expectedResponse = StaffFormResponse(
            staffRoles = staffRoles
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"PITAYA\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNull()
                assertThat(responseBody.providerUnits).isNull()
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNull()
                assertThat(responseBody.staffScore).isNull()
            }
        }

        // Verify service calls
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.PITAYA) }
    }

    @Test
    fun `#buildFormByStaffType returns form for HEALTH_ADMINISTRATIVE`() {
        // Mock data
        val staffRoles = listOf(StaffRolesResponse(id = "role1", name = "Role 1", value = "ROLE1"))

        // Mock service calls
        coEvery { staffBackofficeService.getStaffRoles(StaffType.HEALTH_ADMINISTRATIVE) } returns staffRoles

        // Expected response
        val expectedResponse = StaffFormResponse(
            staffRoles = staffRoles
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"HEALTH_ADMINISTRATIVE\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNull()
                assertThat(responseBody.providerUnits).isNull()
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNull()
                assertThat(responseBody.staffScore).isNull()
            }
        }

        // Verify service calls
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.HEALTH_ADMINISTRATIVE) }
    }

    @Test
    fun `#searchProviderUnits returns provider units by search token`() {
        // Mock data
        val providerUnits = listOf(ProviderUnit(
            name = "Provider Unit 1",
            id = UUID.randomUUID(),
            type = ProviderUnit.Type.HOSPITAL,
            site = "",
            cnpj = "",
            phones = emptyList(),
            workingPeriods = emptyList(),
            qualifications = emptyList(),
            imageUrl = null,
            providerId = UUID.randomUUID()
        ))

        // Mock service calls
        coEvery { providerUnitService.getByFilterWithRange(any(), any()) } returns providerUnits.success()

        authenticatedAs(idToken, staff) {
            get("/staff/search_provider_units?filter={\"q\":\"Hospital\"}") { response ->
                assertThat(response).isOKWithData(providerUnits)
            }
        }

        // Verify service calls
        coVerifyOnce { providerUnitService.getByFilterWithRange(any(), any()) }
    }

    @Test
    fun `#searchProviderUnits returns empty list when search token is empty`() {
        // Mock data
        val providerUnits = emptyList<ProviderUnit>()

        // Mock service calls
        coEvery { providerUnitService.getByFilterWithRange(any(), any()) } returns providerUnits.success()

        authenticatedAs(idToken, staff) {
            get("/staff/search_provider_units?filter={\"q\":\"\"}") { response ->
                assertThat(response).isOKWithData(emptyList<ProviderUnit>())
            }
        }

        // Verify service calls
        coVerifyOnce { providerUnitService.getByFilterWithRange(match { it.searchToken == "" }, any()) }
    }

    @Test
    fun `#buildFormByStaffType returns form for HEALTH_PROFESSIONAL`() {
        // Mock data
        val specialties = listOf(MedicalSpecialty(
            name = "Specialty 1",
            type = MedicalSpecialtyType.SPECIALTY,
            urlSlug = "specialty-1"
        ))
        val providerUnits = listOf(ProviderUnit(
            name = "Provider Unit 1",
            id = UUID.randomUUID(),
            type = ProviderUnit.Type.HOSPITAL,
            site = "",
            cnpj = "",
            phones = emptyList(),
            workingPeriods = emptyList(),
            qualifications = emptyList(),
            imageUrl = null,
            providerId = UUID.randomUUID()
        ))
        val staffRoles = listOf(StaffRolesResponse(id = "role1", name = "Role 1", value = "ROLE1"))
        val staffTiers = listOf(StaffTiersResponse(id = "tier1", name = "Tier 1", value = "TIER1"))
        val staffScore = listOf(StaffScoreResponse(id = "score1", name = "Score 1", value = "SCORE1"))

        // Mock service calls
        coEvery { medicalSpecialtyService.getActivesByType(any(), any()) } returns specialties.success()
        coEvery { providerUnitService.getByFilterWithRange(any(), any()) } returns providerUnits.success()
        coEvery { staffBackofficeService.getStaffRoles(StaffType.HEALTH_PROFESSIONAL) } returns staffRoles
        coEvery { staffBackofficeService.getStaffTiers() } returns staffTiers
        coEvery { staffBackofficeService.getStaffScore() } returns staffScore

        // Expected response
        val expectedResponse = StaffFormResponse(
            specialties = specialties,
            providerUnits = providerUnits,
            staffRoles = staffRoles,
            staffTiers = staffTiers,
            staffScore = staffScore
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"HEALTH_PROFESSIONAL\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNotNull
                assertThat(responseBody.providerUnits).isNotNull
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNotNull
                assertThat(responseBody.staffScore).isNotNull
            }
        }

        // Verify service calls
        coVerifyOnce { medicalSpecialtyService.getActivesByType(any(), any()) }
        coVerifyOnce { providerUnitService.getByFilterWithRange(any(), any()) }
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.HEALTH_PROFESSIONAL) }
        coVerifyOnce { staffBackofficeService.getStaffTiers() }
        coVerifyOnce { staffBackofficeService.getStaffScore() }
    }

    @Test
    fun `#buildFormByStaffType returns form for PARTNER_HEALTH_PROFESSIONAL`() {
        // Mock data
        val specialties = listOf(MedicalSpecialty(
            name = "Specialty 1",
            type = MedicalSpecialtyType.SPECIALTY,
            urlSlug = "specialty-1"
        ))
        val providerUnits = listOf(ProviderUnit(
            name = "Provider Unit 1",
            id = UUID.randomUUID(),
            type = ProviderUnit.Type.HOSPITAL,
            site = "",
            cnpj = "",
            phones = emptyList(),
            workingPeriods = emptyList(),
            qualifications = emptyList(),
            imageUrl = null,
            providerId = UUID.randomUUID()
        ))
        val staffRoles = listOf(StaffRolesResponse(id = "role1", name = "Role 1", value = "ROLE1"))
        val staffTiers = listOf(StaffTiersResponse(id = "tier1", name = "Tier 1", value = "TIER1"))
        val staffScore = listOf(StaffScoreResponse(id = "score1", name = "Score 1", value = "SCORE1"))

        // Mock service calls
        coEvery { medicalSpecialtyService.getActivesByType(any(), any()) } returns specialties.success()
        coEvery { providerUnitService.getByFilterWithRange(any(), any()) } returns providerUnits.success()
        coEvery { staffBackofficeService.getStaffRoles(StaffType.PARTNER_HEALTH_PROFESSIONAL) } returns staffRoles
        coEvery { staffBackofficeService.getStaffTiers() } returns staffTiers
        coEvery { staffBackofficeService.getStaffScore() } returns staffScore

        // Expected response
        val expectedResponse = StaffFormResponse(
            specialties = specialties,
            providerUnits = providerUnits,
            staffRoles = staffRoles,
            staffTiers = staffTiers,
            staffScore = staffScore
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"PARTNER_HEALTH_PROFESSIONAL\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNotNull
                assertThat(responseBody.providerUnits).isNotNull
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNotNull
                assertThat(responseBody.staffScore).isNotNull
            }
        }

        // Verify service calls
        coVerifyOnce { medicalSpecialtyService.getActivesByType(any(), any()) }
        coVerifyOnce { providerUnitService.getByFilterWithRange(any(), any()) }
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.PARTNER_HEALTH_PROFESSIONAL) }
        coVerifyOnce { staffBackofficeService.getStaffTiers() }
        coVerifyOnce { staffBackofficeService.getStaffScore() }
    }
}
