package br.com.alice.nullvs.services.internals

import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CompanyContractIntegrationStatus
import br.com.alice.data.layer.models.CompanySubContractIntegrationStatus
import br.com.alice.data.layer.models.ContractFile
import br.com.alice.data.layer.models.ContractType
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.converters.NullvsCompanyConverter.toCompanyProductPriceListingData
import br.com.alice.nullvs.exceptions.BillingAccountablePartyIdRequiredException
import br.com.alice.nullvs.exceptions.BillingGroupRequiredException
import br.com.alice.nullvs.exceptions.ContractDoesNotHaveStartAtDate
import br.com.alice.nullvs.exceptions.ContractFileDoesNotExist
import br.com.alice.nullvs.exceptions.DependsOnModelException
import br.com.alice.nullvs.exceptions.ExternalClientNotFound
import br.com.alice.nullvs.exceptions.ExternalCompanyContractNotFound
import br.com.alice.nullvs.exceptions.NatureRequiredException
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.NatureCode
import br.com.alice.nullvs.models.company.CompanyContractData
import br.com.alice.nullvs.models.company.CompanySubContractData
import br.com.alice.nullvs.models.company.NullvsCompanyContractBatchRequest
import br.com.alice.nullvs.models.company.NullvsCompanySubContractBatchRequest
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TotvsCompanyIntegrationServiceTest {

    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService = mockk()
    private val contractService: CompanyContractService = mockk()
    private val companyService: CompanyService = mockk()
    private val nullvsIntegrationLogService: NullvsIntegrationLogService = mockk()
    private val subcontractService: CompanySubContractService = mockk()
    private val companyProductPriceListingService: CompanyProductPriceListingService = mockk()
    private val service =
        TotvsCompanyIntegrationService(
            nullvsIntegrationRecordService,
            contractService,
            companyService,
            companyProductPriceListingService,
            nullvsIntegrationLogService,
            subcontractService
        )

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    private val meta = Meta(
        eventId = RangeUUID.generate(),
        eventName = "event01",
        internalId = RangeUUID.generate(),
        internalModelName = InternalModelType.CONTRACT,
        integrationEventName = "integration01",
        externalId = null,
        externalModelName = ExternalModelType.CONTRACT,
        integratedAt = LocalDateTime.now().minusDays(1),
        originalTopic = "original01",
    )

    private val log = TestModelFactory.buildNullvsIntegrationLog(
        eventId = meta.eventId,
        eventName = meta.eventName,
        integrationEventName = meta.integrationEventName,
        internalId = meta.internalId,
        internalModelName = meta.internalModelName,
        externalModelName = meta.externalModelName,
        batchId = "",
        idSoc = "",
        payloadSequenceId = 1,
        status = LogStatus.TOTVS_NOT_CALLED,
    )

    @Nested
    inner class Contract {
        @Nested
        inner class CreateContract {
            private val contractFileIds = listOf(
                ContractFile(
                    id = RangeUUID.generate(),
                    type = ContractType.MAIN,
                )
            )

            @Test
            fun `#should create a NullvsCompanyContractBatchRequest`() = runBlocking {
                val contract = TestModelFactory.buildCompanyContract(
                    groupCompany = "0001",
                    isProRata = true,
                    nature = NatureCode.B2B_ALICE.value,
                    startedAt = LocalDate.now(),
                    contractFileIds = contractFileIds,
                )

                val result = service.createContract(meta, contract)

                ResultAssert.assertThat(result).isSuccessWithData(
                    NullvsCompanyContractBatchRequest(
                        meta,
                        NullvsActionType.CREATE,
                        CompanyContractData(
                            isBillingLevel = contract.isBillingLevel,
                            codeClient = null,
                            dueDate = contract.dueDate,
                            id = contract.id,
                            groupCompany = contract.groupCompany!!,
                            isProRata = contract.isProRata!!,
                            startedAt = contract.startedAt!!,
                            nature = NatureCode.B2B_ALICE.value,
                        )
                    )
                )

                coVerifyNone {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        any(),
                    )
                }
            }

            @Test
            fun `#should create a NullvsCompanyContractBatchRequest finding the client code`() = runBlocking {
                val billingAccountablePartyId = RangeUUID.generate()
                val record = TestModelFactory.buildNullvsIntegrationRecord(
                    internalId = billingAccountablePartyId,
                    externalId = "111000",
                )

                val contract = TestModelFactory.buildCompanyContract(
                    groupCompany = "0001",
                    billingAccountablePartyId = billingAccountablePartyId,
                    isProRata = true,
                    isBillingLevel = true,
                    nature = NatureCode.B2B_ALICE.value,
                    startedAt = LocalDate.now(),
                    contractFileIds = contractFileIds,
                )

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        billingAccountablePartyId,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                } returns record

                val result = service.createContract(meta, contract)

                ResultAssert.assertThat(result).isSuccessWithData(
                    NullvsCompanyContractBatchRequest(
                        meta,
                        NullvsActionType.CREATE,
                        CompanyContractData(
                            isBillingLevel = contract.isBillingLevel,
                            codeClient = "111000",
                            dueDate = contract.dueDate,
                            id = contract.id,
                            groupCompany = contract.groupCompany!!,
                            isProRata = contract.isProRata!!,
                            startedAt = contract.startedAt!!,
                            nature = NatureCode.B2B_ALICE.value,
                        )
                    )
                )

                coVerifyOnce {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        billingAccountablePartyId,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                }
            }

            @Test
            fun `#should not create a NullvsCompanyContractBatchRequest when the contract billing level is true but it does not have a billing accountable party`() =
                runBlocking {
                    val contract = TestModelFactory.buildCompanyContract(
                        groupCompany = "0001",
                        isProRata = true,
                        isBillingLevel = true,
                        contractFileIds = contractFileIds,
                    )
                    val failedContract = contract.copy(integrationStatus = CompanyContractIntegrationStatus.FAILED)

                    coEvery { contractService.get(contract.id) } returns contract
                    coEvery { contractService.update(failedContract) } returns failedContract
                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.createContract(meta, contract)

                        ResultAssert.assertThat(result)
                            .isFailureOfType(BillingAccountablePartyIdRequiredException::class)
                    }
                }

            @Test
            fun `#should not create a NullvsCompanyContractBatchRequest when the contract billing level is true but it does not have a billing accountable party - feature flag disabled`() =
                runBlocking {

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", false) {
                        val contract = TestModelFactory.buildCompanyContract(
                            groupCompany = "0001",
                            isProRata = true,
                            isBillingLevel = true,
                            contractFileIds = contractFileIds,
                        )
                        val failedContract = contract.copy(integrationStatus = CompanyContractIntegrationStatus.FAILED)

                        coEvery { contractService.get(contract.id) } returns contract
                        coEvery { contractService.update(failedContract) } returns failedContract
                        coEvery { nullvsIntegrationLogService.add(match { it.eventId == meta.eventId }) } returns log

                        val result = service.createContract(meta, contract)

                        ResultAssert.assertThat(result)
                            .isFailureOfType(BillingAccountablePartyIdRequiredException::class)

                        coVerifyOnce {
                            nullvsIntegrationLogService.add(match { it.eventId == meta.eventId })
                        }

                        coVerifyNone {
                            nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId })
                        }

                    }

                }

            @Test
            fun `#should not create a NullvsCompanyContractBatchRequest when some the contract file does not exist`() =
                runBlocking {
                    val contract = TestModelFactory.buildCompanyContract(
                        groupCompany = "0001",
                        isProRata = true,
                        isBillingLevel = true,
                        contractFileIds = emptyList(),
                    )

                    val result = service.createContract(meta, contract)

                    ResultAssert.assertThat(result).isFailureOfType(ContractFileDoesNotExist::class)
                }

            @Test
            fun `#should not create a NullvsCompanyContractBatchRequest when the contract billing level is true but it does not have a nature code`() =
                runBlocking {


                    val billingAccountablePartyId = RangeUUID.generate()
                    val contract = TestModelFactory.buildCompanyContract(
                        groupCompany = "0001",
                        isProRata = true,
                        isBillingLevel = true,
                        billingAccountablePartyId = billingAccountablePartyId,
                        nature = null,
                        contractFileIds = contractFileIds,
                    )
                    val failedContract = contract.copy(integrationStatus = CompanyContractIntegrationStatus.FAILED)

                    coEvery { contractService.get(contract.id) } returns contract
                    coEvery { contractService.update(failedContract) } returns failedContract
                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.createContract(meta, contract)
                        ResultAssert.assertThat(result).isFailureOfType(NatureRequiredException::class)
                    }
                }

            @Test
            fun `#should not create a NullvsCompanyContractBatchRequest when the external code client is not found`() =
                runBlocking {
                    val billingAccountablePartyId = RangeUUID.generate()
                    val contract = TestModelFactory.buildCompanyContract(
                        groupCompany = "0001",
                        isProRata = true,
                        isBillingLevel = true,
                        billingAccountablePartyId = billingAccountablePartyId,
                        nature = NatureCode.B2B_ALICE.value,
                        dueDate = 10,
                        contractFileIds = contractFileIds,
                    )
                    val failedContract = contract.copy(integrationStatus = CompanyContractIntegrationStatus.FAILED)

                    coEvery { contractService.get(contract.id) } returns contract
                    coEvery { contractService.update(failedContract) } returns failedContract
                    coEvery {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountablePartyId,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    } returns NotFoundException()

                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.createContract(meta, contract)

                        ResultAssert.assertThat(result).isFailureOfType(DependsOnModelException::class)
                    }
                }
        }

        @Nested
        inner class UpdateContract {
            @Test
            fun `#should create a NullvsCompanyContractBatchRequest`() = runBlocking {
                val contract = TestModelFactory.buildCompanyContract(
                    externalId = "0001",
                    groupCompany = "0001",
                    isProRata = true,
                    nature = NatureCode.B2B_ALICE.value,
                    startedAt = LocalDate.now(),
                )

                val result = service.updateContract(meta, contract)

                ResultAssert.assertThat(result).isSuccessWithData(
                    NullvsCompanyContractBatchRequest(
                        meta,
                        NullvsActionType.UPDATE,
                        CompanyContractData(
                            number = "0001",
                            isBillingLevel = contract.isBillingLevel,
                            codeClient = null,
                            dueDate = contract.dueDate,
                            id = contract.id,
                            groupCompany = contract.groupCompany!!,
                            isProRata = contract.isProRata!!,
                            startedAt = contract.startedAt!!,
                            nature = NatureCode.B2B_ALICE.value,
                        )
                    )
                )

                coVerifyNone {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        any(),
                    )
                }
            }

            @Test
            fun `#should create a NullvsCompanyContractBatchRequest finding the client code`() = runBlocking {
                val billingAccountablePartyId = RangeUUID.generate()
                val record = TestModelFactory.buildNullvsIntegrationRecord(
                    internalId = billingAccountablePartyId,
                    externalId = "111000"
                )

                val contract = TestModelFactory.buildCompanyContract(
                    externalId = "0001",
                    groupCompany = "0001",
                    billingAccountablePartyId = billingAccountablePartyId,
                    isProRata = true,
                    isBillingLevel = true,
                    nature = NatureCode.B2B_ALICE.value,
                    startedAt = LocalDate.now()
                )

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        billingAccountablePartyId,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                } returns record

                val result = service.updateContract(meta, contract)

                ResultAssert.assertThat(result).isSuccessWithData(
                    NullvsCompanyContractBatchRequest(
                        meta,
                        NullvsActionType.UPDATE,
                        CompanyContractData(
                            number = "0001",
                            isBillingLevel = contract.isBillingLevel,
                            codeClient = "111000",
                            dueDate = contract.dueDate,
                            id = contract.id,
                            groupCompany = contract.groupCompany!!,
                            isProRata = contract.isProRata!!,
                            startedAt = contract.startedAt!!,
                            nature = NatureCode.B2B_ALICE.value,
                        )
                    )
                )

                coVerifyOnce {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        billingAccountablePartyId,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                }
            }

            @Test
            fun `#should create a NullvsCompanyContractBatchRequest to create on wrong update event with null external id`() =
                runBlocking {
                    val contractFileIds = listOf(
                        ContractFile(
                            id = RangeUUID.generate(),
                            type = ContractType.MAIN,
                        )
                    )
                    val billingAccountablePartyId = RangeUUID.generate()
                    val record = TestModelFactory.buildNullvsIntegrationRecord(
                        internalId = billingAccountablePartyId,
                        externalId = "111000"
                    )

                    val contract = TestModelFactory.buildCompanyContract(
                        externalId = null,
                        groupCompany = "0001",
                        billingAccountablePartyId = billingAccountablePartyId,
                        isProRata = true,
                        isBillingLevel = true,
                        nature = NatureCode.B2B_ALICE.value,
                        startedAt = LocalDate.now(),
                        contractFileIds = contractFileIds,
                    )

                    coEvery {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountablePartyId,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    } returns record

                    val result = service.updateContract(meta, contract)

                    ResultAssert.assertThat(result).isSuccessWithData(
                        NullvsCompanyContractBatchRequest(
                            meta,
                            NullvsActionType.CREATE,
                            CompanyContractData(
                                number = null,
                                isBillingLevel = contract.isBillingLevel,
                                codeClient = "111000",
                                dueDate = contract.dueDate,
                                id = contract.id,
                                groupCompany = contract.groupCompany!!,
                                isProRata = contract.isProRata!!,
                                startedAt = contract.startedAt!!,
                                nature = NatureCode.B2B_ALICE.value,
                            )
                        )
                    )

                    coVerifyOnce {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountablePartyId,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    }
                }

            @Test
            fun `#should not create a NullvsCompanyContractBatchRequest when the external id and the contract file ids is empty`() =
                runBlocking {
                    val billingAccountablePartyId = RangeUUID.generate()

                    val contract = TestModelFactory.buildCompanyContract(
                        externalId = null,
                        groupCompany = "0001",
                        billingAccountablePartyId = billingAccountablePartyId,
                        isProRata = true,
                        isBillingLevel = true,
                        nature = NatureCode.B2B_ALICE.value,
                        startedAt = LocalDate.now()
                    )

                    val result = service.updateContract(meta, contract)

                    ResultAssert.assertThat(result).isFailureOfType(ContractFileDoesNotExist::class)
                }

            @Test
            fun `#should not create a NullvsCompanyContractBatchRequest when the contract billing level is true but it does not have a billing accountable party`() =
                runBlocking {
                    val contract = TestModelFactory.buildCompanyContract(
                        externalId = "0001",
                        groupCompany = "0001",
                        isProRata = true,
                        isBillingLevel = true,
                        nature = NatureCode.B2B_ALICE.value
                    )

                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.updateContract(meta, contract)

                        ResultAssert.assertThat(result)
                            .isFailureOfType(BillingAccountablePartyIdRequiredException::class)
                    }
                }

            @Test
            fun `#should not create a NullvsCompanyContractBatchRequest when the contract billing level is true but it does not have a nature code`() =
                runBlocking {
                    val billingAccountablePartyId = RangeUUID.generate()
                    val contract = TestModelFactory.buildCompanyContract(
                        externalId = "0001",
                        groupCompany = "0001",
                        isProRata = true,
                        isBillingLevel = true,
                        billingAccountablePartyId = billingAccountablePartyId,
                        nature = null,
                    )

                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.updateContract(meta, contract)

                        ResultAssert.assertThat(result).isFailureOfType(NatureRequiredException::class)
                    }
                }

            @Test
            fun `#should not create a NullvsCompanyContractBatchRequest when the external code client is not found`() =
                runBlocking {
                    val billingAccountablePartyId = RangeUUID.generate()
                    val contract = TestModelFactory.buildCompanyContract(
                        externalId = "0001",
                        groupCompany = "0001",
                        isProRata = true,
                        isBillingLevel = true,
                        billingAccountablePartyId = billingAccountablePartyId,
                        nature = NatureCode.B2B_ALICE.value,
                    )

                    coEvery {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountablePartyId,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    } returns NotFoundException()

                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.updateContract(meta, contract)

                        ResultAssert.assertThat(result).isFailureOfType(DependsOnModelException::class)
                    }
                }
        }
    }

    @Nested
    inner class SubContract {
        @Nested
        inner class CreateSubContract {
            private val company = TestModelFactory.buildCompany()
            private val contract = TestModelFactory.buildCompanyContract(
                externalId = "0001",
                groupCompany = "0001",
                isProRata = true,
                startedAt = LocalDate.now(),
            )
            private val subContract = TestModelFactory.buildCompanySubContract(
                externalId = null,
                isProRata = true,
                hasRetroactiveCharge = true,
                contractId = contract.id,
                companyId = company.id,
                createdAt = LocalDateTime.of(2023, 5, 1, 0, 0, 0, 0),
            )
            private val companyProductPriceListing = TestModelFactory.buildCompanyProductPriceListing(
                companySubContractId = subContract.id,
                companyId = company.id,
            )

            @Test
            fun `#should create a NullvsCompanySubContractBatchRequest without client when it is not a billing level`() =
                runBlocking {
                    val companyProductPriceListing = companyProductPriceListing.copy(
                        product = TestModelFactory.buildProduct(ansNumber = "231000"),
                        priceListing = TestModelFactory.buildPriceListing()
                    )

                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(withProduct = true, withPriceListing = true)
                        )
                    } returns listOf(companyProductPriceListing)

                    val result = service.createSubContract(meta, subContract)

                    ResultAssert.assertThat(result).isSuccessWithData(
                        NullvsCompanySubContractBatchRequest(
                            meta,
                            NullvsActionType.CREATE,
                            CompanySubContractData(
                                isBillingLevel = subContract.isBillingLevel,
                                codeClient = null,
                                dueDate = subContract.dueDate,
                                id = subContract.id,
                                groupCompany = contract.groupCompany!!,
                                billingGroup = "0004",
                                hasProRata = contract.isProRata!!,
                                hasRetroactiveCharge = subContract.hasRetroactiveCharge,
                                startedAt = contract.startedAt!!,
                                cnpj = company.cnpj,
                                contractId = contract.id,
                                contractNumber = contract.externalId!!,
                                description = company.legalName,
                                shortDescription = company.name,
                                readjustmentMonth = contract.startedAt!!.month,
                                nature = NatureCode.B2B_ALICE.value,
                                availableCompanyProductPriceListing = listOf(companyProductPriceListing.toCompanyProductPriceListingData())
                            )
                        )
                    )

                    coVerifyNone {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            any(),
                            any(),
                        )
                    }

                    coVerifyOnce {
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                    }
                }

            @Test
            fun `#should create a NullvsCompanySubContractBatchRequest with client when it is a billing level`() =
                runBlocking {
                    val billingAccountablePartyId = RangeUUID.generate()
                    val subContract = subContract.copy(
                        isBillingLevel = true,
                        billingAccountablePartyId = billingAccountablePartyId,
                        nature = NatureCode.B2B_ALICE.value,
                        billingGroup = "0004",
                    )

                    val companyProductPriceListing = companyProductPriceListing.copy(
                        product = TestModelFactory.buildProduct(ansNumber = "231000"),
                        priceListing = TestModelFactory.buildPriceListing()
                    )

                    val record = TestModelFactory.buildNullvsIntegrationRecord(
                        internalId = billingAccountablePartyId,
                        externalId = "111000",
                    )

                    coEvery {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountablePartyId,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    } returns record
                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)

                    val result = service.createSubContract(meta, subContract)

                    ResultAssert.assertThat(result).isSuccessWithData(
                        NullvsCompanySubContractBatchRequest(
                            meta,
                            NullvsActionType.CREATE,
                            CompanySubContractData(
                                isBillingLevel = subContract.isBillingLevel,
                                codeClient = "111000",
                                dueDate = subContract.dueDate,
                                id = subContract.id,
                                groupCompany = contract.groupCompany!!,
                                billingGroup = "0004",
                                hasProRata = contract.isProRata!!,
                                hasRetroactiveCharge = subContract.hasRetroactiveCharge,
                                startedAt = contract.startedAt!!,
                                cnpj = company.cnpj,
                                contractId = contract.id,
                                contractNumber = contract.externalId!!,
                                description = company.legalName,
                                shortDescription = company.name,
                                readjustmentMonth = contract.startedAt!!.month,
                                nature = NatureCode.B2B_ALICE.value,
                                availableCompanyProductPriceListing = listOf(companyProductPriceListing.toCompanyProductPriceListingData())
                            )
                        )
                    )

                    coVerifyOnce {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountablePartyId,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountablePartyId,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    }

                    coVerifyNone {
                        subcontractService.update(any(), false)
                        nullvsIntegrationLogService.checkToCreateErrorLog(any())
                    }
                }

            @Test
            fun `#should not create a NullvsCompanySubContractBatchRequest when the contract billing level is true but it does not have a billing accountable party`() =
                runBlocking {
                    val subContract = subContract.copy(
                        isProRata = true,
                        isBillingLevel = true,
                    )

                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)

                    val failedSubContract =
                        subContract.copy(integrationStatus = CompanySubContractIntegrationStatus.FAILED)

                    coEvery { subcontractService.get(subContract.id) } returns subContract
                    coEvery { subcontractService.update(failedSubContract, false) } returns failedSubContract
                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.createSubContract(meta, subContract)

                        ResultAssert.assertThat(result)
                            .isFailureOfType(BillingAccountablePartyIdRequiredException::class)
                    }

                    coVerifyOnce {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        subcontractService.get(subContract.id)
                        subcontractService.update(failedSubContract, false)
                        nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId })
                    }

                    coVerifyNone {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            any(),
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    }
                }

            @Test
            fun `#should not create a NullvsCompanySubContractBatchRequest when the contract billing level is true but it does not have a nature code`() =
                runBlocking {
                    val billingAccountablePartyId = RangeUUID.generate()
                    val subContract = subContract.copy(
                        isProRata = true,
                        isBillingLevel = true,
                        billingAccountablePartyId = billingAccountablePartyId,
                        billingGroup = "0004",
                        nature = null,
                    )

                    val failedSubContract =
                        subContract.copy(integrationStatus = CompanySubContractIntegrationStatus.FAILED)

                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery { subcontractService.get(subContract.id) } returns subContract
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)
                    coEvery { subcontractService.update(failedSubContract, false) } returns failedSubContract
                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.createSubContract(meta, subContract)

                        ResultAssert.assertThat(result).isFailureOfType(NatureRequiredException::class)
                    }

                    coVerifyOnce {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        subcontractService.get(subContract.id)
                        subcontractService.update(failedSubContract, false)
                        nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId })
                    }

                    coVerifyNone {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            any(),
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    }
                }

            @Test
            fun `#should not create a NullvsCompanySubContractBatchRequest when the contract billing level is true but it does not have a billing group`() =
                runBlocking {
                    val billingAccountablePartyId = RangeUUID.generate()
                    val subContract = subContract.copy(
                        isProRata = true,
                        isBillingLevel = true,
                        billingAccountablePartyId = billingAccountablePartyId,
                        billingGroup = null,
                    )

                    val failedSubContract =
                        subContract.copy(integrationStatus = CompanySubContractIntegrationStatus.FAILED)

                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery { subcontractService.get(subContract.id) } returns subContract
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)
                    coEvery { subcontractService.update(failedSubContract, false) } returns failedSubContract
                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.createSubContract(meta, subContract)

                        ResultAssert.assertThat(result).isFailureOfType(BillingGroupRequiredException::class)
                    }

                    coVerifyOnce {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        subcontractService.get(subContract.id)
                        subcontractService.update(failedSubContract, false)
                        nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId })
                    }

                    coVerifyNone {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            any(),
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    }
                }

            @Test
            fun `#should not create a NullvsCompanySubContractBatchRequest when the external code client is not found`() =
                runBlocking {
                    val billingAccountablePartyId = RangeUUID.generate()
                    val subContract = subContract.copy(
                        isProRata = true,
                        isBillingLevel = true,
                        billingAccountablePartyId = billingAccountablePartyId,
                        billingGroup = "0004",
                    )

                    val failedSubContract =
                        subContract.copy(integrationStatus = CompanySubContractIntegrationStatus.FAILED)

                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery { subcontractService.get(subContract.id) } returns subContract
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)

                    coEvery {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountablePartyId,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    } returns NotFoundException()

                    coEvery { subcontractService.update(failedSubContract, false) } returns failedSubContract
                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.createSubContract(meta, subContract)

                        ResultAssert.assertThat(result).isFailureOfType(DependsOnModelException::class)
                    }

                    coVerifyOnce {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        subcontractService.get(subContract.id)
                        subcontractService.update(failedSubContract, false)
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountablePartyId,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                        nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId })
                    }
                }

            @Test
            fun `#should not create a NullvsCompanySubContractBatchRequest when the external code client is not found and with use_dependency_service FF is disabled`() =
                runBlocking {
                    val billingAccountablePartyId = RangeUUID.generate()
                    val subContract = subContract.copy(
                        isProRata = true,
                        isBillingLevel = true,
                        billingAccountablePartyId = billingAccountablePartyId,
                        billingGroup = "0004",
                    )

                    val failedSubContract =
                        subContract.copy(integrationStatus = CompanySubContractIntegrationStatus.FAILED)

                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery { subcontractService.get(subContract.id) } returns subContract
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)

                    coEvery {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountablePartyId,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    } returns NotFoundException()

                    coEvery { subcontractService.update(failedSubContract, false) } returns failedSubContract
                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()

                    withFeatureFlags(
                        FeatureNamespace.NULLVS to mapOf("is_check_error_validation_enabled" to true),
                        FeatureNamespace.NULLVS to mapOf("use_dependency_service" to false),
                    ) {
                        val result = service.createSubContract(meta, subContract)

                        ResultAssert.assertThat(result).isFailureOfType(ExternalClientNotFound::class)
                    }

                    coVerifyOnce {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        subcontractService.get(subContract.id)
                        subcontractService.update(failedSubContract, false)
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountablePartyId,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                        nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId })
                    }
                }

            @Test
            fun `#should create an integration log when the contract external id is not found`() =
                runBlocking {
                    val contract = contract.copy(
                        externalId = null,
                        groupCompany = "0001",
                        isProRata = true,
                    )

                    val failedSubContract =
                        subContract.copy(integrationStatus = CompanySubContractIntegrationStatus.FAILED)

                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery { subcontractService.get(subContract.id) } returns subContract
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)
                    coEvery { subcontractService.update(failedSubContract, false) } returns failedSubContract
                    coEvery { nullvsIntegrationLogService.add(match { it.eventId == meta.eventId && it.internalId == meta.internalId }) } returns log

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", false) {
                        val result = service.createSubContract(meta, subContract)

                        ResultAssert.assertThat(result).isFailureOfType(DependsOnModelException::class)
                    }


                    coVerifyOnce {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        subcontractService.get(subContract.id)
                        subcontractService.update(failedSubContract, false)
                        nullvsIntegrationLogService.add(match { it.eventId == meta.eventId && it.internalId == meta.internalId })
                    }
                    coVerifyNone { nullvsIntegrationLogService.checkToCreateErrorLog(any()) }
                }

            @Test
            fun `#should create an integration log when the contract external id is not found with use_dependency_service FF is disabled`() =
                runBlocking {
                    val contract = contract.copy(
                        externalId = null,
                        groupCompany = "0001",
                        isProRata = true,
                    )

                    val failedSubContract =
                        subContract.copy(integrationStatus = CompanySubContractIntegrationStatus.FAILED)

                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery { subcontractService.get(subContract.id) } returns subContract
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)
                    coEvery { subcontractService.update(failedSubContract, false) } returns failedSubContract
                    coEvery { nullvsIntegrationLogService.add(match { it.eventId == meta.eventId && it.internalId == meta.internalId }) } returns log

                    withFeatureFlags(
                        FeatureNamespace.NULLVS to mapOf("is_check_error_validation_enabled" to false),
                        FeatureNamespace.NULLVS to mapOf("use_dependency_service" to false),
                    ) {
                        val result = service.createSubContract(meta, subContract)

                        ResultAssert.assertThat(result).isFailureOfType(ExternalCompanyContractNotFound::class)
                    }

                    coVerifyOnce {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        subcontractService.get(subContract.id)
                        subcontractService.update(failedSubContract, false)
                        nullvsIntegrationLogService.add(match { it.eventId == meta.eventId && it.internalId == meta.internalId })
                    }
                    coVerifyNone { nullvsIntegrationLogService.checkToCreateErrorLog(any()) }
                }

        }

        @Nested
        inner class UpdateSubContract {
            private val company = TestModelFactory.buildCompany()
            private val contract = TestModelFactory.buildCompanyContract(
                externalId = "0001",
                groupCompany = "0001",
                isProRata = true,
                startedAt = LocalDate.now(),
            )
            private val subContract = TestModelFactory.buildCompanySubContract(
                externalId = null,
                isProRata = true,
                hasRetroactiveCharge = true,
                contractId = contract.id,
                companyId = company.id,
                createdAt = LocalDateTime.of(2023, 5, 1, 0, 0, 0, 0),
            )
            private val companyProductPriceListing = TestModelFactory.buildCompanyProductPriceListing(
                companySubContractId = subContract.id,
                companyId = company.id,
            )

            @Test
            fun `#should create a NullvsCompanySubContractBatchRequest`() = runBlocking {
                val contract = contract.copy(
                    externalId = "0001",
                    groupCompany = "0001",
                    isProRata = true,
                    startedAt = LocalDate.now(),
                )

                val subContract = subContract.copy(
                    externalId = "00001",
                    isProRata = true,
                    hasRetroactiveCharge = true,
                    contractId = contract.id,
                    companyId = company.id,
                    createdAt = LocalDateTime.of(2023, 5, 1, 0, 0, 0, 0),
                )

                val companyProductPriceListing = companyProductPriceListing.copy(
                    product = TestModelFactory.buildProduct(ansNumber = "231000"),
                    priceListing = TestModelFactory.buildPriceListing()
                )

                coEvery { contractService.get(subContract.contractId) } returns contract
                coEvery { companyService.get(subContract.companyId) } returns company
                coEvery {
                    companyProductPriceListingService.findCurrentBySubContractId(
                        subContract.id,
                        CompanyProductPriceListingService.FindOptions(withProduct = true, withPriceListing = true)
                    )
                } returns listOf(companyProductPriceListing)

                val result = service.updateSubContract(meta, subContract)

                ResultAssert.assertThat(result).isSuccessWithData(
                    NullvsCompanySubContractBatchRequest(
                        meta,
                        NullvsActionType.UPDATE,
                        CompanySubContractData(
                            number = "00001",
                            isBillingLevel = contract.isBillingLevel,
                            codeClient = null,
                            dueDate = contract.dueDate,
                            id = subContract.id,
                            groupCompany = contract.groupCompany!!,
                            billingGroup = "0004",
                            nature = NatureCode.B2B_ALICE.value,
                            hasProRata = contract.isProRata!!,
                            hasRetroactiveCharge = subContract.hasRetroactiveCharge,
                            startedAt = contract.startedAt!!,
                            cnpj = company.cnpj,
                            contractId = contract.id,
                            contractNumber = contract.externalId!!,
                            description = company.legalName,
                            shortDescription = company.name,
                            readjustmentMonth = contract.startedAt!!.month,
                            availableCompanyProductPriceListing = listOf(companyProductPriceListing.toCompanyProductPriceListingData())
                        )
                    )
                )

                coVerifyNone {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        any(),
                    )
                }

                coVerifyOnce {
                    contractService.get(subContract.contractId)
                    companyService.get(subContract.companyId)
                    companyProductPriceListingService.findCurrentBySubContractId(
                        subContract.id,
                        CompanyProductPriceListingService.FindOptions(withProduct = true, withPriceListing = true)
                    )
                }
            }

            @Test
            fun `#should create a NullvsCompanySubContractBatchRequest to create on wrong update event with null external id`() =
                runBlocking {
                    val contract = contract.copy(
                        externalId = null,
                        groupCompany = "0001",
                        isProRata = true,
                        startedAt = LocalDate.now(),
                    )
                    val subContract = subContract.copy(
                        externalId = null,
                        isProRata = true,
                        hasRetroactiveCharge = true,
                        contractId = contract.id,
                        companyId = company.id,
                        createdAt = LocalDateTime.of(2023, 5, 1, 0, 0, 0, 0)
                    )
                    val companyProductPriceListing = companyProductPriceListing.copy(
                        product = TestModelFactory.buildProduct(ansNumber = "231000"),
                        priceListing = TestModelFactory.buildPriceListing()
                    )

                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)

                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(any()) } returns true.success()

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.updateSubContract(meta, subContract)

                        ResultAssert.assertThat(result).isFailureOfType(
                            DependsOnModelException::class
                        )
                    }

                    coVerifyOnce {
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(withProduct = true, withPriceListing = true)
                        )
                    }

                    coVerifyNone {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            any(),
                            any(),
                        )
                    }
                }

            @Test
            fun `#should create a NullvsCompanySubContractBatchRequest to create on wrong update event with null contract start at`() =
                runBlocking {
                    val company = TestModelFactory.buildCompany()
                    val contract = contract.copy(
                        startedAt = null,
                    )

                    val companyProductPriceListing = companyProductPriceListing.copy(
                        product = TestModelFactory.buildProduct(ansNumber = "231000"),
                        priceListing = TestModelFactory.buildPriceListing()
                    )

                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)

                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(any()) } returns true.success()

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.updateSubContract(meta, subContract)

                        ResultAssert.assertThat(result).isFailureOfType(
                            ContractDoesNotHaveStartAtDate::class
                        )
                    }

                    coVerifyOnce {
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(withProduct = true, withPriceListing = true)
                        )
                    }

                    coVerifyNone {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            any(),
                            any(),
                        )
                    }
                }

            @Test
            fun `#should create a NullvsCompanySubContractBatchRequest finding the client code`() =
                runBlocking {
                    val billingAccountablePartyId = RangeUUID.generate()
                    val contract = contract.copy(
                        startedAt = LocalDate.now(),
                    )
                    val subContract = subContract.copy(
                        externalId = "00001",
                        isBillingLevel = true,
                        billingAccountablePartyId = billingAccountablePartyId,
                        billingGroup = "0004",
                    )
                    val record = TestModelFactory.buildNullvsIntegrationRecord(
                        internalId = billingAccountablePartyId,
                        externalId = "111000",
                    )
                    val companyProductPriceListing = TestModelFactory.buildCompanyProductPriceListing(
                        companySubContractId = subContract.id,
                        companyId = company.id,
                    ).copy(
                        product = TestModelFactory.buildProduct(ansNumber = "231000"),
                        priceListing = TestModelFactory.buildPriceListing()
                    )

                    coEvery {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountablePartyId,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    } returns record
                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)

                    val result = service.updateSubContract(meta, subContract)

                    ResultAssert.assertThat(result).isSuccessWithData(
                        NullvsCompanySubContractBatchRequest(
                            meta,
                            NullvsActionType.UPDATE,
                            CompanySubContractData(
                                number = "00001",
                                isBillingLevel = subContract.isBillingLevel,
                                codeClient = "111000",
                                dueDate = subContract.dueDate,
                                id = subContract.id,
                                groupCompany = contract.groupCompany!!,
                                billingGroup = "0004",
                                nature = NatureCode.B2B_ALICE.value,
                                hasProRata = contract.isProRata!!,
                                hasRetroactiveCharge = subContract.hasRetroactiveCharge,
                                startedAt = contract.startedAt!!,
                                cnpj = company.cnpj,
                                contractId = contract.id,
                                contractNumber = contract.externalId!!,
                                description = company.legalName,
                                shortDescription = company.name,
                                readjustmentMonth = contract.startedAt!!.month,
                                availableCompanyProductPriceListing = listOf(companyProductPriceListing.toCompanyProductPriceListingData())
                            )
                        )
                    )

                    coVerifyOnce {
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(withProduct = true, withPriceListing = true)
                        )
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountablePartyId,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    }
                }

            @Test
            fun `#should not create a NullvsCompanySubContractBatchRequest when the contract billing level is true but it does not have a billing accountable party`() =
                runBlocking {
                    val subContract = TestModelFactory.buildCompanySubContract(
                        isProRata = true,
                        isBillingLevel = true,
                    )

                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)
                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.updateSubContract(meta, subContract)

                        ResultAssert.assertThat(result)
                            .isFailureOfType(BillingAccountablePartyIdRequiredException::class)
                    }

                    coVerifyOnce {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId })
                    }

                    coVerifyNone {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            any(),
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    }
                }

            @Test
            fun `#should not create a NullvsCompanySubContractBatchRequest when the contract billing level is true but it does not have a nature code`() =
                runBlocking {
                    val billingAccountablePartyId = RangeUUID.generate()
                    val subContract = TestModelFactory.buildCompanySubContract(
                        isProRata = true,
                        isBillingLevel = true,
                        billingAccountablePartyId = billingAccountablePartyId,
                        billingGroup = "0004",
                        nature = null,
                    )

                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)

                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.updateSubContract(meta, subContract)

                        ResultAssert.assertThat(result).isFailureOfType(NatureRequiredException::class)
                    }

                    coVerifyOnce {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId })
                    }

                    coVerifyNone {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            any(),
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    }
                }

            @Test
            fun `#should not create a NullvsCompanySubContractBatchRequest when the contract billing level is true but it does not have a billing group`() =
                runBlocking {
                    val billingAccountablePartyId = RangeUUID.generate()
                    val subContract = TestModelFactory.buildCompanySubContract(
                        isProRata = true,
                        isBillingLevel = true,
                        billingAccountablePartyId = billingAccountablePartyId,
                        nature = NatureCode.B2B_ALICE.value,
                        billingGroup = null,
                    )

                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)
                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.updateSubContract(meta, subContract)

                        ResultAssert.assertThat(result).isFailureOfType(BillingGroupRequiredException::class)
                    }

                    coVerifyOnce {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId })
                    }

                    coVerifyNone {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            any(),
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    }
                }

            @Test
            fun `#should not create a NullvsCompanySubContractBatchRequest when the external code client is not found`() =
                runBlocking {
                    val billingAccountablePartyId = RangeUUID.generate()

                    val subContract = subContract.copy(
                        isBillingLevel = true,
                        billingAccountablePartyId = billingAccountablePartyId,
                        billingGroup = "0001",
                        nature = NatureCode.B2B_ALICE.value,
                    )

                    coEvery {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountablePartyId,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    } returns NotFoundException()

                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)
                    coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()


                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                        val result = service.updateSubContract(meta, subContract)

                        ResultAssert.assertThat(result).isFailureOfType(DependsOnModelException::class)
                    }

                    coVerifyOnce {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId })
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountablePartyId,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    }
                }

            @Test
            fun `#should create an integration log when the contract external id is not found`() =
                runBlocking {
                    val failedSubContract =
                        subContract.copy(integrationStatus = CompanySubContractIntegrationStatus.FAILED)

                    coEvery { contractService.get(subContract.contractId) } returns contract
                    coEvery { companyService.get(subContract.companyId) } returns company
                    coEvery {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                    } returns listOf(companyProductPriceListing)
                    coEvery { nullvsIntegrationLogService.add(match { it.eventId == meta.eventId && it.internalId == meta.internalId }) } returns log

                    withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", false) {
                        service.updateSubContract(meta, subContract)
                    }

                    coVerifyOnce {
                        companyProductPriceListingService.findCurrentBySubContractId(
                            subContract.id,
                            CompanyProductPriceListingService.FindOptions(
                                withProduct = true,
                                withPriceListing = true
                            )
                        )
                        contractService.get(subContract.contractId)
                        companyService.get(subContract.companyId)
                        nullvsIntegrationLogService.add(match { it.eventId == meta.eventId && it.internalId == meta.internalId })
                    }
                    coVerifyNone { nullvsIntegrationLogService.checkToCreateErrorLog(any()) }
                }
        }
    }
}
