package br.com.alice.api.backoffice.controllers.execIndicator

import br.com.alice.api.backoffice.mappers.execIndicator.HealthSpecialistResourceBundleInputMapper
import br.com.alice.api.backoffice.mappers.execIndicator.HealthSpecialistResourceBundleOutputMapper
import br.com.alice.api.backoffice.transfers.execIndicator.HealthSpecialistResourceBundleCreateRequest
import br.com.alice.api.backoffice.transfers.execIndicator.HealthSpecialistResourceBundleUpdateRequest
import br.com.alice.common.ErrorResponse
import br.com.alice.common.Response
import br.com.alice.common.coFoldResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.mappers.CommonInputMapper
import br.com.alice.data.layer.models.HealthSpecialistResourceBundle
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleManagementService
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleService
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePatchRequest
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistRequestFilters
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import io.ktor.http.Parameters
import kotlinx.coroutines.coroutineScope
import java.util.UUID
import kotlin.reflect.KClass

class HealthSpecialistResourceBundleController(
    private val healthSpecialistResourceBundleService: HealthSpecialistResourceBundleService,
    private val healthcareResourceService: HealthcareResourceService,
    private val healthSpecialistResourceBundleManagementService: HealthSpecialistResourceBundleManagementService,
): Controller() {
    private object ErrorsCode {
        const val DUPLICATED_ITEM = "health_specialist_resource_bundle_duplicated"
        const val NOT_FOUND_KEY = "health_specialist_resource_bundle_not_found"
    }

    private val mapFailures: Array<Pair<KClass<*>, suspend (Throwable) -> ErrorResponse>> = arrayOf(
        DuplicatedItemException::class to {
            ErrorResponse(
                ErrorsCode.DUPLICATED_ITEM,
                i18n(ErrorsCode.DUPLICATED_ITEM)
            )
        },
        NotFoundException::class to { ErrorResponse(ErrorsCode.NOT_FOUND_KEY, i18n(ErrorsCode.NOT_FOUND_KEY)) },
    )

    suspend fun upsert(request: HealthSpecialistResourceBundleCreateRequest): Response {
        val model = HealthSpecialistResourceBundle(
            primaryTuss = request.primaryTuss,
            secondaryResources = request.secondaryResources?.map { it.id } ?: emptyList(),
            executionAmount = request.executionAmount,
            executionEnvironment = request.executionEnvironment,
            description = request.aliceDescription,
            code = request.aliceCode ?: HealthSpecialistResourceBundle.generateRandomCode(),
            status = request.status,
            serviceType = request.serviceType
        )

        return healthSpecialistResourceBundleService.upsert(model)
            .coFoldResponse(
                { HealthSpecialistResourceBundleOutputMapper.toResponse(it) },
                *mapFailures
            )
    }

    suspend fun index(queryParams: Parameters): Response = coroutineScope {
        val range = CommonInputMapper.getPaginationParams(queryParams)
        val query = CommonInputMapper.getFilterParams<String>(queryParams, "q")

        healthSpecialistResourceBundleManagementService.list(query, range).map { paginatedResponse ->
            HealthSpecialistResourceBundleOutputMapper.toPaginatedResponse(
                paginatedResponse.items,
                paginatedResponse.total,
                queryParams
            )
        }.coFoldResponse({ it }, *mapFailures)
    }

    suspend fun update(
        healthSpecialistResourceBundleId: UUID,
        request: HealthSpecialistResourceBundleUpdateRequest
    ): Response =
        healthSpecialistResourceBundleService.get(healthSpecialistResourceBundleId)
            .flatMap {
                healthSpecialistResourceBundleService.update(
                    HealthSpecialistResourceBundleInputMapper.toUpdate(
                        it,
                        request
                    )
                )
            }
            .coFoldResponse(
                { HealthSpecialistResourceBundleOutputMapper.toResponse(it) },
                *mapFailures
            )

    suspend fun patch(
        healthSpecialistResourceBundleId: UUID,
        request: HealthSpecialistResourceBundlePatchRequest
    ): Response =
        healthSpecialistResourceBundleManagementService.update(healthSpecialistResourceBundleId, request)
            .coFoldResponse(
                { it },
                *mapFailures
    )

    suspend fun getMedicalSpecialtiesRelatedToResourceBundle(
        healthSpecialistResourceBundleId: UUID,
        queryParams: Parameters,
    ): Response {
        val range = CommonInputMapper.getPaginationParams(queryParams)

        return healthSpecialistResourceBundleManagementService.getMedicalSpecialtiesRelatedToResourceBundle(
            healthSpecialistResourceBundleId,
            range,
        ).map {
            HealthSpecialistResourceBundleOutputMapper.toAssociatedResourceBundleSpecialtiesPaginatedResponse(
                it.specialties,
                it.count,
                queryParams
            )
        }.coFoldResponse(
            { it },
            *mapFailures
        )
    }

    suspend fun getPendingResourceBundleSpecialtiesForPricing(): Response =
        healthSpecialistResourceBundleManagementService.getPendingResourceSpecialtyBundlesForPricing()
            .map { it.map { it.id } }
            .coFoldResponse(
                { it },
                *mapFailures
            )

    suspend fun get(healthSpecialistResourceBundleId: UUID): Response =
        healthSpecialistResourceBundleService.get(healthSpecialistResourceBundleId).flatMapPair {
            healthcareResourceService.getByIds(it.secondaryResources)
        }.coFoldResponse(
            { HealthSpecialistResourceBundleOutputMapper.toResponse(it.second, it.first) },
            *mapFailures
        )

    suspend fun getResourceBundleSpecialtyPricingList(parameters: Parameters): Response {
        val range = CommonInputMapper.getPaginationParams(parameters)
        val query = CommonInputMapper.getFilterParams<String>(parameters, "q")
        val medicalSpecialtyIds = CommonInputMapper.getFilterParams<List<UUID>>(parameters, "medicalSpecialtyIds")
        val pricingStatus = CommonInputMapper.getFilterParams<String>(parameters, "pricingStatus")
            ?.let { PricingStatus.valueOf(it) }
        val serviceTypes = CommonInputMapper.getFilterParams<List<String>>(parameters, "serviceTypes")
            ?.map { HealthSpecialistResourceBundleServiceType.valueOf(it) }


        return healthSpecialistResourceBundleManagementService.getResourceBundleSpecialtyPricingList(
            PricingForHealthSpecialistRequestFilters(
                query = query,
                medicalSpecialtyIds = medicalSpecialtyIds,
                status = pricingStatus,
                serviceTypes = serviceTypes
            ),
            range
        ).coFoldResponse(
            {
                HealthSpecialistResourceBundleOutputMapper.toPricingForHealthSpecialistResourceBundlePaginatedResponse(
                    it,
                    parameters
                )
            },
            *mapFailures
        )
    }
}
