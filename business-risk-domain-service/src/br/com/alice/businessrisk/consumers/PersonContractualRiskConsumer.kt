package br.com.alice.businessrisk.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.businessrisk.clients.MacoService
import br.com.alice.businessrisk.events.PersonContractualRiskCreatedEvent
import br.com.alice.businessrisk.exceptions.MacoIncompleteDataException
import br.com.alice.businessrisk.exceptions.StandardCostNotFoundForProductException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coFoldError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class PersonContractualRiskConsumer(
    private val companyService: CompanyService,
    private val beneficiaryService: BeneficiaryService,
    private val macoService: MacoService
) : AutoRetryableConsumer(NotFoundException::class) {
    suspend fun handleCalculateMaco(event: PersonContractualRiskCreatedEvent) =
        withSubscribersEnvironment {
            val personId = event.payload.personContractualRisk.personId

            if (!shouldCalculateMaco()) {
                logger.info(
                    "PersonContractualRiskConsumer::handleCalculateMaco - skip the maco calculation by FF.should_calculate_maco",
                    "person_id" to personId,
                )

                return@withSubscribersEnvironment false.success()
            }

            logger.info(
                "PersonContractualRiskConsumer::handleCalculateMaco - calculate and save maco",
                "person_id" to personId,
            )

            beneficiaryService.findByPersonId(personId).flatMap { companyService.get(it.companyId) }
                .map { company ->
                    company.contractIds.pmap { contractId ->
                        logger.info(
                            "CompanyConsumer::calculateMaco - Start to calculate the maco by contract",
                            "contract_id" to contractId
                        )

                        macoService.calculate(contractId).flatMap {
                            logger.info("CompanyConsumer::calculateMaco - Save the calculated maco")
                            macoService.saveMacos(it)
                        }
                    }
                }.coFoldError(MacoIncompleteDataException::class to {
                    logger.error("Missing fields for maco calculation", it)
                    true.success()
                }, StandardCostNotFoundForProductException::class to {
                    logger.error("Standard cost not found for product", it)
                    true.success()
                })
        }

    private fun shouldCalculateMaco() =
        FeatureService.get(FeatureNamespace.BUSINESS_RISK, "should_calculate_maco", false)
}
