package br.com.alice.data.layer.helpers

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.Disease
import br.com.alice.common.MvUtil
import br.com.alice.common.RangeUUID
import br.com.alice.common.RangeUUID.PERSON_NON_PII_TOKEN_RANGE
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.Status
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.models.Gender
import br.com.alice.common.models.Sex
import br.com.alice.common.models.State
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.data.layer.converters.toTransport
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.models.Appointment.ReferenceLinkModel.HEALTH_PLAN_TASK
import br.com.alice.data.layer.models.Appointment.ReferenceLinkModel.SCHEDULE
import br.com.alice.data.layer.models.AppointmentScheduleStatus.SCHEDULED
import br.com.alice.data.layer.models.AppointmentScheduleType.NUTRITIONIST
import br.com.alice.data.layer.models.AppointmentType.ASSISTANCE_CARE
import br.com.alice.data.layer.models.MedicineUnit.CAPSULE
import br.com.alice.data.layer.services.PersonNonPiiToken
import br.com.alice.data.layer.services.PersonPiiToken
import br.com.alice.data.layer.tables.*
import org.apache.commons.lang3.RandomStringUtils
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

object TestTableFactory {

    internal fun buildMemberTable(
        personId: PersonPiiToken = PersonPiiToken(),
        parentPerson: PersonPiiToken? = null,
    ): MemberTable {
        val product = TestModelFactory.buildProduct()
        return MemberTable(
            personId = personId,
            contract = ContractModel("https://www.hhs.gov/sites/default/files/privacysummary.pdf"),
            activationDate = LocalDateTime.now(),
            status = MemberStatus.ACTIVE,
            statusHistory = listOf(
                MemberStatusHistoryEntryModel(MemberStatus.PENDING, LocalDateTime.now().minusDays(10).toString()),
                MemberStatusHistoryEntryModel(MemberStatus.ACTIVE, LocalDateTime.now().toString())
            ),
            selectedProduct = MemberProduct(product.id, listOf(), ProductType.B2C),
            parentPerson = parentPerson,
        )
    }

    internal fun buildPersonTable(
        id: PersonPiiToken = PersonPiiToken(),
        sex: Sex? = null,
        dateOfBirth: LocalDateTime? = null,
        nationalId: String = "609.048.950-68",
        email: String = "<EMAIL>",
        isTest: Boolean = false
    ) = PersonTable(
        firstName = "José",
        lastName = "da Silva",
        nationalId = nationalId,
        email = email,
        addresses = listOf(),
        nickName = null,
        dateOfBirth = dateOfBirth,
        sex = sex,
        gender = null,
        id = id,
        isTest = isTest
    )

    internal fun buildFinancialDataTable(
        id: UUID = RangeUUID.generate(),
        personId: PersonPiiToken = PersonPiiToken(),
        bankCode: String = "001",
        bankAgency: String = "0001",
        accountNumber: String = "000001",
        nationalId: String = "***********",
        accountNickname: String? = null,
        active: Boolean = false,
    ) = FinancialDataTable(
        id = id,
        personId = personId,
        bankCode = bankCode,
        bankAgency = bankAgency,
        accountNumber = accountNumber,
        nationalId = nationalId,
        accountNickname = accountNickname,
        active = active
    )

    internal fun buildRefundTable(
        id: UUID = RangeUUID.generate(),
        personId: PersonPiiToken
    ) = RefundTable(
        id = id,
        personId = personId,
        type = RefundRequestType.MEMBER_CHOICE,
        step = RefundStep.SELFIE_VERIFICATION,
        healthEventType = RefundHealthEventType.APPOINTMENT
    )

    internal fun buildRefundCounterReferralTable(
        id: UUID = RangeUUID.generate(),
        personId: PersonPiiToken,
        refundId: UUID = RangeUUID.generate()
    ) = RefundCounterReferralTable(
        id = id,
        personId = personId,
        refundId = refundId
    )

    internal fun buildFileVaultPiiTable(
        id: UUID = RangeUUID.generate(),
        personId: PersonPiiToken,
        domain: String = "refund",
        namespace: String = "alice_app",
        fileType: String = "png",
        url: String = "s3://url",
        originalFileName: String = "refund-file"
    ) = FileVaultPiiTable(
        id = id,
        personId = personId,
        domain = domain,
        namespace = namespace,
        fileType = fileType,
        url = url,
        originalFileName = originalFileName
    )

    fun buildHealthProfessionalTable() = StaffTable(
        email = "<EMAIL>",
        firstName = "José",
        lastName = "Pereira",
        gender = Gender.MALE,
        role = Role.MANAGER_PHYSICIAN,
        type = StaffType.PITAYA
    )

    fun buildHealthcareTeamPhysicianTable() = buildHealthProfessionalTable()

    fun buildHealthcareTeamNurseTable() =
        buildHealthProfessionalTable().copy(
            email = "<EMAIL>",
            role = Role.HEALTHCARE_TEAM_NURSE
        )

    fun buildHealthCommunitySpecialist(specialtyId: UUID = RangeUUID.generate()) =
        HealthCommunitySpecialistTable(
            id = RangeUUID.generate(),
            name = "Dino da Silva Sauro",
            specialtyId = specialtyId,
            council = CouncilModel("1234", State.SP),
            phones = emptyList(),
            qualifications = emptyList(),
            imageUrl = null,
            staffId = RangeUUID.generate(),
            email = "<EMAIL>",
        )

    fun buildDigitalCareNurseTable() =
        buildHealthProfessionalTable().copy(
            email = "<EMAIL>",
            role = Role.DIGITAL_CARE_NURSE
        )

    fun buildNutritionistTable() =
        buildHealthProfessionalTable().copy(
            email = "<EMAIL>",
            role = Role.NUTRITIONIST
        )

    fun buildProdTechStaffTable() =
        buildHealthProfessionalTable().copy(
            email = "<EMAIL>",
            role = Role.PRODUCT_TECH
        )

    fun buildProdTechHealthStaffTable() =
        buildHealthProfessionalTable().copy(
            email = "<EMAIL>",
            role = Role.PRODUCT_TECH_HEALTH
        )

    internal fun buildClinicalBackground(
        personId: PersonNonPiiToken,
        addedByStaffId: UUID,
        appointmentId: UUID? = null,
    ) =
        ClinicalBackgroundTable(
            id = RangeUUID.generate(),
            personId = personId,
            type = ClinicalBackgroundType.DISEASE,
            content = emptyMap(),
            addedByStaffId = addedByStaffId,
            addedAt = LocalDateTime.now(),
            appointmentId = appointmentId,
        )

    internal fun buildProductOrderTable(personId: PersonPiiToken): ProductOrderTable {
        val product = ProductModel(
            id = RangeUUID.generate(),
            title = "Alice 222 [AP.NAC.02.SC.CR.EMP] - Opcional - PME",
            displayName = "Conforto +",
            complementName = "(Reembolso)",
            prices = listOf(
                ProductPriceModel(
                    id = "1",
                    title = "Price Title",
                    amount = BigDecimal("900.00"),
                    minAge = 0,
                    maxAge = 99
                )
            ),
            externalIds = listOf(ExternalIdModel(ExternalIdKey.MV_PLAN, "1"))
        )

        return ProductOrderTable(
            personId = personId,
            price = BigDecimal("800.00"),
            selectedProduct = MemberProduct(product.id, product.prices.map { it.toTransport() }, ProductType.valueOf(product.type.name)),
        )

    }


    internal fun buildMedicineTable() =
        MedicineTable(
            ean = "7896269901553",
            tissCmed = "**********",
            name = "ZIAGENAVIR",
            presentation = "300 mg. 60 caps.",
            concentration = "300",
            unit = "MG",
            pharmaceuticalForm = "CAPSULA",
            routeOfAdministration = "VO",
            quantityPerPackage = "60",
            drugId = 5000,
            drugName = "ABACAVIR",
            atcCode = "J05AF06",
            medicineType = "REFERENCIA",
            mip = false,
            portaria344 = "LISTA - C4"
        )

    val staff = buildHealthcareTeamPhysicianTable()

    internal fun buildHealthPlan(
        personId: PersonNonPiiToken,
        description: String = "Melhorar alimentação, sono e praticar exercícios.",
    ) =
        HealthPlanTable(
            personId = personId,
            description = description,
            healthGoal = "viver melhor"
        )

    internal fun buildHealthPlanTask(
        personId: PersonNonPiiToken,
        healthPlanTable: HealthPlanTable,
        staffTable: StaffTable
    ) =
        HealthPlanTaskTable(
            personId = personId,
            healthPlanId = healthPlanTable.id,
            title = "Go to the doctor",
            description = "Hurry!",
            content = mapOf("diagnosticHypothesis" to "I have no idea"),
            dueDate = LocalDate.now(),
            status = HealthPlanTaskStatus.DRAFT,
            lastRequesterStaffId = staffTable.id,
            requestersStaffIds = setOf(staffTable.id),
            type = HealthPlanTaskType.REFERRAL,
            attachments = listOf(
                Attachment(
                    id = RangeUUID.generate(),
                    type = "exe",
                    fileName = "as_fotos_da_festa_ficaram_otimas.exe"
                )
            ),
            finishedBy = TaskUpdatedBy(
                id = staffTable.id,
                source = TaskSourceType.STAFF
            ),
            createdBy = TaskUpdatedBy(
                id = staffTable.id,
                source = TaskSourceType.STAFF
            ),
            favorite = false,
            appointmentId = RangeUUID.generate(),
            releasedAt = null,
            finishedAt = null,
            acknowledgedAt = null,
            updatedBy = null,
            frequency = null,
            deadline = null,
            start = null,
            groupId = null,
            originTaskId = null,
            releasedByStaffId = null
        )

    internal fun buildHealthPlanTaskTemplateTable(
        title: String = "Comer cenoura",
    ) =
        HealthPlanTaskTemplateTable(
            type = HealthPlanTaskType.EATING,
            title = title,
            description = "Coma cenoure cozida no vapor ou assada, nunca crua.",
            content = emptyMap(),
            frequency = FrequencyModel(FrequencyType.TIMES, PeriodUnit.DAY, 2),
            deadline = DeadlineModel(PeriodUnit.MONTH, 3),
            start = StartModel(StartType.IMMEDIATE),
            attachments = emptyList(),
            active = true
        )

    internal fun buildAppointmentTable(
        staffId: UUID,
        personId: PersonNonPiiToken,
        createdAt: LocalDateTime = LocalDateTime.now(),
    ) =
        AppointmentTable(
            createdAt = createdAt,
            staffId = staffId,
            personId = personId,
            description = "Any",
            guidance = "Any",
            excuseNotes = listOf(ExcuseNote("presente no período da manhã para consulta")),
            completedAt = LocalDateTime.now(),
            type = ASSISTANCE_CARE,
            subjectiveCodes = listOf(Disease(Disease.Type.CIAP_2, "A03", "Febre - CIAP A03")),
            objective = "Suspeita de covid",
            plan = "Foi recomendado ficar em repouso.",
            referencedLinks = listOf(
                Appointment.ReferencedLink(RangeUUID.generate(), SCHEDULE),
                Appointment.ReferencedLink(RangeUUID.generate(), HEALTH_PLAN_TASK)
            ),
            status = AppointmentStatus.FINISHED,
        )

    internal fun buildExternalReferralTable(
        id: UUID = RangeUUID.generate(),
        personId: PersonNonPiiToken,
        reason: String = "reason",
        specialty: ExternalReferralModel.Specialty = ExternalReferralModel.Specialty(RangeUUID.generate(), "specialty"),
        referredAt: LocalDateTime = LocalDateTime.now(),
        referredByCouncilName: String = "CRM",
        referredByCouncilState: State = State.SP,
        referredByCouncilNumber: String = "0123456",
        referredByName: String = "Specialty Name",
        dueDate: LocalDateTime = LocalDateTime.now().plusMonths(3L),
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = ExternalReferralTable(
        id = id,
        personId = personId,
        reason = reason,
        specialty = specialty,
        referredAt = referredAt,
        referredByCouncilName = referredByCouncilName,
        referredByCouncilState = referredByCouncilState,
        referredByCouncilNumber = referredByCouncilNumber,
        referredByName = referredByName,
        dueDate = dueDate,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
    )

    internal fun buildDeadletterQueueTable(
        eventId: UUID = RangeUUID.generate(),
        topic: String = "topic",
        producer: String = "producer",
        payload: String = "payload",
        status: DeadLetterStatus = DeadLetterStatus.QUEUED,
    ) = DeadletterQueueTable(
        eventId = eventId,
        topic = topic,
        producer = producer,
        payload = payload,
        errorMessage = "error-message",
        stackTrace = "any stack trace",
        status = status,
    )

    internal fun buildDbLaboratoryTestResultProcessTable(
        claimId: Int,
        status: DbLaboratoryTestResultProcessType = DbLaboratoryTestResultProcessType.PENDING,
        personId: PersonNonPiiToken,
        testRequestTags: List<String> = emptyList(),
        executionGroupId: UUID? = null,
    ) =
        DbLaboratoryTestResultProcessTable(
            attendanceId = "attendanceId",
            claimId = claimId,
            status = status,
            personId = personId,
            testRequestTags = testRequestTags,
            executionGroupId = executionGroupId

        )

    internal fun buildHealthcareTeamTable(
        physicianStaffId: UUID = RangeUUID.generate(),
        nurseStaffId: UUID? = null,
        digitalCareNurseStaffIds: List<UUID> = emptyList(),
    ) =
        HealthcareTeamTable(
            physicianStaffId = physicianStaffId,
            nurseStaffId = nurseStaffId,
            digitalCareNurseStaffIds = digitalCareNurseStaffIds,
            type = HealthcareTeamModel.Type.STANDARD,
        )

    internal fun buildAppointmentScheduleOptionTable(
        title: String = "Ale Carreiro",
        description: String = "Nutrição comportamental",
        calendarUrl: String = "https://calendar.alice.com.br/ale_carreiro",
        imageUrl: String = "https://assets.alice.com.br/ale_carreiro.jpg",
        type: AppointmentScheduleType = NUTRITIONIST,
        staffId: UUID = RangeUUID.generate(),
        active: Boolean = true,
        specialtyId: UUID? = null,
        specialistId: UUID? = null,
        showOnApp: Boolean = true,
        healthcareModelType: HealthcareModelType = HealthcareModelType.V2
    ) = AppointmentScheduleOptionTable(
        title = title,
        description = description,
        calendarUrl = calendarUrl,
        imageUrl = imageUrl,
        type = type,
        staffId = staffId,
        active = active,
        specialtyId = specialtyId,
        specialistId = specialistId,
        showOnApp = showOnApp,
        healthcareModelType = healthcareModelType
    )

    internal fun buildHealthGoalTable(
        name: String = "Quero cuidar de um problema de saúde",
        imageUrl: String = "https://alice-member-app-assets.s3.amazonaws.com/health_goals/health_issue.png",
    ) =
        HealthGoalTable(
            name = name,
            imageUrl = imageUrl,
            active = true
        )

    internal fun buildPersonTaskTable(
        personId: PersonNonPiiToken,
        title: String = "Exame de Urina",
        description: String? = "Enviado pelo Usuário",
        type: TaskType = TaskType.TEST_REQUEST,
        status: TaskStatus = TaskStatus.WAITING_REVIEW,
        attachmentIds: List<UUID> = emptyList(),
    ) =
        PersonTaskTable(
            personId = personId,
            title = title,
            description = description,
            type = type,
            status = status,
            attachmentIds = attachmentIds
        )

    internal fun buildServiceScriptExecutionTable(
        triggerId: String,
        triggerType: String,
    ) = ServiceScriptExecutionTable(
        triggerId = triggerId,
        triggerType = triggerType,
        originalConditions = emptyList(),
        triggerConditions = emptyMap(),
        originalActions = emptyList(),
        executedActions = emptyList(),
    )

    internal fun buildPrescriptionSentenceTable() =
        PrescriptionSentenceTable(
            tokens = listOf("Amoxilina 300 MG"),
            dose = 1f,
            unit = CAPSULE,
            routeOfAdministration = RouteOfAdministration.ORAL,
            action = ActionType.TAKE,
            start = StartType.IMMEDIATE,
            frequency = FrequencyModel(type = FrequencyType.EVERY, unit = PeriodUnit.HOUR, quantity = 8),
            deadline = DeadlineModel(unit = PeriodUnit.DAY, quantity = 7)
        )

    internal fun buildEligibilityCheckTable(
        id: UUID = RangeUUID.generate(),
        personId: PersonNonPiiToken,
        checkedByEmail: String = "<EMAIL>",
        checkedAt: LocalDateTime = LocalDateTime.now(),
        providerUnitId: UUID = RangeUUID.generate(),
        result: EligibilityResult = EligibilityResult.ELIGIBLE,
        staffId: UUID? = null,
    ) = EligibilityCheckTable(
        id = id,
        personId = personId,
        checkedByEmail = checkedByEmail,
        checkedAt = checkedAt,
        providerUnitId = providerUnitId,
        result = result,
        staffId = staffId
    )

    internal fun buildExecutionGroupTable(id: UUID = RangeUUID.generate()) =
        ExecutionGroupTable(
            id = id,
            code = ExecutionGroupModel.generateRandomCode(),
            userEmail = "<EMAIL>",
            providerUnitId = RangeUUID.generate()
        )

    internal fun buildMvAuthorizedProcedure(
        personId: PersonNonPiiToken = PersonNonPiiToken(RangeUUID.generate(PERSON_NON_PII_TOKEN_RANGE)),
        procedureId: String = "123456",
        testRequestId: UUID? = null,
        status: MvAuthorizedProcedureStatus = MvAuthorizedProcedureStatus.ACTIVE,
    ) =
        MvAuthorizedProcedureTable(
            id = RangeUUID.generate(),
            executionGroupId = RangeUUID.generate(),
            personId = personId,
            procedureId = procedureId,
            executedAt = LocalDateTime.now(),
            testRequestId = testRequestId,
            status = status,
            extraGuiaInfo = ExtraGuiaInfoModel()
        )

    internal fun buildFleuryProcess(
        claimId: String,
        personId: PersonNonPiiToken,
        status: FleuryProcessType = FleuryProcessType.PENDING,
        executionGroupId: UUID? = RangeUUID.generate(),
        totvsGuiaId: UUID? = RangeUUID.generate(),
        lastProcessAt: LocalDateTime? = null,
    ) =
        FleuryProcessTable(
            claimId = claimId,
            personId = personId,
            status = status,
            executionGroupId = executionGroupId,
            totvsGuiaId = totvsGuiaId,
            lastProcessAt = lastProcessAt
        )

    internal fun buildFleuryTestResult(
        claimId: String,
        personId: PersonNonPiiToken,
        idFicha: String = "IdFicha",
        idItem: String = "idItem",
        idProduto: String = "123",
        idUnidade: String = "4567",
        procedimento: String = "procedimento",
        laudos: List<FleuryLaudo> = emptyList(),
        type: FleuryResultType = FleuryResultType.STRUCTURED,
    ) =
        FleuryTestResultTable(
            personId = personId,
            idFicha = idFicha,
            idItem = idItem,
            idProduto = idProduto,
            idUnidade = idUnidade,
            procedimento = procedimento,
            claimId = claimId,
            laudos = laudos,
            type = type
        )

    internal fun buildHaocProntoAtendimentoResult(
        personId: PersonNonPiiToken,
        claimId: Int? = null,
        haocDocumentId: UUID = RangeUUID.generate(),
        dataEntrada: LocalDateTime = LocalDateTime.now(),
        dataSaida: LocalDateTime = LocalDateTime.now(),
        motivoAtendimento: String = "motivoAtendimento",
        atendimentos: List<HaocAtendimento> = emptyList(),
    ) = HaocProntoAtendimentoResultTable(
        personId = personId,
        claimId = claimId,
        motivoAtendimento = motivoAtendimento,
        atendimentos = atendimentos,
        caracterizacaoAtendimento = CaracterizacaoAtendimento(
            Unidade.PAULISTA, "procedencia", dataEntrada
        ),
        desfecho = HaocDesfecho(
            motivo = "motivo",
            dataSaida = dataSaida,
            profissionalDeAlta = HaocProfissional(
                nome = "profissional",
                ocupacao = "ocupacao",
                uf = "uf",
                conselho = "conselho",
                numeroRegistro = "numeroRegistro"
            )
        ),
        haocDocumentId = haocDocumentId
    )

    internal fun buildHaocSumarioDeAltaResult(
        personId: PersonNonPiiToken,
        claimId: Int? = null,
        haocDocumentId: UUID = RangeUUID.generate(),
        dataEntrada: LocalDateTime = LocalDateTime.now(),
        dataSaida: LocalDateTime = LocalDateTime.now(),
    ) = HaocSumarioDeAltaResultTable(
        personId = personId,
        claimId = claimId,
        motivoAdmissao = HaocMotivoAdmissao(
            diagnostico = emptyList(),
        ),
        caracterizacaoAtendimento = HaocCaracterizacaoChegada(
            procedencia = "procedencia",
            local = "local",
            caraterDaInternacao = "caraterDaInternacao",
            dataInternacao = dataEntrada
        ),
        evolucaoClinica = "evolucaoClinica",
        desfecho = HaocDesfechoInternacao(
            motivo = "motivo",
            dataSaida = dataSaida,
            diasUTI = 0,
            profissionalDeAlta = HaocProfissional(
                nome = "profissional",
                ocupacao = "ocupacao",
                uf = "uf",
                conselho = "conselho",
                numeroRegistro = "numeroRegistro"
            )
        ),
        haocDocumentId = haocDocumentId
    )

    internal fun buildHaocDocument(
        personId: PersonNonPiiToken,
        claimId: Int? = null,
        content: String = "content",
        documentIdHash: String = RangeUUID.generate().toString(),
        documentType: HaocDocumentType = HaocDocumentType.PRONTO_ATENDIMENTO,
    ) = HaocDocumentTable(
        personId = personId,
        claimId = claimId,
        content = content,
        documentIdHash = documentIdHash,
        documentType = documentType
    )

    internal fun buildTestCode(
        code: String = "code",
        description: String = "description",
        internalDescription: String = "internalDescription",
        priority: Boolean = false,
        sensitiveResult: Boolean = false,
        resultExpirationInDays: Int = 180,
        synonyms: List<String> = listOf("synonyms"),
        active: Boolean = true,
    ) =
        TestCodeTable(
            code = code,
            description = description,
            internalDescription = internalDescription,
            sensitiveResult = sensitiveResult,
            resultExpirationInDays = resultExpirationInDays,
            priority = priority,
            synonyms = synonyms,
            active = active,
        )

    internal fun buildHealthFormTable() =
        HealthFormTable(
            name = "Pre Imersão",
            key = "PRE_IMMERSION",
            type = FormType.HEALTH
        )

    internal fun buildHealthFormSectionTable(formId: UUID = RangeUUID.generate()) =
        HealthFormSectionTable(
            healthFormId = formId,
            title = "Euroqol"
        )

    internal fun buildHealthFormQuestionTable(
        formId: UUID = RangeUUID.generate(),
        sectionId: UUID = RangeUUID.generate(),
    ) =
        HealthFormQuestionTable(
            healthFormId = formId,
            healthFormSectionId = sectionId,
            question = "Nunc sed velit dignissim sodales ut eu sem integer vitae",
            type = HealthFormQuestionType.MULTIPLE_OPTIONS,
            index = 1,
            defaultNext = 2,
            validations = mapOf(
                HealthFormQuestionValidationType.SEX to Sex.MALE
            ),
            options = listOf(
                HealthFormQuestionOption(
                    next = 2,
                    value = true,
                    label = "Sim"
                ),
                HealthFormQuestionOption(
                    next = 3,
                    value = false,
                    label = "Não"
                )
            )
        )

    internal fun buildMemberInvoiceTable(
        id: UUID = RangeUUID.generate(),
        memberId: UUID = RangeUUID.generate(),
        personId: PersonPiiToken = PersonPiiToken(),
        totalAmount: BigDecimal = BigDecimal.ONE,
        referenceDate: LocalDate = LocalDate.now(),
        dueDate: LocalDateTime = LocalDateTime.now(),
    ) =
        MemberInvoiceTable(
            id = id,
            memberId = memberId,
            personId = personId,
            totalAmount = totalAmount,
            referenceDate = referenceDate,
            dueDate = dueDate,
        )

    internal fun buildMemberInvoiceGroup(
        id: UUID = RangeUUID.generate(),
        memberInvoiceIds: List<UUID> = listOf(RangeUUID.generate()),
        billingAccountablePartyId: UUID = RangeUUID.generate(),
        referenceDate: LocalDate = LocalDate.now(),
        dueDate: LocalDate = LocalDate.now(),
        status: MemberInvoiceGroupStatus = MemberInvoiceGroupStatus.WAITING_PAYMENT,
        externalId: String = RangeUUID.generate().toString(),
        type: MemberInvoiceType = MemberInvoiceType.B2B_REGULAR_PAYMENT,
        totalAmount: BigDecimal = BigDecimal(1000.50),
    ) = MemberInvoiceGroupTable(
        id = id,
        externalId = externalId,
        memberInvoiceIds = memberInvoiceIds,
        billingAccountablePartyId = billingAccountablePartyId,
        referenceDate = referenceDate,
        dueDate = dueDate,
        status = status,
        totalAmount = totalAmount,
        type = type
    )

    internal fun buildPreActivationPayment(
        id: UUID = RangeUUID.generate(),
        memberInvoiceIds: List<UUID> = listOf(RangeUUID.generate()),
        billingAccountablePartyId: UUID = RangeUUID.generate(),
        referenceDate: LocalDate = LocalDate.now(),
        dueDate: LocalDate = LocalDate.now(),
        status: PreActivationPaymentStatus = PreActivationPaymentStatus.PROCESSED,
        externalId: String = RangeUUID.generate().toString(),
        type: PreActivationPaymentType = PreActivationPaymentType.B2B,
        totalAmount: BigDecimal = BigDecimal(1000.50),
    ) = PreActivationPaymentTable(
        id = id,
        externalId = externalId,
        memberInvoiceIds = memberInvoiceIds,
        billingAccountablePartyId = billingAccountablePartyId,
        referenceDate = referenceDate,
        dueDate = dueDate,
        status = status,
        totalAmount = totalAmount,
        type = type
    )

    internal fun buildStaffChannelHistoryTable(
        id: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        status: String = "BUSY",
        changedDate: LocalDateTime = LocalDateTime.now(),
    ) =
        StaffChannelHistoryTable(
            id = id,
            staffId = staffId,
            status = status,
            changedDate = changedDate,
        )

    internal fun buildChannelHealthConditionIgnoreTable(
        id: UUID = RangeUUID.generate(),
        healthConditionId: UUID = RangeUUID.generate(),
        code: String = "P013",
        codeType: HealthConditionCodeType = HealthConditionCodeType.CID_10,
    ) = ChannelHealthConditionIgnoreTable(
        id = id,
        healthConditionId = healthConditionId,
        code = code,
        codeType = codeType
    )

    internal fun buildHealthProductSimulationTable(
        id: UUID = RangeUUID.generate(),
    ) = HealthProductSimulationTable(
        id = id,
    )

    internal fun buildProductTable(
        id: UUID = RangeUUID.generate(),
    ) = ProductTable(
        title = "Alice 222 [AP.NAC.02.SC.CR.EMP] - Opcional - PME",
        displayName = "Conforto +",
        complementName = "(Reembolso)",
        id = id
    )

    internal fun buildOpportunity(
        productId: UUID,
        simulationId: UUID,
        id: UUID = RangeUUID.generate(),
    ) = OpportunityTable(
        id = id,
        productId = productId,
        simulationId = simulationId,
        prices = emptyList(),
        expiresAt = LocalDate.now().atEndOfTheDay().plusDays(30),
        source = OpportunitySource.SIMULATION
    )

    internal fun buildPersonClinicalAccount(
        personId: PersonNonPiiToken,
        healthcareTeamId: UUID = RangeUUID.generate(),
        channelId: String? = null,
        multiStaffIds: List<UUID> = emptyList(),
    ) =
        PersonClinicalAccountTable(
            personId = personId,
            healthcareTeamId = healthcareTeamId,
            channelId = channelId,
            multiStaffIds = multiStaffIds
        )

    internal fun buildPriceListing(
        id: UUID = RangeUUID.generate(),
        title: String = "Tabela 10",
        ranges: List<PriceListingItemModel> = listOf(
            PriceListingItemModel(
                minAge = 1,
                maxAge = 18,
                amount = BigDecimal.valueOf(100.0)
            )
        ),
    ) = PriceListingTable(
        id = id,
        title = title,
        items = ranges,
    )

    internal fun buildProductPriceListing(
        id: UUID = RangeUUID.generate(),
        priceListingId: UUID = RangeUUID.generate(),
        productId: UUID = RangeUUID.generate(),
        startDate: LocalDateTime = LocalDateTime.now(),
        endDate: LocalDateTime? = null,
    ) = ProductPriceListingTable(
        id = id,
        priceListingId = priceListingId,
        productId = productId,
        startDate = startDate,
        endDate = endDate,
    )

    internal fun buildAppointmentSchedule(
        personId: PersonNonPiiToken,
        type: AppointmentScheduleType = NUTRITIONIST,
        staffId: UUID? = RangeUUID.generate(),
    ) = AppointmentScheduleTable(
        personId = personId,
        eventId = RandomStringUtils.randomAlphabetic(20).uppercase(),
        eventName = "Test",
        location = null,
        status = SCHEDULED,
        startTime = LocalDateTime.now(),
        type = type,
        staffId = staffId,
        eventUuid = null,
    )

    internal fun buildBillingAccountableParty(
        firstName: String = "Duck",
        lastName: String = "Philips",
        type: BillingAccountablePartyTypeModel = BillingAccountablePartyTypeModel.NATURAL_PERSON,
        nationalId: String = "*********",
        email: String = "<EMAIL>",
        address: AddressModel = DataLayerTestModelFactory.buildAddress(),
    ) = BillingAccountablePartyTable(
        firstName = firstName,
        lastName = lastName,
        type = type,
        nationalId = nationalId,
        email = email,
        address = address
    )

    internal fun buildHealthMeasurementCategoryTable() =
        HealthMeasurementCategoryTable(
            key = "ANTHROPOMETRIC ${RangeUUID.generate()}",
            name = "Antropométrico",
            active = true,
        )

    internal fun buildHealthMeasurementTypeTable(
        categoryId: UUID = RangeUUID.generate(),
    ) = HealthMeasurementTypeTable(
        key = "WEIGHT",
        name = "Peso",
        healthMeasurementCategoryId = categoryId,
        format = HealthMeasurementTypeFormat.FLOAT,
        unit = "Kg",
        readOnly = false,
        active = true,
    )

    internal fun buildBeneficiaryTable(
        parentBeneficiary: UUID? = null,
        personId: PersonPiiToken = PersonPiiToken(),
        companyId: UUID = RangeUUID.generate(),
        type: BeneficiaryType = BeneficiaryType.EMPLOYEE,
        contractType: BeneficiaryContractType? = null,
        parentBeneficiaryRelationType: ParentBeneficiaryRelationType? = null,
        activatedAt: LocalDateTime = LocalDateTime.now(),
        memberId: UUID = RangeUUID.generate(),
        parentPerson: PersonPiiToken? = null,
    ) = BeneficiaryTable(
        parentBeneficiary = parentBeneficiary,
        personId = personId,
        companyId = companyId,
        type = type,
        contractType = contractType,
        parentBeneficiaryRelationType = parentBeneficiaryRelationType,
        activatedAt = activatedAt,
        memberId = memberId,
        parentPerson = parentPerson,
    )

    internal fun buildBeneficiaryOnboardingTable(
        beneficiaryId: UUID = RangeUUID.generate(),
        flowType: BeneficiaryOnboardingFlowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
        initialProductId: UUID = RangeUUID.generate(),
    ) = BeneficiaryOnboardingTable(
        beneficiaryId = beneficiaryId,
        flowType = flowType,
        initialProductId = initialProductId
    )

    internal fun buildBeneficiaryCompiledViewTable(
        id: UUID = RangeUUID.generate(),
        companyId: UUID = RangeUUID.generate(),
        personId: PersonPiiToken = PersonPiiToken(),
        personNationalId: String = "*********",
        personFullSocialName: String = "Beneficiário de Teste da View",
        personEmail: String = "<EMAIL>",
        beneficiaryId: UUID = RangeUUID.generate(),
        beneficiaryType: BeneficiaryType = BeneficiaryType.EMPLOYEE,
        beneficiaryContractType: BeneficiaryContractType = BeneficiaryContractType.CLT,
        productId: UUID = RangeUUID.generate(),
        productTitle: String = "Título de Produto",
        memberId: UUID = RangeUUID.generate(),
        memberStatus: MemberStatus = MemberStatus.ACTIVE,
        immersionStatus: BeneficiaryViewImmersionStatus = BeneficiaryViewImmersionStatus.UNSCHEDULED,
        insuranceStatus: BeneficiaryViewInsuranceStatus = BeneficiaryViewInsuranceStatus.ACTIVE,
    ) = BeneficiaryCompiledViewTable(
        id = id,
        companyId = companyId,
        personId = personId,
        personNationalId = personNationalId,
        personFullSocialName = personFullSocialName,
        personEmail = personEmail,
        beneficiaryId = beneficiaryId,
        beneficiaryType = beneficiaryType,
        beneficiaryContractType = beneficiaryContractType,
        productId = productId,
        productTitle = productTitle,
        memberId = memberId,
        memberStatus = memberStatus,
        immersionStatus = immersionStatus,
        insuranceStatus = insuranceStatus,
    )

    internal fun buildCassiMemberTable(
        id: UUID = RangeUUID.generate(),
        memberId: UUID = RangeUUID.generate(),
        expirationDate: LocalDateTime = LocalDateTime.now(),
        startDate: LocalDateTime = LocalDateTime.now(),
        accountNumber: String = "****************",
    ) = CassiMemberTable(
        id = id,
        memberId = memberId,
        accountNumber = accountNumber,
        startDate = startDate,
        expirationDate = expirationDate
    )

    internal fun buildBeneficiaryHubspotTable(
        id: UUID = RangeUUID.generate(),
        beneficiaryId: UUID = RangeUUID.generate(),
        personId: PersonPiiToken = PersonPiiToken(),
        companyId: UUID = RangeUUID.generate(),
        externalContactId: String = "***********",
        externalDealId: String = "5464123",
    ) = BeneficiaryHubspotTable(
        id = id,
        beneficiaryId = beneficiaryId,
        externalContactId = externalContactId,
        externalDealId = externalDealId,
        personId = personId,
        companyId = companyId,
    )

    internal fun buildFeatureConfigTable(
        namespace: FeatureNamespace = FeatureNamespace.EHR,
        key: String = "key",
        type: FeatureType = FeatureType.STRING,
        value: String = "value",
        description: String = "Feature config descriptiom",
        active: Boolean = true,
    ) = FeatureConfigTable(
        namespace = namespace,
        key = key,
        type = type,
        value = value,
        description = description,
        active = active
    )


    internal fun buildAliceTestResultFileTable(
        id: UUID = RangeUUID.generate(),
        personId: PersonNonPiiToken,
    ) = AliceTestResultFileTable(
        id = id,
        personId = personId,
        referencedModelId = RangeUUID.generate(),
        referencedFileId = RangeUUID.generate().toString(),
        referencedModelClass = "ALICE-TEST-RESULT",
    )

    internal fun buildServiceScriptNavigationGroupTable(
        personId: PersonNonPiiToken,
        scriptNodeId: UUID = RangeUUID.generate(),
        startedAt: LocalDateTime = LocalDateTime.now(),
        finishedAt: LocalDateTime? = null,
        source: ServiceScriptNavigationSource? = null
    ) =
        ServiceScriptNavigationGroupTable(
            personId = personId,
            scriptNodeId = scriptNodeId,
            startedAt = startedAt,
            finishedAt = finishedAt,
            source = source
        )

    internal fun buildSchedulePreferenceTable(
        staffId: UUID = RangeUUID.generate(),
        weeklyHours: Int = 10,
        intervalBetweenEvents: Int = 10,
        googleRefreshToken: String? = "",
        zoomLink: String? = null,
        zoomRefreshToken: String? = null,
        staffSearchTokens: TsVector = TsVector("")
    ) = SchedulePreferenceTable(
        staffId = staffId,
        weeklyHours = weeklyHours,
        intervalBetweenEvents = intervalBetweenEvents,
        googleRefreshToken = googleRefreshToken,
        zoomLink = zoomLink,
        zoomRefreshToken = zoomRefreshToken,
    )

    internal fun buildHealthConditionTable(
        name: String = "Glaucoma secundário a traumatismo ocular",
        displayName: String = "Glaucoma",
        code: String = "H403",
        codeType: HealthConditionCodeType = HealthConditionCodeType.CID_10,
        specialities: List<String>? = listOf("Oftalmologia"),
        cptApplicationRule: CptApplicationRule = CptApplicationRule.WITHOUT_SURGERY_ONLY,
        surgeryNames: List<String>? = listOf("Trabeculectomia"),
        riskRating: Int = 0,
        isChronic: Boolean = false,
        conditionType: HealthConditionType? = null
    ) = HealthConditionTable(
        name = name,
        displayName = displayName,
        code = code,
        codeType = codeType,
        specialities = specialities,
        cptApplicationRule = cptApplicationRule,
        surgeryNames = surgeryNames,
        riskRating = riskRating,
        isChronic = isChronic,
        conditionType = conditionType
    )

    internal fun buildHealthConditionTemplateTable(
        id: UUID = RangeUUID.generate(),
        healthConditionId: UUID = RangeUUID.generate(),
        template: String = "template",
    ) = HealthConditionTemplateTable(
        id = id,
        healthConditionId = healthConditionId,
        template = template
    )

    internal fun buildHealthConditionAxisTable(
        name: String = "axis",
        type: HealthConditionCodeType = HealthConditionCodeType.CIPE,
        id: UUID = RangeUUID.generate(),
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = HealthConditionAxisTable(
        name = name,
        type = type,
        id = id,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    internal fun buildHealthInstitutionNegotiationTable(
        code: String = "code",
        description: String = "description",
        tableType: String = "tableType",
        providerUnitGroupId: UUID = RangeUUID.generate(),
        healthcareResourceId: UUID = RangeUUID.generate(),
        validAfter: LocalDate = LocalDate.now(),
        activeHealthcareResource: Boolean = true,
        id: UUID = RangeUUID.generate(),
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        externalId: String = "externalId"
    ) = HealthInstitutionNegotiationTable(
        code = code,
        description = description,
        tableType = tableType,
        providerUnitGroupId = providerUnitGroupId,
        healthcareResourceId = healthcareResourceId,
        validAfter = validAfter,
        activeHealthcareResource = activeHealthcareResource,
        id = id,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
        externalId = externalId
    )

    internal fun buildHealthSpecialistResourceBundleTable(
        id: UUID = RangeUUID.generate(),
        primaryTuss: String = "primaryTuss",
        description: String = "description",
        code: String = "code",
        serviceType: HealthSpecialistResourceBundleServiceType = HealthSpecialistResourceBundleServiceType.CONSULTATION,
        status: Status = Status.ACTIVE
    ) = HealthSpecialistResourceBundleTable(
        id = id,
        primaryTuss = primaryTuss,
        description = description,
        code = code,
        serviceType = serviceType,
        status = status
    )

    internal fun buildOutcomeConfTable(
        type: OutcomeConf.OutcomeType = OutcomeConf.OutcomeType.CLINICAL,
        key: String = "MSQ",
        description: String = "mini sleep questionnaire = MSQ",
        status: OutcomeConf.OutcomeStatus = OutcomeConf.OutcomeStatus.ACTIVE,
        referenceRange: List<OutcomeConf.ReferenceRange> = listOf(
            OutcomeConf.ReferenceRange(
                lowerLimit = BigDecimal(10),
                upperLimit = BigDecimal(25),
                description = "LIGHT"
            )
        ),
        healthMeasurementTypeId: UUID? = RangeUUID.generate()
    ) = OutcomeConfTable(
        type = type,
        key = key,
        description = description,
        status = status,
        referenceRange = referenceRange,
        healthMeasurementTypeId = healthMeasurementTypeId
    )

    internal fun buildMemberOnboardingTemplateTable(
        name: String = "",
        type: MemberOnboardingTemplate.MemberOnboardingTemplateType = MemberOnboardingTemplate.MemberOnboardingTemplateType.B2C,
        id: UUID = RangeUUID.generate(),
    ) = MemberOnboardingTemplateTable(
        name = name,
        type = type,
        id = id
    )

    internal fun buildNullvsIntegrationRecordTable(
        id: UUID = RangeUUID.generate(),
        internalId: UUID = RangeUUID.generate(),
        internalModelName: InternalModelType = InternalModelType.MEMBER,
        externalId: String = "totvs-external-id",
        externalModelName: ExternalModelType = ExternalModelType.BENEFICIARY,
        integratedAt: LocalDateTime = LocalDateTime.now(),
    ) = NullvsIntegrationRecordTable(
        id = id,
        internalId = internalId,
        internalModelName = internalModelName,
        externalId = externalId,
        externalModelName = externalModelName,
        integratedAt = integratedAt
    )

    internal fun buildEitaNullvsIntegrationRecordTable(
        id: UUID = RangeUUID.generate(),
        internalId: UUID = RangeUUID.generate(),
        internalModelName: EitaNullvsInternalModelType = EitaNullvsInternalModelType.HEALTHCARE_BUNDLE,
        externalId: String = "totvs-external-id",
        externalModelName: EitaNullvsExternalModelType = EitaNullvsExternalModelType.BUNDLE,
        integratedAt: LocalDateTime = LocalDateTime.now(),
    ) = EitaNullvsIntegrationRecordTable(
        id = id,
        internalId = internalId,
        internalModelName = internalModelName,
        externalId = externalId,
        externalModelName = externalModelName,
        integratedAt = integratedAt
    )

    internal fun buildNullvsIntegrationLogTable(
        id: UUID = RangeUUID.generate(),
        eventId: UUID = RangeUUID.generate(),
        eventName: String = "MEMBER_CREATED_EVENT",
        internalId: UUID = RangeUUID.generate(),
        internalModelName: InternalModelType = InternalModelType.MEMBER,
        externalModelName: ExternalModelType = ExternalModelType.BENEFICIARY,
        integrationEventName: String = "beneficiario_create_request",
        batchId: String = "0000000000001",
        idSoc: String = RangeUUID.generate().toString(),
        payloadSequenceId: Int = 1,
        batchType: BatchType = BatchType.CREATE,
        description: String? = "",
        status: LogStatus = LogStatus.PENDING,
    ) = NullvsIntegrationLogTable(
        id = id,
        eventId = eventId,
        eventName = eventName,
        internalId = internalId,
        internalModelName = internalModelName,
        integrationEventName = integrationEventName,
        externalModelName = externalModelName,
        batchId = batchId,
        idSoc = idSoc,
        payloadSequenceId = payloadSequenceId,
        batchType = batchType,
        description = description,
        status = status,
    )

    internal fun buildEitaNullvsIntegrationLogTable(
        id: UUID = RangeUUID.generate(),
        eventId: UUID = RangeUUID.generate(),
        eventName: String = "MEMBER_CREATED_EVENT",
        internalId: UUID = RangeUUID.generate(),
        internalModelName: EitaNullvsInternalModelType = EitaNullvsInternalModelType.HEALTHCARE_BUNDLE,
        externalModelName: EitaNullvsExternalModelType = EitaNullvsExternalModelType.BUNDLE,
        integrationEventName: String = "beneficiario_create_request",
        batchId: String = "0000000000001",
        idSoc: String = RangeUUID.generate().toString(),
        payloadSequenceId: Int = 1,
        batchType: BatchType = BatchType.CREATE,
        description: String? = "",
        status: LogStatus = LogStatus.PENDING,
    ) = EitaNullvsIntegrationLogTable(
        id = id,
        eventId = eventId,
        eventName = eventName,
        internalId = internalId,
        internalModelName = internalModelName,
        integrationEventName = integrationEventName,
        externalModelName = externalModelName,
        batchId = batchId,
        idSoc = idSoc,
        payloadSequenceId = payloadSequenceId,
        batchType = batchType,
        description = description,
        status = status,
    )

    internal fun buildAnalyteOutcomeMapping(
        id: UUID = RangeUUID.generate(),
        outcomeId: UUID = RangeUUID.generate(),
        analyteId: String = "00011122",
        createdAt: LocalDateTime = LocalDateTime.now()
    ) = AnalyteOutcomeMapping(
        id = id,
        outcomeId = outcomeId,
        analyteId = analyteId,
        createdAt = createdAt
    )

    internal fun buildSpecialistOpinionTable(
        staffId: UUID,
        appointmentId: UUID,
        medicalSpecialtyId: UUID,
        personId: PersonNonPiiToken,
    ) =
        SpecialistOpinionTable(
            staffId = staffId,
            personId = personId,
            appointmentId = appointmentId,
            medicalSpecialtyId = medicalSpecialtyId,
            question = "Testing question",
            caseSummary = "Testing case summary",
            files = emptyList(),
        )

    internal fun buildCompanyTable(
        id: UUID = RangeUUID.generate(),
        parentId: UUID? = null,
        externalCode: String? = null,
        name: String = "Acme",
        legalName: String = "Acme LTDA.",
        cnpj: String = "00.000.000/0001-00",
        email: String = "<EMAIL>",
        phoneNumber: String = "+*************",
        address: CompanyAddressModel = CompanyAddressModel(
            postalCode = "12345-123",
            street = "Av. Paulista",
            number = 1000,
            city = "São Paulo",
            State = "SP",
            neighborhood = "Pinheiros"
        ),
        bankingInfo: CompanyBankingInfo = CompanyBankingInfo(
            bankCode = 0,
            agencyNumber = "0000",
            accountNumber = "00000-0"
        ),
        billingAccountablePartyId: UUID = RangeUUID.generate(),
        availableProducts: List<UUID> = listOf(RangeUUID.generate()),
        defaultProductId: UUID = RangeUUID.generate(),
        flexBenefit: Boolean? = null,
        hasEmployeesAbroad: Boolean = false,
        contractUrls: List<String> = listOf("url-1", "url-2"),
        priceAdjustmentType: PriceAdjustmentType = PriceAdjustmentType.POOL,
        contractStartedAt: LocalDateTime = LocalDateTime.now(),
        beneficiariesCountAtDayZero: Int = 40,
        defaultFlowType: BeneficiaryOnboardingFlowType? = null,
        version: Int = 0,
        brand: Brand? = Brand.ALICE,
        totvsContract: String? = null,
        externalBrandId: String? = null,
        contractIds: List<UUID> = emptyList(),
    ) = CompanyTable(
        id = id,
        parentId = parentId,
        externalCode = externalCode,
        name = name,
        legalName = legalName,
        cnpj = cnpj,
        email = email,
        phoneNumber = phoneNumber,
        address = address,
        bankingInfo = bankingInfo,
        billingAccountablePartyId = billingAccountablePartyId,
        availableProducts = availableProducts,
        defaultProductId = defaultProductId,
        flexBenefit = flexBenefit,
        hasEmployeesAbroad = hasEmployeesAbroad,
        contractsUrls = contractUrls,
        priceAdjustmentType = priceAdjustmentType,
        contractStartedAt = contractStartedAt,
        beneficiariesCountAtDayZero = beneficiariesCountAtDayZero,
        defaultFlowType = defaultFlowType,
        version = version,
        brand = brand,
        totvsContract = totvsContract,
        externalBrandId = externalBrandId,
        contractIds = contractIds,
    )

    internal fun buildCompanyContractTable(
        id: UUID = RangeUUID.generate(),
        externalId: String = "1234567",
        title: String = "plano abc",
        billingAccountablePartyId: UUID? = null,
        startedAt: LocalDate = LocalDate.now(),
        accountableEmail: String = "<EMAIL>",
        contractFileIds: List<ContractFileModel> = emptyList(),
        isProRata: Boolean? = null,
        defaultProductId: UUID? = null,
        availableProducts: List<UUID>? = null,
        dueDate: Int = 10,
        isBillingLevel: Boolean = false,
        groupCompany: String? = null,
        paymentType: PaymentModel? = null,
    ) = CompanyContractTable(
        id = id,
        externalId = externalId,
        title = title,
        billingAccountablePartyId = billingAccountablePartyId,
        startedAt = startedAt,
        accountableEmail = accountableEmail,
        contractFileIds = contractFileIds,
        isProRata = isProRata,
        defaultProductId = defaultProductId,
        availableProducts = availableProducts,
        dueDate = dueDate,
        isBillingLevel = isBillingLevel,
        groupCompany = groupCompany,
        paymentType = paymentType,
    )

    internal fun buildCompanyProductConfigurationTable(
        id: UUID = RangeUUID.generate(),
        companyProductType: CompanyProductType = CompanyProductType.MICRO_2_5_OPTIONAL,
        availableProductIds: List<UUID> = listOf(RangeUUID.generate())
    ) = CompanyProductConfigurationTable(
        id = id,
        companyProductType = companyProductType,
        availableProductIds = availableProductIds
    )

    internal fun buildCompanySubContractTable(
        id: UUID = RangeUUID.generate(),
        externalId: String = "1234567",
        title: String = "plano abc",
        companyId: UUID = RangeUUID.generate(),
        contractId: UUID = RangeUUID.generate(),
        hasEmployeesAbroad: Boolean = true,
        billingAccountablePartyId: UUID? = null,
        isProRata: Boolean? = null,
        defaultProductId: UUID? = null,
        availableProducts: List<UUID>? = null,
        dueDate: Int = 10,
        isBillingLevel: Boolean = false,
        paymentType: PaymentModel? = null,
    ) = CompanySubContractTable(
        id = id,
        externalId = externalId,
        title = title,
        billingAccountablePartyId = billingAccountablePartyId,
        isProRata = isProRata,
        defaultProductId = defaultProductId,
        availableProducts = availableProducts,
        dueDate = dueDate,
        isBillingLevel = isBillingLevel,
        paymentType = paymentType,
        companyId = companyId,
        contractId = contractId,
        hasEmployeesAbroad = hasEmployeesAbroad,
    )

    internal fun buildLegalGuardianAssociationTable(
        personId: PersonPiiToken = PersonPiiToken(),
        guardianId: PersonPiiToken = PersonPiiToken(),
        degreeOfKinship: DegreeOfKinship = DegreeOfKinship.GUARDIAN,
        status: LegalGuardianAssociationStatusType = LegalGuardianAssociationStatusType.VALID
    ) = LegalGuardianAssociationTable(
        personId = personId,
        guardianId = guardianId,
        degreeOfKinship = degreeOfKinship,
        status = status
    )

    internal fun buildSalesFirmStaffTable(
        id: UUID = RangeUUID.generate(),
        salesFirmId: UUID = RangeUUID.generate(),
        firstName: String = "Rogerio",
        lastName: String = "Ceni",
        email: String = "<EMAIL>",
        role: SalesFirmStaffRole = SalesFirmStaffRole.MAIN_STAFF,
        status: Status = Status.ACTIVE,
        version: Int = 0,
        deletedAt: LocalDateTime? = null,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = SalesFirmStaffTable(
        id,
        salesFirmId,
        firstName,
        lastName,
        email,
        role,
        status,
        deletedAt,
        version,
        createdAt,
        updatedAt,
    )

    internal fun buildSalesFirmTable(
        id: UUID = RangeUUID.generate(),
        name: String = "Corretora Tricolor",
        legalName: String = "Corretora Tricolor Ltda",
        cnpj: String = "12345678910",
        email: String = "<EMAIL>",
        phoneNumber: String = "1140028922",
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = SalesFirmTable(
        id = id,
        name = name,
        legalName = legalName,
        cnpj = cnpj,
        email = email,
        phoneNumber = phoneNumber,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    internal fun buildTotvsGuiaTable(
        id: UUID = RangeUUID.generate(),
        code: String = "123456789",
        externalCode: String? = null,
        origin: TotvsGuiaOrigin = TotvsGuiaOrigin.EHR,
        personId: PersonNonPiiToken = PersonNonPiiToken(RangeUUID.generate(PERSON_NON_PII_TOKEN_RANGE)),
        requestedAt: LocalDate = LocalDate.now(),
        status: TotvsGuiaStatus = TotvsGuiaStatus.PENDING,
        type: MvUtil.TISS = MvUtil.TISS.EXAM
    ) = TotvsGuiaTable(
        code = code,
        externalCode = externalCode,
        origin = origin,
        personId = personId,
        requestedAt = requestedAt,
        status = status,
        type = type
    )

    internal fun buildHealthcareBundleTable(
        id: UUID = RangeUUID.generate(),
        code: String = "",
        primaryTuss: String = "",
        status: HealthcareBundleStatus = HealthcareBundleStatus.ACTIVE,
        category: HealthcareBundleCategory = HealthcareBundleCategory.HEALTH_INSTITUTION,
        hasMedicalFees: Boolean = false,
        validAfter: LocalDate = LocalDate.now(),
        name: String = "Apendicectomia",
        providerName: String? = null,
        groups: List<UUID> = emptyList(),
        includedResources: List<UUID> = emptyList(),
        excludedResources: List<UUID> = emptyList(),
    ) = HealthcareBundleTable(
        id = id,
        code = code,
        primaryTuss = primaryTuss,
        status = status,
        category = category,
        hasMedicalFees = hasMedicalFees,
        validAfter = validAfter,
        name = name,
        providerName = providerName,
        groups = groups,
        includedResources = includedResources,
        excludedResources = excludedResources,
    )

    internal fun buildGlossAuthorizationInfoTable(
        id: UUID = RangeUUID.generate(),
        code: String = "09R",
        title: String = "Carência ativa",
        description: String = "Procedimento em período de carência.",
    ) = GlossAuthorizationInfoTable(
        id = id,
        title = title,
        code = code,
        description = description
    )

    internal fun buildHospitalizationInfo(
        providerUnitId: UUID = RangeUUID.generate(),
        totvsGuiaId: UUID = RangeUUID.generate(),
        suggestedDate: LocalDate = LocalDate.now(),
        clinicalIndication: String = "teste",
        healthCondition: HospitalizationHealthConditionModel = HospitalizationHealthConditionModel(
            name = "teste",
            code = "83",
        )
    ) = HospitalizationInfoTable(
        providerUnitId = providerUnitId,
        totvsGuiaId = totvsGuiaId,
        suggestedDate = suggestedDate,
        clinicalIndication = clinicalIndication,
        healthCondition = healthCondition
    )

    internal fun buildChannelsZendeskTag(
        id: UUID = RangeUUID.generate(),
        channelTag: String = "boleto_final",
        zendeskAssignmentTag: String = "n2_queue",
        description: String = "segunda via de boleto"
    ) = ChannelsZendeskTagTable(
        id = id,
        channelTag = channelTag,
        zendeskAssignmentTag = zendeskAssignmentTag,
        description = description
    )

    internal fun buildAttachmentOpmeTable(
        id: UUID = RangeUUID.generate(),
        totvsGuiaId: UUID = RangeUUID.generate(),
        status: AttachmentStatus? = AttachmentStatus.PENDING,
        technicalJustification: String = "Uma justificativa técnica bem legal",
        materialSpecification: String = "Uma especificação de material bem legal",
        requestedOpmes: List<RequestedOPMEModel> = emptyList(),
        observation: String? = null,
    ) = AttachmentOpmeTable(
        id = id,
        totvsGuiaId = totvsGuiaId,
        status = status,
        technicalJustification = technicalJustification,
        materialSpecification = materialSpecification,
        requestedOpmes = requestedOpmes,
        observation = observation,
    )

    fun buildAttachmentChemotherapyTable(
        id: UUID = RangeUUID.generate(),
        totvsGuiaId: UUID = RangeUUID.generate(),
        status: AttachmentStatus = AttachmentStatus.PENDING,
        chemotherapyOncologicalDiagnosis: OncologicalDiagnosisModel = OncologicalDiagnosisModel(
            diagnosisDate = LocalDate.now(),
            stage = AnsStage.FIRST,
            type = AnsChemotherapyType.FIRST_LINE,
            purpose = AnsPurpose.ADJUVANT,
            tumor = AnsTumor.NAO_SE_APLICA,
            nodule = AnsNodule.NAO_SE_APLICA,
            metastasis = AnsMetastasis.NAO_SE_APLICA,
            healthCondition = ChemotherapyHealthConditionModel(
                name = "name",
                code = "123",
            ),
            ecoGt = AnsEcoGT.FULL_ACTIVE
        ),
        requestedDrugs: List<RequestedDrugsModel> = listOf(
            RequestedDrugsModel(
                startDate = LocalDate.now(),
                totalCycleDosage = 1.0,
                unitOfMeasurement = AnsUnitOfMeasurement.MG,
                administrationRoute = AnsAdministrationRoute.ORAL,
                frequency = 1,
                status = MvAuthorizedProcedureStatus.PENDING,
                drugsIdentification = DrugsIdentificationModel(
                    table = "19",
                    code = "1245678",
                    description = "droga01",
                )
            )
        ),
        cyclesQuantity: Int = 5,
        currentCycle: Int = 1,
        currentCycleDays: Int = 10,
        cyclesInterval: Int = 10,
        observation: String? = null,
        height: BigDecimal = BigDecimal("1.80"),
        weight: BigDecimal = BigDecimal("70.00"),
    ) = AttachmentChemotherapyTable(
        id = id,
        totvsGuiaId = totvsGuiaId,
        status = status,
        chemotherapyOncologicalDiagnosis = chemotherapyOncologicalDiagnosis,
        requestedDrugs = requestedDrugs,
        cyclesQuantity = cyclesQuantity,
        currentCycle = currentCycle,
        currentCycleDays = currentCycleDays,
        cyclesInterval = cyclesInterval,
        height = height,
        weight = weight,
        observation = observation,
    )

    fun buildInvoiceGroupTaxReceiptTable(
        id: UUID = RangeUUID.generate(),
        status: TaxReceiptStatus = TaxReceiptStatus.ISSUED,
        issuedAt: LocalDateTime = LocalDateTime.now(),
        pdfUrl: String = "http://www.example.com",
        invoiceGroupId: UUID = RangeUUID.generate(),
    ) = InvoiceGroupTaxReceiptTable(
        id = id,
        status = status,
        issuedAt = issuedAt,
        pdfUrl = pdfUrl,
        invoiceGroupId = invoiceGroupId
    )

    fun buildInvoiceLiquidationTaxReceiptTable(
        id: UUID = RangeUUID.generate(),
        status: TaxReceiptStatus = TaxReceiptStatus.ISSUED,
        issuedAt: LocalDateTime = LocalDateTime.now(),
        pdfUrl: String = "http://www.example.com",
        invoiceLiquidationId: UUID = RangeUUID.generate(),
    ) = InvoiceLiquidationTaxReceiptTable(
        id = id,
        status = status,
        issuedAt = issuedAt,
        pdfUrl = pdfUrl,
        invoiceLiquidationId = invoiceLiquidationId
    )

    fun buildTimelineTable(
        id: UUID = RangeUUID.generate(),
        personId: PersonNonPiiToken = PersonNonPiiToken(RangeUUID.generate(PERSON_NON_PII_TOKEN_RANGE)),
        staffId: UUID = RangeUUID.generate(),
        providerUnitId: UUID = RangeUUID.generate(),
        title: String = "Test Timeline",
        description: String = "Timeline Description",
        type: TimelineType = TimelineType.APPOINTMENT_ANNOTATION,
        status: TimelineStatus = TimelineStatus.FINISHED,
        draftGroup: List<UUID> = emptyList(),
        channelIds: List<String> = emptyList(),
        specialtyId: UUID = RangeUUID.generate(),
        evolutions: List<TimelineEvolution> = emptyList(),
        referencedLinks: List<Timeline.ReferencedLink> = emptyList(),
        referencedModelId: UUID = RangeUUID.generate(),
        referencedModelDate: LocalDateTime = LocalDateTime.now(),
        referencedModelClass: TimelineReferenceModel = TimelineReferenceModel.APPOINTMENT,
        hasSpecialistOpinion: Boolean? = false,
        appendages: List<TimelineAppendage> = emptyList(),
        aiSummary: String? = null,
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = TimelineTable(
        id = id,
        personId = personId,
        staffId = staffId,
        providerUnitId = providerUnitId,
        title = title,
        description = description,
        type = type,
        status = status,
        draftGroup = draftGroup,
        channelIds = channelIds,
        specialtyId = specialtyId,
        evolutions = evolutions,
        referencedLinks = referencedLinks,
        referencedModelId = referencedModelId,
        referencedModelDate = referencedModelDate,
        referencedModelClass = referencedModelClass,
        hasSpecialistOpinion = hasSpecialistOpinion,
        appendages = appendages,
        aiSummary = aiSummary,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
    )

    fun buildAttachmentRadiotherapyTable(
        id: UUID = RangeUUID.generate(),
        totvsGuiaId: UUID = RangeUUID.generate(),
        status: AttachmentStatus = AttachmentStatus.PENDING,
        oncologicalDiagnosisRadio: OncologicalDiagnosisRadioModel = OncologicalDiagnosisRadioModel(
            diagnosisDate = LocalDate.now(),
            imageDiagnosis = AnsImageDiagnosis.MAGNETIC_RESONANCE,
            stage = AnsStage.FIRST,
            purpose = AnsPurpose.ADJUVANT,
            healthCondition = RadiotherapyHealthConditionModel(
                name = "name",
                code = "123",
            ),
            ecoGt = AnsEcoGT.FULL_ACTIVE
        ),
        fieldsQuantity: Int = 5,
        fieldDose: Int = 1,
        totalDose: Int = 10,
        daysQuantity: Int = 10,
        expectedStartDate: LocalDate = LocalDate.now().plusDays(1),
        observation: String? = null,
    ) = AttachmentRadiotherapyTable(
        id = id,
        totvsGuiaId = totvsGuiaId,
        status = status,
        oncologicalDiagnosisRadio = oncologicalDiagnosisRadio,
        fieldsQuantity = fieldsQuantity,
        fieldDose = fieldDose,
        totalDose = totalDose,
        daysQuantity = daysQuantity,
        expectedStartDate = expectedStartDate,
        observation = observation,
    )
}
