package br.com.alice.data.layer.pipelines.subjects

import br.com.alice.common.core.Role
import br.com.alice.common.core.Subject
import br.com.alice.data.layer.models.Authorizable
import br.com.alice.data.layer.models.StaffModel
import java.util.UUID

open class StaffSubject(
    open val id: UUID,
    override val role: Role,
) : Subject, Authorizable {

    constructor(staff: StaffModel) : this(
        id = staff.id,
        role = staff.role
    )

    open fun viewAnonymized(): Boolean = false

    override fun toString() = "${this::class.simpleName}($id)"
}
