package br.com.alice.app.content.services.screens

import br.com.alice.app.content.client.screens.MemberOnboardingScreeService
import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.Alignment
import br.com.alice.app.content.model.AppBar
import br.com.alice.app.content.model.Button
import br.com.alice.app.content.model.Image
import br.com.alice.app.content.model.ImageSize
import br.com.alice.app.content.model.ImageType
import br.com.alice.app.content.model.Label
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.SectionPadding
import br.com.alice.app.content.model.SectionTextLayout
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.Variant
import br.com.alice.app.content.model.section.ButtonSection
import br.com.alice.app.content.model.section.GridGutter
import br.com.alice.app.content.model.section.GridSection
import br.com.alice.app.content.model.section.GridVariant
import br.com.alice.app.content.model.section.ImageSection
import br.com.alice.app.content.model.section.TextSection
import br.com.alice.app.content.services.screens.health.HealthProfessionalRecommendationScreenService
import br.com.alice.common.core.PersonId
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import java.util.UUID

private const val MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE = "member_onboarding_screen_call_to_complete"

class MemberOnboardingScreeServiceImpl(
    private val healthProfessionalRecommendationScreenService: HealthProfessionalRecommendationScreenService
) : MemberOnboardingScreeService {

    override suspend fun getCallToComplete(appVersion: SemanticVersion): Result<ScreensTransport, Throwable> {
        val banner = getBanner(appVersion)
        val content = getContent()

        return ScreensTransport(
            id = MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE,
            layout = ScreenLayout(
                type = "single_column",
                appBar = AppBar(
                    title = "",
                    back = ""
                ),
                body = listOf(banner, content)
            )
        ).success()
    }

    override suspend fun getRecommendedPhysicians(personId: PersonId): Result<ScreensTransport, Throwable> =
        healthProfessionalRecommendationScreenService.getRecommendedPhysicians(personId)

    override suspend fun getRecommendedPhysicianDetails(personId: PersonId, healthcareTeamId: UUID, recommended: Boolean): Result<ScreensTransport, Throwable> =
        healthProfessionalRecommendationScreenService.getRecommendedPhysicianDetails(personId, healthcareTeamId, recommended)

    private fun getBanner(appVersion: SemanticVersion) =
        Section(
            id = "${MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE}_banner_image",
            type = SectionType.IMAGE_SECTION,
            minAppVersion = SectionType.IMAGE_SECTION.minAppVersion!!,
            data = ImageSection(
                content = getImageContent(appVersion)
            )
        )

    private fun getContent() = Section(
        id = "${MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE}_grid_section",
        type = SectionType.GRID_SECTION,
        minAppVersion = SectionType.GRID_SECTION.minAppVersion!!,
        data = GridSection(
            gutter = GridGutter.GUTTER_3,
            gridVariant = GridVariant.FULL_WIDTH,
            children = listOf(
                buildText(
                    "Cuide da sua saúde desde o primeiro dia",
                    SectionTextLayout.TITLE_LARGE_HIGHLIGHT,
                    1
                ),
                buildText(
                    "Ao desbloquear seu primeiro Plano de Ação, você terá tarefas criadas com base no seu histórico e estado atual de saúde. Como:",
                    SectionTextLayout.BODY_LARGE,
                    2
                ),
                buildText(
                    "• Consulta com Médico de Família",
                    SectionTextLayout.TITLE_SMALL_HIGHLIGHT,
                    3
                ),
                buildText(
                    "• Indicação de aula ou encontro",
                    SectionTextLayout.TITLE_SMALL_HIGHLIGHT,
                    4
                ),
                buildText(
                    "• Avaliação completa da nossa calculadora de saúde",
                    SectionTextLayout.TITLE_SMALL_HIGHLIGHT,
                    5
                ),
                getButtonSection()
            )
        )
    )

    private fun getButtonSection() =
        Section(
            id = "${MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE}_grid_section_button_section",
            type = SectionType.BUTTON_SECTION,
            minAppVersion = SectionType.BUTTON_SECTION.minAppVersion!!,
            data = ButtonSection(
                alignment = Alignment.CENTER,
                button = Button(
                    id = "${MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE}_grid_section_button",
                    variant = Variant.PRIMARY,
                    shrinkWrap = false,
                    onTapAction = RemoteAction(
                        mobileRoute = ActionRouting.MEMBER_ONBOARDING_V2
                    ),
                    label = Label(
                        text = "Desbloquear meu Plano de Ação"
                    )
                )
            )
        )

    private fun getImageContent(appVersion: SemanticVersion) =
        takeIf { isNewVersion(appVersion) }?.let {
            ImageSection.Content(
                alignment = Alignment.CENTER,
                verticalPadding = SectionPadding.P1,
                horizontalPadding = SectionPadding.P0,
                image = Image(
                    url = "https://alice-member-app-assets.s3.us-east-1.amazonaws.com/member_onboarding/images/v2/physhisian_and_score_magenta_and_appointment_schedule.png",
                    type = ImageType.STATIC_FULL_WIDTH,
                    size = ImageSize.BIG
                )
            )
        } ?: ImageSection.Content(
            alignment = Alignment.LEFT,
            verticalPadding = SectionPadding.P1,
            horizontalPadding = SectionPadding.P3,
            image = Image(
                url = "https://alice-member-app-assets.s3.us-east-1.amazonaws.com/member_onboarding/images/v2/shortcut_appointment_schedule.png",
                type = ImageType.STATIC,
                size = ImageSize.BIG
            )
        )

    private fun buildText(text: String, layout: SectionTextLayout, index: Int) =
        Section(
            id = "${MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE}_grid_section_text_section_$index",
            type = SectionType.TEXT_SECTION,
            minAppVersion = SectionType.TEXT_SECTION.minAppVersion!!,
            data = TextSection.Content(
                content = TextSection.Content.Value(
                    title = text,
                    alignment = Alignment.LEFT,
                    layout = layout
                )
            )
        )

    private fun isNewVersion(appVersion: SemanticVersion): Boolean =
        FeatureService.get(
            namespace = FeatureNamespace.ALICE_APP,
            key = "minor_version_to_show_new_image_full_width",
            defaultValue = "4.8.0"
        ).let { SemanticVersion(it) <= appVersion }
}
