package br.com.alice.member.onboarding.model

import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType
import br.com.alice.data.layer.models.MemberOnboardingFlowType

enum class OnboardingVersion {
    V1, V2, V3
}

data class TitleAndDescriptionData(
    val title: String,
    val description: String,
    val stepsCompleted: List<Int>,
    val version: OnboardingVersion,
    val flowType: MemberOnboardingFlowType
)

data class OnboardingStepTypes(
    val flowType: MemberOnboardingFlowType,
    val version: OnboardingVersion,
    val types: List<MemberOnboardingStepType>
)
