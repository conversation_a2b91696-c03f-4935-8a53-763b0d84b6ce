package br.com.alice.tiss.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.events.AttachmentOpmeCreatedEvent
import br.com.alice.exec.indicator.events.AttachmentOpmeCreatedPayload
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.tiss.consumers.converters.AttachmentOpmeRequestBuilder
import br.com.alice.tiss.models.AttachmentHeader
import br.com.alice.tiss.models.OPMERequestAttachment
import br.com.alice.tiss.models.RequestedOPME
import br.com.alice.tiss.services.internal.AttachmentService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.math.BigInteger
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AttachmentOpmeCreatedConsumerTest : ConsumerTest() {

    @AfterTest
    fun setup() = clearAllMocks()

    private val attachmentService: AttachmentService = mockk()
    private val attachmentOpmeBuilder: AttachmentOpmeRequestBuilder = mockk()
    private val totvsGuiaService: TotvsGuiaService = mockk()

    private val consumer = AttachmentOpmeCreatedConsumer(attachmentService, attachmentOpmeBuilder, totvsGuiaService)

    private val totvsGuia = TestModelFactory.buildTotvsGuia()

    private val payload = AttachmentOpmeCreatedPayload(
        opmeTotvsGuia = totvsGuia,
        hospitalizationTotvsGuia = TestModelFactory.buildTotvsGuia(),
        attachment = TestModelFactory.buildAttachmentOpme(),
        beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        origin = GuiaOrigin.EITA
    )

    private val opmeRequest = OPMERequestAttachment(
        requestedOPMEs = listOf(
            RequestedOPME(
                opmeIdentification = GuiaProcedure(table = "22", code = "123", quantity = 1, description = ""),
                manufacturerOption = "1",
                requestedQuantity = BigInteger.valueOf(2),
                requestedValue = 10.25.toBigDecimal()
            ),
            RequestedOPME(
                opmeIdentification = GuiaProcedure(table = "22", code = "653", quantity = 1, description = ""),
                manufacturerOption = "1",
                requestedQuantity = BigInteger.valueOf(1),
                requestedValue = 2.25.toBigDecimal()
            )
        ),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE,
            phoneNumber = "(11) 1111-1111",
            email = "<EMAIL>"
        ),
        beneficiaryData = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        origin = GuiaOrigin.EHR,
        attachmentHeader = AttachmentHeader(
            ansCode = "1234",
            attachmentGuideNumber = "************",
            referencedGuideNumber = "123",
            requestDate = LocalDate.now()
        ),
        providerCnpj = "00000000000",
        observation = "Teste",
        materialSpecification = "Teste",
        technicalJustification = "Teste"
    )

    private val event = AttachmentOpmeCreatedEvent(payload)

    @Test
    fun `#handle GuiaHospitalizationCreatedEvent should process event`() = runBlocking {
        coEvery {
            totvsGuiaService.get(payload.hospitalizationTotvsGuia.id)
        } returns payload.hospitalizationTotvsGuia.success()

        coEvery {
            attachmentService.createOpme(any())
        } returns true.success()

        coEvery {
            attachmentOpmeBuilder.build(
                opmeTotvsGuia = payload.opmeTotvsGuia,
                hospitalizationTotvsGuia = payload.hospitalizationTotvsGuia,
                attachment = payload.attachment,
                beneficiary = payload.beneficiary,
                origin = payload.origin
            )
        } returns opmeRequest.success()

        coEvery {
            totvsGuiaService.get(totvsGuia.id)
        } returns totvsGuia.success()

        val result = consumer.handle(event)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { attachmentService.createOpme(any()) }
    }

    @Test
    fun `#handle GuiaHospitalizationCreatedEvent should not process event when it already has been processed`() = runBlocking {
        coEvery {
            attachmentService.createOpme(any())
        } returns true.success()

        coEvery {
            attachmentOpmeBuilder.build(
                opmeTotvsGuia = payload.opmeTotvsGuia,
                hospitalizationTotvsGuia = payload.hospitalizationTotvsGuia,
                attachment = payload.attachment,
                beneficiary = payload.beneficiary,
                origin = payload.origin
            )
        } returns opmeRequest.success()

        coEvery {
            totvsGuiaService.get(totvsGuia.id)
        } returns totvsGuia.copy(externalCode = "hasExternalCode").success()

        val result = consumer.handle(event)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyNone { attachmentService.createOpme(any()) }
    }
}
