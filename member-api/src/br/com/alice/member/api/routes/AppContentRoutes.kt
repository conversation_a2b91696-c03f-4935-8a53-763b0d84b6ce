package br.com.alice.member.api.routes

import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.member.api.controllers.ScreeningController
import br.com.alice.member.api.controllers.appContent.DrawerController
import br.com.alice.member.api.controllers.appContent.ScreenController
import br.com.alice.member.api.controllers.onboarding.MemberOnboardingV2Controller
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Route.appContentRoutes() {
    val screeningController by inject<ScreeningController>()
    val screenController by inject<ScreenController>()
    val drawerController by inject<DrawerController>()
    val memberOnboardingController by inject<MemberOnboardingV2Controller>()

    authenticate {
        route("/app_content") {
            get("/screen/{screenType}") { coHandler("screenType", screenController::getByScreenType) }
            get("/drawers/{screenType}") { coHandler("screenType", drawerController::getDrawersByScreenType) }
            get("/screen/screening") { coHandler(screeningController::getScreeningQuestion) }
            get("/screen/empty_mfc") { coHandler(memberOnboardingController::getEmptyScreenMFC) }
            get("/screen/healthcare_team_recommendations") { coHandler(memberOnboardingController::getRecommendedPhysicians) }
            get("/screen/healthcare_team_details/{healthcareTeamId}") { coHandler("healthcareTeamId", memberOnboardingController::getRecommendedPhysicianDetails) }
        }

        route("/screening") {
            route("/{screeningNavigationId}") {
                post("/open_chat") {
                    coHandler("screeningNavigationId", screeningController::finishScreeningAndOpenChat)
                }
                post("/close") {
                    coHandler("screeningNavigationId", screeningController::finishScreeningAndClose)
                }
            }
        }
    }
}
