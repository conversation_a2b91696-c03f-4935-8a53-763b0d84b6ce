package br.com.alice.data.layer.pipelines.subjects

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import org.junit.jupiter.api.Assertions.assertEquals
import kotlin.test.Test

class InternalHealthProfessionalSubjectTest {

    @Test
    fun `should create a InternalHealthProfessionalSubject from StaffModel`() {
        val id = RangeUUID.generate()
        val role = Role.TECHNIQUE_NURSE
        val staffModel = DataLayerTestModelFactory.buildStaff(id = id, role = role)

        val internalHealthProfessionalSubject = InternalHealthProfessionalSubject(staffModel)

        assertEquals(id, internalHealthProfessionalSubject.id)
        assertEquals(role, internalHealthProfessionalSubject.role)
    }
}
