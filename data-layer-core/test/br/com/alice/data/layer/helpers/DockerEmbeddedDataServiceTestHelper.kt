package br.com.alice.data.layer.helpers

import br.com.alice.common.DatabaseMigrator
import br.com.alice.common.TestApplication
import br.com.alice.common.rfc.TestInvoker
import br.com.alice.common.tokenVerifier
import br.com.alice.data.layer.ApplicationModule
import br.com.alice.data.layer.MAIN_JDBI
import br.com.alice.data.layer.MAIN_RO_JDBI
import br.com.alice.data.layer.TOKEN_JDBI
import br.com.alice.data.layer.repositories.JdbiRepositoryFactory
import br.com.alice.data.layer.tables.AnonymizableTable
import br.com.alice.data.layer.tables.PersonTokenTable
import br.com.alice.data.layer.tables.Table
import com.opentable.db.postgres.embedded.EmbeddedPostgres
import io.ktor.server.testing.TestApplicationEngine
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.koin.core.qualifier.named
import org.koin.dsl.module
import org.reflections.Reflections
import org.testcontainers.utility.DockerImageName
import kotlin.reflect.KClass

open class DockerEmbeddedDataServiceTestHelper {
    protected val httpInvoker = TestInvoker(testEngine, "data")

    companion object {
        private val db = EmbeddedPostgres.builder()
            .setImage(DockerImageName.parse("postgis/postgis"))
            .setTag("13-3.4")
            .start()

        val customMainJdbi = DatabaseMigrator.customMainJdbiEmbedded(
            customDb = db,
            customMigrationFolder = "dockerEmbedded"
        )

        val mainJdbiReplica = customMainJdbi
        val tokenJdbi = TestApplication.tokenJdbi

        var testEngine: TestApplicationEngine = TestApplicationEngine()

        private val customDbModule = module(createdAtStart = true) {
            single(qualifier = named(MAIN_JDBI)) { customMainJdbi }
            single(qualifier = named(MAIN_RO_JDBI)) { mainJdbiReplica }
            single(qualifier = named(TOKEN_JDBI)) { tokenJdbi }

            single { tokenVerifier }
        }

        @AfterAll
        @JvmStatic
        fun tearDown() {
            testEngine.stop(0, 0)
            db.close()
        }

        @Suppress("TYPE_MISMATCH_WARNING")
        @BeforeAll
        @JvmStatic
        fun init() {
            testEngine = TestApplicationEngine()
            val applicationEngine = testEngine.start()

                val modules = ApplicationModule.dependencyInjectionModules(true) + customDbModule

                ApplicationModule.module(modules, applicationEngine.application)

            Reflections("br.com.alice.data.layer.tables")
                .getSubTypesOf(Table::class.java)
                .filter { it != PersonTokenTable::class.java && it != AnonymizableTable::class.java }
                .forEach { truncate(it.kotlin) }
        }

        private fun <T : Table<T>> truncate(cls: KClass<T>) {
            val repoFactory = JdbiRepositoryFactory(customMainJdbi)
            repoFactory.get(cls).truncate()
        }
    }
}
