package br.com.alice.itau.converter

import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ItauPayment
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.itau.ServiceConfig
import br.com.alice.itau.clients.models.ItauBolecodeBeneficiaryCreateRequest
import br.com.alice.itau.clients.models.ItauBolecodeCreateDataResponse
import br.com.alice.itau.clients.models.ItauBolecodeCreateRequest
import br.com.alice.itau.clients.models.ItauBolecodeQrCodeCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoCodeBoletoDataCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoConfigDivergentReceipt
import br.com.alice.itau.clients.models.ItauBoletoDescriptionCharge
import br.com.alice.itau.clients.models.ItauBoletoFineCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoIndividualCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoInterestCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoPayerCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoPayerPersonCode
import br.com.alice.itau.clients.models.ItauBoletoPayerPersonCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoPayerTypeCreateRequest
import br.com.alice.itau.clients.models.ItauPaymentCreatedExternalIds
import br.com.alice.moneyin.models.AcquirerCreatePaymentResponse
import br.com.alice.moneyin.models.BankSlipInfo
import br.com.alice.moneyin.models.PaymentRequestInput
import br.com.alice.moneyin.models.PaymentStatus
import br.com.alice.moneyin.models.PixInfo
import java.math.BigDecimal
import java.time.format.DateTimeFormatter

fun ItauBolecodeCreateDataResponse.toAcquirerCreatePaymentResponse(itauPayment: ItauPayment): Pair<AcquirerCreatePaymentResponse, ItauPaymentCreatedExternalIds> =
    Pair(
        AcquirerCreatePaymentResponse(
            id = itauPayment.id.toString(),
            externalUrl = "Itau",
            status = PaymentStatus.PENDING,
            pix = PixInfo(
                copyAndPaste = this.pix.copyAndPaste,
                qrCodeBase64 = this.pix.qrCodeBase64,
            ),
            bankSlip = BankSlipInfo(
                barcodeData = this.boleto.individualBoleto.first().barcode,
                digitableLine = this.boleto.individualBoleto.first().digitableLine,
            )
        ), ItauPaymentCreatedExternalIds(
            pixId = this.pix.id,
            boletoId = this.boleto.individualBoleto.first().id
        )
    )

fun BigDecimal.toItauBoletoValue(): String {
    val value = this.multiply(BigDecimal(100)).toBigIntegerExact()
    return value.toString().padStart(17, '0')
}

fun PaymentRequestInput.toItauBolecodeCreateRequest(
    itauPayment: ItauPayment
): ItauBolecodeCreateRequest =
    ItauBolecodeCreateRequest(
        beneficiary = ItauBolecodeBeneficiaryCreateRequest(ServiceConfig.itauBoletoConfig.aliceAccountKey),
        boletoData = ItauBoletoCodeBoletoDataCreateRequest(
            descriptionCharge = ItauBoletoDescriptionCharge.BOLECODE.code,
            payer = ItauBoletoPayerCreateRequest(
                person = ItauBoletoPayerPersonCreateRequest(
                    name = this.payer.name,
                    personType = ItauBoletoPayerTypeCreateRequest(
                        personCode = when (this.payer.type) {
                            BillingAccountablePartyType.NATURAL_PERSON -> ItauBoletoPayerPersonCode.FISICA.code
                            BillingAccountablePartyType.LEGAL_PERSON -> ItauBoletoPayerPersonCode.JURIDICA.code
                        },
                        cnpj = if (this.payer.type == BillingAccountablePartyType.LEGAL_PERSON) {
                            this.payer.nationalId
                        } else null,
                        cpf = if (this.payer.type == BillingAccountablePartyType.NATURAL_PERSON) {
                            this.payer.nationalId
                        } else null
                    ),
                ),
            ),
            interest = if (shouldIgnoreInterestAndFineIfFirstPayment()) null else ItauBoletoInterestCreateRequest(),
            fine = if (shouldIgnoreInterestAndFineIfFirstPayment()) null else ItauBoletoFineCreateRequest(),
            divergentReceipt = ItauBoletoConfigDivergentReceipt(),
            individualBoleto = listOf(
                ItauBoletoIndividualCreateRequest(
                    ourNumber = itauPayment.ourNumber!!.toString().padStart(8, '0'),
                    dueDate = this.dueDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    titleValue = this.totalAmount.toItauBoletoValue()
                )
            ),
            qrCode = ItauBolecodeQrCodeCreateRequest(ServiceConfig.itauPixConfig.alicePixKey),
        )
    )
