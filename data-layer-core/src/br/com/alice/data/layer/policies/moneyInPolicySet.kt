package br.com.alice.data.layer.policies

import br.com.alice.data.layer.MONEY_IN_ENVIRONMENT_SUBSCRIBERS
import br.com.alice.data.layer.MONEY_IN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.BeneficiaryModel
import br.com.alice.data.layer.models.BillingAccountablePartyModel
import br.com.alice.data.layer.models.CancelPaymentOnAcquirerScheduleModel
import br.com.alice.data.layer.models.CompanyModel
import br.com.alice.data.layer.models.EmailCommunicationModel
import br.com.alice.data.layer.models.InvoiceItemModel
import br.com.alice.data.layer.models.InvoiceLiquidationModel
import br.com.alice.data.layer.models.InvoicePaymentModel
import br.com.alice.data.layer.models.ItauPaymentModel
import br.com.alice.data.layer.models.MemberInvoiceGroupModel
import br.com.alice.data.layer.models.MemberInvoiceModel
import br.com.alice.data.layer.models.MemberLightweightModel
import br.com.alice.data.layer.models.MemberModel
import br.com.alice.data.layer.models.MemberProductPriceAdjustmentModel
import br.com.alice.data.layer.models.MemberProductPriceModel
import br.com.alice.data.layer.models.PaymentDetailModel
import br.com.alice.data.layer.models.PersonBillingAccountablePartyModel
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.models.PersonPreferencesModel
import br.com.alice.data.layer.models.PixPaymentDetailModel
import br.com.alice.data.layer.models.PreActivationPaymentModel
import br.com.alice.data.layer.models.PriceListingModel
import br.com.alice.data.layer.models.ProductBundleModel
import br.com.alice.data.layer.models.ProductModel
import br.com.alice.data.layer.models.ProductPriceAdjustmentModel
import br.com.alice.data.layer.models.ProductPriceListingModel
import br.com.alice.data.layer.models.ResourceSignTokenModel
import br.com.alice.data.layer.models.TrackPersonABModel
import br.com.alice.data.layer.policies.features.viewBeneficiary
import br.com.alice.data.layer.subjects.Unauthenticated

val moneyInPolicySet = policySet {
    match("at money-in-domain-service", { rootService.name == MONEY_IN_ROOT_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            can(View, Update, Create) {
                resources(
                    MemberInvoiceModel::class,
                    InvoicePaymentModel::class,
                    ResourceSignTokenModel::class,
                    ItauPaymentModel::class,
                    CancelPaymentOnAcquirerScheduleModel::class,
                    PaymentDetailModel::class,
                    PixPaymentDetailModel::class,
                    InvoiceItemModel::class,
                    MemberInvoiceGroupModel::class,
                    PreActivationPaymentModel::class,
                    MemberModel::class,
                    PersonModel::class,
                    BeneficiaryModel::class,
                    ProductModel::class,
                    CompanyModel::class,
                    BillingAccountablePartyModel::class,
                    InvoiceLiquidationModel::class,
                )
            }
        }
    }

    match("at money-in-domain-service-subscribers", { rootService.name == MONEY_IN_ENVIRONMENT_SUBSCRIBERS }) {
        can(View, Update, Create) {
            resources(
                ResourceSignTokenModel::class,
                ItauPaymentModel::class,
                CancelPaymentOnAcquirerScheduleModel::class,
                MemberInvoiceModel::class,
                InvoiceLiquidationModel::class,
                InvoicePaymentModel::class,
                PaymentDetailModel::class,
                InvoiceItemModel::class,
                ProductModel::class,
                ProductBundleModel::class,
                PriceListingModel::class,
                ProductPriceListingModel::class,
                BillingAccountablePartyModel::class,
                PersonBillingAccountablePartyModel::class,
                EmailCommunicationModel::class,
                MemberInvoiceGroupModel::class,
                PreActivationPaymentModel::class,
            )
        }

        can(View, Create) {
            resources(
                TrackPersonABModel::class,
            )
        }

        can(View) {
            resources(
                PersonModel::class,
                MemberModel::class,
                ProductModel::class,
                MemberLightweightModel::class,
                MemberProductPriceModel::class,
                PersonPreferencesModel::class,
                ProductPriceAdjustmentModel::class,
                MemberProductPriceAdjustmentModel::class,
            )
        }

        includes(viewBeneficiary)
    }

    describe("for /status endpoints") {
        match("Unauthenticated", { subject is Unauthenticated }) {
            match("can view", { action is View }) {
                match("any MemberInvoice of Test Persons") { testPersonResource(MemberInvoiceModel::class) }
            }
        }
    }
}
