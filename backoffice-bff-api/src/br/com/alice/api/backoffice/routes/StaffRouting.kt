package br.com.alice.api.backoffice.routes

import br.com.alice.api.backoffice.controllers.staff.StaffController
import br.com.alice.common.coHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.staffRoutes() {

    authenticate {
        val staffController by inject<StaffController>()
        route("staff") {
            get("/") {
                co<PERSON>andler(staffController::index)
            }
            get("/build_staff") {
                co<PERSON>andler(staffController::buildFormByStaffType)
            }
            get("/search_provider_units") {
                co<PERSON>and<PERSON>(staffController::searchProviderUnits)
            }
        }
    }
}
