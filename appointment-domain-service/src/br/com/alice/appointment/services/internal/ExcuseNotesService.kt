package br.com.alice.appointment.services.internal

import br.com.alice.appointment.event.AppointmentExcuseNotesCreatedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.mapPair
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.Attachment
import br.com.alice.data.layer.models.ExcuseNote
import br.com.alice.data.layer.services.AppointmentDataService
import br.com.alice.documentsigner.services.DocumentPrinterService
import com.github.kittinunf.result.map
import org.apache.commons.lang3.RandomStringUtils
import java.util.UUID

class ExcuseNotesService(
    private val printerService: DocumentPrinterService,
    private val appointmentDataService: AppointmentDataService,
    private val kafkaProducerService: KafkaProducerService
) {

    suspend fun publishExcuseNotes(
        appointment: Appointment,
        staffId: UUID,
        token: String? = null,
        withExcuseNotesEvent: Boolean = false
    ) = appointment.excuseNotes.pmap { excuseNote ->
        createExcuseNote(
            appointment = appointment,
            excuseNote = excuseNote.fillId(),
            staffId = staffId,
            token = token
        )
    }.let { excuseNoteAttachments ->
        checkIfLauchExcuseNoteEvent(appointment, excuseNoteAttachments, withExcuseNotesEvent)
    }

    private suspend fun createExcuseNote(
        appointment: Appointment,
        excuseNote: ExcuseNote,
        staffId: UUID,
        token: String?
    ) = generateTaskToken().let { documentToken ->
            printerService.printExcuseNotes(
                appointment = appointment.copy(excuseNotes = listOf(excuseNote)),
                staffId = staffId,
                token = token,
                documentToken = documentToken
            ).map { byteArray ->
                printerService.saveFile(
                    id = appointment.id,
                    personId = appointment.personId,
                    doc = byteArray,
                    namespace = "signed_excuse_notes",
                    fileName = "Atestado.pdf"
                )
            }.mapPair { attachment ->
                excuseNote.copy(
                    attachmentId = attachment.id.toString(),
                    token = documentToken
                )
            }.get()
        }

    private fun generateTaskToken() = RandomStringUtils.randomNumeric(4)

    private suspend fun checkIfLauchExcuseNoteEvent(
        appointment: Appointment,
        excuseNoteAttachments: List<Pair<ExcuseNote, Attachment>>,
        withExcuseNotesEvent: Boolean
    ) = appointment.copy(
        excuseNotes = excuseNoteAttachments.map { it.first },
        attachments = excuseNoteAttachments.map { it.second }
    ).let { updatedAppointment ->
        if (withExcuseNotesEvent) {
            appointmentDataService.update(updatedAppointment).then {
                kafkaProducerService.produce(AppointmentExcuseNotesCreatedEvent(it))
            }.get()
        } else {
            updatedAppointment
        }
    }

    private fun ExcuseNote.fillId() =
        this.copy(id = this.id ?: RangeUUID.generate().toString())
}
