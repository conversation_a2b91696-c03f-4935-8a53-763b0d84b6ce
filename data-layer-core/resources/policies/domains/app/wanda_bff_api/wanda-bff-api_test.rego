package app.wanda_bff_api_test

import rego.v1

import data.app.wanda_bff_api

test_staff_view_models_allowed if {
    resources = [
        "HealthcareTeamModel",
        "HealthcareAdditionalTeam"
    ]

    every resource in resources {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_staff_view_count_models_allowed if {
    resources = [
        "BeneficiaryModel",
        "BeneficiaryOnboardingModel",
        "BeneficiaryOnboardingPhaseModel",
        "CaseRecord",
        "CassiMemberModel",
        "CompanyModel",
        "HealthCondition",
        "HealthConditionAxis",
        "HealthConditionRelated",
        "HealthConditionTemplate",
        "HealthcareMap",
        "PersonClinicalAccount",
        "RiskCalculationConf",
        "StaffModel"
    ]

    every resource in resources {
        {1,2} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_staff_view_count_create_models_allowed if {
    resources = [
        "Risk"
    ]

    every resource in resources {
        {1,2,3} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": resource},
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "StaffModel"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_product_tech_health_view_count_models_allowed_pt1 if {
    resources = [
        "ActionPlanTask",
        "AppContentScreenDetail",
        "Appointment",
        "AppointmentCoordination",
        "AppointmentEvent",
        "AppointmentEvolution",
        "AssistanceCare",
        "ChannelComment",
        "ChannelHistory",
        "ChannelTheme",
        "ClinicalBackground",
        "ConsentRegistration",
        "ConsolidatedRewardsModel",
        "DasaDiagnosticReport",
        "DischargeSummary",
        "EinsteinAlergia",
        "EinsteinAtendimento",
        "EinsteinAvaliacaoInicial",
        "EinsteinDadosDeAlta",
        "EinsteinDiagnostico",
        "EinsteinEncaminhamento",
        "EinsteinMedicamento",
        "EinsteinProcedimento",
        "EinsteinResultadoExame",
        "EinsteinResumoInternacao",
        "EinsteinStructuredTestResult",
        "ExternalReferralModel",
        "FhirBundle",
        "FhirDiagnosticReport",
        "FhirDocument"
    ]

    every resource in resources {
        {1,2} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": resource
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": resource
                }
            }
        ]}
    }
}

test_product_tech_health_view_count_models_allowed_pt2 if {
    resources = [
        "GuiaModel",
        "HDataOverview",
        "HLActionRecommendation",
        "HLAdherence",
        "HaocFhirProcess",
        "HealthLogicRecord",
        "HealthMeasurementModel",
        "HealthcareMap",
        "IntentionCoordination",
        "LaboratoryTestResultModel",
        "PersonEligibilityDuquesa",
        "PersonHealthEvent",
        "PersonHealthGoalModel",
        "PersonHealthLogic",
        "PersonHealthcareTeamRecommendationModel",
        "PersonModel",
        "PersonTeamAssociation",
        "PregnancyModel",
        "StaffModel",
        "TertiaryIntentionTouchPoint",
        "TestResultFeedback",
        "TestResultFileModel",
        "Timeline",
        "VideoCall",
        "WandaComment"
    ]

    every resource in resources {
        {1,2} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": resource
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH_HEALTH"
                },
                "resource": {
                    "opaType": resource
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_ActionPlanTask_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "ActionPlanTask"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_AppContentScreenDetail_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "AppContentScreenDetail"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_Appointment_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "Appointment"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_AppointmentCoordination_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "AppointmentCoordination"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_AppointmentEvent_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "AppointmentEvent"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_AppointmentEvolution_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "AppointmentEvolution"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_AssistanceCare_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "AssistanceCare"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_ChannelComment_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "ChannelComment"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_ChannelHistory_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "ChannelHistory"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_ChannelTheme_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "ChannelTheme"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_ClinicalBackground_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "ClinicalBackground"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_ConsentRegistration_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "ConsentRegistration"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_ConsolidatedRewardsModel_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "ConsolidatedRewardsModel"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_DasaDiagnosticReport_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "DasaDiagnosticReport"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_DischargeSummary_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "DischargeSummary"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_EinsteinAlergia_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "EinsteinAlergia"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_EinsteinAtendimento_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "EinsteinAtendimento"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_EinsteinAvaliacaoInicial_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "EinsteinAvaliacaoInicial"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_EinsteinDadosDeAlta_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "EinsteinDadosDeAlta"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_EinsteinDiagnostico_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "EinsteinDiagnostico"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_EinsteinEncaminhamento_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "EinsteinEncaminhamento"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_EinsteinMedicamento_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "EinsteinMedicamento"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_EinsteinProcedimento_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "EinsteinProcedimento"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_EinsteinResultadoExame_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "EinsteinResultadoExame"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_EinsteinResumoInternacao_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "EinsteinResumoInternacao"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_EinsteinStructuredTestResult_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "EinsteinStructuredTestResult"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_ExternalReferralModel_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "ExternalReferralModel"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_FhirBundle_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "FhirBundle"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_FhirDiagnosticReport_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "FhirDiagnosticReport"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_FhirDocument_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "FhirDocument"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_GuiaModel_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "GuiaModel"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_HDataOverview_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "HDataOverview"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_HLActionRecommendation_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "HLActionRecommendation"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_HLAdherence_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "HLAdherence"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_HaocFhirProcess_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "HaocFhirProcess"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_HealthLogicRecord_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "HealthLogicRecord"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_HealthMeasurementModel_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "HealthMeasurementModel"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_HealthcareMap_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "HealthcareMap"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_IntentionCoordination_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "IntentionCoordination"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_LaboratoryTestResultModel_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "LaboratoryTestResultModel"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_MemberModel_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "MemberModel"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_PersonClinicalAccount_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_PersonEligibilityDuquesa_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "PersonEligibilityDuquesa"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_PersonHealthGoalModel_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "PersonHealthGoalModel"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_PersonHealthLogic_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "PersonHealthLogic"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_PersonHealthcareTeamRecommendationModel_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "PersonHealthcareTeamRecommendationModel"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_PersonModel_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "PersonModel"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_PersonTeamAssociation_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "PersonTeamAssociation"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_PregnancyModel_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "PregnancyModel"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_TestResultFeedback_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "TestResultFeedback"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_TestResultFileModel_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "TestResultFileModel"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_TertiaryIntentionTouchPoint_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "TertiaryIntentionTouchPoint"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_Timeline_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "Timeline"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_VideoCall_allowed if {
    roles = [
        "CHIEF_RISK",
        "MED_RISK"
    ]

    every role in roles {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": role
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            }
        ]}
    }
}

test_wanda_bff_user_view_count_models_allowed if {
    resources = [
        "ChannelFup"
    ]

    roles = [
      "CHIEF_RISK",
      "ON_SITE_PHYSICIAN",
      "MANAGER_PHYSICIAN",
      "MED_RISK"
    ]

    every resource in resources {
        every role in roles {
            {1,2} == wanda_bff_api.allow with input as {"cases": [
                {
                    "index": 1,
                    "action": "view",
                    "subject": {
                        "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                        "opaType": "StaffModel",
                        "role": role
                    },
                    "resource": {
                        "opaType": resource
                    }
                },
                {
                    "index": 2,
                    "action": "count",
                    "subject": {
                        "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                        "opaType": "StaffModel",
                        "role": role
                    },
                    "resource": {
                        "opaType": resource
                    }
                }
            ]}
        }
    }
}

test_wanda_bff_user_view_count_create_update_models_allowed if {
    resources = [
       "PersonHealthEvent",
       "WandaComment"
    ]

    roles = [
      "CHIEF_RISK",
      "ON_SITE_PHYSICIAN",
      "MANAGER_PHYSICIAN",
      "MED_RISK"
    ]

    every resource in resources {
        every role in roles {
            {1,2,3,4} == wanda_bff_api.allow with input as {"cases": [
                {
                    "index": 1,
                    "action": "view",
                    "subject": {
                        "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                        "opaType": "StaffModel",
                        "role": role
                    },
                    "resource": {
                        "opaType": resource
                    }
                },
                {
                    "index": 2,
                    "action": "count",
                    "subject": {
                        "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                        "opaType": "StaffModel",
                        "role": role
                    },
                    "resource": {
                        "opaType": resource
                    }
                },
                {
                    "index": 3,
                    "action": "create",
                    "subject": {
                        "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                        "opaType": "StaffModel",
                        "role": role
                    },
                    "resource": {
                        "opaType": resource
                    }
                },
                {
                    "index": 4,
                    "action": "update",
                    "subject": {
                        "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                        "opaType": "StaffModel",
                        "role": role
                    },
                    "resource": {
                        "opaType": resource
                    }
                }
            ]}
        }
    }
}

test_care_coord_and_pops_view_models_allowed if {
    resources = [
        "CaseRecord",
        "HealthCondition",
        "HealthConditionAxis",
        "HealthConditionRelated",
        "Risk",
        "RiskCalculationConf",
        "StaffModel"
    ]

    roles = [
      "CARE_COORD_NURSE",
      "MANAGER_PHYSICIAN",
      "DIGITAL_CARE_PHYSICIAN",
      "HEALTHCARE_TEAM_NURSE",
      "CHIEF_RISK"
    ]

    every resource in resources {
        every role in roles {
            {1} == wanda_bff_api.allow with input as {"cases": [
                {
                    "index": 1,
                    "action": "view",
                    "subject": {
                        "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                        "opaType": "StaffModel",
                        "role": role
                    },
                    "resource": {
                        "opaType": resource
                    }
                }
            ]}
        }
    }
}

test_care_coord_and_pops_count_models_allowed if {
    resources = [
        "CaseRecord",
        "HealthCondition",
        "HealthConditionAxis",
        "HealthConditionRelated",
        "Risk",
        "RiskCalculationConf",
        "StaffModel"
    ]

    roles = [
      "CARE_COORD_NURSE",
      "MANAGER_PHYSICIAN",
      "DIGITAL_CARE_PHYSICIAN",
      "HEALTHCARE_TEAM_NURSE",
      "CHIEF_RISK"
    ]

    every resource in resources {
        every role in roles {
            {1} == wanda_bff_api.allow with input as {"cases": [
                {
                    "index": 1,
                    "action": "count",
                    "subject": {
                        "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                        "opaType": "StaffModel",
                        "role": role
                    },
                    "resource": {
                        "opaType": resource
                    }
                }
            ]}
        }
    }
}

test_care_coord_and_pops_create_models_allowed if {
    resources = [
        "Risk"
    ]

    roles = [
      "CARE_COORD_NURSE",
      "MANAGER_PHYSICIAN",
      "DIGITAL_CARE_PHYSICIAN",
      "HEALTHCARE_TEAM_NURSE",
      "CHIEF_RISK"
    ]

    every resource in resources {
        every role in roles {
            {1} == wanda_bff_api.allow with input as {"cases": [
                {
                    "index": 1,
                    "action": "create",
                    "subject": {
                        "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                        "opaType": "StaffModel",
                        "role": role
                    },
                    "resource": {
                        "opaType": resource
                    }
                }
            ]}
        }
    }
}

test_insurance_ops_health_institution_ops_view_models_allowed if {
    resources = [
        "PersonClinicalAccount",
        "PersonModel"
    ]

    every resource in resources {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"
                },
                "resource": {
                    "opaType": resource
                }
            }
        ]}
    }
}

test_insurance_ops_health_institution_ops_count_models_allowed if {
    resources = [
        "PersonHealthEvent"
    ]

    every resource in resources {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"
                },
                "resource": {
                    "opaType": resource
                }
            }
        ]}
    }
}

test_insurance_ops_health_institution_ops_view_create_update_models_allowed if {
    resources = [
        "PersonHealthEvent"
    ]

    categories = [
        "COMMUNITY_REQUEST",
        "APPOINTMENT_SCHEDULE_HEALTH_COMMUNITY",
        "APPOINTMENT_COMMUNITY"
    ]

    every resource in resources {
        every category in categories {
            {1,2,3} == wanda_bff_api.allow with input as {"cases": [
                {
                    "index": 1,
                    "action": "view",
                    "subject": {
                        "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                        "opaType": "StaffModel",
                        "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"
                    },
                    "resource": {
                        "opaType": resource,
                        "category": category
                    }
                },
                {
                    "index": 2,
                    "action": "create",
                    "subject": {
                        "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                        "opaType": "StaffModel",
                        "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"
                    },
                    "resource": {
                        "opaType": resource,
                        "category": category
                    }
                },
                {
                    "index": 3,
                    "action": "update",
                    "subject": {
                        "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                        "opaType": "StaffModel",
                        "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"
                    },
                    "resource": {
                        "opaType": resource,
                        "category": category
                    }
                },
            ]}
        }
    }
}

test_insurance_ops_health_institution_ops_view_create_update_models_not_allowed if {
    resources = [
        "PersonHealthEvent"
    ]

    every resource in resources {
        not {1,2,3} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"
                },
                "resource": {
                    "opaType": resource,
                    "category": "APPOINTMENT_FOLLOW_UP"
                }
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"
                },
                "resource": {
                    "opaType": resource,
                    "category": "APPOINTMENT_FOLLOW_UP"
                }
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"
                },
                "resource": {
                    "opaType": resource,
                    "category": "APPOINTMENT_FOLLOW_UP"
                }
            }
        ]}
    }
}

test_cx_ops_view_models_allowed if {
    resources = [
        "MemberModel",
        "PersonClinicalAccount",
        "PersonModel"
    ]

    every resource in resources {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "CX_OPS"
                },
                "resource": {
                    "opaType": resource
                }
            }
        ]}
    }
}

test_insurance_ops_health_institution_ops_count_models_allowed if {
    resources = [
        "PersonHealthEvent"
    ]

    every resource in resources {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "CX_OPS"
                },
                "resource": {
                    "opaType": resource
                }
            }
        ]}
    }
}

test_cx_ops_view_create_update_models_allowed if {
    resources = [
        "PersonHealthEvent"
    ]

    categories = [
        "ADM_ATTENDANCE",
        "ALICE_AT_HOME",
        "INTERNAL_TASK",
        "REFUND_REQUESTED"
    ]

    every resource in resources {
        every category in categories {
            {1,2,3} == wanda_bff_api.allow with input as {"cases": [
                {
                    "index": 1,
                    "action": "view",
                    "subject": {
                        "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                        "opaType": "StaffModel",
                        "role": "CX_OPS"
                    },
                    "resource": {
                        "opaType": resource,
                        "category": category
                    }
                },
                {
                    "index": 2,
                    "action": "create",
                    "subject": {
                        "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                        "opaType": "StaffModel",
                        "role": "CX_OPS"
                    },
                    "resource": {
                        "opaType": resource,
                        "category": category
                    }
                },
                {
                    "index": 3,
                    "action": "update",
                    "subject": {
                        "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                        "opaType": "StaffModel",
                        "role": "CX_OPS"
                    },
                    "resource": {
                        "opaType": resource,
                        "category": category
                    }
                },
            ]}
        }
    }
}

test_cx_ops_view_create_update_models_not_allowed if {
    resources = [
        "PersonHealthEvent"
    ]

    every resource in resources {
        not {1,2,3} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "CX_OPS"
                },
                "resource": {
                    "opaType": resource,
                    "category": "APPOINTMENT_FOLLOW_UP"
                }
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "CX_OPS"
                },
                "resource": {
                    "opaType": resource,
                    "category": "APPOINTMENT_FOLLOW_UP"
                }
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "CX_OPS"
                },
                "resource": {
                    "opaType": resource,
                    "category": "APPOINTMENT_FOLLOW_UP"
                }
            }
        ]}
    }
}


test_technique_nurse_view_models_allowed_pt1 if {
    resources = [
        "ActionPlanTask",
        "AppContentScreenDetail",
        "Appointment",
        "AppointmentCoordination",
        "AppointmentEvent",
        "AppointmentEvolution",
        "AssistanceCare",
        "ChannelComment",
        "ChannelHistory",
        "ChannelTheme",
        "ClinicalBackground",
        "ConsolidatedRewardsModel",
        "ConsentRegistration",
        "DasaDiagnosticReport",
        "DischargeSummary",
        "EinsteinAlergia",
        "EinsteinAtendimento",
        "EinsteinAvaliacaoInicial",
        "EinsteinDadosDeAlta",
        "EinsteinDiagnostico",
        "EinsteinEncaminhamento",
        "EinsteinMedicamento",
        "EinsteinProcedimento",
        "EinsteinResultadoExame",
        "EinsteinResumoInternacao",
        "EinsteinStructuredTestResult",
        "ExternalReferralModel",
        "FhirBundle",
        "FhirDiagnosticReport",
        "FhirDocument"
    ]

    every resource in resources {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": resource
                }
            }
        ]}
    }
}

test_technique_nurse_view_models_allowed_pt2 if {
    resources = [
        "GuiaModel",
        "HDataOverview",
        "HLActionRecommendation",
        "HLAdherence",
        "HaocFhirProcess",
        "HealthLogicRecord",
        "HealthMeasurementModel",
        "HealthcareMap",
        "IntentionCoordination",
        "LaboratoryTestResultModel",
        "MemberModel",
        "PersonClinicalAccount",
        "PersonEligibilityDuquesa",
        "PersonHealthEvent",
        "PersonHealthGoalModel",
        "PersonHealthLogic",
        "PersonHealthcareTeamRecommendationModel",
        "PersonModel",
        "PersonTeamAssociation",
        "PregnancyModel",
        "TertiaryIntentionTouchPoint",
        "TestResultFeedback",
        "TestResultFileModel",
        "Timeline",
        "VideoCall",
        "WandaComment"
    ]

    every resource in resources {
        {1} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": resource
                }
            }
        ]}
    }
}

test_technique_nurse_view_count_create_update_models_allowed if {
    resources = [
        "PersonHealthEvent"
    ]

    every resource in resources {
        {1,2,3,4} == wanda_bff_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": resource
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": resource
                }
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": resource
                }
            },
            {
                "index": 4,
                "action": "update",
                "subject": {
                    "id": "48a57f8b-33ce-40cf-aa81-d5ec6e0ebb00",
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": resource
                }
            },
        ]}
    }
}
