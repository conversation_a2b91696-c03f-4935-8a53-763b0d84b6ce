package br.com.alice.api.backoffice.mappers.execIndicator

import br.com.alice.api.backoffice.ServiceConfig
import br.com.alice.api.backoffice.mappers.execIndicator.ResourceSpecialistPricingOutputMapper.toPaginatedResponse
import br.com.alice.common.core.Role
import br.com.alice.common.core.extensions.toCustomFormat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CSVPricingUpdateError
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdateStatus
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyPricingUpdateHistoryItem
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyPricingUpdateHistoryWithCount
import io.ktor.http.Parameters
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test

class ResourceSpecialistPricingOutputMapperTest {

    private val staffId = UUID.randomUUID()
    private val staff = TestModelFactory.buildStaff(
        id = staffId,
        firstName = "Fernanda",
        lastName = "Silva",
        role = Role.INSURANCE_OPS_HEALTH_INSTITUTION_OPS
    )

    private val historyItem = ResourceBundleSpecialtyPricingUpdateHistoryItem(
        id = UUID.randomUUID(),
        fileName = "preços_2024-12-01",
        fileUrl = "https://example.com/file.csv",
        createdByStaff = staff,
        processingAt = LocalDateTime.now().minusMinutes(10),
        completedAt = LocalDateTime.now(),
        rowsCount = 100,
        failedRowsCount = 10,
        failedRowsErrors = listOf(
            CSVPricingUpdateError(
                row = 1,
                error = "Invalid price format"
            )
        ),
        parsingError = null,
        pricesBeginAt = LocalDate.of(2024, 12, 1),
        createdAt = LocalDateTime.now().minusMinutes(15)
    )

    private val historyWithCount = ResourceBundleSpecialtyPricingUpdateHistoryWithCount(
        count = 1,
        items = listOf(historyItem)
    )

    @Test
    fun `toPaginatedResponse returns ListPaginatedResponse with ResourceSpecialistPricingIndexResponse`() {
        val params = Parameters.build {
            append("page", "1")
            append("pageSize", "10")
        }

        val result = historyWithCount.toPaginatedResponse(params)

        // Verify pagination
        assertThat(result.pagination.totalPages).isEqualTo(1)
        assertThat(result.pagination.page).isEqualTo(1)
        assertThat(result.pagination.pageSize).isEqualTo(10)

        // Verify results count
        assertThat(result.results).hasSize(1)

        // Verify content
        val firstResult = result.results.first()
        assertThat(firstResult.id).isEqualTo(historyItem.id)
        assertThat(firstResult.filename).isEqualTo(historyItem.fileName)
        assertThat(firstResult.downloadFileUrl).isEqualTo(historyItem.fileUrl)
        assertThat(firstResult.downloadFailedLinesFileUrl).isEqualTo("${ServiceConfig.baseUrl}/resourceBundleSpecialtyPricingUpdate/${historyItem.id}/failedLines")
        assertThat(firstResult.createdAt).isEqualTo(historyItem.createdAt.toCustomFormat("dd/MM/yyyy | HH:mm:ss"))
        assertThat(firstResult.createdBy).isEqualTo("Fernanda Silva")

        // Verify status
        assertThat(firstResult.status.value).isEqualTo(ResourceBundleSpecialtyPricingUpdateStatus.PROCESSED_WITH_ERRORS)
        assertThat(firstResult.status.friendlyName).isEqualTo("Possui dados incorretos")

        // Verify processing details
        assertThat(firstResult.processingDetails.totalItems).isEqualTo(100)
        assertThat(firstResult.processingDetails.errorItems).isEqualTo(10)
        assertThat(firstResult.processingDetails.friendlyDescription).isEqualTo("90/100 linhas cadastradas")
    }

    @Test
    fun `toPaginatedResponse handles parsing error status correctly`() {
        val params = Parameters.build {
            append("page", "1")
            append("pageSize", "10")
        }

        val itemWithParsingError = historyItem.copy(
            parsingError = "Invalid CSV format",
            completedAt = null
        )

        val historyWithParsingError = ResourceBundleSpecialtyPricingUpdateHistoryWithCount(
            count = 1,
            items = listOf(itemWithParsingError)
        )

        val result = historyWithParsingError.toPaginatedResponse(params)
        val firstResult = result.results.first()

        // Verify status for parsing error
        assertThat(firstResult.status.value).isEqualTo(ResourceBundleSpecialtyPricingUpdateStatus.PARSING_ERROR)
        assertThat(firstResult.status.friendlyName).isEqualTo("Falha na leitura")

        // Verify processing details for parsing error
        assertThat(firstResult.processingDetails.friendlyDescription).isEqualTo("Arquivo inválido")
    }

    @Test
    fun `toPaginatedResponse handles processing status correctly`() {
        val params = Parameters.build {
            append("page", "1")
            append("pageSize", "10")
        }

        val processingItem = historyItem.copy(
            completedAt = null
        )

        val historyWithProcessingItem = ResourceBundleSpecialtyPricingUpdateHistoryWithCount(
            count = 1,
            items = listOf(processingItem)
        )

        val result = historyWithProcessingItem.toPaginatedResponse(params)
        val firstResult = result.results.first()

        // Verify status for processing
        assertThat(firstResult.status.value).isEqualTo(ResourceBundleSpecialtyPricingUpdateStatus.PROCESSING)
        assertThat(firstResult.status.friendlyName).isEqualTo("Processando")
    }

    @Test
    fun `toPaginatedResponse handles successful processing correctly`() {
        val params = Parameters.build {
            append("page", "1")
            append("pageSize", "10")
        }

        val successItem = historyItem.copy(
            failedRowsCount = 0,
            failedRowsErrors = emptyList()
        )

        val historyWithSuccessItem = ResourceBundleSpecialtyPricingUpdateHistoryWithCount(
            count = 1,
            items = listOf(successItem)
        )

        val result = historyWithSuccessItem.toPaginatedResponse(params)
        val firstResult = result.results.first()

        // Verify status for successful processing
        assertThat(firstResult.status.value).isEqualTo(ResourceBundleSpecialtyPricingUpdateStatus.PROCESSED)
        assertThat(firstResult.status.friendlyName).isEqualTo("Processado com sucesso")

        // Verify processing details for successful processing
        assertThat(firstResult.processingDetails.friendlyDescription).isEqualTo("100/100 linhas cadastradas")
        assertThat(firstResult.downloadFailedLinesFileUrl).isNull()
    }
}
