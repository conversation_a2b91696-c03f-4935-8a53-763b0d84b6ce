package br.com.alice.tiss.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.events.GuiaWithChemotherapyCreatedEvent
import br.com.alice.exec.indicator.events.GuiaWithChemotherapyCreatedPayload
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.exec.indicator.models.RequesterData
import br.com.alice.tiss.consumers.converters.GuiaWithChemotherapyCreatedRequestBuilder
import br.com.alice.tiss.models.AttachmentHeader
import br.com.alice.tiss.models.BeneficiaryAdditionalData
import br.com.alice.tiss.models.ChemotherapyOncologicalDiagnosis
import br.com.alice.tiss.models.ChemotherapyRequestAttachment
import br.com.alice.tiss.models.CreateGuiaRequest
import br.com.alice.tiss.models.OncologicalDiagnosis
import br.com.alice.tiss.models.RequestedDrug
import br.com.alice.tiss.models.RequestedDrugs
import br.com.alice.tiss.services.internal.GuiaWithAttachmentService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GuiaWithChemotherapyCreatedConsumerTest : ConsumerTest() {

    private val guiaWithChemotherapyCreatedRequestBuilder: GuiaWithChemotherapyCreatedRequestBuilder = mockk()
    private val guiaWithAttachmentService: GuiaWithAttachmentService = mockk()
    private val totvsGuiaService: TotvsGuiaService = mockk()

    private val consumer = GuiaWithChemotherapyCreatedConsumer(
        guiaWithChemotherapyCreatedRequestBuilder,
        guiaWithAttachmentService,
        totvsGuiaService
    )

    private val person = TestModelFactory.buildPerson()
    private val attachment = TestModelFactory.buildAttachmentChemotherapy()
    private val totvsGuia = TestModelFactory.buildTotvsGuia()
    private val chemotherapyTotvsGuia = TestModelFactory.buildTotvsGuia()

    private val payload = GuiaWithChemotherapyCreatedPayload(
        attachment = attachment,
        totvsGuia = totvsGuia,
        chemotherapyTotvsGuia = chemotherapyTotvsGuia,
        beneficiary = Beneficiary(nationalId = "12345678900", newBornAttendance = false, name = "Batata frita"),
        origin = GuiaOrigin.EITA,
        person = person,
        referenceGuiaProcedures = emptyList(),
    )

    private val chemotherapyRequestAttachment = ChemotherapyRequestAttachment(
        providerCnpj = "",
        origin = GuiaOrigin.EITA,
        attachmentHeader = AttachmentHeader(
            ansCode = "1234",
            attachmentGuideNumber = "************",
            referencedGuideNumber = "123",
            requestDate = LocalDate.now()
        ),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE,
            phoneNumber = "(11) 1111-1111",
            email = "<EMAIL>"
        ),
        beneficiaryData = Beneficiary(nationalId = "12345678900", newBornAttendance = false, name = "Batata frita"),
        beneficiaryAdditionalData = BeneficiaryAdditionalData(
            weight = 70.toBigDecimal(),
            height = 1.7.toBigDecimal(),
            bodySurface = 0.18.toBigDecimal(),
            age = 60.toBigInteger(),
            sex = "1",
        ),
        chemotherapyOncologicalDiagnosis = ChemotherapyOncologicalDiagnosis(
            chemoDiagnosis = OncologicalDiagnosis(
                diagnosisDate = LocalDate.now(),
                diagnosisCID = null,
                local = "0",
                purpose = "1",
                ecog = "0",
                relevantInfo = null
            ),
            tumor = "1",
            nodule = "1",
            metastasis = "1",
            chemotherapyType = "1",
            therapeuticPlan = "NO DESCRIPTION"
        ),
        requestedDrugs = RequestedDrugs(listOf(
            RequestedDrug(
                probableDate = LocalDate.now(),
                identification = GuiaProcedure(
                    table = "123",
                    code = "456",
                    description = "droga01",
                    quantity = 1,
                    status = MvAuthorizedProcedureStatus.PENDING
                ),
                dosesQuantity = 1.0,
                unitMeasure = "01",
                administrationRoute = "01",
                frequency = 1.toBigInteger()
            )
        )),
        cyclesNumber = 1.toBigInteger(),
        currentCycle = 1.toBigInteger(),
        currentCycleDays = 1.toBigInteger(),
        cyclesInterval = 1.toBigInteger(),
        observation = null
    )

    private val createGuiaRequest = CreateGuiaRequest(
        procedures = listOf(
            GuiaProcedure(table = "98", code = "123", quantity = 1, description = ""),
            GuiaProcedure(table = "98", code = "545", quantity = 1, description = "")
        ),
        requester = RequesterData(name = "Alice Operadora", providerCode = "00000000000"),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE,
            phoneNumber = "123",
            email = "<EMAIL>"
        ),
        guiaNumber = "************",
        requestDate = LocalDate.now(),
        beneficiary = Beneficiary(nationalId = "12345678900", newBornAttendance = false, name = "Batata frita"),
        ansCode = "1234",
        authorizationType = "1",
        attendanceCharacter = "1",
        origin = GuiaOrigin.EITA,
        attendanceRegime = "1",
        accidentIndication = "1",
    )

    private val event = GuiaWithChemotherapyCreatedEvent(payload)

    @AfterTest
    fun setup() = clearAllMocks()

    @Test
    fun `#handle GuiaWithChemotherapyCreatedEvent should process event`() = runBlocking {
        coEvery { guiaWithChemotherapyCreatedRequestBuilder.build(
            attachment = attachment,
            totvsGuia = totvsGuia,
            chemotherapyTotvsGuia = chemotherapyTotvsGuia,
            beneficiary = payload.beneficiary,
            origin = payload.origin,
            person = person,
            referenceGuiaProcedures = emptyList(),
        ) } returns Pair(chemotherapyRequestAttachment, createGuiaRequest).success()

        coEvery { guiaWithAttachmentService.createGuiaWithChemotherapy(
            createGuiaRequest,
            chemotherapyRequestAttachment
        ) } returns true.success()

        coEvery {
            totvsGuiaService.get(totvsGuia.id)
        } returns totvsGuia.success()

        val result = consumer.handle(event)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { guiaWithAttachmentService.createGuiaWithChemotherapy(any(), any()) }
    }

    @Test
    fun `#handle GuiaWithChemotherapyCreatedEvent should not process event when totvsGuia already has external code`() = runBlocking {
        coEvery { guiaWithChemotherapyCreatedRequestBuilder.build(
            attachment = attachment,
            totvsGuia = totvsGuia,
            chemotherapyTotvsGuia = chemotherapyTotvsGuia,
            beneficiary = payload.beneficiary,
            origin = payload.origin,
            person = person,
            referenceGuiaProcedures = emptyList(),
        ) } returns Pair(chemotherapyRequestAttachment, createGuiaRequest).success()

        coEvery { guiaWithAttachmentService.createGuiaWithChemotherapy(
            createGuiaRequest,
            chemotherapyRequestAttachment
        ) } returns true.success()

        coEvery {
            totvsGuiaService.get(totvsGuia.id)
        } returns totvsGuia.copy(externalCode = "hasExternalCode").success()

        val result = consumer.handle(event)

        ResultAssert.assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyNone { guiaWithAttachmentService.createGuiaWithChemotherapy(any(), any()) }
    }
}
