package br.com.alice.data.layer.pipelines.subjects

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import org.junit.jupiter.api.Assertions.assertEquals
import kotlin.test.Test

class CareCoordinationSubjectTest {

    @Test
    fun `should create a CareCoordinationSubject from StaffModel`() {
        val id = RangeUUID.generate()
        val role = Role.CARE_COORD_NURSE
        val staffModel = DataLayerTestModelFactory.buildStaff(id = id, role = role)

        val careCoordinationSubject = CareCoordinationSubject(staffModel)

        assertEquals(id, careCoordinationSubject.id)
        assertEquals(role, careCoordinationSubject.role)
    }
}
