package br.com.alice.member.api.controllers

import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.Tag
import br.com.alice.app.content.model.TagColorScheme
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.coverage.br.com.alice.coverage.converter.HealthProfessionalConsolidatedLightConverter.toConsolidatedType
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.builders.FavoriteInfoTransportBuilder
import br.com.alice.member.api.models.ContactCallOutResponse
import br.com.alice.member.api.models.accreditedNetwork.AccreditedNetworkProfileTransport
import br.com.alice.member.api.models.accreditedNetwork.HeaderTransport
import br.com.alice.member.api.models.accreditedNetwork.ProviderDetailsTransport
import br.com.alice.member.api.models.accreditedNetwork.ProviderResponse
import br.com.alice.member.api.models.accreditedNetwork.ProviderTransport
import br.com.alice.member.api.models.accreditedNetwork.ProviderUnitRequestType
import br.com.alice.member.api.models.accreditedNetwork.SpecialistDetailsTransport
import br.com.alice.member.api.models.accreditedNetwork.TabBarTransport
import br.com.alice.member.api.models.appContent.Navigation
import br.com.alice.member.api.services.AccreditedNetworkInternalService
import br.com.alice.member.api.services.AccreditedNetworkSpecialistInternalService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import java.time.LocalDate
import kotlin.test.BeforeTest
import kotlin.test.Test

class AccreditedNetworkProviderControllerTest : RoutesTestHelper() {
    private val person = TestModelFactory.buildPerson()
    private val token = person.id.toString()
    private val accreditedProvidersNetworkService: AccreditedNetworkInternalService = mockk()
    private val accreditedNetworkSpecialistInternalService: AccreditedNetworkSpecialistInternalService = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            AccreditedNetworkProviderController(
                accreditedProvidersNetworkService,
                accreditedNetworkSpecialistInternalService
            )
        }
    }

    private val lat = "-23.5718116"
    private val lng = "-46.69273700000001"
    private val type = ProviderUnitRequestType.HOSPITAL
    private val structuredAddress = TestModelFactory.buildStructuredAddress()
    private val appVersion = SemanticVersion("1.0.0")

    private val providerUnit =
        TestModelFactory.buildProviderUnit(name = "Hospital 1", type = ProviderUnit.Type.HOSPITAL)
            .copy(address = structuredAddress)
    private val firstTag = Tag(
        text = "Mais próximo de você",
        colorScheme = TagColorScheme.MAGENTA
    )
    private val distance = 1000.00
    private val deAccreditationDate = LocalDate.of(2025, 1, 1)
    private val deAccreditationTag = Tag(
        text = "Disponível até 01/01",
        colorScheme = TagColorScheme.BLUE
    )

    @Nested
    inner class GetUnitByType {
        @Test
        fun `#Should return ok with providers`() {
            val expectedResponse = ProviderResponse(
                action = null,
                title = "",
                otherSectionTitle = "",
                items = listOf(
                    ProviderTransport(
                        id = providerUnit.id,
                        name = providerUnit.name,
                        title = providerUnit.name,
                        subtitle = null,
                        description = providerUnit.address!!.formattedAddress(),
                        tag = firstTag,
                        imageUrl = providerUnit.imageUrl,
                        type = ConsolidatedAccreditedNetworkType.HOSPITAL,
                        navigation = Navigation(
                            method = RemoteActionMethod.GET,
                            endpoint = ""
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = null,
                    )
                ),
                isDuquesa = false,
                emptyContent = ProviderResponse.EmptyContentTransport(),
            )
            coEvery {
                accreditedProvidersNetworkService.getUnitsByTypes(
                    person.id,
                    listOf(type),
                    lat,
                    lng,
                    appVersion
                )
            } returns
                    expectedResponse.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/provider_units?lat=$lat&lng=$lng&type=$type") { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }
        }

        @Test
        fun `#Should return ok with de accreditation tag`() {
            val expectedResponse = ProviderResponse(
                action = null,
                title = "",
                otherSectionTitle = "",
                items = listOf(
                    ProviderTransport(
                        id = providerUnit.id,
                        name = providerUnit.name,
                        title = providerUnit.name,
                        subtitle = null,
                        description = providerUnit.address!!.formattedAddress(),
                        tag = deAccreditationTag,
                        imageUrl = providerUnit.imageUrl,
                        type = ConsolidatedAccreditedNetworkType.HOSPITAL,
                        navigation = Navigation(
                            method = RemoteActionMethod.GET,
                            endpoint = ""
                        ),
                        distance = distance,
                        deAccreditationDate = deAccreditationDate,
                        captions = null,
                    )
                ),
                isDuquesa = false,
                emptyContent = ProviderResponse.EmptyContentTransport(),
            )
            coEvery {
                accreditedProvidersNetworkService.getUnitsByTypes(
                    person.id,
                    listOf(type),
                    lat,
                    lng,
                    appVersion
                )
            } returns
                    expectedResponse.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/provider_units?lat=$lat&lng=$lng&type=$type") { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }
        }

        @Test
        fun `#Should return bad request when missing lat`() {
            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/provider_units?lng=$lng&type=$type") { response ->
                    assertThat(response).isBadRequestWithErrorCode("missing_params_exception")
                }
            }
        }

        @Test
        fun `#Should return bad request when missing lng`() {
            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/provider_units?lat=$lat&type=$type") { response ->
                    assertThat(response).isBadRequestWithErrorCode("missing_params_exception")
                }
            }
        }

        @Test
        fun `#Should return bad request when missing type`() {
            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/provider_units?lat=$lat&lng=$lng") { response ->
                    assertThat(response).isBadRequestWithErrorCode("missing_params_exception")
                }
            }
        }
    }

    @Nested
    inner class GetHealthSpecialists {
        private val specialtyId = RangeUUID.generate()

        @Test
        fun `#Should return ok with health specialists`() {
            val expectedResponse = ProviderResponse(
                action = null,
                title = "",
                otherSectionTitle = "",
                items = listOf(
                    ProviderTransport(
                        id = providerUnit.id,
                        name = providerUnit.name,
                        title = providerUnit.name,
                        subtitle = null,
                        description = providerUnit.address!!.formattedAddress(),
                        tag = firstTag,
                        imageUrl = providerUnit.imageUrl,
                        type = ConsolidatedAccreditedNetworkType.HOSPITAL,
                        navigation = Navigation(
                            method = RemoteActionMethod.GET,
                            endpoint = ""
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = null,
                    )
                ),
                isDuquesa = false,
                emptyContent = ProviderResponse.EmptyContentTransport(),
            )
            coEvery {
                accreditedProvidersNetworkService.getSpecialists(
                    person.id,
                    specialtyId,
                    null,
                    null,
                    lat,
                    lng,
                    appVersion
                )
            } returns expectedResponse.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/health_specialists?specialty_id=${specialtyId}&lat=$lat&lng=$lng") { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }
        }

        @Test
        fun `#Should return bad request when missing lat`() {
            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/health_specialists?id=${specialtyId}&lng=$lng") { response ->
                    assertThat(response).isBadRequestWithErrorCode("missing_params_exception")
                }
            }
        }

        @Test
        fun `#Should return bad request when missing lng`() {
            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/health_specialists?id=${specialtyId}&lat=$lat") { response ->
                    assertThat(response).isBadRequestWithErrorCode("missing_params_exception")
                }
            }
        }

        @Test
        fun `#Should return bad request when missing specialtyId`() {
            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/health_specialists?lat=$lat&lng=$lng") { response ->
                    assertThat(response).isBadRequestWithErrorCode("missing_params_exception")
                }
            }
        }
    }

    @Nested
    inner class GetMyHealthCareTeamPhysicianDetails {
        private val latitude = "-23.5718116"
        private val longitude = "-46.69273700000001"

        @Test
        fun `#Should return ok with details`() {
            val specialist = TestModelFactory.buildHealthProfessional()

            val expectedResponse = SpecialistDetailsTransport(
                id = specialist.id,
                providerType = ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL,
                title = ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL.title,
                name = "Mateo Abreu",
                imageUrl = "https://www.example.com.br/images/doctor.png",
                crm = "123456/BA",
                tag = Tag(
                    text = "Seu médico de família",
                    colorScheme = TagColorScheme.MAGENTA
                ),
                showMembershipCardShareButton = false,
                favoriteInfo = FavoriteInfoTransportBuilder.build(
                    referenceId = specialist.id,
                    specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                    referenceType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                    isFavorite = false,
                    appVersion = appVersion,
                )
            )

            coEvery {
                accreditedNetworkSpecialistInternalService.getMyHealthCareTeamSpecialist(
                    person.id,
                    latitude.toDouble(),
                    longitude.toDouble(),
                    appVersion
                )
            } returns expectedResponse.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/healthcare_team/physician?lat=${latitude}&lng=${longitude}") { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                    val content: ProviderDetailsTransport = response.bodyAsJson()
                    Assertions.assertThat(content.therapySpecialtyDisclaimer).isEqualTo(content.disclaimer)
                }
            }
        }

        @Test
        fun `Should return notFound when service returns CassiMemberNotFoundException`() {
            coEvery {
                accreditedNetworkSpecialistInternalService.getMyHealthCareTeamSpecialist(
                    person.id,
                    latitude.toDouble(),
                    longitude.toDouble(),
                    appVersion
                )
            } returns NotFoundException().failure()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/healthcare_team/physician?lat=${latitude}&lng=${longitude}") { response ->
                    assertThat(response).isNotFound()
                }
            }
        }

        @Test
        fun `Should return Temporary Redirect`() {
            coEvery {
                accreditedNetworkSpecialistInternalService.getMyHealthCareTeamSpecialist(
                    person.id,
                    latitude.toDouble(),
                    longitude.toDouble(),
                    appVersion
                )
            } returns UnsupportedOperationException().failure()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/healthcare_team/physician?lat=${latitude}&lng=${longitude}") { response ->
                    assertThat(response).isTemporaryRedirectTo("${ServiceConfig.baseUrl}/app_content/screen/empty_mfc")
                }
            }
        }

        @Test
        fun `Should return ok with details without lat and lng`() {
            val specialist = TestModelFactory.buildHealthProfessional()

            val expectedResponse = SpecialistDetailsTransport(
                id = specialist.id,
                providerType = ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL,
                title = ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL.title,
                name = "Mateo Abreu",
                imageUrl = "https://www.example.com.br/images/doctor.png",
                crm = "123456/BA",
                tag = Tag(
                    text = "Seu médico de família",
                    colorScheme = TagColorScheme.MAGENTA
                ),
                showMembershipCardShareButton = false,
                favoriteInfo = FavoriteInfoTransportBuilder.build(
                    referenceId = specialist.id,
                    specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                    referenceType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                    isFavorite = false,
                    appVersion = appVersion,
                )
            )

            coEvery {
                accreditedNetworkSpecialistInternalService.getMyHealthCareTeamSpecialist(
                    person.id,
                    null,
                    null,
                    appVersion
                )
            } returns expectedResponse.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/healthcare_team/physician") { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                    val content: ProviderDetailsTransport = response.bodyAsJson()
                    Assertions.assertThat(content.therapySpecialtyDisclaimer).isEqualTo(content.disclaimer)
                }
            }
        }

    }

    @Nested
    inner class GetHealthCareTeamPhysicianDetails {
        private val physicianId = RangeUUID.generate()
        private val latitude = "-23.5718116"
        private val longitude = "-46.69273700000001"

        @Test
        fun `#Should return ok with details`() {
            val specialist = TestModelFactory.buildHealthProfessional()

            val expectedResponse = SpecialistDetailsTransport(
                id = specialist.id,
                providerType = ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL,
                title = ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL.title,
                name = "Mateo Abreu",
                imageUrl = "https://www.example.com.br/images/doctor.png",
                crm = "123456/BA",
                tag = Tag(
                    text = "Seu médico de família",
                    colorScheme = TagColorScheme.MAGENTA
                ),
                showMembershipCardShareButton = false,
                favoriteInfo = FavoriteInfoTransportBuilder.build(
                    referenceId = specialist.id,
                    specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                    referenceType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                    isFavorite = false,
                    appVersion = appVersion,
                )
            )

            coEvery {
                accreditedNetworkSpecialistInternalService.getHealthCareTeamSpecialist(
                    person.id,
                    physicianId,
                    latitude.toDouble(),
                    longitude.toDouble(),
                    appVersion
                )
            } returns expectedResponse.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/healthcare_team/physician/${physicianId}?lat=${latitude}&lng=${longitude}") { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                    val content: ProviderDetailsTransport = response.bodyAsJson()
                    Assertions.assertThat(content.therapySpecialtyDisclaimer).isEqualTo(content.disclaimer)
                }
            }
        }

        @Test
        fun `Should return notFound when service returns CassiMemberNotFoundException`() {
            coEvery {
                accreditedNetworkSpecialistInternalService.getHealthCareTeamSpecialist(
                    person.id,
                    physicianId,
                    latitude.toDouble(),
                    longitude.toDouble(),
                    appVersion
                )
            } returns NotFoundException().failure()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/healthcare_team/physician/${physicianId}?lat=${latitude}&lng=${longitude}") { response ->
                    assertThat(response).isNotFound()
                }
            }
        }
    }

    @Nested
    inner class GetSpecialistDetails {
        private val physicianId = RangeUUID.generate()
        private val latitude = "-23.5718116"
        private val longitude = "-46.69273700000001"

        @Test
        fun `#Should return ok with specialist details`() {
            val specialist = TestModelFactory.buildHealthProfessional()

            val expectedResponse = SpecialistDetailsTransport(
                id = specialist.id,
                providerType = ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL,
                title = ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL.title,
                name = "Mateo Abreu",
                imageUrl = "https://www.example.com.br/images/doctor.png",
                crm = "123456/BA",
                tag = Tag(
                    text = "Seu médico de família",
                    colorScheme = TagColorScheme.MAGENTA
                ),
                showMembershipCardShareButton = false,
                favoriteInfo = FavoriteInfoTransportBuilder.build(
                    referenceId = specialist.id,
                    specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                    referenceType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                    isFavorite = false,
                    appVersion = appVersion,
                )
            )

            coEvery {
                accreditedNetworkSpecialistInternalService.getSpecialistDetails(
                    person.id,
                    physicianId,
                    appVersion,
                    latitude.toDouble(),
                    longitude.toDouble(),
                    null
                )
            } returns expectedResponse.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/specialists/${physicianId}?lat=${latitude}&lng=${longitude}") { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                    val content: ProviderDetailsTransport = response.bodyAsJson()
                    Assertions.assertThat(content.therapySpecialtyDisclaimer).isEqualTo(content.disclaimer)
                }
            }
        }

        @Test
        fun `Should return notFound when service returns NotFoundException`() {
            coEvery {
                accreditedNetworkSpecialistInternalService.getSpecialistDetails(
                    person.id,
                    physicianId,
                    appVersion,
                    latitude.toDouble(),
                    longitude.toDouble(),
                    null
                )
            } returns NotFoundException().failure()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/specialists/${physicianId}?lat=${latitude}&lng=${longitude}") { response ->
                    assertThat(response).isNotFound()
                }
            }
        }

        @Test
        fun `Should return ok with specialist details without lat and lng`() {
            val specialist = TestModelFactory.buildHealthProfessional()

            val expectedResponse = SpecialistDetailsTransport(
                id = specialist.id,
                providerType = ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL,
                title = ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL.title,
                name = "Mateo Abreu",
                imageUrl = "https://www.example.com.br/images/doctor.png",
                crm = "123456/BA",
                tag = Tag(
                    text = "Seu médico de família",
                    colorScheme = TagColorScheme.MAGENTA
                ),
                showMembershipCardShareButton = false,
                favoriteInfo = FavoriteInfoTransportBuilder.build(
                    referenceId = specialist.id,
                    specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                    referenceType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                    isFavorite = false,
                    appVersion = appVersion,
                )
            )

            coEvery {
                accreditedNetworkSpecialistInternalService.getSpecialistDetails(
                    person.id,
                    physicianId,
                    appVersion,
                    null,
                    null,
                    null
                )
            } returns expectedResponse.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/specialists/${physicianId}") { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                    val content: ProviderDetailsTransport = response.bodyAsJson()
                    Assertions.assertThat(content.therapySpecialtyDisclaimer).isEqualTo(content.disclaimer)
                }
            }
        }
    }

    @Nested
    inner class GetProviderDetails {

        private val providerType = "SPECIALIST_HEALTH_PROFESSIONAL"
        private val providerId = RangeUUID.generate()
        private val latitude = "-23.5718116"
        private val longitude = "-46.69273700000001"

        @Test
        fun `#Should return ok with details`() {
            val specialist = TestModelFactory.buildHealthProfessional(type = StaffType.PARTNER_HEALTH_PROFESSIONAL)

            val expectedResponse = ProviderDetailsTransport(
                id = specialist.id,
                type = ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL,
                title = ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL.title,
                name = "name",
                header = ProviderDetailsTransport.Header(
                    name = specialist.name,
                    providerImageUrl = specialist.imageUrl,
                ),
                membershipCard = null,
                disclaimer = ContactCallOutResponse(
                    title = "É necessário o encaminhamento",
                    body = "Para agendar com essa especialidade, fale com Alice Agora",
                    variant = ContactCallOutResponse.Variant.WARNING
                ),
                addresses = emptyList(),
                phoneNumbers = emptyList(),
                typeOfService = null,
                providerInformation = null,
                action = null,
                careCoordCard = null,
                favoriteInfo = FavoriteInfoTransportBuilder.build(
                    referenceId = specialist.id,
                    specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                    referenceType = specialist.type.toConsolidatedType(),
                    isFavorite = false,
                    appVersion = appVersion,
                ),
            )


            coEvery {
                accreditedProvidersNetworkService.getProviderDetails(
                    person.id,
                    ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL,
                    providerId,
                    appVersion,
                    lat = latitude,
                    lng = longitude,
                    specialtyId = null,
                )
            } returns expectedResponse.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/providers/${providerType}/${providerId}?lat=${latitude}&lng=${longitude}") { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                    val content: ProviderDetailsTransport = response.bodyAsJson()
                    Assertions.assertThat(content.therapySpecialtyDisclaimer).isEqualTo(content.disclaimer)
                }
            }
        }

        @Test
        fun `#Should return notFound when service returns CassiMemberNotFoundException`() {
            val medicalSpecialtyId = RangeUUID.generate()
            coEvery {
                accreditedProvidersNetworkService.getProviderDetails(
                    person.id,
                    ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL,
                    providerId,
                    appVersion,
                    lat = latitude,
                    lng = longitude,
                    specialtyId = medicalSpecialtyId,
                )
            } returns NotFoundException().failure()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/providers/${providerType}/${providerId}?lat=${latitude}&lng=${longitude}&specialty_id=${medicalSpecialtyId}") { response ->
                    assertThat(response).isNotFound()
                }
            }
        }

    }

    @Nested
    inner class GetProviders {

        private val healthPlanTaskId = RangeUUID.generate()

        @Test
        fun `#Should return ok with list of providers`() {

            val expectedResponse = ProviderResponse(
                action = null,
                title = "",
                otherSectionTitle = "",
                items = listOf(
                    ProviderTransport(
                        id = providerUnit.id,
                        name = providerUnit.name,
                        title = providerUnit.name,
                        subtitle = null,
                        description = providerUnit.address!!.formattedAddress(),
                        tag = firstTag,
                        imageUrl = providerUnit.imageUrl,
                        type = ConsolidatedAccreditedNetworkType.HOSPITAL,
                        navigation = Navigation(
                            method = RemoteActionMethod.GET,
                            endpoint = ""
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = null,
                    )
                ),
                isDuquesa = false,
                emptyContent = ProviderResponse.EmptyContentTransport(),
            )


            coEvery {
                accreditedProvidersNetworkService.getProvidersByHealthPlanTask(
                    person.id,
                    healthPlanTaskId,
                    lat,
                    lng,
                    appVersion
                )
            } returns expectedResponse.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/providers?health_plan_task_id=${healthPlanTaskId}&lat=$lat&lng=$lng") { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }
        }


        @Test
        fun `#Should return bad request when missing health_plan_task_id`() {
            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/providers?lat=$lat&lng=$lng") { response ->
                    assertThat(response).isBadRequestWithErrorCode("missing_params_exception")
                }
            }
        }

        @Test
        fun `#Should return bad request when missing lat`() {
            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/health_specialists?health_plan_task_id=${healthPlanTaskId}&lng=$lng") { response ->
                    assertThat(response).isBadRequestWithErrorCode("missing_params_exception")
                }
            }
        }

        @Test
        fun `#Should return bad request when missing lng`() {
            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/health_specialists?health_plan_task_id=${healthPlanTaskId}&lat=$lat") { response ->
                    assertThat(response).isBadRequestWithErrorCode("missing_params_exception")
                }
            }
        }

    }

    @Nested
    inner class GetFavorites {
        @Test
        fun `#Should return ok with favorites`() {
            val expectedResponse = ProviderResponse(
                action = null,
                title = "Favoritos",
                otherSectionTitle = "Favoritos",
                items = listOf(
                    ProviderTransport(
                        id = providerUnit.id,
                        name = providerUnit.name,
                        title = providerUnit.name,
                        subtitle = null,
                        description = providerUnit.address!!.formattedAddress(),
                        tag = firstTag,
                        imageUrl = providerUnit.imageUrl,
                        type = ConsolidatedAccreditedNetworkType.HOSPITAL,
                        navigation = Navigation(
                            method = RemoteActionMethod.GET,
                            endpoint = ""
                        ),
                        distance = distance,
                        deAccreditationDate = null,
                        captions = null,
                    )
                ),
                isDuquesa = false,
                emptyContent = ProviderResponse.EmptyContentTransport(),
            )

            coEvery {
                accreditedProvidersNetworkService.getFavorites(
                    person.id,
                    lat,
                    lng,
                    appVersion
                )
            } returns expectedResponse.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/favorites?lat=$lat&lng=$lng") { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }
        }

        @Test
        fun `#Should return bad request when missing lat`() {
            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/favorites?lng=$lng") { response ->
                    assertThat(response).isBadRequestWithErrorCode("missing_params_exception")
                }
            }
        }

        @Test
        fun `#Should return bad request when missing lng`() {
            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/favorites?lat=$lat") { response ->
                    assertThat(response).isBadRequestWithErrorCode("missing_params_exception")
                }
            }
        }
    }

    @Nested
    inner class GetProfileDetails {
        private val providerId = RangeUUID.generate()
        private val latitude = "-23.5718116"
        private val longitude = "-46.69273700000001"

        @Test
        fun `#Should return profile details for health professional`() {
            val providerType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL.name
            val specialist = TestModelFactory.buildHealthProfessional()
            val specialistDetails = SpecialistDetailsTransport(
                id = specialist.id,
                providerType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                title = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL.title,
                name = "Mateo Abreu",
                imageUrl = "https://www.example.com.br/images/doctor.png",
                crm = "CRM 123456/BA",
                tag = Tag(
                    text = "Seu médico de família",
                    colorScheme = TagColorScheme.MAGENTA
                ),
                showMembershipCardShareButton = false,
                favoriteInfo = FavoriteInfoTransportBuilder.build(
                    referenceId = specialist.id,
                    specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                    referenceType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                    isFavorite = false,
                    appVersion = appVersion,
                )
            )

            val expectedProfileResponse = AccreditedNetworkProfileTransport(
                id = specialist.id,
                type = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                navbarTitle = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL.title,
                favoriteInfo = specialistDetails.favoriteInfo,
                header = HeaderTransport(
                    imageUrl = specialistDetails.imageUrl,
                    name = specialistDetails.name,
                    description = specialistDetails.crm,
                    tag = specialistDetails.tag
                ),
                tabBar = TabBarTransport(
                    selectedTabId = 1L,
                    tabs = emptyList(),
                )
            )

            coEvery {
                accreditedNetworkSpecialistInternalService.getSpecialistDetails(
                    personId = person.id,
                    specialistId = providerId,
                    appVersion = appVersion,
                    lat = latitude.toDouble(),
                    lng = longitude.toDouble(),
                    healthPlanTask = null,
                )
            } returns specialistDetails.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/profile/${providerId}?lat=${latitude}&lng=${longitude}&provider_type=${providerType}") { response ->
                    assertThat(response).isOKWithData(expectedProfileResponse)
                }
            }
        }

        @Test
        fun `#Should return profile details for hospital`() {
            val providerType = ConsolidatedAccreditedNetworkType.HOSPITAL.name
            val hospital = TestModelFactory.buildProviderUnit(name = "Test Hospital", type = ProviderUnit.Type.HOSPITAL)

            val providerDetails = ProviderDetailsTransport(
                id = hospital.id,
                type = ConsolidatedAccreditedNetworkType.HOSPITAL,
                title = ConsolidatedAccreditedNetworkType.HOSPITAL.title,
                name = "Test Hospital",
                header = ProviderDetailsTransport.Header(
                    name = hospital.name,
                    providerImageUrl = hospital.imageUrl,
                ),
                membershipCard = null,
                disclaimer = null,
                addresses = emptyList(),
                phoneNumbers = emptyList(),
                typeOfService = null,
                providerInformation = null,
                action = null,
                careCoordCard = null,
                favoriteInfo = FavoriteInfoTransportBuilder.build(
                    referenceId = hospital.id,
                    specialtyIds = emptyList(),
                    referenceType = ConsolidatedAccreditedNetworkType.HOSPITAL,
                    isFavorite = false,
                    appVersion = appVersion,
                ),
            )

            val expectedProfileResponse = AccreditedNetworkProfileTransport(
                id = hospital.id,
                type = ConsolidatedAccreditedNetworkType.HOSPITAL,
                navbarTitle = ConsolidatedAccreditedNetworkType.HOSPITAL.title,
                favoriteInfo = providerDetails.favoriteInfo,
                header = HeaderTransport(
                    imageUrl = hospital.imageUrl ?: "",
                    name = hospital.name,
                    description = null,
                    tag = null
                ),
                tabBar = TabBarTransport(
                    selectedTabId = 1L,
                    tabs = emptyList()
                )
            )

            coEvery {
                accreditedProvidersNetworkService.getProviderDetails(
                    person.id,
                    ConsolidatedAccreditedNetworkType.HOSPITAL,
                    providerId,
                    appVersion,
                    lat = latitude,
                    lng = longitude,
                    isExamRedirect = false,
                    specialtyId = null,
                )
            } returns providerDetails.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/profile/${providerId}?lat=${latitude}&lng=${longitude}&provider_type=${providerType}") { response ->
                    assertThat(response).isOKWithData(expectedProfileResponse)
                }
            }
        }

        @Test
        fun `#Should return bad request when missing provider_type parameter`() {
            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/profile/${providerId}?lat=${latitude}&lng=${longitude}") { response ->
                    assertThat(response).isBadRequestWithErrorCode("missing_params_exception")
                }
            }
        }

        @Test
        fun `#Should return bad request when missing lat parameter`() {
            val providerType = ConsolidatedAccreditedNetworkType.HOSPITAL.name

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/profile/${providerId}?lng=${longitude}&provider_type=${providerType}") { response ->
                    assertThat(response).isBadRequestWithErrorCode("missing_params_exception")
                }
            }
        }

        @Test
        fun `#Should return bad request when missing lng parameter`() {
            val providerType = ConsolidatedAccreditedNetworkType.HOSPITAL.name

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/profile/${providerId}?lat=${latitude}&provider_type=${providerType}") { response ->
                    assertThat(response).isBadRequestWithErrorCode("missing_params_exception")
                }
            }
        }

        @Test
        fun `#Should return not found when service returns NotFoundException for health professional`() {
            val providerType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL.name

            coEvery {
                accreditedNetworkSpecialistInternalService.getSpecialistDetails(
                    person.id,
                    providerId,
                    appVersion,
                    latitude.toDouble(),
                    longitude.toDouble(),
                    null
                )
            } returns NotFoundException().failure()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/profile/${providerId}?lat=${latitude}&lng=${longitude}&provider_type=${providerType}") { response ->
                    assertThat(response).isNotFound()
                }
            }
        }

        @Test
        fun `#Should return not found when service returns NotFoundException for hospital`() {
            val providerType = ConsolidatedAccreditedNetworkType.HOSPITAL.name

            coEvery {
                accreditedProvidersNetworkService.getProviderDetails(
                    person.id,
                    ConsolidatedAccreditedNetworkType.HOSPITAL,
                    providerId,
                    appVersion,
                    lat = latitude,
                    lng = longitude,
                    isExamRedirect = false,
                    specialtyId = null,
                )
            } returns NotFoundException().failure()

            authenticatedAs(token, toTestPerson(person)) {
                get("/accredited_network/profile/${providerId}?lat=${latitude}&lng=${longitude}&provider_type=${providerType}") { response ->
                    assertThat(response).isNotFound()
                }
            }
        }
    }
}
