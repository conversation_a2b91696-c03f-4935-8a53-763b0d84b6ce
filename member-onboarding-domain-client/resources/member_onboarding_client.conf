systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}

host = ${?MEMBER_ONBOARDING_DOMAIN_SERVICE_HOST}

baseUrl = "https://"${?host}""
memberApiUrl = "http://localhost:8824"

development {
  baseUrl = "http://localhost:8823"
  baseUrl = ${?host}
  memberApiUrl = ${?MEMBER_API_URL}
}

test {
  baseUrl = "http://localhost:9000"
  memberApiUrl = "http://localhost:8824"
}

production {
  baseUrl = ${baseUrl}
  memberApiUrl = ${?MEMBER_API_URL}
}
