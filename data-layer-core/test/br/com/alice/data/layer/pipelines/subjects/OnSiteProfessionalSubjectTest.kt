package br.com.alice.data.layer.pipelines.subjects

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import org.junit.jupiter.api.Assertions.assertEquals
import kotlin.test.Test

class OnSiteProfessionalSubjectTest {

    @Test
    fun `should create a OnSiteProfessionalSubject from StaffModel`() {
        val id = RangeUUID.generate()
        val role = Role.PSYCHOLOGIST
        val staffModel = DataLayerTestModelFactory.buildStaff(id = id, role = role)

        val onSiteProfessionalSubject = OnSiteProfessionalSubject(staffModel)

        assertEquals(id, onSiteProfessionalSubject.id)
        assertEquals(role, onSiteProfessionalSubject.role)
    }
}
