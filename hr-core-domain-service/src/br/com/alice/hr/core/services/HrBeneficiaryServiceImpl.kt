package br.com.alice.hr.core.services

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.core.exceptions.InternalServiceErrorException
import br.com.alice.common.core.PersonId
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.hr.core.client.HrBeneficiaryService
import br.com.alice.hr.core.model.DependentTransport
import br.com.alice.hr.core.model.TemplateSheetResult
import br.com.alice.hr.core.ServiceConfig
import br.com.alice.hr.core.services.useCases.CreateBeneficiaryDependentUseCase
import br.com.alice.hr.core.services.useCases.GetBeneficiaryRelationTypeOptionsUseCase
import br.com.alice.hr.core.clients.interfaces.AmazonS3Client
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID
import java.io.File

class HrBeneficiaryServiceImpl(
    private val beneficiaryService: BeneficiaryService,
    private val companyService: CompanyService,
    private val companySubContractService: CompanySubContractService,
    private val getBeneficiaryRelationTypeOptionsUseCase: GetBeneficiaryRelationTypeOptionsUseCase,
    private val createBeneficiaryDependentUseCase: CreateBeneficiaryDependentUseCase,
    private val amazonS3Client: AmazonS3Client
) : HrBeneficiaryService {

    companion object {
        val s3SheetFileName = "member_template.xlsx"
    }

    override suspend fun getRelationTypes(companyId: UUID, subcontractId: UUID): Result<List<ParentBeneficiaryRelationType>, Throwable> =
        companyService.get(companyId).flatMap { company ->
            companySubContractService.get(subcontractId).flatMap { subcontract ->
                getBeneficiaryRelationTypeOptionsUseCase.run(company, subcontract)
            }
        }

    override suspend fun createDependent(dependent: DependentTransport, companyId: UUID): Result<Beneficiary, Throwable> = span("createDependent") {
        companyService.get(companyId).flatMap { company ->
            companySubContractService.get(dependent.subcontractId).flatMap { subContract ->
                getBeneficiaryRelationTypeOptionsUseCase.run(company, subContract).flatMap { relationTypes ->
                    createBeneficiaryDependentUseCase.run(dependent, company, subContract, relationTypes)
                }
            }
        }.flatMapError {
            logger.error(
                "Error creating dependent",
                "error" to it,
                "parent_beneficiary_id" to dependent.parentBeneficiary,
                "company_id" to companyId,
                "subcontract_id" to dependent.subcontractId,
                "product_id" to dependent.productId,
                "parent_beneficiary_relation_type" to dependent.parentBeneficiaryRelationType
            )

            it.failure()
        }
    }

    override suspend fun getBeneficiarySheetTemplate(companyId: UUID): Result<TemplateSheetResult, Throwable> = span("getBeneficiarySheetTemplate") {
        val s3SheetPath = ServiceConfig.S3.templateSheetBucketUrl

        amazonS3Client.getS3ObjectContent(s3SheetPath).fold(
            success = { content ->
                TemplateSheetResult(content, s3SheetFileName).success()
            },
            failure = {
                logger.error(
                    "Error getting beneficiary sheet template",
                    "error" to it,
                    "company_id" to companyId
                )

                InternalServiceErrorException("Error getting beneficiary sheet template").failure()
            }
        )
    }
}
