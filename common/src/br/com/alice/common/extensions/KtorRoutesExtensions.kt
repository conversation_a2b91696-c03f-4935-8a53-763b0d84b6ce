package br.com.alice.common.extensions

import br.com.alice.common.coHandler
import br.com.alice.common.controllers.HealthCheckerController
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Route.setupHealthCheckRoutes() {
    route("/ops") {
        val healthCheckerController by inject<HealthCheckerController>()

        get("/live") { coHandler(healthCheckerController::isLive) }
        get("/ready") { co<PERSON>and<PERSON>(healthCheckerController::isReady) }
    }
}
