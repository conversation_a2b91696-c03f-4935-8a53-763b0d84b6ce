package br.com.alice.exec.indicator.controllers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.EventReference
import br.com.alice.data.layer.models.GuiaProcedures
import br.com.alice.data.layer.models.HealthEventLocationEnum
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.data.layer.services.GuiaWithProceduresModelDataService
import br.com.alice.data.layer.services.MvAuthorizedProcedureModelDataService
import br.com.alice.data.layer.services.TotvsGuiaModelDataService
import br.com.alice.eventinder.client.HealthEventsService
import br.com.alice.exec.indicator.client.EitaHealthEventService
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleManagementService
import br.com.alice.exec.indicator.client.HealthcareBundleService
import br.com.alice.exec.indicator.client.HealthcareResourceGroupAssociationService
import br.com.alice.exec.indicator.client.HealthcareResourceGroupService
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.client.TussProcedureSpecialtyService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePatchRequest
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePatchResponse
import br.com.alice.exec.indicator.models.SecondaryResourcesTransport
import br.com.alice.exec.indicator.service.internal.InternalMvAuthorizedProcedureService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Ignore
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class BackFillControllerTest : RecurrentControllerTestHelper() {

    private val eitaHealthEventService: EitaHealthEventService = mockk()
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService = mockk()
    private val internalMvAuthorizedProcedureService: InternalMvAuthorizedProcedureService = mockk()
    private val dataService: MvAuthorizedProcedureModelDataService = mockk()
    private val healthEventsService: HealthEventsService = mockk()
    private val healthcareResourceService: HealthcareResourceService = mockk()
    private val healthcareResourceGroupAssociationService: HealthcareResourceGroupAssociationService = mockk()
    private val healthcareResourceGroupService: HealthcareResourceGroupService = mockk()
    private val guiaWithProceduresDataService: GuiaWithProceduresModelDataService = mockk()
    private val totvsGuiaDataService: TotvsGuiaModelDataService = mockk()
    private val healthcareBundleService: HealthcareBundleService = mockk()
    private val tussProcedureSpecialtyService: TussProcedureSpecialtyService = mockk()
    private val healthSpecialistResourceBundleManagementService: HealthSpecialistResourceBundleManagementService = mockk()


    private val executionGroupId = RangeUUID.generate()
    private val controller = BackFillController(
        eitaHealthEventService,
        mvAuthorizedProcedureService,
        internalMvAuthorizedProcedureService,
        dataService,
        healthEventsService,
        healthcareResourceService,
        healthcareResourceGroupAssociationService,
        healthcareResourceGroupService,
        guiaWithProceduresDataService,
        totvsGuiaDataService,
        healthcareBundleService,
        tussProcedureSpecialtyService,
        healthSpecialistResourceBundleManagementService
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @AfterTest
    fun afterTest() {
        clearAllMocks()
    }

    private val executedProcedures = listOf(
        TestModelFactory.buildMvAuthorizedProcedure(
            procedureId = "10",
            executionGroupId = executionGroupId
        ),
        TestModelFactory.buildMvAuthorizedProcedure(
            procedureId = "20",
            executionGroupId = executionGroupId
        ),
        TestModelFactory.buildMvAuthorizedProcedure(
            procedureId = "30",
            executionGroupId = executionGroupId
        )
    )

    @Test
    fun `#findUnSyncProceduresByRange - call service for range`() = runBlocking {
        val request = BackFillUnSyncHealthEventsRequest(
            start = LocalDate.now().minusDays(2),
            end = LocalDate.now()
        )

        coEvery {
            mvAuthorizedProcedureService.findUnSyncProceduresByRange(
                LocalDateTime.of(
                    request.start,
                    LocalTime.MIN
                ), LocalDateTime.of(request.end, LocalTime.MAX)
            )
        } returns executedProcedures.success()
        coEvery {
            eitaHealthEventService.publishExecutedProceduresHealthEvent(
                executedProcedures,
                executionGroupId
            )
        } returns true.success()

        internalAuthentication {
            post("/backfill/find_un_sync_procedures_by_range", request) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            mvAuthorizedProcedureService.findUnSyncProceduresByRange(any(), any())
            eitaHealthEventService.publishExecutedProceduresHealthEvent(
                any(),
                any()
            )
        }
    }

    @Test
    fun `#syncUncoordinatedProcedures should return sucessfully`() = runBlocking {
        val request = BackFillAuthorizeProceduresRequest(
            limit = 10,
            offset = 0
        )

        val healthEvents = listOf(
            TestModelFactory.buildHealthEvents(
                procedureIds = listOf("123456789"),
                originReferences = listOf(
                    EventReference(
                        id = RangeUUID.generate().toString(),
                        location = HealthEventLocationEnum.TOTVS_GUIA
                    )
                )
            )
        )

        val procedures = listOf(TestModelFactory.buildMvAuthorizedProcedure(procedureId = "123456789"))

        coEvery {
            healthEventsService.findByOriginReferenceLocation(
                HealthEventLocationEnum.TOTVS_GUIA,
                request.limit,
                request.offset
            )
        } returns healthEvents.success()

        coEvery {
            mvAuthorizedProcedureService.findByTotvsGuiaId(healthEvents.first().originReferences!!.first().id.toUUID())
        } returns procedures.success()

        coEvery {
            mvAuthorizedProcedureService.updateList(procedures.map { it.copy(healthEventId = healthEvents.first().id) })
        } returns procedures.success()

        internalAuthentication {
            post("/backfill/sync_uncoordinated_procedures", request) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            mvAuthorizedProcedureService.updateList(any())
        }
    }

    @Test
    fun `#backfillResourceGroupAssociations should return sucessfully`() = runBlocking {
        val group = TestModelFactory.buildHealthcareResourceGroup()
        val request = BackfillGroupAssociationsRequest(
            groupName = group.name,
            codes = listOf("123"),
            groupType = group.type
        )

        val resource = TestModelFactory.buildHealthcareResource()

        coEvery {
            healthcareResourceGroupService.getByNameAndType(request.groupName, group.type)
        } returns group.success()

        coEvery {
            healthcareResourceService.findByTussCodes(request.codes)
        } returns listOf(resource).success()

        coEvery {
            healthcareResourceGroupAssociationService.upsertList(match {
                it.first().healthcareResourceGroupId == group.id &&
                        it.first().healthcareResourceId == resource.id
            })
        } returns listOf(TestModelFactory.buildHealthcareResourceGroupAssociation()).success()

        internalAuthentication {
            post("/backfill/resource_group_associations", request) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#backfillTussCodeAssociations should return sucessfully`() = runBlocking {
        val request = BackfillTussCodeAssociationsRequest(
            associations = listOf(
                BackfillTussCodeAssociationRequest(
                    code = "123",
                    tableType = "123",
                    tussCode = "123",
                    tussTableType = "123"
                )
            )
        )

        val resource = TestModelFactory.buildHealthcareResource()

        coEvery {
            healthcareResourceService.findByTussCodes(any())
        } returns listOf(resource).success()

        coEvery {
            healthcareResourceService.updateList(any(), any())
        } returns listOf(resource).success()

        internalAuthentication {
            post("/backfill/tuss_code_associations", request) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#backfillExecuteOldPreExecutedProcedures should return sucessfully`() = runBlocking {
        mockLocalDateTime { mockedLocalDateTime ->
            val procedure = TestModelFactory.buildMvAuthorizedProcedure(
                status = MvAuthorizedProcedureStatus.PRE_EXECUTED,
            )

            coEvery {
                dataService.find(queryEq {
                    where {
                        this.status.eq(MvAuthorizedProcedureStatus.PRE_EXECUTED)
                            .and(this.executedAt.lessEq(mockedLocalDateTime.minusMonths(1)))
                    }
                })
            } returns listOf(procedure).map { it.toModel() }.success()

            coEvery {
                dataService.updateList(listOf(procedure.copy(status = MvAuthorizedProcedureStatus.EXECUTED)).map {
                    it.toModel()
                })
            } returns listOf(procedure).map { it.toModel() }.success()

            internalAuthentication {
                post("/backfill/execute_pre_executed_procedures") { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }
        }
    }

    @Ignore
    @Test
    fun `#backfillGuiaWithProceduresFillStatusAddIdProcedure should return successfully`() = runBlocking {
        val request = BackFillAuthorizeProceduresRequest(
            offset = 1,
            limit = 1
        )

        val guiaWithProcedure = TestModelFactory.buildGuiaWithProcedures(
            closed = false,
            procedures = listOf(GuiaProcedures(
                id = null,
                code = "procedure-id",
                status = MvAuthorizedProcedureStatus.EXECUTED
            ))
        )

        val totvsGuia = TestModelFactory.buildTotvsGuia(
            externalCode = guiaWithProcedure.externalCode
        )

        val procedure = TestModelFactory.buildMvAuthorizedProcedure(
            totvsGuiaId = totvsGuia.id
        )

        val expectedUpdated = listOf(guiaWithProcedure.copy(
            closed = true,
            procedures = guiaWithProcedure.procedures.map { it.copy(id = procedure.id) }
        ))

        coEvery {
            guiaWithProceduresDataService.find(queryEq {
                where { externalCode.isNotNull() }
                    .orderBy { createdAt }.sortOrder { asc }
                    .limit { request.limit }
                    .offset { request.offset }
                })
        } returns listOf(guiaWithProcedure).map { it.toModel() }.success()

        coEvery {
            totvsGuiaDataService.findOne(queryEq { where { externalCode.eq(guiaWithProcedure.externalCode!!) }})
        } returns totvsGuia.toModel().success()

        coEvery {
            dataService.findOne(queryEq {
                where {
                    totvsGuiaId.eq(totvsGuia.id)
                        .and(procedureId.eq(guiaWithProcedure.procedures[0].code!!))
                        .and(personId.eq(guiaWithProcedure.personId!!))
                }
            })
        } returns procedure.toModel().success()

        coEvery {
            guiaWithProceduresDataService.updateList(any())
        } returns expectedUpdated.toModel().success()

        internalAuthentication {
            post("/backfill/populate_status_and_procedures_id", request) { response ->
                ResponseAssert.assertThat(response).isOK()

                coVerifyOnce {
                    guiaWithProceduresDataService.updateList(match { it == expectedUpdated.toModel() })
                }
            }
        }
    }

    @Test
    fun `#populateExecutedAt should be successfull`() = runBlocking {
        val procedures = listOf(
            TestModelFactory.buildMvAuthorizedProcedure(executionRequestedAt = LocalDateTime.now()),
            TestModelFactory.buildMvAuthorizedProcedure(executionRequestedAt = LocalDateTime.now())
        )
        coEvery {
            dataService.find(queryEq {
                where {
                    this.executedAt.isNull() and this.executionRequestedAt.isNotNull()
                }.orderBy { createdAt }.sortOrder { desc }.limit { 50 }
            })
        } returns procedures.toModel().success()


        val updatedProcedure1 = procedures[0].copy(executedAt = procedures[0].executionRequestedAt)

        coEvery {
            dataService.update(updatedProcedure1.toModel())
        } returns updatedProcedure1.toModel().success()

        val updatedProcedure2 = procedures[1].copy(executedAt = procedures[1].executionRequestedAt)

        coEvery {
            dataService.update(updatedProcedure2.toModel())
        } returns updatedProcedure2.toModel().success()

        internalAuthentication {
            post("/backfill/populate_executed_at") { response ->
                ResponseAssert.assertThat(response).isOKWithData(BackfillResponse(2, 0))
            }
        }

        coVerifyOnce { dataService.find(any()) }
        coVerify(exactly = 2) { dataService.update(any()) }

    }

    @Test
    fun `#populateAuthorizedAt should be successfull`() = runBlocking {
        val procedures = listOf(
            TestModelFactory.buildMvAuthorizedProcedure(authorizationRequestedAt = LocalDateTime.now()),
            TestModelFactory.buildMvAuthorizedProcedure(authorizationRequestedAt = LocalDateTime.now())
        )
        coEvery {
            dataService.find(queryEq {
                where {
                    this.authorizedAt.isNull() and this.authorizationRequestedAt.isNotNull()
                }.orderBy { createdAt }.sortOrder { desc }.limit { 50 }
            })
        } returns procedures.toModel().success()


        val updatedProcedure1 = procedures[0].copy(authorizedAt = procedures[0].authorizationRequestedAt)

        coEvery {
            dataService.update(updatedProcedure1.toModel())
        } returns updatedProcedure1.toModel().success()

        val updatedProcedure2 = procedures[1].copy(authorizedAt = procedures[1].authorizationRequestedAt)

        coEvery {
            dataService.update(updatedProcedure2.toModel())
        } returns updatedProcedure2.toModel().success()

        internalAuthentication {
            post("/backfill/populate_authorized_at") { response ->
                ResponseAssert.assertThat(response).isOKWithData(BackfillResponse(2, 0))
            }
        }

        coVerifyOnce { dataService.find(any()) }
        coVerify(exactly = 2) { dataService.update(any()) }
    }

    @Test
    fun `#resetHealthSpecialistResourceBundleId should reset health specialist resource bundle id of tuss procedure specialties`() = runBlocking {
        val request = BackfillTussProcedureSpecialtyRequest(
            ids = listOf(RangeUUID.generate())
        )

        val tussProcedureSpecialty = TestModelFactory.buildTussProcedureSpecialty()

        coEvery {
            tussProcedureSpecialtyService.getByIds(request.ids)
        } returns listOf(tussProcedureSpecialty).success()

        val updatedTussProcedureSpecialty = tussProcedureSpecialty.copy(healthSpecialistResourceBundleId = null)

        coEvery {
            tussProcedureSpecialtyService.updateList(listOf(updatedTussProcedureSpecialty))
        } returns listOf(updatedTussProcedureSpecialty).success()

        internalAuthentication {
            post("/backfill/reset_health_specialist_resource_bundle_id_of_tuss_procedure_specialty", request) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            tussProcedureSpecialtyService.updateList(listOf(updatedTussProcedureSpecialty))
        }
    }


    @Test
    fun `#backfillResourceBundleSpecialty should update procedure specialties`() = runBlocking {
        val request = BackFillResourceBundleSpecialtyRequest(
            ids = listOf(RangeUUID.generate())
        )

        val tussProcedureSpecialty = TestModelFactory.buildTussProcedureSpecialty()
        val healthSpecialistResourceBundle = TestModelFactory.buildHealthSpecialistResourceBundle()

        coEvery { tussProcedureSpecialtyService.findActiveByHealthSpecialistResourceBundleIds(request.ids) } returns listOf(tussProcedureSpecialty).success()

        coEvery {
            healthSpecialistResourceBundleManagementService.update(
                tussProcedureSpecialty.healthSpecialistResourceBundleId!!,
                HealthSpecialistResourceBundlePatchRequest(
                    medicalSpecialtyIds = listOf(tussProcedureSpecialty.medicalSpecialtyId!!),
                )
            )
        } returns HealthSpecialistResourceBundlePatchResponse(
            id = healthSpecialistResourceBundle.id,
            secondaryResources = healthSpecialistResourceBundle.secondaryResources.map { SecondaryResourcesTransport(it) },
            executionAmount = healthSpecialistResourceBundle.executionAmount,
            executionEnvironment = healthSpecialistResourceBundle.executionEnvironment,
            aliceDescription = healthSpecialistResourceBundle.description,
            status = healthSpecialistResourceBundle.status,
            serviceType = healthSpecialistResourceBundle.serviceType,
        ).success()

        internalAuthentication {
            post("/backfill/resource_bundle_specialty", request) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyOnce { healthSpecialistResourceBundleManagementService.update(any(), any()) }
    }
}
