package br.com.alice.bff.business.services

import br.com.alice.data.layer.models.CompanySize
import br.com.alice.data.layer.models.CompanyStaffAccessLevel
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import java.util.UUID

class ModuleService {
    companion object {
        private val READONLY_MODULES = listOf(
            Modules.INVOICES_MODULE_READONLY,
            Modules.BENEFICIARIES_MODULE_READONLY,
            Modules.PRODUCTS_MODULE_READONLY,
            Modules.DATA_MODULE_READONLY,
            Modules.DATA_MODULE_DEMOGRAPHICS_TAB_READONLY,
            Modules.DATA_MODULE_MAGENTA_TAB_READONLY,
            Modules.DATA_MODULE_JOURNEY_TAB_READONLY,
            Modules.BENEFICIARIES_ADD_READONLY,
            Modules.BENEFICIARIES_CANCEL_READONLY,
            Modules.BENEFICIARIES_CHANGE_PRODUCT_READONLY,
            Modules.SETTINGS_MODULE_READONLY,
        )
        private val FINANCE_MODULES = listOf(
            Modules.INVOICES_MODULE,
            Modules.PRODUCTS_MODULE,
        )
    }

    fun getActiveModules(
        traits: CompanyTraits,
        accessLevelStaff: CompanyStaffAccessLevel = CompanyStaffAccessLevel.ADMIN,
    ): List<Modules> =
        Modules.values().toList()
            .validateJourneyTab(traits)
            .validateMicro(traits)
            .restrictAccordingToAccessLevel(accessLevelStaff)

    private fun List<Modules>.validateJourneyTab(traits: CompanyTraits) =
        if (traits.companySize != CompanySize.MLA) this.filter { it != Modules.DATA_MODULE_JOURNEY_TAB }
        else this

    private fun List<Modules>.validateMicro(traits: CompanyTraits) =
        if (traits.companySize == null ||
            listOf(CompanySize.MICRO, CompanySize.UNKNOWN).contains(traits.companySize)
        ) this.filter { it == Modules.INVOICES_MODULE }
        else this

    private fun List<Modules>.restrictAccordingToAccessLevel(
        accessLevelStaff: CompanyStaffAccessLevel,
    ): List<Modules> =
        when (accessLevelStaff) {
            CompanyStaffAccessLevel.ADMIN -> {
                this
                    .filter { !READONLY_MODULES.contains(it) }
                    .let { list ->
                        if (shouldReturnBeneficiaryBatchModule()) {
                            list
                        } else {
                            list.filter { it != Modules.BENEFICIARIES_BATCH_MODULE }
                        }
                    }
            }
            CompanyStaffAccessLevel.FINANCE -> this.filter { FINANCE_MODULES.contains(it) }
            CompanyStaffAccessLevel.VIEW -> {
                val filteredModules = this.filter { !READONLY_MODULES.contains(it) }

                filteredModules.mapNotNull {
                    Modules.values().find { v -> v.name == "${it.name}_READONLY" }
                }
            }
        }

    private fun shouldReturnBeneficiaryBatchModule() =
        FeatureService.get(
            namespace = FeatureNamespace.PORTAL_2_HR,
            key = "should_return_beneficiary_batch_module",
                defaultValue = false,
        )
}

enum class Modules {
    INVOICES_MODULE,
    BENEFICIARIES_MODULE,
    PRODUCTS_MODULE,
    DATA_MODULE,
    DATA_MODULE_DEMOGRAPHICS_TAB,
    DATA_MODULE_MAGENTA_TAB,
    DATA_MODULE_JOURNEY_TAB,
    BENEFICIARIES_ADD,
    BENEFICIARIES_CANCEL,
    BENEFICIARIES_CHANGE_PRODUCT,
    SETTINGS_MODULE,
    BENEFICIARIES_BATCH_MODULE,
    INVOICES_MODULE_READONLY,
    BENEFICIARIES_MODULE_READONLY,
    PRODUCTS_MODULE_READONLY,
    DATA_MODULE_READONLY,
    DATA_MODULE_DEMOGRAPHICS_TAB_READONLY,
    DATA_MODULE_MAGENTA_TAB_READONLY,
    DATA_MODULE_JOURNEY_TAB_READONLY,
    BENEFICIARIES_ADD_READONLY,
    BENEFICIARIES_CANCEL_READONLY,
    BENEFICIARIES_CHANGE_PRODUCT_READONLY,
    SETTINGS_MODULE_READONLY,
}

data class CompanyTraits(
    val companySize: CompanySize?,
    val companyCnpj: String,
    val companyId: UUID,
    val userMail: String? = null
)
