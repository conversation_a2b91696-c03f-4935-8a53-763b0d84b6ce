package br.com.alice.exec.indicator.service

import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.HealthSpecialistScoreEnum
import br.com.alice.data.layer.models.TussProcedureSpecialty
import br.com.alice.data.layer.services.TussProcedureSpecialtyModelDataService
import br.com.alice.exec.indicator.client.FindExistingTussProcedureSpecialtyParams
import br.com.alice.exec.indicator.client.TussProcedureSpecialtyFilters
import br.com.alice.exec.indicator.client.TussProcedureSpecialtyService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.converters.modelConverters.toTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import java.util.UUID

class TussProcedureSpecialtyServiceImpl(
    private val data: TussProcedureSpecialtyModelDataService
) : TussProcedureSpecialtyService {

    override suspend fun addList(models: List<TussProcedureSpecialty>) = data.addList(models.toModel()).map {
        it.toTransport()
    }

    override suspend fun add(model: TussProcedureSpecialty) = data.add(model.toModel()).map {
        it.toTransport()
    }

    override suspend fun get(id: UUID) = useReadDatabase { data.get(id).map {
        it.toTransport()
    } }

    override suspend fun getByIds(ids: List<UUID>) = useReadDatabase {
            data.find { where { this.id.inList(ids) } }.map {
                it.toTransport()
            }
        }

    override suspend fun update(model: TussProcedureSpecialty): Result<TussProcedureSpecialty, Throwable> =
        data.update(model.toModel()).map {
            it.toTransport()
        }

    override suspend fun updateList(
        models: List<TussProcedureSpecialty>,
        returnOnFailure: Boolean
    ) = data.updateList(models.toModel()).map {
        it.toTransport()
    }


    override suspend fun findByAliceCodes(aliceCodes: List<String>) = useReadDatabase {
        data.find { where { this.aliceCode.inList(aliceCodes) } }.map {
            it.toTransport()
        }
    }

    override suspend fun findActiveByHealthSpecialistResourceBundleIds(
        healthSpecialistResourceBundleIds: List<UUID>,
    ) = useReadDatabase {
        data.find {
            where {
                this.healthSpecialistResourceBundleId.inList(healthSpecialistResourceBundleIds)
                    .and(this.endAt.isNull())
            }
        }.map {
            it.toTransport()
        }
    }

    override suspend fun findByTierAndScoreAndSpecialtyAndTussCode(
        tier: SpecialistTier,
        score: HealthSpecialistScoreEnum?,
        specialtyId: UUID,
        tussCode: String
    ) = useReadDatabase {
        data.find {
            where {
                this.tier.eq(tier)
                    .and(score?.let { healthSpecialistScore.eq(it) } ?: healthSpecialistScore.isNull())
                    .and(this.specialtyId.eq(specialtyId))
                    .and(this.tussCode.eq(tussCode))
            }
        }.map {
            it.toTransport()
        }
    }

    override suspend fun findByAliceCodeScoreTierSpecialtyIdBrandAndProductTier(
        params: FindExistingTussProcedureSpecialtyParams
    ) = useReadDatabase {
        data.find {
            where {
                this.aliceCode.eq(params.aliceCode)
                    .and(params.score?.let { healthSpecialistScore.eq(it) } ?: healthSpecialistScore.isNull())
                    .and(this.tier.eq(params.tier))
                    .and(this.specialtyId.eq(params.specialtyId))
                    .and(this.endAt.isNull())
                    .and(this.brand.eq(params.brand))
                    .and(this.productTier.eq(params.productTier))

            }
        }.map {
            it.toTransport()
        }
    }

    override suspend fun findByEndAtNullAndQuery(tussProcedureSpecialtyFilters: TussProcedureSpecialtyFilters) =
        useReadDatabase {
            data.find {
                buildFilterQuery(tussProcedureSpecialtyFilters)
            }.map {
                it.toTransport()
            }
        }

    override suspend fun count(tussProcedureSpecialtyFilters: TussProcedureSpecialtyFilters): Result<Int, Throwable> =
        useReadDatabase {
            data.count { buildFilterQuery(tussProcedureSpecialtyFilters) }
        }

    override suspend fun findActiveByAliceCodesAndSpeciality(
        specialityId: UUID,
        aliceCodes: List<String>
    ): Result<List<TussProcedureSpecialty>, Throwable> =
        useReadDatabase {
            data.find {
                where {
                    this.specialtyId.eq(specialityId)
                        .and(this.aliceCode.inList(aliceCodes))
                        .and(this.endAt.isNull())
                }
            }.map {
                it.toTransport()
            }
        }


    private fun buildFilterQuery(tussProcedureSpecialtyFilters: TussProcedureSpecialtyFilters): QueryBuilder<TussProcedureSpecialtyModelDataService.FieldOptions, TussProcedureSpecialtyModelDataService.OrderingOptions> {
        val filters = buildFilterPredicate(tussProcedureSpecialtyFilters)
        val range = tussProcedureSpecialtyFilters.range

        val queryBuilder = QueryBuilder(
            TussProcedureSpecialtyModelDataService.FieldOptions(),
            TussProcedureSpecialtyModelDataService.OrderingOptions()
        ).where {
            filters?.and(Predicate.isNull(TussProcedureSpecialtyModelDataService.FieldOptions().endAt))
                ?: Predicate.isNull(TussProcedureSpecialtyModelDataService.FieldOptions().endAt)
        }

        return range?.let {
            queryBuilder.orderBy { updatedAt }
                .sortOrder { desc }
                .offset { it.first }
                .limit { it.count() }
        } ?: queryBuilder
    }

    private fun buildFilterPredicate(tussProcedureSpecialtyFilters: TussProcedureSpecialtyFilters) =
        tussProcedureSpecialtyFilters.allFilters().reduceOrNull { aliceCodeSearch, tussCodeSearch ->
            tussCodeSearch?.let { aliceCodeSearch?.and(it) } ?: aliceCodeSearch
        }
}

fun TussProcedureSpecialtyFilters.allFilters(): List<Predicate?> =
    listOf(
        getAliceCodeEqPredicate(),
        getTussCodeEqPredicate(),
    )

private fun TussProcedureSpecialtyFilters.getAliceCodeEqPredicate(): Predicate? =
    aliceCode?.let { TussProcedureSpecialtyModelDataService.FieldOptions().aliceCode.eq(it) }

private fun TussProcedureSpecialtyFilters.getTussCodeEqPredicate(): Predicate? =
    tussCode?.let { TussProcedureSpecialtyModelDataService.FieldOptions().tussCode.eq(it) }
