package br.com.alice.api.scheduler.controllers

import br.com.alice.api.scheduler.ControllerTestHelper
import br.com.alice.api.scheduler.controllers.model.ProviderUnitResponse
import br.com.alice.api.scheduler.controllers.model.ProviderUnitsResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.models.Staff
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class ProviderControllerTest : ControllerTestHelper() {

    private val providerUnitService: ProviderUnitService = mockk()
    private val providerController = ProviderController(providerUnitService)

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { providerController }
    }

    private val providerUnitFlag = TestModelFactory.buildProviderUnit(
        name = "Provider Flag",
    )
    private val staffForAuth = staff.convertTo(Staff::class)

    @Test
    fun `should return 200 when getAliceHouses and provider units is coming from datalayer`() = runBlocking {
        val token = RangeUUID.generate().toString()

        coEvery {
            providerUnitService.getActiveAndShowOnScheduler(null, null)
        } returns listOf(providerUnitFlag).success()
        val expectedResponse = ProviderUnitsResponse(
            listOf(
                ProviderUnitResponse(
                    id = providerUnitFlag.id,
                    name = providerUnitFlag.name,
                )
            )
        )

        authenticatedAs(token, staffForAuth) {
            get("/v1/provider_units") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

    }

    @Test
    fun `should return 200 when getAliceHouses and provider units is coming from datalayer with query`() = runBlocking {
        val token = RangeUUID.generate().toString()

        coEvery {
            providerUnitService.getActiveAndShowOnScheduler(null, "Alice")
        } returns listOf(providerUnitFlag).success()
        val expectedResponse = ProviderUnitsResponse(
            listOf(
                ProviderUnitResponse(
                    id = providerUnitFlag.id,
                    name = providerUnitFlag.name,
                )
            )
        )

        authenticatedAs(token, staffForAuth) {
            get("/v1/provider_units?q=Alice") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

    }
}
