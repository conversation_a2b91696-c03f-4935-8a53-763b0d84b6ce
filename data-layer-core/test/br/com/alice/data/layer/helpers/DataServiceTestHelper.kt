package br.com.alice.data.layer.helpers

import br.com.alice.common.TestApplication
import br.com.alice.common.core.exceptions.DatabaseException
import br.com.alice.common.core.extensions.toSnakeCase
import br.com.alice.data.layer.tables.AnonymizableTable
import br.com.alice.data.layer.tables.PersonTokenTable
import br.com.alice.data.layer.tables.Table
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.TestInstance
import org.reflections.Reflections
import kotlin.reflect.KClass
import kotlin.test.BeforeTest

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
open class DataServiceTestHelper {

    protected val httpInvoker = TestApplication.httpInvoker

    val mainJdbi = TestApplication.mainJdbi
    val mainJdbiReplica = TestApplication.mainJdbiReplica
    val tokenJdbi = TestApplication.tokenJdbi

    private val tableNames = Reflections("br.com.alice.data.layer.tables")
        .getSubTypesOf(Table::class.java)
        .filter { it != PersonTokenTable::class.java && it != AnonymizableTable::class.java }
        .map { it.simpleName.toSnakeCase().removeSuffix("_table") }

    @BeforeAll
    fun init() {
        TestApplication.start()
        truncateAll()
    }

    @BeforeTest
    fun startServer() = TestApplication.start()

    private fun truncateAll() {
        val tablesSql = tableNames.joinToString(", ") { tableName ->
            "\"$tableName\""
        }
        val sql = "TRUNCATE $tablesSql CASCADE"
        mainJdbi.withHandle<Int, DatabaseException> { handle ->
            handle.createUpdate(sql).execute()
        }
    }

    fun truncate(vararg models: KClass<out Table<*>>) {
        val tablesSql = models
            .map { it.simpleName!!.toSnakeCase().removeSuffix("_table") }
            .joinToString(", ") { tableName -> "\"$tableName\"" }

        val sql = "TRUNCATE $tablesSql CASCADE"
        mainJdbi.withHandle<Int, DatabaseException> { handle ->
            handle.createUpdate(sql).execute()
        }
    }

    fun truncatePersonToken() {
        tokenJdbi.withHandle<Int, DatabaseException> { handle ->
            handle.createUpdate("TRUNCATE person_token CASCADE").execute()
        }
    }

}
