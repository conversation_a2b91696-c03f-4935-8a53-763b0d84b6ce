package br.com.alice.exec.indicator.client

import br.com.alice.common.MvUtil
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.HealthcareBundle
import br.com.alice.data.layer.models.HealthcareResource
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HealthcareResourceService : Service {

    override val namespace get() = "exec_indicator"
    override val serviceName get() = "healthcare_resource"

    companion object {
        const val DEFAULT_PRIMARY_TUSS_TABLE = "22"
        const val DEFAULT_ALICE_PROCEDURE_TABLE = "98"
    }

    suspend fun updateList(
        models: List<HealthcareResource>,
        returnOnFailure: Boolean
    ): Result<List<HealthcareResource>, Throwable>

    suspend fun get(id: UUID): Result<HealthcareResource, Throwable>

    suspend fun getByIds(ids: List<UUID>): Result<List<HealthcareResource>, Throwable>

    suspend fun add(model: HealthcareResource): Result<HealthcareResource, Throwable>

    suspend fun update(model: HealthcareResource): Result<HealthcareResource, Throwable>

    suspend fun list(
        searchTerm: String?,
        range: IntRange,
        filters: HealthcareResourceFilters,
    ): Result<HealthcareResourceListWithCount, Throwable>

    suspend fun associateCompositionHashById(
        healthcareResourceId: UUID,
        compositionHash: String,
    ): Result<HealthcareResource, Throwable>

    suspend fun findBySearchTokens(
        query: String,
        ignoredCodes: List<String>? = null,
    ): Result<List<HealthcareResource>, Throwable>

    suspend fun findBySearchTokensAndTableType(
        query: String,
        tableType: String
    ): Result<List<HealthcareResource>, Throwable>

    suspend fun findByTussCodes(codes: List<String>): Result<List<HealthcareResource>, Throwable>

    suspend fun findByCodes(
        codes: List<String>,
        onlyActive: Boolean = true
    ): Result<List<HealthcareResource>, Throwable>

    suspend fun getByTussCode(code: String): Result<HealthcareResource, Throwable>

    suspend fun getByCodeAndTableType(code: String, tableType: String): Result<HealthcareResource, Throwable>

    suspend fun findByCodesAndTableType(
        codes: List<String>,
        tableType: String
    ): Result<List<HealthcareResource>, Throwable>

    suspend fun findByList(
        procedureIds: List<UUID>,
    ): Result<List<HealthcareResource>, Throwable>

    suspend fun getByRange(range: IntRange): Result<List<HealthcareResource>, Throwable>

    suspend fun getByRangeAndTableTypes(
        range: IntRange,
        tableTypes: List<String> = listOf(DEFAULT_PRIMARY_TUSS_TABLE, DEFAULT_ALICE_PROCEDURE_TABLE)
    ): Result<List<HealthcareResource>, Throwable>

    suspend fun getTussResourcesByRange(range: IntRange): Result<List<HealthcareResource>, Throwable>

    suspend fun findBySearchTokensPaginated(
        query: String,
        offset: Int = 0,
        limit: Int = 20
    ): Result<List<HealthcareResource>, Throwable>

    suspend fun findBySearchTokensAndTableTypesPaginated(
        query: String,
        offset: Int = 0,
        limit: Int = 20,
        tableTypes: List<String> = listOf(DEFAULT_PRIMARY_TUSS_TABLE, DEFAULT_ALICE_PROCEDURE_TABLE)
    ): Result<List<HealthcareResource>, Throwable>

    suspend fun findByCompositionHash(compositionHash: String): Result<HealthcareResource, Throwable>

    suspend fun findByCompositionHashAndCode(
        compositionHash: String,
        code: String
    ): Result<HealthcareResource, Throwable>

    suspend fun getHealthcareResourceAssociatedToBundle(
        healthcareBundle: HealthcareBundle
    ): Result<HealthcareResource, Throwable>

    suspend fun findByCompositionHashList(compositionHash: List<String>): Result<List<HealthcareResource>, Throwable>

    suspend fun findTussResourcesBySearchTokensPaginated(
        query: String,
        offset: Int,
        limit: Int
    ): Result<List<HealthcareResource>, Throwable>

    suspend fun findPrimaryTussResourcesBySearchTokensPaginated(
        query: String?,
        offset: Int,
        limit: Int
    ): Result<List<HealthcareResource>, Throwable>

    suspend fun count(): Result<Int, Throwable>
    suspend fun countTussResources(): Result<Int, Throwable>
    suspend fun countBySearchTokens(query: String): Result<Int, Throwable>
    suspend fun countByTableTypes(
        tableTypes: List<String> = listOf(DEFAULT_PRIMARY_TUSS_TABLE, DEFAULT_ALICE_PROCEDURE_TABLE)
    ): Result<Int, Throwable>
    suspend fun countBySearchTokensAndTableTypes(
        query: String,
        tableTypes: List<String> = listOf(DEFAULT_PRIMARY_TUSS_TABLE, DEFAULT_ALICE_PROCEDURE_TABLE)
    ): Result<Int, Throwable>
    suspend fun countTussResourcesBySearchTokens(query: String): Result<Int, Throwable>
    suspend fun countPrimaryTussResourcesBySearchTokens(
        query: String?,
    ): Result<Int, Throwable>

    suspend fun getByCode(code: String): Result<HealthcareResource, Throwable>
    suspend fun getByCodeOrTussCode(code: String): Result<HealthcareResource, Throwable>

    suspend fun findByTussCodeOrCode(codes: List<String>): Result<List<HealthcareResource>, Throwable>
}

data class HealthcareResourceListWithCount(
    val list: List<HealthcareResource>,
    val count: Int
)

data class HealthcareResourceFilters(
    val tableType: List<String>? = emptyList(),
    val hasCompositionHash: Boolean? = null,
    val compositionHash: String? = null,
    val searchGuiaType: MvUtil.TISS? = null
)
