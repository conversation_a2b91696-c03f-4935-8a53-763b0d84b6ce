package br.com.alice.api.healthcareops.models

import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.AccommodationType
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.CompanyAddress
import br.com.alice.data.layer.models.CompanyBusinessUnit
import br.com.alice.data.layer.models.CompanySize
import br.com.alice.data.layer.models.CompanyStatus
import br.com.alice.data.layer.models.DealChannel
import br.com.alice.data.layer.models.DealStage
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.data.layer.models.OngoingCompanyDealDetails
import br.com.alice.data.layer.models.OngoingDealStatusHistory
import br.com.alice.data.layer.models.PaymentDetail
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.models.PriceListing
import br.com.alice.data.layer.models.ProductType
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class CompanyInfoResponse(
    val id: UUID,
    val name: String,
    val legalName: String,
    val cnpj: String,
    val email: String,
    val phoneNumber: String,
    val address: CompanyAddress,
    val billingAccountablePartyId: UUID? = null,
    val defaultProductId: ProductResponse? = null,
    val availableProducts: List<ProductResponse>? = emptyList(),
    val billingAccountableParty: BillingAccountablePartyResponse? = null,
    val totalEmployees: Int? = null,
    val contracts: List<CompanyContractResponse> = emptyList(),
    val status: CompanyStatus? = null,
    val deals: List<OngoingCompanyDealTransport>? = emptyList(),
    val companySize: CompanySize? = null,
    val companyBusinessUnit: CompanyBusinessUnit? = null,
)

data class CompanyInvoiceResponse(
    val id: UUID,
    val name: String,
    val legalName: String,
    val cnpj: String,
    val email: String,
    val phoneNumber: String,
    val address: CompanyAddress,
    val billingAccountableParty: CompanyBillingAccountableParty,
    val memberInvoiceGroup: List<CompanyMemberInvoiceGroupInfo>,
)

data class CompanyDealResponse(
    val id: UUID = RangeUUID.generate(),
    val name: String,
    val status: DealStage,
    val dealDetails: OngoingCompanyDealDetails,
    val sourceId: String,
    val channel: DealChannel,
    val statusHistory: List<OngoingDealStatusHistory> = emptyList(),
    val deletedAt: LocalDateTime? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val salesFirmName: String
)

data class CompanyBillingAccountableParty(
    val id: UUID,
    val firstName: String,
    val lastName: String,
    val type: BillingAccountablePartyType,
    val nationalId: String,
    val email: String,
)

data class CompanyMemberInvoiceGroupInfo(
    val id: UUID,
    val externalId: String,
    val payments: List<CompanyMemberInvoiceGroupPayment>,
    val billingAccountablePartyId: UUID,
    val referenceDate: LocalDate,
    val dueDate: LocalDate,
    val status: MemberInvoiceGroupStatus,
    val type: MemberInvoiceType? = null,
)

data class CompanyMemberInvoiceGroupPayment(
    val id: UUID,
    val totalAmount: BigDecimal,
    val status: InvoicePaymentStatus,
    val invoiceGroupId: UUID?,
    val externalId: String?,
    val paymentDetail: PaymentDetail?,
    val approvedAt: LocalDateTime?,
    val reason: PaymentReason?,
    val canceledReason: CancellationReason?,
    val createdAt: LocalDateTime,
    val method: PaymentMethod,
    val portalUrl: String? = null
)

data class ProductInfo(
    val productId: UUID,
    val title: String,
    val ansNumber: String? = null,
    val displayName: String? = null,
    val priceListing: PriceListing? = null,
    val type: ProductType,
    val accommodation: AccommodationType? = null,
    val hasNationalCoverage: Boolean = false
)

data class CreateInvoicePaymentRequest(
    val paymentMethod: PaymentMethod,
    val dueDate: String?,
)

data class CreateFirstInvoicePaymentRequest(
    val referenceDate: String,
    val paymentMethod: PaymentMethod,
    val dueDate: String,
)

data class CreateCompanyRequest(
    val parentId: UUID? = null,
    val externalCode: String? = null,
    val name: String,
    val legalName: String,
    val cnpj: String,
    val email: String,
    val phoneNumber: String,
    val addressPostalCode: String,
    val addressStreet: String,
    val addressNumber: Int,
    val addressComplement: String? = null,
    val addressNeighborhood: String? = null,
    val addressCity: String,
    val addressState: String,
    val totalEmployees: Int? = null,
    val billingAccountablePartyId: UUID? = null,
    val defaultProductId: UUID? = null,
    val availableProducts: List<UUID>? = null,
    val billingAccountableParty: BillingAccountablePartyRequest? = null,
    val companySize: CompanySize? = null,
    val companyBusinessUnit: CompanyBusinessUnit? = null,
)

data class UpdateCompanyRequest(
    val parentId: UUID? = null,
    val contractId: UUID? = null,
    val externalCode: String? = null,
    val name: String? = null,
    val legalName: String? = null,
    val cnpj: String? = null,
    val email: String? = null,
    val phoneNumber: String? = null,
    val addressPostalCode: String? = null,
    val addressStreet: String? = null,
    val addressNumber: Int? = null,
    val addressComplement: String? = null,
    val addressNeighborhood: String? = null,
    val addressCity: String? = null,
    val addressState: String? = null,
    val companyContractId: UUID? = null,
    val totalEmployees: Int? = null,
    val billingAccountablePartyId: UUID? = null,
    val defaultProductId: UUID? = null,
    val availableProducts: List<UUID>? = null,
    val billingAccountableParty: BillingAccountablePartyRequest? = null,
    val companySize: CompanySize? = null,
    val companyBusinessUnit: CompanyBusinessUnit? = null,
)
