package br.com.alice.sales_channel.api.converters

import br.com.alice.data.layer.models.DealStage
import br.com.alice.data.layer.models.getDealStage
import br.com.alice.sales_channel.events.toDateTime
import br.com.alice.sales_channel.model.DealRequest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

class DealConverterTest {
    @Test
    fun `should throw exception when required createdAt is null`() {
        assertFailsWith<IllegalArgumentException> {
            val dealRequest = DealRequest(
                id = "criado",
                status = DealStage.PAYMENT_REQUESTED.name,
                createdAt = null,
                updatedAt = "2023-09-02",
                companyDocument = "338.983.589-19",
                companyName = "Compay Y",
                companyLegalName = "LegalName",
                employeeCount = 5,
                livesCount = 6,
                contractModel = "contract model",

                sellerName = "Corretora X",
                sellerDocument = "338.983.589-19",
                sellerEmail = "<EMAIL>",
                sellerPhoneNumber = "11999999999",
                sellerBirthDate = "2000-01-01"

            )
            DealConverter.toChangeEvent(dealRequest)
        }
    }

    @Test
    fun `should throw exception when updatedAt is null`() {
        assertFailsWith<IllegalArgumentException> {
            val dealRequest = DealRequest(
                id = "criado",
                status = DealStage.PAYMENT_REQUESTED.name,
                createdAt = "2023-09-02",
                updatedAt = null,
                companyDocument = "338.983.589-19",
                companyName = "Compay Y",
                companyLegalName = "LegalName",
                employeeCount = 5,
                livesCount = 6,
                contractModel = "contract model" ,
                        sellerName = "Corretora X",
                sellerDocument = "338.983.589-19",
                sellerEmail = "<EMAIL>",
                sellerPhoneNumber = "11999999999",
                sellerBirthDate = "2000-01-01"
            )
            DealConverter.toChangeEvent(dealRequest)
        }
    }

    @Test
    fun `should throw exception when sellerName is null`() {
        assertFailsWith<IllegalArgumentException> {
            val dealRequest = DealRequest(
                id = "criado",
                status = DealStage.PAYMENT_REQUESTED.name,
                createdAt = "2023-09-02",
                updatedAt = "2023-09-02",
                companyDocument = "338.983.589-19",
                companyName = "Compay Y",
                companyLegalName = "LegalName",
                employeeCount = 5,
                livesCount = 6,
                contractModel = "contract model",
                sellerName = "null",
                sellerDocument = "338.983.589-19",
                sellerEmail = "<EMAIL>",
                sellerPhoneNumber = "11999999999",
                sellerBirthDate = "2000-01-01"
            )
            DealConverter.toChangeEvent(dealRequest)
        }
    }

    @Test
    fun `should throw exception when required sellerDocument is null`() {
        assertFailsWith<IllegalArgumentException> {
            val dealRequest = DealRequest(
                id = "criado",
                status = DealStage.PAYMENT_REQUESTED.name,
                createdAt = "2023-09-02",
                updatedAt = "2023-09-02",
                companyDocument = "338.983.589-19",
                companyName = "Compay Y",
                companyLegalName = "LegalName",
                employeeCount = 5,
                livesCount = 6,
                contractModel = "contract model",
                sellerName = "Corretora X",
                sellerDocument = null,
                sellerEmail = "<EMAIL>",
                sellerPhoneNumber = "11999999999",
                sellerBirthDate = "2000-01-01"
            )
            DealConverter.toChangeEvent(dealRequest)
        }
    }

    @Test
    fun `should throw exception when required CompanyName is null`() {
        assertFailsWith<IllegalArgumentException> {
            val dealRequest = DealRequest(
                id = "criado",
                status = DealStage.PAYMENT_REQUESTED.name,
                createdAt = "2023-09-02",
                updatedAt = "2023-09-02",
                companyName = null,
                companyDocument = "338.983.589-19",
                companyLegalName = "LegalName",
                employeeCount = 5,
                livesCount = 6,
                contractModel = "contract model",
                sellerName = "Corretora X",
                sellerDocument = "338.983.589-19",
                sellerEmail = "<EMAIL>",
                sellerPhoneNumber = "11999999999",
                sellerBirthDate = "2000-01-01"
            )
            DealConverter.toChangeEvent(dealRequest)
        }
    }

    @Test
    fun `should convert when required has values`() {
            val dealRequest = DealRequest(
                id = "criado",
                status = DealStage.PAYMENT_REQUESTED.name,
                createdAt = "1693916444658",
                updatedAt = "1695346499246",
                companyName = "Compay Y",
                companyDocument = "338.983.589-19",
                companyLegalName = "LegalName",
                employeeCount = null,
                livesCount = 6,
                contractModel = "contract model",
                sellerName = "Corretora X",
                sellerDocument = "338.983.589-19",
                sellerEmail = "<EMAIL>",
                sellerPhoneNumber = "11999999999",
                sellerBirthDate = "949370400000"
            )
            DealConverter.toChangeEvent(dealRequest)
    }

    @Test
    fun `should convert to dealEvent successfully`() {
        val dealRequest = DealRequest(
            id = "criado",
            status = "5333873",
            createdAt = "1693916444658",
            updatedAt = "1695346499246",
            companyName = "Compay Y",
            companyDocument = "338.983.589-19",
            companyLegalName = "LegalName",
            employeeCount = null,
            livesCount = 6,
            contractModel = "contract model",
            sellerName = "Corretora X",
            sellerDocument = null,
            sellerEmail = "<EMAIL>",
            sellerPhoneNumber = "11999999999",
            sellerBirthDate = "949370400000"

        )
        val event = DealConverter.toChangeEvent(dealRequest)

        assertEquals(getDealStage(dealRequest.status).name, event.stage.name)
        assertEquals(toDateTime(dealRequest.createdAt, "createdAt"), event.createdAt)
        assertEquals(toDateTime(dealRequest.updatedAt, "updatedAt"), event.updatedAt)
        assertEquals(dealRequest.sellerName, event.sellerName)
        assertEquals(dealRequest.sellerDocument, event.sellerDocument)
        assertEquals(dealRequest.companyName, event.companyName)
        assertEquals(dealRequest.companyDocument, event.companyDocument)
        assertEquals(dealRequest.employeeCount, event.employeeCount)

    }

    @Test
    fun `should set isCompulsoryMembership to true when isCompulsoryMembershipP is Compulsoria`() {
        val dealRequest = DealRequest(
            id = "criado",
            status = DealStage.PAYMENT_REQUESTED.name,
            createdAt = "1693916444658",
            updatedAt = "1695346499246",
            companyName = "Compay Y",
            companyDocument = "338.983.589-19",
            companyLegalName = "LegalName",
            employeeCount = 5,
            livesCount = 6,
            contractModel = "contract model",
            isCompulsoryMembershipP = "Compulsória",
            compulsoryMembershipType = "Type A",
            compulsoryMembershipResponse = "Response A"
        )
        val event = DealConverter.toChangeEvent(dealRequest)
        assertEquals(true, event.isCompulsoryMembership)
        assertEquals("Type A", event.compulsoryMembershipType)
        assertEquals("Response A", event.compulsoryMembershipResponse)
    }

    @Test
    fun `should set isCompulsoryMembership to true when isCompulsoryMembership3p is Compulsorio`() {
        val dealRequest = DealRequest(
            id = "criado",
            status = DealStage.PAYMENT_REQUESTED.name,
            createdAt = "1693916444658",
            updatedAt = "1695346499246",
            companyName = "Compay Y",
            companyDocument = "338.983.589-19",
            companyLegalName = "LegalName",
            employeeCount = 5,
            livesCount = 6,
            contractModel = "contract model",
            isCompulsoryMembership3p = "Compulsório",
            compulsoryMembershipType = "Type B",
            compulsoryMembershipResponse = "Response B"
        )
        val event = DealConverter.toChangeEvent(dealRequest)
        assertEquals(true, event.isCompulsoryMembership)
        assertEquals("Type B", event.compulsoryMembershipType)
        assertEquals("Response B", event.compulsoryMembershipResponse)
    }

    @Test
    fun `should set isCompulsoryMembership to null when isCompulsoryMembershipP and isCompulsoryMembership3p are not Compulsoria or Compulsorio`() {
        val dealRequest = DealRequest(
            id = "criado",
            status = DealStage.PAYMENT_REQUESTED.name,
            createdAt = "1693916444658",
            updatedAt = "1695346499246",
            companyName = "Compay Y",
            companyDocument = "338.983.589-19",
            companyLegalName = "LegalName",
            employeeCount = 5,
            livesCount = 6,
            contractModel = "contract model",
            isCompulsoryMembershipP = "Voluntária",
            isCompulsoryMembership3p = "Voluntário",
            compulsoryMembershipType = "Type C",
            compulsoryMembershipResponse = "Response C"
        )
        val event = DealConverter.toChangeEvent(dealRequest)
        assertEquals(null, event.isCompulsoryMembership)
        assertEquals("Type C", event.compulsoryMembershipType)
        assertEquals("Response C", event.compulsoryMembershipResponse)
    }

    @Test
    fun `should set compulsoryMembership fields correctly when isCompulsoryMembershipP is Compulsoria`() {
        val dealRequest = DealRequest(
            id = "criado",
            status = DealStage.PAYMENT_REQUESTED.name,
            createdAt = "1693916444658",
            updatedAt = "1695346499246",
            companyName = "Compay Y",
            companyDocument = "338.983.589-19",
            companyLegalName = "LegalName",
            employeeCount = 5,
            livesCount = 6,
            contractModel = "contract model",
            isCompulsoryMembershipP = "Compulsória",
            compulsoryMembershipType = "Type A",
            compulsoryMembershipResponse = "Response A"
        )
        val event = DealConverter.toChangeEvent(dealRequest)
        assertEquals(true, event.isCompulsoryMembership)
        assertEquals("Type A", event.compulsoryMembershipType)
        assertEquals("Response A", event.compulsoryMembershipResponse)
    }

    @Test
    fun `should set compulsoryMembership fields correctly when isCompulsoryMembership3p is Compulsorio`() {
        val dealRequest = DealRequest(
            id = "criado",
            status = DealStage.PAYMENT_REQUESTED.name,
            createdAt = "1693916444658",
            updatedAt = "1695346499246",
            companyName = "Compay Y",
            companyDocument = "338.983.589-19",
            companyLegalName = "LegalName",
            employeeCount = 5,
            livesCount = 6,
            contractModel = "contract model",
            isCompulsoryMembership3p = "Compulsório",
            compulsoryMembershipType = "Type B",
            compulsoryMembershipResponse = "Response B"
        )
        val event = DealConverter.toChangeEvent(dealRequest)
        assertEquals(true, event.isCompulsoryMembership)
        assertEquals("Type B", event.compulsoryMembershipType)
        assertEquals("Response B", event.compulsoryMembershipResponse)
    }

    @Test
    fun `should set compulsoryMembership fields to null when isCompulsoryMembershipP and isCompulsoryMembership3p are not Compulsoria or Compulsorio`() {
        val dealRequest = DealRequest(
            id = "criado",
            status = DealStage.PAYMENT_REQUESTED.name,
            createdAt = "1693916444658",
            updatedAt = "1695346499246",
            companyName = "Compay Y",
            companyDocument = "338.983.589-19",
            companyLegalName = "LegalName",
            employeeCount = 5,
            livesCount = 6,
            contractModel = "contract model",
            isCompulsoryMembershipP = "Voluntária",
            isCompulsoryMembership3p = "Voluntário",
            compulsoryMembershipType = "Type C",
            compulsoryMembershipResponse = "Response C"
        )
        val event = DealConverter.toChangeEvent(dealRequest)
        assertEquals(null, event.isCompulsoryMembership)
        assertEquals("Type C", event.compulsoryMembershipType)
        assertEquals("Response C", event.compulsoryMembershipResponse)
    }
}
