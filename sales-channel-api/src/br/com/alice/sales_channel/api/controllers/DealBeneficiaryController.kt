package br.com.alice.sales_channel.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.toResponse
import br.com.alice.membership.client.MemberPriceService
import br.com.alice.product.client.ProductService
import br.com.alice.sales_channel.api.converters.DealBeneficiaryConverter
import br.com.alice.sales_channel.api.models.DealBeneficiaryListResponse
import br.com.alice.sales_channel.service.DealBeneficiaryService
import br.com.alice.sales_channel.service.SalesAgentService
import br.com.alice.sales_channel.service.SalesFirmStaffService
import br.com.alice.schedule.client.AppointmentScheduleService
import com.github.kittinunf.result.getOrElse
import io.ktor.http.Parameters
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class DealBeneficiaryController(
    salesFirmStaffService: SalesFirmStaffService,
    salesAgentService: SalesAgentService,
    private val dealBeneficiaryService: DealBeneficiaryService,
    private val appointmentScheduleService: AppointmentScheduleService,
    private val productService: ProductService,
    private val memberPriceService: MemberPriceService,
) : BaseAuthenticatedController(salesFirmStaffService, salesAgentService) {

    suspend fun listByDeal(ongoingCompanyDealId: UUID, queryParams: Parameters): Response {
        val range = parseRange(queryParams)
        val query = parseQuery(queryParams)

        val beneficiaries = dealBeneficiaryService.searchByOngoingDeal(
            id = ongoingCompanyDealId,
            offset = range.first,
            limit = range.last,
            query = query
        ).get()

        val personIds = beneficiaries.map { it.personId }
        val productIds = beneficiaries.map { it.product.id }
        val memberIds = beneficiaries.map { it.memberId }

        return coroutineScope {
            val appointmentScheduleDeferred = async { appointmentScheduleService.findLastForEachPersonIn(personIds) }
            val productsDeferred = async { productService.findByIds(productIds, ProductService.FindOptions(withPriceListing = true)) }
            val memberPricesDeferred = memberIds.map { memberId ->
                async {
                    memberId to memberPriceService.getCurrentPriceForMember(memberId).getOrElse { null }
                }
            }

            val appointmentSchedule = appointmentScheduleDeferred.await().getOrElse { emptyList() }
            val products = productsDeferred.await().getOrElse { emptyList() }
            val memberPrices = memberPricesDeferred.awaitAll().toMap()

            DealBeneficiaryListResponse(
                beneficiaries = DealBeneficiaryConverter.convert(beneficiaries, appointmentSchedule, products, memberPrices),
                totalFinished = beneficiaries.count { it.stage.completed == it.stage.count },
                total = beneficiaries.size,
                offset = range.first,
                limit = range.last
            ).toResponse()
        }
    }
}
