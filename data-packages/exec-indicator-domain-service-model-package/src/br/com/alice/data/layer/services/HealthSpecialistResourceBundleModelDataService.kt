package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.client.UpdaterList
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleModel
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import java.time.LocalDateTime
import java.util.UUID
import com.github.kittinunf.result.Result

@RemoteService
interface HealthSpecialistResourceBundleModelDataService :
    Service,
    Finder<HealthSpecialistResourceBundleModelDataService.FieldOptions, HealthSpecialistResourceBundleModelDataService.OrderingOptions, HealthSpecialistResourceBundleModel>,
    Counter<HealthSpecialistResourceBundleModelDataService.FieldOptions, HealthSpecialistResourceBundleModelDataService.OrderingOptions, HealthSpecialistResourceBundleModel>,
    Getter<HealthSpecialistResourceBundleModel>,
    Adder<HealthSpecialistResourceBundleModel>,
    Updater<HealthSpecialistResourceBundleModel>,
    UpdaterList<HealthSpecialistResourceBundleModel>,
    Deleter<HealthSpecialistResourceBundleModel> {

    override val namespace get() = "exec_indicator"
    override val serviceName get() = "health_specialist_resource_bundle"

    class Id : Field.UUIDField(HealthSpecialistResourceBundleModel::id) {
        fun isNotNull() = Predicate.isNotNull(this)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class Code : Field.TextField(HealthSpecialistResourceBundleModel::code) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class PrimaryTuss : Field.TextField(HealthSpecialistResourceBundleModel::primaryTuss) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class TempOldAliceCode : Field.TextField(HealthSpecialistResourceBundleModel::tempOldAliceCode) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class SearchTokens : Field.TextField(HealthSpecialistResourceBundleModel::searchTokens) {
        fun search(value: String) = Predicate.search(this, value)
    }

    class CreatedAt : Field.DateTimeField(HealthSpecialistResourceBundleModel::createdAt)

    class UpdatedAt : Field.DateTimeField(HealthSpecialistResourceBundleModel::updatedAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }

    class ExecutionAmount : Field.IntegerField(HealthSpecialistResourceBundleModel::executionAmount) {
        fun eq(value: Int) = Predicate.eq(this, value)
    }

    class ServiceType : Field.TextField(HealthSpecialistResourceBundleModel::serviceType) {
        fun eq(value: HealthSpecialistResourceBundleServiceType) = Predicate.eq(this, value)
        fun inList(value: List<HealthSpecialistResourceBundleServiceType>) = Predicate.inList(this, value)
    }

    class ExecutionEnvironment : Field.TextField(HealthSpecialistResourceBundleModel::executionEnvironment) {
        fun eq(value: HealthSpecialistProcedureExecutionEnvironment) = Predicate.eq(this, value)
    }

    class SecondaryResources : Field.JsonbField(HealthSpecialistResourceBundleModel::secondaryResources) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: List<UUID>) =
            Predicate.jsonSearch(this, "[${value.sorted().distinct().joinToString(",") { "\"$it\"" }}]")
    }

    class Status : Field.TextField(HealthSpecialistResourceBundleModel::status) {
        fun eq(value: br.com.alice.common.core.Status) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val id = Id()
        val code = Code()
        val primaryTuss = PrimaryTuss()
        val tempOldAliceCode = TempOldAliceCode()
        val searchTokens = SearchTokens()
        val executionAmount = ExecutionAmount()
        val serviceType = ServiceType()
        val executionEnvironment = ExecutionEnvironment()
        val secondaryResources = SecondaryResources()
        val status = Status()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
        val updatedAt = UpdatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<HealthSpecialistResourceBundleModel, Throwable>
    override suspend fun add(model: HealthSpecialistResourceBundleModel): Result<HealthSpecialistResourceBundleModel, Throwable>
    override suspend fun update(model: HealthSpecialistResourceBundleModel): Result<HealthSpecialistResourceBundleModel, Throwable>
    override suspend fun delete(model: HealthSpecialistResourceBundleModel): Result<Boolean, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<HealthSpecialistResourceBundleModel>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun updateList(
        models: List<HealthSpecialistResourceBundleModel>,
        returnOnFailure: Boolean
    ): Result<List<HealthSpecialistResourceBundleModel>, Throwable>
}
