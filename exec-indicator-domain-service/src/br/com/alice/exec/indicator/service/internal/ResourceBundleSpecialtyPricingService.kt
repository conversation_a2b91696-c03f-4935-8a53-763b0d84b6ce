package br.com.alice.exec.indicator.service.internal


import br.com.alice.common.extensions.mapEach
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricing

import br.com.alice.data.layer.services.ResourceBundleSpecialtyPricingModelDataService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.converters.modelConverters.toTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import java.time.LocalDate
import java.util.UUID


class ResourceBundleSpecialtyPricingService(
    private val resourceBundleSpecialtyPricingModelDataService: ResourceBundleSpecialtyPricingModelDataService,
) {
    suspend fun getActiveByResourceBundleSpecialtyId(
        resourceBundleSpecialtyIds: List<UUID>
    ): Result<List<ResourceBundleSpecialtyPricing>, Throwable> =
        resourceBundleSpecialtyPricingModelDataService.find {
            where {
                resourceBundleSpecialtyId.inList(resourceBundleSpecialtyIds)
            }
        }.mapEach {
            it.toTransport()
        }.map {
            it.filter {
                it.beginAt <= LocalDate.now() && (it.endAt == null || it.endAt!! >= LocalDate.now())
            }
        }

    suspend fun add(specialty: ResourceBundleSpecialtyPricing) =
        resourceBundleSpecialtyPricingModelDataService.add(specialty.toModel()).map { it.toTransport() }

    suspend fun update(specialty: ResourceBundleSpecialtyPricing) =
        resourceBundleSpecialtyPricingModelDataService.update(specialty.toModel()).map { it.toTransport() }

    suspend fun updateList(
        specialties: List<ResourceBundleSpecialtyPricing>
    ): Result<List<ResourceBundleSpecialtyPricing>, Throwable> =
        resourceBundleSpecialtyPricingModelDataService.updateList(specialties.map { it.toModel() }).mapEach {
            it.toTransport()
        }

    suspend fun deleteList(
        specialties: List<ResourceBundleSpecialtyPricing>
    ): Result<List<Boolean>, Throwable> =
        resourceBundleSpecialtyPricingModelDataService.deleteList(specialties.map { it.toModel() })
}
