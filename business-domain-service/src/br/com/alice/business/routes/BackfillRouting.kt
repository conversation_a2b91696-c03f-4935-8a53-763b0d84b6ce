package br.com.alice.business.routes

import br.com.alice.business.controllers.BackFillController
import br.com.alice.business.controllers.BeneficiaryCompiledViewBackfillController
import br.com.alice.business.controllers.CompanyActivationFilesBackfillController
import br.com.alice.business.controllers.CompanyProductConfigurationBackfillController
import br.com.alice.business.controllers.CompanyStaffBackfillController
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import io.ktor.server.routing.Routing
import io.ktor.server.routing.delete
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Routing.backFillRoutes() {
    val backFillController by inject<BackFillController>()
    val companyProductConfigurationBackfillController by inject<CompanyProductConfigurationBackfillController>()
    val companyActivationFilesBackfillController by inject<CompanyActivationFilesBackfillController>()

    route("/backfill") {
        post("/onboarding_phases") { coHandler(backFillController::backFillOnboardingPhase) }
        post("/retry_beneficiary_created") { coHandler(backFillController::retryBeneficiaryCreated) }
        post("/beneficiary_created_batch") { coHandler(backFillController::backfillBeneficiaryCreatedBatch) }
        post("/member_product_price") { coHandler(backFillController::backFillMemberProductPrice) }
        post("/beneficiary_member") { coHandler(backFillController::backFillBeneficiaryMember) }
        post("/archive_beneficiaries") { coHandler(backFillController::archiveBeneficiaries) }
        post("/beneficiary_with_existent_member") { coHandler(backFillController::createBeneficiaryForActiveMember) }
        post("/beneficiary_hired_at") { coHandler(backFillController::populateHiredAtForBeneficiary) }
        post("/beneficiary_churn") { coHandler(backFillController::populateCanceledReasonForBeneficiary) }
        post("/beneficiary_cancellation") { coHandler(backFillController::cancellationRequestByBeneficiaryIds) }
        post("/member_without_member_contract") { coHandler(backFillController::createMemberContractToMemberWithoutContract) }
        post("/create_cassi_member_from_existent_one") { coHandler(backFillController::createCassiMemberFromExistentOne) }
        post("/create_beneficiary_hubspot_deal_and_contact") { coHandler(backFillController::createBeneficiaryHubspotDealAndContact) }
        post("/update_cassi_older_expiration_date") { coHandler(backFillController::executeUpdateCassiExpirationDateRoutine) }
        post("/update_cassi_older_expiration_date_by_member_id") { coHandler(backFillController::updateCassiMemberByMemberIds) }
        post("/create_cassi_member_from_member_without_one") { coHandler(backFillController::createCassiMemberFromMemberWithoutOne) }
        post("/cancel_cancellation_request") { coHandler(backFillController::cancelCancellationRequest) }
        post("/create_subcontract_for_company") { coHandler(backFillController::createSubcontractForCompany) }
        post("/update_company_billing_accountable_party") { coHandler(backFillController::updateCompanyBillingAccountableParty) }
        post("/force_member_activation") { coHandler(backFillController::forceMemberActivation) }
        post("/request_update_beneficiary_dependent") { coHandler(backFillController::requestUpdateDependent) }
        post("/update_parent_and_status_field") { coHandler(backFillController::updateBeneficiaryParentAndStatusFields) }
        post("/reset_beneficiary_risk_flow") { coHandler(backFillController::resetBeneficiaryRiskFlow) }
        post("/move_beneficiary_company") { coHandler(backFillController::moveBeneficiaryCompany) }
        post("/update_parent_member_beneficiary") { coHandler(backFillController::backfillBeneficiariesParentMember) }
        post("/upsert_company_product_configuration") { coHandler(companyProductConfigurationBackfillController::upsert) }
        post("/company_product_configuration_change_transitory_to_stable") {
            coHandler(
                companyProductConfigurationBackfillController::changeTransitoryToStable
            )
        }
        post("/company_product_configuration_change_version") { coHandler(companyProductConfigurationBackfillController::companyProductConfigurationChangeVersion) }
        post("/migrate_company_products_to_cppl") { coHandler(backFillController::migrateCompanyProductsToCompanyProductPriceListing) }
        post("/grace_period_type") { coHandler(backFillController::backfillGracePeriodType) }
        post("/update_grace_period_type") { coHandler(backFillController::updateGracePeriodTypeAndSync) }
        post("/update_company_and_contract_started_at") { coHandler(backFillController::updateCompanyAndContractStartedAt) }
        post("/update_subcontracts_cppl") { coHandler(backFillController::updateCpplOnSubcontracts) }
        post("/update_subcontracts_nature_and_billing_group") { coHandler(backFillController::updateCompanySubcontractsNatureAndBillingGroup) }
        post("/update_company_size_in_company") { coHandler(backFillController::updateCompanySizeInCompany) }
        post("/update_cppls_items") { coHandler(backFillController::backfillItemsCppl) }
        post("/update_or_create_cppl") { coHandler(backFillController::updateOrCreateCppl) }
        post("/remove_duplicated_current_cppl") { coHandler(backFillController::backfillRemovingDuplicatedCurrentCppl) }
        post("/update_beneficiary_relation_type") { coHandler(backFillController::updateBeneficiaryRelationType) }
        post("/sanitize_product_ans_number") { coHandler(backFillController::sanitizeAnsNumber) }
        post("/sanitize_cppl_ans_number") { coHandler(backFillController::sanitizeCpplAnsNumber) }

        route("billing_accountable_party") {
            post("/update_address") { coHandler(backFillController::backfillUpdateBillingAccountablePartyAddress) }
        }

        route("/beneficiary/") {
            post("move_to_phase") { coHandler(backFillController::moveBeneficiaryToPhase) }
            post("update_parent_person") { coHandler(backFillController::updateBeneficiaryParentPerson) }
            post("create_new_dependents") { coHandler(backFillController::createNewDependents) }
            post("update") { coHandler(backFillController::updateBeneficiaries) }
            post("emit_beneficiary_updated_event") { coHandler(backFillController::emitBeneficiaryUpdatedEvent) }
        }

        route("/company") {
            post("/update_contract_id") { coHandler(backFillController::updateCompanyContractId) }
            post("/update_with_subcontracts") { coHandler(backFillController::updateWithSubcontracts) }
        }

        route("/contract") {
            post("/delete_contract") { coHandler(backFillController::deleteContract) }
            post("/update_status") { coHandler(backFillController::updateContractStatus) }
        }

        route("/subcontract") {
            post("/delete") { coHandler(backFillController::deleteSubcontract) }
            post("/change_contract_id") { coHandler(backFillController::updateContractIdOfSubcontract) }
            post("/update_blocked_at") { coHandler(backFillController::updateSubcontractBlockedAt) }
            post("/update_status") { coHandler(backFillController::updateSubcontractStatus) }
        }

        route("/member") {
            post("/delete_b2c_data") { coHandler(backFillController::deleteB2bDataFromB2cMember) }
            post("update_parent_info") { coHandler(backFillController::updateMemberParentInfo) }
            post("/change_members_product") { coHandler(backFillController::changeMembersProduct) }
        }

        route("/company_product_price_listing") {
            post("/delete") { coHandler(backFillController::deleteCompanyProductPriceListingById) }
            post("/delete_from_company") { coHandler(backFillController::deleteCompanyProductPriceListingByCompanyId) }
            post("/create_new_and_block_or_remove_old") { coHandler(backFillController::createNewCpplAndBlockOrRemoveOldCppl) }
        }

        route("/beneficiary_compiled_view") {
            val beneficiaryCompiledViewBackfillController by inject<BeneficiaryCompiledViewBackfillController>()
            post("/") { coHandler(beneficiaryCompiledViewBackfillController::createBeneficiaryCompiledView) }
            delete("/") { coHandler(beneficiaryCompiledViewBackfillController::deleteBeneficiaryCompiledView) }
            post("/parent_name") { coHandler(beneficiaryCompiledViewBackfillController::backfillBeneficiaryCompiledParentData) }
            post("/fill_company_sub_contract_id") { coHandler(beneficiaryCompiledViewBackfillController::fillCompanySubContractId) }
        }

        route("/company_staff") {
            val companyStaffBackFillController by inject<CompanyStaffBackfillController>()
            post("/") { coHandler(companyStaffBackFillController::backfillCompanyStaff) }
        }

        route("/standard_cost") {
            post("/insert_standard_cost") { coHandler(backFillController::insertStandardCost) }
        }

        route("/company_activation_files") {
            post("/delete_by_company_id") { coHandler(companyActivationFilesBackfillController::deleteByCompanyId) }
        }
    }
}
