package br.com.alice.business.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CassiMemberService
import br.com.alice.business.events.RequestCassiMemberUpdateEvent
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.helpers.verifyNone
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CassiMember
import br.com.alice.data.layer.models.withCassiMember
import br.com.alice.person.model.events.MemberCancelledEvent
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class CassiMemberConsumerTest : ConsumerTest() {
    private val beneficiaryService: BeneficiaryService = mockk()
    private val cassiMemberService: CassiMemberService = mockk()

    @Nested
    inner class RequestUpdate {
        private val consumer = CassiMemberConsumer(
            cassiMemberService,
            beneficiaryService,
        )

        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)

        @BeforeTest
        fun setup() {
            mockkObject(logger)
        }

        @AfterTest
        fun clear() {
            unmockkAll()
        }

        @Test
        fun `#handleRequestedCassiMemberUpdate - should update the CassiMember by person and member`() = runBlocking {
            val cassiMember = CassiMember(memberId = member.id)
            val memberWithCassi = member.withCassiMember(cassiMember)
            val event = RequestCassiMemberUpdateEvent(member, person)

            coEvery { cassiMemberService.updateCassiMember(person, member) } returns memberWithCassi

            val result = consumer.handleRequestedCassiMemberUpdate(event)

            assertThat(result).isSuccess()

            coVerifyOnce { cassiMemberService.updateCassiMember(person, member) }
            coVerifyNone {
                beneficiaryService.findByPersonId(
                    person.id, findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
                )
            }
        }

        @Test
        fun `#handleRequestedCassiMemberUpdate - should only log error when not found exception is caught and the error message should be according expirationDate null`() = runBlocking {
            val beneficiary = TestModelFactory.buildBeneficiary()
            val onboarding = TestModelFactory.buildBeneficiaryOnboarding()
            val beneficiaryWithOnboarding = beneficiary.copy(onboarding = onboarding)
            val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id, expirationDate = null)

            val event = RequestCassiMemberUpdateEvent(member, person)

            coEvery { cassiMemberService.updateCassiMember(person, member) } returns NotFoundException()
            coEvery { cassiMemberService.getByMemberId(member.id) } returns cassiMember
            coEvery {
                beneficiaryService.findByPersonId(
                    person.id, findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
                )
            } returns beneficiaryWithOnboarding

            val result = consumer.handleRequestedCassiMemberUpdate(event)

            assertThat(result).isSuccess()

            coVerifyOnce { cassiMemberService.updateCassiMember(person, member) }
            coVerifyOnce {
                beneficiaryService.findByPersonId(
                    person.id, findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
                )
            }
            coVerifyOnce { cassiMemberService.getByMemberId(member.id) }
            verifyOnce {
                logger.error(
                    "Cassi data not found",
                    "person_id" to person.id,
                    "member_id" to member.id,
                    "beneficiary_id" to beneficiary.id,
                    "member_status" to member.status,
                    "beneficiary_onboarding_phase" to beneficiary.onboarding?.currentPhase?.phase,
                    "beneficiary_activated_at" to beneficiary.activatedAt,
                    "cassi_member_expiration_date" to null,
                )
            }
        }

        @Test
        fun `#handleRequestedCassiMemberUpdate - should only log error when not found exception is caught and the error message should be according expirationDate filled`() = runBlocking {
            val beneficiary = TestModelFactory.buildBeneficiary()
            val onboarding = TestModelFactory.buildBeneficiaryOnboarding()
            val beneficiaryWithOnboarding = beneficiary.copy(onboarding = onboarding)
            val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id, expirationDate = LocalDateTime.now().minusDays(1))

            val event = RequestCassiMemberUpdateEvent(member, person)

            coEvery { cassiMemberService.updateCassiMember(person, member) } returns NotFoundException()
            coEvery { cassiMemberService.getByMemberId(member.id) } returns cassiMember
            coEvery {
                beneficiaryService.findByPersonId(
                    person.id, findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
                )
            } returns beneficiaryWithOnboarding

            val result = consumer.handleRequestedCassiMemberUpdate(event)

            assertThat(result).isSuccess()

            coVerifyOnce { cassiMemberService.updateCassiMember(person, member) }
            coVerifyOnce {
                beneficiaryService.findByPersonId(
                    person.id, findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
                )
            }
            coVerifyOnce { cassiMemberService.getByMemberId(member.id) }
            verifyOnce {
                logger.error(
                    "Expiration_date is filled and Cassi info not found",
                    "person_id" to person.id,
                    "member_id" to member.id,
                    "beneficiary_id" to beneficiary.id,
                    "member_status" to member.status,
                    "beneficiary_onboarding_phase" to beneficiary.onboarding?.currentPhase?.phase,
                    "beneficiary_activated_at" to beneficiary.activatedAt,
                    "cassi_member_expiration_date" to cassiMember.expirationDate,
                )
            }
        }

        @Test
        fun `#handleRequestedCassiMemberUpdate - should pass any exception to forward`() = runBlocking {
            val event = RequestCassiMemberUpdateEvent(member, person)

            coEvery { cassiMemberService.updateCassiMember(person, member) } returns Exception()

            val result = consumer.handleRequestedCassiMemberUpdate(event)

            assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce { cassiMemberService.updateCassiMember(person, member) }
            coVerifyNone {
                beneficiaryService.findByPersonId(
                    person.id, findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
                )
            }
            verifyNone {
                logger.error(
                    "Cassi data not found",
                    "person_id" to person.id,
                    "member_id" to member.id,
                    any(),
                    "member_status" to member.status,
                    any(),
                    any()
                )
            }
        }
    }

    @Nested
    inner class MemberCanceled {
        private val consumer = CassiMemberConsumer(
            cassiMemberService,
            beneficiaryService,
        )

        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)

        @Test
        fun `#handleMemberCanceled - should delete the CassiMember when card details is empty`() = runBlocking {
            val event = MemberCancelledEvent(member)

            val cassiMember = TestModelFactory.buildCassiMember(
                memberId = member.id,
                accountNumber = null,
            )

            coEvery { cassiMemberService.getByMemberId(member.id) } returns cassiMember
            coEvery { cassiMemberService.delete(cassiMember) } returns true

            val result = consumer.handleMemberCanceled(event)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { cassiMemberService.getByMemberId(member.id) }
            coVerifyOnce { cassiMemberService.delete(cassiMember) }
        }

        @Test
        fun `#handleMemberCanceled - shouldn't delete the CassiMember when it has card details `() = runBlocking {
            val event = MemberCancelledEvent(member)

            val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)

            coEvery { cassiMemberService.getByMemberId(member.id) } returns cassiMember
            coEvery { cassiMemberService.delete(cassiMember) } returns true

            val result = consumer.handleMemberCanceled(event)

            assertThat(result).isSuccessWithData(cassiMember)

            coVerifyOnce { cassiMemberService.getByMemberId(member.id) }
            coVerifyNone { cassiMemberService.delete(cassiMember) }
        }

        @Test
        fun `#handleMemberCanceled - shouldn't delete the CassiMember when it is not found`() = runBlocking {
            val event = MemberCancelledEvent(member)

            val cassiMember = TestModelFactory.buildCassiMember(
                memberId = member.id,
                accountNumber = null,
            )

            coEvery { cassiMemberService.getByMemberId(member.id) } returns NotFoundException()
            coEvery { cassiMemberService.delete(cassiMember) } returns true

            val result = consumer.handleMemberCanceled(event)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { cassiMemberService.getByMemberId(member.id) }
            coVerifyNone { cassiMemberService.delete(cassiMember) }
        }
    }

}
