package br.com.alice.api.ops.converters

import br.com.alice.api.ops.models.ProductBundleIdRequest
import br.com.alice.api.ops.models.ProductBundleResponse
import br.com.alice.api.ops.models.SpecialistResponse
import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.ProductBundle

object ProductBundleConverter : Converter<ProductBundle, ProductBundleResponse>(
    ProductBundle::class, ProductBundleResponse::class
) {
    fun convert(source: ProductBundle) =
        super.convert(source,
            map(ProductBundleResponse::providerIds)
                    from (
                    if (source.providerIds.isNotEmpty())
                        source.providerIds.map { ProductBundleIdRequest(it.toString()) }
                    else null
                    ),
            map(ProductBundleResponse::specialistIds)
                    from (
                    if (source.specialistIds.isNotEmpty())
                        source.specialistIds.map { ProductBundleIdRequest(it.toString()) }
                    else null
                    ),
            map(ProductBundleResponse::specialtyTiers)
                    from (
                    if (source.specialtyTiers.isNotEmpty())
                        source.specialtyTiers
                    else null
                    ),
            map(ProductBundleResponse::externalSpecialistIds)
                    from (
                    if (source.externalSpecialists.isNotEmpty())
                        source.externalSpecialists.map { ProductBundleIdRequest(it.toString()) }
                    else null
                    ),
        )
}

object SpecialistConverter : Converter<HealthProfessional, SpecialistResponse>(
    HealthProfessional::class, SpecialistResponse::class
) {
    fun convert(source: HealthProfessional) =
        super.convert(source)
}
