package br.com.alice.member.api.converters.onboarding

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.common.Converter
import br.com.alice.data.layer.models.MemberOnboarding
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.models.appContent.RemoteAction
import br.com.alice.member.api.models.onboarding.v2.MemberOnboardingResponse
import br.com.alice.member.api.models.onboarding.v2.MemberOnboardingStepResponse
import br.com.alice.member.onboarding.factory.OnboardingFactory
import br.com.alice.member.onboarding.model.OnboardingCoverButton
import br.com.alice.member.onboarding.model.OnboardingVersion
import java.time.LocalDateTime

object MemberOnboardingConverter : Converter<MemberOnboarding, MemberOnboardingResponse>(
    MemberOnboarding::class, MemberOnboardingResponse::class
) {
    fun convert(
        source: MemberOnboarding,
        isMemberActive: Boolean,
        version: OnboardingVersion = OnboardingVersion.V1,
        aliceInfoVersion: Int? = null,
        memberActivationDate: LocalDateTime
    ) = OnboardingFactory.getTitleAndDescription(source, version)
        .let { (title, description) ->
            MemberOnboardingResponse(
                id = source.id,
                title = title,
                description = description,
                stepsDone = source.finishedSteps,
                steps = buildStep(source, version, aliceInfoVersion, memberActivationDate),
                isMemberActive = isMemberActive,
                button =  takeIf { isMemberActive }?.let { buildButton() }
            )
        }

    private fun buildStep(
        memberOnboarding: MemberOnboarding,
        version: OnboardingVersion,
        aliceInfoVersion: Int?,
        memberActivationDate: LocalDateTime
    ) =
        memberOnboarding.steps.map { step ->
            val template = OnboardingFactory.get(
                type = step.templateType,
                flowType = memberOnboarding.flowType,
                version = version,
                aliceInfoVersion = aliceInfoVersion,
                memberActivationDate = memberActivationDate
            )

            MemberOnboardingStepResponse(
                type = step.templateType,
                status = step.status,
                title = template.title,
                timeToComplete = template.timeToComplete,
                url = template.url,
                path = template.path,
                bottomSheet = template.bottomSheet,
                backgroundImage = template.backgroundImage,
                caption = template.caption,
                questionnaireType = template.questionnaireType,
                data = template.data
            )
        }

    private fun buildButton() =
        OnboardingCoverButton(
            label = "Confira detalhes do plano",
            mobileRoute = ActionRouting.CHESHIRE_SCREEN,
            params = mapOf(
                "action" to RemoteAction(
                    method = RemoteActionMethod.GET,
                    endpoint = ServiceConfig.url("/app_content/screen/plan_details_menu"),
                    params = mapOf("screen_id" to "plan_details_menu")
                )
            )
        )
}
