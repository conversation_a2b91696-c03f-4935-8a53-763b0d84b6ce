package br.com.alice.exec.indicator.service

import br.com.alice.common.MvUtil
import br.com.alice.common.core.exceptions.ConflictException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.mapFirst
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthcareBundle
import br.com.alice.data.layer.models.HealthcareResource
import br.com.alice.data.layer.services.HealthcareResourceModelDataService
import br.com.alice.exec.indicator.client.HealthcareResourceFilters
import br.com.alice.exec.indicator.client.HealthcareResourceListWithCount
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.HealthcareResourceService.Companion.DEFAULT_PRIMARY_TUSS_TABLE
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.converters.modelConverters.toTransport
import br.com.alice.exec.indicator.events.HealthcareResourceUpsertedEvent
import br.com.alice.featureconfig.core.FeatureService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class HealthcareResourceServiceImpl(
    private val data: HealthcareResourceModelDataService,
    private val kafkaProducerService: KafkaProducerService,
) : HealthcareResourceService {

    override suspend fun updateList(
        models: List<HealthcareResource>,
        returnOnFailure: Boolean
    ): Result<List<HealthcareResource>, Throwable> =
        data.updateList(models.map { it.toModel() }).then {
            models.pmap {
                produceHealthcareResourceUpsertedEvent(it)
            }
        }.map {
            it.toTransport()
        }

    override suspend fun get(id: UUID): Result<HealthcareResource, Throwable> = useReadDatabase {
        data.get(id).map { it.toTransport() }
    }

    override suspend fun getByIds(ids: List<UUID>): Result<List<HealthcareResource>, Throwable> = useReadDatabase {
        data.find { where { this.id.inList(ids) } }.map {
            it.toTransport()
        }
    }

    override suspend fun add(model: HealthcareResource): Result<HealthcareResource, Throwable> =
        data.add(model.toModel()).then {
            produceHealthcareResourceUpsertedEvent(model)
        }.map { it.toTransport() }

    override suspend fun update(model: HealthcareResource): Result<HealthcareResource, Throwable> =
        data.update(model.toModel()).then {
            produceHealthcareResourceUpsertedEvent(model)
        }.map {
            it.toTransport()
        }

    override suspend fun list(
        searchTerm: String?,
        range: IntRange,
        filters: HealthcareResourceFilters,
    ): Result<HealthcareResourceListWithCount, Throwable> = useReadDatabase {
        coResultOf {
            val predicate = listPredicate(searchTerm, filters)
            coroutineScope {
                val rowsDeferred = async {
                    data.find {
                        where { predicate }
                            .orderByList({ listOf(this.updatedAt, this.tableType) }, { listOf(desc, desc) })
                            .offset { range.first() }
                            .limit { range.count() }
                    }
                }

                val countDeferred = async { data.count { where { predicate } } }

                HealthcareResourceListWithCount(
                    list = rowsDeferred.await().map { it.map { it.toTransport() } }.get(),
                    count = countDeferred.await().get()
                )
            }
        }
    }

    private fun listPredicate(
        searchTerm: String?,
        filters: HealthcareResourceFilters
    ) = HealthcareResourceModelDataService.FieldOptions().active.eq(true)
        .andSearchTermPredicate(searchTerm)
        .andTableType(filters.tableType)
        .andHasCompositionHash(filters.hasCompositionHash, filters.compositionHash)
        .andFilterPSCodes(filters.searchGuiaType)


    private fun Predicate.andSearchTermPredicate(searchTerm: String?): Predicate {
        logger.info("HealthcareResourceServiceImpl::list::searchTermPredicate", "searchTerm" to searchTerm)
        if (searchTerm.isNullOrBlank()) return this
        return this and Predicate.search(HealthcareResourceModelDataService.FieldOptions().searchTokens, searchTerm)
    }

    private fun Predicate.andTableType(tableType: List<String>?): Predicate {
        logger.info("HealthcareResourceServiceImpl::list::andTableType", "tableType" to tableType)
        if (tableType.isNullOrEmpty()) return this
        return this and HealthcareResourceModelDataService.FieldOptions().tableType.inList(tableType)
    }

    private fun Predicate.andFilterPSCodes(searchGuiaType: MvUtil.TISS?): Predicate {
        logger.info("HealthcareResourceServiceImpl::list::andFilterEmergencyCare", "searchGuiaType" to searchGuiaType)

        val emergencyCareCodes = FeatureService.getList(
            namespace = FeatureNamespace.EXEC_INDICATOR,
            key = "emergency_room_procedures",
            defaultValue = listOf("10101039", "98450001", "98450002", "98450003", "98450005")
        )

        if (searchGuiaType != MvUtil.TISS.PS) {
            return this and HealthcareResourceModelDataService.FieldOptions().code.notInList(emergencyCareCodes)
        }

        return this and HealthcareResourceModelDataService.FieldOptions().code.inList(emergencyCareCodes)
    }

    @OptIn(OrPredicateUsage::class)
    private fun Predicate.andHasCompositionHash(hasCompositionHash: Boolean?, compositionHash: String?): Predicate {
        logger.info(
            "HealthcareResourceServiceImpl::list::andHasCompositionHash",
            "hasCompositionHash" to hasCompositionHash
        )
        return if (hasCompositionHash == null) {
            if (compositionHash.isNullOrBlank()) {
                this
            } else {
                this and HealthcareResourceModelDataService.FieldOptions().compositionHash.eq(compositionHash)
            }
        } else if (hasCompositionHash) {
            if (compositionHash.isNullOrBlank()) {
                this and HealthcareResourceModelDataService.FieldOptions().compositionHash.isNotNull()
            } else {
                this and HealthcareResourceModelDataService.FieldOptions().compositionHash.eq(compositionHash)
            }
        } else {
            if (compositionHash.isNullOrBlank()) {
                this and HealthcareResourceModelDataService.FieldOptions().compositionHash.isNull()
            } else {
                this and scope(
                    HealthcareResourceModelDataService.FieldOptions().compositionHash.isNull()
                        .or(HealthcareResourceModelDataService.FieldOptions().compositionHash.eq(compositionHash))
                )
            }
        }


    }

    private suspend fun produceHealthcareResourceUpsertedEvent(healthcareResource: HealthcareResource) {
        kafkaProducerService.produce(
            HealthcareResourceUpsertedEvent(
                healthcareResource.id
            ),
            healthcareResource.id.toString()
        )
    }

    override suspend fun associateCompositionHashById(
        healthcareResourceId: UUID,
        compositionHash: String,
    ): Result<HealthcareResource, Throwable> {
        return data.get(healthcareResourceId)
            .map { result ->
                if (result.compositionHash.isNullOrEmpty()) {
                    result.copy(compositionHash = compositionHash)
                } else if (result.compositionHash != compositionHash) {
                    throw ConflictException()
                } else {
                    result
                }
            }.flatMap { result ->
                data.update(result).map {
                    it.toTransport()
                }
            }
    }

    override suspend fun findBySearchTokens(
        query: String,
        ignoredCodes: List<String>?,
    ): Result<List<HealthcareResource>, Throwable> = useReadDatabase {
        return@useReadDatabase if (ignoredCodes.isNullOrEmpty()) {
            data.find {
                where { this.searchTokens.search(query) }.limit { 10 }
            }.map {
                it.toTransport()
            }
        } else {
            data.find {
                where { this.searchTokens.search(query).and(code.notInList(ignoredCodes)) }.limit { 10 }
            }.map {
                it.toTransport()
            }
        }
    }

    override suspend fun findBySearchTokensAndTableType(
        query: String,
        tableType: String
    ): Result<List<HealthcareResource>, Throwable> = useReadDatabase {
        data.find {
            where { this.searchTokens.search(query).and(this.tableType.eq(tableType)) }.limit { 10 }
        }.mapEach { it.toTransport() }
    }

    override suspend fun findByTussCodes(codes: List<String>): Result<List<HealthcareResource>, Throwable> =
        useReadDatabase {
            data.find { where { this.code.inList(codes) and this.active.eq(true) } }.map {
                it.map {
                    it.toTransport()
                }
            }
        }

    override suspend fun findByCodes(codes: List<String>, onlyActive: Boolean) =
        useReadDatabase {
            data.find { where { this.code.inList(codes).andOnlyActive(onlyActive) } }
        }.mapEach { it.toTransport() }

    override suspend fun findByCodesAndTableType(codes: List<String>, tableType: String) =
        useReadDatabase {
            data.find { where { this.code.inList(codes) and this.tableType.eq(tableType) and this.active.eq(true) } }
                .mapEach { it.toTransport() }
        }

    override suspend fun getByTussCode(code: String): Result<HealthcareResource, Throwable> = useReadDatabase {
        data.find { where { this.code.eq(code) and this.tussCode.eq(code) } }.mapFirst().map {
            it.toTransport()
        }
    }

    override suspend fun getByCode(code: String): Result<HealthcareResource, Throwable> = useReadDatabase {
        data.findOne { where { this.code.eq(code) and this.active.eq(true) } }
            .map { it.toTransport() }
    }

    @OptIn(OrPredicateUsage::class)
    override suspend fun getByCodeOrTussCode(code: String): Result<HealthcareResource, Throwable> = useReadDatabase {
        data.findOne { where { this.code.eq(code).or(this.tussCode.eq(code)) and this.active.eq(true) } }
            .map { it.toTransport() }
    }

    @OptIn(OrPredicateUsage::class)
    override suspend fun findByTussCodeOrCode(codes: List<String>): Result<List<HealthcareResource>, Throwable> =
        useReadDatabase {
            data.find {
                where { this.code.inList(codes).or(this.tussCode.inList(codes)) and this.active.eq(true) }
            }.map {
                it.toTransport()
            }
        }

    override suspend fun getByCodeAndTableType(
        code: String,
        tableType: String
    ): Result<HealthcareResource, Throwable> = useReadDatabase {
        data.find { where { this.code.eq(code).and(this.tableType.eq(tableType)) } }.mapFirst()
            .map { it.toTransport() }
    }

    override suspend fun findByList(procedureIds: List<UUID>): Result<List<HealthcareResource>, Throwable> =
        useReadDatabase {
            data.find { where { this.id.inList(procedureIds) } }.map {
                it.toTransport()
            }
        }

    override suspend fun getByRange(range: IntRange): Result<List<HealthcareResource>, Throwable> = useReadDatabase {
        data.find {
            orderBy { updatedAt }
                .sortOrder { desc }
                .offset { range.first }
                .limit { range.count() }
        }.map {
            it.toTransport()
        }
    }

    override suspend fun getByRangeAndTableTypes(
        range: IntRange,
        tableTypes: List<String>
    ): Result<List<HealthcareResource>, Throwable> =
        useReadDatabase {
            data.find {
                where { this.tableType.inList(tableTypes) }
                    .orderBy { updatedAt }
                    .sortOrder { desc }
                    .offset { range.first }
                    .limit { range.count() }
            }.map {
                it.toTransport()
            }
        }

    @OptIn(OrPredicateUsage::class)
    override suspend fun getTussResourcesByRange(range: IntRange): Result<List<HealthcareResource>, Throwable> =
        useReadDatabase {
            data.find {
                where {
                    (this.isOriginalTuss.eq(true) or this.tussCode.isNull())
                        .and(this.active.eq(true))
                }
                    .orderBy { updatedAt }
                    .sortOrder { desc }
                    .offset { range.first }
                    .limit { range.count() }
            }.map {
                it.toTransport()
            }
        }

    override suspend fun findBySearchTokensAndTableTypesPaginated(
        query: String,
        offset: Int,
        limit: Int,
        tableTypes: List<String>
    ): Result<List<HealthcareResource>, Throwable> = useReadDatabase {
        data.find {
            where {
                this.searchTokens.search(query).and(this.tableType.inList(tableTypes))
            }
                .orderBy { updatedAt }
                .sortOrder { desc }
                .offset { offset }
                .limit { limit }
        }.map {
            it.toTransport()
        }
    }

    override suspend fun findByCompositionHash(
        compositionHash: String,
    ): Result<HealthcareResource, Throwable> = useReadDatabase {
        data.findOne {
            where { this.compositionHash.eq(compositionHash) }
        }.map {
            it.toTransport()
        }
    }

    override suspend fun findByCompositionHashAndCode(
        compositionHash: String,
        code: String
    ): Result<HealthcareResource, Throwable> = useReadDatabase {
        data.findOne {
            where { this.compositionHash.eq(compositionHash).and(this.code.eq(code)) }
        }.map {
            it.toTransport()
        }
    }

    override suspend fun getHealthcareResourceAssociatedToBundle(
        healthcareBundle: HealthcareBundle
    ): Result<HealthcareResource, Throwable> {
        return when {
            (healthcareBundle.compositionHash == null) -> {
                return NotFoundException("No composition hash").failure()
            }

            (healthcareBundle.code != null) -> {
                findByCompositionHashAndCode(
                    healthcareBundle.compositionHash!!,
                    healthcareBundle.code!!
                )
            }

            else -> {
                findByCompositionHash(
                    healthcareBundle.compositionHash!!
                )
            }
        }
    }

    override suspend fun findByCompositionHashList(
        compositionHashList: List<String>,
    ): Result<List<HealthcareResource>, Throwable> = useReadDatabase {
        data.find {
            where { this.compositionHash.inList(compositionHashList) }
        }.map {
            it.toTransport()
        }
    }

    override suspend fun findBySearchTokensPaginated(
        query: String,
        offset: Int,
        limit: Int
    ): Result<List<HealthcareResource>, Throwable> = useReadDatabase {
        data.find {
            where {
                this.searchTokens.search(query)
            }
                .orderBy { updatedAt }
                .sortOrder { desc }
                .offset { offset }
                .limit { limit }
        }.map {
            it.toTransport()
        }
    }

    @OptIn(OrPredicateUsage::class)
    override suspend fun findTussResourcesBySearchTokensPaginated(
        query: String,
        offset: Int,
        limit: Int
    ): Result<List<HealthcareResource>, Throwable> = useReadDatabase {
        data.find {
            where {
                this.searchTokens.search(query).and(
                    scope(this.isOriginalTuss.eq(true) or this.tussCode.isNull())
                ).and(this.active.eq(true))
            }
                .orderBy { updatedAt }
                .sortOrder { desc }
                .offset { offset }
                .limit { limit }
        }.map {
            it.toTransport()
        }
    }

    @OptIn(OrPredicateUsage::class)
    override suspend fun findPrimaryTussResourcesBySearchTokensPaginated(
        query: String?,
        offset: Int,
        limit: Int
    ): Result<List<HealthcareResource>, Throwable> = useReadDatabase {
        data.find {
            where {
                scope(this.isOriginalTuss.eq(true) or this.tussCode.isNull())
                    .and(this.tableType.eq(DEFAULT_PRIMARY_TUSS_TABLE))
                    .let { predicate ->
                        if (query == null)
                            predicate
                        else
                            predicate.and(this.searchTokens.search(query))
                    }
            }
                .orderBy { updatedAt }
                .sortOrder { desc }
                .offset { offset }
                .limit { limit }
        }.map {
            it.toTransport()
        }
    }

    override suspend fun count(): Result<Int, Throwable> = useReadDatabase {
        data.count { all() }
    }

    @OptIn(OrPredicateUsage::class)
    override suspend fun countTussResources(): Result<Int, Throwable> = useReadDatabase {
        data.count { where { (this.isOriginalTuss.eq(true) or this.tussCode.isNull()).and(this.active.eq(true)) } }
    }

    override suspend fun countBySearchTokens(query: String): Result<Int, Throwable> = useReadDatabase {
        data.count {
            where {
                this.searchTokens.search(query)
            }
        }
    }

    override suspend fun countByTableTypes(tableTypes: List<String>): Result<Int, Throwable> {
        return useReadDatabase {
            data.count {
                where {
                    this.tableType.inList(tableTypes)
                }
            }
        }
    }

    override suspend fun countBySearchTokensAndTableTypes(
        query: String,
        tableTypes: List<String>
    ): Result<Int, Throwable> {
        return useReadDatabase {
            data.count {
                where {
                    this.searchTokens.search(query).and(this.tableType.inList(tableTypes))
                }
            }
        }
    }


    @OptIn(OrPredicateUsage::class)
    override suspend fun countTussResourcesBySearchTokens(
        query: String
    ): Result<Int, Throwable> = useReadDatabase {
        data.count {
            where {
                this.searchTokens.search(query).and(this.isOriginalTuss.eq(true) or this.tussCode.isNull())
                    .and(this.active.eq(true))
            }
        }
    }

    @OptIn(OrPredicateUsage::class)
    override suspend fun countPrimaryTussResourcesBySearchTokens(
        query: String?
    ): Result<Int, Throwable> = useReadDatabase {
        data.count {
            where {
                scope(this.isOriginalTuss.eq(true) or this.tussCode.isNull())
                    .and(this.tableType.eq(DEFAULT_PRIMARY_TUSS_TABLE))
                    .let { predicate ->
                        if (query == null)
                            predicate
                        else
                            predicate.and(this.searchTokens.search(query))
                    }
            }
        }
    }

    private fun Predicate.andOnlyActive(onlyActive: Boolean) =
        if (onlyActive) this.and(HealthcareResourceModelDataService.FieldOptions().active.eq(true)) else this

}
