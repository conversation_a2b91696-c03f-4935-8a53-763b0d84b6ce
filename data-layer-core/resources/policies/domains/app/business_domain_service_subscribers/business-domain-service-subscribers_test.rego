package app.business_domain_service_subscribers_test

import data.app.business_domain_service_subscribers
import rego.v1

test_unauthenticated_view_count_models_allow if {
	resources = [
		"PersonClinicalAccount",
		"ProductModel",
		"ProductPriceListingModel",
		"PriceListingModel",
		"HealthDeclaration",
		"CompanyProductConfigurationModel",
		"StaffModel",
		"RefundCostInfoModel",
		"CompanyRefundCostInfoModel",
		"PreActivationPaymentModel",
		"PersonGracePeriod",
		"InvoiceLiquidationModel",
	]

	every resource in resources {
		{1, 2} == business_domain_service_subscribers.allow with input as {"cases": [
			{
				"index": 1,
				"action": "view",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "count",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_unauthenticated_CRU_count_models_allow if {
	resources = [
		"BeneficiaryOnboardingModel",
		"B2bBatchInvoiceReportModel",
		"BillingAccountablePartyModel",
		"PersonBillingAccountablePartyModel",
		"PersonModel",
		"FileVault",
		"CompanyModel",
		"CompanyContractModel",
		"GenericFileVault",
		"CompanySubContractModel",
		"CompanyStaffModel",
		"MemberModel",
	]

	every resource in resources {
		{1, 2, 3, 4} == business_domain_service_subscribers.allow with input as {"cases": [
			{
				"index": 1,
				"action": "view",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "count",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 3,
				"action": "update",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 4,
				"action": "create",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_unauthenticated_view_create_count_InvoiceGroupTaxReceipt_allow if {
	{1, 2, 3} == business_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "InvoiceGroupTaxReceiptModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "InvoiceGroupTaxReceiptModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "InvoiceGroupTaxReceiptModel"},
		},
	]}
}

test_unauthenticated_view_create_count_HealthDeclaration_allow if {
	{1, 2, 3} == business_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "HealthDeclaration"},
		},
	]}
}

test_unauthenticated_view_create_count_PersonRegistration_allow if {
	{1, 2, 3} == business_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
	]}
}

test_unauthenticated_view_create_count_PersonOnboarding_allow if {
	{1, 2, 3} == business_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
	]}
}

test_unauthenticated_CRU_models_allow if {
	resources = [
		"CompanyActivationFilesModel",
		"MemberInvoiceModel",
		"InvoicePaymentModel",
		"ResourceSignTokenModel",
        "ItauPaymentModel",
        "CancelPaymentOnAcquirerScheduleModel",
		"PaymentDetailModel",
		"BolepixPaymentDetailModel",
		"PixPaymentDetailModel",
		"SimpleCreditCardPaymentDetailModel",
		"BoletoPaymentDetailModel",
		"InvoiceItemModel",
		"MemberInvoiceGroupModel",
		"CompanyProductPriceListingModel",
		"MemberProductChangeScheduleModel",
		"MemberTelegramTrackingModel",
	]

	every resource in resources {
		{1, 2, 3} == business_domain_service_subscribers.allow with input as {"cases": [
			{
				"index": 1,
				"action": "view",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "create",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 3,
				"action": "update",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_unauthenticated_view_count_update_OngoingCompanyDeal_allow if {
	{1, 2, 3} == business_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
	]}
}

test_unauthenticated_create_PersonLogin_allow if {
	{1} == business_domain_service_subscribers.allow with input as {"cases": [{
		"index": 1,
		"action": "create",
		"subject": {"opaType": "Unauthenticated"},
		"resource": {"opaType": "PersonLoginModel"},
	}]}
}

test_unauthenticated_CRUD_count_CassiMember_allow if {
	{1, 2, 3, 4, 5} == business_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 4,
			"action": "delete",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 5,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CassiMemberModel"},
		},
	]}
}

test_unauthenticated_CRUD_count_MemberContract_allow if {
	{1, 2, 3, 4, 5} == business_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberContractModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberContractModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberContractModel"},
		},
		{
			"index": 4,
			"action": "delete",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberContractModel"},
		},
		{
			"index": 5,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberContractModel"},
		},
	]}
}

test_unauthenticated_CRUD_count_MemberContractTerm_allow if {
	{1, 2, 3, 4, 5} == business_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
		{
			"index": 4,
			"action": "delete",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
		{
			"index": 5,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
	]}
}

test_unauthenticated_CRUD_count_BeneficiaryCompiledView_allow if {
	{1, 2, 3, 4, 5} == business_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BeneficiaryCompiledViewModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BeneficiaryCompiledViewModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BeneficiaryCompiledViewModel"},
		},
		{
			"index": 4,
			"action": "delete",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BeneficiaryCompiledViewModel"},
		},
		{
			"index": 5,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BeneficiaryCompiledViewModel"},
		},
	]}
}

test_unauthenticated_CRU_FileVault_term_file_allow if {
	{1, 2, 3} == business_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "FileVault", "domain": "member", "namespace": "term"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "FileVault", "domain": "member", "namespace": "term"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "FileVault", "domain": "member", "namespace": "term"},
		},
	]}
}

test_unauthenticated_CR_GenericFileVault_company_activation_files_allow if {
	{1, 2} == business_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "GenericFileVault", "namespace": "company_activation_files"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "GenericFileVault", "namespace": "company_activation_files"},
		},
	]}
}
