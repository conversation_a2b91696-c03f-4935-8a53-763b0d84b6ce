package br.com.alice.moneyin.services.internal

import br.com.alice.common.PaymentMethod
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InvoicePaymentSource
import br.com.alice.moneyin.models.AcquirerCreatePaymentResponse
import br.com.alice.moneyin.models.AcquirerGetPaymentResponse
import br.com.alice.moneyin.models.PaymentRequestInput
import br.com.alice.moneyin.models.PaymentStatus
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import kotlin.test.Test

class AcquirerOrchestratorServiceTest {

    private val iuguPaymentService = mockk<IuguPaymentService>()
    private val itauPaymentService = mockk<ItauPaymentServiceProxy>()
    private val service = AcquirerOrchestratorService(iuguPaymentService, itauPaymentService)

    private val requestInput = mockk<PaymentRequestInput>(relaxed = true)

    @Test
    fun `#getDefaultPaymentSource should return IUGU when no feature flag is set`(): Unit = runBlocking {
        val result = service.getDefaultPaymentSource(requestInput.payer.id)

        assert(result == InvoicePaymentSource.IUGU)
    }

    @Test
    fun `#getDefaultPaymentSource should return ITAU when feature flag is set`(): Unit = runBlocking {
        withFeatureFlag(
            FeatureNamespace.MONEY_IN, "billing-accountable-to-use-itau", listOf(requestInput.payer.id.toString())
        ) {
            val result = service.getDefaultPaymentSource(requestInput.payer.id)

            assert(result == InvoicePaymentSource.ITAU)
        }
    }

    @Test
    fun `#getDefaultPaymentSource should return ITAU when feature flag is set to ITAU`(): Unit = runBlocking {
        withFeatureFlag(
            FeatureNamespace.MONEY_IN, "default-payment-source", InvoicePaymentSource.ITAU.name
        ) {
            val result = service.getDefaultPaymentSource(requestInput.payer.id)

            assert(result == InvoicePaymentSource.ITAU)
        }
    }

    @Test
    fun `#create should return failure when payment method is SIMPLE_CREDIT_CARD`(): Unit = runBlocking {
        every { requestInput.method } returns PaymentMethod.SIMPLE_CREDIT_CARD

        val result = service.create(requestInput)

        assertThat(result).isFailureOfType(IllegalArgumentException::class)
    }

    @Test
    fun `#create should call iuguPaymentService when default source is IUGU`(): Unit = runBlocking {
        withFeatureFlag(
            FeatureNamespace.MONEY_IN, "default-payment-source", InvoicePaymentSource.IUGU.name
        ) {
            val response = AcquirerCreatePaymentResponse(
                id = "123",
                status = PaymentStatus.PENDING,
                null,
                null,
                null
            )

            every { requestInput.method } returns PaymentMethod.BOLEPIX
            coEvery { iuguPaymentService.create(requestInput) } returns response

            val result = service.create(requestInput)

            assertThat(result).isSuccessWithData(response)

            coVerifyOnce { iuguPaymentService.create(any()) }
        }
    }

    @Test
    fun `#create should call iuguPaymentService when default source is ITAU`(): Unit = runBlocking {
        withFeatureFlag(
            FeatureNamespace.MONEY_IN, "default-payment-source", InvoicePaymentSource.ITAU.name
        ) {
            val response = AcquirerCreatePaymentResponse(
                id = "123",
                status = PaymentStatus.PENDING,
                null,
                null,
                null
            )

            every { requestInput.method } returns PaymentMethod.BOLEPIX
            coEvery { itauPaymentService.create(requestInput) } returns response

            val result = service.create(requestInput)

            assertThat(result).isSuccessWithData(response)

            coVerifyOnce { itauPaymentService.create(any()) }
        }
    }

    @Test
    fun `#create should call iuguPaymentService when billing is on FF`(): Unit = runBlocking {
        withFeatureFlag(
            FeatureNamespace.MONEY_IN, "billing-accountable-to-use-itau", listOf(requestInput.payer.id.toString())
        ) {
            val response = AcquirerCreatePaymentResponse(
                id = "123",
                status = PaymentStatus.PENDING,
                null,
                null,
                null
            )

            every { requestInput.method } returns PaymentMethod.BOLEPIX
            coEvery { itauPaymentService.create(requestInput) } returns response

            val result = service.create(requestInput)

            assertThat(result).isSuccessWithData(response)

            coVerifyOnce { itauPaymentService.create(any()) }
        }
    }

    @Test
    fun `#get should return failure when externalId is null`(): Unit = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment(
            externalId = null,
        )

        val result = service.get(invoicePayment)

        assertThat(result).isFailureOfType(IllegalArgumentException::class)
    }

    @Test
    fun `#get should call acquirer service with externalId when source is IUGU`(): Unit = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment(
            externalId = "123",
            source = InvoicePaymentSource.IUGU,
        )
        val response = AcquirerGetPaymentResponse(
            id = "123",
            status = PaymentStatus.PENDING,
            null,
            null,
            dueDate = LocalDate.now(),
            externalUrl = null,
            totalCents = 10
        )
        coEvery { iuguPaymentService.get("123") } returns response

        val result = service.get(invoicePayment)

        assertThat(result).isSuccessWithData(response)

        coVerifyOnce { iuguPaymentService.get(any()) }
    }

    @Test
    fun `#get should call acquirer service with externalId when source is ITAU`(): Unit = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment(
            externalId = "123",
            source = InvoicePaymentSource.ITAU,
        )
        val response = AcquirerGetPaymentResponse(
            id = "123",
            status = PaymentStatus.PENDING,
            null,
            null,
            dueDate = LocalDate.now(),
            externalUrl = null,
            totalCents = 10
        )
        coEvery { itauPaymentService.get("123") } returns response

        val result = service.get(invoicePayment)

        assertThat(result).isSuccessWithData(response)

        coVerifyOnce { itauPaymentService.get(any()) }
    }

    @Test
    fun `#cancel should return failure when externalId is null`(): Unit = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment(
            externalId = null,
        )

        val result = service.cancel(invoicePayment)

        assertThat(result).isFailureOfType(IllegalArgumentException::class)
    }

    @Test
    fun `#cancel should call acquirer service with externalId when source is IUGU`(): Unit = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment(
            externalId = "123",
            source = InvoicePaymentSource.IUGU
        )

        coEvery { iuguPaymentService.cancel("123") } returns true

        val result = service.cancel(invoicePayment)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { iuguPaymentService.cancel(any()) }
    }

    @Test
    fun `#cancel should call acquirer service with externalId when source is ITAU`(): Unit = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment(
            externalId = "123",
            source = InvoicePaymentSource.ITAU
        )

        coEvery { itauPaymentService.cancel("123") } returns true

        val result = service.cancel(invoicePayment)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { itauPaymentService.cancel(any()) }
    }
}
