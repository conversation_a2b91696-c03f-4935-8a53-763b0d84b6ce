package br.com.alice.nullvs.consumers.client

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.coFoldError
import br.com.alice.common.extensions.foldError
import br.com.alice.common.extensions.foldNotFound
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.NullvsIntegrationRecord
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.moneyin.event.BillingAccountablePartyAssignedEvent
import br.com.alice.moneyin.event.BillingAccountablePartyCreatedEvent
import br.com.alice.moneyin.event.BillingAccountablePartyUpdatedEvent
import br.com.alice.nullvs.clients.TotvsClientClient
import br.com.alice.nullvs.consumers.AutoRetryableConsumer
import br.com.alice.nullvs.converters.NullvsClientConverterService
import br.com.alice.nullvs.events.NullvsClientActivatedEvent
import br.com.alice.nullvs.events.NullvsClientBatchRequestEvent
import br.com.alice.nullvs.events.NullvsMemberBatchRequestEvent
import br.com.alice.nullvs.exceptions.ExternalClientNotFound
import br.com.alice.nullvs.exceptions.TotvsClientNotFoundByInternalIdException
import br.com.alice.nullvs.exceptions.TotvsMemberNotFoundByInternalIdException
import br.com.alice.nullvs.exceptions.TotvsParentMemberNotFoundByInternalIdException
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.NullvsIntegrationRecordCheck
import br.com.alice.nullvs.services.internals.NullvsIntegrationRecordService
import br.com.alice.nullvs.services.internals.TotvsMemberIntegrationService
import br.com.alice.nullvs.services.internals.dependency.NullvsDependencyService
import br.com.alice.person.client.MemberService
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.person.model.events.PersonUpdatedEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

class BillingAccountablePartyConsumer(
    private val nullvsClientConverterService: NullvsClientConverterService,
    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService,
    private val totvsMemberIntegrationService: TotvsMemberIntegrationService,
    private val memberService: MemberService,
    private val kafkaProducerService: KafkaProducerService,
    private val totvsClientClient: TotvsClientClient,
    private val nullvsDependencyService: NullvsDependencyService,
) : AutoRetryableConsumer(
    TotvsClientNotFoundByInternalIdException::class, TotvsMemberNotFoundByInternalIdException::class,
    TotvsParentMemberNotFoundByInternalIdException::class, ExternalClientNotFound::class
) {

    suspend fun syncBillingAccountablePartyFromCreate(event: BillingAccountablePartyCreatedEvent) =
        withSubscribersEnvironment {
            val payload = event.payload

            logger.info(
                "Consuming BillingAccountablePartyCreatedEvent",
                "billing_accountable_party" to payload.billingAccountableParty,
            )

            val metadata = Meta(
                eventId = event.messageId,
                eventName = event.name,
                internalModelName = InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                internalId = payload.billingAccountableParty.id,
                externalModelName = ExternalModelType.CLIENT,
                integratedAt = event.eventDate,
                originalTopic = MemberActivatedEvent.name,
                integrationEventName = BillingAccountablePartyCreatedEvent.name,
            )

            nullvsClientConverterService.generateCreationRequest(
                metadata,
                payload.billingAccountableParty
            ).let {
                kafkaProducerService.produce(NullvsClientBatchRequestEvent(it))
            }
            true.success()
        }

    suspend fun syncBillingAccountablePartyFromUpdate(event: BillingAccountablePartyUpdatedEvent) =
        withSubscribersEnvironment {
            val payload = event.payload

            logger.info(
                "Consuming BillingAccountablePartyUpdatedEvent",
                "billing_accountable_party" to payload.billingAccountableParty,
            )

            shouldUpdateClient(event)
        }

    private suspend fun shouldUpdateClient(event: BillingAccountablePartyUpdatedEvent): Result<ProducerResult, Throwable> {

        val meta = Meta(
            eventId = event.messageId,
            eventName = BillingAccountablePartyUpdatedEvent.name,
            internalModelName = InternalModelType.BILLING_ACCOUNTABLE_PARTY,
            internalId = event.payload.billingAccountableParty.id,
            externalModelName = ExternalModelType.CLIENT,
            integratedAt = event.eventDate,
            originalTopic = BillingAccountablePartyUpdatedEvent.name,
            integrationEventName = BillingAccountablePartyUpdatedEvent.name,
        )

        return nullvsIntegrationRecordService.findByInternalIdAndModel(
            event.payload.billingAccountableParty.id,
            InternalModelType.BILLING_ACCOUNTABLE_PARTY
        ).flatMap {
            nullvsClientConverterService.generateUpdateRequest(meta, event.payload.billingAccountableParty).let {
                kafkaProducerService.produce(NullvsClientBatchRequestEvent(it))
            }.success()
        }
            .coFoldError(NotFoundException::class to {
                logger.info(
                    "NullvsIntegrationRecordService.findByInternalId NotFoundException",
                    "internalId" to event.payload.billingAccountableParty,
                )

                val validation =
                    checkPreviousBillingActivation(event.payload.billingAccountableParty.id).get()

                if (validation.hasPreviousActivation) {

                    nullvsIntegrationRecordService.add(
                        NullvsIntegrationRecord(
                            internalId = validation.response!!.internalId!!.toUUID(),
                            internalModelName = InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                            externalModelName = ExternalModelType.CLIENT,
                            externalId = validation.response.clientCode,
                            integratedAt = LocalDateTime.now()
                        )
                    ).let { data ->
                        val record = data.get()
                        logger.info(
                            "BillingAccountablePartyConsumer::create record via callback",
                            "record_id" to record.id,
                            "record" to record,
                        )

                        nullvsClientConverterService.generateUpdateRequest(meta, event.payload.billingAccountableParty)
                            .let {
                                kafkaProducerService.produce(NullvsClientBatchRequestEvent(it))
                            }.success()

                    }
                } else {
                    nullvsClientConverterService.generateCreationRequest(meta, event.payload.billingAccountableParty)
                        .let {
                            kafkaProducerService.produce(NullvsClientBatchRequestEvent(it))
                        }.success()

                }
            })
    }

    suspend fun updateBeneficiaryTotvs(event: BillingAccountablePartyAssignedEvent) = withSubscribersEnvironment {
        val personId = event.payload.personId

        memberService.getCurrent(personId)
            .foldNotFound { Result.of { null } }
            .flatMap { member ->
                if (member == null) {
                    logger.info("Should ignore the update once the membership does not exist")
                    false.success()
                } else {
                    val metadata = Meta(
                        eventId = event.messageId,
                        eventName = event.name,
                        internalModelName = InternalModelType.MEMBER,
                        internalId = member.id,
                        externalModelName = ExternalModelType.BENEFICIARY,
                        integratedAt = event.eventDate,
                        originalTopic = PersonUpdatedEvent.name,
                        integrationEventName = NullvsMemberBatchRequestEvent.name,
                    )

                    logger.info(
                        "Creating NullvsMemberBatchRequest from PersonUpdatedEvent",
                        "metadata" to metadata,
                    )

                    if (member.status == MemberStatus.ACTIVE) {
                        totvsMemberIntegrationService.updateBeneficiaryTotvs(metadata, member)
                            .map {
                                logger.info(
                                    "NullvsMemberBatchRequest generated due to PersonUpdatedEvent",
                                    "payload" to it
                                )

                                kafkaProducerService.produce(NullvsMemberBatchRequestEvent(it))

                                true
                            }.foldError(TotvsMemberNotFoundByInternalIdException::class to {
                                if (it.message!!.contains(member.id.toString())) {

                                    logger.info(
                                        "This error will be ignored.",
                                        "ex" to it
                                    )

                                    true.success()
                                } else {
                                    it.failure()
                                }
                            })
                    } else {
                        logger.info(
                            "Member is pending, skipping the update",
                            "member_id" to member.id
                        )
                        false.success()
                    }

                }
            }
    }

    private suspend fun checkPreviousBillingActivation(billingAccountablePartyId: UUID) =
        totvsClientClient.getInfoByBillingAccountablePartyId(billingAccountablePartyId).flatMap {
            NullvsIntegrationRecordCheck(
                hasPreviousActivation = true,
                response = it
            ).success()
        }
            .flatMapError { NullvsIntegrationRecordCheck(hasPreviousActivation = false).success() }

    private fun shouldUseNullvsDependencySystem() =
        FeatureService.get(FeatureNamespace.NULLVS, "use_dependency_service", true)

    suspend fun reprocessClientDependencies(event: NullvsClientActivatedEvent) = withSubscribersEnvironment {
        if (shouldUseNullvsDependencySystem()) {
            logger.info(
                "NullvsClientActivatedEvent::reprocessClientDependencies - reprocessing client dependencies",
            )

            nullvsDependencyService.reprocessItsDependents(
                event.payload.billingAccountablePartyId,
                InternalModelType.BILLING_ACCOUNTABLE_PARTY,
            )
        } else {
            logger.info(
                "NullvsClientActivatedEvent::reprocessClientDependencies - disabled by FF.use_dependency_service",
            )

            true.success()
        }
    }

}
