package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.api.ops.converters.ClinicalStaffResponseConverter
import br.com.alice.api.ops.models.ClinicalStaffResponse
import br.com.alice.api.ops.models.ClinicalStaffWithAddresses
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.SpecialistStatus.ACTIVE
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.success
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class ClinicalStaffControllerTest : ControllerTestHelper() {
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val clinicalStaffController = ClinicalStaffController(
        healthProfessionalService,
    )

    private val specialty = TestModelFactory.buildMedicalSpecialty("Ortopedia")
    private val staffId = RangeUUID.generate()
    private val healthProfessional = TestModelFactory.buildHealthProfessional(
        staffId = staffId,
        specialtyId = specialty.id
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        clearMocks(
            healthProfessionalService,
        )
        module.single { clinicalStaffController }
    }

    @Test
    fun `#getClinicalStaffs should get healthCommunitySpecialist by range`() {
        coEvery { healthProfessionalService.getSpecialistsByRange(IntRange(0, 49)) } returns
                listOf(healthProfessional).success()

        coEvery { healthProfessionalService.countAllSpecialists() } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/clinical_staff?filter={}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: List<ClinicalStaffResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                val responseIds = content.map { it.id }
                assertThat(responseIds).contains(healthProfessional.id)
            }
        }
    }

    @Test
    fun `#getClinicalStaffs should get healthCommunitySpecialist by id`() {
        coEvery { healthProfessionalService.getByStaffIds(listOf(staffId)) } returns
                listOf(healthProfessional).success()

        coEvery {
            healthProfessionalService.countByNameAndId(
                ids = listOf(staffId)
            )
        } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/clinical_staff?filter={\"id\":[$staffId]}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: List<ClinicalStaffResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                val responseIds = content.map { it.id }
                assertThat(responseIds).contains(healthProfessional.id)
            }
        }
    }

    @Test
    fun `#getClinicalStaffs should get healthCommunitySpecialist by filter name, status and range`() {
        coEvery {
            healthProfessionalService.getByFilterAndRange(
                namePrefix = "b",
                statusFilter = ACTIVE,
                range = IntRange(0, 49),
            )
        } returns listOf(healthProfessional).success()

        coEvery { healthProfessionalService.countByFilter("b", ACTIVE) } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/clinical_staff?filter={\"q\"=\"b\",\"status\":\"ACTIVE\"}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: List<ClinicalStaffResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                val responseIds = content.map { it.id }
                assertThat(responseIds).contains(healthProfessional.id)
            }
        }
    }

    @Test
    fun `#getClinicalStaffs should get healthCommunitySpecialist by filter name and range`() {
        coEvery {
            healthProfessionalService.searchByNameAndIdWithRange(
                namePrefix = "b",
                range = IntRange(0, 49),
            )
        } returns listOf(healthProfessional).success()
        coEvery {
            healthProfessionalService.countByNameAndId(
                namePrefix = "b"
            )
        } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/clinical_staff?filter={\"q\"=\"b\"}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: List<ClinicalStaffResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                val responseIds = content.map { it.id }
                assertThat(responseIds).contains(healthProfessional.id)
            }
        }
    }

    @Test
    fun `#getClinicalStaffs should get healthCommunitySpecialist by filter status and range`() {
        coEvery {
            healthProfessionalService.getByFilterAndRange(
                namePrefix = "",
                statusFilter = ACTIVE,
                range = IntRange(0, 49),
            )
        } returns listOf(healthProfessional).success()

        coEvery { healthProfessionalService.countByFilter("", ACTIVE) } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/clinical_staff?filter={\"status\":\"ACTIVE\"}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: List<ClinicalStaffResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                val responseIds = content.map { it.id }
                assertThat(responseIds).contains(healthProfessional.id)
            }
        }
    }

    @Test
    fun `#getClinicalStaffs filters specialists by specialty id`() {
        val cardiology = TestModelFactory.buildMedicalSpecialty(
            name = "Cardiology",
        )

        coEvery {
            healthProfessionalService.findActivesByInternalAndExternalSpecialties(
                listOf(cardiology.id)
            )
        } returns listOf(healthProfessional).success()

        authenticatedAs(idToken, staff) {
            get("/clinical_staff?filter={\"specialty\":\"${cardiology.id}\"}") { response ->
                ResponseAssert.assertThat(response).isOKWithData(
                    listOf(
                        ClinicalStaffResponseConverter.convert(
                            ClinicalStaffWithAddresses(
                                healthProfessional
                            )
                        )
                    )
                )
            }
        }

        coVerify(exactly = 1) { healthProfessionalService.findActivesByInternalAndExternalSpecialties(any()) }
    }
}
