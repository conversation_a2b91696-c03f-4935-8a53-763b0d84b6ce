package br.com.alice.moneyin.ioc

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.rfc.HttpInvoker
import br.com.alice.common.rfc.Invoker
import br.com.alice.moneyin.MoneyInDomainConfiguration
import br.com.alice.moneyin.SERVICE_NAME
import br.com.alice.moneyin.client.*
import org.koin.core.qualifier.named
import org.koin.dsl.module

val MoneyInClientModule = module {

    val baseUrl = MoneyInDomainConfiguration.baseUrl()
    val invoker = HttpInvoker(DefaultHttpClient(timeoutInMillis = 20_000), "$baseUrl/rfc")

    single<Invoker>(named(SERVICE_NAME)) { invoker }
    single<InvoicesService> { InvoicesServiceClient(get(named(SERVICE_NAME))) }
    single<InvoicePaymentService> { InvoicePaymentServiceClient(get(named(SERVICE_NAME))) }
    single<InvoiceItemService> { InvoiceItemServiceClient(get(named(SERVICE_NAME))) }
    single<BillingAccountablePartyService> { BillingAccountablePartyServiceClient(get(named(SERVICE_NAME))) }
    single<MemberInvoiceGroupService> { MemberInvoiceGroupServiceClient(get(named(SERVICE_NAME))) }
    single<InvoiceLiquidationService> { InvoiceLiquidationServiceClient(get(named(SERVICE_NAME))) }
    single<MemberInvoiceGroupQueryService> { MemberInvoiceGroupQueryServiceClient(get(named(SERVICE_NAME))) }
    single<FinancialDataService> { FinancialDataServiceClient(get(named(SERVICE_NAME))) }
    single<InvoicePdfService> { InvoicePdfServiceClient(get(named(SERVICE_NAME))) }
    single<PreActivationPaymentService> { PreActivationPaymentServiceClient(get(named(SERVICE_NAME))) }
    single<PreActivationCompanyInvoiceService> { PreActivationCompanyInvoiceServiceClient(get(named(SERVICE_NAME))) }
    single<CompanyInvoiceDetailService> { CompanyInvoiceDetailServiceClient(get(named(SERVICE_NAME))) }
    single<CompanyInvoiceService> { CompanyInvoiceServiceClient(get(named(SERVICE_NAME))) }
    single<FirstPaymentScheduleService> { FirstPaymentScheduleServiceClient(get(named(SERVICE_NAME))) }
    single<MoneyInResourceSignTokenService> { MoneyInResourceSignTokenServiceClient(get(named(SERVICE_NAME))) }
    single<PortalUrlGeneratorService> { PortalUrlGeneratorServiceClient(get(named(SERVICE_NAME))) }
}
