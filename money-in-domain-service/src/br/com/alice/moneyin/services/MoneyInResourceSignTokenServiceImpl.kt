package br.com.alice.moneyin.services

import br.com.alice.common.UUIDv7
import br.com.alice.data.layer.models.ResourceSignTokenType
import br.com.alice.data.layer.services.impl.ResourceSignTokenServiceImpl
import br.com.alice.moneyin.client.MoneyInResourceSignTokenService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import java.time.Duration
import java.util.UUID

class MoneyInResourceSignTokenServiceImpl(
    private val resourceSignTokenServiceImpl: ResourceSignTokenServiceImpl
) : MoneyInResourceSignTokenService {

    override suspend fun createSignTokenForMoneyInBff(invoicePaymentId: UUID): Result<UUIDv7, Throwable> {
        return resourceSignTokenServiceImpl.generateResourceSignToken(
            resourceId = invoicePaymentId.toString(),
            resourceType = ResourceSignTokenType.INVOICE_PAYMENT,
        ).map { it.signUuid }
    }

    override suspend fun getSignTokenForMoneyInBff(invoicePaymentId: UUID): Result<UUIDv7, Throwable> =
        resourceSignTokenServiceImpl.findSignTokenByResourceIdAndResourceType(
            resourceId = invoicePaymentId.toString(),
            resourceType = ResourceSignTokenType.INVOICE_PAYMENT
        ).map { it.signUuid }

    override suspend fun isSignTokenValidForMoneyInBff(
        invoicePaymentId: UUID,
        signUuid: UUIDv7
    ): Result<Boolean, Throwable> {
        return resourceSignTokenServiceImpl.isSignTokenValid(
            signUuid = signUuid,
            resource = ResourceSignTokenType.INVOICE_PAYMENT,
            expirationDate = Duration.ofDays(90),
            resourceId = invoicePaymentId.toString(),
        )
    }

    override suspend fun softDeleteSignToken(resourceSignTokenId: UUID): Result<Boolean, Throwable> =
        resourceSignTokenServiceImpl.softDeleteSignToken(resourceSignTokenId)

}
