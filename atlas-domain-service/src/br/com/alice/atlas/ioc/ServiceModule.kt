package br.com.alice.atlas.ioc

import br.com.alice.atlas.SERVICE_NAME
import br.com.alice.atlas.ServiceConfig
import br.com.alice.atlas.consumers.ProductUpdatedConsumer
import br.com.alice.atlas.consumers.network_category_update.MedicalSpecialtyCreatedConsumer
import br.com.alice.atlas.consumers.network_category_update.MedicalSpecialtyUpdatedConsumer
import br.com.alice.atlas.controllers.SiteAccreditedNetworkCategoryController
import br.com.alice.atlas.services.AccreditedNetworkFlagshipService
import br.com.alice.atlas.services.AccreditedNetworkFlagshipServiceImpl
import br.com.alice.atlas.services.AccreditedProviderDetailsService
import br.com.alice.atlas.services.AccreditedProviderDetailsServiceImpl
import br.com.alice.atlas.services.AccreditedSearchListService
import br.com.alice.atlas.services.AccreditedSearchListServiceImpl
import br.com.alice.atlas.services.AccreditedService
import br.com.alice.atlas.services.AccreditedServiceImpl
import br.com.alice.atlas.services.AccreditedSpecialistService
import br.com.alice.atlas.services.AccreditedSpecialistServiceImpl
import br.com.alice.atlas.services.FlagshipsAccreditedLogicService
import br.com.alice.atlas.services.FlagshipsAccreditedLogicServiceImpl
import br.com.alice.atlas.services.SiteAccreditedNetworkCategoryService
import br.com.alice.atlas.services.SiteAccreditedNetworkCategoryServiceImpl
import br.com.alice.atlas.services.SiteAccreditedNetworkFiltersService
import br.com.alice.atlas.services.SiteAccreditedNetworkFiltersServiceImpl
import br.com.alice.atlas.services.SiteAccreditedNetworkService
import br.com.alice.atlas.services.SiteAccreditedNetworkServiceImpl
import br.com.alice.atlas.services.network_category_creation.SiteAccreditedNetworkCategoryFromMedicalSpecialtyService
import br.com.alice.atlas.services.network_category_creation.SiteAccreditedNetworkCategoryFromMedicalSpecialtyServiceImpl
import br.com.alice.atlas.services.network_category_creation.SiteAccreditedNetworkCategoryFromProviderUnitService
import br.com.alice.atlas.services.network_category_creation.SiteAccreditedNetworkCategoryFromProviderUnitServiceImpl
import br.com.alice.atlas.usecases.BuildFilterUseCase
import br.com.alice.atlas.usecases.FindAccreditedSearchAreaUseCase
import br.com.alice.atlas.usecases.FindFlagshipUseCase
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.googlemaps.clients.GoogleMapsClient
import br.com.alice.common.redis.CacheFactory
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig
import org.koin.dsl.module

val ServiceModule = module(createdAtStart = true) {

    // Configuration
    single { HoconApplicationConfig(ConfigFactory.load("application.conf")) }

    val cache = CacheFactory.newInstance("atlas-domain-service-cache")

    // Services
    single<AccreditedService> { AccreditedServiceImpl(cache, get(), get()) }
    single<FlagshipsAccreditedLogicService> { FlagshipsAccreditedLogicServiceImpl(get(), get()) }
    single<AccreditedProviderDetailsService> { AccreditedProviderDetailsServiceImpl(get(), get(), get(), get(), get()) }
    single<SiteAccreditedNetworkService> { SiteAccreditedNetworkServiceImpl(get(), get()) }
    single<SiteAccreditedNetworkCategoryService> { SiteAccreditedNetworkCategoryServiceImpl(get()) }
    single<AccreditedSpecialistService> { AccreditedSpecialistServiceImpl(get(), get(), get(), get(), get(), get()) }
    single<SiteAccreditedNetworkFiltersService> { SiteAccreditedNetworkFiltersServiceImpl(cache, get(), get()) }
    single<AccreditedNetworkFlagshipService> { AccreditedNetworkFlagshipServiceImpl(get()) }
    single<AccreditedSearchListService> { AccreditedSearchListServiceImpl(get(), get(), get(), get(), get()) }

    // Use Cases
    single { FindAccreditedSearchAreaUseCase(get()) }
    single { BuildFilterUseCase() }
    single { FindFlagshipUseCase(get(), get()) }

    // Non Injected Services
    single<SiteAccreditedNetworkCategoryFromProviderUnitService> {
        SiteAccreditedNetworkCategoryFromProviderUnitServiceImpl(
            get()
        )
    }
    single<SiteAccreditedNetworkCategoryFromMedicalSpecialtyService> {
        SiteAccreditedNetworkCategoryFromMedicalSpecialtyServiceImpl(
            get()
        )
    }

    //Servers
    loadServiceServers("br.com.alice.atlas.services")

    // Consumers
    single { ProductUpdatedConsumer(get()) }
    single { MedicalSpecialtyUpdatedConsumer(get()) }
    single { MedicalSpecialtyCreatedConsumer(get()) }

    //Controllers
    single { HealthController(SERVICE_NAME) }
    single { SiteAccreditedNetworkCategoryController(get()) }

    // Using custom Google Maps Key
    single { GoogleMapsClient(ServiceConfig.googleMapsKey) }
}
