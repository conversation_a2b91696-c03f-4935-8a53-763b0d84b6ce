plugins {
    kotlin
    application
    id("com.github.johnrengelman.shadow")
    id("com.google.cloud.tools.jib")
    id("org.sonarqube")
}

group = "br.com.alice.member-onboarding-domain-service"
version = aliceMemberOnboardingDomainServiceVersion

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    main {
        kotlin.sourceDirs = files("src", "${layout.buildDirectory}/generated/source/kotlin")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:member-onborarding-domain-service")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
        property("sonar.exclusions", "**/*.java")
    }
}

tasks {
    shadowJar {
        isZip64 = true
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":common-kafka"))
    implementation(project(":common-service"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:health-plan-domain-service-data-package"))
	implementation(project(":data-packages:product-domain-service-data-package"))
	implementation(project(":data-packages:staff-domain-service-data-package"))
	implementation(project(":data-packages:person-domain-service-data-package"))
	implementation(project(":data-packages:member-onboarding-domain-service-data-package"))
    implementation(project(":data-packages:membership-domain-service-data-package"))
    implementation(project(":data-packages:health-condition-domain-service-data-package"))
	implementation(project(":data-packages:marauders-map-domain-service-data-package"))
    implementation(project(":data-packages:ehr-domain-service-data-package"))
    implementation(project(":data-packages:business-domain-service-data-package"))
    implementation(project(":data-packages:sales-channel-domain-service-data-package"))
    implementation(project(":data-packages:health-logic-domain-service-data-package"))
    implementation(project(":data-packages:clinical-account-domain-service-data-package"))
    implementation(project(":feature-config-domain-client"))
    implementation(project(":health-plan-domain-client"))
    implementation(project(":member-onboarding-domain-client"))
    implementation(project(":membership-domain-client"))
    implementation(project(":marauders-map-domain-client"))
    implementation(project(":health-logic-domain-client"))
    implementation(project(":person-domain-client"))
    implementation(project(":staff-domain-client"))
    implementation(project(":app-content-domain-client"))
    implementation(project(":business-domain-client"))
    implementation(project(":sales-channel-domain-client"))
    implementation(project(":clinical-account-domain-client"))



    ktor2Dependencies()

    testImplementation(project(":data-layer-common-tests"))
    testImplementation(project(":common-tests"))
    test2Dependencies()
}
