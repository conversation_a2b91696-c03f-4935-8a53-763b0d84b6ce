package br.com.alice.api.moneyin.controllers.converters

import br.com.alice.api.moneyin.model.InvoiceItemResponse
import br.com.alice.api.moneyin.model.InvoicePaymentResponseV2
import br.com.alice.api.moneyin.model.InvoicePaymentStatusResponse
import br.com.alice.api.moneyin.model.InvoiceResponse
import br.com.alice.common.core.extensions.toCNPJMask
import br.com.alice.common.core.extensions.toCPFMask
import br.com.alice.common.extensions.money
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InvoiceBreakdown
import br.com.alice.moneyin.helpers.BoletoAndPixHelpers
import br.com.alice.moneyin.models.BankSlipInfo
import br.com.alice.moneyin.models.PixInfo
import com.github.kittinunf.result.success
import io.mockk.every
import io.mockk.mockkObject
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import java.math.BigDecimal
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class InvoiceResponseConverterTest {

    @Test
    fun `#convert should take an invoice and person then return a InvoiceResponse`() {
        val invoice = TestModelFactory.buildMemberInvoice(
            invoiceBreakdown = InvoiceBreakdown(
                totalAmount = BigDecimal.valueOf(100),
                productPrice = BigDecimal.valueOf(120),
                promoCode = BigDecimal.valueOf(-20)
            )
        )
        val person = TestModelFactory.buildPerson()

        val result = InvoiceResponseConverter.convert(invoice, person)
        val expectedResult = InvoiceResponse(
            firstName = person.firstName,
            lastName = person.lastName,
            nationalId = person.nationalId,
            amount = invoice.totalAmount.money,
            referenceDate = invoice.referenceDate,
            invoiceItems = listOf(
                InvoiceItemResponse(BigDecimal.valueOf(-20), "Código Promocional"),
                InvoiceItemResponse(BigDecimal.valueOf(120), "Preço do Produto"),
            ),
        )

        assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#convertV2 should take an invoice and person then return a InvoiceResponse`() {
        val invoicePayment = TestModelFactory.buildInvoicePayment(
            paymentDetail = TestModelFactory.buildBoletoPaymentDetail()
        )
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val pixInfo = PixInfo()
        val boletoInfo = BankSlipInfo()

        mockkObject(BoletoAndPixHelpers)
        every { BoletoAndPixHelpers.getPixAndBoletoInfo(any()) } returns Pair(
            pixInfo,
            boletoInfo
        ).success()

        val result = InvoiceResponseConverter.convertToV2(invoicePayment, memberInvoiceGroup, billingAccountableParty)

        assertThat(result).isEqualTo(
            InvoicePaymentResponseV2(
                id = invoicePayment.id,
                referenceDate = memberInvoiceGroup.referenceDate,
                method = invoicePayment.method,
                amount = invoicePayment.amount.money,
                status = InvoicePaymentStatusResponse.valueOf(invoicePayment.status.name),
                dueDate = invoicePayment.dueDateFromDetail?.toLocalDate(),
                approvedAt = invoicePayment.approvedAt?.toLocalDate(),
                emissionDate = invoicePayment.createdAt.toLocalDate(),
                billingAccountableName = billingAccountableParty.fullNameConsideringType,
                billingAccountableAddress = billingAccountableParty.address.toString(),
                billingAccountableNationalId = billingAccountableParty.nationalId.toCNPJMask().toCPFMask(),
                invoiceType = memberInvoiceGroup.type,
                pixCopyAndPaste = pixInfo.copyAndPaste,
                pixQrCodeBase64 = pixInfo.qrCodeBase64,
                pixImageUrl = pixInfo.qrCodeImageUrl,
                barcodeBase64 = boletoInfo.barcodeBase64,
                barcodeWritableLine = boletoInfo.digitableLine,
                boletoImageUrl = boletoInfo.barcodeImageUrl,
                receiverName = "ALICE",
                receiverNationalId = "34.266.553/0001-02",
                receiverAddress = " Av. Reboucas, 3535, São Paulo",
            )

        )
    }
}
