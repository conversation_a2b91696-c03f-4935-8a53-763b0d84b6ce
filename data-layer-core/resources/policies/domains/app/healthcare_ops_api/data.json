{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["PersonModel"], "actions": ["view"]}, {"resources": ["Lead"], "actions": ["create", "update"]}, {"resources": ["HealthDeclaration", "PersonOnboardingModel", "PersonRegistrationModel", "PromoCodeModel"], "actions": ["view", "create", "update"]}], "branches": [{"conditions": ["${resource.email} == ${subject.key}"], "allow": [{"resources": ["StaffModel", "Lead"], "actions": ["view"]}]}, {"conditions": ["${resource.nationalId} == ${subject.key}"], "allow": [{"resources": ["PersonModel", "Lead", "PersonLoginModel"], "actions": ["view", "create", "update"]}]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= OPS || ${subject.role} @= B2B_OPS"], "allow": [{"resources": ["PersonContractualRiskModel", "NullvsIntegrationLogModel", "ProductOrderModel", "MemberProductChangeScheduleModel", "StaffModel", "ProductBundleModel", "OnboardingBackgroundCheckModel", "Lead", "AppointmentScheduleModel", "Channel", "SalesFirm", "SalesFirmStaff", "BeneficiaryCompiledViewModel"], "actions": ["view", "count"]}, {"resources": ["HealthDeclaration"], "actions": ["view", "count", "create"]}, {"resources": ["PersonModel", "OngoingCompanyDeal"], "actions": ["view", "update", "count"]}, {"resources": ["MemberTelegramTrackingModel", "MemberLifeCycleEventsModel"], "actions": ["view", "update", "create"]}, {"resources": ["PersonPreferencesModel", "MemberModel", "PersonOnboardingModel", "OnboardingContractModel", "Beneficiary<PERSON><PERSON>l", "CassiMemberModel", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "InvoicePaymentModel", "CancelPaymentOnAcquirerScheduleModel", "ResourceSignTokenModel", "ItauPaymentModel", "PaymentDetailModel", "BoletoPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "PixPaymentDetailModel", "BolepixPaymentDetailModel", "InvoiceItemModel", "BillingAccountablePartyModel", "InvoiceLiquidationModel", "MemberInvoiceModel", "MemberInvoiceGroupModel", "ProductModel", "ProductPriceListingModel", "PriceListingModel", "ProductPriceAdjustmentModel", "PersonBillingAccountablePartyModel", "CompanyModel", "CompanyContractModel", "CompanySubContractModel", "Generic<PERSON><PERSON><PERSON><PERSON>", "CompanyStaffModel", "MemberProductPriceModel", "UpdatedPersonContactInfoTempModel", "FileVault", "PersonLoginModel", "ResourceSignTokenModel"], "actions": ["view", "update", "create", "count"]}, {"resources": ["InsurancePortabilityHealthInsuranceModel", "InsurancePortabilityRequestModel", "InsurancePortabilityRequestFileModel", "CompanyProductPriceListingModel", "LegalGuardianAssociationModel", "LegalGuardianInfoTempModel", "MemberContractModel", "MemberContractTermModel"], "actions": ["view", "count", "create", "update", "delete"]}], "branches": [{"conditions": ["${resource.domain} == member", "${resource.namespace} == documents"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}, {"conditions": ["${resource.domain} == member", "${resource.namespace} == contract"], "allow": [{"resources": ["FileVault"], "actions": ["view", "update", "create"]}]}, {"conditions": ["${resource.namespace} == portability"], "allow": [{"resources": ["FileVault"], "actions": ["view", "update", "create"]}]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= OPS"], "allow": [{"resources": ["ProviderModel", "DeviceModel"], "actions": ["view"]}, {"resources": ["MemberRegistrationModel"], "actions": ["update", "create"]}, {"resources": ["PersonRegistrationModel", "PersonModel", "Lead", "PersonLoginModel", "OnboardingBackgroundCheckModel", "ProductOrderModel", "PromoCodeModel"], "actions": ["view", "update", "create", "count"]}, {"resources": ["PriceListingModel"], "actions": ["delete"]}], "branches": [{"conditions": ["${resource.type} == HEALTH_DECLARATION"], "allow": [{"resources": ["AppointmentScheduleModel"], "actions": ["view", "update", "create", "count"]}]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= CX_OPS"], "allow": [{"resources": ["MemberProductChangeScheduleModel", "InvoicePaymentModel", "ItauPaymentModel", "PaymentDetailModel", "BoletoPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "PixPaymentDetailModel", "BolepixPaymentDetailModel", "InvoiceItemModel", "BillingAccountablePartyModel", "InvoiceLiquidationModel", "MemberInvoiceModel", "MemberInvoiceGroupModel", "ResourceSignTokenModel"], "actions": ["view", "count"]}, {"resources": ["StaffModel", "ProductModel", "ProductBundleModel", "ProviderModel", "ProductOrderModel", "PersonModel", "DeviceModel", "PersonOnboardingModel", "PersonRegistrationModel", "MemberModel", "Lead", "PersonLoginModel", "ProductPriceListingModel", "PriceListingModel", "MemberProductPriceModel", "HealthDeclaration"], "actions": ["view"]}], "branches": [{"conditions": ["${resource.type} == HEALTH_DECLARATION"], "allow": [{"resources": ["AppointmentScheduleModel"], "actions": ["view"]}]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= CHIEF_DIGITAL_CARE_NURSE || ${subject.role} @= NAVIGATOR || ${subject.role} @= QUALITY_NURSE"], "allow": [{"resources": ["AppointmentScheduleModel", "BillingAccountablePartyModel", "BolepixPaymentDetailModel", "BoletoPaymentDetailModel", "CassiMemberModel", "CompanyProductPriceListingModel", "HealthDeclaration", "InvoiceItemModel", "InvoiceLiquidationModel", "InvoicePaymentModel", "CancelPaymentOnAcquirerScheduleModel", "ResourceSignTokenModel", "ItauPaymentModel", "Lead", "MemberModel", "MemberLifeCycleEventsModel", "MemberInvoiceModel", "MemberInvoiceGroupModel", "MemberRegistrationModel", "PaymentDetailModel", "PersonModel", "PersonBillingAccountablePartyModel", "PersonLoginModel", "PersonOnboardingModel", "PersonPreferencesModel", "PersonRegistrationModel", "PixPaymentDetailModel", "PriceListingModel", "ProductModel", "ProductOrderModel", "ProductPriceAdjustmentModel", "ProductPriceListingModel", "SimpleCreditCardPaymentDetailModel", "UpdatedPersonContactInfoTempModel", "ResourceSignTokenModel"], "actions": ["view", "count", "create", "update"]}, {"resources": ["Beneficiary<PERSON><PERSON>l", "BeneficiaryCompiledViewModel", "CompanyModel", "CompanyActivationFilesModel", "CompanyContractModel", "CompanySubContractModel", "DeviceModel", "MemberProductChangeScheduleModel", "MemberProductPriceModel", "MemberProductPriceAdjustmentModel", "OnboardingBackgroundCheckModel", "OnboardingContractModel", "OngoingCompanyDeal", "ProductBundleModel", "ProviderModel", "Generic<PERSON><PERSON><PERSON><PERSON>", "StaffModel"], "actions": ["view", "count"]}, {"resources": ["InsurancePortabilityHealthInsuranceModel", "InsurancePortabilityRequestModel", "InsurancePortabilityRequestFileModel", "LegalGuardianAssociationModel", "LegalGuardianInfoTempModel"], "actions": ["view", "count", "create", "update", "delete"]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= RISK_NURSE"], "allow": [{"resources": ["AppointmentScheduleModel", "Beneficiary<PERSON><PERSON>l", "BeneficiaryCompiledViewModel", "CassiMemberModel", "CompanyModel", "CompanyActivationFilesModel", "CompanyContractModel", "CompanySubContractModel", "DeviceModel", "Lead", "MemberModel", "MemberProductPriceModel", "MemberProductPriceAdjustmentModel", "MemberRegistrationModel", "OnboardingBackgroundCheckModel", "OnboardingContractModel", "OngoingCompanyDeal", "PersonModel", "PersonBillingAccountablePartyModel", "PersonLoginModel", "PersonOnboardingModel", "PersonRegistrationModel", "PersonPreferencesModel", "PriceListingModel", "ProductModel", "ProductBundleModel", "ProductOrderModel", "ProductPriceAdjustmentModel", "ProductPriceListingModel", "ProviderModel", "Generic<PERSON><PERSON><PERSON><PERSON>", "StaffModel", "MemberContractModel", "MemberContractTermModel", "FileVault"], "actions": ["view", "count"]}, {"resources": ["BillingAccountablePartyModel", "HealthDeclaration", "PromoCodeModel", "UpdatedPersonContactInfoTempModel", "InvoicePaymentModel", "CancelPaymentOnAcquirerScheduleModel", "ResourceSignTokenModel", "ItauPaymentModel", "PaymentDetailModel", "BoletoPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "PixPaymentDetailModel", "BolepixPaymentDetailModel", "InvoiceItemModel", "InvoiceLiquidationModel", "MemberInvoiceModel", "MemberInvoiceGroupModel", "ResourceSignTokenModel"], "actions": ["view", "count", "update", "create"]}, {"resources": ["InsurancePortabilityHealthInsuranceModel", "InsurancePortabilityRequestModel", "InsurancePortabilityRequestFileModel", "LegalGuardianAssociationModel", "LegalGuardianInfoTempModel"], "actions": ["view", "count", "update", "create", "delete"]}], "branches": [{"conditions": ["${resource.type} == HEALTH_DECLARATION"], "allow": [{"resources": ["AppointmentScheduleModel"], "actions": ["update", "create"]}]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= HEALTH_OPS || ${subject.role} == HEALTH_OPS_MULTI"], "allow": [{"resources": ["StaffModel", "ProductModel", "ProductBundleModel", "ProviderModel", "ProductOrderModel", "DeviceModel", "PersonOnboardingModel", "ProductPriceListingModel", "PriceListingModel", "MemberProductPriceModel", "HealthDeclaration"], "actions": ["view"]}, {"resources": ["PersonRegistrationModel", "MemberModel", "PersonModel", "Lead", "PersonLoginModel"], "actions": ["view", "create", "update"]}, {"resources": ["OutcomeConf", "HealthDemandMonitoring", "CassiMemberModel"], "actions": ["view", "create", "update", "count"]}, {"resources": ["InvoicePaymentModel", "CancelPaymentOnAcquirerScheduleModel", "ResourceSignTokenModel", "ItauPaymentModel", "PaymentDetailModel", "BoletoPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "PixPaymentDetailModel", "BolepixPaymentDetailModel", "InvoiceItemModel", "BillingAccountablePartyModel", "InvoiceLiquidationModel", "MemberInvoiceModel", "MemberInvoiceGroupModel", "ResourceSignTokenModel"], "actions": ["view", "count"]}], "branches": [{"conditions": ["${resource.type} == HEALTH_DECLARATION"], "allow": [{"resources": ["AppointmentScheduleModel"], "actions": ["view"]}]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= PRODUCT_TECH"], "allow": [{"resources": ["StaffModel", "BeneficiaryHubspotModel", "ProductBundleModel", "ProviderModel", "ProviderUnitModel", "StructuredAddress", "ContactModel", "OnboardingContractModel", "MemberProductPriceAdjustmentModel", "SalesFirm", "SalesFirmStaff"], "actions": ["view"]}, {"resources": ["NullvsIntegrationLogModel", "MemberContractModel", "MemberContractTermModel", "FileVault", "AppointmentScheduleModel", "BeneficiaryCompiledViewModel", "MemberProductChangeScheduleModel"], "actions": ["view", "count"]}, {"resources": ["HealthDeclaration"], "actions": ["view", "count", "create"]}, {"resources": ["CompanyActivationFilesModel"], "actions": ["view", "update", "count"]}, {"resources": ["MemberTelegramTrackingModel", "MemberLifeCycleEventsModel"], "actions": ["view", "update", "create"]}, {"resources": ["PersonOnboardingModel", "PersonRegistrationModel", "MemberModel", "PersonModel", "Lead", "PersonLoginModel", "OnboardingContractModel", "OnboardingBackgroundCheckModel", "ProductOrderModel", "PromoCodeModel", "InsurancePortabilityRequestModel", "InsurancePortabilityRequestFileModel", "BillingAccountablePartyModel", "PersonBillingAccountablePartyModel", "PersonPreferencesModel", "ProductPriceListingModel", "MemberProductPriceModel", "OngoingCompanyDeal", "Beneficiary<PERSON><PERSON>l", "CassiMemberModel", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "ProductModel", "ProductPriceAdjustmentModel", "UpdatedPersonContactInfoTempModel", "InvoicePaymentModel", "CancelPaymentOnAcquirerScheduleModel", "ResourceSignTokenModel", "ItauPaymentModel", "PaymentDetailModel", "BoletoPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "PixPaymentDetailModel", "BolepixPaymentDetailModel", "InvoiceItemModel", "BillingAccountablePartyModel", "InvoiceLiquidationModel", "MemberInvoiceModel", "MemberInvoiceGroupModel", "CompanyContractModel", "CompanySubContractModel", "CompanyModel", "Generic<PERSON><PERSON><PERSON><PERSON>", "PersonHealthConditionContractualRiskModel", "ResourceSignTokenModel"], "actions": ["view", "update", "count", "create"]}, {"resources": ["PriceListingModel", "LegalGuardianInfoTempModel", "LegalGuardianAssociationModel", "InsurancePortabilityHealthInsuranceModel", "InsurancePortabilityRequestModel", "InsurancePortabilityRequestFileModel", "CompanyProductPriceListingModel"], "actions": ["view", "create", "update", "count", "delete"]}], "branches": [{"conditions": ["${resource.type} == HEALTH_DECLARATION"], "allow": [{"resources": ["AppointmentScheduleModel"], "actions": ["view", "update", "create", "count"]}]}, {"conditions": ["${resource.namespace} == company_activation_files"], "allow": [{"resources": ["Generic<PERSON><PERSON><PERSON><PERSON>"], "actions": ["view", "create"]}]}, {"conditions": ["${subject.role} == PRODUCT_TECH_HEALTH"], "allow": [{"resources": ["PersonContractualRiskModel", "BeneficiaryMacoModel", "CompanyContractMacoModel", "StandardCostModel"], "actions": ["view", "count", "create", "update"]}]}]}, {"conditions": ["${subject.opaType} == StaffModel"], "allow": [{"resources": ["Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "CassiMemberModel", "CompanyModel", "CompanyContractModel", "CompanySubContractModel", "Generic<PERSON><PERSON><PERSON><PERSON>", "CompanyActivationFilesModel", "OngoingCompanyDeal", "SalesFirm", "SalesFirmStaff", "FileVault"], "actions": ["view", "count"]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= MEMBER_OPS"], "allow": [{"resources": ["OnboardingBackgroundCheckModel", "StaffModel", "ProductBundleModel", "ProviderModel", "DeviceModel", "HealthDeclaration"], "actions": ["view"]}, {"resources": ["ProductModel", "MemberProductChangeScheduleModel", "AppointmentScheduleModel", "CompanyModel", "CompanyContractModel", "CompanySubContractModel", "Generic<PERSON><PERSON><PERSON><PERSON>"], "actions": ["view", "count"]}, {"resources": ["PersonOnboardingModel", "PersonRegistrationModel", "PersonModel", "MemberModel", "Lead", "PersonLoginModel", "ProductOrderModel", "PromoCodeModel", "PersonPreferencesModel", "MemberProductPriceModel", "BillingAccountablePartyModel", "PersonBillingAccountablePartyModel", "ProductPriceListingModel", "MemberProductPriceAdjustmentModel", "OnboardingContractModel", "UpdatedPersonContactInfoTempModel", "CassiMemberModel", "Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel"], "actions": ["view", "create", "update", "count"]}, {"resources": ["PriceListingModel", "ProductPriceAdjustmentModel", "LegalGuardianAssociationModel", "LegalGuardianInfoTempModel", "InsurancePortabilityHealthInsuranceModel", "InsurancePortabilityRequestModel", "InsurancePortabilityRequestFileModel"], "actions": ["view", "create", "update", "count", "delete"]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= CHIEF_RISK || ${subject.role} @= MED_EX"], "allow": [{"resources": ["AppointmentScheduleModel", "Beneficiary<PERSON><PERSON>l", "BeneficiaryCompiledViewModel", "CompanyModel", "CompanyActivationFilesModel", "CompanyContractModel", "CompanyProductPriceListingModel", "CompanySubContractModel", "DeviceModel", "MemberProductPriceAdjustmentModel", "OngoingCompanyDeal", "ProductModel", "ProductBundleModel", "ProductPriceAdjustmentModel", "ProviderModel", "Generic<PERSON><PERSON><PERSON><PERSON>", "StaffModel", "MemberInvoiceModel", "MemberInvoiceGroupModel", "InvoicePaymentModel", "CancelPaymentOnAcquirerScheduleModel", "ResourceSignTokenModel", "ItauPaymentModel", "InvoiceItemModel", "InvoiceLiquidationModel", "MemberProductChangeScheduleModel", "MemberContractModel", "MemberContractTermModel", "ResourceSignTokenModel"], "actions": ["view", "count"]}, {"resources": ["PersonContractualRiskModel"], "actions": ["view", "create", "count"]}, {"resources": ["BillingAccountablePartyModel", "CassiMemberModel", "HealthDeclaration", "Lead", "MemberModel", "MemberProductPriceModel", "MemberRegistrationModel", "OnboardingBackgroundCheckModel", "OnboardingContractModel", "PaymentDetailModel", "PersonModel", "PersonBillingAccountablePartyModel", "PersonLoginModel", "PersonOnboardingModel", "PersonRegistrationModel", "PersonPreferencesModel", "PriceListingModel", "ProductOrderModel", "ProductPriceListingModel", "PromoCodeModel", "BoletoPaymentDetailModel", "BolepixPaymentDetailModel", "PixPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "UpdatedPersonContactInfoTempModel", "BeneficiaryMacoModel", "CompanyContractMacoModel", "PersonHealthConditionContractualRiskModel", "StandardCostModel"], "actions": ["view", "create", "count", "update"]}, {"resources": ["InsurancePortabilityHealthInsuranceModel", "InsurancePortabilityRequestModel", "InsurancePortabilityRequestFileModel", "LegalGuardianAssociationModel", "LegalGuardianInfoTempModel"], "actions": ["view", "create", "count", "update", "delete"]}], "branches": [{"conditions": ["${resource.type} == HEALTH_DECLARATION"], "allow": [{"resources": ["AppointmentScheduleModel"], "actions": ["create", "update"]}]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} == FIN_OPS"], "allow": [{"resources": ["StaffModel", "ProductModel", "ProductBundleModel", "ProviderModel", "ProductOrderModel", "PersonModel", "DeviceModel", "PersonOnboardingModel", "PersonRegistrationModel", "MemberModel", "Lead", "PersonLoginModel", "ProductPriceListingModel", "PriceListingModel", "MemberProductPriceModel"], "actions": ["view"]}, {"resources": ["InvoicePaymentModel", "CancelPaymentOnAcquirerScheduleModel", "ResourceSignTokenModel", "ItauPaymentModel", "PaymentDetailModel", "BoletoPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "PixPaymentDetailModel", "BolepixPaymentDetailModel", "InvoiceItemModel", "BillingAccountablePartyModel", "InvoiceLiquidationModel", "MemberInvoiceModel", "MemberInvoiceGroupModel", "ResourceSignTokenModel"], "actions": ["view", "count"]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= INSURANCE_OPS_HEALTH_INSTITUTION_OPS || ${subject.role} @= INSURANCE_OPS_COMMUNITY_SUCCESS"], "allow": [{"resources": ["StaffModel", "ProductModel", "ProductBundleModel", "ProviderModel", "ProductOrderModel", "PersonModel", "DeviceModel", "PersonOnboardingModel", "PersonRegistrationModel", "MemberModel", "PersonModel", "PersonInternalReference", "Lead", "PersonLoginModel", "HealthDeclaration", "ProductPriceAdjustmentModel", "OnboardingContractModel", "InsurancePortabilityRequestModel", "InsurancePortabilityRequestFileModel", "OnboardingBackgroundCheckModel", "AppointmentScheduleModel"], "actions": ["view"]}, {"resources": ["MemberProductPriceModel", "PriceListingModel", "ProductPriceListingModel"], "actions": ["view", "create", "update", "count"]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} == HEALTH_COMMUNITY"], "allow": [{"resources": ["StaffModel", "ProductModel", "ProductBundleModel", "ProviderModel", "ProductOrderModel", "PersonModel", "DeviceModel", "PersonOnboardingModel", "PersonRegistrationModel", "MemberModel", "PersonInternalReference", "Lead", "PersonLoginModel", "ProductPriceListingModel", "PriceListingModel", "MemberProductPriceModel", "HealthDeclaration"], "actions": ["view"]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= RISK_NURSE || ${subject.role} @= MED_RISK"], "allow": [{"resources": ["PersonModel", "MemberModel", "ProductOrderModel", "PersonOnboardingModel", "PersonRegistrationModel", "Lead", "PersonLoginModel", "OnboardingContractModel", "OnboardingBackgroundCheckModel", "InsurancePortabilityRequestModel", "InsurancePortabilityRequestFileModel", "PersonPreferencesModel", "MemberProductPriceModel", "ProductModel", "ProductBundleModel", "ProductPriceListingModel", "PriceListingModel", "ProviderModel"], "actions": ["view"]}, {"resources": ["CompanyProductPriceListingModel"], "actions": ["view", "count"]}, {"resources": ["HealthDeclaration", "AppointmentScheduleModel"], "actions": ["view", "create", "update"]}, {"resources": ["PersonContractualRiskModel"], "actions": ["view", "count", "create"]}, {"resources": ["CompanyContractMacoModel", "BeneficiaryMacoModel", "PersonHealthConditionContractualRiskModel", "StandardCostModel"], "actions": ["view", "count", "create", "update"]}]}], "aggregate": ["ProviderModel", "BeneficiaryOnboardingPhaseModel"]}