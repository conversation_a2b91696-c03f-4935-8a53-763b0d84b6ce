package br.com.alice.tiss.services.internal

import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.exec.indicator.models.RequesterData
import br.com.alice.tiss.clients.v4_01_00.GuiaClient
import br.com.alice.tiss.models.AttachmentHeader
import br.com.alice.tiss.models.BeneficiaryAdditionalData
import br.com.alice.tiss.models.ChemotherapyOncologicalDiagnosis
import br.com.alice.tiss.models.ChemotherapyRequestAttachment
import br.com.alice.tiss.models.CreateGuiaRequest
import br.com.alice.tiss.models.OncologicalDiagnosis
import br.com.alice.tiss.models.OncologicalRadioDiagnosis
import br.com.alice.tiss.models.RadioRequestAttachment
import br.com.alice.tiss.models.RequestedDrug
import br.com.alice.tiss.models.RequestedDrugs
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

class GuiaWithAttachmentServiceTest {

    private val guiaClient: GuiaClient = mockk()

    private val service = GuiaWithAttachmentService(guiaClient)

    private val createGuiaRequest = CreateGuiaRequest(
        procedures = listOf(
            GuiaProcedure(table = "22", code = "123", quantity = 1, description = ""),
            GuiaProcedure(table = "22", code = "545", quantity = 1, description = "")
        ),
        requester = RequesterData(name = "Alice Operadora", providerCode = "34266553000102"),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE
        ),
        guiaNumber = "************",
        requestDate = LocalDate.now(),
        beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        ansCode = "1234",
        authorizationType = "1",
        attendanceCharacter = "1",
        origin = GuiaOrigin.EHR,
        attendanceRegime = "1",
        accidentIndication = "1"
    )

    private val chemoRequest = ChemotherapyRequestAttachment(
        requestedDrugs = RequestedDrugs(
            listOf(
                RequestedDrug(
                    probableDate = LocalDate.now(),
                    identification = GuiaProcedure(table = "22", code = "545", quantity = 1, description = ""),
                    dosesQuantity = 12.0,
                    unitMeasure = "01",
                    administrationRoute = "02",
                    frequency = BigInteger.valueOf(12)
                ),
                RequestedDrug(
                    probableDate = LocalDate.now(),
                    identification = GuiaProcedure(table = "22", code = "545", quantity = 1, description = "OHMY GOD"),
                    dosesQuantity = 12.0,
                    unitMeasure = "01",
                    administrationRoute = "02",
                    frequency = BigInteger.valueOf(12)
                ),
            )
        ),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE,
            phoneNumber = "123",
            email = "<EMAIL>"
        ),
        beneficiaryData = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        origin = GuiaOrigin.EHR,
        attachmentHeader = AttachmentHeader(
            ansCode = "1234",
            attachmentGuideNumber = "************",
            referencedGuideNumber = "123",
            requestDate = LocalDate.now()
        ),
        providerCnpj = "87145534000194",
        observation = "Teste",
        beneficiaryAdditionalData = BeneficiaryAdditionalData(
            weight = BigDecimal.valueOf(180),
            height = BigDecimal.valueOf(100),
            bodySurface = BigDecimal.valueOf(23),
            age = BigInteger.valueOf(12),
            sex = "01"
        ),
        chemotherapyOncologicalDiagnosis = ChemotherapyOncologicalDiagnosis(
            chemoDiagnosis = OncologicalDiagnosis(
                diagnosisDate = LocalDate.now(),
                diagnosisCID = listOf("C50"),
                local = "Teste",
                purpose = "Teste",
                ecog = "Teste"
            ),
            tumor = "Teste",
            nodule = "Teste",
            metastasis = "Teste",
            chemotherapyType = "Teste",
            therapeuticPlan = "Teste"
        ),
        cyclesNumber = BigInteger.valueOf(1),
        currentCycle = BigInteger.valueOf(1),
        currentCycleDays = BigInteger.valueOf(1),
        cyclesInterval = BigInteger.valueOf(1)
    )

    val radioRequest = RadioRequestAttachment(
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE,
            phoneNumber = "123",
            email = "<EMAIL>"
        ),
        beneficiaryData = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        origin = GuiaOrigin.EHR,
        attachmentHeader = AttachmentHeader(
            ansCode = "1234",
            attachmentGuideNumber = "************",
            referencedGuideNumber = "123",
            requestDate = LocalDate.now()
        ),
        providerCnpj = "87145534000194",
        observation = "Teste",
        beneficiaryAdditionalData = BeneficiaryAdditionalData(
            weight = BigDecimal.valueOf(180),
            height = BigDecimal.valueOf(100),
            bodySurface = BigDecimal.valueOf(23),
            age = BigInteger.valueOf(12),
            sex = "01"
        ),
        oncologicalDiagnosisRadio = OncologicalRadioDiagnosis(
            radioDiagnosis = OncologicalDiagnosis(
                diagnosisDate = LocalDate.now(),
                diagnosisCID = listOf("C50"),
                local = "Teste",
                purpose = "Teste",
                ecog = "Teste"
            )
        ),
        numberFields = BigInteger.valueOf(1),
        fieldDose = BigInteger.valueOf(1),
        totalDose = BigInteger.valueOf(1),
        numberDays = BigInteger.valueOf(1),
        expectedStartDate = LocalDate.now(),
    )

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    @Test
    fun `#createGuiaWithChemotherapy should throw error if procedures is empty`() = runBlocking {
        val result = service.createGuiaWithChemotherapy(
            createGuiaRequest.copy(procedures = emptyList()),
            chemoRequest
        )

        ResultAssert.assertThat(result).isFailureOfType(NotFoundException::class)
        coVerifyNone { guiaClient.createGuiaWithChemotherapy(any(), any()) }
    }

    @Test
    fun `#createGuiaWithChemotherapy should throw error if cnpj is invalid`() = runBlocking {
        val result = service.createGuiaWithChemotherapy(
            createGuiaRequest.copy(requester = RequesterData("123", "Teste")),
            chemoRequest
        )

        ResultAssert.assertThat(result).isFailureOfType(InvalidArgumentException::class)
        coVerifyNone { guiaClient.createGuiaWithChemotherapy(any(), any()) }
    }

    @Test
    fun `#createGuiaWithChemotherapy should throw error if drugs is empty`() = runBlocking {
        val result = service.createGuiaWithChemotherapy(
            createGuiaRequest,
            chemoRequest.copy(requestedDrugs = RequestedDrugs(emptyList()))
        )

        ResultAssert.assertThat(result).isFailureOfType(NotFoundException::class)
        coVerifyNone { guiaClient.createGuiaWithChemotherapy(any(), any()) }
    }

    @Test
    fun `#createGuiaWithChemotherapy should call createGuiaExam correctly`() = runBlocking {
        coEvery {
            guiaClient.createGuiaWithChemotherapy(createGuiaRequest, chemoRequest)
        } returns true.success()

        val result = service.createGuiaWithChemotherapy(createGuiaRequest, chemoRequest)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { guiaClient.createGuiaWithChemotherapy(createGuiaRequest, chemoRequest) }
    }

    @Test
    fun `#createGuiaWithRadiotherapy should throw error if procedures is empty`() = runBlocking {
        val result = service.createGuiaWithRadiotherapy(
            createGuiaRequest.copy(procedures = emptyList()),
            radioRequest
        )

        ResultAssert.assertThat(result).isFailureOfType(NotFoundException::class)
        coVerifyNone { guiaClient.createGuiaWithRadiotherapy(any(), any()) }
    }

    @Test
    fun `#createGuiaWithRadiotherapy should throw error if cnpj is invalid`() = runBlocking {
        val result = service.createGuiaWithRadiotherapy(
            createGuiaRequest.copy(requester = RequesterData("123", "Teste")),
            radioRequest
        )

        ResultAssert.assertThat(result).isFailureOfType(InvalidArgumentException::class)
        coVerifyNone { guiaClient.createGuiaWithRadiotherapy(any(), any()) }
    }

    @Test
    fun `#createGuiaWithRadiotherapy should call createGuiaExam correctly`() = runBlocking {
        coEvery {
            guiaClient.createGuiaWithRadiotherapy(createGuiaRequest, radioRequest)
        } returns true.success()

        val result = service.createGuiaWithRadiotherapy(createGuiaRequest, radioRequest)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { guiaClient.createGuiaWithRadiotherapy(createGuiaRequest, radioRequest) }
    }

}
