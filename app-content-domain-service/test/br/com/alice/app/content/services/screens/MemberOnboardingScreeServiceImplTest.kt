package br.com.alice.app.content.services.screens

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.Alignment
import br.com.alice.app.content.model.AppBar
import br.com.alice.app.content.model.Button
import br.com.alice.app.content.model.CardType
import br.com.alice.app.content.model.Image
import br.com.alice.app.content.model.ImageSize
import br.com.alice.app.content.model.ImageType
import br.com.alice.app.content.model.Label
import br.com.alice.app.content.model.MenuVariant
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.ScreenBackgroundType
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreenProperties
import br.com.alice.app.content.model.ScreenSafeArea
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.SectionPadding
import br.com.alice.app.content.model.SectionTextLayout
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.Tag
import br.com.alice.app.content.model.TagColorScheme
import br.com.alice.app.content.model.Variant
import br.com.alice.app.content.model.section.ButtonSection
import br.com.alice.app.content.model.section.CardSection
import br.com.alice.app.content.model.section.ExpandableCardVariant
import br.com.alice.app.content.model.section.GridGutter
import br.com.alice.app.content.model.section.GridSection
import br.com.alice.app.content.model.section.GridVariant
import br.com.alice.app.content.model.section.HealthProfessionalInfo
import br.com.alice.app.content.model.section.HealthProfessionalInfoCardSection
import br.com.alice.app.content.model.section.HealthProfessionalInfoSection
import br.com.alice.app.content.model.section.ImageSection
import br.com.alice.app.content.model.section.LargeListCardSection
import br.com.alice.app.content.model.section.ListMenuSection
import br.com.alice.app.content.model.section.MenuSection
import br.com.alice.app.content.model.section.Metric
import br.com.alice.app.content.model.section.TextSection
import br.com.alice.app.content.services.screens.health.HealthProfessionalRecommendationScreenService
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Contact
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ModalityType
import br.com.alice.data.layer.models.withContacts
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

private const val MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE = "member_onboarding_screen_call_to_complete"

class MemberOnboardingScreeServiceImplTest {
    private val healthProfessionalRecommendationScreenService: HealthProfessionalRecommendationScreenService = mockk()

    private val memberOnboardingScreeService = MemberOnboardingScreeServiceImpl(
        healthProfessionalRecommendationScreenService
    )

    private val personId: PersonId = PersonId()
    private val healthcareTeam = TestModelFactory.buildHealthcareTeam()
    private val healthProfessional = TestModelFactory.buildHealthProfessional(staffId = healthcareTeam.physicianStaffId)

    @Test
    fun `#getCallToComplete - should return screen of superior version`() = runBlocking {
        withFeatureFlag(FeatureNamespace.ALICE_APP, "minor_version_to_show_new_image_full_width", "4.8.0") {
            val appVersion = SemanticVersion("4.8.0")

            val expected = ScreensTransport(
                id = MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE,
                layout = ScreenLayout(
                    type = "single_column",
                    appBar = AppBar(
                        title = "",
                        back = ""
                    ),
                    body = listOf(
                        Section(
                            id = "${MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE}_banner_image",
                            type = SectionType.IMAGE_SECTION,
                            minAppVersion = SectionType.IMAGE_SECTION.minAppVersion!!,
                            data = ImageSection(
                                content = ImageSection.Content(
                                    alignment = Alignment.CENTER,
                                    verticalPadding = SectionPadding.P1,
                                    horizontalPadding = SectionPadding.P0,
                                    image = Image(
                                        url = "https://alice-member-app-assets.s3.us-east-1.amazonaws.com/member_onboarding/images/v2/physhisian_and_score_magenta_and_appointment_schedule.png",
                                        type = ImageType.STATIC_FULL_WIDTH,
                                        size = ImageSize.BIG
                                    )
                                )
                            )
                        ),
                        Section(
                            id = "${MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE}_grid_section",
                            type = SectionType.GRID_SECTION,
                            minAppVersion = SectionType.GRID_SECTION.minAppVersion!!,
                            data = GridSection(
                                gutter = GridGutter.GUTTER_3,
                                gridVariant = GridVariant.FULL_WIDTH,
                                children = listOf(
                                    buildText(
                                        "Cuide da sua saúde desde o primeiro dia",
                                        SectionTextLayout.TITLE_LARGE_HIGHLIGHT,
                                        1
                                    ),
                                    buildText(
                                        "Ao desbloquear seu primeiro Plano de Ação, você terá tarefas criadas com base no seu histórico e estado atual de saúde. Como:",
                                        SectionTextLayout.BODY_LARGE,
                                        2
                                    ),
                                    buildText(
                                        "• Consulta com Médico de Família",
                                        SectionTextLayout.TITLE_SMALL_HIGHLIGHT,
                                        3
                                    ),
                                    buildText(
                                        "• Indicação de aula ou encontro",
                                        SectionTextLayout.TITLE_SMALL_HIGHLIGHT,
                                        4
                                    ),
                                    buildText(
                                        "• Avaliação completa da nossa calculadora de saúde",
                                        SectionTextLayout.TITLE_SMALL_HIGHLIGHT,
                                        5
                                    ),
                                    Section(
                                        id = "${MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE}_grid_section_button_section",
                                        type = SectionType.BUTTON_SECTION,
                                        minAppVersion = SectionType.BUTTON_SECTION.minAppVersion!!,
                                        data = ButtonSection(
                                            alignment = Alignment.CENTER,
                                            button = Button(
                                                id = "${MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE}_grid_section_button",
                                                variant = Variant.PRIMARY,
                                                shrinkWrap = false,
                                                onTapAction = RemoteAction(
                                                    mobileRoute = ActionRouting.MEMBER_ONBOARDING_V2
                                                ),
                                                label = Label(
                                                    text = "Desbloquear meu Plano de Ação"
                                                )
                                            )
                                        )
                                    )

                                )
                            )
                        )
                    )
                )
            )

            val result = memberOnboardingScreeService.getCallToComplete(appVersion)

            ResultAssert.assertThat(result).isSuccessWithData(expected)

        }
    }

    @Test
    fun `#getCallToComplete - should return screen of lower version`() = runBlocking {
        withFeatureFlag(FeatureNamespace.ALICE_APP, "minor_version_to_show_new_image_full_width", "4.8.0") {
            val appVersion = SemanticVersion("4.7.0")

            val expected = ScreensTransport(
                id = MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE,
                layout = ScreenLayout(
                    type = "single_column",
                    appBar = AppBar(
                        title = "",
                        back = ""
                    ),
                    body = listOf(
                        Section(
                            id = "${MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE}_banner_image",
                            type = SectionType.IMAGE_SECTION,
                            minAppVersion = SectionType.IMAGE_SECTION.minAppVersion!!,
                            data = ImageSection(
                                content = ImageSection.Content(
                                    alignment = Alignment.LEFT,
                                    verticalPadding = SectionPadding.P1,
                                    horizontalPadding = SectionPadding.P3,
                                    image = Image(
                                        url = "https://alice-member-app-assets.s3.us-east-1.amazonaws.com/member_onboarding/images/v2/shortcut_appointment_schedule.png",
                                        type = ImageType.STATIC,
                                        size = ImageSize.BIG
                                    )
                                )
                            )
                        ),
                        Section(
                            id = "${MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE}_grid_section",
                            type = SectionType.GRID_SECTION,
                            minAppVersion = SectionType.GRID_SECTION.minAppVersion!!,
                            data = GridSection(
                                gutter = GridGutter.GUTTER_3,
                                gridVariant = GridVariant.FULL_WIDTH,
                                children = listOf(
                                    buildText(
                                        "Cuide da sua saúde desde o primeiro dia",
                                        SectionTextLayout.TITLE_LARGE_HIGHLIGHT,
                                        1
                                    ),
                                    buildText(
                                        "Ao desbloquear seu primeiro Plano de Ação, você terá tarefas criadas com base no seu histórico e estado atual de saúde. Como:",
                                        SectionTextLayout.BODY_LARGE,
                                        2
                                    ),
                                    buildText(
                                        "• Consulta com Médico de Família",
                                        SectionTextLayout.TITLE_SMALL_HIGHLIGHT,
                                        3
                                    ),
                                    buildText(
                                        "• Indicação de aula ou encontro",
                                        SectionTextLayout.TITLE_SMALL_HIGHLIGHT,
                                        4
                                    ),
                                    buildText(
                                        "• Avaliação completa da nossa calculadora de saúde",
                                        SectionTextLayout.TITLE_SMALL_HIGHLIGHT,
                                        5
                                    ),
                                    Section(
                                        id = "${MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE}_grid_section_button_section",
                                        type = SectionType.BUTTON_SECTION,
                                        minAppVersion = SectionType.BUTTON_SECTION.minAppVersion!!,
                                        data = ButtonSection(
                                            alignment = Alignment.CENTER,
                                            button = Button(
                                                id = "${MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE}_grid_section_button",
                                                variant = Variant.PRIMARY,
                                                shrinkWrap = false,
                                                onTapAction = RemoteAction(
                                                    mobileRoute = ActionRouting.MEMBER_ONBOARDING_V2
                                                ),
                                                label = Label(
                                                    text = "Desbloquear meu Plano de Ação"
                                                )
                                            )
                                        )
                                    )

                                )
                            )
                        )
                    )
                )
            )

            val result = memberOnboardingScreeService.getCallToComplete(appVersion)

            ResultAssert.assertThat(result).isSuccessWithData(expected)

        }
    }

    @Test
    fun `#getRecommendedPhysicians - should return physician recommendation screen`() = runBlocking {

        val recommendationScreen = ScreensTransport(
            id = "healthcare_team_recommendations",
            layout = ScreenLayout(
                type = "single_column",
                appBar = AppBar(
                    back = "",
                    title = "Escolher meu médico",
                ),
                backgroundType = ScreenBackgroundType.ACTIVE,
                body = listOf(
                    Section(
                        id = "large_card_list_healthcare_team_section",
                        type = SectionType.LARGE_LIST_CARD_SECTION,
                        minAppVersion = SectionType.LARGE_LIST_CARD_SECTION.minAppVersion!!,
                        data = LargeListCardSection(
                            body = listOf(
                                Section(
                                    id = "healthcare_team_physician",
                                    type = SectionType.CARD_SECTION,
                                    minAppVersion = SectionType.CARD_SECTION.minAppVersion!!,
                                    data = HealthProfessionalInfoCardSection(
                                        healthProfessionalInfo = HealthProfessionalInfo(
                                            id = healthProfessional.id,
                                            avatarUrl = healthProfessional.imageUrl,
                                            name = healthProfessional.name,
                                            council = healthProfessional.council.let {
                                                "${it.number}/${it.state}"
                                            },
                                            tag = Tag(text = "Recomendado para você", colorScheme = TagColorScheme.MAGENTA)
                                        ),
                                        expandableCardVariant = null,
                                        onCardClick = RemoteAction(
                                            mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                                            params = mapOf(
                                                "method" to RemoteActionMethod.GET,
                                                "endpoint" to "/app_content/screen/healthcare_team_details/${healthcareTeam.id}"
                                            )
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
        )

        coEvery {
            healthProfessionalRecommendationScreenService.getRecommendedPhysicians(personId)
        } returns recommendationScreen.success()

        val result = healthProfessionalRecommendationScreenService.getRecommendedPhysicians(personId)
        ResultAssert.assertThat(result).isSuccessWithData(recommendationScreen)

        coVerifyOnce { healthProfessionalRecommendationScreenService.getRecommendedPhysicians(any()) }
    }

    @Test
    fun `#getRecommendedPhysicianDetails - should return recommended physician details screen`() = runBlocking {
        val address = TestModelFactory.buildStructuredAddress()
        val contacts = listOf(
            Contact(modality = ModalityType.REMOTE),
            Contact(
                modality = ModalityType.PRESENTIAL,
                address = address
            )
        )
        val healthProfessional = healthProfessional.copy(name = "João Oliveira").withContacts(contacts)

        val recommendationDetailScreen = ScreensTransport(
            id = "healthcare_team_details",
            properties = ScreenProperties(safeArea = ScreenSafeArea(bottom = true, top = false)),
            layout = ScreenLayout(
                type = "single_column",
                appBar = AppBar(back = ""),
                backgroundType = ScreenBackgroundType.ACTIVE,
                body = listOf(
                    Section(
                        id = "health_professional_info",
                        type = SectionType.HEALTH_PROFESSIONAL_INFO_SECTION,
                        minAppVersion = SectionType.HEALTH_PROFESSIONAL_INFO_SECTION.minAppVersion!!,
                        data = HealthProfessionalInfoSection(
                            HealthProfessionalInfo(
                                id = healthProfessional.id,
                                avatarUrl = healthProfessional.imageUrl,
                                name = healthProfessional.name,
                                council = healthProfessional.council.let {
                                    "${it.number}/${it.state}"
                                },
                                tag = Tag(text = "Recomendado para você", colorScheme = TagColorScheme.MAGENTA)
                            )
                        )
                    ),
                    Section(
                        id = "health_professional_metrics",
                        type = SectionType.CARD_SECTION,
                        data = CardSection(
                            type = CardType.EXPANDABLE,
                            expandableCardVariant = ExpandableCardVariant(
                                metrics = listOf(
                                    Metric(
                                        value = "80",
                                        label = "Consultas"
                                    ),
                                    Metric(
                                        value = "0 m",
                                        label = "De você"
                                    )
                                ),
                                allowsExpansion = true,
                                content = "Dr. João Oliveira, médico de família da Alice",
                                trackingParameters = mapOf(
                                    "id" to healthProfessional.staffId,
                                    "name" to healthProfessional.name
                                )
                            )
                        ),
                        minAppVersion = SectionType.CARD_SECTION.minAppVersion!!
                    ),
                    Section(
                        id = "health_professional_menu",
                        type = SectionType.LIST_MENU_SECTION,
                        minAppVersion = SectionType.LIST_MENU_SECTION.minAppVersion!!,
                        data = ListMenuSection(
                            hasDivider = true,
                            body = listOf(
                                Section(
                                    id = "remote",
                                    type = SectionType.MENU_SECTION,
                                    data = MenuSection(
                                        menuVariant = MenuVariant.EXPANDABLE,
                                        clickAffordance = false,
                                        icon = "video_on",
                                        label = "Consulta por vídeo",
                                    ),
                                    minAppVersion = SectionType.MENU_SECTION.minAppVersion!!
                                ),
                                Section(
                                    id = "local",
                                    type = SectionType.MENU_SECTION,
                                    data = MenuSection(
                                        menuVariant = MenuVariant.EXPANDABLE,
                                        clickAffordance = false,
                                        icon = "local_pin",
                                        label = "Consulta presencial",
                                        title = contacts[1].address!!.formattedAddress()
                                    ),
                                    minAppVersion = SectionType.MENU_SECTION.minAppVersion!!
                                )
                            )
                        )
                    )
                ),
                footer = Section(
                    id = "health_professional_select_button",
                    type = SectionType.BUTTON_SECTION,
                    minAppVersion = SectionType.BUTTON_SECTION.minAppVersion!!,
                    data = ButtonSection(
                        alignment = Alignment.LEFT,
                        button = Button(
                            id = "health_professional_select_button",
                            label = Label(text = "Escolher João"),
                            variant = Variant.PRIMARY,
                            shrinkWrap = false,
                            onTapAction = RemoteAction(
                                mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                                params = mapOf(
                                    "method" to RemoteActionMethod.POST,
                                    "endpoint" to "/app_content/screen/person_clinical_account/${healthcareTeam.id}"
                                )
                            )
                        )
                    )
                )
            )
        )

        coEvery {
            healthProfessionalRecommendationScreenService.getRecommendedPhysicianDetails(personId, healthcareTeam.id, recommended = true)
        } returns recommendationDetailScreen.success()

        val result = healthProfessionalRecommendationScreenService.getRecommendedPhysicianDetails(personId, healthcareTeam.id, recommended = true)
        ResultAssert.assertThat(result).isSuccessWithData(recommendationDetailScreen)

        coVerifyOnce { healthProfessionalRecommendationScreenService.getRecommendedPhysicianDetails(any(), any(), any()) }
    }

    private fun buildText(text: String, layout: SectionTextLayout, index: Int) =
        Section(
            id = "${MEMBER_ONBOARDING_SCREEN_CALL_TO_COMPLETE}_grid_section_text_section_$index",
            type = SectionType.TEXT_SECTION,
            minAppVersion = SectionType.TEXT_SECTION.minAppVersion!!,
            data = TextSection.Content(
                content = TextSection.Content.Value(
                    title = text,
                    alignment = Alignment.LEFT,
                    layout = layout
                )
            )
        )
}
