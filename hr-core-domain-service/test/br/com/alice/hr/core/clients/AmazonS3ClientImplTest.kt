package br.com.alice.hr.core.clients

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.hr.core.ServiceConfig
import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.AmazonS3URI
import com.amazonaws.services.s3.model.GetObjectRequest
import com.amazonaws.services.s3.model.S3Object
import io.mockk.coEvery
import io.mockk.clearAllMocks
import io.mockk.mockk
import java.io.ByteArrayInputStream
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

class AmazonS3ClientImplTest {
    @AfterTest
    fun setup() = clearAllMocks()

    private val s3Client = mockk<AmazonS3>()
    private val amazonS3Client = AmazonS3ClientImpl(s3Client)

    private val s3Path = ServiceConfig.S3.templateSheetBucketUrl
    private val s3Url = AmazonS3URI(s3Path)
    private val s3Object = S3Object();

    @Test
    fun `#getS3ObjectContent should get object content from S3`(): Unit = runBlocking {
        val byteArrayOf = byteArrayOf(1, 2, 3)
        val inputStream = ByteArrayInputStream(byteArrayOf)
        val getObjectRequest = GetObjectRequest(s3Url.getBucket(), s3Url.getKey())

        s3Object.setObjectContent(inputStream)

        coEvery { s3Client.getObject(getObjectRequest) } returns s3Object

        val result = amazonS3Client.getS3ObjectContent(s3Path)

        assertThat(result).isSuccess()

        coVerifyOnce {s3Client.getObject(any())}
    }

    @Test
    fun `#getS3ObjectContent should return failure when get object content from S3`(): Unit = runBlocking {
        coEvery { s3Client.getObject(any()) } returns null

        val result = amazonS3Client.getS3ObjectContent(s3Path)

        assertThat(result).isFailure()
    }
}
