package br.com.alice.sales_channel.api.infrastructure.ioc

import br.com.alice.common.controllers.HealthController
import br.com.alice.data.layer.SALES_CHANNEL_API_ROOT_SERVICE_NAME
import br.com.alice.sales_channel.api.controllers.AuthController
import br.com.alice.sales_channel.api.controllers.ClicksignController
import br.com.alice.sales_channel.api.controllers.DealBeneficiaryController
import br.com.alice.sales_channel.api.controllers.DealController
import br.com.alice.sales_channel.api.controllers.NetlexController
import br.com.alice.sales_channel.api.controllers.OngoingDealController
import br.com.alice.sales_channel.api.controllers.SalesAgentController
import br.com.alice.sales_channel.api.controllers.TicketController
import org.koin.dsl.module

val ControllersModule = module(createdAtStart = true) {
    single { HealthController(SALES_CHANNEL_API_ROOT_SERVICE_NAME) }
    single { OngoingDealController(get(), get(), get(), get(), get(), get(), get(), get(), get()) }
    single { DealBeneficiaryController(get(), get(), get(), get(), get(), get()) }
    single { DealController(get(), get()) }
    single { TicketController(get(), get()) }
    single { NetlexController(get()) }
    single { ClicksignController(get()) }
    single { SalesAgentController(get(), get()) }
    single { AuthController(get()) }
}
