package br.com.alice.bff.business.controllers.v2

import br.com.alice.bff.business.ControllerTestHelper
import br.com.alice.bff.business.converters.v2.toResponse
import br.com.alice.bff.business.models.v2.SendMagicLinkRequest
import br.com.alice.bff.business.models.v2.SignInRequest
import br.com.alice.bff.business.services.AuthService
import br.com.alice.bff.business.services.ModuleService
import br.com.alice.bff.business.services.Modules
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanyStaffService
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyOnce
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class AuthControllerTest : ControllerTestHelper() {
    private val authService : AuthService = mockk()
    private val modulesService : ModuleService = mockk()
    private val companyStaffService: CompanyStaffService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val companyService: CompanyService = mockk()

    private val controller = AuthController(
        authService,
        companyStaffService,
        companyService,
        modulesService,
        beneficiaryService
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        clearMocks(authService, modulesService, companyStaffService, companyService)
        module.single { controller }
    }
    @Test
    fun `#sendMagicLink should return 200 when login is successful with SalesFirmStaff`() =
        runBlocking {
            val requestBody = SendMagicLinkRequest("<EMAIL>", "google.com")

            coEvery { authService.sendSignInLink(requestBody.email, requestBody.url) } returns "email_receipt_id".success()

            post(to = "/send_magic_link", body = requestBody) { response ->
                ResponseAssert.assertThat(response).isOK()
            }

            coVerifyOnce {
                authService.sendSignInLink(requestBody.email, requestBody.url)
            }
        }

    @Test
    fun `#sendMagicLink should trim and lowercase email`() =
        runBlocking {
            val requestBody = SendMagicLinkRequest(" <EMAIL> ", "google.com")

            coEvery { authService.sendSignInLink("<EMAIL>", requestBody.url) } returns "email_receipt_id".success()

            post(to = "/send_magic_link", body = requestBody) { response ->
                ResponseAssert.assertThat(response).isOK()
            }

            coVerifyOnce {
                authService.sendSignInLink("<EMAIL>", requestBody.url)
            }
        }

    @Test
    fun `#signIn should sign in properly when token is valid`() = runBlocking<Unit> {
        val requestBody = SignInRequest("id_token")

        coEvery { authService.validateSignIn(any()) } returns true

        post(to = "/sign_in", body = requestBody) { response ->
            ResponseAssert.assertThat(response).isOK()
        }
    }

    @Test
    fun `#getLogged should return 200 with CompanyStaff data`() =
        runBlocking {
            val modulesEnabled = listOf(
                Modules.INVOICES_MODULE,
            )

            coEvery { companyService.get(companyStaff.companyId) } returns company.success()
            coEvery { companyStaffService.getLatestByEmail(companyStaff.email) } returns companyStaff.success()
            coEvery { beneficiaryService.countUniquePerPersonByCompanyId(any()) } returns 1.success()
            coEvery { modulesService.getActiveModules(any()) } returns modulesEnabled

            authenticatedAsCompanyStaff("id-token", companyStaff) {
                get(url = "/v2/me") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(companyStaff.toResponse(company, 1, modulesEnabled))
                }
            }

            coVerifyOnce { companyStaffService.getLatestByEmail(any()) }
        }

    @Test
    fun `#getLogged should return 200 with CompanyStaff data and hasBeneficiary false when there are no beneficiaries`() =
        runBlocking {
            val modulesEnabled = listOf(
                Modules.INVOICES_MODULE,
            )

            coEvery { companyService.get(companyStaff.companyId) } returns company.success()
            coEvery { companyStaffService.getLatestByEmail(companyStaff.email) } returns companyStaff.success()
            coEvery { beneficiaryService.countUniquePerPersonByCompanyId(any()) } returns 0.success()
            coEvery { modulesService.getActiveModules(any()) } returns modulesEnabled

            authenticatedAsCompanyStaff("id-token", companyStaff) {
                get(url = "/v2/me") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(companyStaff.toResponse(company, 0, modulesEnabled))
                }
            }

            coVerifyOnce { beneficiaryService.countUniquePerPersonByCompanyId(any()) }
        }

    @Test
    fun `#getLogged should return error when there is an error on companyStaffService`() =
        runBlocking {
            val modulesEnabled = listOf(
                Modules.INVOICES_MODULE,
            )

            coEvery { companyStaffService.getLatestByEmail(any()) } returns Exception("Error").failure()

            authenticatedAsCompanyStaff("id-token", companyStaff) {
                get(url = "/v2/me") { response ->
                        ResponseAssert.assertThat(response).isInternalServerError()
                }
            }
        }

    @Test
    fun `#getLogged should return error when there is an error on companyService`() =
        runBlocking {
            coEvery { companyStaffService.getLatestByEmail(any()) } returns companyStaff.success()
            coEvery { companyService.get(any()) } returns Exception("Error").failure()

            authenticatedAsCompanyStaff("id-token", companyStaff) {
                get(url = "/v2/me") { response ->
                    ResponseAssert.assertThat(response).isInternalServerError()
                }
            }
        }

    @Test
    fun `#getLogged should return error when there is an error on beneficiaryService`() =
        runBlocking {
            coEvery { companyService.get(companyStaff.companyId) } returns company.success()
            coEvery { companyStaffService.getLatestByEmail(companyStaff.email) } returns companyStaff.success()
            coEvery { beneficiaryService.countUniquePerPersonByCompanyId(any()) } returns Exception("Error").failure()

            authenticatedAsCompanyStaff("id-token", companyStaff) {
                get(url = "/v2/me") { response ->
                    ResponseAssert.assertThat(response).isInternalServerError()
                }
            }
        }
}
