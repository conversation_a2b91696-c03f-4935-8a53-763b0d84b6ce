package br.com.alice.exec.indicator.service

import br.com.alice.common.MvUtil
import br.com.alice.common.core.exceptions.ConflictException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.services.HealthcareResourceModelDataService
import br.com.alice.exec.indicator.client.HealthcareResourceFilters
import br.com.alice.exec.indicator.client.HealthcareResourceListWithCount
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.events.HealthcareResourceUpsertedEvent
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class HealthcareResourceServiceImplTest {

    private val data: HealthcareResourceModelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val service = HealthcareResourceServiceImpl(data, kafkaProducerService)

    private val healthcareResource = TestModelFactory.buildHealthcareResource(active = false)
    private val healthcareBundle = TestModelFactory.buildHealthcareBundle(
        code = null
    )

    private val range = IntRange(1, 10)

    @AfterTest
    fun setup() = clearAllMocks()

    @Test
    fun `#add should add procedure successfully`() = runBlocking {
        coEvery { data.add(healthcareResource.toModel()) } returns healthcareResource.toModel().success()
        coEvery { kafkaProducerService.produce(
            match { (it as HealthcareResourceUpsertedEvent).payload.healthcareResourceId == healthcareResource.id },
            healthcareResource.id.toString()
        ) } returns ProducerResult(LocalDateTime.now(), "1", 1)

        val result = service.add(healthcareResource)

        assertThat(result).isSuccessWithData(healthcareResource)
        coVerifyOnce { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#get should get procedure successfully`() = runBlocking {
        coEvery { data.get(healthcareResource.id) } returns healthcareResource.toModel().success()

        val result = service.get(healthcareResource.id)

        assertThat(result).isSuccessWithData(healthcareResource)
    }

    @Test
    fun `#update should update procedure successfully`() = runBlocking {
        coEvery { data.update(healthcareResource.toModel()) } returns healthcareResource.toModel().success()
        coEvery { kafkaProducerService.produce(
            match { (it as HealthcareResourceUpsertedEvent).payload.healthcareResourceId == healthcareResource.id },
            healthcareResource.id.toString()
        ) } returns ProducerResult(LocalDateTime.now(), "1", 1)

        val result = service.update(healthcareResource)

        assertThat(result).isSuccessWithData(healthcareResource)
        coVerifyOnce { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#list should list without filters`() = runBlocking {
        coEvery {
            data.find(queryEq {
                where { this.active.eq(true).and(this.code.notInList(listOf("123", "321"))) }
                    .orderByList({ listOf(this.updatedAt, this.tableType) }, { listOf(desc, desc) })
                    .offset { 0 }
                    .limit { 10 }
            })
        } returns listOf(healthcareResource.toModel()).success()

        coEvery {
            data.count(queryEq {
                where { this.active.eq(true).and(this.code.notInList(listOf("123", "321"))) }
            })
        } returns 1.success()
        withFeatureFlag(FeatureNamespace.EXEC_INDICATOR, "emergency_room_procedures", listOf("123", "321")) {
            val result = service.list(
                searchTerm = null,
                range = IntRange(0, 9),
                filters = HealthcareResourceFilters()
            )

            assertThat(result).isSuccessWithData(
                HealthcareResourceListWithCount(listOf(healthcareResource), 1)
            )
        }

    }

    @Test
    fun `#list should list with all filters`() = runBlocking {
        coEvery {
            data.find(queryEq {
                where {
                    this.active.eq(true)
                        .and(this.searchTokens.search("agulha"))
                        .and(this.tableType.inList(listOf("22")))
                        .and(this.compositionHash.eq("123"))
                        .and(this.code.inList(listOf("123", "321")))
                }
                    .orderByList({ listOf(this.updatedAt, this.tableType) }, { listOf(desc, desc) })
                    .offset { 0 }
                    .limit { 10 }
            })
        } returns listOf(healthcareResource.toModel()).success()

        coEvery {
            data.count(queryEq {
                where {
                    this.active.eq(true)
                        .and(this.searchTokens.search("agulha"))
                        .and(this.tableType.inList(listOf("22")))
                        .and(this.compositionHash.eq("123"))
                        .and(this.code.inList(listOf("123", "321")))
                }
            })
        } returns 1.success()

        withFeatureFlag(FeatureNamespace.EXEC_INDICATOR, "emergency_room_procedures", listOf("123", "321")) {
            val result = service.list(
                searchTerm = "agulha",
                range = IntRange(0, 9),
                filters = HealthcareResourceFilters(
                    tableType = listOf("22"),
                    hasCompositionHash = true,
                    compositionHash = "123",
                    searchGuiaType = MvUtil.TISS.PS
                )
            )

            assertThat(result).isSuccessWithData(
                HealthcareResourceListWithCount(listOf(healthcareResource), 1)
            )
        }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#list should list with all filters with compositionHash empty or with specific value`() = runBlocking {
        coEvery {
            data.find(queryEq {
                where {
                    this.active.eq(true)
                        .and(this.searchTokens.search("agulha"))
                        .and(this.tableType.inList(listOf("22")))
                        .and(scope(this.compositionHash.isNull().or(this.compositionHash.eq("123"))))
                        .and(this.code.notInList(listOf("123", "321")))
                }
                    .orderByList({ listOf(this.updatedAt, this.tableType) }, { listOf(desc, desc) })
                    .offset { 0 }
                    .limit { 10 }
            })
        } returns listOf(healthcareResource.toModel()).success()

        coEvery {
            data.count(queryEq {
                where {
                    this.active.eq(true)
                        .and(this.searchTokens.search("agulha"))
                        .and(this.tableType.inList(listOf("22")))
                        .and(scope(this.compositionHash.isNull().or(this.compositionHash.eq("123"))))
                        .and(this.code.notInList(listOf("123", "321")))
                }
            })
        } returns 1.success()

        withFeatureFlag(FeatureNamespace.EXEC_INDICATOR, "emergency_room_procedures", listOf("123", "321")) {
            val result = service.list(
                searchTerm = "agulha",
                range = IntRange(0, 9),
                filters = HealthcareResourceFilters(
                    tableType = listOf("22"),
                    hasCompositionHash = false,
                    compositionHash = "123"
                )
            )

            assertThat(result).isSuccessWithData(
                HealthcareResourceListWithCount(listOf(healthcareResource), 1)
            )
        }

    }

    @Test
    fun `#associateCompositionHashById should associate successfully`() = runBlocking {
        val expectedResult = healthcareResource.copy(compositionHash = "hash")

        coEvery {
            data.get(healthcareResource.id)
        } returns healthcareResource.toModel().copy(compositionHash = null).success()

        coEvery {
            data.update(healthcareResource.toModel().copy(compositionHash = "hash"))
        } returns expectedResult.toModel().success()

        val result = service.associateCompositionHashById(healthcareResource.id, compositionHash = "hash")

        assertThat(result).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#associateCompositionHashById should get error if already associated with different hash`() = runBlocking {
        val expectedResult = healthcareResource.copy(compositionHash = "1234")

        coEvery {
            data.get(healthcareResource.id)
        } returns expectedResult.toModel().success()

        val result = service.associateCompositionHashById(healthcareResource.id, compositionHash = "hash")

        assertThat(result).isFailureOfType(ConflictException::class)
    }

    @Test
    fun `#associateCompositionHashById return existing resource if it has already associated`() = runBlocking {
        val expectedResult = healthcareResource.copy(compositionHash = "hash")

        coEvery {
            data.get(healthcareResource.id)
        } returns expectedResult.toModel().success()

        coEvery {
            data.update(healthcareResource.toModel().copy(compositionHash = "hash"))
        } returns expectedResult.toModel().success()

        val result = service.associateCompositionHashById(healthcareResource.id, compositionHash = "hash")

        assertThat(result).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#getByRange should return successfully`() = runBlocking {
        coEvery {
            data.find(queryEq {
                orderBy { updatedAt }
                    .sortOrder { desc }
                    .offset { range.first }
                    .limit { range.count() }
            })
        } returns listOf(healthcareResource.toModel()).success()

        val result = service.getByRange(range)

        assertThat(result).isSuccessWithData(listOf(healthcareResource))
    }

    @Test
    fun `#findBySearchTokensPaged should return successfully`() = runBlocking {
        val query = "procedure"
        val offset = range.first
        val limit = range.count()
        coEvery {
            data.find(queryEq {
                where {
                    this.searchTokens.search(query)
                }.orderBy { updatedAt }
                    .sortOrder { desc }
                    .offset { offset }
                    .limit { limit }
            })
        } returns listOf(healthcareResource.toModel()).success()

        val result = service.findBySearchTokensPaginated(query, offset, limit)

        assertThat(result).isSuccessWithData(listOf(healthcareResource))
    }

    @Test
    fun `#count should return successfully`() = runBlocking {
        coEvery {
            data.count(queryEq { all() })
        } returns 10.success()

        val result = service.count()

        assertThat(result).isSuccessWithData(10)
    }

    @Test
    fun `#countBySearchTokens should return successfully`() = runBlocking {
        val query = "procedure"
        coEvery {
            data.count(queryEq {
                where {
                    this.searchTokens.search(query)
                }
            })
        } returns 10.success()

        val result = service.countBySearchTokens(query)

        assertThat(result).isSuccessWithData(10)
    }

    @Test
    fun `#findByList should return successfully`() = runBlocking {
        val ids = listOf(healthcareResource.id)
        coEvery {
            data.find(queryEq {
                where {
                    this.id.inList(ids)
                }
            })
        } returns listOf(healthcareResource.toModel()).success()

        val result = service.findByList(ids)

        assertThat(result).isSuccessWithData(listOf(healthcareResource))
    }

    @Test
    fun `#findByTussCodes should return successfully`() = runBlocking {
        val codes = listOf(healthcareResource.code)
        coEvery {
            data.find(queryEq {
                where {
                    this.code.inList(codes) and this.active.eq(true)
                }
            })
        } returns listOf(healthcareResource.toModel()).success()

        val result = service.findByTussCodes(codes)

        assertThat(result).isSuccessWithData(listOf(healthcareResource))
    }

    @Test
    fun `#findBySearchTokens should return successfully when ignored codes is empty`() = runBlocking {
        val query = "procedure"
        coEvery {
            data.find(queryEq {
                where {
                    this.searchTokens.search(query)
                }.limit { 10 }
            })
        } returns listOf(healthcareResource.toModel()).success()

        val result = service.findBySearchTokens(query)

        assertThat(result).isSuccessWithData(listOf(healthcareResource))
    }

    @Test
    fun `#findBySearchTokens should return successfully when ignored codes is not empty`() = runBlocking {
        val query = "procedure"
        val ignoredCodes = listOf("code")
        coEvery {
            data.find(queryEq {
                where {
                    this.searchTokens.search(query) and
                            this.code.notInList(ignoredCodes)
                }.limit { 10 }
            })
        } returns listOf(healthcareResource.toModel()).success()

        val result = service.findBySearchTokens(query, ignoredCodes)

        assertThat(result).isSuccessWithData(listOf(healthcareResource))
    }

    @Test
    fun `#getByTussCode should return successfully`() = runBlocking {
        val code = healthcareResource.code
        coEvery {
            data.find(queryEq {
                where {
                    this.code.eq(code).and(this.tussCode.eq(code))
                }
            })
        } returns listOf(healthcareResource.toModel()).success()

        val result = service.getByTussCode(code)

        assertThat(result).isSuccessWithData(healthcareResource)
    }

    @Test
    fun `#findByCodesAndTableType should return successfully`() = runBlocking {
        val codes = listOf(healthcareResource.code)
        val tableType = HealthcareResourceService.DEFAULT_PRIMARY_TUSS_TABLE
        coEvery {
            data.find(queryEq {
                where {
                    this.code.inList(codes) and this.tableType.eq(tableType) and this.active.eq(true)
                }
            })
        } returns listOf(healthcareResource.toModel()).success()

        val result = service.findByCodesAndTableType(codes, tableType)

        assertThat(result).isSuccessWithData(listOf(healthcareResource))
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findPrimaryTussResourcesBySearchTokensPaginated should return successfully if query is passed`() =
        runBlocking {
            val query = "procedure"
            val offset = range.first
            val limit = range.count()
            coEvery {
                data.find(queryEq {
                    where {
                        scope(this.isOriginalTuss.eq(true) or this.tussCode.isNull())
                            .and(this.tableType.eq(HealthcareResourceService.DEFAULT_PRIMARY_TUSS_TABLE))
                            .and(this.searchTokens.search(query))
                    }
                        .orderBy { updatedAt }
                        .sortOrder { desc }
                        .offset { offset }
                        .limit { limit }
                })
            } returns listOf(healthcareResource.toModel()).success()

            val result = service.findPrimaryTussResourcesBySearchTokensPaginated(query, offset, limit)

            assertThat(result).isSuccessWithData(listOf(healthcareResource))
        }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findPrimaryTussResourcesBySearchTokensPaginated should return successfully if query is not passed`() =
        runBlocking {
            val query = null
            val offset = range.first
            val limit = range.count()
            coEvery {
                data.find(queryEq {
                    where {
                        scope(this.isOriginalTuss.eq(true) or this.tussCode.isNull())
                            .and(this.tableType.eq(HealthcareResourceService.DEFAULT_PRIMARY_TUSS_TABLE))
                    }
                        .orderBy { updatedAt }
                        .sortOrder { desc }
                        .offset { offset }
                        .limit { limit }
                })
            } returns listOf(healthcareResource.toModel()).success()

            val result = service.findPrimaryTussResourcesBySearchTokensPaginated(query, offset, limit)

            assertThat(result).isSuccessWithData(listOf(healthcareResource))
        }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#countPrimaryTussResourcesBySearchTokens should return successfully if query is passed`() = runBlocking {
        val query = "procedure"
        coEvery {
            data.count(queryEq {
                where {
                    scope(this.isOriginalTuss.eq(true) or this.tussCode.isNull())
                        .and(this.tableType.eq(HealthcareResourceService.DEFAULT_PRIMARY_TUSS_TABLE))
                        .and(this.searchTokens.search(query))
                }
            })
        } returns 10.success()

        val result = service.countPrimaryTussResourcesBySearchTokens(query)

        assertThat(result).isSuccessWithData(10)
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#countPrimaryTussResourcesBySearchTokens should return successfully if query is not passed`() = runBlocking {
        val query = null
        coEvery {
            data.count(queryEq {
                where {
                    scope(this.isOriginalTuss.eq(true) or this.tussCode.isNull())
                        .and(this.tableType.eq(HealthcareResourceService.DEFAULT_PRIMARY_TUSS_TABLE))
                }
            })
        } returns 10.success()

        val result = service.countPrimaryTussResourcesBySearchTokens(query)

        assertThat(result).isSuccessWithData(10)
    }

    @Test
    fun `#getByCode should return successfully`() = runBlocking {
        val code = healthcareResource.code
        coEvery {
            data.findOne(queryEq {
                where {
                    this.code.eq(code) and this.active.eq(true)
                }
            })
        } returns healthcareResource.toModel().success()

        val result = service.getByCode(code)

        assertThat(result).isSuccessWithData(healthcareResource)
    }

    @Test
    fun `#findBySearchTokensAndTableTypesPaginated should return successfully`() = runBlocking {
        val query = "procedure"
        val offset = range.first
        val limit = range.count()
        val tableTypes = listOf(HealthcareResourceService.DEFAULT_PRIMARY_TUSS_TABLE)
        coEvery {
            data.find(queryEq {
                where {
                    this.searchTokens.search(query)
                        .and(this.tableType.inList(tableTypes))
                }.orderBy { updatedAt }
                    .sortOrder { desc }
                    .offset { offset }
                    .limit { limit }
            })
        } returns listOf(healthcareResource.toModel()).success()

        val result = service.findBySearchTokensAndTableTypesPaginated(query, offset, limit, tableTypes)

        assertThat(result).isSuccessWithData(listOf(healthcareResource))
    }

    @Test
    fun `#findByCompositionHashAndTableTypesPaginated should return successfully`() = runBlocking {
        val compositionHash = "hash"
        coEvery {
            data.findOne(queryEq {
                where {
                    this.compositionHash.eq(compositionHash)
                }
            })
        } returns healthcareResource.toModel().success()

        val result = service.findByCompositionHash(compositionHash)

        assertThat(result).isSuccessWithData(healthcareResource)
    }

    @Test
    fun `#findByCompositionHashList should return successfully`() = runBlocking {
        val compositionHashList = listOf("hash")
        coEvery {
            data.find(queryEq {
                where {
                    this.compositionHash.inList(compositionHashList)
                }
            })
        } returns listOf(healthcareResource.toModel()).success()

        val result = service.findByCompositionHashList(compositionHashList)

        assertThat(result).isSuccessWithData(listOf(healthcareResource))
    }

    @Test
    fun `#getByRangeAndTableTypes should return successfully`() = runBlocking {
        val offset = range.first
        val limit = range.count()
        val tableTypes = listOf(HealthcareResourceService.DEFAULT_PRIMARY_TUSS_TABLE)
        coEvery {
            data.find(queryEq {
                where {
                    this.tableType.inList(tableTypes)
                }.orderBy { updatedAt }
                    .sortOrder { desc }
                    .offset { offset }
                    .limit { limit }
            })
        } returns listOf(healthcareResource.toModel()).success()

        val result = service.getByRangeAndTableTypes(range, tableTypes)

        assertThat(result).isSuccessWithData(listOf(healthcareResource))
    }
    @Test
    fun `#countBySearchTokensAndTableTypes should return successfully`() = runBlocking {
        val query = "procedure"
        val tableTypes = listOf(HealthcareResourceService.DEFAULT_PRIMARY_TUSS_TABLE)
        coEvery {
            data.count(queryEq {
                where {
                    this.searchTokens.search(query)
                        .and(this.tableType.inList(tableTypes))
                }
            })
        } returns 10.success()

        val result = service.countBySearchTokensAndTableTypes(query, tableTypes)

        assertThat(result).isSuccessWithData(10)
    }

    @Test
    fun `#countByTableTypes should return successfully`() = runBlocking {
        val tableTypes = listOf(HealthcareResourceService.DEFAULT_PRIMARY_TUSS_TABLE)
        coEvery {
            data.count(queryEq {
                where {
                    this.tableType.inList(tableTypes)
                }
            })
        } returns 10.success()

        val result = service.countByTableTypes(tableTypes)

        assertThat(result).isSuccessWithData(10)
    }

    @Test
    fun `#findByCompositionHashAndCode should get resource by composition hash and code`() = runBlocking {
        val compositionHash = "hash"
        val code = "code"
        coEvery {
            data.findOne(queryEq {
                where {
                    this.compositionHash.eq(compositionHash)
                        .and(this.code.eq(code))
                }
            })
        } returns healthcareResource.toModel().success()

        val result = service.findByCompositionHashAndCode(compositionHash, code)

        assertThat(result).isSuccessWithData(healthcareResource)
    }

    @Test
    fun `#getHealthcareResourceAssociatedToBundle should get resource by composition hash if bundle just have it`() = runBlocking {
        val healthcareBundle = healthcareBundle.copy(compositionHash = "123")

        coEvery {
            data.findOne(queryEq {
                where {
                    this.compositionHash.eq(healthcareBundle.compositionHash!!)
                }
            })
        } returns healthcareResource.toModel().success()

        val result = service.getHealthcareResourceAssociatedToBundle(healthcareBundle)

        assertThat(result).isSuccessWithData(healthcareResource)
    }

    @Test
    fun `#getHealthcareResourceAssociatedToBundle should get resource by composition hash and code if bundle have both`() = runBlocking {
        val healthcareBundle = healthcareBundle.copy(
            compositionHash = "123",
            code = "abc"
        )

        coEvery {
            data.findOne(queryEq {
                where {
                    this.compositionHash.eq(healthcareBundle.compositionHash!!)
                        .and(this.code.eq(healthcareBundle.code!!))
                }
            })
        } returns healthcareResource.toModel().success()

        val result = service.getHealthcareResourceAssociatedToBundle(healthcareBundle)

        assertThat(result).isSuccessWithData(healthcareResource)
    }

    @Test
    fun `#getHealthcareResourceAssociatedToBundle should throw not found exception when bundle doesnt have composition hash`() = runBlocking {
        val result = service.getHealthcareResourceAssociatedToBundle(healthcareBundle)

        assertThat(result).isFailureOfType(NotFoundException::class)
    }
    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findByTussCodeOrCode should return successfully`() = runBlocking {
        val codes = listOf(healthcareResource.code)

        coEvery {
            data.find(queryEq {
                where {
                    this.code.inList(codes).or(this.tussCode.inList(codes)) and this.active.eq(true)
                }
            })
        } returns listOf(healthcareResource.toModel()).success()

        val result = service.findByTussCodeOrCode(codes)

        assertThat(result).isSuccessWithData(listOf(healthcareResource))
    }

    @Test
    fun `#findByCodes should return only active`() = runBlocking {
        val codes = listOf(healthcareResource.code)

        coEvery {
            data.find(queryEq {
                where {
                    this.code.inList(codes).and(this.active.eq(true))
                }
            })
        } returns listOf(healthcareResource.toModel()).success()

        val result = service.findByCodes(codes)

        assertThat(result).isSuccessWithData(listOf(healthcareResource))
    }

    @Test
    fun `#findByCodes should return all possible resources`() = runBlocking {
        val codes = listOf(healthcareResource.code)

        coEvery {
            data.find(queryEq {
                where {
                    this.code.inList(codes)
                }
            })
        } returns listOf(healthcareResource.toModel()).success()

        val result = service.findByCodes(codes, onlyActive = false)

        assertThat(result).isSuccessWithData(listOf(healthcareResource))
    }

}
