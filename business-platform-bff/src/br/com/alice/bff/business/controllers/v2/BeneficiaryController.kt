package br.com.alice.bff.business.controllers.v2

import br.com.alice.bff.business.converters.v2.toBeneficiaryTransport
import br.com.alice.bff.business.converters.v2.toFriendlyEnumResponse
import br.com.alice.bff.business.converters.v2.toHrDependentTransport
import br.com.alice.bff.business.converters.v2.toInsuranceStatus
import br.com.alice.bff.business.converters.v2.toResponse
import br.com.alice.bff.business.converters.v2.toSummaryResponse
import br.com.alice.bff.business.converters.v2.toTimeline
import br.com.alice.bff.business.models.v2.BeneficiaryDependentRequest
import br.com.alice.bff.business.models.v2.BeneficiaryDetailResponse
import br.com.alice.bff.business.models.v2.BeneficiaryIsCancelableResponse
import br.com.alice.bff.business.models.v2.BeneficiaryProductChangeRequest
import br.com.alice.bff.business.models.v2.BeneficiaryProductChangeResponse
import br.com.alice.bff.business.models.v2.BeneficiaryScheduledProductChange
import br.com.alice.bff.business.models.v2.ParentBeneficiaryRelationTypeOption
import br.com.alice.bff.business.models.v2.ProductChangeResponse
import br.com.alice.bff.business.models.v2.RelationTypeResponse
import br.com.alice.bff.business.services.ContractService
import br.com.alice.bff.business.services.ProductCachedService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyStaffService
import br.com.alice.business.client.MailerService
import br.com.alice.business.metrics.BeneficiaryMetric.BeneficiaryCreationFlow.HR_PORTAL
import br.com.alice.business.metrics.BeneficiaryMetric.beneficiaryCreatedIncrement
import br.com.alice.business.model.BeneficiaryTransport
import br.com.alice.business.model.ChangePlanType
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.ErrorResponse
import br.com.alice.common.MultipartRequest
import br.com.alice.common.Response
import br.com.alice.common.ValidationErrorResponse
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.toResponse
import br.com.alice.communication.email.model.EmailAddress
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.MemberProductChangeSchedule
import br.com.alice.data.layer.models.MemberProductChangeScheduleStatus
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.data.layer.models.RequestedBy
import br.com.alice.data.layer.models.SourceType
import br.com.alice.data.layer.models.toOnboardingStatus
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.FileType
import br.com.alice.filevault.models.PersonVaultDeleteByteArray
import br.com.alice.filevault.models.PersonVaultUploadByteArray
import br.com.alice.hr.core.client.HrBeneficiaryService
import br.com.alice.membership.client.MemberProductChangeScheduleService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.content.ByteArrayContent
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class BeneficiaryController(
    companyStaffService: CompanyStaffService,
    private val beneficiaryService: BeneficiaryService,
    private val memberService: MemberService,
    private val personService: PersonService,
    private val productService: ProductCachedService,
    private val memberProductChangeScheduleService: MemberProductChangeScheduleService,
    private val fileVaultActionService: FileVaultActionService,
    private val contractService: ContractService,
    private val emailService: MailerService,
    private val hrBeneficiaryService: HrBeneficiaryService
) : BaseController(companyStaffService) {

    companion object {
        const val BUSINESS_FILE_VAULT_STORAGE_DOMAIN = "business"
        const val BUSINESS_FILE_VAULT_STORAGE_NAMESPACE = "dependent_files"
    }

    suspend fun getBeneficiary(beneficiaryId: String): Response {
        // Get beneficiary and check if staff is allowed to see it
        val beneficiary = beneficiaryService
            .get(beneficiaryId.toUUID())
            .get()

        return validateCompany(beneficiary.companyId) {
            coroutineScope {
                // TODO(lolivei): Improve readability
                // Load dependents, parent, member and all person related
                val beneficiaryWithDependentDeferred =
                    async { beneficiaryService.withOnboardingAndDependents(beneficiary) }
                val parentBeneficiaryDeferred =
                    async { beneficiary.parentBeneficiary?.let { beneficiaryService.get(it).get() } }
                val scheduledProductChangeDeferred = async { getScheduledProductChange(beneficiary) }

                val beneficiary = beneficiaryWithDependentDeferred.await().get()
                val parentBeneficiary = parentBeneficiaryDeferred.await()
                val scheduledProductChange = scheduledProductChangeDeferred.await().getOrNull()

                val dependents = getCurrentDependents(beneficiary.dependents)

                // Fetch beneficiary member and persons
                val (member, persons) = getMemberAndPerson(beneficiary, parentBeneficiary, dependents)
                val productSummary = productService.getProductSummary(member.productId)

                // Build dependents and parent response
                val holderResponse =
                    parentBeneficiary?.let { holder -> holder.toSummaryResponse(persons.first { holder.personId == it.id }) }

                val insuranceStatus = beneficiary.toInsuranceStatus(member)
                val timeline = beneficiary.onboarding?.toTimeline(member, insuranceStatus) ?: emptyList()

                logger.info(
                    "Getting beneficiary",
                    "beneficiaryId" to beneficiary.id,
                    "personId" to member.personId,
                    "companyId" to beneficiary.companyId,
                    "membershipId" to member.id,
                    "membershipStatus" to member.status,
                    "insuranceStatus" to insuranceStatus,
                    "beneficiaryType" to beneficiary.type,
                    "beneficiaryOnboardingId" to beneficiary.onboarding?.id,
                )

                // Build beneficiary response
                val person = persons.first { it.id == beneficiary.personId }
                BeneficiaryDetailResponse(
                    id = beneficiary.id,
                    personId = member.personId.toUUID(),
                    fullName = person.fullSocialName,
                    nationalId = person.nationalId,
                    birthDate = person.dateOfBirth,
                    email = person.email,
                    phoneNumber = person.phoneNumber,
                    type = beneficiary.type.toFriendlyEnumResponse(),
                    insuranceStatus = insuranceStatus.toFriendlyEnumResponse(),
                    address = person.addresses.first(),
                    dependents = dependents.map { dependent -> dependent.toSummaryResponse(persons.first { dependent.personId == it.id }) },
                    plan = productSummary,
                    holder = holderResponse,
                    activatedAt = member.activationDate ?: beneficiary.activatedAt,
                    canceledAt = beneficiary.canceledAt,
                    canceledReason = beneficiary.canceledReason,
                    canceledDescription = beneficiary.canceledDescription,
                    hasContributed = beneficiary.hasContributed,
                    onboardingStatus = beneficiary.onboarding?.currentPhase?.phase
                        ?.toOnboardingStatus()
                        .toFriendlyEnumResponse(),
                    scheduledProductChange = scheduledProductChange,
                    timeline = timeline,
                    subContractId = beneficiary.companySubContractId
                ).toResponse()
            }
        }
    }

    // In some cases, there are multiple dependent beneficiaries with the same parentId, so we need to get the current ones
    private suspend fun getCurrentDependents(dependents: List<Beneficiary>?): List<Beneficiary> =
        if (dependents.isNullOrEmpty()) emptyList()
        else memberService.getCurrentsByIds(dependents.map { it.memberId })
                .mapEach { it.id }
                .map { memberIds -> dependents.filter { memberIds.contains(it.memberId) } }
                .get()


    suspend fun abortBeneficiaryCancel(beneficiaryId: String): Response {
        val beneficiary = beneficiaryService
            .get(beneficiaryId.toUUID())
            .get()

        return validateCompany(beneficiary.companyId) {
            beneficiaryService.abortBeneficiaryScheduledCancel(beneficiaryId.toUUID())
                .foldResponse()
        }
    }

    private suspend fun getMemberAndPerson(
        beneficiary: Beneficiary,
        parentBeneficiary: Beneficiary?,
        dependents: List<Beneficiary>
    ) = coroutineScope {
        val personIds = (dependents.map { it.personId } + beneficiary.personId + parentBeneficiary?.personId)
            .filterNotNull()
            .map { it.toString() }

        val memberDeferred = async { memberService.getCurrent(beneficiary.personId) }
        val personsDeferred = async { personService.findByIds(personIds) }

        val persons = personsDeferred.await().get()
        val member = memberDeferred.await().get()

        Pair(
            member,
            persons
        )
    }

    private suspend fun getScheduledProductChange(beneficiary: Beneficiary): Result<BeneficiaryScheduledProductChange?, Throwable> =
        memberProductChangeScheduleService.getSchedulesByMemberAndStatus(
            beneficiary.memberId,
            MemberProductChangeScheduleStatus.REQUESTED
        )
            .map { it.firstOrNull() }
            .map {
                it?.let { request ->
                    val product = productService.getProductSummary(request.productId)
                    BeneficiaryScheduledProductChange(
                        id = product.id,
                        title = product.displayName.orEmpty(),
                        applyAt = request.applyAt
                    )
                }
            }

    suspend fun changeProduct(beneficiaryId: UUID, request: BeneficiaryProductChangeRequest): Response {
        val beneficiary = beneficiaryService.get(beneficiaryId).get()
        val productId = request.productId

        return validateCompany(beneficiary.companyId) {

            val applyDate = contractService.getApplyDateBasedOnGroupCompany(beneficiary.companyId)
                ?: return@validateCompany ValidationErrorResponse("Company contract not found or invalid")

            val requestedBy = RequestedBy(
                id = companyStaff().id,
                source = SourceType.COMPANY_STAFF
            )

            val schedule = MemberProductChangeSchedule(
                memberId = beneficiary.memberId,
                productId = productId,
                personId = beneficiary.personId,
                applyAt = applyDate,
                requestedBy = requestedBy
            )

            memberProductChangeScheduleService.create(schedule).then {
                val companyStaff = companyStaff()
                val sendTo = EmailAddress(
                    email = companyStaff.email,
                    name = companyStaff.fullName()
                )
                emailService.sendChangePlanEmail(
                    sendTo = sendTo,
                    schedule = schedule,
                    memberId = beneficiary.memberId,
                    mailType = ChangePlanType.PLAN_CHANGE_REQUESTED
                )
            }
                .map {
                    BeneficiaryProductChangeResponse(
                        scheduleId = it.id,
                        beneficiaryId = beneficiary.id,
                        productId = productId,
                        applyAt = it.applyAt
                    )
                }.foldResponse()
        }
    }

    suspend fun cancelProductChange(beneficiaryId: UUID): Response {
        val beneficiary = beneficiaryService.get(beneficiaryId).get()

        return validateCompany(beneficiary.companyId) {
            val companyStaff = companyStaff()
            val sendTo = EmailAddress(
                email = companyStaff.email,
                name = companyStaff.fullName()
            )
            memberProductChangeScheduleService
                .getSchedulesByMemberAndStatus(beneficiary.memberId, MemberProductChangeScheduleStatus.REQUESTED)
                .flatMap {
                    it.firstOrNull()?.success()
                        ?: NotFoundException("No product change scheduled for beneficiary").failure()
                }
                .map { memberProductChangeScheduleService.cancel(it.id) }
                .map {
                    emailService.sendChangePlanEmail(
                        sendTo = sendTo,
                        schedule = it.get(),
                        memberId = beneficiary.memberId,
                        mailType = ChangePlanType.PLAN_CHANGE_CANCELED
                    )
                }
                .foldResponse()


        }
    }

    suspend fun uploadFile(personId: String, multipartRequest: MultipartRequest): Response {
        val attachment = multipartRequest.file
        val attachmentContent = multipartRequest.fileContent

        logger.info(
            "BeneficiaryController::uploadFiles",
            "multipartRequest" to multipartRequest
        )

        if (attachment == null) {
            return ValidationErrorResponse("No file found in request")
        }

        val fileName = attachment.nameWithoutExtension
        val fileExtension = attachment.extension
        val content = attachmentContent!!.readBytes()

        val file = PersonVaultUploadByteArray(
            domain = BUSINESS_FILE_VAULT_STORAGE_DOMAIN,
            namespace = BUSINESS_FILE_VAULT_STORAGE_NAMESPACE,
            originalFileName = fileName,
            fileContent = content,
            fileType = FileType.fromExtension(fileExtension)!!,
            personId = personId.toPersonId(),
            fileSize = content.size.toLong()
        )

        return fileVaultActionService.uploadFile(file).map {
            it.toResponse()
        }.foldResponse()
    }

    suspend fun deleteFile(personId: String, fileId: String): Response {

        logger.info(
            "BeneficiaryController::deleteFile",
            "fileId" to fileId,
            "personId" to personId
        )
        val file = PersonVaultDeleteByteArray(
            domain = BUSINESS_FILE_VAULT_STORAGE_DOMAIN,
            namespace = BUSINESS_FILE_VAULT_STORAGE_NAMESPACE,
            fileName = fileId,
            personId = personId.toPersonId()
        )
        return fileVaultActionService.deleteFile(file).foldResponse()
    }

    suspend fun getProductChangeNextDate(companyId: UUID): Response {
        return validateCompany(companyId) {
            val date = contractService.getApplyDateBasedOnGroupCompany(companyId)
                ?: return@validateCompany ValidationErrorResponse("Company contract not found or invalid")

            ProductChangeResponse(date.toString()).toResponse()
        }
    }

    suspend fun isCancelable(beneficiaryId: UUID): Response =
        //TODO: move this logic to the service
        beneficiaryService
            .get(beneficiaryId)
            .flatMap { beneficiary ->
                when {
                    beneficiary.memberStatus != MemberStatus.ACTIVE ||
                    beneficiary.type != BeneficiaryType.EMPLOYEE -> true.success()
                    else -> beneficiaryService
                        .countActiveWithNoPendingCancellation(beneficiary.companyId, BeneficiaryType.EMPLOYEE)
                        .map {
                            it >= 2
                        }
                }
            }
            .map {BeneficiaryIsCancelableResponse(canCancel = it) }
            .foldResponse()

    suspend fun getRelationTypes(companyId: UUID, subcontractId: UUID): Response {
        logger.info("Finding relation types for subcontract",
            "company_id" to companyId,
            "subcontract_id" to subcontractId
        )

        return hrBeneficiaryService.getRelationTypes(companyId, subcontractId)
            .map { RelationTypeResponse(it.map { relation -> relation.toOption() }) }
            .foldResponse()
    }

    suspend fun addBeneficiaryDependent(companyId: UUID, request: BeneficiaryDependentRequest): Response =
        validateCompany(companyId) {
            request.toHrDependentTransport().let { hrDependentTransport ->
                hrBeneficiaryService.createDependent(hrDependentTransport, companyId).flatMap { beneficiary ->
                    sendInclusionEmail(beneficiary, hrDependentTransport.toBeneficiaryTransport(companyId))
                }
            }.foldResponse(HttpStatusCode.Created)
        }

    suspend fun downloadBeneficiarySheetTemplate(companyId: UUID): Response =
        hrBeneficiaryService.getBeneficiarySheetTemplate(companyId).fold(
        { templateSheetResult ->
            logger.info("BeneficiaryController::getBeneficiarySheetTemplate", "company_id" to companyId)

            Response(
                status = HttpStatusCode.OK,
                    headers = mapOf(
                        HttpHeaders.ContentDisposition to "attachment; filename=\"${templateSheetResult.fileName}\"",
                        HttpHeaders.ContentType to "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    ),
                    message = ByteArrayContent(
                        bytes = templateSheetResult.fileByteContent,
                        contentType = ContentType.Application.OctetStream
                    )
                )
        }, {
            logger.error("Error getting beneficiary sheet template", "company_id" to companyId, "error" to it)

            Response(
                status = HttpStatusCode.InternalServerError,
                message = ErrorResponse(
                    code = "internal_server_error",
                    message = "Error getting beneficiary sheet template"
                )
            )
        })

    private suspend fun sendInclusionEmail(beneficiary: Beneficiary, beneficiaryTransport: BeneficiaryTransport): Result<Beneficiary, Throwable> {
        val companyStaff = companyStaff()
        val sendTo = EmailAddress(
            companyStaff.fullName(),
            companyStaff.email
        )

        beneficiaryCreatedIncrement(beneficiary.id, HR_PORTAL)
        emailService.sendInclusionEmail(sendTo, beneficiaryTransport)

        return beneficiary.success()
    }

    private fun ParentBeneficiaryRelationType.toOption() =
        ParentBeneficiaryRelationTypeOption(this.name, this.description)
}
