package br.com.alice.nullvs.services.internals

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.logging.logger
import br.com.alice.common.redis.GenericCache
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.Company
import br.com.alice.nullvs.exceptions.ManySubcontractsToContractException
import br.com.alice.nullvs.exceptions.NoSubcontractForCompanyException
import br.com.alice.nullvs.logics.CacheHelper
import br.com.alice.nullvs.logics.TotvsRequestLogic.validate
import br.com.alice.nullvs.models.company.CompanyInfo
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import redis.clients.jedis.exceptions.JedisException
import java.util.UUID

class CompanyInfoCacheService(
    private val cache: GenericCache,
    private val contractService: CompanyContractService,
    private val companyService: CompanyService,
    private val subcontractService: CompanySubContractService,
    private val beneficiaryService: BeneficiaryService,
) {
    suspend fun getByBeneficiary(beneficiary: Beneficiary): Result<CompanyInfo, Throwable> {
        if (beneficiary.companySubContractId == null) {
            return getCompanyInfo(beneficiary)
        }

        return coResultOf {
            try {
                cache.get(
                    CacheHelper.getCompanyInfoBySubContractIdKey(
                        beneficiary.companySubContractId!!
                    ),
                    CompanyInfo::class,
                    expirationTime = CacheHelper.FIVE_MINUTES_IN_SECONDS,
                ) {
                    getCompanyInfo(beneficiary).then {
                        logger.info(
                            "CompanyInfo was cached",
                            "companyId" to it.companyId,
                            "company_sub_contract_id" to beneficiary.companySubContractId,
                        )
                    }.get()
                }
            } catch (err: JedisException) {
                logger.error(
                    "It's not possible use the cache now",
                    "reason" to err
                )
                getCompanyInfo(beneficiary).get()
            }.validate()
        }
    }

    private suspend fun getCompanyInfo(beneficiary: Beneficiary) = companyService.get(beneficiary.companyId)
        .then {
            logger.info(
                "getCompanyInfoByBeneficiary: Found company",
                "company_id" to it.id,
                "company_totvs_contract" to it.totvsContract,
                "company_totvs_subcontract" to it.totvsSubContract,
                "company_contract_id" to it.contractIds,
            )
        }
        .map { company ->
            val subcontract =
                beneficiary.companySubContractId?.let {
                    subcontractService.get(it).getOrNullIfNotFound()
                } ?: getBeneficiarySubcontract(beneficiary, company)

            val contract =
                contractService.get(subcontract.contractId)
                    .then { logger.info("getCompanyInfoByBeneficiary: Found contract") }
                    .get()

            val contractNumber = contract.externalId
            val subcontractNumber = subcontract.externalId

            CompanyInfo(
                companyId = company.id,
                cnpj = company.cnpj,
                contractId = contract.id,
                contractNumber = contractNumber,
                subContractId = subcontract.id,
                subcontractNumber = subcontractNumber,
                hasCompanyProductPriceListing = subcontract.avaliableCompanyProductPriceListing.isNotNullOrEmpty(),
            )
        }

    private suspend fun getBeneficiarySubcontract(beneficiary: Beneficiary, company: Company) =
        subcontractService.findByCompanyId(company.id)
            .then {
                logger.info(
                    "getBeneficiarySubcontract: Found subcontract",
                    "subcontracts" to it.map { subcontract -> subcontract.id }
                )
            }
            .flatMap {
                if (it.isEmpty())
                    throw NoSubcontractForCompanyException(
                        beneficiary.memberId,
                        beneficiary.companyId,
                    )
                else if (it.size > 1)
                    throw ManySubcontractsToContractException(
                        beneficiary.memberId,
                        beneficiary.companyId,
                        company.contractIds,
                    )
                else {
                    val subcontract = it.first()
                    beneficiaryService.update(beneficiary.copy(companySubContractId = subcontract.id))
                        .map { subcontract }
                }
            }.get()

    suspend fun invalidate(companySubContractId: UUID): Result<Int, Throwable> = coResultOf {
        cache.invalidateKey(
            CacheHelper.getCompanyInfoBySubContractIdKey(
                companySubContractId
            ),
        )
    }
}
