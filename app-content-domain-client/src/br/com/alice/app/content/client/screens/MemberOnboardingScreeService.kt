package br.com.alice.app.content.client.screens

import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.common.core.PersonId
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface MemberOnboardingScreeService: Service {

    override val namespace get() = "app_content"
    override val serviceName get() = "member_onboarding_screen"

    suspend fun getCallToComplete(appVersion: SemanticVersion): Result<ScreensTransport, Throwable>

    suspend fun getRecommendedPhysicians(personId: PersonId): Result<ScreensTransport, Throwable>

    suspend fun getRecommendedPhysicianDetails(personId: PersonId, healthcareTeamId: UUID, recommended: Boolean): Result<ScreensTransport, Throwable>
}
