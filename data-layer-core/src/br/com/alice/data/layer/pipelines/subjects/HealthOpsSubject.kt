package br.com.alice.data.layer.pipelines.subjects

import br.com.alice.common.core.Role
import br.com.alice.data.layer.models.StaffModel
import java.util.UUID

open class HealthOpsSubject(
    id: UUID,
    role: Role,
) : StaffSubject(
    id = id,
    role = role
) {
    constructor(staff: StaffModel) : this(
        id = staff.id,
        role = staff.role
    )

    override fun viewAnonymized(): Boolean = true
}
