package br.com.alice.hr.core

import br.com.alice.common.core.BaseConfig
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object ServiceConfig : BaseConfig(HoconApplicationConfig(ConfigFactory.load("application.conf"))) {
    object DataAPI {
        val baseUrl = config("dataapi.baseUrl")
    }

    object EmailTemplate {
        val firebaseSignInMagicLinkTemplate = config("emailTemplate.firebaseSignInMagicLinkTemplate")
    }

    object PinPoint {
        val campaignId = config("pinPoint.campaignId")
    }

    object S3 {
        val templateSheetBucketUrl = config("s3.templateSheetBucketUrl")
    }
}
