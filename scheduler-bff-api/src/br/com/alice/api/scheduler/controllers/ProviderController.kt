package br.com.alice.api.scheduler.controllers

import br.com.alice.api.scheduler.controllers.model.ProviderUnitResponse
import br.com.alice.api.scheduler.controllers.model.ProviderUnitsResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.convertTo
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.provider.client.ProviderUnitService
import io.ktor.http.Parameters
import kotlinx.coroutines.coroutineScope

class ProviderController(
    private val providerUnitService: ProviderUnitService,
) : Controller() {
    suspend fun getProviders(params: Parameters) = coroutineScope {
        val query = params["q"]
        convertToProviderUnitResponse(providerUnitService.getActiveAndShowOnScheduler(null, query).get())
    }

    private fun convertToProviderUnitResponse(providersUnits: List<ProviderUnit>) =
        providersUnits.map { providerUnit ->
            providerUnit.convertTo(ProviderUnitResponse::class)
        }.let { ProviderUnitsResponse(it) }.toResponse()
}
