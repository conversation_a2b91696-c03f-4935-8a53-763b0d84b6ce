{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["PersonModel", "Generic<PERSON><PERSON><PERSON><PERSON>", "BeneficiaryCompiledViewModel"], "actions": ["view", "count"]}, {"resources": ["MemberModel", "Beneficiary<PERSON><PERSON>l", "CassiMemberModel", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel"], "actions": ["view", "count", "update", "create"]}, {"resources": ["PersonOnboardingModel", "PersonRegistrationModel"], "actions": ["view", "create", "count"]}, {"resources": ["MemberInvoiceModel", "InvoicePaymentModel", "ResourceSignTokenModel", "ItauPaymentModel", "CancelPaymentOnAcquirerScheduleModel", "PaymentDetailModel", "InvoiceItemModel", "MemberInvoiceGroupModel", "PreActivationPaymentModel", "BolepixPaymentDetailModel", "PixPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "BoletoPaymentDetailModel", "MemberTelegramTrackingModel"], "actions": ["view", "update", "create"]}, {"resources": ["CompanyModel", "CompanyContractModel", "CompanySubContractModel"], "actions": ["view", "count", "update"]}, {"resources": ["HealthDeclaration"], "actions": ["create"]}]}]}