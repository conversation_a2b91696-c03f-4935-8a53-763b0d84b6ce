package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.controllers.model.TestResultFileItemResponse
import br.com.alice.api.ehr.controllers.model.TestResultFileResponse
import br.com.alice.common.Response
import br.com.alice.common.convertTo
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.extensions.toLocalDateTime
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.foldResponse
import br.com.alice.common.storage.AliceFile
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.TestResultFile
import br.com.alice.data.layer.models.UploadedBy.STAFF
import br.com.alice.ehr.client.TestResultFileService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.map

class TestResultFileController(
    private val testResultFileService: TestResultFileService,
    staffService: StaffService,
) : StaffController(staffService) {

    suspend fun getAll(personId: PersonId): Response {
        val results = testResultFileService.findByPersonId(personId.toString()).map { files ->
            files.sortedByDescending { it.performedAt }
                .map { it.convertTo(TestResultFileItemResponse::class) }
        }.get()

        return TestResultFileResponse(
            results = results,
            editable = true
        ).toResponse()
    }

    suspend fun create(personId: PersonId, request: TestResultFileRequest): Response =
        testResultFileService.add(
            TestResultFile(
                personId = personId,
                staffId = currentStaffId(),
                performedAt = request.performedAt.toLocalDateTime(),
                file = request.file,
                description = request.description,
                fileExtension = request.file.type,
                uploadedBy = STAFF
            )
        ).foldResponse()

    suspend fun delete(id: String) = testResultFileService.delete(id.toUUID()).foldResponse()
}

data class TestResultFileRequest(
    val description: String,
    val performedAt: String,
    val file: AliceFile
)
