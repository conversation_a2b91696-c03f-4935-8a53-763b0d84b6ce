package br.com.alice.exec.indicator.routes

import br.com.alice.appointment.event.AppointmentExecutedProcedureGroupEvent
import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.internals.ConsumerJob
import br.com.alice.data.layer.events.DbLaboratoryTestResultProcessCreatedEvent
import br.com.alice.eita.nullvs.events.NullvsAttachmentChemotherapyUpsertedResponseEvent
import br.com.alice.eita.nullvs.events.NullvsAttachmentOpmeUpsertedResponseEvent
import br.com.alice.eita.nullvs.events.NullvsAttachmentRadiotherapyUpsertedResponseEvent
import br.com.alice.eita.nullvs.events.NullvsGuiaExtensionUpsertedResponseEvent
import br.com.alice.eita.nullvs.events.NullvsGuiaUpsertedResponseEvent
import br.com.alice.eventinder.events.HealthEventUncoordinatedEvent
import br.com.alice.eventinder.events.ProcessedHealthEventsEvent
import br.com.alice.exec.indicator.consumers.AppointmentProcedureExecutedGroupConsumer
import br.com.alice.exec.indicator.consumers.EligibilityConsumer
import br.com.alice.exec.indicator.consumers.ExecuteTotvsGuiaProceduresV2Consumer
import br.com.alice.exec.indicator.consumers.ExecutionGroupConsumer
import br.com.alice.exec.indicator.consumers.GuiaWithProceduresUpsertedConsumer
import br.com.alice.exec.indicator.consumers.HealthEventUncoordinatedConsumer
import br.com.alice.exec.indicator.consumers.HealthcareBundleUpsertedConsumer
import br.com.alice.exec.indicator.consumers.HealthcareResourceGroupAssociationUpdatedConsumer
import br.com.alice.exec.indicator.consumers.HealthcareResourceUpsertedConsumer
import br.com.alice.exec.indicator.consumers.MagicNumbersConsumer
import br.com.alice.exec.indicator.consumers.NullvsAttachmentChemotherapyUpsertedConsumer
import br.com.alice.exec.indicator.consumers.NullvsAttachmentOpmeUpsertedConsumer
import br.com.alice.exec.indicator.consumers.NullvsAttachmentRadiotherapyUpsertedConsumer
import br.com.alice.exec.indicator.consumers.NullvsGuiaExtensionUpsertedConsumer
import br.com.alice.exec.indicator.consumers.NullvsGuiaUpsertedConsumer
import br.com.alice.exec.indicator.consumers.ProcessedHealthEventsConsumer
import br.com.alice.exec.indicator.consumers.ResourceBundleSpecialtyPricingUpdateCreatedConsumer
import br.com.alice.exec.indicator.consumers.TotvsGuiaUpsertedConsumer
import br.com.alice.exec.indicator.consumers.UnlinkHealthcareResourceFromHealthcareBundleConsumer
import br.com.alice.exec.indicator.events.EligibilityCheckedEvent
import br.com.alice.exec.indicator.events.ExecuteTotvsGuiaProceduresEventV2
import br.com.alice.exec.indicator.events.GuiaWithProceduresUpsertedEvent
import br.com.alice.exec.indicator.events.HealthSpecialistResourceBundleUpsertedEvent
import br.com.alice.exec.indicator.events.HealthcareBundleUpsertedEvent
import br.com.alice.exec.indicator.events.HealthcareResourceGroupAssociationUpdatedEvent
import br.com.alice.exec.indicator.events.HealthcareResourceUpsertedEvent
import br.com.alice.exec.indicator.events.MagicNumbersCreatedEvent
import br.com.alice.exec.indicator.events.ResourceBundleSpecialtyPricingUpdateCreatedEvent
import br.com.alice.exec.indicator.events.TotvsGuiaCreatedEvent
import br.com.alice.exec.indicator.events.UnlinkHealthcareResourceFromHealthcareBundleEvent

fun ConsumerJob.Configuration.kafkaRoutes() {

    val executeTotvsGuiaProceduresV2Consumer by inject<ExecuteTotvsGuiaProceduresV2Consumer>()
    consume(
        "execute-totvs-guia-procedures-v2",
        ExecuteTotvsGuiaProceduresEventV2.name,
        executeTotvsGuiaProceduresV2Consumer::consume
    )

    val executionGroupConsumer by inject<ExecutionGroupConsumer>()
    consume(
        "process-attendance-result",
        DbLaboratoryTestResultProcessCreatedEvent.name,
        executionGroupConsumer::consume
    )

    val eligibilityConsumer by inject<EligibilityConsumer>()
    consume(
        "save-eligibility-checked",
        EligibilityCheckedEvent.name,
        eligibilityConsumer::saveCheckFromEvent
    )

    val healthEventUncoordinatedConsumer by inject<HealthEventUncoordinatedConsumer>()
    consume(
        "sync-eventinder-health-event-with-eita",
        HealthEventUncoordinatedEvent.name,
        healthEventUncoordinatedConsumer::process
    )

    val processedHealthEventsConsumer by inject<ProcessedHealthEventsConsumer>()
    consume(
        "eventinder-processed-health-events",
        ProcessedHealthEventsEvent.name,
        processedHealthEventsConsumer::process
    )

    val nullvsGuiaUpsertedConsumer by inject<NullvsGuiaUpsertedConsumer>()
    consume(
        "exec-indicator-upsert-totvs-guia",
        NullvsGuiaUpsertedResponseEvent.name,
        nullvsGuiaUpsertedConsumer::onGuiaUpserted
    )

    val nullvsAttachmentOpmeUpsertedConsumer by inject<NullvsAttachmentOpmeUpsertedConsumer>()
    consume(
        "exec-indicator-upsert-attachment-opme",
        NullvsAttachmentOpmeUpsertedResponseEvent.name,
        nullvsAttachmentOpmeUpsertedConsumer::onAttachmentOpmeUpserted
    )

    val totvsGuiaUpsertedConsumer by inject<TotvsGuiaUpsertedConsumer>()
    consume(
        "exec-indicator-totvs-guia-upserted",
        TotvsGuiaCreatedEvent.name,
        totvsGuiaUpsertedConsumer::sendCrmEvent
    )

    val healthcareResourceGroupAssociationUpdatedConsumer by inject<HealthcareResourceGroupAssociationUpdatedConsumer>()
    consume(
        "healthcare-resource-group-association-updated-send-bundle-updated-event",
        HealthcareResourceGroupAssociationUpdatedEvent.name,
        healthcareResourceGroupAssociationUpdatedConsumer::sendBundleUpdatedEvents,
    )

    val guiaWithProceduresUpsertedConsumer by inject<GuiaWithProceduresUpsertedConsumer>()
    consume(
        "guia-with-procedures-upserted-event",
        GuiaWithProceduresUpsertedEvent.name,
        guiaWithProceduresUpsertedConsumer::syncContent,
    )

    val unlinkHealthcareResourceFromHealthcareBundleConsumer by inject<UnlinkHealthcareResourceFromHealthcareBundleConsumer>()
    consume(
        "unlink-healthcare-resource-to-healthcare-bundle-event",
        UnlinkHealthcareResourceFromHealthcareBundleEvent.name,
        unlinkHealthcareResourceFromHealthcareBundleConsumer::unlinkHealthcareResource
    )

    val nullvsGuiaExtensionUpsertedConsumer by inject<NullvsGuiaExtensionUpsertedConsumer>()
    consume(
        "exec-indicator-upsert-totvs-guia-extension",
        NullvsGuiaExtensionUpsertedResponseEvent.name,
        nullvsGuiaExtensionUpsertedConsumer::onGuiaExtensionUpserted
    )

    val nullvsAttachmentChemotherapyUpsertedConsumer by inject<NullvsAttachmentChemotherapyUpsertedConsumer>()
    consume(
        "exec-indicator-upsert-attachment-chemotherapy",
        NullvsAttachmentChemotherapyUpsertedResponseEvent.name,
        nullvsAttachmentChemotherapyUpsertedConsumer::onAttachmentChemotherapyUpserted
    )

    val nullvsAttachmentRadiotherapyUpsertedConsumer by inject<NullvsAttachmentRadiotherapyUpsertedConsumer>()
    consume(
        "exec-indicator-upsert-attachment-radiotherapy",
        NullvsAttachmentRadiotherapyUpsertedResponseEvent.name,
        nullvsAttachmentRadiotherapyUpsertedConsumer::onAttachmentRadiotherapyUpserted
    )

    val healthcareResourceUpsertedConsumer by inject<HealthcareResourceUpsertedConsumer>()
    consume(
        "exec-indicator-upserted-healthcare-resource",
        HealthcareResourceUpsertedEvent.name,
        healthcareResourceUpsertedConsumer::consume
    )

    val healthcareBundleUpsertedConsumer by inject<HealthcareBundleUpsertedConsumer>()
    consume(
        "exec-indicator-upserted-bundle-resource",
        HealthcareBundleUpsertedEvent.name,
        healthcareBundleUpsertedConsumer::consume
    )

    val appointmentProcedureExecutedGroupConsumer by inject<AppointmentProcedureExecutedGroupConsumer>()
    consume(
        "exec-indicator-save-appointment-procedure-executed-alice-score",
        AppointmentExecutedProcedureGroupEvent.name,
        appointmentProcedureExecutedGroupConsumer::saveAppointmentProcedureExecutedAliceSource
    )
    consume(
        "exec-indicator-log-not-priced-procedures",
        AppointmentExecutedProcedureGroupEvent.name,
        appointmentProcedureExecutedGroupConsumer::logNotPricedProcedures
    )

    val magicNumbersConsumer by inject<MagicNumbersConsumer>()
    consume(
        "should-expire-old-code",
        MagicNumbersCreatedEvent.name,
        magicNumbersConsumer::shouldExpireOldCode
    )

    val resourceBundleSpecialtyPricingUpdateCreatedConsumer by inject<ResourceBundleSpecialtyPricingUpdateCreatedConsumer>()
    consume(
        "process-resource-bundle-specialty-pricing-update-created",
        ResourceBundleSpecialtyPricingUpdateCreatedEvent.name,
        resourceBundleSpecialtyPricingUpdateCreatedConsumer::processResourceBundleSpecialtyPricingUpdateCreated
    )
}
