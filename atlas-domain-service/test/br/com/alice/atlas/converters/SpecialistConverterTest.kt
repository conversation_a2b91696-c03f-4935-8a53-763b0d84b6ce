package br.com.alice.atlas.converters

import br.com.alice.atlas.converters.PhoneConverter.phoneConverter
import br.com.alice.atlas.converters.SpecialistConverter.CONTRACT_ORIGIN_ALICE
import br.com.alice.atlas.converters.SpecialistConverter.CONTRACT_ORIGIN_CASSI
import br.com.alice.atlas.model.SpecialistTransport
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.SpecialistAppointmentType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class SpecialistConverterTest {

    @Test
    fun `healthProfessionalToSpecialist returns SpecialistTransport when health professional name is not null`() {
        val includedPlans = listOf("Conforto")
        val healthProfessional = TestModelFactory.buildHealthProfessional(
            name = "Dr. House",
            specialtyId = null,
            subSpecialtyIds = emptyList(),
        ).copy(
            contacts = listOf(TestModelFactory.buildContact().copy(address = TestModelFactory.buildStructuredAddress())),
            addressesStructured = null,
        )
        val expected = SpecialistTransport(
            name = healthProfessional.name,
            photo = healthProfessional.imageUrl,
            council = healthProfessional.council.toString(),
            phones = healthProfessional.phones.takeIf { it.isNotEmpty() }?.map { phoneConverter(it) },
            address = healthProfessional.contacts?.mapNotNull { it.address?.formattedAddress() },
            remote = healthProfessional.appointmentTypes.contains(SpecialistAppointmentType.REMOTE),
            contractOrigin = CONTRACT_ORIGIN_ALICE,
            specialty = null,
            subSpecialties = null,
            includedPlans = includedPlans,
            about = healthProfessional.profileBio,
        )

        val result = SpecialistConverter.healthProfessionalToSpecialist(healthProfessional, includedPlans, null, emptyList())

        assertEquals(expected, result)
    }

    @Test
    fun `providerUnitToSpecialist returns SpecialistTransport`() {
        val includedPlans = listOf("Conforto")
        val providerUnit = TestModelFactory.buildProviderUnit(
            name = "Dr. House",
            medicalSpecialtyProfile = null,
            contractOrigin = ProviderUnit.Origin.CASSI,
        )
        val expected = SpecialistTransport(
            name = providerUnit.name,
            photo = providerUnit.imageUrl,
            council = null,
            phones = providerUnit.phones.takeIf { it.isNotEmpty() }?.map { phoneConverter(it) },
            address = null,
            remote = false,
            contractOrigin = CONTRACT_ORIGIN_CASSI,
            specialty = null,
            subSpecialties = null,
            includedPlans = includedPlans,
            about = null,
        )

        val result = SpecialistConverter.providerUnitToSpecialist(providerUnit, includedPlans, null, emptyList())

        assertEquals(expected, result)
    }

    @Test
    fun `cassiSpecialistToSpecialist returns SpecialistTransport`() {
        val includedPlans = listOf("Conforto")
        val cassiSpecialist = TestModelFactory.buildCassiSpecialist(
            name = "Dr. House",
        ).copy(
            contacts = listOf(TestModelFactory.buildContact().copy(address = TestModelFactory.buildStructuredAddress())),
        )
        val expected = SpecialistTransport(
            name = cassiSpecialist.name,
            photo = cassiSpecialist.imageUrl,
            council = cassiSpecialist.council.toString(),
            phones = cassiSpecialist.phones.takeIf { it.isNotEmpty() }?.map { phoneConverter(it) },
            address = cassiSpecialist.contacts?.mapNotNull { it.address?.formattedAddress() },
            remote = cassiSpecialist.appointmentTypes.contains(SpecialistAppointmentType.REMOTE),
            contractOrigin = CONTRACT_ORIGIN_CASSI,
            specialty = null,
            subSpecialties = null,
            includedPlans = includedPlans,
            about = null,
        )

        val result = SpecialistConverter.cassiSpecialistToSpecialist(cassiSpecialist, includedPlans, null, emptyList())

        assertEquals(expected, result)
    }

    @Test
    fun `cassiSpecialistToSpecialist returns SpecialistTransport but get address from address field`() {
        val cassiSpecialist = TestModelFactory.buildCassiSpecialist(
            name = "Dr. House",
            subSpecialtyIds = emptyList(),
        ).copy(
            address = listOf(TestModelFactory.buildStructuredAddress()),
        )

        val result = SpecialistConverter.cassiSpecialistToSpecialist(cassiSpecialist, listOf("Conforto"), null, emptyList())

        assertEquals("Dr. House", result.name)
    }
}
