package br.com.alice.businessrisk.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.events.BeneficiaryOnboardingPhaseChangedEvent
import br.com.alice.businessrisk.clients.MacoService
import br.com.alice.businessrisk.exceptions.MacoIncompleteDataException
import br.com.alice.businessrisk.exceptions.StandardCostNotFoundForProductException
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coFoldError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class BeneficiaryOnboardingPhaseChangedConsumer(
    private val companyService: CompanyService,
    private val beneficiaryService: BeneficiaryService,
    private val macoService: MacoService
) : Consumer() {
    suspend fun handleCalculateMaco(event: BeneficiaryOnboardingPhaseChangedEvent) =
        withSubscribersEnvironment {
            val beneficiaryId = event.payload.beneficiaryId
            val newPhase = event.payload.newPhase

            if (!shouldCalculateMaco()) {
                logger.info(
                    "BeneficiaryOnboardingPhaseChangedConsumer::handleCalculateMaco - skip the maco calculation by FF.should_calculate_maco",
                    "beneficiary_id" to beneficiaryId,
                )

                return@withSubscribersEnvironment false.success()
            }

            logger.info(
                "BeneficiaryOnboardingPhaseChangedConsumer::handleCalculateMaco - calculate and save maco",
                "beneficiary_id" to beneficiaryId,
            )

            if (newPhase.phase != BeneficiaryOnboardingPhaseType.WAITING_CPTS_APPLICATION) {
                logger.info(
                    "BeneficiaryOnboardingPhaseChangedConsumer::handleCalculateMaco - skip this event once the calc is not necessary to it",
                    "beneficiary_id" to beneficiaryId,
                    "phase" to newPhase.phase,
                )

                return@withSubscribersEnvironment true.success()
            }

            beneficiaryService.get(beneficiaryId)
                .flatMap { companyService.get(it.companyId) }
                .map { company ->
                    company.contractIds.pmap { contractId ->
                        logger.info(
                            "CompanyConsumer::calculateMaco - Start to calculate the maco by contract",
                            "contract_id" to contractId
                        )

                        macoService.calculate(contractId)
                            .flatMap {
                                logger.info("CompanyConsumer::calculateMaco - Save the calculated maco")
                                macoService.saveMacos(it)
                            }
                    }
                }.coFoldError(MacoIncompleteDataException::class to {
                    logger.error("Missing fields for maco calculation", it)
                    true.success()
                }, StandardCostNotFoundForProductException::class to {
                    logger.error("Standard cost not found for product", it)
                    true.success()
                })
        }

    private fun shouldCalculateMaco() =
        FeatureService.get(FeatureNamespace.BUSINESS_RISK, "should_calculate_maco", false)
}
