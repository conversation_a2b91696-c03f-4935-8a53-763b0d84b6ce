package br.com.alice.membership.ioc

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.rfc.HttpInvoker
import br.com.alice.common.rfc.Invoker
import br.com.alice.membership.MembershipDomainConfiguration
import br.com.alice.membership.SERVICE_NAME
import br.com.alice.membership.client.*
import br.com.alice.membership.client.onboarding.*
import org.koin.core.qualifier.named
import org.koin.dsl.module

val MembershipClientModule = module {

    val baseUrl = MembershipDomainConfiguration.baseUrl
    val invoker = HttpInvoker(DefaultHttpClient(timeoutInMillis = 20_000), "$baseUrl/rfc")

    single<Invoker>(named(SERVICE_NAME)) { invoker }

    single<DeviceService> { DeviceServiceClient(get(named(SERVICE_NAME))) }
    single<PersonLoginService> { PersonLoginServiceClient(get(named(SERVICE_NAME))) }
    single<ContractRegistry> { ContractRegistryClient(get(named(SERVICE_NAME))) }
    single<HealthDeclarationForm> { HealthDeclarationFormClient(get(named(SERVICE_NAME))) }
    single<OnboardingService> { OnboardingServiceClient(get(named(SERVICE_NAME))) }
    single<ShoppingService> { ShoppingServiceClient(get(named(SERVICE_NAME))) }
    single<PersonRegistrationService> { PersonRegistrationServiceClient(get(named(SERVICE_NAME))) }
    single<ContractGenerator> { ContractGeneratorClient(get(named(SERVICE_NAME))) }
    single<GasProcessService> { GasProcessServiceClient(get(named(SERVICE_NAME))) }
    single<MembershipPersonService> { MembershipPersonServiceClient(get(named(SERVICE_NAME))) }
    single<HealthDeclarationService> { HealthDeclarationServiceClient(get(named(SERVICE_NAME))) }
    single<OperationOnboardingService> { OperationOnboardingServiceClient(get(named(SERVICE_NAME))) }
    single<SessionsService> { SessionsServiceClient(get(named(SERVICE_NAME))) }
    single<HealthDeclarationAppointmentScheduler> { HealthDeclarationAppointmentSchedulerClient(get(named(SERVICE_NAME))) }
    single<LeadAnonymizationService> { LeadAnonymizationServiceClient(get(named(SERVICE_NAME))) }
    single<HealthGoalService> { HealthGoalServiceClient(get(named(SERVICE_NAME))) }
    single<PersonTaskService> { PersonTaskServiceClient(get(named(SERVICE_NAME))) }
    single<PersonTestResultFileService> { PersonTestResultFileServiceClient(get(named(SERVICE_NAME))) }
    single<PromoCodeService> { PromoCodeServiceClient(get(named(SERVICE_NAME))) }
    single<PersonHealthcareTeamRecommendationService> {
        PersonHealthcareTeamRecommendationServiceClient(get(named(SERVICE_NAME)))
    }
    single<ProductOrderService> { ProductOrderServiceClient(get(named(SERVICE_NAME))) }
    single<ShoppingCartService> { ShoppingCartServiceClient(get(named(SERVICE_NAME))) }
    single<GracePeriodService> { GracePeriodServiceClient(get(named(SERVICE_NAME))) }
    single<InvoicePriceService> { InvoicePriceServiceClient(get(named(SERVICE_NAME))) }
    single<FaqGroupService> { FaqGroupServiceClient(get(named(SERVICE_NAME))) }
    single<FaqFeedbackService> { FaqFeedbackServiceClient(get(named(SERVICE_NAME))) }
    single<FaqContentService> { FaqContentServiceClient(get(named(SERVICE_NAME))) }
    single<HealthcareTeamRecommendationService> { HealthcareTeamRecommendationServiceClient(get(named(SERVICE_NAME))) }
    single<HealthcareTeamRecommendationRuleService> {
        HealthcareTeamRecommendationRuleServiceClient(get(named(SERVICE_NAME)))
    }
    single<HealthcareTeamRecommendationRuleToRecommendationService> {
        HealthcareTeamRecommendationRuleToRecommendationServiceClient(get(named(SERVICE_NAME)))
    }
    single<DocumentService> { DocumentServiceClient(get(named(SERVICE_NAME))) }
    single<UpdateAppRuleService> { UpdateAppRuleServiceClient(get(named(SERVICE_NAME))) }
    single<CptsService> { CptsServiceClient(get(named(SERVICE_NAME))) }
    single<PersonPreferencesService> { PersonPreferencesServiceClient(get(named(SERVICE_NAME))) }
    single<EmailCommunicationService> { EmailCommunicationServiceClient(get(named(SERVICE_NAME))) }
    single<PersonDocumentsUploadService> { PersonDocumentsUploadServiceClient(get(named(SERVICE_NAME))) }
    single<PersonBenefitService> { PersonBenefitServiceClient(get(named(SERVICE_NAME))) }
    single<MemberContractTermService> { MemberContractTermServiceClient(get(named(SERVICE_NAME))) }
    single<UpdatedPersonContactInfoTempService> { UpdatedPersonContactInfoTempServiceClient(get(named(SERVICE_NAME))) }
    single<MemberProductChangeScheduleService> { MemberProductChangeScheduleServiceClient(get(named(SERVICE_NAME))) }
    single<MemberPriceService> { MemberPriceServiceClient(get(named(SERVICE_NAME))) }
    single<MemberLifeCycleEventsService> { MemberLifeCycleEventsServiceClient(get(named(SERVICE_NAME))) }
}
