{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["PersonModel", "StaffModel", "HealthProfessionalModel", "HealthCommunitySpecialistModel", "ProviderUnitModel", "StructuredAddress", "ContactModel", "ProviderModel", "ProductRecommendation", "ConsolidatedAccreditedNetwork"], "actions": ["count"]}, {"resources": ["ProductModel", "PersonSalesInfo", "ProductBundleModel", "PromoCodeModel", "ProductPriceListingModel", "PriceListingModel", "ProviderModel", "ProviderUnitModel", "StructuredAddress", "ContactModel", "ProviderUnitGroupModel", "StaffModel", "HealthProfessionalModel", "MedicalSpecialtyModel", "HealthcareTeamModel", "HealthCommunitySpecialistModel", "ProductRecommendation", "CoveredGeoRegionModel", "ConsolidatedAccreditedNetwork", "CassiSpecialistModel"], "actions": ["view"]}, {"resources": ["SiteAccreditedNetwork", "SiteAccreditedNetworkProvider", "SiteAccreditedNetworkAddress", "SiteAccreditedNetworkCategory"], "actions": ["view", "count"]}, {"resources": ["PersonModel", "PersonLoginModel", "ProductOrderModel", "PersonOnboardingModel", "Lead", "HealthDeclaration", "PersonRegistrationModel", "InsurancePortabilityRequestModel", "HealthProductSimulation", "ShoppingCartModel", "Opportunity", "HealthProductSimulationGroup", "UpdatedPersonContactInfoTempModel", "ZipcodeAddress", "CoveredGeoRegionModel"], "actions": ["view", "create", "update"]}, {"resources": ["VicProductOption"], "actions": ["view", "create", "update", "count", "delete"]}, {"resources": ["SiteAccreditedNetworkFlagship"], "actions": ["view", "count"]}]}]}