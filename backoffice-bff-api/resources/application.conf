ktor {
    deployment {
        port = 8080
        port = ${?PORT}
        connectionGroupSize = 2
        workerGroupSize = 2
        callGroupSize = 4
    }
    application {
        modules = [ br.com.alice.api.backoffice.ApplicationKt.module ]
    }
    httpclient {
        timeout = 500
        timeout = ${?HTTP_CLIENT_TIMEOUT}
    }
}

systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}

development {
  localhost = "host.docker.internal"
  localhost = ${?LOCALHOST}
  baseUrl = "http://"${development.localhost}":8062"
}

test {
  baseUrl = "http://localhost:9000"
}

production {
  host = "backoffice-bff-api.wonderland.engineering"
  host = ${?BACKOFFICE_BFF_API_HOST}
  baseUrl = "https://"${production.host}""
}
