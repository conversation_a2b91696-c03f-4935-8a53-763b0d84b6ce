package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.UUIDv7
import br.com.alice.data.layer.models.ResourceSignTokenType
import java.time.LocalDateTime
import java.util.UUID

internal data class ResourceSignTokenTable(
    override val id: UUID = RangeUUID.generate(),
    val signUuid: UUIDv7,
    val resourceType: ResourceSignTokenType,
    val resourceId: String,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val deletedAt: LocalDateTime? = null,
) : Table<ResourceSignTokenTable>, SoftDeletable {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime): ResourceSignTokenTable =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}

