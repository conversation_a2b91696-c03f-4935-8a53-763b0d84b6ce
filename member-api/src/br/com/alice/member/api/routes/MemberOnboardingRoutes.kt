package br.com.alice.member.api.routes

import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.member.api.controllers.MemberOnboardingController
import br.com.alice.member.api.controllers.onboarding.MemberOnboardingV2Controller
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route

fun Route.memberOnboardingRoutes() {
    authenticate {
        route("/member_onboarding") {
            val memberOnboardingController by inject<MemberOnboardingController>()

            post("/begin") { coHandler(memberOnboardingController::startOnboarding) }
            put("/{stepId}") { coHandler("stepId", memberOnboardingController::saveOnboardingAndGetNextStep) }
            put("/drop") { coHandler(memberOnboardingController::dropOnboarding) }
            put("/end") { co<PERSON>andler(memberOnboardingController::findLastStep) }
        }

        route("/v2/member_onboarding") {
            val memberOnboardingV2Controller by inject<MemberOnboardingV2Controller>()

            get("/steps") { coHandler(memberOnboardingV2Controller::getMemberOnboarding) }
            get("/product_details") { coHandler(memberOnboardingV2Controller::getProductDetails) }
            get("/alice_info") { coHandler(memberOnboardingV2Controller::getAliceExperience) }
            get("/v2/alice_info") { coHandler(memberOnboardingV2Controller::getAliceExperienceV2) }
            put("/{memberOnboardingId}/step_finished/{stepType}") {
                coHandler(
                    parameter1 = "memberOnboardingId",
                    parameter2 = "stepType",
                    memberOnboardingV2Controller::finishStep
                )
            }
            get("/health_declaration_cover") { coHandler(memberOnboardingV2Controller::getHealthDeclarationCover) }
            get("/conclusion_screen") { coHandler(memberOnboardingV2Controller::getConclusion) }
            route("/screen") {
                get("/call_to_complete") { coHandler(memberOnboardingV2Controller::getCallToComplete) }
            }
            get("/mfc_cover") { coHandler(memberOnboardingV2Controller::getMFCCover) }
        }
    }
}
