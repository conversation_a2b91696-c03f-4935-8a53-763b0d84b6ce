package br.com.alice.api.scheduler.webhooks

import br.com.alice.common.Response
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.observability.Spannable
import br.com.alice.common.toResponse
import br.com.alice.schedule.model.events.GoogleCalendarEventNotificationEvent
import br.com.alice.schedule.model.googlecalendar.GoogleCalendarEventWebhookNotification

class GoogleCalendarWebhookReceiver(
    private val kafkaProducerService: KafkaProducerService
) : Spannable {

    suspend fun receive(channelId: String, eventId: String, resourceUri: String): Response = span("receive") { span ->
        span.setAttribute("channel_id", channelId)
        span.setAttribute("event_id", eventId)
        span.setAttribute("resource_uri", resourceUri)

        val eventUpdatePayload = GoogleCalendarEventWebhookNotification(
            channelId = channelId.toUUID(),
            resourceId = eventId,
            resourceUri = resourceUri,
        )

        val result = kafkaProducerService.produce(GoogleCalendarEventNotificationEvent(eventUpdatePayload))
        span.setAttribute("kafka_result", result.toString())
        result.toResponse()
    }

}
