package br.com.alice.data.layer.seeds

import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.FeatureType
import br.com.alice.data.layer.tables.FeatureConfigTable

class FeatureConfigSeed : Seed() {
    override fun run() {
        val featureConfigRepo = repoFactory.get(FeatureConfigTable::class)

        featureConfigRepo.add(
            FeatureConfigTable(
                namespace = FeatureNamespace.MEMBERSHIP,
                key = "allowed_user_list",
                description = "Lista de pessoas que efetuam login sem validação de senha",
                type = FeatureType.LIST,
                value = "83688811291,23747832008,93679302088,07599711023",
                active = true,
                isPublic = false
            )
        )
                
        featureConfigRepo.add(
            FeatureConfigTable(
                namespace = FeatureNamespace.CHANNELS,
                key = "aa_channels_view_permission",
                description = "Roles que podem ser adicionadas nos channels",
                type = FeatureType.LIST,
                value = "PRODUCT_TECH,DIGITAL_CARE_NURSE,HEALTHCARE_TEAM_NURSE,CHIEF_PHYSICIAN,MANAGER_PHYSICIAN,NUTRITIONIST,DIGITAL_CARE_PHYSICIAN",
                active = true,
                isPublic = false
            )
        )

    }
}
