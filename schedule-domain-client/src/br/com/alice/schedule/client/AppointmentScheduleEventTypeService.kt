package br.com.alice.schedule.client

import br.com.alice.common.core.Status
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.data.layer.models.AppointmentScheduleEventType
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.EventTypeProviderUnit
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.schedule.model.AppointmentScheduleEventTypeLocationAvailability
import br.com.alice.schedule.model.AppointmentScheduleEventTypeWithProviderUnits
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface AppointmentScheduleEventTypeService : Service {

    override val namespace get() = "schedule"
    override val serviceName get() = "appointment_schedule_event_type"

    suspend fun findBy(filters: AppointmentScheduleEventTypeFilters): Result<List<AppointmentScheduleEventType>, Throwable>

    suspend fun countBy(filters: AppointmentScheduleEventTypeFilters): Result<Int, Throwable>

    suspend fun createWithProviderUnits(
        appointmentScheduleEventType: AppointmentScheduleEventType,
        eventTypeProviderUnits: List<AppointmentScheduleEventTypeLocationAvailability>
    ): Result<AppointmentScheduleEventType, Throwable>

    suspend fun updateWithProviderUnits(
        appointmentScheduleEventType: AppointmentScheduleEventType,
        eventTypeProviderUnits: List<AppointmentScheduleEventTypeLocationAvailability>
    ): Result<AppointmentScheduleEventType, Throwable>

    suspend fun update(
        appointmentScheduleEventType: AppointmentScheduleEventType,
    ): Result<AppointmentScheduleEventType, Throwable>

    suspend fun get(
        id: UUID
    ): Result<AppointmentScheduleEventType, Throwable>

    suspend fun getWithProviderUnits(
        id: UUID
    ): Result<AppointmentScheduleEventTypeWithProviderUnits, Throwable>

    suspend fun getActiveByTitle(title: String): Result<List<AppointmentScheduleEventType>, Throwable>

    suspend fun getActiveByIds(ids: List<UUID>): Result<List<AppointmentScheduleEventType>, Throwable>

    suspend fun findByRange(range: IntRange): Result<List<AppointmentScheduleEventType>, Throwable>

    suspend fun countAll(): Result<Int, Throwable>

    suspend fun count(
        searchQuery: String? = null,
        appointmentScheduleType: AppointmentScheduleType? = null,
        status: List<Status>?
    ): Result<Int, Throwable>

    suspend fun query(
        searchQuery: String? = null,
        appointmentScheduleType: AppointmentScheduleType? = null,
        status: List<Status>?,
        range: IntRange,
    ): Result<List<AppointmentScheduleEventTypeWithProviderUnits>, Throwable>

    suspend fun getBySpecialties(specialtyIds: List<UUID>): Result<List<AppointmentScheduleEventType>, Throwable>

    suspend fun getBySubSpecialties(
        specialtyIds: List<UUID>,
        category: AppointmentScheduleType? = null
    ): Result<List<AppointmentScheduleEventType>, Throwable>

    suspend fun getByIds(ids: List<UUID>): Result<List<AppointmentScheduleEventType>, Throwable>

    suspend fun getProviderUnitsName(eventTypeProviderUnits: List<EventTypeProviderUnit>): Result<Map<UUID, ProviderUnit>, Throwable>

}

data class AppointmentScheduleEventTypeFilters(
    val ids: List<UUID>? = null,
    val statuses: List<Status>? = null,
    val title: String? = null,
    val categories: List<AppointmentScheduleType>? = null,
    val searchQuery: String? = null,
    val specialtyIds: List<UUID>? = null,
    val subSpecialtyIds: List<UUID>? = null,
    val isMultiProfessionalReferral: Boolean? = null,
    val range: IntRange? = null,
    val sortOrder: SortOrder = SortOrder.Descending
) {
    fun isValid() {
        if (ids.isNotNullOrEmpty()) return
        if (statuses.isNotNullOrEmpty()) return
        if (title != null) return
        if (categories.isNotNullOrEmpty()) return
        if (searchQuery != null) return
        if (specialtyIds.isNotNullOrEmpty()) return
        if (subSpecialtyIds.isNotNullOrEmpty()) return
        if (isMultiProfessionalReferral != null) return
        if (range != null) return

        throw IllegalArgumentException("Invalid filters")
    }
}
