package br.com.alice.app.content

import br.com.alice.action.plan.ioc.ActionPlanDomainClientModule
import br.com.alice.app.content.ioc.DataLayerServiceModule
import br.com.alice.app.content.ioc.ServiceModule
import br.com.alice.app.content.routes.apiRoutes
import br.com.alice.app.content.routes.kafkaRoutes
import br.com.alice.app.content.routes.recurringRoutes
import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.authentication.firebase
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.communication.ioc.CommunicationModule
import br.com.alice.data.layer.APP_CONTENT_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.duquesa.ioc.DuquesaDomainClientModule
import br.com.alice.ehr.ioc.EhrDomainClientModule
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.healthlogic.ioc.HealthLogicDomainClientModule
import br.com.alice.member.onboarding.ioc.MemberOnboardingDomainClientModule
import br.com.alice.membership.ioc.MembershipClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.product.ioc.ProductDomainClientModule
import br.com.alice.provider.ioc.ProviderDomainClientModule
import br.com.alice.questionnaire.ioc.QuestionnaireDomainClientModule
import br.com.alice.refund.ioc.RefundDomainClientModule
import br.com.alice.schedule.ioc.AppointmentScheduleDomainClientModule
import br.com.alice.screening.ioc.ScreeningDomainClientModule
import br.com.alice.sortinghat.ioc.SortingHatDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import br.com.alice.testresult.ioc.TestResultDomainClientModule
import br.com.alice.wanda.ioc.WandaDomainClientModule
import io.ktor.server.application.Application
import io.ktor.server.auth.Authentication
import io.ktor.server.routing.routing
import org.koin.core.module.Module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    val dependencyInjectionModules = listOf(
        PersonDomainClientModule,
        FeatureConfigDomainClientModule,
        DataLayerServiceModule,
        QuestionnaireDomainClientModule,
        ServiceModule,
        KafkaProducerModule,
        ActionPlanDomainClientModule,
        MembershipClientModule,
        MemberOnboardingDomainClientModule,
        WandaDomainClientModule,
        HealthLogicDomainClientModule,
        TestResultDomainClientModule,
        ProductDomainClientModule,
        EhrDomainClientModule,
        DuquesaDomainClientModule,
        RefundDomainClientModule,
        ScreeningDomainClientModule,
        CommunicationModule,
        AppointmentScheduleDomainClientModule,
        ProviderDomainClientModule,
        StaffDomainClientModule,
        SortingHatDomainClientModule,
        ClinicalAccountDomainClientModule,
    )
}

@JvmOverloads
fun Application.module(dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules,
                       startRoutesSync: Boolean = true) {
    setupDomainService(dependencyInjectionModules) {
        install(Authentication) {
            <EMAIL>()
            firebase()
        }

        routing {
            application.attributes.put(PolicyRootServiceKey, APP_CONTENT_DOMAIN_ROOT_SERVICE_NAME)
            apiRoutes()
            recurringRoutes()
        }

        kafkaConsumer(startRoutesSync = startRoutesSync) {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }

        featureConfigBootstrap(
            FeatureNamespace.ALICE_APP,
            FeatureNamespace.CHANNELS,
            FeatureNamespace.SCREENING,
            FeatureNamespace.DUQUESA,
            FeatureNamespace.MEMBERSHIP,
            FeatureNamespace.HEALTH_LOGICS,
            FeatureNamespace.SCHEDULE,
            FeatureNamespace.HEALTH_PLAN
        )
    }
}
