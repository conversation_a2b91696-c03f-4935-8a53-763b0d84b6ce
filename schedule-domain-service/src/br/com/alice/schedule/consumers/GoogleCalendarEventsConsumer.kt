package br.com.alice.schedule.consumers

import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.SchedulePreference
import br.com.alice.schedule.extensions.shouldSkipQueryEvents
import br.com.alice.schedule.model.events.GoogleCalendarEventCreationRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarEventCreationRequestedPayload
import br.com.alice.schedule.model.events.GoogleCalendarEventNotificationEvent
import br.com.alice.schedule.model.events.GoogleCalendarEventsFromRecurrenceRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarFetchInstancesForRecurrentEventsEvent
import br.com.alice.schedule.model.events.GoogleCalendarFetchInstancesForRecurrentEventsForPageEvent
import br.com.alice.schedule.model.events.GoogleCalendarFinishedEventsQueryEvent
import br.com.alice.schedule.model.events.GoogleCalendarQueryEventsEvent
import br.com.alice.schedule.model.events.GoogleCalendarRecurringEventCreationRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarSynchronizationRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarWebhookSubscriptionRequestedEvent
import br.com.alice.schedule.model.events.RenewGoogleCalendarWebhooksRequestedEvent
import br.com.alice.schedule.model.googlecalendar.GoogleCalendarEventPayload
import br.com.alice.schedule.services.GoogleCalendarEventServiceImpl
import br.com.alice.schedule.services.SchedulePreferenceServiceImpl
import com.github.kittinunf.result.success
import io.opentelemetry.api.trace.Span

class GoogleCalendarEventsConsumer(
    private val googleCalendarEventService: GoogleCalendarEventServiceImpl,
    private val schedulePreferenceService: SchedulePreferenceServiceImpl
) : Consumer() {

    suspend fun createGoogleCalendarEvent(event: GoogleCalendarEventCreationRequestedEvent) =
        span("createGoogleCalendarEvent") { span ->
            withSubscribersEnvironment {
                val payload = event.payload
                val googleCalendarEvent = payload.event
                val staffId = payload.staffId
                span.setCreateGoogleCalendarEvent(payload, googleCalendarEvent)

                schedulePreferenceService.getByStaffId(staffId).get().let { schedulePreference ->
                    span.setAttribute("has_staff_schedules", schedulePreference.hasStaffSchedules)
                    span.setAttribute("is_doing_full_sync", schedulePreference.isDoingFullSync)
                    if (schedulePreference.shouldSkipQueryEvents()) return@withSubscribersEnvironment true.success()
                }

                googleCalendarEventService.createOrUpdateEvent(googleCalendarEvent, staffId)
            }
        }

    suspend fun createGoogleRecurringCalendarEvent(event: GoogleCalendarRecurringEventCreationRequestedEvent) =
        span("createGoogleRecurringCalendarEvent") { span ->
            withSubscribersEnvironment {
                val payload = event.payload
                val googleCalendarEvent = payload.event
                val staffId = payload.staffId
                span.setCreateGoogleCalendarEvent(payload, googleCalendarEvent)

                schedulePreferenceService.getByStaffId(staffId).get().let { schedulePreference ->
                    span.setAttribute("has_staff_schedules", schedulePreference.hasStaffSchedules)
                    span.setAttribute("is_doing_full_sync", schedulePreference.isDoingFullSync)
                    if (schedulePreference.shouldSkipQueryEvents()) return@withSubscribersEnvironment true.success()
                }

                googleCalendarEventService.createOrUpdateEvent(googleCalendarEvent, payload.staffId)
            }
        }

    suspend fun synchronizeGoogleCalendar(event: GoogleCalendarSynchronizationRequestedEvent) =
        withSubscribersEnvironment {
            logSyncGoogleCalendar(
                event.payload.schedulePreference,
                "synchronizeGoogleCalendar",
            )

            googleCalendarEventService.synchronizeGoogleCalendar(
                event.payload.schedulePreference,
                event.payload.startDate
            )
        }

    suspend fun createWebhookChannelToReceiveEventUpdates(event: GoogleCalendarWebhookSubscriptionRequestedEvent) =
        withSubscribersEnvironment {
            logSyncGoogleCalendar(
                event.payload.schedulePreference,
                "createWebhookChannelToReceiveEventUpdates",
            )

            googleCalendarEventService.registerWebhookChannel(event.payload.schedulePreference)
        }

    suspend fun createOrUpdateEventFromWebhookNotification(event: GoogleCalendarEventNotificationEvent) =
        withSubscribersEnvironment {
            logger.info(
                "GoogleCalendarEventsConsumer::createOrUpdateEventFromWebhookNotification",
                "notification_event" to event.payload.eventNotificationEvent,
            )
            googleCalendarEventService.createOrUpdateEventFromCalendarWebhook(event.payload.eventNotificationEvent)
        }

    suspend fun processGoogleCalendarFinishedEventsQueryEvent(event: GoogleCalendarFinishedEventsQueryEvent) =
        withSubscribersEnvironment {
            logger.info(
                "GoogleCalendarEventsConsumer::processGoogleCalendarFinishedEventsQueryEvent",
                "staff_id" to event.payload.staffId,
                "last_updated_time" to event.payload.lastUpdatedTime,
            )

            googleCalendarEventService.updateSchedulePreferenceWithLastQueryInformation(
                event.payload.staffId,
                event.payload.lastUpdatedTime,
                event.payload.nextSyncToken
            )
        }

    suspend fun queryGoogleCalendarEvents(event: GoogleCalendarQueryEventsEvent) =
        withSubscribersEnvironment {
            logger.info(
                "GoogleCalendarEventsConsumer::queryGoogleCalendarEvents",
                "staff_id" to event.payload.staffId,
                "query_filters" to event.payload.queryFilters,
                "already_republished" to event.payload.alreadyRepublished
            )

            googleCalendarEventService.queryGoogleCalendarEvents(
                event.payload.queryFilters,
                event.payload.staffId,
                event.payload.refreshToken,
                event.payload.alreadyRepublished
            )
        }

    suspend fun renewWebhookChannelsCloseToExpiration(event: RenewGoogleCalendarWebhooksRequestedEvent) =
        withSubscribersEnvironment {
            logger.info(
                "GoogleCalendarEventsConsumer::renewWebhookChannelsCloseToExpiration",
                "reference_date" to event.payload.referenceDate.toString(),
            )

            googleCalendarEventService.renewWebhookChannelsCloseToExpiration(
                event.payload.referenceDate
            )
        }

    suspend fun queryGoogleCalendarEventsForRecurrence(event: GoogleCalendarEventsFromRecurrenceRequestedEvent) =
        withSubscribersEnvironment {
            logger.info(
                "GoogleCalendarEventsConsumer::queryGoogleCalendarEventsForRecurrence",
                "query_filters" to event.payload.queryFilters,
                "staff_id" to event.payload.staffId,
                "external_calendar_recurrent_event_id" to event.payload.queryFilters.recurringEventId,
            )

            googleCalendarEventService.queryGoogleCalendarEventsForRecurrence(
                event.payload.queryFilters,
                event.payload.staffId,
            )
        }

    suspend fun fetchInstancesForRecurrentEvents(event: GoogleCalendarFetchInstancesForRecurrentEventsEvent) =
        withSubscribersEnvironment {
            logger.info(
                "GoogleCalendarEventsConsumer::fetchInstancesForRecurrentEvents",
                "reference_date" to event.payload.referenceDate.toString(),
            )

            googleCalendarEventService.getFutureGoogleCalendarEventsActiveRecurrencesForOneDay(event.payload.referenceDate)
        }

    suspend fun fetchInstancesForRecurrentEventsForPage(event: GoogleCalendarFetchInstancesForRecurrentEventsForPageEvent) =
        withSubscribersEnvironment {
            logger.info(
                "GoogleCalendarEventsConsumer::fetchInstancesForRecurrentEventsForPage",
                "reference_date" to event.payload.referenceDate.toString(),
                "page" to event.payload.page,
                "type" to event.payload.type,
            )

            googleCalendarEventService.getFutureGoogleCalendarEventsActiveRecurrencesForOneDayForPage(
                referenceDate = event.payload.referenceDate,
                page = event.payload.page,
                type = event.payload.type
            )
        }

    private fun logSyncGoogleCalendar(
        schedulePreference: SchedulePreference,
        methodName: String,
    ) {
        val hasGoogleRefreshToken = schedulePreference.googleRefreshToken?.trim()?.isNotBlank() == true
        val hasGoogleSyncToken = schedulePreference.googleNextSyncToken?.trim()?.isNotBlank() == true

        logger.info(
            "GoogleCalendarEventsConsumer::$methodName",
            "schedule_preference_id" to schedulePreference.id,
            "staff_id" to schedulePreference.staffId,
            "google_calendar_last_updated" to schedulePreference.googleCalendarLastUpdated,
            "interval_between_events" to schedulePreference.intervalBetweenEvents,
            "weekly_hours" to schedulePreference.weeklyHours,
            "has_google_refresh_token" to hasGoogleRefreshToken,
            "has_google_sync_token" to hasGoogleSyncToken,
        )
    }

    private fun Span.setCreateGoogleCalendarEvent(
        payload: GoogleCalendarEventCreationRequestedPayload,
        googleCalendarEvent: GoogleCalendarEventPayload
    ) {
        setAttribute("staff_id", payload.staffId.toString())
        setAttribute("event_id", googleCalendarEvent.id)
        setAttribute("event_status", googleCalendarEvent.status)
        setAttribute("event_start_time", googleCalendarEvent.startTime.toString())
        setAttribute("event_end_time", googleCalendarEvent.endTime.toString())
        setAttribute("event_external_updated_at", googleCalendarEvent.externalUpdatedAt.toString())
        setAttribute("event_recurrence", googleCalendarEvent.recurrence?.map { it }.toString())
        setAttribute("event_recurring_event_id", googleCalendarEvent.recurringEventId.orEmpty())
        setAttribute("event_person_id", googleCalendarEvent.personId.toString())
        setAttribute("event_appointment_schedule_id", googleCalendarEvent.appointmentScheduleId.toString())
        setAttribute("event_transparency", googleCalendarEvent.transparency.toString())
        setAttribute("event_staff_schedule_id", googleCalendarEvent.staffScheduleId.toString())
        setAttribute("event_summary", googleCalendarEvent.summary.orEmpty())
        setAttribute("event_response_status", googleCalendarEvent.responseStatus.toString())
    }

}
