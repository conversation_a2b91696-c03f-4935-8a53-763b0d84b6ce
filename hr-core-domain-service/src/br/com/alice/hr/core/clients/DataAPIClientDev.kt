package br.com.alice.hr.core.clients

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.common.serialization.gson
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.hr.core.clients.interfaces.DataAPIClient
import br.com.alice.hr.core.ServiceConfig
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMapError
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.get
import io.ktor.client.statement.bodyAsText
import java.util.UUID

class DataAPIClientDev(
    private val client: HttpClient = DefaultHttpClient(
        {
            expectSuccess = true
            install(ContentNegotiation) {
                gsonSnakeCase {
                    registerTypeAdapter(PopulationDataResponse::class.java, PopulationDataResponseDeserializer())
                    registerTypeAdapter(FrequencyCoordinationDataResponse::class.java, FrequencyCoordinationDataResponseDeserializer())
                }

            }
        },
        timeoutInMillis = TIMEOUT_LIMIT
    )
) : DataAPIClient {
    private val baseUrl = ServiceConfig.DataAPI.baseUrl

    override suspend fun getPopulationDataByCompany(companyId: UUID): PopulationDataResponse {
        val companyMappings = getCompanyMappings()
        val mappedCompanyId = companyMappings?.get(companyId.toString()) ?: companyMappings?.get("*") ?: companyId.toString()

        return execRequest {
            client.get("$baseUrl/$mappedCompanyId/population-data").body<PopulationDataResponse>()
        }
    }

    override suspend fun getFrequencyCoordinationByCompany(companyId: UUID): FrequencyCoordinationDataResponse {
        val companyMappings = getCompanyMappings()
        val mappedCompanyId = companyMappings?.get(companyId.toString()) ?: companyMappings?.get("*") ?: companyId.toString()

        return execRequest {
            client.get("$baseUrl/$mappedCompanyId/frequency-coordination").body<FrequencyCoordinationDataResponse>()
        }
    }

    /**
     * Map company IDs to a specific production company,
     * so we can test the data API (which is prod-only)
     * in a dev environment.
     *
     * The feature flag is a JSON object with the following structure:
     * ```
     * {
     *    "$company_id": "$production_company_id"
     * }
     * ```
     */
    private fun getCompanyMappings() =
        FeatureService.get(
            namespace = FeatureNamespace.HR_CORE,
            key = "data-api-company-mapping",
            defaultValue = ""
        ).let {
            try {
                gson.fromJson<Map<String, String>>(it)
            } catch (exception: Exception) {
                logger.error(
                    "DataAPIClientDev::get error while getting feature flag",
                    "key" to "data-api-company-mapping",
                    "error_message" to exception.message
                )
                null
            }
        }

    private suspend fun <T : Any> execRequest(body: suspend () -> T): T = coResultOf<T, Throwable>(body)
        .flatMapError {
            logger.error(
                "[DataAPIClient::execRequest] Error in Data API request",
                "error" to it
            )

            if (it is ClientRequestException) {
                val responseString = it.response.bodyAsText()
                return@flatMapError DataAPIClientException(message = responseString, cause = it).failure()
            }

            it.failure()
        }.get()
}
