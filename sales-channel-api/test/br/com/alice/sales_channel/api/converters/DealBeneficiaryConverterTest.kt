package br.com.alice.sales_channel.api.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.Product
import br.com.alice.sales_channel.api.models.DealBeneficiaryAppointmentDetails
import br.com.alice.sales_channel.model.DealBeneficiary
import br.com.alice.sales_channel.model.DealBeneficiaryProduct
import br.com.alice.sales_channel.model.DealBeneficiaryStage
import br.com.alice.sales_channel.model.DealBeneficiaryStageDetails
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

class DealBeneficiaryConverterTest {
    private val beneficiaryId = RangeUUID.generate()
    private val memberId = RangeUUID.generate()
    private val personId = PersonId()
    private val products = listOf(TestModelFactory.buildProduct())
    private val beneficiaries = listOf(
        DealBeneficiary(
            id = beneficiaryId,
            name = "John Doe",
            updatedAt = LocalDateTime.now(),
            stage = DealBeneficiaryStageDetails(
                name = DealBeneficiaryStage.WAITING_FOR_CPT_ANALYSIS.name,
                friendlyName = DealBeneficiaryStage.WAITING_FOR_CPT_ANALYSIS.value,
                owner = DealBeneficiaryStage.WAITING_FOR_CPT_ANALYSIS.owner.toString(),
                count = DealBeneficiaryStage.WAITING_FOR_CPT_ANALYSIS.toStageNumber(),
                completed = 1,
            ),
            personId = personId,
            memberId = memberId,
            product = DealBeneficiaryProduct(
                id = products.first().id,
                friendlyName = products.first().displayName!!,
            )
        )
    )
    private val appointments = listOf(
        TestModelFactory.buildAppointmentSchedule(personId = personId)
    )

    @Test
    fun `should convert beneficiaries to DealBeneficiaryItem list`() {
        val memberPrices = mapOf(
            memberId to BigDecimal("500.00")
        )

        val result = DealBeneficiaryConverter.convert(beneficiaries, appointments, products, memberPrices)

        assertThat(result).hasSize(1)
        val item = result.first()
        assertThat(item.id).isEqualTo(beneficiaryId)
        assertThat(item.name).isEqualTo("John Doe")
        assertThat(item.productPrice).isEqualTo(BigDecimal("500.00"))
        assertThat(item.productTitle).isEqualTo(products.first().displayName)
        assertThat(item.appointment).isEqualTo(
            DealBeneficiaryAppointmentDetails(
                status = AppointmentScheduleStatus.SCHEDULED,
                appointmentStartTime = appointments.first().startTime.minusHours(3),
                appointmentEndTime = null,
            )
        )
    }

    @Test
    fun `should handle missing appointment and product gracefully`() {
        val appointments = emptyList<AppointmentSchedule>()
        val products = emptyList<Product>()
        val memberPrices = mapOf(
            memberId to BigDecimal("300.00")
        )

        val result = DealBeneficiaryConverter.convert(beneficiaries, appointments, products, memberPrices)

        assertThat(result).hasSize(1)
        val item = result.first()
        assertThat(item.id).isEqualTo(beneficiaryId)
        assertThat(item.name).isEqualTo("John Doe")
        assertThat(item.productPrice).isEqualTo(BigDecimal("300.00"))
        assertThat(item.productTitle).isEqualTo(beneficiaries.first().product.friendlyName)
        assertThat(item.appointment).isEqualTo(
            DealBeneficiaryAppointmentDetails(
                status = null,
                appointmentStartTime = null,
                appointmentEndTime = null,
            )
        )
    }

    @Test
    fun `should handle beneficiaries without prices`() {
        val appointments = emptyList<AppointmentSchedule>()
        val products = emptyList<Product>()
        val memberPrices = emptyMap<UUID, BigDecimal?>()

        val result = DealBeneficiaryConverter.convert(beneficiaries, appointments, products, memberPrices)

        assertThat(result).hasSize(1)
        val item = result.first()
        assertThat(item.productPrice).isNull()
    }

    @Test
    fun `should return empty list when beneficiaries list is empty`() {
        val result = DealBeneficiaryConverter.convert(emptyList(), emptyList(), emptyList(), emptyMap())

        assertThat(result).isEmpty()
    }
}
