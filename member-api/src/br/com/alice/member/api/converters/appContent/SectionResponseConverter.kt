package br.com.alice.member.api.converters.appContent

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.AppBar
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.TabBarItem
import br.com.alice.app.content.model.section.*
import br.com.alice.common.Conversion
import br.com.alice.common.convertTo
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.appContent.RemoteActionType
import java.time.Duration
import java.time.LocalDateTime

typealias MemberApiSectionData = br.com.alice.member.api.models.appContent.SectionData

object SectionResponseConverter {

    fun convert(sections: List<Section>?): List<br.com.alice.member.api.models.appContent.Section>? =
        sections?.map {
            convert(it)
        }

    fun convert(it: Section): br.com.alice.member.api.models.appContent.Section {
        val data = when (val sectionData = it.data) {
            is BridgeSection -> sectionData.convertToMemberApiSectionData()
            is ButtonSection -> sectionData.convertToMemberApiSectionData()
            is CalendarSection -> sectionData.convertToMemberApiSectionData()
            is CalloutSection -> sectionData.convertToMemberApiSectionData()
            is CardSection -> sectionData.convertToMemberApiSectionData()
            is ChatInputSection -> sectionData.convertToMemberApiSectionData()
            is CheckboxSection -> sectionData.convertToMemberApiSectionData()
            is GroupedColumnSection -> sectionData.convertToMemberApiSectionData()
            is ImageSection -> sectionData.convertToMemberApiSectionData()
            is LinkSection -> sectionData.convertToMemberApiSectionData()
            is ListCardSection -> sectionData.convertToMemberApiSectionData()
            is ListMenuSection -> sectionData.convertToMemberApiSectionData()
            is MenuSection -> sectionData.convertToMemberApiSectionData()
            is ModuleSection -> sectionData.convertToMemberApiSectionData()
            is PillResponseSection -> sectionData.convertToMemberApiSectionData()
            is QuestionnaireSection -> sectionData.convertToMemberApiSectionData()
            is SheetSection -> sectionData.convertToMemberApiSectionData()
            is TabBarSection -> sectionData.convertToMemberApiSectionData()
            is TextSection.Content -> sectionData.convertToMemberApiSectionData()
            is TextSection.ContentAction -> sectionData.convertToMemberApiSectionData()
            is GridItemSection -> sectionData.convertToMemberApiSectionData()
            is GridSection -> sectionData.convertToMemberApiSectionData()
            is InfiniteScrollSection -> sectionData.convertToMemberApiSectionData()
            is ShortcutSection -> sectionData.convertToMemberApiSectionData()
            is AAMainSection -> sectionData.convertToMemberApiSectionData()
            is StackedCarouselSection -> sectionData.convertToMemberApiSectionData()
            is ActionCardSection -> sectionData.convertToMemberApiSectionData()
            is ActivationVideoCallSection -> sectionData.convertToMemberApiSectionData()
            is HealthProfessionalInfoSection -> sectionData.convertToMemberApiSectionData()
            is HealthProfessionalInfoCardSection -> sectionData.convertToMemberApiSectionData()
            is LargeListCardSection -> sectionData.convertToMemberApiSectionData()
            is ExpandableCardSection -> sectionData.convertToMemberApiSectionData()
            is ProfileCardSection -> sectionData.convertToMemberApiSectionData()
        }

        return br.com.alice.member.api.models.appContent.Section(
            id = it.id,
            type = it.type,
            data = data,
            minAppVersion = it.minAppVersion,
            expiresCacheAt = convert(it.cacheDuration),
            requiredSections = it.requiredSections,
        )
    }

    private fun convert(duration: Duration?): LocalDateTime? {
        return duration?.let { LocalDateTime.now().plus(it) }
    }
}

private var customTryConverters: Conversion<*, *>.() -> Any? = {
    tryConvertSectionList() ?: tryConvertSection() ?: tryConvertToAppContentRemoteAction()
    ?: tryConvertToMemberApiRemoteAction() ?: tryConvertActionRouting() ?: tryConvertTabBarItems()
}

private inline fun <reified Data : SectionDataBase> Data.convertToMemberApiSectionData(): MemberApiSectionData {
    return convertTo(MemberApiSectionData::class, customTryConverters)
}

private fun Conversion<*, *>.tryConvertSection(): br.com.alice.member.api.models.appContent.Section? =
    if (fromInstance is Section && toClassifier == br.com.alice.member.api.models.appContent.Section::class) {
        SectionResponseConverter.convert(fromInstance as Section)
    } else {
        null
    }

private fun Conversion<*, *>.tryConvertSectionList(): List<br.com.alice.member.api.models.appContent.Section>? =
    if ((fromInstance as? List<*>)?.firstOrNull() is Section) {
        (fromInstance as List<Section>).map { SectionResponseConverter.convert(it) }
    } else {
        null
    }

private fun Conversion<*, *>.tryConvertTabBarItems(): List<TabBarItem>? =
    if ((fromInstance as? List<*>)?.firstOrNull() is TabBarItem) {
        (fromInstance as List<TabBarItem>).map { it.convertTo(TabBarItem::class, customTryConverters) }
    } else {
        null
    }

private fun Conversion<*, *>.tryConvertToMemberApiRemoteAction(): br.com.alice.member.api.models.appContent.RemoteAction? =
    if (fromInstance is RemoteAction && toClassifier == br.com.alice.member.api.models.appContent.RemoteAction::class) {
        val instance = fromInstance as RemoteAction
        convert(instance)
    } else {
        null
    }

private fun convert(instance: RemoteAction) =
    br.com.alice.member.api.models.appContent.RemoteAction(
        method = instance.method,
        endpoint = instance.endpoint?.tryConvertEndpointPath(),
        mobileRoute = instance.mobileRoute?.convertTo(
            MobileRouting::class,
            customTryConverters = { tryConvertActionRouting() }),
        params = instance.params?.tryConvertParams(),
        type = instance.type?.convertTo(RemoteActionType::class),
        transition = instance.transition,
        removeUntilRouteName = instance.removeUntilRouteName,
        popToRouteOnComplete = instance.popToRouteOnComplete,
    )

private fun Conversion<*, *>.tryConvertToAppContentRemoteAction(): RemoteAction? =
    if (fromInstance is RemoteAction && toClassifier == RemoteAction::class) {
        val instance = fromInstance as RemoteAction
        RemoteAction(
            method = instance.method,
            endpoint = instance.endpoint?.tryConvertEndpointPath(),
            mobileRoute = instance.mobileRoute,
            params = instance.params?.tryConvertParams(),
            type = instance.type,
            transition = instance.transition,
            removeUntilRouteName = instance.removeUntilRouteName,
            popToRouteOnComplete = instance.popToRouteOnComplete,
        )
    } else {
        null
    }

private fun Conversion<*, *>.tryConvertActionRouting(): MobileRouting? =
    if (fromInstance is ActionRouting && toClassifier == MobileRouting::class) {
        when (fromInstance as ActionRouting) {
            ActionRouting.CHESHIRE_SCREEN -> MobileRouting.CHESHIRE_SCREEN
            ActionRouting.ONBOARDING -> MobileRouting.MEMBER_ONBOARDING
            ActionRouting.HEALTH_COMMUNITY -> MobileRouting.HEALTH_COMMUNITY
            ActionRouting.HEALTH_PLAN_DETAILS -> MobileRouting.HEALTH_PLAN_DETAILS
            ActionRouting.HELP_CENTER -> MobileRouting.HELP_CENTER
            ActionRouting.EXTERNAL_APP -> MobileRouting.EXTERNAL_APP
            ActionRouting.DUQUESA_APPOINTMENT_INTRO -> MobileRouting.DUQUESA_APPOINTMENT_INTRO
            ActionRouting.ALICE_AGORA -> MobileRouting.ALICE_AGORA
            ActionRouting.DUQUESA_SCHEDULE_ESTABLISHMENT_GROUPS -> MobileRouting.DUQUESA_SCHEDULE_ESTABLISHMENT_GROUPS
            ActionRouting.DUQUESA_SCHEDULE_AVAILABILITY -> MobileRouting.DUQUESA_SCHEDULE_AVAILABILITY
            ActionRouting.DUQUESA_SCHEDULE_DETAIL -> MobileRouting.DUQUESA_SCHEDULE_DETAIL
            ActionRouting.DUQUESA_SCHEDULE_CONFIRMATION -> MobileRouting.DUQUESA_SCHEDULE_CONFIRMATION
            ActionRouting.TEST_RESULT -> MobileRouting.TEST_RESULT
            ActionRouting.TEST_RESULT_FEEDBACK -> MobileRouting.TEST_RESULT_FEEDBACK
            ActionRouting.REFUND -> MobileRouting.REFUND
            ActionRouting.PDF -> MobileRouting.PDF
            ActionRouting.CHANNEL -> MobileRouting.CHANNEL
            ActionRouting.ACCREDITED_NETWORK -> MobileRouting.ACCREDITED_NETWORK
            ActionRouting.APPOINTMENT_SCHEDULE -> MobileRouting.APPOINTMENT_SCHEDULE
            ActionRouting.PERSONAL_DATA -> MobileRouting.PERSONAL_DATA
            ActionRouting.WEBVIEW -> MobileRouting.WEBVIEW
            ActionRouting.TEST_REQUEST_UPLOAD -> MobileRouting.TEST_REQUEST_UPLOAD
            ActionRouting.HEALTH_DECLARATION -> MobileRouting.HEALTH_DECLARATION
            ActionRouting.HEALTH_TEAM -> MobileRouting.HEALTH_TEAM
            ActionRouting.HEALTH_DOCUMENTS -> MobileRouting.HEALTH_DOCUMENTS
            ActionRouting.MEMBER_ONBOARDING_V2 -> MobileRouting.MEMBER_ONBOARDING_V2
            ActionRouting.HEALTH_MEETINGS_LIST -> MobileRouting.HEALTH_MEETINGS_LIST
            ActionRouting.BENEFITS -> MobileRouting.BENEFITS
            ActionRouting.SPECIALIST_DETAILS -> MobileRouting.SPECIALIST_DETAILS
            ActionRouting.DEMAND_CHANNELS_LIST -> MobileRouting.DEMAND_CHANNELS_LIST
            ActionRouting.SCORE_MAGENTA_INPUT -> MobileRouting.SCORE_MAGENTA_INPUT
            ActionRouting.ARCHIVED_CHANNELS -> MobileRouting.ARCHIVED_CHANNELS
            ActionRouting.QUESTIONNAIRE -> MobileRouting.QUESTIONNAIRE
            ActionRouting.REDESIGN_ALICE_AGORA -> MobileRouting.REDESIGN_ALICE_AGORA
            ActionRouting.APPOINTMENT_DETAILS -> MobileRouting.APPOINTMENT_DETAILS
            ActionRouting.TEST_PREPARATION -> MobileRouting.TEST_PREPARATION
            ActionRouting.REVIEW_TERMS -> MobileRouting.REVIEW_TERMS
            ActionRouting.B2B_ACTIVATION -> MobileRouting.B2B_ACTIVATION
            ActionRouting.MEMBER_ONBOARDING_V2_COVER -> MobileRouting.MEMBER_ONBOARDING_V2_COVER
        }
    } else {
        null
    }

@Suppress("UNCHECKED_CAST")
private fun Map<String, Any>.tryConvertParams(): Map<String, Any> =
    this.map { (key, value) ->
        if (value is Map<*, *>) {
            key to (value as Map<String, Any>).tryConvertParams()
        } else if (key == "endpoint") {
            val endpointValue = value as String
            key to endpointValue.tryConvertEndpointPath()
        } else if (value is RemoteAction) {
            key to convert(value)
        } else {
            key to value
        }
    }.toMap()

private fun String.tryConvertEndpointPath(): String =
    if (this.startsWith("http")) this else ServiceConfig.url(this)

fun AppBar.formatAppBar() =
    this.copy(
        rightRemoteActionItems = this.rightRemoteActionItems?.map { remoteAction ->
            remoteAction.copy(
                action = remoteAction.action.copy(
                    endpoint = remoteAction.action.endpoint?.tryConvertEndpointPath(),
                    params = remoteAction.action.params?.tryConvertParams()
                ),
                popovers = remoteAction.popovers?.map { popover ->
                    popover.copy(
                        action = popover.action?.copy(
                            endpoint = popover.action?.endpoint?.tryConvertEndpointPath(),
                            params = popover.action?.params?.tryConvertParams()
                        )
                    )
                }
            )
        }
    )
