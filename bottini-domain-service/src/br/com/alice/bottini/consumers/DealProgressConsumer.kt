package br.com.alice.bottini.consumers

import br.com.alice.bottini.client.DealProgressService
import br.com.alice.common.logging.logger
import br.com.alice.membership.model.events.ContractPhaseStartedEvent
import br.com.alice.membership.model.events.PersonRegistrationFinishedEvent
import br.com.alice.membership.model.events.ShoppingFinishedEvent
import br.com.alice.moneyin.event.InvoiceSentEvent
import br.com.alice.onboarding.model.events.InsurancePortabilityRequestDeclinedEvent
import br.com.alice.onboarding.model.events.InsurancePortabilityRequestInProgressEvent
import br.com.alice.onboarding.model.events.InsurancePortabilityRequestSubmittedEvent
import br.com.alice.person.br.com.alice.person.model.events.FirstAccessEvent
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.person.model.events.MemberCancelledEvent
import br.com.alice.person.model.events.PersonCreatedEvent
import br.com.alice.person.model.events.PersonUpdatedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCancelledEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleNoShowEvent
import com.github.kittinunf.result.Result

class DealProgressConsumer(
    private val dealProgressService: DealProgressService
): Consumer() {

    suspend fun firstAccess(firstAccessEvent: FirstAccessEvent): Result<Any, Throwable> = withSubscribersEnvironment {
        logger.info("first access event received")
        val login = firstAccessEvent.payload

        return@withSubscribersEnvironment dealProgressService.registerFirstAccessOnDeal(login)
    }

    suspend fun moveDealStageOnShoppingFinished(event: ShoppingFinishedEvent) = withSubscribersEnvironment {
        val (productOrder, promoCodeId) = event.payload

        return@withSubscribersEnvironment dealProgressService.moveDealStageOnShoppingFinished(productOrder, promoCodeId)

    }

    suspend fun moveDealOnPortabilitySubmitted(event: InsurancePortabilityRequestSubmittedEvent) = withSubscribersEnvironment {
        val request = event.payload.request

        return@withSubscribersEnvironment dealProgressService.moveDealOnPortabilitySubmitted(request)
    }

    suspend fun moveDealOnPortabilityDecline(event: InsurancePortabilityRequestDeclinedEvent) = withSubscribersEnvironment {
        val request = event.payload.request

        return@withSubscribersEnvironment dealProgressService.moveDealOnPortabilityDecline(request)
    }

    suspend fun moveDealOnPortabilityInProgress(event: InsurancePortabilityRequestInProgressEvent) = withSubscribersEnvironment {
        val request = event.payload.request

        return@withSubscribersEnvironment dealProgressService.moveDealOnPortabilityInProgress(request)
    }

    suspend fun moveDealStageOnRegistrationFinished(event: PersonRegistrationFinishedEvent) = withSubscribersEnvironment {
        val payload = event.payload

        return@withSubscribersEnvironment dealProgressService.moveDealStageOnRegistrationFinished(payload.person.id)
    }

    suspend fun moveDealStageOnContractStarted(event: ContractPhaseStartedEvent) = withSubscribersEnvironment {
        val payload = event.payload

        return@withSubscribersEnvironment dealProgressService.moveDealStageOnContractStarted(payload.personId)
    }

    suspend fun moveDealOnInvoiceSent(event: InvoiceSentEvent) = withSubscribersEnvironment {
        val payload = event.payload

        return@withSubscribersEnvironment dealProgressService.moveDealOnInvoiceSent(payload.person.id)
    }

    suspend fun moveDealOnMemberActivated(event: MemberActivatedEvent) = withSubscribersEnvironment {
        val payload = event.payload

        return@withSubscribersEnvironment dealProgressService.moveDealOnMemberActivated(payload)
    }

    suspend fun addCancellationDataToDeal(event: MemberCancelledEvent) = withSubscribersEnvironment {
        val payload = event.payload

        return@withSubscribersEnvironment dealProgressService.moveDealOnMemberActivated(payload.member)
    }

    suspend fun moveDealOnAppointmentCreated(event: AppointmentScheduleCreatedEvent) = withSubscribersEnvironment {
        val payload = event.payload

        return@withSubscribersEnvironment dealProgressService.moveDealOnAppointmentCreated(
            payload.appointmentSchedule,
            payload.person.personId
        )
    }

    suspend fun moveDealOnAppointmentCancellation(event: AppointmentScheduleCancelledEvent) =
        withSubscribersEnvironment {
            dealProgressService.moveDealToNoShow(event.payload)
        }

    suspend fun moveDealOnAppointmentNoShow(event: AppointmentScheduleNoShowEvent) =
        withSubscribersEnvironment {
            dealProgressService.moveDealToNoShow(event.payload)
        }
}
