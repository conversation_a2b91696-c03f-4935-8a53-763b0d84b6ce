package br.com.alice.atlas.usecases

import br.com.alice.atlas.model.Accredited
import br.com.alice.atlas.model.AccreditedFilterParams
import br.com.alice.atlas.model.AccreditedSearchArea
import br.com.alice.atlas.services.AccreditedService
import br.com.alice.data.layer.models.SiteAccreditedNetwork
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success

class BuildFilterUseCase() {
    suspend fun buildFilter(
        filterParams: AccreditedFilterParams,
        searchArea: AccreditedSearchArea,
        siteAccreditedNetwork: SiteAccreditedNetwork,
        flagship: List<Accredited>? = null,
    ): Result<AccreditedService.Filter, Throwable> =
        AccreditedService.Filter(
            categories = filterParams.categories,
            geoLocation = AccreditedService.GeoLocationFilter(
                searchArea.radiusInMeters,
                searchArea.latitude,
                searchArea.longitude,
            ),
            specialtyId = filterParams.specialtyId,
            siteAccreditedNetwork = siteAccreditedNetwork,
            providerName = filterParams.providerName,
            limit = calculateLimit(filterParams.range, flagship),
            offset = calculateOffset(filterParams.range, flagship),
            rejectedReferencedIds = flagship?.map { it.referencedId },
        ).success()

    private fun calculateLimit(
        range: IntRange,
        flagship: List<Accredited>? = null,
    ): Int {
        val flagshipSize = flagship?.size ?: 0
        val rangeCount = range.count()

        return if (rangeCount < flagshipSize) 0 else rangeCount - flagshipSize
    }

    private fun calculateOffset(
        range: IntRange,
        flagship: List<Accredited>? = null,
    ): Int =
        if (range.first > 0) range.first - (flagship?.size ?: 0) else range.first
}
