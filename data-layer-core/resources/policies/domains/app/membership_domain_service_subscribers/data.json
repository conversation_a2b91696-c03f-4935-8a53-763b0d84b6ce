{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "branches": [{"conditions": ["${resource.personId} == ${subject.key}"], "allow": [{"resources": ["DeviceModel", "PersonInternalReference", "AppointmentScheduleModel", "ClinicalBackground", "Appointment"], "actions": ["view"]}, {"resources": ["OnboardingContractModel", "MemberRegistrationModel", "OnboardingBackgroundCheckModel", "MemberModel", "PersonRegistrationModel", "ProductOrderModel", "InsurancePortabilityRequestModel", "InsurancePortabilityRequestFileModel", "PersonLoginModel", "HealthDeclaration"], "actions": ["view", "update", "create"]}]}, {"conditions": ["${resource.ownerPersonId} == ${subject.key}"], "allow": [{"resources": ["PromoCodeModel"], "actions": ["view", "update", "create"]}]}, {"conditions": ["${resource.domain} == member", "${resource.namespace} == term"], "allow": [{"resources": ["FileVault"], "actions": ["view", "create"]}]}, {"conditions": ["${resource.domain} == member", "${resource.namespace} == documents"], "allow": [{"resources": ["FileVault"], "actions": ["view", "update"]}]}], "allow": [{"resources": ["Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "CompanyModel", "CompanyContractModel", "CompanySubContractModel", "Generic<PERSON><PERSON><PERSON><PERSON>"], "actions": ["view", "count"]}, {"resources": ["MemberContractTermModel"], "actions": ["view", "create"]}, {"resources": ["PersonOnboardingModel", "PersonModel", "PersonSalesInfo", "DealSalesInfo", "MemberProductPriceModel", "MemberProductPriceAdjustmentModel", "MemberAccreditedNetworkTrackerModel"], "actions": ["view", "update", "create"]}, {"resources": ["CassiMemberModel"], "actions": ["view", "update", "create", "count"]}, {"resources": ["AppointmentScheduleEventTypeModel", "EventTypeProviderUnitModel", "MedicalSpecialtyModel", "ZendeskExternalReference", "PromoCodeModel", "Lead", "ProductModel", "ProductBundleModel", "PriceListingModel", "ProductPriceListingModel", "StaffModel", "HealthProductSimulation", "HealthProductSimulationGroup", "HealthCondition", "ProductPriceAdjustmentModel", "MemberModel", "ProviderModel", "PersonCalendlyModel", "MemberContractModel", "PersonPreferencesModel"], "actions": ["view"]}, {"resources": ["HealthPlanTaskStatusHistoryModel"], "actions": ["create"]}, {"resources": ["MemberProductChangeScheduleModel"], "actions": ["view", "update"]}]}, {"conditions": ["${subject.opaType} == PersonSubject", "${subject.id} == ${resource.personId} || ${resource.personId} in ${subject.dependentPersons}"], "allow": [{"resources": ["HealthDeclaration"], "actions": ["view", "update", "create"]}]}]}