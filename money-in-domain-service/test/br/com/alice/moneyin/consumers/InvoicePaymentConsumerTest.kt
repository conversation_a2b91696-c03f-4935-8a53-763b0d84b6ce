package br.com.alice.moneyin.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InvoiceLiquidationStatus
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.models.PreActivationPaymentStatus
import br.com.alice.moneyin.client.InvoiceLiquidationService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PreActivationPaymentService
import br.com.alice.moneyin.communication.InvoiceMailer
import br.com.alice.moneyin.event.InvoicePaymentApprovedEvent
import br.com.alice.moneyin.event.InvoicePaymentCreatedEvent
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class InvoicePaymentConsumerTest : ConsumerTest() {

    @AfterTest
    fun tearDown() = clearAllMocks()

    private val invoicesService: InvoicesService = mockk()
    private val paymentService: InvoicePaymentService = mockk()
    private val memberInvoiceGroupService: MemberInvoiceGroupService = mockk()
    private val invoiceLiquidationService: InvoiceLiquidationService = mockk()
    private val preActivationPaymentService: PreActivationPaymentService = mockk()
    private val invoiceMailer: InvoiceMailer = mockk()

    private val consumer =
        InvoicePaymentConsumer(
            invoicesService,
            paymentService,
            memberInvoiceGroupService,
            invoiceLiquidationService,
            preActivationPaymentService,
            invoiceMailer
        )

    @Nested
    inner class CloseInvoice {

        @Test
        fun `#closeInvoice should mark invoice as paid, when payment amount is equal to the invoice totalAmount`() =
            runBlocking {
                val invoice = TestModelFactory.buildMemberInvoice(totalAmount = BigDecimal(100))
                val approvedAt = LocalDateTime.now().minusDays(3)
                val payment = TestModelFactory.buildInvoicePayment(
                    amount = BigDecimal(100),
                    memberInvoiceIds = listOf(invoice.id),
                ).approve(approvedAt)
                val paidInvoice = invoice.markAsPaid(approvedAt)

                coEvery { invoicesService.findInvoicesByIds(listOf(invoice.id)) } returns listOf(invoice)
                coEvery {
                    invoicesService.markAsPaid(
                        invoice.id,
                        approvedAt
                    )
                } returns paidInvoice.success()

                val event = InvoicePaymentApprovedEvent(payment)

                val result = consumer.closeInvoice(event)
                ResultAssert.assertThat(result).isSuccess()

                coVerifyOnce { invoicesService.findInvoicesByIds(listOf(invoice.id)) }
                coVerifyOnce { invoicesService.markAsPaid(invoice.id, approvedAt) }
                coVerifyNone { paymentService.listInvoicePayments(invoice.id) }
            }

        @Test
        fun `#closeInvoice should mark invoice as paid, when payment amount is greater than the invoice totalAmount`() =
            runBlocking {
                val invoice = TestModelFactory.buildMemberInvoice(totalAmount = BigDecimal(100))
                val approvedAt = LocalDateTime.now().minusDays(3)
                val payment = TestModelFactory.buildInvoicePayment(
                    amount = BigDecimal(200),
                    memberInvoiceIds = listOf(invoice.id),
                ).approve(approvedAt)
                val paidInvoice = invoice.markAsPaid()

                coEvery { invoicesService.findInvoicesByIds(listOf(invoice.id)) } returns listOf(invoice)
                coEvery {
                    invoicesService.markAsPaid(
                        invoice.id,
                        approvedAt
                    )
                } returns paidInvoice.success()

                val event = InvoicePaymentApprovedEvent(payment)

                val result = consumer.closeInvoice(event)
                ResultAssert.assertThat(result).isSuccess()

                coVerifyOnce { invoicesService.findInvoicesByIds(listOf(invoice.id)) }
                coVerifyOnce { invoicesService.markAsPaid(invoice.id, approvedAt) }
                coVerifyNone { paymentService.listInvoicePayments(invoice.id) }
            }

        @Test
        fun `#closeInvoice should mark invoice as paid, when payment amount fully pays a group of invoices`() =
            runBlocking {
                val firstInvoice = TestModelFactory.buildMemberInvoice(totalAmount = BigDecimal(100))
                val secondInvoice = TestModelFactory.buildMemberInvoice(totalAmount = BigDecimal(100))
                val paidFirstInvoice = firstInvoice.markAsPaid()
                val paidSecondInvoice = secondInvoice.markAsPaid()
                val approvedAt = LocalDateTime.now().minusDays(3)
                val payment = TestModelFactory.buildInvoicePayment(
                    amount = BigDecimal(200),
                    memberInvoiceIds = listOf(firstInvoice.id, secondInvoice.id)
                ).approve(approvedAt)

                coEvery {
                    invoicesService.findInvoicesByIds(
                        listOf(
                            firstInvoice.id,
                            paidSecondInvoice.id
                        )
                    )
                } returns listOf(firstInvoice, paidSecondInvoice)

                coEvery { invoicesService.markAsPaid(firstInvoice.id, approvedAt) } returns paidFirstInvoice.success()
                coEvery {
                    invoicesService.markAsPaid(
                        paidSecondInvoice.id,
                        approvedAt
                    )
                } returns paidSecondInvoice.success()

                val event = InvoicePaymentApprovedEvent(payment)

                val result = consumer.closeInvoice(event)
                ResultAssert.assertThat(result).isSuccess()

                coVerifyOnce { invoicesService.findInvoicesByIds(listOf(firstInvoice.id, paidSecondInvoice.id)) }
                coVerifyOnce { invoicesService.markAsPaid(firstInvoice.id, approvedAt) }
                coVerifyNone { paymentService.listInvoicePayments(paidFirstInvoice.id) }
                coVerifyNone { paymentService.listInvoicePayments(paidSecondInvoice.id) }
            }

        @Test
        fun `#closeInvoice should mark invoice as paid, when payment amount is lower than the invoice totalAmount, but has complementary payments`() =
            runBlocking {
                val invoice = TestModelFactory.buildMemberInvoice(totalAmount = BigDecimal(200))
                val approvedAt = LocalDateTime.now().minusDays(3)
                val payment =
                    TestModelFactory.buildInvoicePayment(amount = BigDecimal(50), memberInvoiceIds = listOf(invoice.id))
                        .approve(approvedAt)
                val paidInvoice = invoice.markAsPaid()

                coEvery { invoicesService.findInvoicesByIds(listOf(invoice.id)) } returns listOf(invoice)
                coEvery {
                    invoicesService.markAsPaid(
                        invoice.id,
                        approvedAt
                    )
                } returns paidInvoice.success()

                val firstComplementaryPayment = TestModelFactory.buildInvoicePayment(amount = BigDecimal(50)).approve()
                val secondComplementaryPayment =
                    TestModelFactory.buildInvoicePayment(amount = BigDecimal(100)).approve()
                coEvery { paymentService.listInvoicePayments(invoice.id) } returns listOf(
                    payment,
                    firstComplementaryPayment,
                    secondComplementaryPayment
                ).success()

                val event = InvoicePaymentApprovedEvent(payment)

                val result = consumer.closeInvoice(event)
                ResultAssert.assertThat(result).isSuccess()

                coVerifyOnce { invoicesService.findInvoicesByIds(listOf(invoice.id)) }
                coVerifyOnce { invoicesService.markAsPaid(invoice.id, approvedAt) }
            }

        @Test
        fun `#closeInvoice should not mark invoice as paid, when payment amount and the complementary payments are lower than the invoice totalAmount`() =
            runBlocking {
                val invoice = TestModelFactory.buildMemberInvoice(totalAmount = BigDecimal(200))
                val approvedAt = LocalDateTime.now().minusDays(3)
                val payment =
                    TestModelFactory.buildInvoicePayment(amount = BigDecimal(50), memberInvoiceIds = listOf(invoice.id))
                        .approve(approvedAt)
                val paidInvoice = invoice.markAsPaid()

                coEvery { invoicesService.findInvoicesByIds(listOf(invoice.id)) } returns listOf(invoice)
                coEvery { invoicesService.markAsPaid(invoice.id) } returns paidInvoice.success()

                val firstComplementaryPayment = TestModelFactory.buildInvoicePayment(amount = BigDecimal(50)).approve()
                coEvery { paymentService.listInvoicePayments(invoice.id) } returns listOf(
                    payment,
                    firstComplementaryPayment
                ).success()

                val event = InvoicePaymentApprovedEvent(payment)

                val result = consumer.closeInvoice(event)
                ResultAssert.assertThat(result).isSuccess()

                coVerifyOnce { invoicesService.findInvoicesByIds(listOf(invoice.id)) }
                coVerifyNone { invoicesService.markAsPaid(any()) }
            }
    }

    @Nested
    inner class AssociateMemberInvoiceGroup {
        @Test
        fun `#should associate a member invoice group to invoice payment when it does not have an associated one`() =
            runBlocking {
                val memberInvoiceId = RangeUUID.generate()
                val group = TestModelFactory.buildMemberInvoiceGroup(memberInvoiceIds = listOf(memberInvoiceId))
                val payment = TestModelFactory.buildInvoicePayment(
                    memberInvoiceIds = listOf(memberInvoiceId),
                    invoiceGroupId = null
                )
                val paymentWithGroup = payment.copy(invoiceGroupId = group.id)

                coEvery { memberInvoiceGroupService.getByMemberInvoices(memberInvoiceId) } returns group
                coEvery { paymentService.update(payment.associateGroup(group)) } returns paymentWithGroup

                val event = InvoicePaymentCreatedEvent(payment, null, PaymentReason.REGULAR_PAYMENT)
                val result = consumer.associateMemberInvoiceGroup(event)

                ResultAssert.assertThat(result).isSuccess()

                coVerifyOnce { memberInvoiceGroupService.getByMemberInvoices(memberInvoiceId) }
                coVerifyOnce { paymentService.update(payment.associateGroup(group)) }
            }

        @Test
        fun `#should associate a member invoice group to invoice payment when it does not have an associated one after update fail`() =
            runBlocking {
                val memberInvoiceId = RangeUUID.generate()
                val group = TestModelFactory.buildMemberInvoiceGroup(memberInvoiceIds = listOf(memberInvoiceId))
                val payment = TestModelFactory.buildInvoicePayment(
                    memberInvoiceIds = listOf(memberInvoiceId),
                    invoiceGroupId = null
                )
                val paymentUpdated = payment.copy(version = 1)
                val paymentWithGroup = paymentUpdated.copy(invoiceGroupId = group.id)

                coEvery { memberInvoiceGroupService.getByMemberInvoices(memberInvoiceId) } returns group
                coEvery { paymentService.update(payment.associateGroup(group)) } returns NotFoundException()
                coEvery { paymentService.get(payment.id, false) } returns paymentUpdated
                coEvery { paymentService.update(paymentUpdated.associateGroup(group)) } returns paymentWithGroup

                val event = InvoicePaymentCreatedEvent(payment, null, PaymentReason.REGULAR_PAYMENT)
                val result = consumer.associateMemberInvoiceGroup(event)

                ResultAssert.assertThat(result).isSuccess()

                coVerifyOnce { memberInvoiceGroupService.getByMemberInvoices(memberInvoiceId) }
                coVerifyOnce { paymentService.update(payment.associateGroup(group)) }
                coVerifyOnce { paymentService.get(payment.id, false) }
                coVerifyOnce { paymentService.update(paymentUpdated.associateGroup(group)) }
            }

        @Test
        fun `#should not associate a member invoice group to when it does not exist`() =
            runBlocking {
                val memberInvoiceId = RangeUUID.generate()
                val group = TestModelFactory.buildMemberInvoiceGroup(memberInvoiceIds = listOf(memberInvoiceId))
                val payment = TestModelFactory.buildInvoicePayment(
                    memberInvoiceIds = listOf(memberInvoiceId),
                    invoiceGroupId = null
                )
                val paymentUpdated = payment.copy(version = 1)

                coEvery { memberInvoiceGroupService.getByMemberInvoices(memberInvoiceId) } returns NotFoundException(
                    ""
                )

                val event = InvoicePaymentCreatedEvent(payment, null, PaymentReason.REGULAR_PAYMENT)
                val result = consumer.associateMemberInvoiceGroup(event)

                ResultAssert.assertThat(result).isSuccess()

                coVerifyOnce { memberInvoiceGroupService.getByMemberInvoices(memberInvoiceId) }
                coVerifyNone {
                    paymentService.update(any())
                    paymentService.get(any(), any())
                    paymentService.update(any())
                }
            }

        @Test
        fun `#should not associate a member invoice group to when the payment has a member group `() =
            runBlocking {
                val memberInvoiceId = RangeUUID.generate()
                val payment = TestModelFactory.buildInvoicePayment(memberInvoiceIds = listOf(memberInvoiceId))

                val event = InvoicePaymentCreatedEvent(payment, null, PaymentReason.REGULAR_PAYMENT)
                val result = consumer.associateMemberInvoiceGroup(event)

                ResultAssert.assertThat(result).isSuccess()

                coVerifyNone {
                    memberInvoiceGroupService.getByMemberInvoices(memberInvoiceId)
                    paymentService.update(any())
                    paymentService.get(any(), any())
                    paymentService.update(any())
                }
            }

        @Test
        fun `#should not associate a member invoice group when the payment belongs to a liquidation`() =
            runBlocking {
                val memberInvoiceId = RangeUUID.generate()
                val payment = TestModelFactory.buildInvoicePayment(
                    memberInvoiceIds = emptyList(),
                    invoiceLiquidationId = RangeUUID.generate(),
                )

                val event = InvoicePaymentCreatedEvent(payment, null, PaymentReason.REGULAR_PAYMENT)
                val result = consumer.associateMemberInvoiceGroup(event)

                ResultAssert.assertThat(result).isSuccess()

                coVerifyNone {
                    memberInvoiceGroupService.getByMemberInvoices(memberInvoiceId)
                    paymentService.update(any())
                    paymentService.get(any(), any())
                    paymentService.update(any())
                }
            }

        @Test
        fun `#should not associate a member invoice group when the payment has no member invoice ids`() =
            runBlocking {
                val memberInvoiceId = RangeUUID.generate()
                val group = TestModelFactory.buildMemberInvoiceGroup(memberInvoiceIds = listOf(memberInvoiceId))
                val payment = TestModelFactory.buildInvoicePayment(
                    memberInvoiceIds = listOf(memberInvoiceId),
                )
                val paymentUpdated = payment.copy(version = 1)

                val event = InvoicePaymentCreatedEvent(payment, null, PaymentReason.REGULAR_PAYMENT)
                val result = consumer.associateMemberInvoiceGroup(event)

                ResultAssert.assertThat(result).isSuccess()

                coVerifyNone { memberInvoiceGroupService.getByMemberInvoices(memberInvoiceId) }
                coVerifyNone { paymentService.update(payment.associateGroup(group)) }
                coVerifyNone { paymentService.get(payment.id, false) }
                coVerifyNone { paymentService.update(paymentUpdated.associateGroup(group)) }
            }
    }

    @Nested
    inner class EmitMemberInvoiceGroupPaid {
        @Test
        fun `#should emit MemberInvoiceGroupPaid if invoice has memberInvoiceGroupId`(): Unit =
            runBlocking {
                val group = TestModelFactory.buildMemberInvoiceGroup()
                val groupPaid = group.markAsPaid()
                val payment = TestModelFactory.buildInvoicePayment(invoiceGroupId = group.id)

                coEvery { memberInvoiceGroupService.get(any()) } returns group
                coEvery { memberInvoiceGroupService.markAsPaid(any(), any()) } returns groupPaid

                val event = InvoicePaymentApprovedEvent(payment)
                val result = consumer.emitMemberInvoiceGroup(event)

                ResultAssert.assertThat(result).isSuccess()

                coVerifyOnce { memberInvoiceGroupService.get(payment.invoiceGroupId!!) }
                coVerifyOnce { memberInvoiceGroupService.markAsPaid(group, payment) }
            }

        @Test
        fun `#should not emit MemberInvoiceGroupPaid if invoice has not memberInvoiceGroupId`(): Unit =
            runBlocking {
                val payment = TestModelFactory.buildInvoicePayment(invoiceGroupId = null)

                val event = InvoicePaymentApprovedEvent(payment)
                val result = consumer.emitMemberInvoiceGroup(event)

                ResultAssert.assertThat(result).isSuccess()

                coVerifyNone { memberInvoiceGroupService.get(any()) }
                coVerifyNone { memberInvoiceGroupService.markAsPaid(any(), any()) }
            }
    }

    @Nested
    inner class SendEmail {
        @Test
        fun `#should send email when invoice is paid`() =
            runBlocking {
                val invoicePayment = TestModelFactory.buildInvoicePayment()

                coEvery { invoiceMailer.sendInvoicePaidEmail(invoicePayment) } returns EmailReceipt("123")

                consumer.sendEmailToNotifyPayment(InvoicePaymentApprovedEvent(invoicePayment))

                coVerifyOnce { invoiceMailer.sendInvoicePaidEmail(any()) }
            }
    }

    @Nested
    inner class EmitInvoiceLiquidationPaid {
        @Test
        fun `#should emit InvoiceLiquidationPaid if invoice has invoiceLiquidationId`(): Unit =
            runBlocking {
                val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()
                val groupPaid = invoiceLiquidation.copy(status = InvoiceLiquidationStatus.PAID)
                val payment = TestModelFactory.buildInvoicePayment(invoiceLiquidationId = invoiceLiquidation.id)

                coEvery { invoiceLiquidationService.get(any()) } returns invoiceLiquidation
                coEvery { invoiceLiquidationService.markAsPaid(any(), any()) } returns groupPaid

                val event = InvoicePaymentApprovedEvent(payment)
                val result = consumer.emitInvoiceLiquidation(event)

                ResultAssert.assertThat(result).isSuccess()

                coVerifyOnce { invoiceLiquidationService.get(payment.invoiceLiquidationId!!) }
                coVerifyOnce { invoiceLiquidationService.markAsPaid(invoiceLiquidation, payment) }
            }

        @Test
        fun `#should not emit InvoiceLiquidationPaid if invoice has not invoiceLiquidationId`(): Unit =
            runBlocking {
                val payment = TestModelFactory.buildInvoicePayment(invoiceLiquidationId = null)

                val event = InvoicePaymentApprovedEvent(payment)
                val result = consumer.emitInvoiceLiquidation(event)

                ResultAssert.assertThat(result).isSuccess()

                coVerifyNone { invoiceLiquidationService.get(any()) }
                coVerifyNone { invoiceLiquidationService.markAsPaid(any(), any()) }
            }
    }

    @Nested
    inner class EmitPreActivationPaymentPaid {
        @Test
        fun `#should emit PreActivationPaymentPaid if invoice has preActivationPaymentId`(): Unit =
            runBlocking {
                val preActivationPayment = TestModelFactory.buildPreActivationPayment()
                val preActivationPaymentPaid = preActivationPayment.copy(status = PreActivationPaymentStatus.PAID)
                val payment = TestModelFactory.buildInvoicePayment(
                    preActivationPaymentId = preActivationPayment.id,
                    invoiceGroupId = null,
                    invoiceLiquidationId = null,
                )

                coEvery { preActivationPaymentService.get(preActivationPayment.id) } returns preActivationPayment
                coEvery {
                    preActivationPaymentService.markAsPaid(
                        preActivationPayment,
                        payment
                    )
                } returns preActivationPaymentPaid

                val event = InvoicePaymentApprovedEvent(payment)
                val result = consumer.emitInvoicePreActivationPayment(event)

                ResultAssert.assertThat(result).isSuccess()

                coVerifyOnce {
                    preActivationPaymentService.get(preActivationPayment.id)
                    preActivationPaymentService.markAsPaid(preActivationPayment, payment)
                }
            }

        @Test
        fun `#should not emit InvoiceLiquidationPaid if invoice has not invoiceLiquidationId`(): Unit =
            runBlocking {
                val payment = TestModelFactory.buildInvoicePayment(preActivationPaymentId = null)

                val event = InvoicePaymentApprovedEvent(payment)
                val result = consumer.emitInvoiceLiquidation(event)

                ResultAssert.assertThat(result).isSuccess()

                coVerifyNone {
                    preActivationPaymentService.get(any())
                    preActivationPaymentService.markAsPaid(any(), any())
                }
            }
    }
}
