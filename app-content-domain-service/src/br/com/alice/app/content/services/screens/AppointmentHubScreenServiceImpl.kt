package br.com.alice.app.content.services.screens

import br.com.alice.app.content.client.AppointmentScheduleEventService
import br.com.alice.app.content.client.screens.AppointmentHubScreenService
import br.com.alice.app.content.model.Accessory
import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.Alignment
import br.com.alice.app.content.model.AppBar
import br.com.alice.app.content.model.Button
import br.com.alice.app.content.model.CardColorScheme
import br.com.alice.app.content.model.CardSize
import br.com.alice.app.content.model.CardType
import br.com.alice.app.content.model.Label
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.RemoteActionTransition
import br.com.alice.app.content.model.ScreenAlignment
import br.com.alice.app.content.model.ScreenAlignmentType
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreenModule
import br.com.alice.app.content.model.ScreenProperties
import br.com.alice.app.content.model.ScreenType
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.SectionPadding
import br.com.alice.app.content.model.SectionTextLayout
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.Size
import br.com.alice.app.content.model.Variant
import br.com.alice.app.content.model.getCheshireScreenFromBudProtocolId
import br.com.alice.app.content.model.getCheshireScreenNavigationAction
import br.com.alice.app.content.model.healthMeetingsList
import br.com.alice.app.content.model.labs
import br.com.alice.app.content.model.redesignAliceAgora
import br.com.alice.app.content.model.saraCalendar
import br.com.alice.app.content.model.section.CardSection
import br.com.alice.app.content.model.section.GridGutter
import br.com.alice.app.content.model.section.GridItemSection
import br.com.alice.app.content.model.section.GridSection
import br.com.alice.app.content.model.section.GridVariant
import br.com.alice.app.content.model.section.SheetSection
import br.com.alice.app.content.model.specialists
import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.person.client.MemberService
import br.com.alice.schedule.model.AppointmentScheduleWithStaff
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map

class AppointmentHubScreenServiceImpl(
    private val appointmentScheduleEventService: AppointmentScheduleEventService,
    private val personClinicalAccountService: PersonClinicalAccountService,
    private val memberService: MemberService

) : AppointmentHubScreenService {

    override suspend fun getScreen(
        personId: PersonId,
        appointmentSchedules: List<AppointmentScheduleWithStaff>?
    ): Result<ScreensTransport, Throwable> =
        Result.of {
            ScreensTransport(
                id = ScreenType.APPOINTMENT_HUB.value,
                layout = ScreenLayout(
                    type = "single_column",
                    appBar = AppBar(
                        title = "Agendamentos",
                        back = "",
                    ),
                    body = listOf(
                        ScreenModule.getInfiniteScrollModuleSection(
                            ScreenModule.GROUPED_COLUMN_MODULE,
                            ScreenType.APPOINTMENT_HUB,
                            subSections = listOf(
                                textSection(id = "text_schedule", text = getScreenTitle(appointmentSchedules)),
                                getCardSection(appointmentSchedules, personId),
                                scheduleMenuSection(),
                                textSection(id = "text_help", text = "Precisa de ajuda?"),
                                helpMenuSection()
                            ),
                            trackUserBehavior = true
                        )
                    ),
                ),
            )
        }

    private suspend fun getCardSection(
        appointmentSchedules: List<AppointmentScheduleWithStaff>?,
        personId: PersonId
    ): Section = if (!shouldShowEmptyMFC()) appointmentsCarousel(appointmentSchedules, personId)
    else personClinicalAccountService.getByPersonId(personId).getOrNullIfNotFound()
            ?.let { appointmentsCarousel(appointmentSchedules, personId) }
            ?: buildCardEmptyMFC(personId)

    override suspend fun getAliceAgoraSheet(): Result<ScreensTransport, Throwable> = Result.of {
        ScreensTransport(
            id = ScreenType.APPOINTMENT_HUB_ALICE_AGORA_SHEET.value,
            properties = ScreenProperties(
                alignment = ScreenAlignment(
                    vertical = ScreenAlignmentType.END,
                    horizontal = ScreenAlignmentType.CENTER
                )
            ),
            layout = ScreenLayout(
                type = "single_column",
                appBar = null,
                body = listOf(
                    Section(
                        type = SectionType.SHEET_SECTION,
                        id = "appointment_hub_alice_agora_sheet",
                        data = SheetSection(
                            title = "Atendimento no Alice Agora",
                            content = SheetSection.Content(
                                text = "Iremos te redirecionar para o chat com nossa equipe para triagem dos sintomas para receber o atendimento necessário.",
                            ),
                            confirmationButton = Button(
                                id = "appointment_hub_alice_agora_sheet_confirm_button",
                                variant = Variant.PRIMARY,
                                shrinkWrap = false,
                                onTapAction = RemoteAction.redesignAliceAgora(),
                                label = Label(
                                    text = "Iniciar atendimento",
                                    icon = "right-arrow",
                                    accessory = Accessory.RIGHT
                                )
                            ),
                            illustrationUrl = "",
                        ),
                        minAppVersion = SectionType.SHEET_SECTION.minAppVersion!!
                    )
                )
            )
        )
    }

    private suspend fun buildCardEmptyMFC(personId: PersonId) : Section =
        memberService.getCurrent(personId).map { member ->
            Section(
                id = "empty_mfc_card",
                type = SectionType.CARD_SECTION,
                minAppVersion = SectionType.CARD_SECTION.minAppVersion!!,
                data = CardSection(
                    title = "Você ainda não escolheu seu Médico",
                    description = "Após ${member.activationDate!!.plusDays(60L).toBrazilianDateFormat()} seu médico será atribuído automaticamente",
                    type = CardType.MAIN,
                    size = CardSize.MEDIUM,
                    colorScheme = CardColorScheme.BRAND,
                    alignment = Alignment.RIGHT_BOTTOM,
                    accessoryIcon = "doctor",
                    onCardClick = RemoteAction(
                        mobileRoute = ActionRouting.MEMBER_ONBOARDING_V2_COVER,
                        params = mapOf(
                            "path" to "mfc_cover",
                        )
                    ),
                    buttonPrimary = Button(
                        id = "health_professional_select_cover",
                        label = Label(
                            text = "Escolher meu médico",
                            icon = "right-arrow",
                            accessory = Accessory.RIGHT
                        ),
                        variant = Variant.CUSTOM,
                        size = Size.MEDIUM,
                        enabled = true,
                        loading = false,
                        shrinkWrap = false,
                        onTapAction = RemoteAction(
                            mobileRoute = ActionRouting.MEMBER_ONBOARDING_V2_COVER,
                            params = mapOf(
                                "path" to "mfc_cover",
                            )
                        )
                    )
                )
            )
        }.get()

    private fun textSection(id: String, text: String) =
        ScreenModule.getTextSectionWithContent(
            sectionId = id,
            text = text,
            layout = SectionTextLayout.TITLE_MEDIUM_HIGHLIGHT,
            alignment = Alignment.LEFT,
            verticalPadding = SectionPadding.P0,
        )

    private fun getScreenTitle(appointmentSchedules: List<AppointmentScheduleWithStaff>?) =
        if (appointmentSchedules.isNullOrEmpty()) "Agendar" else "Meus agendamentos"

    private suspend fun appointmentsCarousel(
        appointmentSchedules: List<AppointmentScheduleWithStaff>?,
        personId: PersonId
    ): Section =
        if (appointmentSchedules.isNullOrEmpty()) {
            getScheduleCard(personId)
        } else {
            var hasHealthTeam = false
            val cards = appointmentSchedules.mapIndexed { index, it ->
                if (AppointmentScheduleType.HEALTHCARE_TEAM == it.appointmentSchedule.type) {
                    hasHealthTeam = true
                }

                ScreenModule.getAppointmentCardSection("appointment_card_$index", it)
            }

            if (cards.size == 1 && hasHealthTeam) {
                cards.first()
            } else {
                stackedCarousel(hasHealthTeam, cards, personId)
            }
        }

    private suspend fun stackedCarousel(
        hasHealthTeam: Boolean,
        cards: List<Section>,
        personId: PersonId
    ) = Section(
        id = "appointment_carousel_grid",
        type = SectionType.GRID_SECTION,
        data = GridSection(
            children = listOf(
                Section(
                    id = "appointment_carousel_grid_item",
                    type = SectionType.GRID_ITEM_SECTION,
                    data = GridItemSection(
                        child = ScreenModule.stackedCarousel(
                            sectionId = "appointments_carousel",
                            children = if (hasHealthTeam) cards else cards.plus(getScheduleCard(personId)),
                        ),
                        span = 4,
                    ),
                    minAppVersion = SectionType.GRID_ITEM_SECTION.minAppVersion!!
                ),
            ),
            gutter = GridGutter.GUTTER_8,
            gridVariant = GridVariant.AUTOMATIC,
        ),
        minAppVersion = SectionType.GRID_SECTION.minAppVersion!!,
    )


    private suspend fun getScheduleCard(personId: PersonId): Section {
        val action =
            RemoteAction.saraCalendar(appointmentScheduleEventService.buildSaraScheduleUrlVersion(personId).get())
        return Section(
            id = "schedule_card",
            type = SectionType.CARD_SECTION,
            data = CardSection(
                type = CardType.MAIN,
                title = "Agendar com meu médico de família",
                alignment = Alignment.RIGHT_BOTTOM,
                colorScheme = CardColorScheme.BRAND,
                size = CardSize.MEDIUM,
                accessoryIcon = "calendar",
                buttonPrimary = Button(
                    id = "schedule_action",
                    label = Label(
                        text = "Agendar",
                        icon = "right-arrow",
                        accessory = Accessory.RIGHT
                    ),
                    onTapAction = action
                ),
                onCardClick = action
            ),
            minAppVersion = SectionType.CARD_SECTION.minAppVersion!!,
        )
    }

    private fun scheduleMenuSection(): Section {
        val menuItems = listOf(
            ScreenModule.getMenuSection(
                sectionId = "test_and_procedures_schedule",
                title = "Exames ou procedimentos",
                icon = "lab",
                onTapAction = RemoteAction.labs(),
            ),
            ScreenModule.getMenuSection(
                sectionId = "specialists_schedule",
                title = "Especialistas e clínicas",
                icon = "doctor",
                onTapAction = RemoteAction.specialists(),
            ),
            ScreenModule.getMenuSection(
                sectionId = "class_schedule",
                title = "Aulas ou encontros",
                icon = "team",
                onTapAction = RemoteAction.healthMeetingsList(),
            ),
        )

        return ScreenModule.getListMenuSectionWithBody(
            sectionId = "schedule_menu",
            body = menuItems,
        )
    }

    private fun helpMenuSection(): Section {
        val menuItems = listOf(
            ScreenModule.getMenuSection(
                sectionId = "prescription_renewal",
                title = "Quero renovar minha receita",
                icon = "pill",
                onTapAction = RemoteAction.getCheshireScreenFromBudProtocolId(
                    getPrescriptionRootNodeId(),
                    screenId = "appointment_hub_prescription"
                ),
            ),
            ScreenModule.getMenuSection(
                sectionId = "alice_agora",
                title = "Preciso de atendimento agora",
                icon = "alert_rotating_light",
                onTapAction = RemoteAction.Companion.getCheshireScreenNavigationAction(
                    ScreenType.APPOINTMENT_HUB_ALICE_AGORA_SHEET,
                    transition = RemoteActionTransition.BOTTOM_SHEET,
                )
            )
        )

        return ScreenModule.getListMenuSectionWithBody(
            sectionId = "help_menu",
            body = menuItems,
        )
    }

    private fun getPrescriptionRootNodeId(): String = FeatureService.get(
        namespace = FeatureNamespace.SCREENING,
        key = "triage_prescription_protocol_id",
        defaultValue = "2b531a26-7d1a-4dc3-b4df-905e2a605100"
    )

    private fun shouldShowEmptyMFC(): Boolean =
        FeatureService.get(
            namespace = FeatureNamespace.ALICE_APP,
            key = "should_show_empty_mfc",
            defaultValue = false
        )
}

