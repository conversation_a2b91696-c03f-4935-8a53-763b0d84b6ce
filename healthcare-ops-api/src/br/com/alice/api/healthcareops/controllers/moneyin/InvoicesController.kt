package br.com.alice.api.healthcareops.controllers.moneyin

import br.com.alice.api.healthcareops.converters.InvoicePaymentConverter
import br.com.alice.api.healthcareops.converters.InvoicePaymentConverter.toCreatePaymentRequest
import br.com.alice.api.healthcareops.converters.MemberInvoiceRequestConverter
import br.com.alice.api.healthcareops.converters.MemberInvoiceResponseConverter
import br.com.alice.api.healthcareops.converters.PaymentAccountsConverter
import br.com.alice.api.healthcareops.models.InvoiceCancellationRequest
import br.com.alice.api.healthcareops.models.InvoiceResponse
import br.com.alice.api.healthcareops.models.IssueInvoiceRequest
import br.com.alice.authentication.currentUserId
import br.com.alice.common.ErrorResponse
import br.com.alice.common.PaymentMethod
import br.com.alice.common.Response
import br.com.alice.common.coFoldResponse
import br.com.alice.common.convertTo
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.core.extensions.yearMonthFormatter
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.mapPair
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.ApprovePaymentResponse
import br.com.alice.data.layer.models.CancelPaymentRequest
import br.com.alice.data.layer.models.CancelPaymentResponse
import br.com.alice.data.layer.models.CreateMemberInvoiceRequest
import br.com.alice.data.layer.models.CreatePaymentRequest
import br.com.alice.data.layer.models.CreatePaymentResponse
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.InvoicePaymentResponse
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoiceResponse
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.models.withInvoicePayments
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.InvoiceAlreadyPaidException
import br.com.alice.moneyin.client.InvoicePaymentInvalidDueDate
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicePdfService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PortalUrlGeneratorService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.content.ByteArrayContent
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.Duration
import java.time.LocalDate
import java.util.UUID

class InvoicesController(
    private val invoicesService: InvoicesService,
    private val personService: PersonService,
    private val memberService: MemberService,
    private val invoicePaymentService: InvoicePaymentService,
    private val billingAccountablePartyService: BillingAccountablePartyService,
    private val memberInvoiceGroupService: MemberInvoiceGroupService,
    private val invoicePdfService: InvoicePdfService,
    private val portalUrlGeneratorService: PortalUrlGeneratorService
) {

    suspend fun getPaymentsProfile(personId: String) = personService.get(personId.toPersonId())
        .flatMapPair { memberService.findByPerson(it.id) }
        .map { (member, person) -> PaymentAccountsConverter.convert(person, member) }
        .foldResponse()

    suspend fun listInvoices(memberId: String) =
        invoicesService.listInvoices(UUID.fromString(memberId), withPayments = true).map {
            MemberInvoiceResponseConverter.convert(it)
        }.map {
            it.copy(memberInvoices = it.memberInvoices.changeListForListWithEnrichedInvoicePayments())
        }.foldResponse()

    suspend fun createInvoice(memberId: UUID, request: CreateMemberInvoiceRequest): Response {
        logger.info(
            "Create invoice route", "current_staff_id" to currentUserId(),
        )

        return memberService.get(memberId)
            .map { MemberInvoiceRequestConverter.convert(request, it) }
            .flatMap { invoicesService.createInvoice(it) }
            .map(MemberInvoiceResponseConverter::convert)
            .foldResponse()
    }

    suspend fun createPayment(memberId: String, invoiceId: UUID, request: CreatePaymentRequest): Response {
        logger.info(
            "Create payment route", "current_staff_id" to currentUserId(),
        )
        logger.info("Creating a payment", "member_id" to memberId, "invoice_id" to invoiceId, "request" to request)
        return createInvoicePaymentPayment(memberId.toUUID(), invoiceId, request)
            .map(::CreatePaymentResponse)
            .foldResponse()
    }

    private suspend fun createInvoicePaymentPayment(
        memberId: UUID,
        memberInvoiceId: UUID,
        request: CreatePaymentRequest,
        memberInvoice: MemberInvoice? = null
    ) =
        coroutineScope {
            val memberDeferred = async { memberService.get(memberId) }
            val invoiceDeferred =
                async { memberInvoice?.success() ?: invoicesService.get(memberInvoiceId) }

            val member = memberDeferred.await()
            val invoice = invoiceDeferred.await()

            member.flatMapPair { invoice }
                .flatMap { (invoice, member) ->
                    validatePaymentRequest(
                        memberInvoiceId, request
                    ).flatMap {

                        if (invoice.memberInvoiceGroupId != null) createInvoicePaymentForInvoiceGroup(
                            memberInvoiceId,
                            invoice,
                            request,
                        ) else {
                            val billingAccountableParty =
                                billingAccountablePartyService.getCurrentOrCreateForPerson(member.personId).get()

                            val payment = InvoicePaymentConverter.convert(
                                request,
                                memberInvoiceId,
                                billingAccountableParty,
                                invoice,
                            )

                            invoicePaymentService.createInvoicePayment(
                                payment,
                                member
                            )
                        }
                    }
                }
        }


    private suspend fun createInvoicePaymentForInvoiceGroup(
        invoiceId: UUID,
        invoice: MemberInvoice,
        request: CreatePaymentRequest,
    ) = coroutineScope {
        val memberInvoiceGroupDeferred = async { memberInvoiceGroupService.get(invoice.memberInvoiceGroupId!!).get() }
        val memberInvoicesDeferred =
            async { invoicesService.listByMemberInvoiceGroupId(invoice.memberInvoiceGroupId!!) }

        val memberInvoiceGroup = memberInvoiceGroupDeferred.await()
        val memberInvoices = memberInvoicesDeferred.await()
            .map { it.ifEmpty { invoicesService.findInvoicesByIds(memberInvoiceGroup.memberInvoiceIds).get() } }

        val billingAccountableParty =
            billingAccountablePartyService.get(memberInvoiceGroup.billingAccountablePartyId).get()

        val payment = InvoicePaymentConverter.convert(
            request,
            invoiceId,
            billingAccountableParty,
            invoice,
        )

        memberInvoices.flatMap {
            val reason = request.payment.reason ?: memberInvoiceGroup.type!!.toPaymentReason()
            invoicePaymentService.createInvoicePaymentForMemberInvoices(
                InvoicePaymentService.CreateInvoicePaymentForMemberInvoicesPayload(
                    payment.method,
                    it,
                    dueDate = (request.payment.dueDate?.let { dueDate -> LocalDate.parse(dueDate) }
                        ?: LocalDate.now())
                        .atEndOfTheDay(),
                    billingAccountableParty,
                    reason,
                    memberInvoiceGroup.copy(type = null),
                    invoice.personId,
                    InvoicePaymentOrigin.ISSUED_BY_STAFF,
                    paymentUrl = request.payment.paymentUrl,
                    externalId = request.payment.externalId
                )
            )
        }
    }

    suspend fun approvePayment(paymentId: UUID): Response = currentUserId().let { staffId ->
        logger.info(
            "Approve payment route", "current_staff_id" to staffId,
        )

        invoicePaymentService.approve(paymentId, approvedByStaffId = staffId.toUUID())
            .map { ApprovePaymentResponse(it) }
            .foldResponse()
    }

    suspend fun cancelPayment(paymentId: UUID, request: CancelPaymentRequest): Response {
        logger.info(
            "Cancel payment route", "current_staff_id" to currentUserId(),
        )
        return invoicePaymentService.cancel(paymentId, request.reason)
            .map { CancelPaymentResponse(it) }
            .foldResponse()
    }

    suspend fun cancelInvoice(invoiceId: String, cancellationRequest: InvoiceCancellationRequest): Response {
        logger.info(
            "Cancel invoice route", "current_staff_id" to currentUserId(),
        )

        val id = UUID.fromString(invoiceId)

        return invoicesService.cancel(id, cancellationRequest.reason).coFoldResponse(
            { it.convertTo(InvoiceResponse::class) },
            InvoiceAlreadyPaidException::class to { ErrorResponse("invoice_already_paid") }
        )
    }

    suspend fun issueFirstInvoice(memberId: String, issueInvoiceRequest: IssueInvoiceRequest): Response {
        logger.info(
            "Issue first invoice route", "current_staff_id" to currentUserId(),
        )
        logger.info("Issuing a invoice", "member_id" to memberId, "issue_invoice_request" to issueInvoiceRequest)
        return invoicesService.issueInvoice(
            memberId = memberId.toUUID(),
            referenceDate = LocalDate.parse(issueInvoiceRequest.referenceDate, yearMonthFormatter),
            dueDate = LocalDate.parse(issueInvoiceRequest.dueDate).atEndOfTheDay(),
            paymentMethod = issueInvoiceRequest.paymentMethod,
            withPayment = false,
            type = MemberInvoiceType.FIRST_PAYMENT,
        ).mapPair { it.toCreatePaymentRequest(issueInvoiceRequest, PaymentReason.FIRST_PAYMENT) }
            .flatMap { (request, memberInvoice) -> createPaymentForInvoice(memberInvoice, request) }
            .foldResponse()
    }

    suspend fun downloadInvoicePdf(invoicePaymentId: UUID): Response {
        return invoicePdfService.generateInvoice(invoicePaymentId)
            .map { ByteArrayContent(it, ContentType.Application.Pdf, HttpStatusCode.OK) }
            .foldResponse()
    }

    suspend fun downloadSecureInvoicePdf(invoicePaymentId: UUID): Response {
        return invoicePdfService.generateInvoice(invoicePaymentId, withPassword = true)
            .map { ByteArrayContent(it, ContentType.Application.Pdf, HttpStatusCode.OK) }
            .foldResponse()
    }

    private suspend fun createPaymentForInvoice(memberInvoice: MemberInvoice, request: CreatePaymentRequest) =
        createInvoicePaymentPayment(memberInvoice.memberId, memberInvoice.id, request, memberInvoice)
            .map { MemberInvoiceResponseConverter.convert(memberInvoice.withInvoicePayments(listOf(it))) }

    private fun validatePaymentRequest(
        memberInvoiceId: UUID,
        request: CreatePaymentRequest
    ): Result<Boolean, Throwable> {
        if (request.payment.method == PaymentMethod.PIX && request.payment.dueDate != null) {
            val now = LocalDate.now().atEndOfTheDay()
            val dueDate = LocalDate.parse(request.payment.dueDate).atEndOfTheDay()

            return if (Duration.between(now, dueDate)
                    .toDays() <= 5
            ) true.success() else InvoicePaymentInvalidDueDate(
                memberInvoiceId,
                dueDate
            ).failure()
        }

        return true.success()
    }

    suspend fun List<MemberInvoiceResponse>.changeListForListWithEnrichedInvoicePayments() =
        this.pmap { invoice ->
            invoice.copy(
                payments = invoice.payments.enrichInvoicePaymentResponseWithPortalUrl()
            )
        }

    private suspend fun List<InvoicePaymentResponse>.enrichInvoicePaymentResponseWithPortalUrl() =
        map {
            it.copy(
                portalUrl = portalUrlGeneratorService.mountPortalUrl(it.id).getOrNull()
            )
        }
}
