package app.business_domain_service_recurrent_test

import data.app.business_domain_service_recurrent
import rego.v1

test_unauthenticated_view_count_PersonModel_allow if {
	{1, 2} == business_domain_service_recurrent.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonModel"},
		},
	]}
}

test_unauthenticated_view_count_GenericFileVault_allow if {
	{1, 2} == business_domain_service_recurrent.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "GenericFileVault"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "GenericFileVault"},
		},
	]}
}

test_unauthenticated_CRU_count_models_allow if {
	resources = [
		"MemberModel",
		"BeneficiaryModel",
		"CassiMemberModel",
		"BeneficiaryOnboardingModel",
		"BeneficiaryOnboardingPhaseModel",
	]

	every resource in resources {
		{1, 2, 3, 4} == business_domain_service_recurrent.allow with input as {"cases": [
			{
				"index": 1,
				"action": "create",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "count",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 3,
				"action": "update",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 4,
				"action": "view",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_unauthenticated_CR_count_PersonOnboarding_allow if {
	{1, 2} == business_domain_service_recurrent.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
	]}
}

test_unauthenticated_CR_count_PersonRegistration_allow if {
	{1, 2} == business_domain_service_recurrent.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
	]}
}

test_unauthenticated_CRU_models_allow if {
	resources = [
		"MemberInvoiceModel",
		"InvoicePaymentModel",
        "ResourceSignTokenModel",
        "ItauPaymentModel",
        "CancelPaymentOnAcquirerScheduleModel",
		"PaymentDetailModel",
		"InvoiceItemModel",
		"MemberInvoiceGroupModel",
		"BolepixPaymentDetailModel",
		"PixPaymentDetailModel",
		"SimpleCreditCardPaymentDetailModel",
		"BoletoPaymentDetailModel",
		"MemberTelegramTrackingModel",
	]

	every resource in resources {
		{1, 2, 3} == business_domain_service_recurrent.allow with input as {"cases": [
			{
				"index": 1,
				"action": "create",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "update",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 3,
				"action": "view",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_unauthenticated_RU_count_Company_allow if {
	{1, 2, 3} == business_domain_service_recurrent.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanyModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanyModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanyModel"},
		},
	]}
}

test_unauthenticated_RU_count_CompanySubContract_allow if {
	{1, 2, 3} == business_domain_service_recurrent.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
	]}
}

test_unauthenticated_RU_count_CompanyContract_allow if {
	{1, 2, 3} == business_domain_service_recurrent.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanyContractModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanyContractModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanyContractModel"},
		},
	]}
}

test_unauthenticated_create_HealthDeclaration_allow if {
	{1} == business_domain_service_recurrent.allow with input as {"cases": [{
		"index": 1,
		"action": "create",
		"subject": {"opaType": "Unauthenticated"},
		"resource": {"opaType": "HealthDeclaration"},
	}]}
}

test_unauthenticated_view_BeneficiaryCompiledView_allow if {
	{1, 2} == business_domain_service_recurrent.allow with input as {"cases": [{
		"index": 1,
		"action": "view",
		"subject": {"opaType": "Unauthenticated"},
		"resource": {"opaType": "BeneficiaryCompiledViewModel"},
	},
	{
        "index": 2,
        "action": "count",
        "subject": {"opaType": "Unauthenticated"},
        "resource": {"opaType": "BeneficiaryCompiledViewModel"},
    },
	]}
}
