openapi: 3.0.3
info:
  title: Backoffice BFF
  description: Backoffice BFF
  version: 1.0.0
servers:
  - url: https://backoffice-bff-api-dev2.dev.wonderland.engineering
    description: Dev2 Environment
  - url: https://backoffice-bff-api-dev1.dev.wonderland.engineering
    description: Dev1 Environment
  - url: https://backoffice-bff-api.staging.wonderland.engineering
    description: Staging Environment
  - url: https://backoffice-bff-api.wonderland.engineering
    description: Production Environment
paths:
  /signIn:
    post:
      tags:
        - Auth
      summary: Set claims
      description: Set claims
      requestBody:
        description: Firebase token ID
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignIn'
      responses:
        '200':
          description: successful operation
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /featureConfig/{id}:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Feature Flags
      summary: Listar feature flags
      description: Listar feature flags
      parameters:
        - name: id
          in: path
          description: ID of feature flag to return
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureFlagFullResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    delete:
      security:
        - bearerAuth: [ ]
      tags:
        - Feature Flags
      summary: Excluir feature flags
      description: Excluir feature flags
      parameters:
        - name: id
          in: path
          description: ID of feature flag to return
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
        '4XX':
          $ref: '#/components/responses/BadRequest'
    put:
      security:
        - bearerAuth: [ ]
      tags:
        - Feature Flags
      summary: Atualizar feature flags
      description: Atualizar feature flags
      parameters:
        - name: id
          in: path
          description: ID of feature flag to return
          required: true
          schema:
            type: string
      requestBody:
        description: Update an existent feature flag
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateFeatureFlag'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureFlagFullResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /featureConfig/namespaces:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Feature Flags
      summary: Listar feature namespaces
      description: Listar feature namespaces
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureNamespaceResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /featureConfig/types:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Feature Flags
      summary: Listar feature types
      description: Listar feature types
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureTypeResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /featureConfig:
    post:
      security:
        - bearerAuth: [ ]
      tags:
        - Feature Flags
      summary: Criar feature flags
      description: Criar feature flags
      requestBody:
        description: Update an existent feature flag
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFeatureFlag'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureFlagFullResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Feature Flags
      summary: Listar feature flags
      description: Listar feature flags
      parameters:
        - name: filter
          in: query
          description: Namespace or key
          example: '{namespace: "schedule"}'
          required: false
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureConfigPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /company:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Companies
      summary: Listar empresas
      description: Listar empresas
      parameters:
        - name: range
          in: query
          description: range para paginação
          required: true
          schema:
            type: string
        - name: filter
          in: query
          description: objeto com filtros
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /companyStaff:
    post:
      security:
        - bearerAuth: [ ]
      tags:
        - Companies
      summary: Listar usuários do portal do RH
      description: Listar usuários do portal do RH
      parameters:
        - name: range
          in: query
          description: range para paginação
          required: true
          schema:
            type: string
        - name: filter
          in: query
          description: objeto com filtros
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCompanyStaffRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyStaffPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Companies
      summary: Listar usuários do portal do RH
      description: Listar usuários do portal do RH
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyStaffPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /companyStaff/{id}:
    put:
      security:
        - bearerAuth: [ ]
      tags:
        - Companies
      summary: Atualizar o usuário do portal do RH
      description: Atualizar o usuário do portal do RH
      parameters:
        - name: id
          in: path
          description: ID da staff
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCompanyStaffRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyStaffResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    delete:
      security:
        - bearerAuth: [ ]
      tags:
        - Companies
      summary: Deletar usuário do portal do RH
      description: Deletar usuário do portal do RH
      parameters:
        - name: id
          in: path
          description: ID da staff
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
        '4XX':
          $ref: '#/components/responses/BadRequest'
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Companies
      summary: Get usuário do portal do RH
      description: Get usuário do portal do RH
      parameters:
        - name: id
          in: path
          description: ID da staff
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyStaffResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /companyStaff/roles:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Companies
      summary: Listar roles de company staff
      description: Listar roles de company staff
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureTypeResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /salesAgent:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Sales Agents
      summary: Listar corretores
      description: Listar corretores
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalesAgentPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    post:
      security:
        - bearerAuth: [ ]
      tags:
        - Sales Agents
      summary: Criar Corretor
      description: Criar Corretor
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSalesAgentRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalesAgentResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /salesAgent/{id}:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Sales Agents
      summary: Buscar Corretor
      description: Buscar Corretor
      parameters:
        - name: id
          in: path
          description: ID do corretor
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalesAgentResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    put:
      security:
        - bearerAuth: [ ]
      tags:
        - Sales Agents
      summary: Editar Corretor
      description: Editar Corretor
      parameters:
        - name: id
          in: path
          description: ID do corretor
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSalesAgentRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalesAgentResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /salesAgent/salesFirm:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Sales Agents
      summary: Listar corretoras
      description: Listar corretoras
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SalesFirmResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /healthcareTeam:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Healthcare Team
      summary: Listar times de saúde
      description: Listar times de saúde
      parameters:
        - name: filter
          in: query
          description: objetivo com filtros - active (boolean para buscar times ativos ou inativos), name (string para filtrar times onde a médica tenha o nome de acordo com o filtro)
          example: { "active": "false", "name": "Jane" }
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Current page
          example: 1
          required: false
          schema:
            type: string
        - name: pageSize
          in: query
          description: Limit
          example: 10
          required: false
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthcareTeamPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    post:
      security:
        - bearerAuth: [ ]
      tags:
        - Healthcare Team
      summary: Criar time de saúde
      description: Criar time de saúde
      requestBody:
        description: Time de saúde para criar
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateHealthcareTeam'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthcareTeam'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    put:
      security:
        - bearerAuth: [ ]
      tags:
        - Healthcare Team
      summary: Atualizar time de saúde
      description: Atualizar time de saúde
      requestBody:
        description: Time de saúde para atualizar
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateHealthcareTeam'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthcareTeam'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /healthcareTeam/{id}:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Healthcare Team
      summary: Buscar time de saúde por id
      description: Buscar time de saúde por id
      parameters:
        - name: id
          in: path
          description: ID do time de saúde
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthcareTeam'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /healthcareTeam/physicians:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Healthcare Team
      summary: Buscar profissionais de saúde
      description: Buscar profissionais de saúde
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthcareTeamPhysicians'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /healthcareTeamAssociation:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Healthcare Team Association
      summary: Listar associaçao dos times de saúde
      description: Listar associaçao dos times de saúde
      parameters:
        - name: filter
          in: query
          description: busca por membro através de searchToken
          example: { "q": "lucas" }
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Current page
          example: 1
          required: false
          schema:
            type: string
        - name: pageSize
          in: query
          description: Limit
          example: 10
          required: false
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthcareTeamAssociationPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    post:
      security:
        - bearerAuth: [ ]
      tags:
        - Healthcare Team Association
      summary: Criar associação de time de saúde
      description: Criar associação membro <> time de saúde
      requestBody:
        description: Time de saúde e membro para associar
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateHealthcareTeamAssociation'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthcareTeamAssociation'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    put:
      security:
        - bearerAuth: [ ]
      tags:
        - Healthcare Team Association
      summary: Atualizar associação de time de saúde
      description: Atualizar associação membro <> time de saúde
      requestBody:
        description: Time de saúde e membro para associar
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HealthcareTeamAssociation'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthcareTeamAssociation'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /healthcareTeamAssociation/{id}:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Healthcare Team Association
      summary: Busca associaçao dos times de saúde por id
      description: Busca associaçao dos times de saúde por id
      parameters:
        - name: id
          in: path
          description: ID da associação do time de saúde
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthcareTeamAssociation'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /healthcareTeamAssociation/healthcareTeams:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Healthcare Team Association
      summary: Buscar times de saúde
      description: Buscar times de saúde
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthcareTeamDetail'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /healthcareTeamAssociation/persons:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Healthcare Team Association
      summary: Buscar membros
      description: Buscar membros
      parameters:
        - name: filter
          in: query
          description: busca por membro através de searchToken
          example: { "q": "lucas" }
          required: false
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthcareTeamPerson'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /medicalSpecialty:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Medical Specialty
      summary: Listar especialidades
      description: Listar especialidades
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MedicalSpecialtyShortResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    post:
      security:
        - bearerAuth: [ ]
      tags:
        - Medical Specialty
      summary: Criar especialidade
      description: Criar especialidade
      requestBody:
        description: Exemplo dos campos para criar especialidades
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MedicalSpecialtyRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MedicalSpecialtyResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

    put:
      security:
        - bearerAuth: [ ]
      tags:
        - Medical Specialty
      summary: Atualizar especialidade
      description: Atualizar especialidade
      requestBody:
        description: Exemplo dos campos para atualizar especialidades
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MedicalSpecialtyUpdateRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MedicalSpecialtyUpdateResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /medicalSpecialty/{id}:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Medical Specialty
      summary: Buscar especialidade por id
      description: Buscar especialidade por id
      parameters:
        - name: id
          in: path
          description: ID da especialidade
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MedicalSpecialtyFullResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /medicalSpecialty/healthSpecialistResourceBundle:
    get:
      summary: Buscar especialidades para relacionar com procedimentos
      description: Buscar especialidades para relacionar com procedimentos
      tags:
        - Medical Specialty
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  medicalSpecialties:
                    type: array
                    items:
                      $ref: '#/components/schemas/MedicalSpecialtyShortResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /staff:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Staffs
      summary: Listar Staffs
      description: Listar Staffs
      parameters:
        - name: q
          in: query
          description: Termo de busca para filtrar staffs
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página para paginação
          required: false
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          description: Número de itens por página
          required: false
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaffPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /staff/build_staff:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Staffs
      summary: Obter formulário para criação/edição de staff
      description: Retorna os dados necessários para construir o formulário de staff baseado no tipo de staff
      parameters:
        - name: filter
          in: query
          description: Filtro com o tipo de staff
          required: true
          example: '{"type":"COMMUNITY_SPECIALIST"}'
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaffFormResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /staff/search_provider_units:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Staffs
      summary: Buscar unidades de atendimento
      description: Busca unidades de atendimento com base em um termo de busca
      parameters:
        - name: filter
          in: query
          description: Filtro com o termo de busca
          required: false
          example: '{"searchToken":"hospital"}'
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProviderUnit'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /siteAccreditedNetwork/flagships:
    post:
      security:
        - bearerAuth: [ ]
      tags:
        - Site Accredited Network
      summary: Criar flagship
      description: Criar flagship
      requestBody:
        description: Exemplo dos campos para criar flagship
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrUpdateFlagshipRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlagshipResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /siteAccreditedNetwork/:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Site Accredited Network
      summary: Buscar os agrupamentos da rede credenciada
      description: Buscar os agrupamentos da rede credenciada
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/SiteAccreditedNetworkResponse'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /siteAccreditedNetwork/{id}:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Site Accredited Network
      summary: Buscar um agrupamento da rede credenciada
      description: Buscar um agrupamento da rede credenciada
      parameters:
        - name: id
          in: path
          description: ID do agrupamento
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SiteAccreditedNetworkItemResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    put:
      security:
        - bearerAuth: [ ]
      tags:
        - Site Accredited Network
      summary: Atualizar um agrupamento da rede credenciada
      description: Atualizar um agrupamento da rede credenciada
      parameters:
        - name: id
          in: path
          description: ID do agrupamento
          required: true
          schema:
            type: string
      requestBody:
        description: Exemplo dos campos para atualizar agrupamento
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSiteAccreditedNetworkRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateSiteAccreditedNetworkResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    delete:
      security:
        - bearerAuth: [ ]
      tags:
        - Site Accredited Network
      summary: Apagar um agrupamento da rede credenciada
      description: Apagar um agrupamento da rede credenciada
      parameters:
        - name: id
          in: path
          description: ID do agrupamento
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
                items:
                  $ref: '#/components/schemas/DeleteSiteAccreditedNetworkResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /healthSpecialistResourceBundle:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Health Specialist Resource Bundle
      summary: Listar procedimentos de Especialistas
      description: Listar procedimentos de Especialistas
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthSpecialistResourceBundlePaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    patch:
      security:
        - bearerAuth: [ ]
      tags:
        - Health Specialist Resource Bundle
      summary: Atualizar procedimentos de Especialistas
      description: Atualizar procedimentos de Especialistas
      requestBody:
        description: Exemplo dos campos para atualizar procedimentos de Especialistas
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HealthSpecialistResourceBundlePatchRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthSpecialistResourceBundlePatchResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'


  /healthSpecialistResourceBundle/{id}/medicalSpecialties:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Health Specialist Resource Bundle
      summary: Listar especialidades de procedimentos de Especialistas
      description: Listar especialidades de procedimentos de Especialistas
      parameters:
        - name: id
          in: path
          description: ID do procedimento de especialista
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/ResourceBundleSpecialtyBffResponse'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /resourceBundleSpecialtyPricing/effectiveDates:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Resource Bundle Specialist Pricing
      summary: Devolve as datas de vigencia permitidas para subir o CSV
      description: Devolve as datas de vigencia permitidas para subir o CSV
      responses:
        '200':
          description: Datas de vigência recuperadas com sucesso
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EffectiveDatesResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /resourceBundleSpecialtyPricing/csv:
    post:
      security:
        - bearerAuth: [ ]
      tags:
        - Resource Bundle Specialist Pricing
      summary: Baixar o csv com os preços dos procedimentos
      description: Baixar o csv com os preços dos procedimentos
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                resourceBundleSpecialtyIds:
                  type: array
                  description: Lista de IDs de especialidades de pacotes de recursos para exportar
                  items:
                    type: string
                    format: uuid
              required:
                - resourceBundleSpecialtyIds
              example:
                resourceBundleSpecialtyIds: [ "38208bed-fbd4-4cc5-8a65-c61368462000" ]
      responses:
        '200':
          description: Arquivo CSV gerado com sucesso para download
          content:
            text/csv:
              schema:
                type: string
                format: binary
                description: Conteúdo do arquivo CSV para download
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /resourceBundleSpecialtyPricingUpdate/{id}/failedLinesFile:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Resource Bundle Specialist Pricing
      summary: Baixar o csv com as linhas que falharam de um upload de preços específico
      description: Baixar o csv com as linhas que falharam de um upload de preços específico
      parameters:
        - name: id
          in: path
          description: ID do upload de preços
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Arquivo CSV gerado com sucesso para download
          content:
            text/csv:
              schema:
                type: string
                format: binary
                description: Conteúdo do arquivo CSV para download
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /resourceBundleSpecialtyPricing/processing:
    get:
      security:
        - bearerAuth: [ ]
      summary: Obter status de processamento de precificação de especialidades
      description: Retorna o status de processamento de precificação de especialidades do bundle de recursos.
      tags:
        - Resource Bundle Specialist Pricing
      responses:
        '200':
          description: Status de processamento retornado com sucesso.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProcessingResourceBundleSpecialtyPricingUpdateResponse'
        '500':
          description: Erro interno do servidor.

  /resourceBundleSpecialtyPricing/upload:
    post:
      security:
        - bearerAuth: [ ]
      tags:
        - Resource Bundle Specialist Pricing
      summary: Upload de alterações de preços
      description: Realiza o upload de um arquivo CSV contendo alterações de preços para pacotes de especialidades.
      requestBody:
        description: Arquivo CSV com as alterações de preços e metadados
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: Arquivo CSV contendo as alterações de preços
                effectiveDate:
                  type: string
                  format: date
                  description: Data de início dos preços
                  example: "2024-09-01"
                staffId:
                  $ref: '#/components/schemas/UUID'
      responses:
        '200':
          description: Upload realizado com sucesso
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceBundleSpecialtyPricingUpdate'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /resourceBundleSpecialtyPricingUpdate:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Resource Bundle Specialist Pricing
      summary: Listar histórico de atualizações de preços
      description: Listar histórico de atualizações de preços com filtros opcionais
      parameters:
        - name: startDate
          in: query
          description: Data de início para filtrar atualizações (formato YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          description: Data de fim para filtrar atualizações (formato YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
        - name: status
          in: query
          description: Status da atualização
          required: false
          schema:
            type: string
            enum: [ PROCESSED, PROCESSING, PROCESSED_WITH_ERRORS, PARSING_ERROR ]
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
            default: 1
        - name: perPage
          in: query
          description: Itens por página
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: Lista de atualizações de preços retornada com sucesso
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/ResourceSpecialistPricingIndexResponse'
                  pagination:
                    $ref: '#/components/schemas/Pagination'

  /resourceBundleSpecialty/pending:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Health Specialist Resource Bundle
      summary: Listar procedimentos de especialistas com precificação pendente
      description: Listar procedimentos de especialistas com precificação pendente
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UUID'

  /healthSpecialistResourceBundle/pricing:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Health Specialist Resource Bundle
      summary: Listar precificação de procedimentos de especialistas
      description: Listar precificação de procedimentos de especialistas
      parameters:
        - name: filter
          in: query
          description: Filtros para a listagem
          required: false
          schema:
            type: object
            properties:
              query:
                type: string
                example: "Consulta"
              pricingStatus:
                type: string
                enum:
                  - PRICED
                  - PENDING
                example: "PRICED"
              medicalSpecialtyIds:
                type: array
                items:
                  type: string
                  format: uuid
                example: ["38208bed-fbd4-4cc5-8a65-c61368462000"]
      responses:
        '200':
          description: Resposta bem-sucedida
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/PricingForHealthSpecialistResourceBundleResponse'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '4XX':
          $ref: '#/components/responses/BadRequest'


  /health-professionals:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Health Professionals
      summary: Listar profissionais de saúde
      description: Listar profissionais de saúde
      parameters:
        - name: filter
          in: query
          description: busca por profissional através de searchToken
          example: { "q": "lucas" }
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Current page
          example: 1
          required: false
          schema:
            type: string
        - name: pageSize
          in: query
          description: Number of items per page
          example: 10
          required: false
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthProfessionalPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

    post:
      security:
        - bearerAuth: [ ]
      tags:
        - Health Professionals
      summary: Criar profissional de saúde
      description: Criar profissional de saúde
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HealthProfessionalRequest'
      responses:
        '201':
          description: successful operation
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /health-professionals/{id}:
    put:
      security:
        - bearerAuth: [ ]
      tags:
        - Health Professionals
      summary: Atualizar profissional de saúde
      description: Atualizar profissional de saúde
      parameters:
        - name: id
          in: path
          description: ID health professional
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HealthProfessionalRequest'
      responses:
        '201':
          description: successful operation
        '4XX':
          $ref: '#/components/responses/BadRequest'
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Health Professionals
      summary: Buscar profissional da saúde por id
      description: Buscar profissional da saúde por id
      parameters:
        - name: id
          in: path
          description: ID do profissional da saúde
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthProfessionalFullResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'


  /health-professionals/states:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Health Professionals
      summary: Listar estados
      description: Listar estados
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthProfessionalsStatesResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /health-professionals/council-names:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Health Professionals
      summary: Listar conselhos dos profissionais de saúde
      description: Listar conselhos dos profissionais de saúde
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthProfessionalsCouncilNamesResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /suggestedProcedures:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Suggested Procedure
      summary: Listar procedimentos sugeridos
      description: Lista procedimentos sugeridos com base em filtros
      parameters:
        - name: filter
          in: query
          description: Filtros para a listagem
          required: false
          schema:
            type: object
            properties:
              q:
                type: string
                example: "exame"
              specialtyId:
                $ref: '#/components/schemas/UUID'
        - name: page
          in: query
          description: Página atual
          required: false
          schema:
            type: string
            example: "1"
        - name: pageSize
          in: query
          description: Tamanho da página
          required: false
          schema:
            type: string
            example: "10"
      responses:
        '200':
          description: Lista de procedimentos sugeridos
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuggestedProcedurePaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /suggestedProcedures/specialties:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - Suggested Procedure
      summary: Listar especialidades sugeridas
      description: Retorna uma lista paginada de especialidades com base no filtro
      parameters:
        - name: filter
          in: query
          description: Filtros para a listagem
          required: false
          schema:
            type: object
            properties:
              q:
                type: string
                example: "cardio"
        - name: page
          in: query
          description: Página atual
          required: false
          schema:
            type: string
            example: "1"
        - name: pageSize
          in: query
          description: Tamanho da página
          required: false
          schema:
            type: string
            example: "10"
      responses:
        '200':
          description: Listar especialidades para sugestão
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuggestedSpecialtyPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /previewEarningSummaries:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - SpecialistEarnings
      summary: Lista todos batchs de resumo de granhos
      description: Lista todos batchs de resumo de granhos
      parameters:
        - name: page
          in: query
          description: Current page
          example: 1
          required: false
          schema:
            type: string
        - name: pageSize
          in: query
          description: Number of items per page
          example: 10
          required: false
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreviewEarningSummariesPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

    post:
      security:
        - bearerAuth: [ ]
      tags:
        - SpecialistEarnings
      summary: Cadastro novo batch de resumo de ganhos
      description: Cadastro novo batch de resumo de ganhos
      requestBody:
        content:
          text/csv:
            schema:
              type: string
              format: csv
      responses:
        '201':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreviewEarningSummariesCreatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /previewEarningSummaries/{id}:
    delete:
      security:
        - bearerAuth: [ ]
      tags:
        - SpecialistEarnings
      summary: Apaga um batch cadastrado
      description: Apaga um batch cadastrado
      parameters:
        - name: id
          in: path
          description: ID do batch
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /invoices:
    get:
      security:
        - bearerAuth: [ ]
      tags:
        - SpecialistEarnings
      summary: Listar faturas
      description: Listar faturas de especialistas
      parameters:
        - name: page
          in: query
          description: Página atual
          example: 1
          required: false
          schema:
            type: string
        - name: pageSize
          in: query
          description: Número de itens por página
          example: 10
          required: false
          schema:
            type: string
        - name: q
          in: query
          description: Termo de busca
          required: false
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoicePaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'


components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  responses:
    BadRequest:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
  schemas:
    UUID:
      type: string
      pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
      minLength: 36
      maxLength: 36

    Date:
      type: string
      format: date
      description: "Date in ISO 8601 format, e.g., 2024-09-13"

    DateTime:
      type: string
      format: date-time
      description: "Date and time in ISO 8601 format, e.g., 2024-09-13T14:30:00"

    SignIn:
      required:
        - id_token
      type: object
      properties:
        id_token:
          type: string

    CreateFeatureFlag:
      required:
        - namespace
        - key
        - type
        - value
        - description
        - active
        - isPublic
      type: object
      properties:
        namespace:
          type: string
          example: SCHEDULE
        key:
          type: string
          example: should_use_sara_url_on_health_declaration_appointment
        type:
          type: string
          example: BOOLEAN
        value:
          type: string
          example: '0a19f0ad-255b-4b5a-a9e3-322f71730400\t'
        description:
          type: string
          example: '0a19f0ad-255b-4b5a-a9e3-322f71730400\t'
        active:
          type: boolean
          example: true
        isPublic:
          type: boolean
          example: false

    UpdateFeatureFlag:
      required:
        - type
        - value
        - active
        - isPublic
        - description
      type: object
      properties:
        type:
          type: string
          example: BOOLEAN
        value:
          type: string
          example: '0a19f0ad-255b-4b5a-a9e3-322f71730400\t'
        description:
          type: string
          example: '0a19f0ad-255b-4b5a-a9e3-322f71730400\t'
        active:
          type: boolean
          example: true
        isPublic:
          type: boolean
          example: false

    FeatureFlagFullResponse:
      required:
        - namespace
        - description
        - key
        - type
        - value
        - active
        - isPublic
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        namespace:
          type: string
          example: SCHEDULE
        description:
          type: string
          example: 'text for ff description'
        key:
          type: string
          example: should_use_sara_url_on_health_declaration_appointment
        type:
          type: string
          example: BOOLEAN
        value:
          type: string
          example: '0a19f0ad-255b-4b5a-a9e3-322f71730400\t'
        active:
          type: boolean
          example: true
        isPublic:
          type: boolean
          example: false
        createdAt:
          type: string
          example: '2024-07-16T17:46:19.915Z'
        updatedAt:
          type: string
          example: '2024-07-16T17:46:19.915Z'

    FeatureFlagShortResponse:
      required:
        - namespace
        - key
        - type
        - value
        - active
        - isPublic
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        namespace:
          type: string
          example: SCHEDULE
        key:
          type: string
          example: should_use_sara_url_on_health_declaration_appointment
        type:
          type: string
          example: BOOLEAN
        value:
          type: string
          example: '0a19f0ad-255b-4b5a-a9e3-322f71730400\t'
        active:
          type: boolean
          example: true
        isPublic:
          type: boolean
          example: false

    FeatureConfigPaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/FeatureFlagShortResponse'
        pagination:
          $ref: '#/components/schemas/Pagination'

    CompanyPaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/CompanyShortResponse'
        total:
          type: number
          example: 354

    CompanyStaffPaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/CompanyStaffResponse'
        total:
          type: number
          example: 354

    SalesAgentPaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/SalesAgentResponse'
        total:
          type: number
          example: 354

    CompanyShortResponse:
      required:
        - name
        - legalName
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: Alice
        legalName:
          type: string
          example: Alice Tech LTDA

    CreateCompanyStaffRequest:
      required:
        - firstName
        - lastName
        - email
        - role
        - companyId
      type: object
      properties:
        companyId:
          $ref: '#/components/schemas/UUID'
        firstName:
          type: string
          example: Alice
        lastName:
          type: string
          example: Braga
        email:
          type: string
          example: <EMAIL>
        role:
          type: string
          example: MAIN_COMPANY_STAFF
        accessLevel:
          type: string
          example: ADMIN

    UpdateCompanyStaffRequest:
      required:
        - firstName
        - lastName
        - email
        - role
      type: object
      properties:
        firstName:
          type: string
          example: Alice
        lastName:
          type: string
          example: Braga
        email:
          type: string
          example: <EMAIL>
        role:
          type: string
          example: MAIN_COMPANY_STAFF
        accessLevel:
          type: string
          example: ADMIN

    CompanyStaffResponse:
      required:
        - firstName
        - lastName
        - email
        - role
        - company
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        firstName:
          type: string
          example: Alice
        lastName:
          type: string
          example: Braga
        email:
          type: string
          example: <EMAIL>
        role:
          type: string
          example: MAIN_COMPANY_STAFF
        accessLevel:
          type: string
          example: ADMIN
        company:
          $ref: '#/components/schemas/CompanyShortResponse'

    SalesAgentResponse:
      required:
        - id
        - name
        - documentNumber
        - email
        - phoneNumber
        - birthDate
        - salesFirmId
        - searchTokens
        - version
        - isActiveInCampaign
        - totalLeadsFromCampaign
        - leadsFromCampaignRound
        - createdAt
        - updatedAt
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: Alice
        documentNumber:
          type: string
          example: 00000000000
        email:
          type: string
          example: <EMAIL>
        phoneNumber:
          type: string
          example: 81999999999
        birthDate:
          $ref: '#/components/schemas/Date'
        salesFirmName:
          type: string
          example: Corretores SA
        salesFirmId:
          $ref: '#/components/schemas/UUID'
        searchTokens:
          type: string
          example: 00000000000 81999999999 Alice
        version:
          type: number
          example: 1
        isActiveInCampaign:
          type: boolean
          example: false
        totalLeadsFromCampaign:
          type: number
          example: 10
        leadsFromCampaignRound:
          type: number
          example: 10
        createdAt:
          $ref: '#/components/schemas/DateTime'
        updatedAt:
          $ref: '#/components/schemas/DateTime'

    CreateSalesAgentRequest:
      required:
        - name
        - documentNumber
        - email
        - phoneNumber
        - birthDate
        - salesFirmId
        - isActiveInCampaign
      type: object
      properties:
        name:
          type: string
          example: Alice
        documentNumber:
          type: string
          example: 00000000000
        email:
          type: string
          example: <EMAIL>
        phoneNumber:
          type: string
          example: 81999999999
        birthDate:
          $ref: '#/components/schemas/Date'
        salesFirmId:
          $ref: '#/components/schemas/UUID'
        isActiveInCampaign:
          type: boolean
          example: false

    UpdateSalesAgentRequest:
      required:
        - name
        - documentNumber
        - email
        - phoneNumber
        - birthDate
        - salesFirmId
        - isActiveInCampaign
      type: object
      properties:
        name:
          type: string
          example: Alice
        documentNumber:
          type: string
          example: 00000000000
        email:
          type: string
          example: <EMAIL>
        phoneNumber:
          type: string
          example: 81999999999
        birthDate:
          $ref: '#/components/schemas/Date'
        salesFirmId:
          $ref: '#/components/schemas/UUID'
        isActiveInCampaign:
          type: boolean
          example: false

    SalesFirmResponse:
      required:
        - id
        - name
        - legalName
        - cnpj
        - email
        - phoneNumber
        - version
        - createdAt
        - updatedAt
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: Corretora X
        legalName:
          type: string
          example: Corretora X Ltda.
        cnpj:
          type: string
          example: 11111111111
        email:
          type: string
          example: <EMAIL>
        phoneNumber:
          type: string
          example: 81999999999
        version:
          type: number
          example: 1
        createdAt:
          $ref: '#/components/schemas/DateTime'
        updatedAt:
          $ref: '#/components/schemas/DateTime'

    MedicalSpecialtyRequest:
      required:
        - name
        - urlSlug
        - type
        - isTherapy
        - requireSpecialist
        - generateGeneralistSubSpecialty
        - internal
        - active
      properties:
        name:
          type: string
          example: "agendamento"
        urlSlug:
          type: string
          example: "agendamento"
        type:
          type: string
          example: "SPECIALTY"
        isTherapy:
          type: boolean
          example: false
        requireSpecialist:
          type: boolean
          example: false
        generateGeneralistSubSpecialty:
          type: boolean
          example: false
        internal:
          type: boolean
          example: false
        active:
          type: boolean
          example: true

    MedicalSpecialtyUpdateRequest:
      allOf:
        - $ref: "#/components/schemas/MedicalSpecialtyRequest"
        - type: object
          properties:
            subSpecialties:
              type: array
              items:
                $ref: '#/components/schemas/MedicalSubSpecialty'


    MedicalSpecialtyResponse:
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: "agendamento"
        active:
          type: boolean
          example: false
        type:
          type: string
          example: "SPECIALTY"
        urlSlug:
          type: string
          example: "agendamento"
        isTherapy:
          type: boolean
          example: false
        requireSpecialist:
          type: boolean
          example: false
        generateGeneralistSubSpecialty:
          type: boolean
          example: false
        internal:
          type: boolean
          example: false

    MedicalSpecialtyUpdateResponse:
      allOf:
        - $ref: "#/components/schemas/MedicalSpecialtyResponse"
        - type: object
          required:
            - subSpecialties
          properties:
            subSpecialties:
              type: array
              items:
                $ref: '#/components/schemas/MedicalSubSpecialty'

    MedicalSubSpecialty:
      required:
        - name
        - active
        - urlSlug
        - isAdvancedAccess
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: "agendamento"
        active:
          type: boolean
          example: false
        urlSlug:
          type: string
          example: "agendamento"
        parentSpecialtyId:
          $ref: '#/components/schemas/UUID'
        isAdvancedAccess:
          type: boolean
          example: false



    MedicalSpecialtyShortResponse:
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: "agendamento"
        active:
          type: boolean
          example: false
        type:
          type: string
          example: "SPECIALTY"
        specialty:
          type: string
          example: "agendamento"
        isTherapy:
          type: boolean
          example: false

    ItemValue:
      properties:
        id:
          type: string
        value:
          type: string

    FeatureNamespaceResponse:
      type: array
      items:
        $ref: '#/components/schemas/ItemValue'

    FeatureTypeResponse:
      type: array
      items:
        $ref: '#/components/schemas/ItemValue'

    Pagination:
      properties:
        totalPages:
          type: number
          example: 10
        pageSize:
          type: number
          example: 1

    Error:
      type: object
      properties:
        code:
          type: string
        message:
          type: string

    HealthcareTeam:
      required:
        - id
        - physician
        - active
        - type
        - segment
        - maxMemberAssociation
        - address
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        physician:
          $ref: '#/components/schemas/HealthcareTeamPhysicians'
        active:
          type: boolean
          example: true
        type:
          type: string
          example: LEAN
        segment:
          type: string
          example: ADULT
        maxMemberAssociation:
          type: number
          example: 123
        address:
          $ref: '#/components/schemas/AddressResponse'

    HealthcareTeamPhysicians:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: Time de saúde X
        role:
          type: string
          example: 'MANAGER_PHYSICIAN'
        gender:
          type: string
          enum:
            - MALE
            - FEMALE
            - NON_BINARY
            - NO_ANSWER
            - null

    CreateHealthcareTeam:
      required:
        - physician
        - active
        - segment
        - maxMemberAssociation
      type: object
      properties:
        physician:
          $ref: '#/components/schemas/HealthcareTeamPhysicians'
        active:
          type: boolean
          example: true
        segment:
          type: string
          example: ADULT
        maxMemberAssociation:
          type: number
          example: 123

    UpdateHealthcareTeam:
      required:
        - id
        - physician
        - active
        - segment
        - maxMemberAssociation
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        physician:
          $ref: '#/components/schemas/HealthcareTeamPhysicians'
        active:
          type: boolean
          example: true
        segment:
          type: string
          example: ADULT
        maxMemberAssociation:
          type: number
          example: 123

    AddressResponse:
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        street:
          type: string
          example: Rua X
        number:
          type: string
          example: 11
        complement:
          type: string
          example: Apto 3
        neighborhood:
          type: string
          example: Centro
        city:
          type: string
          example: São Paulo
        state:
          type: string
          example: SP
        zipcode:
          type: string
          example: 123123
        referencedModelId:
          $ref: '#/components/schemas/UUID'
        referencedModelClass:
          type: string
          example: HEALTHCARE_TEAM
        active:
          type: boolean
          example: true
        label:
          type: string
          example: Casa
        latitude:
          type: string
          example: 123123
        longitude:
          type: string
          example: 12313
        version:
          type: number
          example: 1
        createdAt:
          $ref: '#/components/schemas/DateTime'
        updatedAt:
          $ref: '#/components/schemas/DateTime'

    HealthcareTeamAssociation:
      required:
        - person
        - healthcareTeam
        - multiStaffIds
        - id
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        person:
          $ref: '#/components/schemas/HealthcareTeamPerson'
        healthcareTeam:
          $ref: '#/components/schemas/HealthcareTeamDetail'
        multiStaffIds:
          type: array
          items:
            type: string

    CreateHealthcareTeamAssociation:
      required:
        - person
        - healthcareTeam
        - multiStaffIds
      type: object
      properties:
        person:
          $ref: '#/components/schemas/HealthcareTeamPerson'
        healthcareTeam:
          $ref: '#/components/schemas/HealthcareTeamDetail'
        multiStaffIds:
          type: array
          items:
            type: string

    HealthcareTeamPerson:
      required:
        - id
        - fullName
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        fullName:
          type: string
          example: 'Nome completo'

    HealthcareTeamDetail:
      required:
        - id
        - description
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        description:
          type: string
          example: 'Médico time de saúde'

    CreateOrUpdateFlagshipRequest:
      required:
        - googlePlaceId
        - data
      type: object
      properties:
        googlePlaceId:
          type: string
          example: "ChIJN1t_tDeuEmsRUsoyG83frY4"
        data:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                enum:
                  - HOSPITAL
                  - LABORATORY
                  - EMERGENCY_UNITY
                  - MATERNITY
                  - SPECIALIST
              providerUnitId:
                $ref: '#/components/schemas/UUID'

    HealthcareTeamPaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/HealthcareTeam'
        total:
          type: number
          example: 354

    HealthcareTeamAssociationPaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/HealthcareTeamAssociation'
        total:
          type: number
          example: 354

    StaffPaginatedResponse:
      type: object
      properties:
        pagination:
          $ref: '#/components/schemas/Pagination'
        results:
          type: array
          items:
            $ref: '#/components/schemas/StaffShortResponse'

    StaffShortResponse:
      required:
        - id
        - firstName
        - lastName
        - email
        - active
        - role
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        firstName:
          type: string
          example: 'Fulano'
        lastName:
          type: string
          example: 'Sample'
        email:
          type: string
          example: '<EMAIL>'
        active:
          type: boolean
          example: true
        role:
          type: string
          example: 'consult_staff_roles'

    StaffFormResponse:
      type: object
      properties:
        specialties:
          type: array
          items:
            $ref: '#/components/schemas/MedicalSpecialty'
        providerUnits:
          type: array
          items:
            $ref: '#/components/schemas/ProviderUnit'
        staffRoles:
          type: array
          items:
            $ref: '#/components/schemas/StaffRolesResponse'
        staffTiers:
          type: array
          items:
            $ref: '#/components/schemas/StaffTiersResponse'
        staffScore:
          type: array
          items:
            $ref: '#/components/schemas/StaffScoreResponse'
        councilTypes:
          type: array
          items:
            $ref: '#/components/schemas/CouncilTypeResponse'

    StaffRolesResponse:
      type: object
      properties:
        id:
          type: string
          example: 'MANAGER_PHYSICIAN'
        name:
          type: string
          example: 'Médico(a) Gestor(a)'
        value:
          type: string
          example: 'MANAGER_PHYSICIAN'

    StaffTiersResponse:
      type: object
      properties:
        id:
          type: string
          example: 'EXPERT'
        name:
          type: string
          example: 'Expert'
        value:
          type: string
          example: 'EXPERT'

    StaffScoreResponse:
      type: object
      properties:
        id:
          type: string
          example: 'DOMINATING'
        name:
          type: string
          example: 'Dominando a parada'
        value:
          type: string
          example: 'DOMINATING'

    CouncilTypeResponse:
      type: object
      properties:
        id:
          type: integer
          example: 6
        name:
          type: string
          example: 'CRM'

    MedicalSpecialty:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: 'Cardiologia'
        type:
          type: string
          example: 'SPECIALTY'
        active:
          type: boolean
          example: true

    ProviderUnit:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: 'Hospital Albert Einstein'
        type:
          type: string
          example: 'HOSPITAL'
        status:
          type: string
          example: 'ACTIVE'
        cnpj:
          type: string
          example: '12345678000190'
        cnes:
          type: string
          example: '1234567'
        site:
          type: string
          example: 'https://hospital.com.br'
        imageUrl:
          type: string
          example: 'https://example.com/image.jpg'
        urlSlug:
          type: string
          example: 'hospital-einstein'
        showOnScheduler:
          type: boolean
          example: true
        showOnApp:
          type: boolean
          example: true

    MedicalSpecialtyFullResponse:
      required:
        - id
        - name
        - type
        - active
        - urlSlug
        - isTherapy
        - requireSpecialist
        - generateGeneralistSubSpecialty
        - internal
        - subSpecialties
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: 'Especialidade X'
        type:
          type: string
          example: 'SPECIALTY'
        active:
          type: boolean
          example: true
        urlSlug:
          type: string
          example: 'especialidade-x'
        isTherapy:
          type: boolean
          example: true
        requireSpecialist:
          type: boolean
          example: true
        generateGeneralistSubSpecialty:
          type: boolean
          example: true
        internal:
          type: boolean
          example: true
        subSpecialties:
          type: array
          items:
            $ref: '#/components/schemas/MedicalSubSpecialtiesResponse'

    MedicalSubSpecialtiesResponse:
      required:
        - name
        - active
        - urlSlug
      type: object
      properties:
        name:
          type: string
          example: 'Especialidade Y'
        active:
          type: boolean
          example: true
        urlSlug:
          type: string
          example: 'especialidade-y'
        isAdvancedAccess:
          type: boolean
          example: true

    FlagshipResponse:
      required:
        - id
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'

    HealthProfessionalsStatesResponse:
      type: array
      items:
        $ref: '#/components/schemas/ItemValue'

    HealthProfessionalsCouncilNamesResponse:
      type: array
      items:
        $ref: '#/components/schemas/ItemValue'

    HealthProfessionalRequest:
      required:
        - fullName
        - councilName
        - councilNumber
        - councilState
      type: object
      properties:
        fullName:
          type: string
          example: 'Luiz Gustavo'
        councilName:
          type: string
          example: 'CRM'
        councilState:
          type: string
          example: 'AC'
        councilNumber:
          type: string
          example: '242571'

    HealthProfessionalFullResponse:
      required:
        - id
        - fullName
        - councilName
        - councilNumber
        - state
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        fullNane:
          type: string
          example: 'Luiz Gustavo'
        councilName:
          type: string
          example: 'CRM'
        councilState:
          type: string
          example:  'AC'
        councilNumber:
          type: string
          example: '242571'
    HealthSpecialistResourceBundlePaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/HealthSpecialistResourceBundleShortResponse'
        pagination:
          $ref: '#/components/schemas/Pagination'

    HealthSpecialistResourceBundleShortResponse:
      required:
        - id
        - primaryTuss
        - secondaryResources
        - executionAmount
        - executionEnvironment
        - aliceDescription
        - status
        - serviceType
        - aliceCode
      type: object
      properties:
        id:
          type: string
          example: 'd2057943-e6d6-47b4-9ebd-ff7b77137f1b'
        primaryTuss:
          type: string
          example: '1234123'
        executionAmount:
          type: integer
          example: 1
        executionEnvironment:
          type: string
          example: 'SURGICAL'
        status:
          type: string
          example: 'ACTIVE'
        aliceCode:
          type: string
          example: '**********'
        type:
          type: string
          example: 'BUNDLE'
        serviceType:
          type: object
          properties:
            friendlyName:
              type: string
              example: 'Exame'
            value:
              type: string
              example: 'EXAM'
            color:
              type: string
              example: 'RED'
        pricingStatus:
          type: object
          properties:
            friendlyName:
              type: string
              example: 'Precificado'
            value:
              type: string
              example: 'PRICED'
            color:
              type: string
              example: 'GREEN'
        specialtiesText:
          type: string
          example: 'Todas especialidades'

    HealthProfessionalPaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/HealthProfessionalFullResponse'
        pagination:
          $ref: '#/components/schemas/Pagination'

    PreviewEarningSummariesResponse:
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        status:
          type: string
          enum:
            - PROCESSING
            - ERROR
            - SUCCESS
          example: "PROCESSING"
        file:
          type: object
          properties:
            fileName:
              type: string
              example: import.csv
            fileUrl:
              type: string
              example: http://link.com
            fileId:
              $ref: '#/components/schemas/UUID'
        errors:
          type: array
          items:
            $ref: '#/components/schemas/PreviewEarningSummariesErrorResponse'
        warnings:
          type: array
          items:
            $ref: '#/components/schemas/PreviewEarningSummariesErrorResponse'
        createdAt:
          type: string
          format: date-time

    PreviewEarningSummariesCreatedResponse:
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        status:
          type: string
          enum:
            - PROCESSING
            - ERROR
            - SUCCESS
          example: "PROCESSING"
        file:
          type: object
          properties:
            fileName:
              type: string
              example: import.csv
            fileUrl:
              type: string
              example: http://link.com
            fileId:
              $ref: '#/components/schemas/UUID'
        createdAt:
          type: string
          format: date-time

    PreviewEarningSummariesErrorResponse:
      properties:
        line:
          type: number
          example: 2
        hash:
          type: string
          example: 'aoishdiuasfsaf90sd'
        message:
          type: string
          example: 'já cadastrado'

    HealthSpecialistResourceBundlePatchRequest:
      type: object
      properties:
        aliceDescription:
          type: string
          example: 'Luiz Gustavo'
        status:
          type: string
          example: 'ACTIVE'
        medicalSpecialtyIds:
          type: array
          items:
            type: string
            example: '3b414229-c43e-4f91-b58d-cee242df5600'

    HealthSpecialistResourceBundlePatchResponse:
      type: object
      properties:
        aliceDescription:
          type: string
          example: 'Luiz Gustavo'
        secondaryResources:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                example: 'd2057943-e6d6-47b4-9ebd-ff7b77137f1b'
        executionAmount:
          type: integer
          example: 1
        executionEnvironment:
          type: string
          example: 'SURGICAL'
        status:
          type: string
          example: 'ACTIVE'
        serviceType:
          type: string
          example: 'EXAM'
        medicalSpecialtyIds:
          type: array
          items:
            type: string
            example: '3b414229-c43e-4f91-b58d-cee242df5600'

    ResourceBundleSpecialtyBffResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: 'Consulta'
        isTherapy:
          type: boolean
          example: true
        pricingStatus:
          type: object
          properties:
            friendlyName:
              type: string
              example: 'Precificado'
            value:
              type: string
              example: 'PRICED'
            color:
              type: string
              example: 'GREEN'
        currentBeginAt:
          $ref: '#/components/schemas/DateTime'
        currentEndAt:
          $ref: '#/components/schemas/DateTime'
        hasScheduledPriceChange:
          type: boolean
          example: true
        medicalSpecialtyId:
          $ref: '#/components/schemas/UUID'

    SiteAccreditedNetworkResponse:
      required:
        - id
        - title
        - active
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        title:
          type: string
          example: 'Conforto +'
        active:
          type: boolean
          example: true

    UpdateSiteAccreditedNetworkRequest:
      required:
        - id
        - title
        - active
      type: object
      properties:
        title:
          type: string
          example: 'Conforto +'
        active:
          type: boolean
          example: true
        isDefaultSearch:
          type: boolean
          example: false
        productId:
          $ref: '#/components/schemas/UUID'

    SiteAccreditedNetworkItemResponse:
      required:
        - id
        - title
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        title:
          type: string
          example: 'Conforto +'
        active:
          type: boolean
          example: true
        productId:
          $ref: '#/components/schemas/UUID'
        bundles:
          type: array
          items:
            type: object
            properties:
              id:
                $ref: '#/components/schemas/UUID'
              title:
                type: string

    EffectiveDatesResponse:
      type: object
      properties:
        dates:
          type: array
          description: Lista de datas de vigência disponíveis (formato dd/MM/yyyy)
          items:
            type: string
            example: "01/05/2025"
      required:
        - dates

    ResourceSpecialistPricingIndexResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        filename:
          type: string
          description: "Nome do arquivo de atualização de preços"
          example: "pricing_update.csv"
        downloadFileUrl:
          type: string
          description: "URL para download do arquivo original"
          example: "https://example.com/files/pricing_update.csv"
        downloadFailedLinesFileUrl:
          type: string
          description: "URL para download do arquivo com linhas que falharam"
          example: "https://example.com/files/failed_lines.csv"
        createdAt:
          type: string
          description: "Data e hora de criação formatada"
          example: "13/09/2024 | 14:30:00"
        createdBy:
          type: string
          description: "Nome do usuário que criou a atualização"
          example: "João Silva"
        status:
          $ref: '#/components/schemas/FriendlyEnumResponse'
        processingDetails:
          $ref: '#/components/schemas/ResourceSpecialistPricingProcessingDetailsResponse'

    ResourceSpecialistPricingProcessingDetailsResponse:
      type: object
      properties:
        totalItems:
          type: integer
          description: "Número total de itens no arquivo"
          example: 100
        errorItems:
          type: integer
          description: "Número de itens com erro"
          example: 5
        friendlyDescription:
          type: string
          description: "Descrição amigável do status de processamento"
          example: "95/100 itens processados com sucesso"

    UpdateSiteAccreditedNetworkResponse:
      required:
        - id
        - title
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        title:
          type: string
          example: 'Conforto +'
        active:
          type: boolean
          example: true
        productId:
          $ref: '#/components/schemas/UUID'
        bundles:
          type: array
          items:
            type: object
            properties:
              id:
                $ref: '#/components/schemas/UUID'
              title:
                type: string

    DeleteSiteAccreditedNetworkResponse:
      type: boolean
      example: true

    PricingForHealthSpecialistResourceBundleResponse:
      type: object
      properties:
        healthSpecialistResourceBundleId:
          type: string
          format: uuid
          description: ID do pacote de recursos do especialista
        primaryTuss:
          type: string
          description: Código TUSS primário
        aliceCode:
          type: string
          description: Código Alice
        description:
          type: string
          description: Descrição do pacote
        serviceType:
          type: string
          description: Tipo de serviço
        pendingNumber:
          type: integer
          description: Número de pendências
        medicalSpecialties:
          type: array
          description: Lista de especialidades médicas associadas
          items:
            $ref: '#/components/schemas/PricingForHealthSpecialistMedicalSpecialtyResponse'

    PricingForHealthSpecialistMedicalSpecialtyResponse:
      type: object
      properties:
        medicalSpecialtyId:
          type: string
          format: uuid
          description: ID da especialidade médica
        description:
          type: string
          description: Descrição da especialidade médica
        prices:
          type: array
          description: Lista de preços associados
          items:
            $ref: '#/components/schemas/ResourceBundleSpecialtyPrice'
        pendingNumber:
          type: integer
          description: Número de pendências
        beginAt:
          type: string
          format: date
          description: Data de início
        changeBeginAt:
          type: string
          format: date
          description: Data de início da alteração

    ResourceBundleSpecialtyPrice:
      type: object
      properties:
        tier:
          type: string
          example: TALENTED
        productTier:
          type: string
          example: TIER_0
        price:
          type: string
          format: decimal
          description: Preço associado

    ProcessingResourceBundleSpecialtyPricingUpdateResponse:
      type: object
      properties:
        isProcessing:
          type: boolean
          description: Indica se o processamento está em andamento.
        resourceBundleSpecialtyPricingUpdate:
          $ref: '#/components/schemas/ResourceBundleSpecialtyPricingUpdate'

    ResourceBundleSpecialtyPricingUpdate:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        fileName:
          type: string
          example: "pricing_update.csv"
        fileVaultId:
          $ref: '#/components/schemas/UUID'
        createdByStaffId:
          $ref: '#/components/schemas/UUID'
        processingAt:
          type: string
          format: date-time
          description: "Data e hora de início do processamento"
          example: "2024-09-13T14:30:00"
        completedAt:
          type: string
          format: date-time
          description: "Data e hora de conclusão do processamento"
          example: "2024-09-13T15:00:00"
        rowsCount:
          type: integer
          description: "Número total de linhas no arquivo"
          example: 100
        failedRowsCount:
          type: integer
          description: "Número de linhas com erros"
          example: 5
        failedRowsErrors:
          type: array
          description: "Lista de erros nas linhas com falha"
          items:
            $ref: '#/components/schemas/CSVPricingUpdateError'
        parsingError:
          type: string
          description: "Erro de parsing, se houver"
          example: "Erro ao processar cabeçalho do arquivo"
        pricesBeginAt:
          type: string
          format: date
          description: "Data de início dos preços"
          example: "2024-09-01"
        createdAt:
          type: string
          format: date-time
          description: "Data e hora de criação"
          example: "2024-09-13T14:00:00"
        updatedAt:
          type: string
          format: date-time
          description: "Data e hora da última atualização"
          example: "2024-09-13T14:30:00"
        version:
          type: integer
          description: "Versão do registro"
          example: 1

    CSVPricingUpdateError:
      type: object
      properties:
        row:
          type: integer
          description: "Número da linha com erro"
          example: 10
        error:
          type: string
          description: "Descrição do erro"
          example: "Valor inválido na coluna 'price'"
    SuggestedProcedurePaginatedResponse:
      type: object
      properties:
        pagination:
          $ref: '#/components/schemas/Pagination'
        results:
          type: array
          items:
            $ref: '#/components/schemas/SuggestedProcedureTransport'

    SuggestedProcedureTransport:
      type: object
      required:
        - id
        - friendlyDescription
        - active
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        friendlyDescription:
          type: string
          example: "10101012 - 9910101012 - consulta"
        active:
          type: boolean
          example: true
    SuggestedSpecialtyPaginatedResponse:
      type: object
      properties:
        pagination:
          $ref: '#/components/schemas/Pagination'
        results:
          type: array
          items:
            $ref: '#/components/schemas/SpecialtyResponse'

    SpecialtyResponse:
      type: object
      required:
        - id
        - name
        - active
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: "Cardiologia"
        active:
          type: boolean
          example: true

    PreviewEarningSummariesPaginatedResponse:
      type: object
      properties:
        pagination:
          $ref: '#/components/schemas/Pagination'
        results:
          type: array
          items:
            $ref: '#/components/schemas/PreviewEarningSummaryResponse'

    PreviewEarningSummaryResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        createdAt:
          $ref: '#/components/schemas/DateTime'
        updatedAt:
          $ref: '#/components/schemas/DateTime'
        status:
          type: string
          example: CREATED
        fileName:
          type: string
          example: "file.csv"
        totalRows:
          type: integer
          example: 10

    InvoicePaginatedResponse:
      type: object
      properties:
        pagination:
          $ref: '#/components/schemas/Pagination'
        results:
          type: array
          items:
            $ref: '#/components/schemas/InvoiceIndexResponse'

    InvoiceIndexResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        code:
          type: string
          example: "INV-123"
        status:
          $ref: '#/components/schemas/FriendlyEnumResponse'
        expenseType:
          $ref: '#/components/schemas/FriendlyEnumResponse'
        providerUnit:
          $ref: '#/components/schemas/ProviderUnitInvoiceIndexResponse'
        staff:
          $ref: '#/components/schemas/StaffInvoiceIndexResponse'
        guiaQuantity:
          type: number
          example: 5
        earningsAmount:
          type: number
          format: double
          example: 1000.50
        createdAt:
          type: string
          example: "01/01/2023 | 14:30"

    ProviderUnitInvoiceIndexResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: "Provider Name"

    StaffInvoiceIndexResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: "Staff Name"

    FriendlyEnumResponse:
      type: object
      properties:
        friendlyName:
          type: string
          example: "Pagamento Realizado"
        value:
          type: string
          example: "PAYMENT_DONE"
