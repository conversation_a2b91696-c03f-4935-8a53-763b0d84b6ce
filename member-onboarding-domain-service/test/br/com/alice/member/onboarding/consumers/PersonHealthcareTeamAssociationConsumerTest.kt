package br.com.alice.member.onboarding.consumers

import br.com.alice.clinicalaccount.event.PersonHealthcareTeamAssociationUpdatedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus.COMPLETED
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus.PENDING
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.COVER_MFC
import br.com.alice.data.layer.models.Step
import br.com.alice.member.onboarding.services.MemberOnboardingServiceImpl
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class PersonHealthcareTeamAssociationConsumerTest : ConsumerTest() {

    private val memberOnboardingService: MemberOnboardingServiceImpl = mockk()

    private val consumer = PersonHealthcareTeamAssociationConsumer(memberOnboardingService)

    private val personClinicalAccount = TestModelFactory.buildPersonClinicalAccount()
    private val memberOnboarding = TestModelFactory.buildMemberOnboarding(
        personId = personClinicalAccount.personId,
        steps = listOf(Step(templateType = COVER_MFC, status = PENDING))
    )

    @Test
    fun `#finishCoverMfcOnboardingStep - should return false if personClinicalAccount is null`() = runBlocking {
        val event = PersonHealthcareTeamAssociationUpdatedEvent(
            personId = personClinicalAccount.personId,
            newHealthcareTeamId = RangeUUID.generate(),
            updatedAt = LocalDateTime.now(),
            newPersonClinicalAccount = null
        )

        val result = consumer.completeCoverMfcOnboardingStep(event)
        assertThat(result).isSuccessWithData(false)

        coVerify { memberOnboardingService wasNot called }
    }

    @Test
    fun `#finishCoverMfcOnboardingStep - should return false if step of COVER_MFC is already completed`() = runBlocking {
        val event = PersonHealthcareTeamAssociationUpdatedEvent(
            personId = personClinicalAccount.personId,
            newHealthcareTeamId = RangeUUID.generate(),
            updatedAt = LocalDateTime.now(),
            newPersonClinicalAccount = personClinicalAccount
        )

        val memberOnboarding = memberOnboarding.copy(
            steps = listOf(Step(templateType = COVER_MFC, status = COMPLETED))
        )

        coEvery { memberOnboardingService.getByPersonId(personClinicalAccount.personId) } returns memberOnboarding.success()

        val result = consumer.completeCoverMfcOnboardingStep(event)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { memberOnboardingService.getByPersonId(any()) }
        coVerifyNone { memberOnboardingService.updateStepStatus(any(), any(), any()) }
    }

    @Test
    fun `#finishCoverMfcOnboardingStep - should finish COVER_MFC step`() = runBlocking {
        val event = PersonHealthcareTeamAssociationUpdatedEvent(
            personId = personClinicalAccount.personId,
            newHealthcareTeamId = RangeUUID.generate(),
            updatedAt = LocalDateTime.now(),
            newPersonClinicalAccount = personClinicalAccount
        )

        val expected = memberOnboarding.copy(
            steps = listOf(Step(templateType = COVER_MFC, status = COMPLETED))
        )

        coEvery { memberOnboardingService.getByPersonId(personClinicalAccount.personId) } returns memberOnboarding.success()
        coEvery { memberOnboardingService.updateStepStatus(
            memberOnboardingId = memberOnboarding.id,
            stepType = COVER_MFC,
            stepStatus = COMPLETED
        ) } returns expected.success()

        val result = consumer.completeCoverMfcOnboardingStep(event)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce {
            memberOnboardingService.getByPersonId(any())
            memberOnboardingService.updateStepStatus(any(), any(), any())
        }
    }
}
