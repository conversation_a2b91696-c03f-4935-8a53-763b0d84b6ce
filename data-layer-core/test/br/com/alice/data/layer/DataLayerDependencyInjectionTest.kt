package br.com.alice.data.layer

import br.com.alice.common.DependencyInjectionTest
import br.com.alice.common.TestApplication
import kotlin.test.Test

class DataLayerDependencyInjectionTest: DependencyInjectionTest() {

    override val modules = ApplicationModule.dependencyInjectionModules() + TestApplication.dbModule

    @Test
    fun `Application class should inject all created classes`() {
        stop()
        checkNonInjectedClasses()
    }
}
