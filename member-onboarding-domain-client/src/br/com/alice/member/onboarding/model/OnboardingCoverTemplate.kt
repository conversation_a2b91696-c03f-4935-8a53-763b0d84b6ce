package br.com.alice.member.onboarding.model

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.RemoteAction

data class OnboardingCoverTemplate (
    val type: OnboardingCoverTemplateType,
    val title: String,
    val description: String,
    val imageUrl: String,
    val agreement: String? = null,
    val button: OnboardingCoverButton
)

enum class OnboardingCoverTemplateType {
    HEALTH_DECLARATION,
    MFC
}


data class OnboardingCoverButton(
    val label: String,
    val mobileRoute: ActionRouting,
    val params: Map<String, Any>? = null
)
