package br.com.alice.data.layer.pipelines.subjects

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import org.junit.jupiter.api.Assertions.assertEquals
import kotlin.test.Test

class DigitalCareProfessionalSubjectTest {

    @Test
    fun `should create a DigitalCareProfessionalSubject from StaffModel`() {
        val id = RangeUUID.generate()
        val role = Role.DIGITAL_CARE_NURSE
        val staffModel = DataLayerTestModelFactory.buildStaff(id = id, role = role)

        val digitalCareProfessionalSubject = DigitalCareProfessionalSubject(staffModel)

        assertEquals(id, digitalCareProfessionalSubject.id)
        assertEquals(role, digitalCareProfessionalSubject.role)
    }
}
