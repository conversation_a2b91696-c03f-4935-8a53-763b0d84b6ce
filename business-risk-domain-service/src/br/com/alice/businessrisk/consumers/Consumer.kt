package br.com.alice.businessrisk.consumers

import br.com.alice.common.consumer.Consumer
import br.com.alice.data.layer.BUSINESS_RISK_ENVIRONMENT_SUBSCRIBERS
import kotlin.reflect.KClass

open class Consumer : Consumer(BUSINESS_RISK_ENVIRONMENT_SUBSCRIBERS)

open class AutoRetryableConsumer(vararg types: KClass<out Exception>) :
    br.com.alice.common.kafka.consumer.AutoRetryableConsumer(BUSINESS_RISK_ENVIRONMENT_SUBSCRIBERS, *types)
