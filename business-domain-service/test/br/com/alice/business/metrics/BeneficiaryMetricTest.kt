package br.com.alice.business.metrics

import br.com.alice.business.metrics.BeneficiaryMetric.Status.FAILURE
import br.com.alice.business.metrics.BeneficiaryMetric.Status.SUCCESS
import br.com.alice.common.RangeUUID
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.MetricAssert.Companion.assertThatCounter
import br.com.alice.common.observability.metrics.Metric
import br.com.alice.data.layer.helpers.TestModelFactory
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class BeneficiaryMetricTest {

    @BeforeTest
    fun setup() {
        mockkObject(logger)
        Metric.meterRegistry.meters.map { meter -> Metric.meterRegistry.remove(meter.id) }
    }

    @AfterTest
    fun clear() {
        unmockkAll()
    }

    @Test
    fun `#metrifyBeneficiaryNoAnswerVideoCall counts metric with expected correct label`() {

        BeneficiaryMetric.metrifyBeneficiaryNoAnswerVideoCall(FAILURE)
        BeneficiaryMetric.metrifyBeneficiaryNoAnswerVideoCall(FAILURE)
        BeneficiaryMetric.metrifyBeneficiaryNoAnswerVideoCall(SUCCESS)
        BeneficiaryMetric.metrifyBeneficiaryNoAnswerVideoCall(SUCCESS)
        BeneficiaryMetric.metrifyBeneficiaryNoAnswerVideoCall(SUCCESS)

        assertThatCounter("beneficiary_automatic_video_call_skipping")
            .withLabels("status" to "success")
            .hasCountOf(3)

        assertThatCounter("beneficiary_automatic_video_call_skipping")
            .withLabels("status" to "failure")
            .hasCountOf(2)

    }

    @Test
    fun `#countBeneficiaryMembershipUpdated counts metric with expected correct label`() {

        BeneficiaryMetric.countBeneficiaryMembershipUpdated(FAILURE)
        BeneficiaryMetric.countBeneficiaryMembershipUpdated(FAILURE)
        BeneficiaryMetric.countBeneficiaryMembershipUpdated(SUCCESS)
        BeneficiaryMetric.countBeneficiaryMembershipUpdated(SUCCESS)
        BeneficiaryMetric.countBeneficiaryMembershipUpdated(SUCCESS)

        assertThatCounter("beneficiary_membership_updated")
            .withLabels("status" to "success")
            .hasCountOf(3)

        assertThatCounter("beneficiary_membership_updated")
            .withLabels("status" to "failure")
            .hasCountOf(2)
    }

    @Test
    fun `#metrifyBeneficiaryHubspotNotFound counts metric with expected correct label`() {
        val beneficiaryId = RangeUUID.generate()

        BeneficiaryMetric.metrifyBeneficiaryHubspotNotFound(beneficiaryId)
        BeneficiaryMetric.metrifyBeneficiaryHubspotNotFound(beneficiaryId)
        BeneficiaryMetric.metrifyBeneficiaryHubspotNotFound(beneficiaryId)

        assertThatCounter("beneficiary_hubspot_not_found")
            .hasCountOf(3)

        verify(exactly = 3) { logger.info("BeneficiaryHubspot not found", any()) }
    }

    @Test
    fun `#minorPersonAlertResponsibilityTerm metric for minor person sending responsibility term`() {
        val person = TestModelFactory.buildPerson()

        BeneficiaryMetric.minorPersonAlertResponsibilityTerm(person)

        assertThatCounter("minor_person_send_responsibility_term")
            .withLabels("person_id" to person.id.toString())
            .hasCountOf(1)

        verifyOnce {
            logger.info(
                "Alert to send responsibility term for minor person",
                "person_id" to person.id.toString(),
                "national_code_identifier" to person.nationalId,
            )
        }
    }

    @Test
    fun `#failedToCreateHealthDeclarationContract metric for emitting an alert when health declaration contract creation fails`() {
        val person = TestModelFactory.buildPerson()

        BeneficiaryMetric.failedToCreateHealthDeclarationContract(person)

        assertThatCounter("failed_to_create_health_declaration_contract")
            .withLabels("person_id" to person.id.toString())
            .hasCountOf(1)

        verifyOnce { logger.error(
            "Failed to create health declaration contract for person",
            "national_code_identifier" to person.nationalId,
            "person_id" to person.id.toString(),
        )}
    }
}
