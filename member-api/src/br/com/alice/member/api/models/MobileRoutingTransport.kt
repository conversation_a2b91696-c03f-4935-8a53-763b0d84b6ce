package br.com.alice.member.api.models

import br.com.alice.common.HttpRequest
import br.com.alice.common.mobile.MemberAppVersioning
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthFormAnswerSourceType
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.ServiceConfig
import io.ktor.http.HttpHeaders
import io.ktor.http.encodeURLQueryComponent
import io.ktor.server.request.header
import java.util.UUID
import kotlin.coroutines.coroutineContext

enum class MobileRouting {
    ACCREDITED_NETWORK,
    ACCREDITED_NETWORK_DETAIL,
    ACCREDITED_NETWORK_FAVORITES,
    ACCREDITED_NETWORK_PROFILE,
    ALICE_AGORA,
    APPOINTMENT_DETAILS,
    APPOINTMENT_SCHEDULE,
    APPOINTMENT_TIMELINE,
    ARCHIVED_CHANNELS,
    B2B_ACTIVATION,
    B2B_TERMS,
    B<PERSON><PERSON>ITS,
    CHANNEL,
    CHANNEL_DEMAND,
    CHESHIRE_SCREEN,
    CHILD_VIDEOS_REQUEST,
    CONTRACT,
    CONTRACT_SIGNING,
    DATA_REGISTRATION,
    DEMAND_CHANNELS_LIST,
    DUQUESA_APPOINTMENT_INTRO,
    DUQUESA_HOME,
    DUQUESA_MAIN_MENU,
    DUQUESA_SCHEDULE_AVAILABILITY,
    DUQUESA_SCHEDULE_AVAILABILITY_PROFESSIONAL,
    DUQUESA_SCHEDULE_CONFIRMATION,
    DUQUESA_SCHEDULE_DETAIL,
    DUQUESA_SCHEDULE_ESTABLISHMENT_GROUPS,
    DUQUESA_SERVICE,
    EMERGENCY_CARE, // @deprecated use CHANNEL
    EMERGENCY_CARE_PRICING,
    EMERGENCY_CARE_STATUS,
    EMERGENCY_COLLECTION,
    EMERGENCY_LIST,
    EXTERNAL_APP,
    GAS_INIT,
    GAS_PAYMENT,
    HEALTCARE_TEAM_SCREENING,
    HEALTH_COMMUNITY,
    HEALTH_DECLARATION,
    HEALTH_DECLARATION_APPOINTMENT,
    HEALTH_DOCUMENTS,
    HEALTH_MEETINGS_LIST,
    HEALTH_PLAN_DETAILS,
    HEALTH_PLAN_EXTERNAL_SCHEDULE,
    HEALTH_PLAN_SCHEDULE,
    HEALTH_TEAM,
    HELP_CENTER,
    HOME,
    INVOICES,
    LEGAL_GUARDIAN_REGISTER,
    LEGAL_GUARDIAN_RESPONSIBILITY_TERM,
    MEMBERSHIP_CONDITIONS,
    MEMBER_ADDRESS_INPUT,
    MEMBER_ONBOARDING,
    MEMBER_ONBOARDING_V2,
    MEMBER_ONBOARDING_V2_COVER,
    ONBUDSMAN,
    PDF,
    PERSONAL_DATA,
    PORTABILITY_QUESTIONS,
    PORTABILITY_STATUS,
    PORTABILITY_STEPS,
    PROCEDURE_AUTHORIZATION_DETAIL,
    PROCEDURE_AUTHORIZATION_LIST,
    QUESTIONNAIRE,
    QUEST_IMMERSION_PROFILE,
    REDESIGN_ALICE_AGORA,
    REFUND,
    REGISTRATION,
    REVIEW_TERMS,
    SCHEDULED_APPOINTMENT_DETAIL,
    SCORE_MAGENTA_INPUT,
    SCORE_MAGENTA_RESULT,
    SHOPPING,
    SPECIALIST_DETAILS,
    TASK_SCHEDULE,
    TEST_PREPARATION,
    TEST_REQUEST_UPLOAD,
    TEST_RESULT,
    TEST_RESULT_FEEDBACK,
    TEST_RESULT_FEEDBACK_DETAILS,
    WEBVIEW,
}

data class BasicNavigationResponse(
    val navigation: NavigationResponse
)

interface Navigation {
    val mobileRoute: MobileRouting
    val link: Link?
    val navigation: NavigationResponse?
    val properties: Any?
    val root: Boolean?
}

data class NavigationResponse(
    override val mobileRoute: MobileRouting,
    override val link: Link? = null,
    override val navigation: NavigationResponse? = null,
    override val properties: Map<String, Any>? = null,
    override val root: Boolean? = null,
    val params: Map<String, Any>? = null,
) : Navigation

data class PersonData(
    val firstName: String? = null,
    val lastName: String? = null,
    val nickName: String? = null,
    val email: String? = null,
    val phone: String? = null,
    val dateOfBirth: String? = null,
    val nationalId: String? = null,
    val postalCode: String? = null,
)

data class PersonMissingData(
    val missingData: List<String> = emptyList(),
    val person: PersonData,
)

data class PersonMissingDataNavigationResponse(
    override val mobileRoute: MobileRouting,
    override val link: Link? = null,
    override val navigation: NavigationResponse? = null,
    override val properties: PersonMissingData? = null,
    override val root: Boolean? = null
) : Navigation

enum class HttpMethod {
    GET,
    POST,
    PUT,
    DELETE
}

data class Link(
    val href: String,
    val rel: String? = null,
    val type: HttpMethod? = null,
    val parameters: Collection<String>? = null
)

data class AppMessage(val key: String, val value: String)

object Links {
    val gasHelpWhatsappNumber =
        FeatureService.get(FeatureNamespace.ALICE_APP, "gas_help_whatsapp_number", "*************")

    object Shopping {
        val ORDER = Link(
            href = ServiceConfig.url("/onboarding/shopping/v2/order"),
            rel = "shopping_order"
        )

        val CART = Link(
            href = ServiceConfig.url("/onboarding/shopping/v2/product"),
            rel = "shopping_cart"
        )
    }

    object Timeline {
        val FIRST = Link(
            href = ServiceConfig.url("/health_timeline"),
            rel = "health_timeline",
            type = HttpMethod.GET,
            parameters = listOf("page=1"),
        )
    }

    val NEW_SHOPPING_CART = Link(
        href = ServiceConfig.url("/onboarding/shopping/v2/cart"),
        rel = "shopping_cart"
    )

    val SHOPPING_ORDER = Link(
        href = ServiceConfig.url("/onboarding/shopping/order"),
        parameters = listOf("product_id"),
        type = HttpMethod.POST
    )

    val PORTABILITY_QUESTIONS = Link(
        href = ServiceConfig.url("/onboarding/portability/questions"),
        rel = "portability_questions"
    )

    val PORTABILITY_QUESTIONS_V2 = Link(
        href = ServiceConfig.url("/onboarding/portability/v2/questions"),
        rel = "portability_questions"
    )

    val PORTABILITY_STEP_V3 = Link(
        href = ServiceConfig.url("/onboarding/portability/v3/step"),
        rel = "portability_step"
    )

    val PORTABILITY_STATUS = Link(
        href = ServiceConfig.url("/onboarding/portability/status"),
        rel = "portability_status"
    )

    val REGISTRATION = Link(
        href = ServiceConfig.url("/onboarding/registration"),
        rel = "registration"
    )

    val HEALTH_DECLARATION_APPOINTMENT = Link(
        href = ServiceConfig.url("/onboarding/health_declaration_appointment"),
        rel = "health_declaration_appointment"
    )

    val CONTRACT = Link(
        href = ServiceConfig.url("/onboarding/contract"),
        rel = "contract"
    )

    val ALL_CPTS = Link(
        href = "https://www.alice.com.br/cids",
        rel = "all_cpts"
    )

    val CPTS_DOUBTS_WHATSAPP = Link(
        href = "https://api.whatsapp.com/send?l=pt&phone=$gasHelpWhatsappNumber&text=Oi, tudo bem? Gostaria de tirar algumas dúvidas sobre as minhas carências!&source=&data=&app_absent=0".encodeURLQueryComponent(),
        rel = "cpts_doubts_whatsapp"
    )

    object HealthForm {
        fun getQuestionLink(
            healthFormId: UUID,
            questionId: UUID,
            id: String?,
            type: HealthFormAnswerSourceType?,
            subtype: String?
        ): Link {
            val sourceId = id?.let { "source_id=${it}&" } ?: ""
            val sourceType = type?.let { "source_type=${it}&" } ?: ""
            val sourceSubtype = subtype?.let { "source_subtype=${it}" } ?: ""
            return Link(
                href = ServiceConfig.url("/health_form/${healthFormId}/question/${questionId}?$sourceId$sourceType$sourceSubtype")
            )
        }

        fun getPreviousQuestionLink(
            healthFormId: UUID,
            questionId: UUID,
            id: String?,
            type: HealthFormAnswerSourceType?,
            subtype: String?
        ): Link {
            val sourceId = id?.let { "source_id=${it}&" } ?: ""
            val sourceType = type?.let { "source_type=${it}&" } ?: ""
            val sourceSubtype = subtype?.let { "source_subtype=${it}" } ?: ""
            return Link(
                href = ServiceConfig.url("/health_form/${healthFormId}/previous_question/${questionId}?$sourceId$sourceType$sourceSubtype")
            )
        }
    }
}

suspend fun currentMemberAppVersion(): MemberAppVersioning {
    val defaultUserAgent = "Android/1.0.0-0 (sweet/30)"

    val userAgent = coroutineContext[HttpRequest]?.httpRequest
        ?.header(HttpHeaders.UserAgent) ?: defaultUserAgent

    return try {
        MemberAppVersioning(userAgent)
    } catch (ex: Exception) {
        MemberAppVersioning(defaultUserAgent)
    }
}

suspend fun currentAppVersion() = currentMemberAppVersion()
    .let(MemberAppVersioning::version)

suspend fun currentAppTimeZoneOffset() = coroutineContext[HttpRequest]?.httpRequest
    ?.header("X-TimeZone-Offset")

suspend fun currentAppSessionId() = coroutineContext[HttpRequest]?.httpRequest
    ?.header("X-Session-Id")

suspend fun requestUserAgent() = coroutineContext[HttpRequest]?.httpRequest?.header(HttpHeaders.UserAgent)
