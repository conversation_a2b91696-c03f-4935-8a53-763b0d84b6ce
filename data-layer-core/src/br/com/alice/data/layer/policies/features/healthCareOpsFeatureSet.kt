package br.com.alice.data.layer.policies.features

import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.AppointmentScheduleModel
import br.com.alice.data.layer.models.BeneficiaryCompiledViewModel
import br.com.alice.data.layer.models.BeneficiaryModel
import br.com.alice.data.layer.models.BillingAccountablePartyModel
import br.com.alice.data.layer.models.BolepixPaymentDetailModel
import br.com.alice.data.layer.models.BoletoPaymentDetailModel
import br.com.alice.data.layer.models.CancelPaymentOnAcquirerScheduleModel
import br.com.alice.data.layer.models.CassiMemberModel
import br.com.alice.data.layer.models.Channel
import br.com.alice.data.layer.models.CoPaymentCostInfoModel
import br.com.alice.data.layer.models.CompanyActivationFilesModel
import br.com.alice.data.layer.models.CompanyContractModel
import br.com.alice.data.layer.models.CompanyModel
import br.com.alice.data.layer.models.CompanyProductPriceListingModel
import br.com.alice.data.layer.models.GenericFileVault
import br.com.alice.data.layer.models.HealthDeclaration
import br.com.alice.data.layer.models.InsurancePortabilityRequestModel
import br.com.alice.data.layer.models.InvoiceItemModel
import br.com.alice.data.layer.models.InvoiceLiquidationModel
import br.com.alice.data.layer.models.InvoicePaymentModel
import br.com.alice.data.layer.models.ItauPaymentModel
import br.com.alice.data.layer.models.Lead
import br.com.alice.data.layer.models.MemberInvoiceGroupModel
import br.com.alice.data.layer.models.MemberInvoiceModel
import br.com.alice.data.layer.models.MemberLifeCycleEventsModel
import br.com.alice.data.layer.models.MemberModel
import br.com.alice.data.layer.models.MemberProductPriceModel
import br.com.alice.data.layer.models.NullvsIntegrationLogModel
import br.com.alice.data.layer.models.OnboardingBackgroundCheckModel
import br.com.alice.data.layer.models.OnboardingContractModel
import br.com.alice.data.layer.models.OngoingCompanyDeal
import br.com.alice.data.layer.models.PaymentDetailModel
import br.com.alice.data.layer.models.PersonLoginModel
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.models.PersonOnboardingModel
import br.com.alice.data.layer.models.PersonPreferencesModel
import br.com.alice.data.layer.models.PixPaymentDetailModel
import br.com.alice.data.layer.models.PreActivationPaymentModel
import br.com.alice.data.layer.models.PriceListingModel
import br.com.alice.data.layer.models.ProductBundleModel
import br.com.alice.data.layer.models.ProductModel
import br.com.alice.data.layer.models.ProductOrderModel
import br.com.alice.data.layer.models.ProductPriceAdjustmentModel
import br.com.alice.data.layer.models.ProductPriceListingModel
import br.com.alice.data.layer.models.ResourceSignTokenModel
import br.com.alice.data.layer.models.SimpleCreditCardPaymentDetailModel
import br.com.alice.data.layer.models.StaffModel
import br.com.alice.data.layer.models.UpdatedPersonContactInfoTempModel

val changeUserProduct = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any Products") { resource is ProductModel }
        match("any MemberModel") { resource is MemberModel }
        match("any MemberProductPriceModel") { resource is MemberProductPriceModel }
        match("any ProductPriceListing") { resource is ProductPriceListingModel }
        match("any PriceListing") { resource is PriceListingModel }
    }

    match("can create and update", { action is Create || action is Update }) {
        match("any MemberModel") { resource is MemberModel }
        match("any MemberProductPriceModel") { resource is MemberProductPriceModel }
    }
}

val createProducts = policySet {
    match("can view and create and update", { action is View || action is Create || action is Update }) {
        match("any Products") { resource is ProductModel }
        match("any PriceListing") { resource is PriceListingModel }
        match("any ProductPriceListing") { resource is ProductPriceListingModel }
        match("any ProductBundle") { resource is ProductBundleModel }
    }
}

val reactivateMember = policySet {
    match("can view and create and update", { action is View || action is Create || action is Update }) {
        match("any MemberLifeCycleEventsModel") { resource is MemberLifeCycleEventsModel }
    }
}

val viewHealthCareOpsHomePage = policySet {
    match("can view", { action is View || action is Count }) {
        match("any PersonModel") { resourceIs(PersonModel::class) }
        match("any PersonOnboardingModel") { resourceIs(PersonOnboardingModel::class) }
        match("any MemberInvoice") { resourceIs(MemberInvoiceModel::class) }
        match("any MemberInvoiceGroup") { resourceIs(MemberInvoiceGroupModel::class) }
    }
}

val viewNullvsIntegrationLog = policySet {
    match("can view", { action is View || action is Count }) {
        match("any NullvsIntegrationLog") { resourceIs(NullvsIntegrationLogModel::class) }
    }
}

val viewHealthCareOpsProfilePage = policySet {
    match("can view", { action is View || action is Count }) {
        match("any PersonModel") { resourceIs(PersonModel::class) }
        match("any MemberModel") { resourceIs(MemberModel::class) }
        match("any MemberProductPriceModel") { resourceIs(MemberProductPriceModel::class) }
        match("any CassiMember") { resourceIs(CassiMemberModel::class) }
        match("any PersonOnboardingModel") { resourceIs(PersonOnboardingModel::class) }
        match("any Product") { resourceIs(ProductModel::class) }
        match("any ProductOrderModel") { resourceIs(ProductOrderModel::class) }
        match("any ProductPriceListing") { resourceIs(ProductPriceListingModel::class) }
        match("any ProductPriceAdjustment") { resourceIs(ProductPriceAdjustmentModel::class) }
        match("any PriceListing") { resourceIs(PriceListingModel::class) }
        match("any HealthDeclaration") { resourceIs(HealthDeclaration::class) }
        match("any InsurancePortabilityRequest") { resourceIs(InsurancePortabilityRequestModel::class) }
    }
}

val viewAndUpdatePersonPreferences = policySet {
    match(
        "can view, create, update and count",
        { action is Create || action is Update || action is View || action is Count }) {
        match("any PersonPreferencesModel") { resourceIs(PersonPreferencesModel::class) }
    }
}

val updateHealthCareOpsInsurancePortability = policySet {
    match("can view", { action is View || action is Count }) {
        match("any PersonModel") { resourceIs(PersonModel::class) }
        match("any PersonOnboardingModel") { resourceIs(PersonOnboardingModel::class) }
    }

    match("can update", { action is Update }) {
        match("any InsurancePortabilityRequest") { resourceIs(InsurancePortabilityRequestModel::class) }
        match("any PersonOnboardingModel") { resourceIs(PersonOnboardingModel::class) }
    }
}

val createHealthCareOpsContract = policySet {
    match("can view", { action is View || action is Count }) {
        match("any PersonModel") { resourceIs(PersonModel::class) }
        match("any PersonOnboardingModel") { resourceIs(PersonOnboardingModel::class) }
        match("any OnboardingContractModel") { resourceIs(OnboardingContractModel::class) }
    }

    match("can update", { action is Update }) {
        match("any PersonOnboardingModel") { resourceIs(PersonOnboardingModel::class) }
    }
}

val createAndUpdateHealthCareOpsContract = policySet {
    allows(PersonModel::class, View, Count)
    allows(PersonOnboardingModel::class, View, Count, Update, Create)
    allows(OnboardingContractModel::class, View, Count, Update, Create)
}

val canChangeProduct = policySet {
    match("can view", { action is View || action is Count }) {
        match("any StaffModel") { resourceIs(StaffModel::class) }
        match("any PersonModel") { resourceIs(PersonModel::class) }
        match("any PersonOnboardingModel") { resourceIs(PersonOnboardingModel::class) }
        match("any Product") { resourceIs(ProductModel::class) }
        match("any ProductPriceListing") { resourceIs(ProductPriceListingModel::class) }
        match("any PriceListing") { resourceIs(PriceListingModel::class) }
        match("any ProductBundle") { resourceIs(ProductBundleModel::class) }
    }

    match(
        "can view, create and update",
        { action is View || action is Count || action is Create || action is Update }) {
        match("any MemberModel") { resourceIs(MemberModel::class) }
        match("any ProductOrderModel") { resourceIs(ProductOrderModel::class) }
        match("any MemberProductPriceModel") { resourceIs(MemberProductPriceModel::class) }
    }
}

val updateHealthCareOpsPersonInfo = policySet {
    match("can view and update", { action is View || action is Count || action is Update }) {
        match("any PersonModel") { resourceIs(PersonModel::class) }
    }
}

val processHealthCareOpsBackgroundCheck = policySet {
    match("can view", { action is View || action is Count }) {
        match("any PersonModel") { resourceIs(PersonModel::class) }
        match("any MemberModel") { resourceIs(MemberModel::class) }
        match("any Beneficiary") { resourceIs(BeneficiaryModel::class) }
        match("any OnboardingBackgroundCheck") { resourceIs(OnboardingBackgroundCheckModel::class) }
    }

    match("can create", { action is Create }) {
        match("any OnboardingBackgroundCheck") { resourceIs(OnboardingBackgroundCheckModel::class) }
    }
}

val generateHealthCareOpsAccessCode = policySet {
    match("can view", { action is View || action is Count }) {
        match("any PersonModel") { resourceIs(PersonModel::class) }
        match("any MemberModel") { resourceIs(MemberModel::class) }
        match("any Beneficiary") { resourceIs(BeneficiaryModel::class) }
    }

    match("can create", { action is Create }) {
        match("any PersonLoginModel") { resourceIs(PersonLoginModel::class) }
    }
}

val updateHealthCareOpsMemberApp = policySet {
    match("can view", { action is View || action is Count }) {
        match("any MemberModel") { resourceIs(MemberModel::class) }
        match("any PersonModel") { resourceIs(PersonModel::class) }
        match("any PersonOnboardingModel") { resourceIs(PersonOnboardingModel::class) }
    }

    match("can update", { action is Update }) {
        match("any MemberModel") { resourceIs(MemberModel::class) }
        match("any PersonOnboardingModel") { resourceIs(PersonOnboardingModel::class) }
    }
}

val cancelHealthCareOpsMember = policySet {
    match("can view", { action is View || action is Count }) {
        match("any PersonModel") { resourceIs(PersonModel::class) }
        match("any MemberModel") { resourceIs(MemberModel::class) }
    }

    match("can update", { action is Update }) {
        match("any MemberModel") { resourceIs(MemberModel::class) }
    }
}

val healthCareOpsAnonymizeLead = policySet {
    match("can view", { action is View || action is Count }) {
        match("any Lead") { resourceIs(Lead::class) }
        match("any PersonModel") { resourceIs(PersonModel::class) }
    }

    match("can update", { action is Update }) {
        match("any Lead") { resourceIs(Lead::class) }
        match("any PersonModel") { resourceIs(PersonModel::class) }
        match("any PersonLoginModel") { resourceIs(PersonLoginModel::class) }
    }
}

val viewInvoicePayments = policySet {
    match("can view", { action is View || action is Count }) {
        match("any MemberInvoice") { resourceIs(MemberInvoiceModel::class) }
        match("any ItauPaymentModel") { resourceIs(ItauPaymentModel::class) }
        match("any MemberInvoiceGroup") { resourceIs(MemberInvoiceGroupModel::class) }
        match("any PreActivationPayment") { resourceIs(PreActivationPaymentModel::class) }
        match("any InvoicePayment") { resourceIs(InvoicePaymentModel::class) }
        match("any PaymentDetail") { resourceIs(PaymentDetailModel::class) }
        match("any BoletoPaymentDetail") { resourceIs(BoletoPaymentDetailModel::class) }
        match("any SimpleCreditCardPaymentDetail") { resourceIs(SimpleCreditCardPaymentDetailModel::class) }
        match("any PixPaymentDetail") { resourceIs(PixPaymentDetailModel::class) }
        match("any BolepixPaymentDetail") { resourceIs(BolepixPaymentDetailModel::class) }
        match("any InvoiceItem") { resourceIs(InvoiceItemModel::class) }
        match("any BillingAccountableParty") { resourceIs(BillingAccountablePartyModel::class) }
        match("any InvoiceLiquidation") { resourceIs(InvoiceLiquidationModel::class) }
        match("any ResourceSignToken") { resourceIs(ResourceSignTokenModel::class) }
    }
}

val createInvoicePayments = policySet {
    match("can view", { action is Create || action is Update }) {
        match("any MemberInvoice") { resourceIs(MemberInvoiceModel::class) }
        match("any ItauPaymentModel") { resourceIs(ItauPaymentModel::class) }
        match("any MemberInvoiceGroup") { resourceIs(MemberInvoiceGroupModel::class) }
        match("any PreActivationPayment") { resourceIs(PreActivationPaymentModel::class) }
        match("any InvoicePayment") { resourceIs(InvoicePaymentModel::class) }
        match("any PaymentDetail") { resourceIs(PaymentDetailModel::class) }
        match("any BoletoPaymentDetail") { resourceIs(BoletoPaymentDetailModel::class) }
        match("any SimpleCreditCardPaymentDetail") { resourceIs(SimpleCreditCardPaymentDetailModel::class) }
        match("any PixPaymentDetail") { resourceIs(PixPaymentDetailModel::class) }
        match("any BolepixPaymentDetail") { resourceIs(BolepixPaymentDetailModel::class) }
        match("any InvoiceItem") { resourceIs(InvoiceItemModel::class) }
        match("any InvoiceLiquidation") { resourceIs(InvoiceLiquidationModel::class) }
        match("any ResourceSignTokenModel") { resourceIs(ResourceSignTokenModel::class) }
        match("any CancelPaymentOnAcquirerScheduleModel") { resourceIs(CancelPaymentOnAcquirerScheduleModel::class) }
    }
}

val viewAndUpdateHealthCareOpsPriceListing = policySet {
    match("can view and update", { action is View || action is Count || action is Create || action is Update }) {
        match("any Product") { resourceIs(ProductModel::class) }
        match("any ProductPriceListing") { resourceIs(ProductPriceListingModel::class) }
        match("any PriceListing") { resourceIs(PriceListingModel::class) }
        match("any ProductPriceAdjustment") { resourceIs(ProductPriceAdjustmentModel::class) }
        match("any CompanyProductPriceListing") { resourceIs(CompanyProductPriceListingModel::class) }
    }
}

val createRequestChangeContactInfo = policySet {
    match("can view and update", { action is View || action is Count || action is Create || action is Update }) {
        match("any UpdatedPersonContactInfoTempModel") { resourceIs(UpdatedPersonContactInfoTempModel::class) }
    }
}

val viewHealthCareOpsLeadPage = policySet {
    match("can view", { action is View || action is Count }) {
        match("any Lead") { resourceIs(Lead::class) }
    }
}

val viewHealthCareOpsSuperAppPage = policySet {
    match("can view", { action is View || action is Count }) {
        match("any PersonModel") { resourceIs(PersonModel::class) }
        match("any AppointmentScheduleModel") { resourceIs(AppointmentScheduleModel::class) }
        match("any Channel") { resourceIs(Channel::class) }
    }
}

val viewCompanyActivationFiles = policySet {
    can(View, Count) {
        resources(CompanyActivationFilesModel::class, GenericFileVault::class)
    }
}

val viewAndUpdateOngoingCompanyDeal = policySet {
    can(View, Update, Count) {
        resources(OngoingCompanyDeal::class)
    }
}

val viewOngoingCompanyDeal = policySet {
    can(View, Count) {
        resources(OngoingCompanyDeal::class)
    }
}

val viewCoPaymentCostInfo = policySet {
    can(View, Count) {
        resources(CoPaymentCostInfoModel::class)
    }
}

val viewCompanyPage = policySet {
    can(View, Count) {
        match("any Company") { resource is CompanyModel }
        match("any Beneficiary") { resource is BeneficiaryModel }
        match("any BillingAccountableParty") { resource is BillingAccountablePartyModel }
        match("any Beneficiary") { resource is BeneficiaryModel }
        match("any OngoingCompanyDeal") { resource is OngoingCompanyDeal }
        match("any Product") { resource is ProductModel }
        match("any CompanyContract") { resource is CompanyContractModel }
        match("any BeneficiaryCompiledView") { resource is BeneficiaryCompiledViewModel }
    }
    includes(viewCompanyActivationFiles)
}
