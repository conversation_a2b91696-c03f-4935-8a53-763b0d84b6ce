package br.com.alice.data.layer.models

import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.common.service.serialization.gsonCompleteSerializer

data class SpecialistBankAccountInfoModel(
    val bankCode: String,
    val agencyNumber: String,
    val accountNumber: String
)

data class CouncilModel(
    val number: String,
    val state: State,
    val type: CouncilType? = null
) : JsonSerializable {
    override fun toString() = type?.let { "$it $number/$state" } ?: "CRM $number/$state"

    fun toJson(): String = gsonCompleteSerializer.toJson(this)
}
