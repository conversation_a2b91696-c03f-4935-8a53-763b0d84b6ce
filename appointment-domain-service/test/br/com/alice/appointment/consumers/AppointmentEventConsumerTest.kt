package br.com.alice.appointment.consumers

import br.com.alice.appointment.client.AppointmentEventService
import br.com.alice.appointment.event.AppointmentCompletedEvent
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.inProgressTag
import br.com.alice.channel.notifier.ChannelUpsertedEvent
import br.com.alice.channel.notifier.ChannelUpsertedPayload
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory.buildMedicalSpecialty
import br.com.alice.data.layer.models.*
import br.com.alice.healthplan.events.HealthPlanTaskUpsertedEvent
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.schedule.model.events.AppointmentScheduleCancelledEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCompletedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import br.com.alice.wanda.event.PersonHealthEventCreatedEvent
import br.com.alice.wanda.event.PersonHealthEventUpdatedEvent
import br.com.alice.wanda.event.TestResultFeedbackCreatedEvent
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.AfterTest
import kotlin.test.Test

class AppointmentEventConsumerTest : ConsumerTest() {

    private val appointmentEventService: AppointmentEventService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()

    private val consumer: AppointmentEventConsumer =
        AppointmentEventConsumer(appointmentEventService, medicalSpecialtyService)

    private val personId = PersonId()
    private val person = TestModelFactory.buildPerson(personId = personId)
    private val personReference = TestModelFactory.buildPersonInternalReference(personId)
    private val channelDocument = ChannelDocument(
        id = "channel_id",
        name = "Chat Name",
        personId = personId.toString(),
        channelPersonId = personReference.channelPersonId.toString(),
        type = ChannelType.ADMINISTRATIVE,
        kind = ChannelKind.CHANNEL,
        category = ChannelCategory.ADMINISTRATIVE,
        status = ChannelStatus.ACTIVE,
        tags = listOf(inProgressTag),
    )
    private val channelUpsertedPayload = ChannelUpsertedPayload(
        channelId = channelDocument.id!!,
        personId = personId,
        name = channelDocument.name,
        type = channelDocument.type!!,
        status = channelDocument.status,
        action = ChannelChangeAction.CREATE_CHANNEL,
        kind = channelDocument.kind,
        category = channelDocument.category,
        subCategory = channelDocument.subCategory,
        tags = channelDocument.tags,
        appVersion = "3.0.0"
    )
    private val testResultFeedback = TestModelFactory.buildTestResultFeedback(personId = personId)
    private val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(personId = personId)
    private val specialty = buildMedicalSpecialty(
        name = "Cardiologia",
    )
    private val referral = TestModelFactory.buildHealthPlanTaskReferral(
        personId = personId,
        specialty = ReferralSpecialty(
            id = specialty.id,
            name = specialty.name
        )
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(appointmentEventService)

    @Test
    fun `#createAppointmentEventByChannelUpserted should add appointment event`() = mockRangeUUID { uuid ->
        mockLocalDateTime { date ->
            val event = ChannelUpsertedEvent(channelUpsertedPayload)
            val appointmentEvent = AppointmentEvent(
                id = uuid,
                personId = event.payload.personId,
                name = event.payload.name!!,
                clinicalRecordName = "Atendimento administrativo",
                referenceModel = AppointmentEventReferenceModel.CHANNEL,
                referenceModelId = event.payload.channelId,
                referenceModelDate = date,
                createdAt = date,
                updatedAt = date
            )

            coEvery { appointmentEventService.upsert(appointmentEvent) } returns appointmentEvent.success()

            val result = consumer.createAppointmentEventByChannelUpserted(event)
            assertThat(result).isSuccessWithData(appointmentEvent)

            coVerifyOnce { appointmentEventService.upsert(any()) }
        }
    }

    @Test
    fun `#createAppointmentEventByChannelUpserted should add appointment event when channel does not have classification`() =
        mockRangeUUID { uuid ->
            mockLocalDateTime { date ->
                val event = ChannelUpsertedEvent(
                    channelUpsertedPayload.copy(name = null, category = null, subCategory = null)
                )
                val appointmentEvent = AppointmentEvent(
                    id = uuid,
                    personId = event.payload.personId,
                    name = "Chat em andamento",
                    clinicalRecordName = "Atendimento",
                    referenceModel = AppointmentEventReferenceModel.CHANNEL,
                    referenceModelId = event.payload.channelId,
                    referenceModelDate = date,
                    createdAt = date,
                    updatedAt = date
                )

                coEvery { appointmentEventService.upsert(appointmentEvent) } returns appointmentEvent.success()

                val result = consumer.createAppointmentEventByChannelUpserted(event)
                assertThat(result).isSuccessWithData(appointmentEvent)

                coVerifyOnce { appointmentEventService.upsert(any()) }
            }
        }

    @Test
    fun `#createAppointmentEventByChannelUpserted should delete event when channel status is not active`() =
        runBlocking {
            val payloadWithArchived = channelUpsertedPayload.copy(status = ChannelStatus.ARCHIVED)
            val event = ChannelUpsertedEvent(payloadWithArchived)

            coEvery { appointmentEventService.deleteByReferenceId(payloadWithArchived.channelId) } returns true.success()

            val result = consumer.createAppointmentEventByChannelUpserted(event)
            assertThat(result).isSuccess()

            coVerifyNone { appointmentEventService.upsert(any()) }
            coVerifyOnce { appointmentEventService.deleteByReferenceId(any()) }
        }

    @Test
    fun `#createAppointmentEventByChannelUpserted should skip when channel status is not active and no event is found`() =
        runBlocking {
            val payloadWithArchived = channelUpsertedPayload.copy(status = ChannelStatus.ARCHIVED)
            val event = ChannelUpsertedEvent(payloadWithArchived)

            coEvery {
                appointmentEventService.deleteByReferenceId(payloadWithArchived.channelId)
            } returns NotFoundException().failure()

            val result = consumer.createAppointmentEventByChannelUpserted(event)
            assertThat(result).isSuccessWithData(false)

            coVerifyNone { appointmentEventService.upsert(any()) }
            coVerifyOnce { appointmentEventService.deleteByReferenceId(any()) }
        }

    @Test
    fun `#createAppointmentEventByAppointmentScheduleCreated should add appointment event`() = mockRangeUUID { uuid ->
        mockLocalDateTime { date ->
            val event = AppointmentScheduleCreatedEvent(person, appointmentSchedule)

            val appointmentEvent = AppointmentEvent(
                id = uuid,
                personId = event.payload.person.personId,
                name = event.payload.appointmentSchedule.eventName,
                clinicalRecordName = event.payload.appointmentSchedule.generateName(),
                referenceModel = AppointmentEventReferenceModel.SCHEDULING,
                referenceModelId = event.payload.appointmentSchedule.id.toString(),
                referenceModelDate = event.payload.appointmentSchedule.startTime,
                createdAt = date,
                updatedAt = date
            )

            coEvery { appointmentEventService.create(appointmentEvent) } returns appointmentEvent.success()

            val result = consumer.createAppointmentEventByAppointmentScheduleCreated(event)
            assertThat(result).isSuccessWithData(appointmentEvent)

            coVerifyOnce { appointmentEventService.create(any()) }
        }
    }

    @Test
    fun `#createAppointmentEventByAppointmentScheduleCreated should skip appointment event when duplicated`() =
        mockRangeUUID { uuid ->
            mockLocalDateTime { date ->
                val event = AppointmentScheduleCreatedEvent(person, appointmentSchedule)

                val appointmentEvent = AppointmentEvent(
                    id = uuid,
                    personId = event.payload.person.personId,
                    name = event.payload.appointmentSchedule.eventName,
                    clinicalRecordName = event.payload.appointmentSchedule.generateName(),
                    referenceModel = AppointmentEventReferenceModel.SCHEDULING,
                    referenceModelId = event.payload.appointmentSchedule.id.toString(),
                    referenceModelDate = event.payload.appointmentSchedule.startTime,
                    createdAt = date,
                    updatedAt = date
                )

                coEvery {
                    appointmentEventService.create(appointmentEvent)
                } returns DuplicatedItemException("").failure()

                val result = consumer.createAppointmentEventByAppointmentScheduleCreated(event)
                assertThat(result).isSuccessWithData(false)

                coVerifyOnce { appointmentEventService.create(any()) }
            }
        }

    @Test
    fun `#deleteAppointmentEventByAppointmentScheduleCancelled should delete appointment event`() = runBlocking {
        val id = RangeUUID.generate()
        val event = AppointmentScheduleCancelledEvent(
            appointmentSchedule.copy(
                id = id,
                personId = personId,
                eventId = "some-event-id",
                eventName = "Some event name",
                location = null,
                status = AppointmentScheduleStatus.CANCELED,
                healthcareTeamId = null
            )
        )

        coEvery {
            appointmentEventService.deleteByReferenceId(id.toString())
        } returns true.success()

        val result = consumer.deleteAppointmentEventByAppointmentScheduleCancelled(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { appointmentEventService.deleteByReferenceId(any()) }
    }

    @Test
    fun `#deleteAppointmentEventByAppointmentScheduleCancelled should skip when no appointment event is found`() =
        runBlocking {
            val id = RangeUUID.generate()
            val event = AppointmentScheduleCancelledEvent(
                appointmentSchedule.copy(
                    id = id,
                    personId = personId,
                    eventId = "some-event-id",
                    eventName = "Some event name",
                    location = null,
                    status = AppointmentScheduleStatus.CANCELED,
                    healthcareTeamId = null
                )
            )

            coEvery {
                appointmentEventService.deleteByReferenceId(id.toString())
            } returns NotFoundException().failure()

            val result = consumer.deleteAppointmentEventByAppointmentScheduleCancelled(event)
            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { appointmentEventService.deleteByReferenceId(any()) }
        }

    @Test
    fun `#deleteAppointmentEventByAppointmentScheduleCompleted should delete appointment event`() = runBlocking {
        val event = AppointmentScheduleCompletedEvent(appointmentSchedule)

        coEvery {
            appointmentEventService.deleteByReferenceId(appointmentSchedule.id.toString())
        } returns true.success()

        val result = consumer.deleteAppointmentEventByAppointmentScheduleCompleted(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { appointmentEventService.deleteByReferenceId(any()) }
    }

    @Test
    fun `#deleteAppointmentEventByAppointmentScheduleCompleted should skip no appointment event is found`() =
        runBlocking {
            val event = AppointmentScheduleCompletedEvent(appointmentSchedule)

            coEvery {
                appointmentEventService.deleteByReferenceId(appointmentSchedule.id.toString())
            } returns NotFoundException().failure()

            val result = consumer.deleteAppointmentEventByAppointmentScheduleCompleted(event)
            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { appointmentEventService.deleteByReferenceId(any()) }
        }

    @Test
    fun `#createAppointmentEventByTestResultFeedbackCreated should add teste result feedback on appointment event`() =
        mockRangeUUID { uuid ->
            mockLocalDateTime { date ->
                val event = TestResultFeedbackCreatedEvent(testResultFeedback)
                val appointmentEvent = AppointmentEvent(
                    id = uuid,
                    personId = event.payload.testResultFeedback.personId,
                    name = "Feedback de resultado de exame",
                    clinicalRecordName = "Feedback de resultado de exame",
                    referenceModel = AppointmentEventReferenceModel.TEST_RESULT,
                    referenceModelId = event.payload.testResultFeedback.id.toString(),
                    referenceModelDate = event.payload.testResultFeedback.addedAt,
                    createdAt = date,
                    updatedAt = date
                )

                coEvery {
                    appointmentEventService.create(appointmentEvent)
                } returns appointmentEvent.success()

                val result = consumer.createAppointmentEventByTestResultFeedbackCreated(event)
                assertThat(result).isSuccessWithData(appointmentEvent)

                coVerifyOnce { appointmentEventService.create(any()) }
            }
        }

    @Test
    fun `#createOrDeleteAppointmentEventByHealthPlanTaskCreated should add referral`() =
        mockRangeUUID { uuid ->
            mockLocalDateTime { date ->
                withFeatureFlag(
                    FeatureNamespace.APPOINTMENT,
                    "can_import_health_plan_tasks_to_appointment_event",
                    true
                ) {
                    val referral = referral.copy(
                        id = uuid,
                        createdAt = date
                    )

                    val event = HealthPlanTaskUpsertedEvent(referral)
                    val appointmentEvent = AppointmentEvent(
                        referenceModel = AppointmentEventReferenceModel.HEALTH_PLAN_TASK,
                        referenceModelId = event.payload.task.id.toString(),
                        referenceModelDate = event.payload.task.createdAt,
                        name = "Consulta de Cardiologia",
                        personId = event.payload.task.personId,
                        clinicalRecordName = "Consulta de Cardiologia",
                    )

                    coEvery {
                        appointmentEventService.create(appointmentEvent)
                    } returns appointmentEvent.success()
                    coEvery { medicalSpecialtyService.getById(specialty.id) } returns specialty.success()

                    val result = consumer.createOrDeleteAppointmentEventByHealthPlanTaskCreated(event)
                    assertThat(result).isSuccessWithData(appointmentEvent)

                    coVerifyOnce { appointmentEventService.create(any()) }
                    coVerifyOnce { medicalSpecialtyService.getById(specialty.id) }
                }
            }
        }

    @Test
    fun `#createOrDeleteAppointmentEventByHealthPlanTaskCreated should add referral when specialty is therapy`() =
        mockRangeUUID { uuid ->
            mockLocalDateTime { date ->
                withFeatureFlag(
                    FeatureNamespace.APPOINTMENT,
                    "can_import_health_plan_tasks_to_appointment_event",
                    true
                ) {
                    val specialty = specialty.copy(
                        isTherapy = true
                    )
                    val referral = referral.copy(
                        id = uuid,
                        createdAt = date
                    )

                    val event = HealthPlanTaskUpsertedEvent(referral)
                    val appointmentEvent = AppointmentEvent(
                        referenceModel = AppointmentEventReferenceModel.HEALTH_PLAN_TASK,
                        referenceModelId = event.payload.task.id.toString(),
                        referenceModelDate = event.payload.task.createdAt,
                        name = "Sessão de Cardiologia",
                        personId = event.payload.task.personId,
                        clinicalRecordName = "Sessão de Cardiologia",
                    )

                    coEvery {
                        appointmentEventService.create(appointmentEvent)
                    } returns appointmentEvent.success()
                    coEvery { medicalSpecialtyService.getById(specialty.id) } returns specialty.success()

                    val result = consumer.createOrDeleteAppointmentEventByHealthPlanTaskCreated(event)
                    assertThat(result).isSuccessWithData(appointmentEvent)

                    coVerifyOnce { appointmentEventService.create(any()) }
                    coVerifyOnce { medicalSpecialtyService.getById(specialty.id) }
                }
            }
        }

    @Test
    fun `#createOrDeleteAppointmentEventByHealthPlanTaskCreated should delete referral when status is not active`() =
        mockRangeUUID { uuid ->
            mockLocalDateTime { date ->
                withFeatureFlag(
                    FeatureNamespace.APPOINTMENT,
                    "can_import_health_plan_tasks_to_appointment_event",
                    true
                ) {
                    val referral = referral.copy(
                        status = HealthPlanTaskStatus.DELETED,
                        id = uuid,
                        createdAt = date
                    )

                    val event = HealthPlanTaskUpsertedEvent(referral)

                    coEvery {
                        appointmentEventService.deleteByReferenceId(referral.id.toString())
                    } returns true.success()

                    val result = consumer.createOrDeleteAppointmentEventByHealthPlanTaskCreated(event)
                    assertThat(result).isSuccessWithData(true)

                    coVerifyOnce { appointmentEventService.deleteByReferenceId(any()) }
                }
            }
        }

    @Test
    fun `#createOrDeleteAppointmentEventByHealthPlanTaskCreated should not add when health plan task is not referral`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.APPOINTMENT, "can_import_health_plan_tasks_to_appointment_event", true) {

                val task = TestModelFactory.buildHealthPlanTaskEmergency()

                val event = HealthPlanTaskUpsertedEvent(task)

                val result = consumer.createOrDeleteAppointmentEventByHealthPlanTaskCreated(event)
                assertThat(result).isSuccessWithData(false)

                coVerify { appointmentEventService wasNot called }
            }
        }

    @Test
    fun `#createOrDeleteAppointmentEventByHealthPlanTaskCreated should not add when feature is disabled`() =
        runBlocking {
            val event = HealthPlanTaskUpsertedEvent(referral)
            coEvery {
                appointmentEventService.deleteByReferenceId(referral.id.toString())
            } returns false.success()

            val result = consumer.createOrDeleteAppointmentEventByHealthPlanTaskCreated(event)
            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { appointmentEventService.deleteByReferenceId(any()) }
        }

    @Test
    fun `#createOrDeleteAppointmentEventByHealthPlanTaskCreated should add follow up request`() =
        mockRangeUuidAndDateTime { _, _ ->
            withFeatureFlag(
                FeatureNamespace.APPOINTMENT,
                "can_import_health_plan_tasks_to_appointment_event",
                true
            ) {
                val name = "Retorno com Joaozinho"
                val followUpRequest = FollowUpRequest(
                    providerType = FollowUpProviderType.REMOTE,
                    followUpInterval = FollowUpInterval(
                        type = FollowUpIntervalType.AFTER_MEDICAL_TREATMENT,
                        unit = PeriodUnit.DAY,
                        quantity = 1
                    ),
                    task = TestModelFactory.buildHealthPlanTask(
                        personId = person.id,
                        type = HealthPlanTaskType.FOLLOW_UP_REQUEST,
                        groupId = RangeUUID.generate(),
                        status = HealthPlanTaskStatus.ACTIVE,
                        title = name
                    ),
                )

                val event = HealthPlanTaskUpsertedEvent(followUpRequest)
                val appointmentEvent = AppointmentEvent(
                    referenceModel = AppointmentEventReferenceModel.HEALTH_PLAN_TASK,
                    referenceModelId = event.payload.task.id.toString(),
                    referenceModelDate = event.payload.task.createdAt,
                    name = name,
                    personId = event.payload.task.personId,
                    clinicalRecordName = name,
                )

                coEvery {
                    appointmentEventService.create(appointmentEvent)
                } returns appointmentEvent.success()

                val result = consumer.createOrDeleteAppointmentEventByHealthPlanTaskCreated(event)
                assertThat(result).isSuccessWithData(appointmentEvent)

                coVerifyOnce { appointmentEventService.create(any()) }
            }
        }

    @Test
    fun `#createAppointmentEventByTestResultFeedbackCreated should skip when is duplicated`() = mockRangeUUID { uuid ->
        mockLocalDateTime { date ->
            val event = TestResultFeedbackCreatedEvent(testResultFeedback)
            val appointmentEvent = AppointmentEvent(
                id = uuid,
                personId = event.payload.testResultFeedback.personId,
                name = "Feedback de resultado de exame",
                clinicalRecordName = "Feedback de resultado de exame",
                referenceModel = AppointmentEventReferenceModel.TEST_RESULT,
                referenceModelId = event.payload.testResultFeedback.id.toString(),
                referenceModelDate = event.payload.testResultFeedback.addedAt,
                createdAt = date,
                updatedAt = date
            )

            coEvery {
                appointmentEventService.create(appointmentEvent)
            } returns DuplicatedItemException("").failure()

            val result = consumer.createAppointmentEventByTestResultFeedbackCreated(event)
            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { appointmentEventService.create(any()) }
        }
    }

    @Test
    fun `#createAppointmentEventByPersonHealthEventCreated should add appointment event`() = mockRangeUUID { uuid ->
        mockLocalDateTime { date ->
            withFeatureFlag(FeatureNamespace.APPOINTMENT, "can_import_wanda_to_appointment_event", true) {
                val personHealthEvent = TestModelFactory.buildPersonHealthEvent(
                    category = PersonHealthEventCategory.EXTENDED_HUDDLE,
                    status = PersonHealthEventStatus.NOT_STARTED
                )
                val event = PersonHealthEventCreatedEvent(personHealthEvent)
                val appointmentEvent = AppointmentEvent(
                    id = uuid,
                    personId = personHealthEvent.personId,
                    name = personHealthEvent.title,
                    clinicalRecordName = "Wanda | Discussão de caso (síncrona ou assíncrona)",
                    referenceModel = AppointmentEventReferenceModel.WANDA_TASK,
                    referenceModelId = personHealthEvent.id.toString(),
                    referenceModelDate = personHealthEvent.eventDate,
                    createdAt = date,
                    updatedAt = date
                )

                coEvery { appointmentEventService.upsert(appointmentEvent) } returns appointmentEvent.success()

                val result = consumer.createAppointmentEventByPersonHealthEventCreated(event)
                assertThat(result).isSuccessWithData(appointmentEvent)

                coVerifyOnce { appointmentEventService.upsert(any()) }
            }
        }
    }

    @Test
    fun `#updateAppointmentEventByPersonHealthEventUpdated should update appointment event`() = mockRangeUUID { uuid ->
        mockLocalDateTime { date ->

            withFeatureFlag(FeatureNamespace.APPOINTMENT, "can_import_wanda_to_appointment_event", true) {

                val personHealthEvent = TestModelFactory.buildPersonHealthEvent(
                    category = PersonHealthEventCategory.AA_HEALTH_FOLLOW_UP,
                    status = PersonHealthEventStatus.IN_PROGRESS
                )
                val event = PersonHealthEventUpdatedEvent(personHealthEvent)
                val appointmentEvent = AppointmentEvent(
                    id = uuid,
                    personId = personHealthEvent.personId,
                    name = personHealthEvent.title,
                    clinicalRecordName = "Wanda | Acompanhamento de caso (FUP)",
                    referenceModel = AppointmentEventReferenceModel.WANDA_TASK,
                    referenceModelId = personHealthEvent.id.toString(),
                    referenceModelDate = personHealthEvent.eventDate,
                    createdAt = date,
                    updatedAt = date
                )

                coEvery { appointmentEventService.upsert(appointmentEvent) } returns appointmentEvent.success()

                val result = consumer.updateAppointmentEventByPersonHealthEventUpdated(event)
                assertThat(result).isSuccessWithData(appointmentEvent)

                coVerifyOnce { appointmentEventService.upsert(any()) }
            }
        }
    }

    @Test
    fun `#updateAppointmentEventByPersonHealthEventUpdated should delete appointment event`() = runBlocking {
        val personHealthEvent = TestModelFactory.buildPersonHealthEvent(status = PersonHealthEventStatus.FINISHED)
        val event = PersonHealthEventUpdatedEvent(personHealthEvent)

        coEvery {
            appointmentEventService.deleteByReferenceId(personHealthEvent.id.toString())
        } returns true.success()

        val result = consumer.updateAppointmentEventByPersonHealthEventUpdated(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { appointmentEventService.deleteByReferenceId(any()) }
    }

    @Test
    fun `#deleteEventByAppointmentCompleted returns true and delete event by appointment association`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.APPOINTMENT, "can_delete_event_by_appointment", true) {

                val appointmentEvent = AppointmentEventDetail(
                    name = "event",
                    referenceModelId = "referenceModelId",
                    referenceModel = AppointmentEventReferenceModel.SCHEDULING
                )

                val appointment = TestModelFactory.buildAppointment(event = appointmentEvent)
                val event = AppointmentCompletedEvent(appointment)

                coEvery {
                    appointmentEventService.deleteByReferenceId(appointmentEvent.referenceModelId)
                } returns true.success()

                val result = consumer.deleteEventByAppointmentCompleted(event)
                assertThat(result).isSuccessWithData(true)

                coVerifyOnce { appointmentEventService.deleteByReferenceId(any()) }
            }
        }

    @Test
    fun `#deleteEventByAppointmentCompleted returns false and do nothing when appointment have not association`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.APPOINTMENT, "can_delete_event_by_appointment", true) {
                val appointment = TestModelFactory.buildAppointment(event = null)
                val event = AppointmentCompletedEvent(appointment)

                val result = consumer.deleteEventByAppointmentCompleted(event)
                assertThat(result).isSuccessWithData(false)

                coVerify { appointmentEventService wasNot called }
            }
        }

    @Test
    fun `#deleteEventByAppointmentCompleted returns false when flag is disabled`() =
        runBlocking {
            val appointment = TestModelFactory.buildAppointment(event = null)
            val event = AppointmentCompletedEvent(appointment)

            val result = consumer.deleteEventByAppointmentCompleted(event)
            assertThat(result).isSuccessWithData(false)

            coVerify { appointmentEventService wasNot called }
        }

    @Test
    fun `#deleteEventByAppointmentCompleted returns false when event is CHANNEL`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.APPOINTMENT, "can_delete_event_by_appointment", true) {

                val appointmentEvent = AppointmentEventDetail(
                    name = "event",
                    referenceModelId = "referenceModelId",
                    referenceModel = AppointmentEventReferenceModel.CHANNEL
                )

                val appointment = TestModelFactory.buildAppointment(event = appointmentEvent)
                val event = AppointmentCompletedEvent(appointment)

                val result = consumer.deleteEventByAppointmentCompleted(event)
                assertThat(result).isSuccessWithData(false)

                coVerify { appointmentEventService wasNot called }
            }
        }

    companion object {
        @JvmStatic
        fun appointmentScheduleTypesToClinicalRecordName() = listOf(
            arrayOf(AppointmentScheduleType.COMMUNITY, "Consulta"),
            arrayOf(AppointmentScheduleType.CONNECTION, "Consulta"),
            arrayOf(AppointmentScheduleType.FOLLOW_UP, "Retorno (Digital)"),
            arrayOf(AppointmentScheduleType.FOLLOW_UP_NUTRITIONIST, "Retorno | Nutrição"),
            arrayOf(AppointmentScheduleType.FOLLOW_UP_PHYSICAL_EDUCATOR, "Retorno | Preparação física"),
            arrayOf(AppointmentScheduleType.HEALTH_DECLARATION, "Declaração de saúde"),
            arrayOf(AppointmentScheduleType.HEALTHCARE_TEAM, "Retorno | Méd. (Digital)"),
            arrayOf(AppointmentScheduleType.IMMERSION, "Imersão"),
            arrayOf(AppointmentScheduleType.NURSE, "Consulta | Enf."),
            arrayOf(AppointmentScheduleType.NURSE_ONSITE, "Consulta | Enf. OnSite"),
            arrayOf(AppointmentScheduleType.NUTRITIONIST, "Consulta | Nutrição"),
            arrayOf(AppointmentScheduleType.OTHER, "Consulta"),
            arrayOf(AppointmentScheduleType.PHYSICAL_EDUCATOR, "Consulta | Preparação física"),
            arrayOf(AppointmentScheduleType.PHYSICIAN_ONSITE, "Avaliação Presencial Aguda"),
            arrayOf(AppointmentScheduleType.PROC_NURSE, "Procedimento | Enf. OnSite"),
            arrayOf(AppointmentScheduleType.PROC_ONSITE, "Procedimento | Méd. OnSite"),
            arrayOf(AppointmentScheduleType.PSYCHOLOGIST, "Consulta | Psicologia"),
            arrayOf(AppointmentScheduleType.TEST, "Coleta"),
            arrayOf(AppointmentScheduleType.HOME_TEST, "Consulta"),
        )
    }

    @ParameterizedTest(name = "#appointmentSchedule GenerateName for type {0} is {1}")
    @MethodSource("appointmentScheduleTypesToClinicalRecordName")
    fun appointmentScheduleGenerateName(type: AppointmentScheduleType, expected: String) {
        assertThat(appointmentSchedule.copy(type = type).generateName()).isEqualTo(expected)
    }

}
