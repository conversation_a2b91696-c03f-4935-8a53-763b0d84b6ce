package br.com.alice.api.healthcareops.ioc

import br.com.alice.api.healthcareops.ApplicationModule
import br.com.alice.api.healthcareops.consumers.TaxDocumentConsumer
import br.com.alice.api.healthcareops.controllers.*
import br.com.alice.api.healthcareops.controllers.business.BeneficiaryController
import br.com.alice.api.healthcareops.controllers.business.BeneficiaryOnboardingController
import br.com.alice.api.healthcareops.controllers.business.CompanyActivationFilesController
import br.com.alice.api.healthcareops.controllers.business.CompanyBeneficiaryController
import br.com.alice.api.healthcareops.controllers.business.CompanyContractController
import br.com.alice.api.healthcareops.controllers.business.CompanyContractualRiskController
import br.com.alice.api.healthcareops.controllers.business.CompanyController
import br.com.alice.api.healthcareops.controllers.business.CompanyInvoiceController
import br.com.alice.api.healthcareops.controllers.business.CompanyProductPriceListingController
import br.com.alice.api.healthcareops.controllers.business.CompanySubContractController
import br.com.alice.api.healthcareops.controllers.business.ContractualRiskController
import br.com.alice.api.healthcareops.controllers.moneyin.InvoiceItemsController
import br.com.alice.api.healthcareops.controllers.moneyin.InvoiceLiquidationController
import br.com.alice.api.healthcareops.controllers.moneyin.InvoicesController
import br.com.alice.api.healthcareops.services.AuthService
import br.com.alice.api.healthcareops.services.DocumentService
import br.com.alice.api.healthcareops.services.TaxDocumentService
import br.com.alice.api.healthcareops.usecases.GetCompanyInvoicesUseCase
import br.com.alice.api.healthcareops.usecases.PreActivationCompanyInvoiceUseCase
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.RunningMode
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.common.storage.FileVaultStorage
import br.com.alice.communication.email.sender.EmailSenderClient
import br.com.alice.communication.email.sender.MockEmailClient
import br.com.alice.communication.email.sender.SimpleEmailServiceClient
import br.com.alice.communication.email.template.EmailTemplateClient
import br.com.alice.communication.email.template.MockEmailTemplateClient
import br.com.alice.communication.email.template.SimpleEmailServiceTemplateClient
import br.com.alice.data.layer.HEALTH_CARE_OPS_ROOT_SERVICE_NAME
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.module


val ServiceModule = module(createdAtStart = true) {
    // Configuration
    single { ApplicationModule.config }

    // file vault
    val client = DefaultHttpClient({
        install(ContentNegotiation) {
            gsonSnakeCase()
        }
    }, timeoutInMillis = 15_000)

    single { FileVaultStorage(client) }

    // Services
    singleOf(::AuthService)
    singleOf(::DocumentService)
    singleOf(::TaxDocumentService)

    // Internal services
    singleOf(::GetCompanyInvoicesUseCase)
    singleOf(::PreActivationCompanyInvoiceUseCase)

    // Consumers
    singleOf(::TaxDocumentConsumer)

    // Controllers
    single { HealthController(HEALTH_CARE_OPS_ROOT_SERVICE_NAME) }
    singleOf(::AuthController)
    singleOf(::PersonController)
    singleOf(::OnboardingController)
    singleOf(::NullvsIntegrationLogController)
    singleOf(::HealthDeclarationController)
    singleOf(::MemberController)
    singleOf(::MemberProductPriceController)
    singleOf(::LeadsController)
    singleOf(::InvoicesController)
    singleOf(::ContractController)
    singleOf(::LegalGuardianAssociationController)
    singleOf(::LegalGuardianInfoTempController)
    singleOf(::UpdatedPersonContactInfoTempController)
    singleOf(::PersonRegistrationController)
    singleOf(::MemberTelegramTrackingController)
    singleOf(::SearchController)

    // business
    singleOf(::BeneficiaryController)
    singleOf(::BeneficiaryOnboardingController)
    singleOf(::CompanyController)
    singleOf(::CompanyInvoiceItemsController)
    singleOf(::CompanyContractController)
    singleOf(::CompanySubContractController)
    singleOf(::ContractualRiskController)
    singleOf(::CompanyProductPriceListingController)
    singleOf(::CompanyActivationFilesController)
    singleOf(::CompanyBeneficiaryController)
    singleOf(::CompanyContractualRiskController)
    singleOf(::CompanyInvoiceController)

    singleOf(::BackgroundCheckController)
    singleOf(::OperationOnboardingController)
    singleOf(::HealthDeclarationAppointmentScheduleController)
    singleOf(::AnonymizationController)
    singleOf(::PersonAuthController)
    singleOf(::ProductsController)
    singleOf(::ProductOrderController)
    singleOf(::InsurancePortabilityController)
    singleOf(::InsurancePortabilityV3Controller)
    singleOf(::PromoCodeController)
    singleOf(::TaxDocumentController)
    singleOf(::InvoicePriceController)
    singleOf(::InvoiceItemsController)
    singleOf(::CompanyInvoiceItemsController)
    singleOf(::FeatureController)
    singleOf(::PersonPreferencesController)
    singleOf(::PriceListingController)
    singleOf(::ProductPriceListingController)
    singleOf(::BillingAccountablePartyController)
    singleOf(::BackfillPriceListingController)
    singleOf(::BackfillMemberProductController)
    singleOf(::PaymentBackfillController)
    singleOf(::CompanyDealController)
    singleOf(::OngoingCompanyDealController)
    singleOf(::MemberContractController)
    singleOf(::InvoiceLiquidationController)

    if (BaseConfig.instance.runningMode == RunningMode.PRODUCTION) {
        single<EmailSenderClient> { SimpleEmailServiceClient(get()) }
        single<EmailTemplateClient> { SimpleEmailServiceTemplateClient(get()) }
    } else {
        single<EmailSenderClient> { MockEmailClient }
        single<EmailTemplateClient> { MockEmailTemplateClient }
    }
}
