package br.com.alice.bff.business.services

import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.data.layer.models.CompanySize
import br.com.alice.data.layer.models.CompanyStaffAccessLevel
import br.com.alice.data.layer.models.FeatureNamespace
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.util.UUID
import kotlin.test.Test

class ModuleServiceTest {
    private val service = ModuleService()

    @Test
    fun `#getActiveModules should return modules correctly when company is MLA`(): Unit = runBlocking {
        val companyTraits = CompanyTraits(CompanySize.MLA, "123456", UUID.randomUUID() )
        val modules = service.getActiveModules(companyTraits)

        assertThat(modules).isNotEmpty
        assertThat(modules).contains(Modules.BENEFICIARIES_CHANGE_PRODUCT)
    }

    @Test
    fun `#getActiveModules should return change product module correctly when company is SMALL`(): Unit = runBlocking {
        val companyTraits = CompanyTraits(CompanySize.SMALL, "123456", UUID.randomUUID() )

        val modules = service.getActiveModules(companyTraits)

        assertThat(modules).isNotEmpty
        assertThat(modules).contains(Modules.BENEFICIARIES_CHANGE_PRODUCT)
    }


    @Test
    fun `#getActiveModules should return invoices when company is micro`(): Unit = runBlocking {
        val companyTraits = CompanyTraits(CompanySize.MICRO, "123456", UUID.randomUUID() )

        val modules = service.getActiveModules(companyTraits)

        assertThat(modules).size().isEqualTo(1)
        assertThat(modules).contains(Modules.INVOICES_MODULE)
    }

    @Test
    fun `#getActiveModules should return invoices when company is UNKNOWN`(): Unit = runBlocking {
        val companyTraits = CompanyTraits(CompanySize.UNKNOWN, "123456", UUID.randomUUID() )

        val modules = service.getActiveModules(companyTraits)

        assertThat(modules).size().isEqualTo(1)
        assertThat(modules).contains(Modules.INVOICES_MODULE)
    }

    @Test
    fun `#getActiveModules should return invoices when company is null`(): Unit = runBlocking {
        val companyTraits = CompanyTraits(null, "123456", UUID.randomUUID() )

        val modules = service.getActiveModules(companyTraits)

        assertThat(modules).size().isEqualTo(1)
        assertThat(modules).contains(Modules.INVOICES_MODULE)
    }

    @Test
    fun `#getActiveModules should return modules correctly when access level is admin`(): Unit = runBlocking {
        val companyTraits = CompanyTraits(CompanySize.MLA, "123456", UUID.randomUUID() )
        val modules = service.getActiveModules(companyTraits, CompanyStaffAccessLevel.ADMIN)

        assertThat(modules).isNotEmpty
        assertThat(modules).containsOnly(
            Modules.INVOICES_MODULE,
            Modules.BENEFICIARIES_MODULE,
            Modules.PRODUCTS_MODULE,
            Modules.DATA_MODULE,
            Modules.DATA_MODULE_DEMOGRAPHICS_TAB,
            Modules.DATA_MODULE_MAGENTA_TAB,
            Modules.DATA_MODULE_JOURNEY_TAB,
            Modules.BENEFICIARIES_ADD,
            Modules.BENEFICIARIES_CANCEL,
            Modules.BENEFICIARIES_CHANGE_PRODUCT,
            Modules.SETTINGS_MODULE,
        )
    }

    @Test
    fun `#getActiveModules should return modules correctly when access level is admin when ff is enabled`(): Unit = runBlocking {
        withFeatureFlag(
            namespace = FeatureNamespace.PORTAL_2_HR,
            key = "should_return_beneficiary_batch_module",
            value = true,
        ) {
            val companyTraits = CompanyTraits(CompanySize.MLA, "123456", UUID.randomUUID() )
            val modules = service.getActiveModules(companyTraits, CompanyStaffAccessLevel.ADMIN)

            assertThat(modules).isNotEmpty
            assertThat(modules).containsOnly(
                Modules.INVOICES_MODULE,
                Modules.BENEFICIARIES_MODULE,
                Modules.PRODUCTS_MODULE,
                Modules.DATA_MODULE,
                Modules.DATA_MODULE_DEMOGRAPHICS_TAB,
                Modules.DATA_MODULE_MAGENTA_TAB,
                Modules.DATA_MODULE_JOURNEY_TAB,
                Modules.BENEFICIARIES_ADD,
                Modules.BENEFICIARIES_CANCEL,
                Modules.BENEFICIARIES_CHANGE_PRODUCT,
                Modules.SETTINGS_MODULE,
                Modules.BENEFICIARIES_BATCH_MODULE,
            )
        }
    }

    @Test
    fun `#getActiveModules should return modules correctly when access level is admin when ff is disabled`(): Unit = runBlocking {
        withFeatureFlag(
            namespace = FeatureNamespace.PORTAL_2_HR,
            key = "should_return_beneficiary_batch_module",
            value = false,
        ) {
            val companyTraits = CompanyTraits(CompanySize.MLA, "123456", UUID.randomUUID() )
            val modules = service.getActiveModules(companyTraits, CompanyStaffAccessLevel.ADMIN)

            assertThat(modules).isNotEmpty
            assertThat(modules).containsOnly(
                Modules.INVOICES_MODULE,
                Modules.BENEFICIARIES_MODULE,
                Modules.PRODUCTS_MODULE,
                Modules.DATA_MODULE,
                Modules.DATA_MODULE_DEMOGRAPHICS_TAB,
                Modules.DATA_MODULE_MAGENTA_TAB,
                Modules.DATA_MODULE_JOURNEY_TAB,
                Modules.BENEFICIARIES_ADD,
                Modules.BENEFICIARIES_CANCEL,
                Modules.BENEFICIARIES_CHANGE_PRODUCT,
                Modules.SETTINGS_MODULE,
            )
        }
    }

    @Test
    fun `#getActiveModules should return modules correctly when access level is finance for micro`(): Unit = runBlocking {
        val companyTraits = CompanyTraits(null, "123456", UUID.randomUUID() )
        val modules = service.getActiveModules(companyTraits, CompanyStaffAccessLevel.FINANCE)

        assertThat(modules).isNotEmpty
        assertThat(modules).containsOnly(Modules.INVOICES_MODULE)
    }

    @Test
    fun `#getActiveModules should return modules correctly when access level is finance for MLA`(): Unit = runBlocking {
        val companyTraits = CompanyTraits(CompanySize.MLA, "123456", UUID.randomUUID() )
        val modules = service.getActiveModules(companyTraits, CompanyStaffAccessLevel.FINANCE)

        assertThat(modules).isNotEmpty
        assertThat(modules).containsOnly(
            Modules.INVOICES_MODULE,
            Modules.PRODUCTS_MODULE,
        )
    }

    @Test
    fun `#getActiveModules should return modules correctly when access level is view`(): Unit = runBlocking {
        val companyTraits = CompanyTraits(CompanySize.MLA, "123456", UUID.randomUUID() )
        val modules = service.getActiveModules(companyTraits, CompanyStaffAccessLevel.VIEW)

        assertThat(modules).isNotEmpty()
        assertThat(modules).containsOnly(
            Modules.INVOICES_MODULE_READONLY,
            Modules.BENEFICIARIES_MODULE_READONLY,
            Modules.PRODUCTS_MODULE_READONLY,
            Modules.DATA_MODULE_READONLY,
            Modules.DATA_MODULE_DEMOGRAPHICS_TAB_READONLY,
            Modules.DATA_MODULE_MAGENTA_TAB_READONLY,
            Modules.DATA_MODULE_JOURNEY_TAB_READONLY,
            Modules.BENEFICIARIES_ADD_READONLY,
            Modules.BENEFICIARIES_CANCEL_READONLY,
            Modules.BENEFICIARIES_CHANGE_PRODUCT_READONLY,
            Modules.SETTINGS_MODULE_READONLY,
        )
    }

    @Test
    fun `#getActiveModules should return modules correctly when access level is view when it is not all read only modules`(): Unit = runBlocking {
        val companyTraits = CompanyTraits(CompanySize.SMALL, "123456", UUID.randomUUID() )
        val modules = service.getActiveModules(companyTraits, CompanyStaffAccessLevel.VIEW)

        assertThat(modules).isNotEmpty()
        assertThat(modules).containsOnly(
            Modules.INVOICES_MODULE_READONLY,
            Modules.BENEFICIARIES_MODULE_READONLY,
            Modules.PRODUCTS_MODULE_READONLY,
            Modules.DATA_MODULE_READONLY,
            Modules.DATA_MODULE_DEMOGRAPHICS_TAB_READONLY,
            Modules.DATA_MODULE_MAGENTA_TAB_READONLY,
            Modules.BENEFICIARIES_ADD_READONLY,
            Modules.BENEFICIARIES_CANCEL_READONLY,
            Modules.BENEFICIARIES_CHANGE_PRODUCT_READONLY,
            Modules.SETTINGS_MODULE_READONLY,
        )
    }
}
