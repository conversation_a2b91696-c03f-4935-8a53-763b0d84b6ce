package br.com.alice.data.layer.pipelines.subjects.builders

import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.core.Subject
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.AppointmentScheduleModel
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthcareTeamModel
import br.com.alice.data.layer.models.PersonClinicalAccount
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.pipelines.subjects.ExternalHealthProfessionalSubject
import br.com.alice.data.layer.pipelines.subjects.StaffSubject
import br.com.alice.data.layer.services.AppointmentScheduleModelDataService
import br.com.alice.data.layer.services.HealthPlanTaskDataService
import br.com.alice.data.layer.services.HealthcareTeamModelDataService
import br.com.alice.data.layer.services.PersonClinicalAccountDataService
import br.com.alice.data.layer.tables.AppointmentScheduleTable
import br.com.alice.data.layer.tables.HealthPlanTaskTable
import br.com.alice.data.layer.tables.HealthcareTeamTable
import br.com.alice.data.layer.tables.PersonClinicalAccountTable
import br.com.alice.data.layer.tables.Table
import java.util.UUID

internal class BatchExternalHealthProfessionalSubjectBuilder(
    private val factory: DatabasePipelineFactory,
    private val subject: StaffSubject,
) {

    suspend fun <M> build(resources: List<M>): List<Pair<M, Subject>> {
        val membersInPortfolio = membersInPortfolio(resources)
        return membersInPortfolio.mapValues { entry ->
            buildSubject(subject, entry.value)
        }.toList()
    }

    private suspend fun <M> membersInPortfolio(resources: List<M>): Map<M, Boolean> {
        val testPeople = resources.filter {
            it is PersonModel && it.isTest
        }.associateBy({ it }, { true })
        val notPersonResources = resources.filter {
            !(it is PersonModel || it is PersonReference)
        }.associateBy({ it }, { false })

        val peopleIds = resources.filter {
            it !in testPeople && it !in notPersonResources
        }.map {
            when (it) {
                is PersonReference -> it.personId
                is PersonModel -> it.id
                else -> PersonId()// should never happen
            }
        }

        val portfolioSet = mutableSetOf<PersonId>()
        portfolioSet.addAll(checkInHealthCareTeam(subject.id, peopleIds))
        portfolioSet.addAll(checkInReferrals(subject.id, peopleIds))
        portfolioSet.addAll(checkInAppointmentScheduling(subject.id, peopleIds))

        val peopleInPortfolio = resources.associateBy({ it }, {
            when (it) {
                is PersonReference -> it.personId in portfolioSet
                is PersonModel -> it.id in portfolioSet
                else -> false
            }
        })

        //order is important, as test people may not be in portfolio
        return notPersonResources + peopleInPortfolio + testPeople
    }

    private suspend fun checkInHealthCareTeam(staffId: UUID, peopleIds: List<PersonId>): List<PersonId> {
        val personClinicalAccounts = getPersonClinicalAccounts(peopleIds)

        return peopleIds.filter { personId ->
            val personClinicalAccount = personClinicalAccounts.find { it.personId == personId } ?: return@filter false
            if (personClinicalAccount.multiStaffIds.contains(staffId)) true
            else getHealthcareTeam(personClinicalAccount.healthcareTeamId)?.staffIds?.contains(staffId) == true
        }
    }

    private suspend fun getPersonClinicalAccounts(peopleIds: List<PersonId>): List<PersonClinicalAccount> =
        getPipeline<PersonClinicalAccount, PersonClinicalAccountTable>()
            .findByQuery(
                Query(
                    where = PersonClinicalAccountDataService.PersonIdField().inList(peopleIds)
                )
            ).get()

    private suspend fun getHealthcareTeam(healthCareTeamId: UUID): HealthcareTeamModel? =
        getPipeline<HealthcareTeamModel, HealthcareTeamTable>()
            .findByQuery(
                Query(
                    where = HealthcareTeamModelDataService.Id().eq(healthCareTeamId)
                )
            ).get().firstOrNull()

    private suspend fun checkInReferrals(staffId: UUID, peopleIds: List<PersonId>): List<PersonId> {
        val healthPlanTasks = getHealthPlanTasks(staffId, peopleIds)
        return healthPlanTasks.map { it.personId }.intersect(peopleIds.toSet()).toList()
    }

    private suspend fun getHealthPlanTasks(
        staffId: UUID,
        peopleIds: List<PersonId>,
    ) = getPipeline<HealthPlanTask, HealthPlanTaskTable>()
        .findByQuery(
            Query(
                where = HealthPlanTaskDataService.HealthSpecialistIdField().eq(staffId.toString())
                        and HealthPlanTaskDataService.PersonIdField().inList(peopleIds)
            )
        ).get()

    private suspend fun checkInAppointmentScheduling(staffId: UUID, peopleIds: List<PersonId>): List<PersonId> {
        val appointmentSchedules = getAppointmentSchedules(peopleIds, staffId)
        return appointmentSchedules.map { it.personId }.intersect(peopleIds.toSet()).toList()
    }

    private suspend fun getAppointmentSchedules(
        peopleIds: List<PersonId>,
        staffId: UUID,
    ) = getPipeline<AppointmentScheduleModel, AppointmentScheduleTable>()
        .findByQuery(
            Query(
                where =
                AppointmentScheduleModelDataService.PersonIdField().inList(peopleIds)
                        and AppointmentScheduleModelDataService.StaffId().eq(staffId)
            )
        ).get()


    private fun buildSubject(subject: StaffSubject, memberInPortfolio: Boolean) =
        ExternalHealthProfessionalSubject(
            id = subject.id,
            role = subject.role,
            memberInPortfolio = memberInPortfolio
        )

    private inline fun <reified M : Model, reified T : Table<T>> getPipeline() =
        factory.get<M, T>(withAuthorizationPipeline = false)

}
