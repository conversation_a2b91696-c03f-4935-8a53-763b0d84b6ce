package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.converters.ClinicalStaffResponseConverter
import br.com.alice.api.ops.models.ClinicalStaffWithAddresses
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.staff.client.HealthProfessionalService
import io.ktor.http.HttpHeaders.ContentRange
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.http.Parameters
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class ClinicalStaffController(
    private val healthProfessionalService: HealthProfessionalService,
) : Controller() {
    private suspend fun getByRange(range: IntRange) = healthProfessionalService.getSpecialistsByRange(range)

    suspend fun getClinicalStaffs(queryParams: Parameters) = coroutineScope {
        val range = parseRange(queryParams)
        val filterQuery = parseFilter(queryParams)

        logger.info(
            "Searching for clinical staff",
            "range" to range,
            "filters" to filterQuery
        )

        return@coroutineScope filterQuery?.let { filter ->
            val namePrefix = filter["q"]?.toString()
            val statusFilter = filter["status"]?.toString()

            val specialtyId = filter["specialty"]
            val staffIds = filter["id"]

            when {
                specialtyId != null -> {
                    val professionals = healthProfessionalService.findActivesByInternalAndExternalSpecialties(
                        listOf(specialtyId.toString().toUUID())
                    ).get()

                    createResponse(professionals, professionals.size)
                }

                staffIds != null -> {
                    val uuids = (staffIds as List<String>).map { it.toUUID() }

                    val professionalsDef = async { healthProfessionalService.getByStaffIds(uuids).get() }
                    val totalOfProfessionalsDef = async {
                        healthProfessionalService.countByNameAndId(ids = uuids).get()
                    }

                    val professionals = professionalsDef.await()
                    val totalOfProfessionals = totalOfProfessionalsDef.await()

                    createResponse(professionals, totalOfProfessionals)
                }

                statusFilter == null -> {
                    val professionalsDef = async {
                        healthProfessionalService.searchByNameAndIdWithRange(
                            range,
                            namePrefix
                        ).get()
                    }
                    val totalOfProfessionalsDef = async {
                        healthProfessionalService.countByNameAndId(
                            namePrefix
                        ).get()
                    }

                    val professionals = professionalsDef.await()
                    val totalOfProfessionals = totalOfProfessionalsDef.await()

                    createResponse(professionals, totalOfProfessionals)
                }

                else -> {
                    val professionalsDef = async {
                        healthProfessionalService.getByFilterAndRange(
                            namePrefix ?: "",
                            SpecialistStatus.valueOf(statusFilter),
                            range
                        ).get()
                    }

                    val totalOfProfessionalsDef = async {
                        healthProfessionalService.countByFilter(
                            namePrefix ?: "",
                            SpecialistStatus.valueOf(statusFilter)
                        ).get()
                    }

                    val professionals = professionalsDef.await()
                    val totalOfProfessionals = totalOfProfessionalsDef.await()

                    createResponse(professionals, totalOfProfessionals)
                }
            }
        } ?: run {
            val allElements = getByRange(range).get()
            val totalCount = count().get()

            createResponse(allElements, totalCount)
        }
    }

    private fun createResponse(professionals: List<HealthProfessional>, totalCount: Int): Response {
        val response = professionals.map { formatResponse(it) }
        return Response(OK, response, mapOf(ContentRange to totalCount.toString()))
    }

    private fun formatResponse(item: HealthProfessional) =
        ClinicalStaffResponseConverter.convert(
            buildProfessionalWithoutAddress(item)
        )

    private suspend fun count() = healthProfessionalService.countAllSpecialists()

    private fun buildProfessionalWithoutAddress(healthProfessional: HealthProfessional): ClinicalStaffWithAddresses {
        return ClinicalStaffWithAddresses(
            healthProfessional = healthProfessional
        )
    }
}
