package br.com.alice.moneyin.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.moneyin.models.InvoicePaymentWithPortalUrl
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface PortalUrlGeneratorService : Service {
    override val namespace get() = "money_in"
    override val serviceName get() = "portal_url_generator"

    suspend fun mountPortalUrl(invoicePaymentId: UUID): Result<String, Throwable>

    suspend fun mountPortalUrlForInvoicePayments(
        invoicePayments: List<InvoicePayment>
    ): Result<List<InvoicePaymentWithPortalUrl>, Throwable>
}
