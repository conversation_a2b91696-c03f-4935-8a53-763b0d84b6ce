package br.com.alice.hr.core.services

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.core.exceptions.InternalServiceErrorException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.hr.core.clients.interfaces.AmazonS3Client
import br.com.alice.hr.core.ServiceConfig
import br.com.alice.hr.core.model.DependentTransport
import br.com.alice.hr.core.model.TemplateSheetResult
import br.com.alice.hr.core.services.useCases.CreateBeneficiaryDependentUseCase
import br.com.alice.hr.core.services.useCases.GetBeneficiaryRelationTypeOptionsUseCase
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals

class HrBeneficiaryServiceImplTest {
    private val beneficiaryService: BeneficiaryService = mockk()
    private val companyService: CompanyService = mockk()
    private val companySubContractService: CompanySubContractService = mockk()
    private val getBeneficiaryRelationTypeOptionsUseCase: GetBeneficiaryRelationTypeOptionsUseCase = mockk()
    private val createBeneficiaryDependentUseCase: CreateBeneficiaryDependentUseCase = mockk()
    private val amazonS3Client: AmazonS3Client = mockk()
    private val hrBeneficiaryService = HrBeneficiaryServiceImpl(
        beneficiaryService,
        companyService,
        companySubContractService,
        getBeneficiaryRelationTypeOptionsUseCase,
        createBeneficiaryDependentUseCase,
        amazonS3Client
    )

    private val company = TestModelFactory.buildCompany()
    private val subContract = TestModelFactory.buildCompanySubContract(companyId = company.id)
    private val relationTypes = listOf(ParentBeneficiaryRelationType.CHILD)

    @BeforeEach
    fun setup() {
        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { companySubContractService.get(subContract.id) } returns subContract.success()
        coEvery { getBeneficiaryRelationTypeOptionsUseCase.run(company, subContract) } returns relationTypes.success()
    }

    @Test
    fun`#getRelationTypes should return relation types`() = runBlocking {
        val result = hrBeneficiaryService.getRelationTypes(company.id, subContract.id).get()

        assertEquals(relationTypes, result)

        coVerifyOnce { getBeneficiaryRelationTypeOptionsUseCase.run(any(), any()) }
    }

    private val address = TestModelFactory.buildAddress()
    private val beneficiary = TestModelFactory.buildBeneficiary()

    private val dependent = DependentTransport(
        firstName = "John",
        lastName = "Doe",
        mothersName = "Jane Doe",
        nationalId = "1234567890",
        email = "<EMAIL>",
        sex = Sex.MALE,
        birthDate = LocalDateTime.now(),
        phoneNumber = "1234567890",
        activatedAt = LocalDateTime.now(),
        address = address,
        productId = UUID.randomUUID(),
        parentBeneficiary = UUID.randomUUID(),
        parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
        parentBeneficiaryRelatedAt = LocalDateTime.now(),
        cnpj = "1234567890",
        subcontractId = subContract.id
    )

    @Test
    fun `#createDependent should create dependent beneficiary`() = runBlocking {
        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { companySubContractService.get(subContract.id) } returns subContract.success()
        coEvery { getBeneficiaryRelationTypeOptionsUseCase.run(company, subContract) } returns relationTypes.success()
        coEvery { createBeneficiaryDependentUseCase.run(dependent, company, subContract, relationTypes) } returns beneficiary.success()

        val result = hrBeneficiaryService.createDependent(dependent, company.id)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { companySubContractService.get(any()) }
        coVerifyOnce { getBeneficiaryRelationTypeOptionsUseCase.run(any(), any()) }
        coVerifyOnce { createBeneficiaryDependentUseCase.run(any(), any(), any(), any()) }
    }

    @Test
    fun `#createDependent should return exeception when companyService returns failure`() = runBlocking {
        coEvery { companyService.get(company.id) } returns Exception().failure()

        val result = hrBeneficiaryService.createDependent(dependent, company.id)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
    }

    @Test
    fun `#createDependent should return exeception when companySubContractService returns failure`() = runBlocking {
        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { companySubContractService.get(subContract.id) } returns Exception().failure()

        val result = hrBeneficiaryService.createDependent(dependent, company.id)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { companySubContractService.get(any()) }
    }

    @Test
    fun `#createDependent should return exeception when getBeneficiaryRelationTypeOptionsUseCase returns failure`() = runBlocking {
        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { companySubContractService.get(subContract.id) } returns subContract.success()
        coEvery { getBeneficiaryRelationTypeOptionsUseCase.run(company, subContract) } returns Exception().failure()

        val result = hrBeneficiaryService.createDependent(dependent, company.id)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { companySubContractService.get(any()) }
        coVerifyOnce { getBeneficiaryRelationTypeOptionsUseCase.run(any(), any()) }
    }

    @Test
    fun `#createDependent should return exeception when createBeneficiaryDependentUseCase returns failure`() = runBlocking {
        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { companySubContractService.get(subContract.id) } returns subContract.success()
        coEvery { getBeneficiaryRelationTypeOptionsUseCase.run(company, subContract) } returns relationTypes.success()
        coEvery { createBeneficiaryDependentUseCase.run(dependent, company, subContract, relationTypes) } returns Exception().failure()

        val result = hrBeneficiaryService.createDependent(dependent, company.id)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { companySubContractService.get(any()) }
        coVerifyOnce { getBeneficiaryRelationTypeOptionsUseCase.run(any(), any()) }
        coVerifyOnce { createBeneficiaryDependentUseCase.run(any(), any(), any(), any()) }
    }

    @Test
    fun `#getBeneficiarySheetTemplate should return template sheet result`(): Unit = runBlocking {
        coEvery { amazonS3Client.getS3ObjectContent(any()) } returns byteArrayOf(1, 2, 3).success()

        val result = hrBeneficiaryService.getBeneficiarySheetTemplate(company.id)

        assertThat(result).isSuccess()
    }

    @Test
    fun `#getBeneficiarySheetTemplate should return internal service error exception when amazonS3Client returns failure`(): Unit = runBlocking {
        coEvery { amazonS3Client.getS3ObjectContent(any()) } returns Exception().failure()

        val result = hrBeneficiaryService.getBeneficiarySheetTemplate(company.id)

        assertThat(result).isFailureOfType(InternalServiceErrorException::class)

        coVerifyOnce { amazonS3Client.getS3ObjectContent(any()) }
    }
}
