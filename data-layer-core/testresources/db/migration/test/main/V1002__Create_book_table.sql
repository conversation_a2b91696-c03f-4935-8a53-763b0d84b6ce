CREATE TABLE book(
    id uuid PRIMARY KEY,
    name text not null,
    author text not null,
    isbn text not null,
    age integer,
    available boolean not null,
    genres jsonb not null,
    nested jsonb,
    json_object_array jsonb not null default '[]'::jsonb,
    list_of_map jsonb not null default '[]'::jsonb,
    launch_date timestamp without time zone not null,
    search_tokens TSVECTOR not null,
    person_id uuid not null,
    some_uuid uuid,
    anonymized boolean NOT NULL DEFAULT false,
    version integer NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL
);
