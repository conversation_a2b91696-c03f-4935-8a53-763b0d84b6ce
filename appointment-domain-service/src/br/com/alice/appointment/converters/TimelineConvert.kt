package br.com.alice.appointment.converters

import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.AppointmentEvolution
import br.com.alice.data.layer.models.CounterReferral
import br.com.alice.data.layer.models.EinsteinAtendimento
import br.com.alice.data.layer.models.HaocProntoAtendimentoResult
import br.com.alice.data.layer.models.HaocSumarioDeAltaResult
import br.com.alice.data.layer.models.HealthForm
import br.com.alice.data.layer.models.HealthFormAnswerGroup
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.RefundCounterReferral
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.data.layer.models.Timeline
import br.com.alice.data.layer.models.TimelineEvolution
import br.com.alice.data.layer.models.TimelineReferenceModel
import br.com.alice.data.layer.models.TimelineStatus
import br.com.alice.data.layer.models.TimelineType
import java.time.LocalTime
import java.util.UUID

fun Appointment.toTimeline(specialtyId: UUID?) = Timeline(
    personId = personId,
    type = templateType?.let { TimelineType.CLINICAL_RECORD } ?: type.toTimeline(),
    title = name ?: type.description,
    status = status.toTimeline(discardedType),
    referencedModelId = id,
    referencedModelClass = TimelineReferenceModel.APPOINTMENT,
    referencedModelDate = createdAt,
    channelIds = getChannels(this),
    draftGroup = draftGroupStaffIds.orEmpty(),
    staffId = staffId,
    description = content,
    specialtyId = specialtyId
)

fun getChannels(appointment: Appointment): List<String> {
    val channelId = listOfNotNull(appointment.channelId)
    val channels = appointment.caseRecordDetails?.mapNotNull { it.channel?.id }.orEmpty()
    return listOf(channelId + channels).flatten().distinct()
}

fun AppointmentEvolution.toTimeline() = TimelineEvolution(
    id = id,
    staffId = staffId,
    description = description,
    createdAt = createdAt,
)

fun CounterReferral.toTimeline(staffId: UUID, specialty: MedicalSpecialty?) = Timeline(
    personId = this.personId,
    title = "Consulta com ${specialty?.name ?: "Especialista"}",
    type = TimelineType.APPOINTMENT_COMMUNITY,
    referencedModelId = this.id,
    staffId = staffId,
    specialtyId = specialty?.id,
    status = getStatus(this),
    referencedModelClass = TimelineReferenceModel.COUNTER_REFERRAL,
    referencedModelDate = this.createdAt,
    description = sanitizeDescription(this)
)

private fun sanitizeDescription(counterReferral: CounterReferral): String {
    val builder = StringBuilder()
    if (counterReferral.healthcareTeamOrientation.isNotBlank())
        builder.append(counterReferral.healthcareTeamOrientation).append(". ")
    builder.append(counterReferral.healthcareTeamOrientationDetail.orEmpty())
    return builder.toString().trim()
}

private fun getStatus(counterReferral: CounterReferral): TimelineStatus? {
    if (!counterReferral.isAppointmentOccurred()) {
        return TimelineStatus.DISCARDED_NO_SHOW
    }
    return counterReferral.notOccurredReason?.let { TimelineStatus.DISCARDED_NO_SHOW }
}

fun TertiaryIntentionTouchPoint.toTimeline() = Timeline(
    personId = personId,
    type = type!!.toTimeline(),
    title = type!!.description,
    referencedModelId = id,
    referencedModelClass = TimelineReferenceModel.TERTIARY_INTENTION_TOUCH_POINT,
    referencedModelDate = createdAt,
    staffId = staffId,
    providerUnitId = providerUnitId,
    description = reason ?: "",
)

fun HaocSumarioDeAltaResult.toTimeline(): Timeline {
    val type = TimelineType.THIRD_PARTY_APPOINTMENT_HOSPITALIZATION
    return Timeline(
        personId = personId,
        type = type,
        title = type.getTitle(),
        referencedModelId = id,
        referencedModelClass = TimelineReferenceModel.HAOC_SUMMARIO_DE_ALTA_RESULT,
        referencedModelDate = caracterizacaoAtendimento.dataInternacao,
        description = "HAOC - ${caracterizacaoAtendimento.local}",
    )
}

fun HaocProntoAtendimentoResult.toTimeline(): Timeline {
    val type = TimelineType.THIRD_PARTY_APPOINTMENT_EMERGENCY
    return Timeline(
        personId = personId,
        type = type,
        title = type.getTitle(),
        referencedModelId = id,
        referencedModelClass = TimelineReferenceModel.HAOC_PRONTO_ATENDIMENTO_RESULT,
        referencedModelDate = caracterizacaoAtendimento.dataChegada,
        description = "HAOC - ${caracterizacaoAtendimento.local}",
    )
}

fun EinsteinAtendimento.toTimeline(): Timeline {
    val type = TimelineType.THIRD_PARTY_APPOINTMENT_EMERGENCY
    return Timeline(
        personId = personId,
        type = type,
        title = type.getTitle(),
        referencedModelId = id,
        referencedModelClass = TimelineReferenceModel.EINSTEIN_ATENDIMENTO,
        referencedModelDate = dataAtendimento.toLocalDate().atTime(LocalTime.NOON),
        description = "EINSTEIN - $estabelecimento",
    )
}

fun HealthFormAnswerGroup.toTimeline(healthForm: HealthForm) = Timeline(
    personId = this.personId,
    title = healthForm.name,
    type = TimelineType.HEALTH_FORM_ANSWER_GROUP,
    referencedModelId = this.id,
    referencedModelClass = TimelineReferenceModel.HEALTH_FORM_ANSWER_GROUP,
    referencedModelDate = this.startedAt,
    description = ""
)

fun RefundCounterReferral.toTimeline() = Timeline(
    personId = this.personId,
    title = "Contrarreferência de Reembolso",
    type = TimelineType.REFUND_APPOINTMENT,
    referencedModelId = this.id,
    referencedModelClass = TimelineReferenceModel.REFUND_COUNTER_REFERRAL,
    referencedModelDate = this.createdAt,
    description = ""
)

private fun TimelineType.getTitle(): String? = when (this) {
    TimelineType.THIRD_PARTY_APPOINTMENT_EMERGENCY -> "Ida ao Pronto Atendimento"
    TimelineType.THIRD_PARTY_APPOINTMENT_HOSPITALIZATION -> "Internação"
    else -> null
}
