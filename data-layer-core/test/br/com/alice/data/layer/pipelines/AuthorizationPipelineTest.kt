package br.com.alice.data.layer.pipelines

import br.com.alice.authentication.Claims
import br.com.alice.authentication.RootService
import br.com.alice.common.core.Subject
import br.com.alice.common.core.exceptions.AccessForbiddenException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.data.layer.authorization.Authorization
import br.com.alice.data.layer.authorization.AuthorizationRequest
import br.com.alice.data.layer.authorization.AuthorizationService
import br.com.alice.data.layer.authorization.BatchAuthorizationRequest
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Book
import br.com.alice.data.layer.pipelines.context.ContextService
import br.com.alice.data.layer.pipelines.subjects.StaffSubject
import br.com.alice.data.layer.pipelines.subjects.SubjectBuilder
import br.com.alice.data.layer.services.AnonymizerService
import br.com.alice.data.layer.tables.BookTable
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class AuthorizationPipelineTest {

    private val modelClass = Book::class
    private val tableClass = BookTable::class
    private val databasePipeline: DatabasePipeline<Book> = mockk()
    private val authorizationService: AuthorizationService = mockk()
    private val contextService: ContextService = mockk()
    private val factory: DatabasePipelineFactory = mockk()

    private val authorizationPipeline = AuthorizationPipeline(
        modelClass = modelClass,
        tableClass = tableClass,
        databasePipeline = databasePipeline,
        authorizationService = authorizationService,
        contextService = contextService,
        onlyLog = false,
        factory = factory
    )

    private val book = TestModelFactory.buildBook()
    private val bookId = book.id

    private val baseSubject: Subject = mockk()
    private val subject: Subject = mockk()

    private val claims: Claims = mockk()
    private val rootService: RootService = mockk()

    private val authorizationTrue = Authorization(granted = true, "granted true")
    private val authorizationFalse = Authorization(granted = false, "granted false")

    @BeforeTest
    fun mocks() {
        clearAllMocks()
        mockkObject(SubjectBuilder)
    }

    @AfterTest
    fun confirmMocks() = confirmVerified(
        databasePipeline,
        authorizationService,
        contextService,
        factory,
        SubjectBuilder
    )

    @Nested
    inner class GetTest {
        @Test
        fun `#get returns model when subject is authorized`() = runBlocking {
            coEvery { databasePipeline.get(bookId) } returns book.success()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, View, book, rootService, modelClass))
            } returns authorizationTrue

            val result = authorizationPipeline.get(bookId)
            assertThat(result).isSuccessWithData(book)

            coVerifyOnce { databasePipeline.get(any()) }
            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
        }

        @Test
        fun `#get returns anonymized model when subject is authorized`() = runBlocking {
            val subject: StaffSubject = mockk()

            mockkObject(AnonymizerService)

            coEvery { databasePipeline.get(bookId) } returns book.success()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns subject
            coEvery { SubjectBuilder.enrichSubject(factory, subject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, View, book, rootService, modelClass))
            } returns authorizationTrue

            coEvery { subject.viewAnonymized() } returns true
            coEvery { AnonymizerService.anonymizeModel(book) } returns book

            val result = authorizationPipeline.get(bookId)
            assertThat(result).isSuccessWithData(book)

            coVerifyOnce { databasePipeline.get(any()) }
            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { AnonymizerService.anonymizeModel(any()) }
        }

        @Test
        fun `#get returns error when subject is not authorized`() = runBlocking {
            coEvery { databasePipeline.get(bookId) } returns book.success()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, View, book, rootService, modelClass))
            } returns authorizationFalse

            val result = authorizationPipeline.get(bookId)
            assertThat(result).fails()
                .withMessage("Access forbidden: granted false")
                .ofType(AccessForbiddenException::class)

            coVerifyOnce { databasePipeline.get(any()) }
            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
        }

        @Test
        fun `#get returns error when get returns any error`() = runBlocking {
            coEvery { databasePipeline.get(bookId) } returns Exception("my exception").failure()

            val result = authorizationPipeline.get(bookId)
            assertThat(result).fails()
                .withMessage("my exception")
                .ofType(Exception::class)

            coVerifyOnce { databasePipeline.get(any()) }
        }
    }

    @Nested
    inner class AddTest {
        @Test
        fun `#add returns model when subject is authorized`() = runBlocking {
            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Create, book, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.add(book) } returns book.success()

            val result = authorizationPipeline.add(book)
            assertThat(result).isSuccessWithData(book)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.add(any()) }
        }

        @Test
        fun `#add returns error when subject is not authorized`() = runBlocking {
            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Create, book, rootService, modelClass))
            } returns authorizationFalse

            val result = authorizationPipeline.add(book)
            assertThat(result).fails()
                .withMessage("Access forbidden: granted false")
                .ofType(AccessForbiddenException::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerify { databasePipeline wasNot called }
        }

        @Test
        fun `#add returns error when subject cannot edit model`() = runBlocking {
            val subject: StaffSubject = mockk()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns subject
            coEvery { SubjectBuilder.enrichSubject(factory, subject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Create, book, rootService, modelClass))
            } returns authorizationFalse

            val result = authorizationPipeline.add(book)
            assertThat(result).fails()
                .withMessage("Access forbidden: granted false")
                .ofType(AccessForbiddenException::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerify { databasePipeline wasNot called }
        }

        @Test
        fun `#add returns error when add returns any error`() = runBlocking {
            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Create, book, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.add(book) } returns Exception("my exception").failure()

            val result = authorizationPipeline.add(book)
            assertThat(result).fails()
                .withMessage("my exception")
                .ofType(Exception::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.add(any()) }
        }
    }

    @Nested
    inner class UpdateTest {
        @Test
        fun `#update returns model when subject is authorized`() = runBlocking {
            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Update, book, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.update(book) } returns book.success()

            val result = authorizationPipeline.update(book)
            assertThat(result).isSuccessWithData(book)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.update(any()) }
        }

        @Test
        fun `#update returns error when subject is not authorized`() = runBlocking {
            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Update, book, rootService, modelClass))
            } returns authorizationFalse

            val result = authorizationPipeline.update(book)
            assertThat(result).fails()
                .withMessage("Access forbidden: granted false")
                .ofType(AccessForbiddenException::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerify { databasePipeline wasNot called }
        }

        @Test
        fun `#update returns error when subject cannot edit model`() = runBlocking {
            val subject: StaffSubject = mockk()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns subject
            coEvery { SubjectBuilder.enrichSubject(factory, subject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Update, book, rootService, modelClass))
            } returns authorizationFalse

            val result = authorizationPipeline.update(book)
            assertThat(result).fails()
                .withMessage("Access forbidden: granted false")
                .ofType(AccessForbiddenException::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerify { databasePipeline wasNot called }
        }

        @Test
        fun `#update returns error when add returns any error`() = runBlocking {
            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Update, book, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.update(book) } returns Exception("my exception").failure()

            val result = authorizationPipeline.update(book)
            assertThat(result).fails()
                .withMessage("my exception")
                .ofType(Exception::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.update(any()) }
        }
    }

    @Nested
    inner class SoftDeleteTest {
        @Test
        fun `#softDelete returns model when subject is authorized`() = runBlocking {
            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Delete, book, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.softDelete(book) } returns true.success()

            val result = authorizationPipeline.softDelete(book)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.softDelete(any()) }
        }

        @Test
        fun `#softDelete returns error when subject is not authorized`() = runBlocking {
            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Delete, book, rootService, modelClass))
            } returns authorizationFalse

            val result = authorizationPipeline.softDelete(book)
            assertThat(result).fails()
                .withMessage("Access forbidden: granted false")
                .ofType(AccessForbiddenException::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerify { databasePipeline wasNot called }
        }

        @Test
        fun `#softDelete returns error when subject cannot edit model`() = runBlocking {
            val subject: StaffSubject = mockk()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns subject
            coEvery { SubjectBuilder.enrichSubject(factory, subject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Delete, book, rootService, modelClass))
            } returns authorizationFalse

            val result = authorizationPipeline.softDelete(book)
            assertThat(result).fails()
                .withMessage("Access forbidden: granted false")
                .ofType(AccessForbiddenException::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerify { databasePipeline wasNot called }
        }

        @Test
        fun `#softDelete returns error when add returns any error`() = runBlocking {
            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Delete, book, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.softDelete(book) } returns Exception("my exception").failure()

            val result = authorizationPipeline.softDelete(book)
            assertThat(result).fails()
                .withMessage("my exception")
                .ofType(Exception::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.softDelete(any()) }
        }
    }

    @Nested
    inner class DeleteTest {
        @Test
        fun `#delete returns model when subject is authorized`() = runBlocking {
            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Delete, book, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.delete(book) } returns true.success()

            val result = authorizationPipeline.delete(book)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.delete(any()) }
        }

        @Test
        fun `#delete returns error when subject is not authorized`() = runBlocking {
            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Delete, book, rootService, modelClass))
            } returns authorizationFalse

            val result = authorizationPipeline.delete(book)
            assertThat(result).fails()
                .withMessage("Access forbidden: granted false")
                .ofType(AccessForbiddenException::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerify { databasePipeline wasNot called }
        }

        @Test
        fun `#delete returns error when subject cannot edit model`() = runBlocking {
            val subject: StaffSubject = mockk()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns subject
            coEvery { SubjectBuilder.enrichSubject(factory, subject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Delete, book, rootService, modelClass))
            } returns authorizationFalse

            val result = authorizationPipeline.delete(book)
            assertThat(result).fails()
                .withMessage("Access forbidden: granted false")
                .ofType(AccessForbiddenException::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerify { databasePipeline wasNot called }
        }

        @Test
        fun `#delete returns error when add returns any error`() = runBlocking {
            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Delete, book, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.delete(book) } returns Exception("my exception").failure()

            val result = authorizationPipeline.delete(book)
            assertThat(result).fails()
                .withMessage("my exception")
                .ofType(Exception::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.delete(any()) }
        }
    }

    @Nested
    inner class FindByQueryTest {
        @Test
        fun `#findByQuery returns models when subject is authorized`() = runBlocking {
            val query = Query()
            val books = listOf(book, book)
            val subjects = listOf(book to subject, book to subject)

            coEvery { databasePipeline.findByQuery(query) } returns books.success()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubjects(factory, baseSubject, books, rootService) } returns subjects

            coEvery {
                authorizationService.authorizeBatch(
                    BatchAuthorizationRequest(
                        requests = listOf(
                            AuthorizationRequest(subject, View, book, rootService, modelClass),
                            AuthorizationRequest(subject, View, book, rootService, modelClass)
                        ),
                        rootService = rootService
                    )
                )
            } returns listOf(book to authorizationTrue, book to authorizationTrue)

            val result = authorizationPipeline.findByQuery(query)
            assertThat(result).isSuccessWithData(books)

            coVerifyOnce { databasePipeline.findByQuery(any()) }
            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubjects(any(), any(), any<List<Book>>(), any()) }
            coVerifyOnce { authorizationService.authorizeBatch(any()) }
        }

        @Test
        fun `#findByQuery returns anonymized models when subject is authorized`() = runBlocking {
            val subject: StaffSubject = mockk()
            val query = Query()
            val books = listOf(book, book)
            val subjects = listOf(book to subject, book to subject)

            mockkObject(AnonymizerService)

            coEvery { databasePipeline.findByQuery(query) } returns books.success()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubjects(factory, baseSubject, books, rootService) } returns subjects

            coEvery {
                authorizationService.authorizeBatch(
                    BatchAuthorizationRequest(
                        requests = listOf(
                            AuthorizationRequest(subject, View, book, rootService, modelClass),
                            AuthorizationRequest(subject, View, book, rootService, modelClass)
                        ),
                        rootService = rootService
                    )
                )
            } returns listOf(book to authorizationTrue, book to authorizationTrue)

            coEvery { subject.viewAnonymized() } returns true
            coEvery { AnonymizerService.anonymizeModel(book) } returns book

            val result = authorizationPipeline.findByQuery(query)
            assertThat(result).isSuccessWithData(books)

            coVerifyOnce { databasePipeline.findByQuery(any()) }
            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubjects(any(), any(), any<List<Book>>(), any()) }
            coVerifyOnce { authorizationService.authorizeBatch(any()) }
            coVerify(exactly = 2) { AnonymizerService.anonymizeModel(any()) }
        }

        @Test
        fun `#findByQuery returns error when subject is not authorized for some model`() = runBlocking {
            val query = Query()
            val books = listOf(book, book)
            val subjects = listOf(book to subject, book to subject)

            coEvery { databasePipeline.findByQuery(query) } returns books.success()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubjects(factory, baseSubject, books, rootService) } returns subjects

            coEvery {
                authorizationService.authorizeBatch(
                    BatchAuthorizationRequest(
                        requests = listOf(
                            AuthorizationRequest(subject, View, book, rootService, modelClass),
                            AuthorizationRequest(subject, View, book, rootService, modelClass)
                        ),
                        rootService = rootService
                    )
                )
            } returns listOf(book to authorizationTrue, book to authorizationFalse)

            val result = authorizationPipeline.findByQuery(query)
            assertThat(result).fails()
                .withMessage("Access forbidden: granted false")
                .ofType(AccessForbiddenException::class)

            coVerifyOnce { databasePipeline.findByQuery(any()) }
            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubjects(any(), any(), any<List<Book>>(), any()) }
            coVerifyOnce { authorizationService.authorizeBatch(any()) }
        }

        @Test
        fun `#findByQuery returns error when find returns error`() = runBlocking {
            val query = Query()

            coEvery { databasePipeline.findByQuery(query) } returns Exception("my exception").failure()

            val result = authorizationPipeline.findByQuery(query)
            assertThat(result).fails()
                .withMessage("my exception")
                .ofType(Exception::class)

            coVerifyOnce { databasePipeline.findByQuery(any()) }
        }
    }

    @Nested
    inner class FindAuthorizedByQueryTest {
        @Test
        fun `#findAuthorizedByQuery returns models when subject is authorized`() = runBlocking {
            val query = Query()
            val books = listOf(book, book)
            val subjects = listOf(book to subject, book to subject)

            coEvery { databasePipeline.findByQuery(query) } returns books.success()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubjects(factory, baseSubject, books, rootService) } returns subjects

            coEvery {
                authorizationService.authorizeBatch(
                    BatchAuthorizationRequest(
                        requests = listOf(
                            AuthorizationRequest(subject, View, book, rootService, modelClass),
                            AuthorizationRequest(subject, View, book, rootService, modelClass)
                        ),
                        rootService = rootService
                    )
                )
            } returns listOf(book to authorizationTrue, book to authorizationTrue)

            val result = authorizationPipeline.findAuthorizedByQuery(query)
            assertThat(result).isSuccessWithData(books)

            coVerifyOnce { databasePipeline.findByQuery(any()) }
            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubjects(any(), any(), any<List<Book>>(), any()) }
            coVerifyOnce { authorizationService.authorizeBatch(any()) }
        }

        @Test
        fun `#findAuthorizedByQuery returns anonymized models when subject is authorized`() = runBlocking {
            val subject: StaffSubject = mockk()
            val query = Query()
            val books = listOf(book, book)
            val subjects = listOf(book to subject, book to subject)

            mockkObject(AnonymizerService)

            coEvery { databasePipeline.findByQuery(query) } returns books.success()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubjects(factory, baseSubject, books, rootService) } returns subjects

            coEvery {
                authorizationService.authorizeBatch(
                    BatchAuthorizationRequest(
                        requests = listOf(
                            AuthorizationRequest(subject, View, book, rootService, modelClass),
                            AuthorizationRequest(subject, View, book, rootService, modelClass)
                        ),
                        rootService = rootService
                    )
                )
            } returns listOf(book to authorizationTrue, book to authorizationTrue)

            coEvery { subject.viewAnonymized() } returns true
            coEvery { AnonymizerService.anonymizeModel(book) } returns book

            val result = authorizationPipeline.findAuthorizedByQuery(query)
            assertThat(result).isSuccessWithData(books)

            coVerifyOnce { databasePipeline.findByQuery(any()) }
            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubjects(any(), any(), any<List<Book>>(), any()) }
            coVerifyOnce { authorizationService.authorizeBatch(any()) }
            coVerify(exactly = 2) { AnonymizerService.anonymizeModel(any()) }
        }

        @Test
        fun `#findAuthorizedByQuery returns only authorized models`() = runBlocking {
            val query = Query()
            val books = listOf(book, book)
            val subjects = listOf(book to subject, book to subject)

            coEvery { databasePipeline.findByQuery(query) } returns books.success()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubjects(factory, baseSubject, books, rootService) } returns subjects

            coEvery {
                authorizationService.authorizeBatch(
                    BatchAuthorizationRequest(
                        requests = listOf(
                            AuthorizationRequest(subject, View, book, rootService, modelClass),
                            AuthorizationRequest(subject, View, book, rootService, modelClass)
                        ),
                        rootService = rootService
                    )
                )
            } returns listOf(book to authorizationTrue, book to authorizationFalse)

            val result = authorizationPipeline.findAuthorizedByQuery(query)
            assertThat(result).isSuccessWithData(listOf(book))

            coVerifyOnce { databasePipeline.findByQuery(any()) }
            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubjects(any(), any(), any<List<Book>>(), any()) }
            coVerifyOnce { authorizationService.authorizeBatch(any()) }
        }

        @Test
        fun `#findAuthorizedByQuery returns error when find returns error`() = runBlocking {
            val query = Query()

            coEvery { databasePipeline.findByQuery(query) } returns Exception("my exception").failure()

            val result = authorizationPipeline.findAuthorizedByQuery(query)
            assertThat(result).fails()
                .withMessage("my exception")
                .ofType(Exception::class)

            coVerifyOnce { databasePipeline.findByQuery(any()) }
        }
    }

    @Nested
    inner class CountByQueryTest {
        @Test
        fun `#countByQuery returns count when subject is authorized`() = runBlocking {
            val query = Query()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, null, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Count, null, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.countByQuery(query) } returns 1.success()

            val result = authorizationPipeline.countByQuery(query)
            assertThat(result).isSuccessWithData(1)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.countByQuery(any()) }
        }

        @Test
        fun `#countByQuery returns error when subject is not authorized`() = runBlocking {
            val query = Query()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, null, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Count, null, rootService, modelClass))
            } returns authorizationFalse

            val result = authorizationPipeline.countByQuery(query)
            assertThat(result).fails()
                .withMessage("Access forbidden: granted false")
                .ofType(AccessForbiddenException::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerify { databasePipeline wasNot called }
        }

        @Test
        fun `#countByQuery returns error when add returns any error`() = runBlocking {
            val query = Query()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, null, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Count, null, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.countByQuery(query) } returns Exception("my exception").failure()

            val result = authorizationPipeline.countByQuery(query)
            assertThat(result).fails()
                .withMessage("my exception")
                .ofType(Exception::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.countByQuery(any()) }
        }
    }

    @Nested
    inner class CountGroupedByQueryTest {

        @Test
        fun `#countGroupedByQuery returns count when subject is authorized`() = runBlocking {
            val query = Query()
            val counts = listOf(CountByValues(values = listOf("id"), count = 1))

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, null, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Count, null, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.countGroupedByQuery(query) } returns counts.success()

            val result = authorizationPipeline.countGroupedByQuery(query)
            assertThat(result).isSuccessWithData(counts)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.countGroupedByQuery(any()) }
        }

        @Test
        fun `#countGroupedByQuery returns error when subject is not authorized`() = runBlocking {
            val query = Query()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, null, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Count, null, rootService, modelClass))
            } returns authorizationFalse

            val result = authorizationPipeline.countGroupedByQuery(query)
            assertThat(result).fails()
                .withMessage("Access forbidden: granted false")
                .ofType(AccessForbiddenException::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerify { databasePipeline wasNot called }
        }

        @Test
        fun `#countGroupedByQuery returns error when add returns any error`() = runBlocking {
            val query = Query()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, null, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Count, null, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.countGroupedByQuery(query) } returns Exception("my exception").failure()

            val result = authorizationPipeline.countGroupedByQuery(query)
            assertThat(result).fails()
                .withMessage("my exception")
                .ofType(Exception::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.countGroupedByQuery(any()) }
        }
    }

    @Nested
    inner class ExistsByQueryTest {

        @Test
        fun `#existsByQuery returns count when subject is authorized`() = runBlocking {
            val query = Query()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, null, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Count, null, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.existsByQuery(query) } returns true.success()

            val result = authorizationPipeline.existsByQuery(query)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.existsByQuery(any()) }
        }

        @Test
        fun `#existsByQuery returns error when subject is not authorized`() = runBlocking {
            val query = Query()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, null, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Count, null, rootService, modelClass))
            } returns authorizationFalse

            val result = authorizationPipeline.existsByQuery(query)
            assertThat(result).fails()
                .withMessage("Access forbidden: granted false")
                .ofType(AccessForbiddenException::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerify { databasePipeline wasNot called }
        }

        @Test
        fun `#existsByQuery returns error when add returns any error`() = runBlocking {
            val query = Query()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, null, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Count, null, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.existsByQuery(query) } returns Exception("my exception").failure()

            val result = authorizationPipeline.existsByQuery(query)
            assertThat(result).fails()
                .withMessage("my exception")
                .ofType(Exception::class)

            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.existsByQuery(any()) }
        }
    }

    @Nested
    inner class AnonymizeTest {

        @Test
        fun `#anonymize returns true when subject is authorized`() = runBlocking {
            coEvery { databasePipeline.get(bookId) } returns book.success()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Update, book, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.anonymize(bookId) } returns true.success()

            val result = authorizationPipeline.anonymize(bookId)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { databasePipeline.get(any()) }
            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.anonymize(any()) }
        }

        @Test
        fun `#anonymize returns error when subject is not authorized`() = runBlocking {
            coEvery { databasePipeline.get(bookId) } returns book.success()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Update, book, rootService, modelClass))
            } returns authorizationFalse

            val result = authorizationPipeline.anonymize(bookId)
            assertThat(result).fails()
                .withMessage("Access forbidden: granted false")
                .ofType(AccessForbiddenException::class)

            coVerifyOnce { databasePipeline.get(any()) }
            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyNone { databasePipeline.anonymize(any()) }
        }

        @Test
        fun `#anonymize returns error when get returns any error`() = runBlocking {
            coEvery { databasePipeline.get(bookId) } returns Exception("my exception").failure()

            val result = authorizationPipeline.anonymize(bookId)
            assertThat(result).fails()
                .withMessage("my exception")
                .ofType(Exception::class)

            coVerifyOnce { databasePipeline.get(any()) }
        }

        @Test
        fun `#anonymize returns error when anonymize returns any error`() = runBlocking {
            coEvery { databasePipeline.get(bookId) } returns book.success()

            coEvery { contextService.getClaims() } returns claims
            coEvery { contextService.getRootService() } returns rootService

            coEvery { SubjectBuilder.subject(factory, claims) } returns baseSubject
            coEvery { SubjectBuilder.enrichSubject(factory, baseSubject, book, rootService) } returns subject

            coEvery {
                authorizationService.authorize(AuthorizationRequest(subject, Update, book, rootService, modelClass))
            } returns authorizationTrue

            coEvery { databasePipeline.anonymize(bookId) } returns Exception("my exception").failure()

            val result = authorizationPipeline.anonymize(bookId)
            assertThat(result).fails()
                .withMessage("my exception")
                .ofType(Exception::class)

            coVerifyOnce { databasePipeline.get(any()) }
            coVerifyOnce { contextService.getClaims() }
            coVerifyOnce { contextService.getRootService() }
            coVerifyOnce { SubjectBuilder.subject(any(), any()) }
            coVerifyOnce { SubjectBuilder.enrichSubject(any(), any(), any(), any()) }
            coVerifyOnce { authorizationService.authorize(any()) }
            coVerifyOnce { databasePipeline.anonymize(any()) }
        }
    }
}
