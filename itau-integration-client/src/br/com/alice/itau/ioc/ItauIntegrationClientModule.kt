package br.com.alice.itau.ioc

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.rfc.HttpInvoker
import br.com.alice.common.rfc.Invoker
import br.com.alice.itau.ItauIntegrationConfiguration
import br.com.alice.itau.SERVICE_NAME
import br.com.alice.itau.client.AcquirerItauPaymentService
import br.com.alice.itau.client.AcquirerItauPaymentServiceClient
import org.koin.core.qualifier.named
import org.koin.dsl.module

val ItauIntegrationClientModule = module(createdAtStart = true) {

    val baseUrl = ItauIntegrationConfiguration.baseUrl
    val invoker = HttpInvoker(DefaultHttpClient(timeoutInMillis = 5_000), "$baseUrl/rfc")

    single<Invoker>(named(SERVICE_NAME)) { invoker }
    single<AcquirerItauPaymentService> { AcquirerItauPaymentServiceClient(get(named(SERVICE_NAME))) }
}
