package br.com.alice.api.backoffice.routes

import br.com.alice.api.backoffice.routes.healthcareTeam.healthcareTeamAssociationRoutes
import br.com.alice.api.backoffice.routes.healthcareTeam.healthcareTeamRoutes
import br.com.alice.api.backoffice.routes.sales.salesAgentRoutes
import br.com.alice.api.backoffice.routes.sales.salesFirmRoutes
import br.com.alice.api.backoffice.routes.sales.salesFirmStaffRoutes
import br.com.alice.api.backoffice.routes.sales.siteAccreditedNetworkRoutes
import br.com.alice.api.backoffice.routes.sales.vicProductOptionRoutes
import io.ktor.server.plugins.swagger.swaggerUI
import io.ktor.server.routing.Routing

fun Routing.apiRoutes() {
    swaggerUI(path = "swagger", swaggerFile = "openapi/documentation.yaml")

    featureConfigRoutes()
    companyRoutes()
    authRoutes()
    salesAgentRoutes()
    salesFirmRoutes()
    salesFirmStaffRoutes()
    execIndicatorRoutes()
    vicProductOptionRoutes()
    healthcareTeamRoutes()
    siteAccreditedNetworkRoutes()
    structuredAddressRoutes()
    medicalSpecialtyRoutes()
    healthcareTeamAssociationRoutes()
    staffRoutes()
    healthProfessionalRouting()
    suggestedProcedureRouting()
    specialistEarningsRoutes()
}
