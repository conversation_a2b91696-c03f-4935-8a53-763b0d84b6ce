package br.com.alice.exec.indicator.client

import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.HealthSpecialistResourceBundle
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HealthSpecialistResourceBundleService : Service {
    override val namespace get() = "eita"
    override val serviceName get() = "health_specialist_resource_bundle"

    suspend fun get(id: UUID): Result<HealthSpecialistResourceBundle, Throwable>
    suspend fun add(model: HealthSpecialistResourceBundle): Result<HealthSpecialistResourceBundle, Throwable>
    suspend fun update(model: HealthSpecialistResourceBundle): Result<HealthSpecialistResourceBundle, Throwable>
    suspend fun delete(model: HealthSpecialistResourceBundle): Result<Boolean, Throwable>
    suspend fun findByIds(ids: List<UUID>): Result<List<HealthSpecialistResourceBundle>, Throwable>
    suspend fun findByCodes(codes: List<String>): Result<List<HealthSpecialistResourceBundle>, Throwable>
    suspend fun findByPrimaryTuss(primaryTuss: String): Result<List<HealthSpecialistResourceBundle>, Throwable>
    suspend fun findByPrimaryTussList(primaryTussList: List<String>): Result<List<HealthSpecialistResourceBundle>, Throwable>
    suspend fun search(query: String, limit: Int): Result<List<HealthSpecialistResourceBundle>, Throwable>
    suspend fun findByOldAliceCodes(aliceCodes: List<String>): Result<List<HealthSpecialistResourceBundle>, Throwable>
    suspend fun findByFilters(
        query: String? = null,
        range: IntRange? = null,
        ids: List<UUID>? = null,
        serviceTypes: List<HealthSpecialistResourceBundleServiceType>? = null,
    ): Result<List<HealthSpecialistResourceBundle>, Throwable>

    suspend fun countByFilters(
        query: String?,
        ids: List<UUID>? = null,
        serviceTypes: List<HealthSpecialistResourceBundleServiceType>? = null,
    ): Result<Int, Throwable>

    suspend fun findByResourceBundleFilters(filter: HealthSpecialistResourceBundleFilter): Result<HealthSpecialistResourceBundle, Throwable>

    suspend fun upsert(model: HealthSpecialistResourceBundle): Result<HealthSpecialistResourceBundle, Throwable>

    suspend fun findByCodesOrPrimaryTuss(codes: List<String>): Result<List<HealthSpecialistResourceBundle>, Throwable>

    suspend fun findBy(filter: Filter, range: IntRange? = null): Result<List<HealthSpecialistResourceBundle>, Throwable>

    suspend fun countBy(filter: Filter): Result<Int, Throwable>

    data class Filter(
        val ids: List<UUID>? = null,
        val searchToken: String? = null,
    ) {
        fun valid() =
            if (ids != null) Unit
            else if (searchToken != null) Unit
            else throw InvalidArgumentException(code = "empty_filters", message = "Filters cannot be empty")
    }
}

data class HealthSpecialistResourceBundleFilter(
    val primaryTuss: String,
    val secondaryResources: List<UUID>? = emptyList<UUID>(),
    val executionAmount: Int,
    val serviceType: HealthSpecialistResourceBundleServiceType,
    val executionEnvironment: HealthSpecialistProcedureExecutionEnvironment,
)
