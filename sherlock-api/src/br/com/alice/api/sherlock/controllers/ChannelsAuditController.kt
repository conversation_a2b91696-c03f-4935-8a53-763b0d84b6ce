package br.com.alice.api.sherlock.controllers

import br.com.alice.api.sherlock.model.ChannelByCSVQueryRequest
import br.com.alice.api.sherlock.model.ChannelQueryRequest
import br.com.alice.common.ErrorResponse
import br.com.alice.common.Response
import br.com.alice.common.foldResponse
import br.com.alice.sherlock.client.ChannelsAuditService
import br.com.alice.sherlock.client.ChannelsResultStaffService
import br.com.alice.staff.client.StaffService
import io.ktor.http.HttpStatusCode


class ChannelsAuditController(
    staffService: StaffService,
    private val channelsResultStaffService: ChannelsResultStaffService,
    private val channelsAuditService: ChannelsAuditService,
) : Staff<PERSON><PERSON>roller(staffService) {

    companion object {
        const val MAX_CHANNEL_BATCH_SIZE = 100
    }

    suspend fun channels(request: ChannelQueryRequest): Response {
        if (request.channelIds.isEmpty() || request.personInternalCode.isBlank() || request.reason.isBlank())
            return Response(HttpStatusCode.BadRequest, ErrorResponse("missing_params_values"))
        if (request.channelIds.size > MAX_CHANNEL_BATCH_SIZE)
            return Response(HttpStatusCode.BadRequest, ErrorResponse("too_many_params"))

        return channelsResultStaffService.addChannelsResult(
            staffId = currentStaffId(),
            personInternalCode = request.personInternalCode.trim(),
            reason = request.reason,
            channelIds = request.channelIds.map { it.trim() }
        ).foldResponse()
    }

    suspend fun channelsCSV(request: ChannelByCSVQueryRequest): Response {
        if (request.channelIds.isBlank() || request.reason.isBlank())
            return Response(HttpStatusCode.BadRequest, ErrorResponse("missing_params_values"))
        val channelIds = request.channelIds.split(",").map { it.trim() }
        if (channelIds.size > MAX_CHANNEL_BATCH_SIZE)
            return Response(HttpStatusCode.BadRequest, ErrorResponse("too_many_params"))

        return channelsResultStaffService.addChannelsResult(
            staffId = currentStaffId(),
            personInternalCode = null,
            reason = request.reason,
            channelIds = channelIds
        ).foldResponse()
    }

    suspend fun getChannels(personInternalCode: String): Response =
        channelsAuditService.getChannels(personInternalCode.trim(), false)
            .foldResponse()
}
