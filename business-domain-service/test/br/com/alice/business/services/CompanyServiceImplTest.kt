package br.com.alice.business.services

import br.com.alice.business.clients.CompanyExternalInformationResponse
import br.com.alice.business.clients.ReceitaFederalClient
import br.com.alice.business.converters.CompanyConverter.toBillingAccountableParty
import br.com.alice.business.converters.model.toModel
import br.com.alice.business.converters.model.toTransport
import br.com.alice.business.events.CompanyBillingAccountablePartyUpdatedEvent
import br.com.alice.business.events.CompanyUpsertedEvent
import br.com.alice.business.exceptions.InvalidDefaultProductIdException
import br.com.alice.business.exceptions.ProductIdNotOnAvailableProducts
import br.com.alice.business.logics.CompanyLogic
import br.com.alice.business.logics.CompanyLogic.validateDefaultProductId
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.data.layer.models.CompanyAddress
import br.com.alice.data.layer.models.CompanySize
import br.com.alice.data.layer.models.CompanyStatus
import br.com.alice.data.layer.models.ProductType
import br.com.alice.data.layer.services.CompanyModelDataService
import br.com.alice.moneyin.client.BillingAccountablePartyService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.spyk
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class CompanyServiceImplTest {

    private val companyDataService: CompanyModelDataService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val receitaFederalClient: ReceitaFederalClient = mockk()
    private val companyService =
        CompanyServiceImpl(
            companyDataService,
            billingAccountablePartyService,
            kafkaProducerService,
            receitaFederalClient
        )
    private val spyService = spyk(companyService)

    @BeforeTest
    fun setup() {
        mockkObject(CompanyLogic)
    }

    @AfterTest
    fun clear() {
        clearAllMocks()
        unmockkAll()
    }

    @Test
    fun `#findByName returns list of companies`() = runBlocking {
        val companyName = "Acme"
        val company = TestModelFactory.buildCompany().toModel()
        val companies = listOf(company)

        coEvery { companyDataService.find(any()) } returns companies

        val result = companyService.findByName(companyName)
        assertThat(result).isSuccessWithData(companies.map { it.toTransport() })

        coVerifyOnce {
            companyDataService.find(queryEq {
                where { this.name.eq(companyName) }
            })
        }
    }

    @Test
    fun `#findByCnpj returns a company`() = runBlocking {
        val companyCnpj = "00.000.000/0001-00"
        val company = TestModelFactory.buildCompany().toModel()

        coEvery { companyDataService.findOne(any()) } returns company

        val result = companyService.findByCnpj(companyCnpj)
        assertThat(result).isSuccessWithData(company.toTransport())

        coVerifyOnce {
            companyDataService.findOne(queryEq {
                where { this.cnpj.eq(companyCnpj) }
            })
        }
    }

    @Test
    fun `#findByCnpjs returns companies with given CNPJ`() = runBlocking {
        val companyCnpjs = listOf("cnpj-1", "cnpj-2")
        val companies = listOf(TestModelFactory.buildCompany().toModel())

        coEvery { companyDataService.find(any()) } returns companies

        val result = companyService.findByCnpjs(companyCnpjs)
        assertThat(result).isSuccessWithData(companies.map { it.toTransport() })

        coVerifyOnce { companyDataService.find(queryEq { where { this.cnpj.inList(companyCnpjs) } }) }
    }

    @Test
    fun `#createOrGetBillingAccountablePartyForCompany returns billingId when not exists`() = runBlocking {
        val company = TestModelFactory.buildCompany()
        val billingAccountableParty = company.toBillingAccountableParty(null)

        coEvery {
            billingAccountablePartyService.add(match { it.nationalId == company.cnpj && it.type == BillingAccountablePartyType.LEGAL_PERSON })
        } returns billingAccountableParty

        val result = companyService.createOrGetBillingAccountablePartyForCompany(company, null)
        assertThat(result).isSuccessWithData(billingAccountableParty.id)

        coVerifyOnce { billingAccountablePartyService.add(any()) }
    }

    @Test
    fun `#createOrGetBillingAccountablePartyForCompany returns billingId when not exists using email`() = runBlocking {
        val company = TestModelFactory.buildCompany()
        val email = "<EMAIL>"
        val billingAccountableParty = company.toBillingAccountableParty(email)

        coEvery {
            billingAccountablePartyService.add(match { it.nationalId == company.cnpj && it.type == BillingAccountablePartyType.LEGAL_PERSON && it.email == email })
        } returns billingAccountableParty

        val result = companyService.createOrGetBillingAccountablePartyForCompany(company, email)
        assertThat(result).isSuccessWithData(billingAccountableParty.id)

        coVerifyOnce { billingAccountablePartyService.add(any()) }
    }

    @Test
    fun `#createOrGetBillingAccountablePartyForCompany returns billingId when exists`() = runBlocking {
        val company = TestModelFactory.buildCompany()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        coEvery {
            billingAccountablePartyService.add(any())
        } returns DuplicatedItemException("").failure()
        coEvery { billingAccountablePartyService.findNationalIdEq(company.cnpj) } returns billingAccountableParty

        val result = companyService.createOrGetBillingAccountablePartyForCompany(company, null)
        assertThat(result).isSuccessWithData(billingAccountableParty.id)

        coVerifyOnce { billingAccountablePartyService.add(any()) }
        coVerifyOnce { billingAccountablePartyService.findNationalIdEq(any()) }
    }

    @Test
    fun `#findByBillingAccountablePartyId returns companies with given BillingAccountablePartyId`() = runBlocking {
        val billingAccountablePartyId = RangeUUID.generate()
        val company = TestModelFactory.buildCompany().toModel()

        coEvery { companyDataService.findOne(any()) } returns company

        val result = companyService.findByBillingAccountablePartyId(billingAccountablePartyId)
        assertThat(result).isSuccessWithData(company.toTransport())

        coVerifyOnce {
            companyDataService.findOne(queryEq {
                where {
                    this.billingAccountablePartyId.eq(
                        billingAccountablePartyId
                    )
                }
            })
        }
    }

    @Test
    fun `#findByEmail returns company`() = runBlocking {
        val companyEmail = "<EMAIL>"
        val company = TestModelFactory.buildCompany().toModel()

        coEvery { companyDataService.findOne(any()) } returns company.success()

        val result = companyService.findByEmail(companyEmail)
        assertThat(result).isSuccessWithData(company.toTransport())

        coVerifyOnce {
            companyDataService.findOne(queryEq {
                where { this.email.eq(companyEmail) }
            })
        }
    }

    @Test
    fun `#findByEmails returns companies`() = runBlocking {
        val companyEmail1 = "<EMAIL>"
        val companyEmail2 = "<EMAIL>"
        val companyEmails = listOf(companyEmail1, companyEmail2)
        val company1 = TestModelFactory.buildCompany(email = companyEmail1).toModel()
        val company2 = company1.copy(email = companyEmail2)
        val companies = listOf(company1, company2)

        coEvery { companyDataService.find(any()) } returns companies

        val result = companyService.findByEmails(companyEmails)
        assertThat(result).isSuccessWithData(companies.map { it.toTransport() })

        coVerifyOnce {
            companyDataService.find(queryEq {
                where { this.email.inList(companyEmails) }
            })
        }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findByFilters returns companies`() = runBlocking {
        val range = IntRange(0, 19)
        val company = TestModelFactory.buildCompany().toModel()

        coEvery { companyDataService.find(any()) } returns listOf(company)

        val result = companyService.findByFilters(company.name, range)

        assertThat(result).isSuccessWithData(listOf(company.toTransport()))

        coVerifyOnce {
            companyDataService.find(queryEq {
                where {
                    this.name.like(company.name) or this.cnpj.like(company.name)
                }
                    .orderBy { this.createdAt }
                    .sortOrder { desc }
                    .offset { range.first }
                    .limit { range.count() }
            })
        }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#countByFilters returns total of companies by filter`() = runBlocking {
        val company = TestModelFactory.buildCompany()
        val total = 12

        coEvery { companyDataService.count(any()) } returns total

        val result = companyService.countByFilters(company.name)
        assertThat(result).isSuccessWithData(total)

        coVerifyOnce {
            companyDataService.count(queryEq {
                where {
                    this.name.like(company.name) or this.cnpj.like(
                        company.name
                    )
                }
            })
        }
    }

    @Test
    fun `#countByIds returns total of companies by ids`() = runBlocking {
        val total = 12
        val ids = listOf(RangeUUID.generate())

        coEvery { companyDataService.count(any()) } returns total

        val result = companyService.countByIds(ids)
        assertThat(result).isSuccessWithData(total)

        coVerifyOnce { companyDataService.count(queryEq { where { this.id.inList(ids) } }) }
    }

    @Test
    fun `#findByIds returns companies by ids`() = runBlocking {
        val companies = listOf(TestModelFactory.buildCompany().toModel())
        val ids = listOf(RangeUUID.generate())

        coEvery { companyDataService.find(any()) } returns companies

        val result = companyService.findByIds(ids)
        assertThat(result).isSuccessWithData(companies.map { it.toTransport() })

        coVerifyOnce {
            companyDataService.find(
                queryEq {
                    where { this.id.inList(ids) }
                        .orderBy { this.createdAt }
                        .sortOrder { desc }
                }
            )
        }
    }

    @Test
    fun `#add - if company has valid default product id`() = runBlocking {
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty(nationalId = "86.207.165/0001-54")
        val company = TestModelFactory.buildCompany(
            cnpj = "86.207.165/0001-54",
            billingAccountablePartyId = billingAccountableParty.id,
            status = CompanyStatus.DRAFT
        ).toModel()

        every { company.validateDefaultProductId() } returns company.success()
        coEvery { companyDataService.add(company.sanitizeCompany()) } returns company.sanitizeCompany()
        coEvery { billingAccountablePartyService.add(any()) } returns billingAccountableParty
        coEvery {
            kafkaProducerService.produce(
                match<CompanyUpsertedEvent> { it.payload.company == company.sanitizeCompany().toTransport() }
            )
        } returns mockk()

        val result = companyService.add(company.toTransport())
        assertThat(result).isSuccessWithData(company.sanitizeCompany().toTransport())

        coVerifyOnce {
            companyDataService.add(match {
                it.address.postalCode == company.address.postalCode.onlyNumbers()
                        && it.billingAccountablePartyId == billingAccountableParty.id
            })
        }
        coVerifyOnce { billingAccountablePartyService.add(match { it.nationalId == company.cnpj }) }
        coVerifyOnce {
            kafkaProducerService.produce(
                match<CompanyUpsertedEvent> { it.payload.company == company.sanitizeCompany().toTransport() }
            )
        }
    }

    @Test
    fun `#add - if company has valid default product id and has billingEmail`() = runBlocking {
        val myEmail = "<EMAIL>"
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty(nationalId = "86.207.165/0001-54")
        val company = TestModelFactory.buildCompany(
            cnpj = "86.207.165/0001-54",
            billingAccountablePartyId = billingAccountableParty.id,
            status = CompanyStatus.DRAFT
        ).toModel()

        every { company.validateDefaultProductId() } returns company.success()
        coEvery { companyDataService.add(company.sanitizeCompany()) } returns company.sanitizeCompany()
        coEvery { billingAccountablePartyService.add(any()) } returns billingAccountableParty
        coEvery {
            kafkaProducerService.produce(
                match<CompanyUpsertedEvent> { it.payload.company == company.sanitizeCompany().toTransport() }
            )
        } returns mockk()

        val result = companyService.add(company.toTransport(), false, myEmail)
        assertThat(result).isSuccessWithData(company.sanitizeCompany().toTransport())

        coVerifyOnce {
            companyDataService.add(match {
                it.address.postalCode == company.address.postalCode.onlyNumbers()
                        && it.billingAccountablePartyId == billingAccountableParty.id
            })
        }
        coVerifyOnce { billingAccountablePartyService.add(match { it.nationalId == company.cnpj && it.email == myEmail }) }
        coVerifyOnce {
            kafkaProducerService.produce(
                match<CompanyUpsertedEvent> { it.payload.company == company.sanitizeCompany().toTransport() }
            )
        }
    }

    @Test
    fun `#add - if company has invalid default product id`() = runBlocking {
        val company = TestModelFactory.buildCompany().toModel()

        every {
            company.validateDefaultProductId()
        } returns InvalidDefaultProductIdException(company.defaultProductId!!, company.availableProducts!!)

        val result = companyService.add(company.toTransport())
        assertThat(result).isFailureOfType(InvalidDefaultProductIdException::class)

        coVerifyNone { companyDataService.add(any()) }
        coVerifyNone { billingAccountablePartyService.add(any()) }

    }

    @Test
    fun `#add - should not add company if cnpj is invalid`() = runBlocking {
        val company = TestModelFactory.buildCompany(cnpj = "123").toModel()

        every { company.validateDefaultProductId() } returns company.success()

        val result = companyService.add(company.toTransport())
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyNone { companyDataService.add(any()) }
        coVerifyNone { billingAccountablePartyService.add(any()) }
    }


    @Test
    fun `#getRootCompany returns root Company`() = runBlocking {
        val rootCompany = TestModelFactory.buildCompany(parentId = null).toModel()
        val parentCompany = TestModelFactory.buildCompany(parentId = rootCompany.id).toModel()
        val childCompany = TestModelFactory.buildCompany(parentId = parentCompany.id).toModel()

        coEvery { companyDataService.get(childCompany.id) } returns childCompany
        coEvery { companyDataService.get(parentCompany.id) } returns parentCompany
        coEvery { companyDataService.get(rootCompany.id) } returns rootCompany

        val result = companyService.getRootCompany(childCompany.id)
        assertThat(result).isSuccessWithData(rootCompany.toTransport())

        coVerify(exactly = 3) { companyDataService.get(any()) }
    }

    @Test
    fun `#isThisProductAvailableForCompany should return ProductIdNotOnAvailableProducts`() = runBlocking {
        val company = TestModelFactory.buildCompany().toModel()
        val product = TestModelFactory.buildProduct(type = ProductType.B2B)

        coEvery { companyDataService.get(company.id) } returns company

        val result = companyService.isThisProductAvailableForCompany(company.id, product)
        assertThat(result).isFailureOfType(ProductIdNotOnAvailableProducts::class)

        coVerifyOnce { companyService.get(company.id) }
    }

    @Test
    fun `#isThisProductAvailableForCompany should return product if have product in available list`() = runBlocking {
        val product = TestModelFactory.buildProduct(type = ProductType.B2B)
        val company = TestModelFactory.buildCompany(availableProducts = listOf(product.id)).toModel()

        coEvery { companyDataService.get(company.id) } returns company

        val result = companyService.isThisProductAvailableForCompany(company.id, product)
        assertThat(result).isSuccessWithData(product)

        coVerifyOnce { companyService.get(company.id) }
    }

    @Test
    fun `#findByExternalBrandIds returns companies with given externalBrandId`() = runBlocking {
        val companyExternalBrandIds = listOf("externalBrandId-1", "externalBrandId-2")
        val companies = companyExternalBrandIds.map {
            TestModelFactory.buildCompany(externalBrandId = it).toModel()
        }

        coEvery { companyDataService.find(any()) } returns companies

        val result = companyService.findByExternalBrandIds(companyExternalBrandIds)
        assertThat(result).isSuccessWithData(companies.map { it.toTransport() })

        coVerifyOnce { companyDataService.find(queryEq { where { this.externalBrandId.inList(companyExternalBrandIds) } }) }
    }

    @Test
    fun `#update - happy path`() = runBlocking {
        val defaultProductId = RangeUUID.generate()
        val company = TestModelFactory.buildCompany(
            cnpj = "86.207.165/0001-54",
            defaultProductId = defaultProductId,
            availableProducts = listOf(defaultProductId)
        ).toModel()
        val updatedCompany = company.copy(legalName = "Novo nome maluco")

        coEvery { companyDataService.get(any()) } returns company
        coEvery { companyDataService.update(any()) } returns updatedCompany.sanitizeCompany()
        coEvery {
            kafkaProducerService.produce(
                match<CompanyUpsertedEvent> { it.payload.company == updatedCompany.sanitizeCompany().toTransport() }
            )
        } returns mockk()

        val result = companyService.update(updatedCompany.toTransport())
        assertThat(result).isSuccessWithData(updatedCompany.sanitizeCompany().toTransport())

        coVerifyOnce { companyDataService.get(company.id) }
        coVerifyOnce { companyDataService.update(updatedCompany.sanitizeCompany()) }
        coVerifyOnce {
            kafkaProducerService.produce(
                match<CompanyUpsertedEvent> { it.payload.company == updatedCompany.sanitizeCompany().toTransport() }
            )
        }
    }

    @Test
    fun `#update - happy path without send event`() = runBlocking {
        val defaultProductId = RangeUUID.generate()
        val company = TestModelFactory.buildCompany(
            cnpj = "86.207.165/0001-54",
            defaultProductId = defaultProductId,
            availableProducts = listOf(defaultProductId)
        ).toModel()
        val updatedCompany = company.copy(legalName = "Novo nome maluco")

        coEvery { companyDataService.get(any()) } returns company
        coEvery { companyDataService.update(any()) } returns updatedCompany.sanitizeCompany()

        val result = companyService.update(updatedCompany.toTransport(), sendEvent = false)
        assertThat(result).isSuccessWithData(updatedCompany.sanitizeCompany().toTransport())

        coVerifyOnce { companyDataService.get(company.id) }
        coVerifyOnce { companyDataService.update(updatedCompany.sanitizeCompany()) }
        coVerifyNone { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#update - happy path with billing accountable party change`() = runBlocking {
        val defaultProductId = RangeUUID.generate()
        val company = TestModelFactory.buildCompany(
            cnpj = "86.207.165/0001-54",
            defaultProductId = defaultProductId,
            availableProducts = listOf(defaultProductId)
        ).toModel()
        val updatedCompany = company.copy(billingAccountablePartyId = RangeUUID.generate())

        coEvery { companyDataService.get(any()) } returns company
        coEvery { companyDataService.update(any()) } returns updatedCompany
        coEvery {
            kafkaProducerService.produce(
                match<CompanyUpsertedEvent> { it.payload.company == updatedCompany.toTransport() }
            )
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(match<CompanyBillingAccountablePartyUpdatedEvent> {
                it.payload.oldBillingAccountablePartyId == company.billingAccountablePartyId &&
                        it.payload.currentCompany == updatedCompany.toTransport()
            })
        } returns mockk()

        val result = companyService.update(updatedCompany.toTransport())
        assertThat(result).isSuccessWithData(updatedCompany.toTransport())


        coVerifyOnce { companyDataService.get(company.id) }
        coVerifyOnce { companyDataService.update(updatedCompany.sanitizeCompany()) }
        coVerifyOnce {
            kafkaProducerService.produce(
                match<CompanyUpsertedEvent> { it.payload.company == updatedCompany.toTransport() }
            )
            kafkaProducerService.produce(match<CompanyBillingAccountablePartyUpdatedEvent> {
                it.payload.oldBillingAccountablePartyId == company.billingAccountablePartyId &&
                        it.payload.currentCompany == updatedCompany.toTransport()
            })
        }
    }

    @Test
    fun `#update - should not add company if cnpj is invalid`() = runBlocking {
        val company = TestModelFactory.buildCompany(cnpj = "123").toModel()

        every {
            company.sanitizeCompany().validateDefaultProductId()
        } returns company.success()

        val result = companyService.update(company.sanitizeCompany().toTransport())
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyNone { companyDataService.update(match { it.address.postalCode == company.address.postalCode.onlyNumbers() }) }
    }

    @Test
    fun `#add - company with null status should be added as DRAFT`() = runBlocking {
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty(nationalId = "86.207.165/0001-54")
        val company = TestModelFactory.buildCompany(
            cnpj = "86.207.165/0001-54",
            billingAccountablePartyId = billingAccountableParty.id,
            status = null
        ).toModel()

        val expectedCompany = company.sanitizeCompany().copy(status = CompanyStatus.DRAFT)

        every { company.validateDefaultProductId() } returns company.success()
        coEvery { companyDataService.add(expectedCompany) } returns expectedCompany
        coEvery { billingAccountablePartyService.add(any()) } returns billingAccountableParty
        coEvery {
            kafkaProducerService.produce(
                match<CompanyUpsertedEvent> { it.payload.company == expectedCompany.toTransport() }
            )
        } returns mockk()


        val result = companyService.add(company.toTransport())
        assertThat(result).isSuccessWithData(expectedCompany.toTransport())
        coVerifyOnce {
            kafkaProducerService.produce(
                match<CompanyUpsertedEvent> { it.payload.company == expectedCompany.toTransport() }
            )
        }
    }

    @Test
    fun `#findByContractId returns company with given contractId`() = runBlocking {
        val contractId = RangeUUID.generate()
        val company = TestModelFactory.buildCompany(contractIds = listOf(contractId)).toModel()

        coEvery { companyDataService.find(any()) } returns listOf(company)

        val result = companyService.findByContractId(contractId)
        assertThat(result).isSuccessWithData(listOf(company.toTransport()))

        coVerifyOnce {
            companyDataService.find(queryEq {
                where {
                    this.contractIds.contains(
                        contractId
                    )
                }
            })
        }
    }

    @Test
    fun `#findByContractIds returns company with given contractId`() = runBlocking {
        val contractId = RangeUUID.generate()
        val company = TestModelFactory.buildCompany(contractIds = listOf(contractId)).toModel()

        coEvery { companyDataService.find(any()) } returns listOf(company)

        val result = companyService.findByContractIds(listOf(contractId))
        assertThat(result).isSuccessWithData(listOf(company.toTransport()))

        coVerifyOnce {
            companyDataService.find(queryEq {
                where {
                    this.contractIds.containsAny(
                        listOf(contractId)
                    )
                }
            })
        }
    }

    @Test
    fun `#findActiveCompanies returns active companies with sizes`() = runBlocking {
        val range = IntRange(0, 19)
        val sizes = listOf(CompanySize.MLA.name)
        val company = TestModelFactory.buildCompany(
            status = CompanyStatus.ACTIVE, companySize = CompanySize.MLA
        ).toModel()

        coEvery { companyDataService.find(any()) } returns listOf(company)

        val result = companyService.findActiveCompanies(range, sizes)
        assertThat(result).isSuccessWithData(listOf(company.toTransport()))

        coVerifyOnce {
            companyDataService.find(queryEq {
                where { this.status.eq(CompanyStatus.ACTIVE) and this.companySize.inList(sizes) }
                    .orderBy { this.createdAt }
                    .sortOrder { asc }
                    .offset { range.first }
                    .limit { range.count() }
            })
        }
    }

    @Test
    fun `#findActiveCompanies returns active companies without sizes`() = runBlocking {
        val range = IntRange(0, 19)
        val company = TestModelFactory
            .buildCompany(status = CompanyStatus.ACTIVE, companySize = CompanySize.MLA).toModel()

        coEvery { companyDataService.find(any()) } returns listOf(company)

        val result = companyService.findActiveCompanies(range)
        assertThat(result).isSuccessWithData(listOf(company.toTransport()))

        coVerifyOnce {
            companyDataService.find(queryEq {
                where { this.status.eq(CompanyStatus.ACTIVE) }
                    .orderBy { this.createdAt }
                    .sortOrder { asc }
                    .offset { range.first }
                    .limit { range.count() }
            })
        }
    }

    @Test
    fun `#add - should parse email to lowercase`() = runBlocking {
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty(nationalId = "86.207.165/0001-54")
        val company = TestModelFactory.buildCompany(
            email = "<EMAIL>",
            cnpj = "86.207.165/0001-54",
            billingAccountablePartyId = billingAccountableParty.id,
            status = CompanyStatus.DRAFT
        ).toModel()

        every { company.validateDefaultProductId() } returns company.success()
        coEvery { companyDataService.add(any()) } returns company.sanitizeCompany()
        coEvery { billingAccountablePartyService.add(any()) } returns billingAccountableParty
        coEvery {
            kafkaProducerService.produce(
                match<CompanyUpsertedEvent> { it.payload.company == company.sanitizeCompany().toTransport() }
            )
        } returns mockk()

        val result = companyService.add(company.toTransport())
        assertThat(result).isSuccessWithData(company.sanitizeCompany().toTransport())

        coVerifyOnce {
            companyDataService.add(match {
                it.email == company.email.lowercase()
            })
        }
        coVerifyOnce { billingAccountablePartyService.add(match { it.nationalId == company.cnpj }) }
        coVerifyOnce {
            kafkaProducerService.produce(
                match<CompanyUpsertedEvent> { it.payload.company == company.sanitizeCompany().toTransport() }
            )
        }
    }


    @Test
    fun `#update - should parse email to lowercase`() = runBlocking {
        val defaultProductId = RangeUUID.generate()
        val company = TestModelFactory.buildCompany(
            email = "<EMAIL>",
            cnpj = "86.207.165/0001-54",
            defaultProductId = defaultProductId,
            availableProducts = listOf(defaultProductId)
        ).toModel()
        val updatedCompany = company.copy(legalName = "Novo nome maluco")

        coEvery { companyDataService.get(any()) } returns company
        coEvery { companyDataService.update(any()) } returns updatedCompany.sanitizeCompany()
        coEvery {
            kafkaProducerService.produce(
                match<CompanyUpsertedEvent> { it.payload.company == updatedCompany.sanitizeCompany().toTransport() }
            )
        } returns mockk()

        val result = companyService.update(updatedCompany.toTransport())
        assertThat(result).isSuccessWithData(updatedCompany.sanitizeCompany().toTransport())

        coVerifyOnce { companyDataService.get(company.id) }
        coVerifyOnce {
            companyDataService.update(match {
                it.email == company.email.lowercase()
            })
        }
        coVerifyOnce {
            kafkaProducerService.produce(
                match<CompanyUpsertedEvent> { it.payload.company == updatedCompany.sanitizeCompany().toTransport() }
            )
        }
    }

    @Test
    fun `#enrichCompanyEmptyAddressById - should enrich company with empty address`() = mockRangeUUID { uuid ->
        val company = TestModelFactory.buildCompany(
            id = uuid,
            cnpj = "83.762.231/0001-60",
            address = CompanyAddress(
                postalCode = "",
                street = "",
                number = 0,
                city = "",
                State = "",
                neighborhood = ""
            )
        )

        val companyExternalInformation = CompanyExternalInformationResponse(
            createdAt = "13/10/2016",
            status = "ATIVA",
            type = "MATRIZ",
            legalName = "ACME LTDA",
            name = "ACME",
            sizeCategory = "MICRO EMPRESA",
            postalCode = "05.018-000",
            state = "SP",
            city = "SAO PAULO",
            neighborhood = "PERDIZES",
            street = "RUA CAYOWAA",
            number = "519",
            complement = "APT 123"
        )

        val updatedCompany = company.copy(address = companyExternalInformation.toCompanyAddress())

        coEvery { spyService.get(company.id) } returns company
        coEvery { receitaFederalClient.getCompanyInfoByCNPJ(company.cnpj) } returns companyExternalInformation
        coEvery { spyService.update(updatedCompany) } returns updatedCompany

        val result = spyService.enrichCompanyEmptyAddressById(company.id)

        assertThat(result).isSuccessWithData(updatedCompany)

        coVerifyOnce { spyService.get(any()) }
        coVerifyOnce { receitaFederalClient.getCompanyInfoByCNPJ(any()) }
        coVerifyOnce { spyService.update(any()) }
    }

    @Test
    fun `#enrichCompanyEmptyAddressById - should return error when address is already filled`() =
        mockRangeUUID { uuid ->
            val company = TestModelFactory.buildCompany(
                id = uuid,
                cnpj = "83.762.231/0001-60",
                address = CompanyAddress(
                    postalCode = "05018000",
                    street = "Av. Paulista",
                    number = 1000,
                    city = "São Paulo",
                    State = "SP",
                    neighborhood = "Pinheiros"
                )
            )

            coEvery { spyService.get(company.id) } returns company

            val result = spyService.enrichCompanyEmptyAddressById(company.id)

            assertThat(result).isFailureOfType(InvalidArgumentException::class)

            coVerifyOnce { spyService.get(company.id) }
            coVerifyNone { receitaFederalClient.getCompanyInfoByCNPJ(any()) }
            coVerifyNone { spyService.update(any()) }
        }

    @Test
    fun `#enrichCompanyEmptyAddressById - should return error when receita federal client fails with invalid national id error`() =
        mockRangeUUID { uuid ->
            val company = TestModelFactory.buildCompany(
                id = uuid,
                cnpj = "83.762.231/0001-60",
                address = CompanyAddress(
                    postalCode = "",
                    street = "",
                    number = 0,
                    city = "",
                    State = "",
                    neighborhood = ""
                )
            )

            val exception = IllegalArgumentException("Invalid national ID format")

            coEvery { spyService.get(company.id) } returns company
            coEvery { receitaFederalClient.getCompanyInfoByCNPJ(company.cnpj) } throws exception

            val result = spyService.enrichCompanyEmptyAddressById(company.id)

            assertThat(result).isFailureOfType(IllegalArgumentException::class)

            coVerifyOnce { spyService.get(company.id) }
            coVerifyOnce { receitaFederalClient.getCompanyInfoByCNPJ(company.cnpj) }
            coVerifyNone { spyService.update(any()) }
        }

    @Test
    fun `#enrichCompanyEmptyAddressById - should return error when receita federal client fails`() =
        mockRangeUUID { uuid ->
            val company = TestModelFactory.buildCompany(
                id = uuid,
                cnpj = "83.762.231/0001-60",
                address = CompanyAddress(
                    postalCode = "",
                    street = "",
                    number = 0,
                    city = "",
                    State = "",
                    neighborhood = ""
                )
            )

            val exception = Exception()

            coEvery { spyService.get(company.id) } returns company
            coEvery { receitaFederalClient.getCompanyInfoByCNPJ(company.cnpj) } throws exception

            val result = spyService.enrichCompanyEmptyAddressById(company.id)

            assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce { spyService.get(company.id) }
            coVerifyOnce { receitaFederalClient.getCompanyInfoByCNPJ(company.cnpj) }
            coVerifyNone { spyService.update(any()) }
        }
}
