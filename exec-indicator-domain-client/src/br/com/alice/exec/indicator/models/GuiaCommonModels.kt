package br.com.alice.exec.indicator.models

import br.com.alice.common.MvUtil
import br.com.alice.common.core.PersonId
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.data.layer.models.AnsAccidentIndication
import br.com.alice.data.layer.models.AttendanceCharacter
import br.com.alice.data.layer.models.ExtraGuiaInfo
import br.com.alice.data.layer.models.HospitalizationType
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.data.layer.models.ProfessionalIdentification
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.data.layer.models.TotvsGuiaStatus
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

interface GuiaData {
    val newBorn: Boolean
}

data class GuiaExamData(override val newBorn: Boolean) : GuiaData

data class GuiaPsData(val accidentIndication: String, override val newBorn: Boolean) : GuiaData

data class GuiaBaseData(override val newBorn: Boolean) : GuiaData

data class GuiaCreationProcedure(
    val procedureId: String,
    val quantity: Int,
    val unitValue: Double = 0.0
)

data class Beneficiary(
    val name: String?,
    val nationalId: String,
    val newBornAttendance: Boolean,
    val externalId: String? = null,
)

data class RequesterData(
    val providerCode: String,
    val name: String,
)

const val DEFAULT_CBO = "999999"
const val DEFAULT_PROFESSIONAL_NAME = "Nao Informado"
const val DEFAULT_COUNCIL_NUMBER = "NULL"
const val MAX_NAME_LENGTH = 70

data class Professional(
    var name: String = DEFAULT_PROFESSIONAL_NAME,
    val cbo: String = DEFAULT_CBO,
    val councilNumber: String,
    val council: CouncilType,
    val councilState: State,
    val phoneNumber: String? = null,
    val email: String? = null
) {
    init {
        if (name.length > MAX_NAME_LENGTH) {
            name = name.take(MAX_NAME_LENGTH).substringBeforeLast(' ').ifEmpty { name.take(MAX_NAME_LENGTH) }
        }
    }
}

data class ExecutorData(
    val providerCode: String,
    val name: String,
    val cnes: String
)

data class GuiaProcedure(
    val table: String,
    val code: String,
    val description: String,
    val quantity: Int? = null,
    val status: MvAuthorizedProcedureStatus? = MvAuthorizedProcedureStatus.PENDING,
    val dosage: Double? = null,
)

enum class GuiaOrigin {
    EHR, EITA
}

data class MvAuthorizedProcedureData(
    val items: List<Item>,
    val professional: ProfessionalIdentification,
    val personId: PersonId,
    val authorizerId: UUID? = null,
    val userEmail: String,
    val extraGuiaInfo: ExtraGuiaInfo,
    val testRequestId: UUID? = null,
)

data class GuiaCreationRequest(
    val type: MvUtil.TISS,
    val accidentIndication: AnsAccidentIndication? = null,
    val requestedAt: LocalDate,
    val newBorn: Boolean = false,
    val requester: ProfessionalIdentification,
    val procedures: List<GuiaCreationProcedure>,
    val personId: UUID,
    val executorCnpj: String? = null,
    val medicalRequestFileIds: List<UUID> = emptyList(),
    val status: TotvsGuiaStatus = TotvsGuiaStatus.PENDING,
    val referenceTotvsGuiaId: UUID? = null,
    val creatorCnpj: String? = null,
    val providerUnitId: UUID? = null
) {
    init {
        if (type == MvUtil.TISS.PS) {
            requireNotNull(this.accidentIndication) { "accidentIndication is required for PS guide type" }
        }
    }
}

data class CanCreateGuiaResponse(
    val canCreate: Boolean,
    val totvsGuiaId: UUID? = null,
    val motive: String? = null,
    val data: CanCreateGuiaResponseData? = null,
)

data class CanCreateGuiaResponseData(
    val totvsGuiaStatus: TotvsGuiaStatus,
    val guiaNumber: String,
    val totvsGuiaId: UUID,
    val startDate: String?,
    val type: HospitalizationType,
    val accidentIndication: AnsAccidentIndication,
    val attendanceCharacter: AttendanceCharacter,
    val professionalName: String,
)

enum class TotvsGuiaStatusResponse(val description: String) {
    PROCESSING("Processamento solicitação"),
    ERROR("Erro"),
    AUTHORIZED("Autorizado"),
    CANCELLED("Cancelado"),
    PARTIALLY_AUTHORIZED("Parcialmente autorizado"),
    PENDING("Pendente"),
    UNAUTHORIZED("Não autorizado");

    companion object {
        fun fromTotvsGuiaStatus(totvsGuia: TotvsGuia, toleranceTime: Long): TotvsGuiaStatusResponse {
            val externalCode = totvsGuia.externalCode
            val createdAt = totvsGuia.createdAt
            val status = totvsGuia.status

            if (status == TotvsGuiaStatus.PENDING && externalCode == null) {
                if (createdAt.isBefore(LocalDateTime.now().minusMinutes(toleranceTime))) {
                    return ERROR
                }

                return PROCESSING
            }

            return when (status) {
                TotvsGuiaStatus.PENDING -> PENDING
                TotvsGuiaStatus.AUTHORIZED -> AUTHORIZED
                TotvsGuiaStatus.CANCELLED -> CANCELLED
                TotvsGuiaStatus.PARTIALLY_AUTHORIZED -> PARTIALLY_AUTHORIZED
                TotvsGuiaStatus.UNAUTHORIZED -> UNAUTHORIZED
                else -> ERROR
            }
        }
    }
}
