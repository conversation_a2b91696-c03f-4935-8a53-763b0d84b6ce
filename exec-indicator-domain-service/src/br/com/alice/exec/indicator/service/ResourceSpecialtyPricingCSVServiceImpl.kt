package br.com.alice.exec.indicator.service

import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.foldNotFound
import br.com.alice.common.logging.logger
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.HealthSpecialistResourceBundle
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.ResourceBundleSpecialty
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricing
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdate
import br.com.alice.data.layer.models.TierType
import br.com.alice.exec.indicator.client.CSVGenerationResponse
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleService
import br.com.alice.exec.indicator.client.PricingUpdateHistoryFilters
import br.com.alice.exec.indicator.client.ResourceSpecialtyPricingCSVService
import br.com.alice.exec.indicator.client.UploadPriceChangesRequest
import br.com.alice.exec.indicator.models.ProcessingResourceBundleSpecialtyPricingUpdateResponse
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyPricingUpdateHistoryItem
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyPricingUpdateHistoryWithCount
import br.com.alice.exec.indicator.service.internal.ResourceBundleSpecialtyPricingService
import br.com.alice.exec.indicator.service.internal.ResourceBundleSpecialtyPricingUpdateService
import br.com.alice.exec.indicator.service.internal.ResourceBundleSpecialtyService
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.GenericVaultUploadByteArray
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser
import org.apache.commons.csv.CSVPrinter
import org.apache.commons.csv.CSVRecord
import java.io.ByteArrayOutputStream
import java.io.OutputStreamWriter
import java.math.BigDecimal
import java.net.URL
import java.util.UUID

class ResourceSpecialtyPricingCSVServiceImpl(
    private val resourceBundleSpecialtyService: ResourceBundleSpecialtyService,
    private val resourceBundleSpecialtyPricingService: ResourceBundleSpecialtyPricingService,
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val healthSpecialistResourceBundleService: HealthSpecialistResourceBundleService,
    private val resourceBundleSpecialtyPricingUpdateService: ResourceBundleSpecialtyPricingUpdateService,
    private val fileVaultActionService: FileVaultActionService,
    private val staffService: StaffService
) : ResourceSpecialtyPricingCSVService {

    companion object {
        const val CSV_FILE_NAME = "precificacao_pendente"
        const val FAILED_LINES_CSV_FILE_NAME = "falhas_precificacao"
        const val CHUNK_SIZE = 500
        const val EITA_FILE_VAULT_STORAGE_DOMAIN = "eita"
        const val EITA_FILE_VAULT_STORAGE_NAMESPACE = "resource_bundle_specialty_pricing_update"
    }

    override suspend fun generate(resourceBundleSpecialtyIds: List<UUID>): Result<CSVGenerationResponse, Throwable> {
        if (resourceBundleSpecialtyIds.isEmpty()) {
            logger.info(
                "ResourceSpecialtyPricingCSVServiceImpl generate all"
            )

            return generateAllActive()
        }

        logger.info(
            "ResourceSpecialtyPricingCSVServiceImpl generate from ids",
            "resourceBundleSpecialtyIds" to resourceBundleSpecialtyIds
        )

        return generateFromIds(resourceBundleSpecialtyIds)
    }

    override suspend fun generateFailedLinesFile(resourceBundleSpecialtyPricingUpdateId: UUID) =
        resourceBundleSpecialtyPricingUpdateService.get(resourceBundleSpecialtyPricingUpdateId)
            .flatMapPair { fileVaultActionService.securedGenericLink(it.fileVaultId) }
            .map { (vaultResponse, resourceBundleSpecialtyPricingUpdate) ->
                val failedRows = resourceBundleSpecialtyPricingUpdate.failedRowsErrors.map { it.row }

                generateFailedLinesFileForFileAndLines(
                    fileUrl = vaultResponse.url,
                    failedRows = failedRows
                )
            }

    override suspend fun getProcessingResourceBundleSpecialtyPricingUpdate(): Result<ProcessingResourceBundleSpecialtyPricingUpdateResponse, Throwable> {
        return resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate().map {
            ProcessingResourceBundleSpecialtyPricingUpdateResponse(
                isProcessing = true,
                resourceBundleSpecialtyPricingUpdate = it
            )
        }.foldNotFound {
            ProcessingResourceBundleSpecialtyPricingUpdateResponse(
                isProcessing = false,
            ).success()
        }
    }

    override suspend fun getPricingUpdateHistory(
        filters: PricingUpdateHistoryFilters,
        range: IntRange
    ): Result<ResourceBundleSpecialtyPricingUpdateHistoryWithCount, Throwable> {
        logger.info(
            "ResourceSpecialtyPricingCSVServiceImpl - getPricingUpdateHistory",
            "filters" to filters,
            "range" to range
        )

        return coroutineScope {
            val itemsDef = async {
                resourceBundleSpecialtyPricingUpdateService
                    .getResourceBundleSpecialtyPricingUpdateHistory(filters, range)
            }

            val totalDef = async {
                resourceBundleSpecialtyPricingUpdateService
                    .countResourceBundleSpecialtyPricingUpdateHistory(filters)
            }

            coResultOf {
                ResourceBundleSpecialtyPricingUpdateHistoryWithCount(
                    count = totalDef.await().get(),
                    items = itemsDef.await().toHistoryResponse().get()
                )
            }

        }
    }

    override suspend fun uploadPriceChanges(
        request: UploadPriceChangesRequest
    ): Result<ResourceBundleSpecialtyPricingUpdate, Throwable> {
        val content = request.content

        val file = GenericVaultUploadByteArray(
            domain = EITA_FILE_VAULT_STORAGE_DOMAIN,
            namespace = EITA_FILE_VAULT_STORAGE_NAMESPACE,
            originalFileName = request.fileName,
            fileContent = content,
            fileType = request.fileType,
            fileSize = content.size.toLong(),
        )

        return getProcessingResourceBundleSpecialtyPricingUpdate().flatMap {
            if (it.isProcessing) {
                logger.error(
                    "ResourceSpecialtyPricingCSVServiceImpl uploadPriceChanges",
                    "isProcessing" to true
                )

                return InvalidArgumentException("There is already a process running").failure()
            }
            fileVaultActionService.uploadGenericFile(
                file
            )
        }.flatMap {
            resourceBundleSpecialtyPricingUpdateService.add(
                ResourceBundleSpecialtyPricingUpdate(
                    fileVaultId = it.id,
                    fileName = request.fileName,
                    pricesBeginAt = request.pricesBeginAt,
                    createdByStaffId = request.staffId,
                    rowsCount = 0,
                    failedRowsCount = 0,
                )
            )
        }
    }

    private suspend fun generateAllActive(): Result<CSVGenerationResponse, Throwable> =
        resourceBundleSpecialtyService.findAllActive()
            .map { resourceBundleSpecialties ->

                if (resourceBundleSpecialties.isEmpty()) {
                    logger.error(
                        "ResourceSpecialtyPricingCSVServiceImpl generateAllActive",
                        "resourceBundleSpecialties" to resourceBundleSpecialties
                    )

                    throw InvalidArgumentException("Resource bundle specialties not found")
                }

                val chunks = resourceBundleSpecialties.chunked(CHUNK_SIZE)

                val allResourceSpecialtyPricingCSVList = chunks.pmap { resourceBundleSpecialtiesChunk ->

                    val resourceSpecialtyBundleMap = resourceBundleSpecialtiesChunk.associateBy { it.id }
                    val resourceBundleSpecialtyIds = resourceSpecialtyBundleMap.keys.toList()

                    val specialtyIds = resourceBundleSpecialtiesChunk.map { it.medicalSpecialtyId }
                    val resourceIds = resourceBundleSpecialtiesChunk.map { it.healthSpecialistResourceBundleId }

                    coroutineScope {
                        val resourcesAndSpecialtiesMapResultDeferred = async {
                            fetchResourcesAndSpecialties(resourceIds, specialtyIds)
                        }

                        val resourceBundleSpecialtyPricingDeferred = async {
                            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(
                                resourceBundleSpecialtyIds
                            )
                        }

                        val resourcesAndSpecialtiesMap = resourcesAndSpecialtiesMapResultDeferred.await().get()
                        val pricing = resourceBundleSpecialtyPricingDeferred.await().get()

                        resourceBundleSpecialtiesChunk.map { bundle ->
                            mapToResourceSpecialtyPricingCSV(
                                bundle,
                                pricing.associateBy { it.resourceBundleSpecialtyId },
                                resourcesAndSpecialtiesMap.specialtiesMap,
                                resourcesAndSpecialtiesMap.resourceBundlesMap
                            )
                        }
                    }

                }.flatten()

                logger.info(
                    "ResourceSpecialtyPricingCSVServiceImpl generateAllActive: Generated",
                    "size" to allResourceSpecialtyPricingCSVList.size
                )

                val csvBytes = allResourceSpecialtyPricingCSVList.toCSV()
                val fileName = CSV_FILE_NAME

                CSVGenerationResponse(
                    fileName = fileName,
                    bytes = csvBytes
                )

            }

    private suspend fun generateFromIds(
        resourceBundleSpecialtyIds: List<UUID>
    ): Result<CSVGenerationResponse, Throwable> = coroutineScope {
        val resourceBundleSpecialtiesDeferred = async {
            resourceBundleSpecialtyService.findByIds(resourceBundleSpecialtyIds)
        }

        val resourcePricingDeferred = async {
            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(resourceBundleSpecialtyIds)
        }

        return@coroutineScope coResultOf {

            val resourceBundle = resourceBundleSpecialtiesDeferred.await().get()
            val resourcePricing = resourcePricingDeferred.await().get()

            if (resourceBundle.isEmpty() || resourcePricing.isEmpty()) {
                logger.error(
                    "ResourceSpecialtyPricingCSVServiceImpl generateFromIds",
                    "resourceBundleSpecialties" to resourceBundle,
                    "resourcePricing" to resourcePricing
                )

                throw InvalidArgumentException("Resource bundle specialties or pricing not found")
            }

            val specialtyIds = resourceBundle.map { it.medicalSpecialtyId }
            val resourceIds = resourceBundle.map { it.healthSpecialistResourceBundleId }

            val resourcesAndSpecialtiesMapResult = fetchResourcesAndSpecialties(resourceIds, specialtyIds).get()

            val resourceBundleSpecialtyPricingCSVList = resourceBundle.map { bundle ->
                mapToResourceSpecialtyPricingCSV(
                    bundle,
                    resourcePricing.associateBy { it.resourceBundleSpecialtyId },
                    resourcesAndSpecialtiesMapResult.specialtiesMap,
                    resourcesAndSpecialtiesMapResult.resourceBundlesMap
                )
            }

            val csvBytes = resourceBundleSpecialtyPricingCSVList.toCSV()
            val fileName = CSV_FILE_NAME

            CSVGenerationResponse(
                fileName = fileName,
                bytes = csvBytes
            )
        }
    }

    private suspend fun Result<List<ResourceBundleSpecialtyPricingUpdate>, Throwable>.toHistoryResponse() =
        this.flatMapPair { resources -> staffService.findByList(resources.map { it.createdByStaffId }) }
            .flatMap { (staffs, resources) ->
                val staffsMap = staffs.associateBy { it.id }
                fileVaultActionService.securedGenericLinks(resources.map { it.fileVaultId })
                    .map { files ->
                        val filesMap = files.associateBy { it.id }

                        resources.map { resource ->
                            ResourceBundleSpecialtyPricingUpdateHistoryItem(
                                id = resource.id,
                                fileName = resource.fileName,
                                fileUrl = filesMap[resource.fileVaultId]?.url,
                                createdByStaff = staffsMap[resource.createdByStaffId],
                                processingAt = resource.processingAt,
                                completedAt = resource.completedAt,
                                rowsCount = resource.rowsCount,
                                failedRowsCount = resource.failedRowsCount,
                                failedRowsErrors = resource.failedRowsErrors,
                                parsingError = resource.parsingError,
                                pricesBeginAt = resource.pricesBeginAt,
                                createdAt = resource.createdAt
                            )
                        }
                    }
            }

    private suspend fun fetchResourcesAndSpecialties(
        resourceIds: List<UUID>,
        specialtyIds: List<UUID>
    ): Result<ResourcesAndSpecialtiesMap, Throwable> = coroutineScope {
        val resourceBundlesDeferred = async { healthSpecialistResourceBundleService.findByIds(resourceIds.distinct()) }
        val specialtiesDeferred = async { medicalSpecialtyService.getByIds(specialtyIds.distinct()) }

        return@coroutineScope coResultOf {
            ResourcesAndSpecialtiesMap(
                resourceBundlesMap = resourceBundlesDeferred.await().get().associateBy { it.id },
                specialtiesMap = specialtiesDeferred.await().get().associateBy { it.id }
            )
        }
    }

    private fun mapToResourceSpecialtyPricingCSV(
        resourceBundleSpecialty: ResourceBundleSpecialty,
        pricingMap: Map<UUID, ResourceBundleSpecialtyPricing>,
        specialtiesMap: Map<UUID, MedicalSpecialty>,
        resourceBundlesMap: Map<UUID, HealthSpecialistResourceBundle>
    ): ResourceSpecialtyPricingCSV {
        val specialty = specialtiesMap[resourceBundleSpecialty.medicalSpecialtyId]
        val resource = resourceBundlesMap[resourceBundleSpecialty.healthSpecialistResourceBundleId]
        val pricing = pricingMap[resourceBundleSpecialty.id]

        val executionEnvironment = resource?.executionEnvironment
            ?: HealthSpecialistProcedureExecutionEnvironment.DOES_NOT_APPLY

        return ResourceSpecialtyPricingCSV(
            resourceBundleSpecialtyId = resourceBundleSpecialty.id,
            medicalSpecialtyName = specialty?.name ?: "",
            aliceCode = resource?.code ?: "",
            primaryTuss = resource?.primaryTuss ?: "",
            description = resource?.description ?: "",
            executionEnvironment = executionEnvironment,
            talentedT3 = priceOrZero(pricing, SpecialistTier.TALENTED, TierType.TIER_3),
            talentedT2 = priceOrZero(pricing, SpecialistTier.TALENTED, TierType.TIER_2),
            talentedT1 = priceOrZero(pricing, SpecialistTier.TALENTED, TierType.TIER_1),
            talentedT0 = priceOrZero(pricing, SpecialistTier.TALENTED, TierType.TIER_0),
            expertT2 = priceOrZero(pricing, SpecialistTier.EXPERT, TierType.TIER_2),
            expertT1 = priceOrZero(pricing, SpecialistTier.EXPERT, TierType.TIER_1),
            expertT0 = priceOrZero(pricing, SpecialistTier.EXPERT, TierType.TIER_0),
            superExpertT1 = priceOrZero(pricing, SpecialistTier.SUPER_EXPERT, TierType.TIER_1),
            superExpertT0 = priceOrZero(pricing, SpecialistTier.SUPER_EXPERT, TierType.TIER_0),
            ultraExpertT0 = priceOrZero(pricing, SpecialistTier.ULTRA_EXPERT, TierType.TIER_0)
        )
    }

    private fun List<ResourceSpecialtyPricingCSV>.toCSV(): ByteArray {
        val outputStream = ByteArrayOutputStream()
        val writer = OutputStreamWriter(outputStream)

        CSVPrinter(writer, getCSVFormat()).use { printer ->
            this.forEach { item ->
                printer.printRecord(
                    item.resourceBundleSpecialtyId.toString(),
                    item.medicalSpecialtyName,
                    item.aliceCode,
                    item.primaryTuss,
                    item.description,
                    item.executionEnvironment.description,
                    item.talentedT3.toString(),
                    item.talentedT2.toString(),
                    item.talentedT1.toString(),
                    item.talentedT0.toString(),
                    item.expertT2.toString(),
                    item.expertT1.toString(),
                    item.expertT0.toString(),
                    item.superExpertT1.toString(),
                    item.superExpertT0.toString(),
                    item.ultraExpertT0.toString()
                )
            }

            writer.flush()
        }
        return outputStream.toByteArray()
    }

    private fun getCSVFormat(): CSVFormat {
        val headers = arrayOf(
            "RESOURCE_BUNDLE_SPECIALTY_ID",
            "Especialidade",
            "Alice Code",
            "Tuss Primário",
            "Descricao",
            "Ambiente",
            "TALENTED T3",
            "TALENTED T2",
            "TALENTED T1",
            "TALENTED T0",
            "EXPERT T2",
            "EXPERT T1",
            "EXPERT T0",
            "SUPER EXPERT T1",
            "SUPER EXPERT T0",
            "ULTRA EXPERT T0"
        )

        return CSVFormat.DEFAULT
            .withHeader(*headers)
            .withDelimiter(',')
            .withIgnoreSurroundingSpaces()
            .withRecordSeparator('\n')
    }

    private fun priceOrZero(pricing: ResourceBundleSpecialtyPricing?, tier: SpecialistTier, productTier: TierType) =
        pricing?.getPriceByTierAndProductTier(tier, productTier) ?: BigDecimal.ZERO

    private data class ResourcesAndSpecialtiesMap(
        val resourceBundlesMap: Map<UUID, HealthSpecialistResourceBundle>,
        val specialtiesMap: Map<UUID, MedicalSpecialty>
    )

    private fun generateFailedLinesFileForFileAndLines(
        fileUrl: String,
        failedRows: List<Int>
    ): CSVGenerationResponse {
        val inputStream = URL(fileUrl).openStream()
        val reader = inputStream.bufferedReader()
        val csvParser = CSVParser(reader, CSVFormat.DEFAULT.withHeader())
        val headers = csvParser.headerNames

        val selectedRecords = mutableListOf<CSVRecord>()
        csvParser.forEachIndexed { index, record ->
            if (index in failedRows) {
                selectedRecords.add(record)
            }
        }

        val outputStream = ByteArrayOutputStream()
        val writer = OutputStreamWriter(outputStream)

        CSVPrinter(writer, getCSVFormat()).use { printer ->
            for (record in selectedRecords) {
                printer.printRecord(headers.map { record[it] })
            }

            printer.flush()
            printer.close()
            writer.close()
        }

        val byteArray = outputStream.toByteArray()

        return CSVGenerationResponse(
            fileName = FAILED_LINES_CSV_FILE_NAME,
            bytes = byteArray
        )
    }
}

data class ResourceSpecialtyPricingCSV(
    val resourceBundleSpecialtyId: UUID,
    val medicalSpecialtyName: String,
    val aliceCode: String,
    val primaryTuss: String,
    val description: String,
    val executionEnvironment: HealthSpecialistProcedureExecutionEnvironment,
    val talentedT3: BigDecimal,
    val talentedT2: BigDecimal,
    val talentedT1: BigDecimal,
    val talentedT0: BigDecimal,
    val expertT2: BigDecimal,
    val expertT1: BigDecimal,
    val expertT0: BigDecimal,
    val superExpertT1: BigDecimal,
    val superExpertT0: BigDecimal,
    val ultraExpertT0: BigDecimal
)
