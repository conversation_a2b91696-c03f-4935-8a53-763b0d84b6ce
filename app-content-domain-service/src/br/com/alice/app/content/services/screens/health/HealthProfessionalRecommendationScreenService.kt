package br.com.alice.app.content.services.screens.health

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.Alignment
import br.com.alice.app.content.model.AppBar
import br.com.alice.app.content.model.BackgroundColor
import br.com.alice.app.content.model.Button
import br.com.alice.app.content.model.CardType
import br.com.alice.app.content.model.Label
import br.com.alice.app.content.model.MenuVariant
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.ScreenBackgroundType
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreenProperties
import br.com.alice.app.content.model.ScreenSafeArea
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.Tag
import br.com.alice.app.content.model.TagColorScheme
import br.com.alice.app.content.model.Variant
import br.com.alice.app.content.model.section.ButtonSection
import br.com.alice.app.content.model.section.CardSection
import br.com.alice.app.content.model.section.ExpandableCardVariant
import br.com.alice.app.content.model.section.HealthProfessionalInfo
import br.com.alice.app.content.model.section.HealthProfessionalInfoCardSection
import br.com.alice.app.content.model.section.HealthProfessionalInfoSection
import br.com.alice.app.content.model.section.LargeListCardSection
import br.com.alice.app.content.model.section.ListMenuSection
import br.com.alice.app.content.model.section.MenuSection
import br.com.alice.app.content.model.section.Metric
import br.com.alice.common.DistanceUtils
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.googlemaps.observability.GoogleMapsObservability.span
import br.com.alice.common.models.Gender
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.redis.GenericCache
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.ModalityType
import br.com.alice.data.layer.models.StructuredAddress
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.sortinghat.client.RoutingClient
import br.com.alice.sortinghat.models.input.HealthcareTeamModel
import br.com.alice.sortinghat.models.output.HealthcareTeamOutputModel
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.HealthProfessionalService.FindOptions
import br.com.alice.staff.client.HealthcareTeamFilters
import br.com.alice.staff.client.HealthcareTeamService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID
import kotlin.collections.map

class HealthProfessionalRecommendationScreenService(
    private val routingClient: RoutingClient,
    private val healthcareTeamService: HealthcareTeamService,
    private val appointmentService: AppointmentScheduleService,
    private val healthProfessionalService: HealthProfessionalService,
    private val personService: PersonService,
    private val cache: GenericCache
) {
    suspend fun getRecommendedPhysicians(personId: PersonId): Result<ScreensTransport, Throwable> =
        getHealthProfessionalRecommendationInfos(personId).map {
            buildRecommendationScreen(healthProfessionalRecommendationInfos = it)
        }

    suspend fun getRecommendedPhysicianDetails(
        personId: PersonId, healthcareTeamId: UUID,
        recommended: Boolean
    ): Result<ScreensTransport, Throwable> =
        span("getRecommendedPhysicianDetails") { span ->
            coroutineScope {
                span.setAttribute("person_id", personId)
                span.setAttribute("healthcare_team_id", healthcareTeamId)

                healthcareTeamService.get(healthcareTeamId)
                    .map { healthcareTeam ->
                        val personDeferred = async { personService.get(personId).getOrNull() }
                        val healthProfessionalDeferred = async { healthProfessionalService.findByStaffId(healthcareTeam.physicianStaffId, FindOptions(withStaff = false, withContact = true)).get() }
                        val appointmentCountByStaffDeferred = async {
                            appointmentService.countBy(
                                AppointmentScheduleFilter(
                                    staffId = healthcareTeam.physicianStaffId,
                                    status = listOf(AppointmentScheduleStatus.COMPLETED)
                                )
                            ).getOrNull()
                        }

                        buildRecommendationDetailsScreen(
                            healthProfessional = healthProfessionalDeferred.await(),
                            personAddress = personDeferred.await()?.mainAddress,
                            recommended = recommended,
                            appointmentCount = appointmentCountByStaffDeferred.await(),
                            healthcareTeam = healthcareTeam
                        )
                    }
            }
        }

    private suspend fun getHealthProfessionalRecommendationInfos(personId: PersonId): Result<List<HealthProfessionalRecommendationInfo>, Throwable> =
        span("getHealthProfessionalRecommendationInfos") { span ->
            coroutineScope {
                val cacheKey = "healthProfessionalRecommendationInfo:personId:${personId}"
                cache.getList(
                    key = cacheKey,
                    type = HealthProfessionalRecommendationInfo::class,
                    expirationTime = CACHE_EXPIRATION_TIME
                ) {
                    span.setAttribute("person_id", personId)
                    getRecommendedTeamsIds(personId).flatMap { healthcareTeamIds ->
                        span.setAttribute("recommended_healthcare_team_ids", healthcareTeamIds)
                        healthcareTeamService.findBy(
                            filters = HealthcareTeamFilters(ids = healthcareTeamIds, withAddress = true)
                        ).map { healthcareTeams ->
                            val staffIds = healthcareTeams.map { it.physicianStaffId }

                            val personDeferred = async { personService.get(personId).getOrNull() }
                            val healthProfessionalsDeferred = async {
                                healthProfessionalService.findByStaffIds(staffIds = staffIds).get()
                            }
                            val appointmentCountByStaffsDeferred = async {
                                appointmentService.countByStaffIds(
                                    staffIds = staffIds,
                                    status = listOf(AppointmentScheduleStatus.COMPLETED)
                                ).getOrNull()
                            }

                            buildHealthProfessionalRecommendationInfo(
                                healthcareTeams = healthcareTeams,
                                healthProfessionals = healthProfessionalsDeferred.await(),
                                appointmentCountByStaffs = appointmentCountByStaffsDeferred.await(),
                                personAddress = personDeferred.await()?.mainAddress
                            )
                        }
                    }.get()
                }.success()
            }
        }

    private suspend fun getRecommendedTeamsIds(personId: PersonId) =
        span("getRecommendedTeams") { span ->
            routingClient.execute<HealthcareTeamOutputModel>(
                HealthcareTeamModel(
                    id = personId.toString(),
                    personId = personId
                )
            ).map { output ->
                output.take(3).map { it.id.toUUID() }
            }
        }

    private fun buildRecommendationScreen(
        healthProfessionalRecommendationInfos: List<HealthProfessionalRecommendationInfo>
    ) = ScreensTransport(
        id = "healthcare_team_recommendations",
        layout = ScreenLayout(
            type = "single_column",
            appBar = AppBar(
                back = "",
                title = "Escolher meu médico",
            ),
            backgroundType = ScreenBackgroundType.ACTIVE,
            body = listOf(
                Section(
                    id = "large_card_list_healthcare_team_section",
                    type = SectionType.LARGE_LIST_CARD_SECTION,
                    minAppVersion = SectionType.LARGE_LIST_CARD_SECTION.minAppVersion!!,
                    data = LargeListCardSection(
                        body = buildRecommendationScreenBody(healthProfessionalRecommendationInfos)
                    )
                )
            )
        )
    )

    private fun buildRecommendationScreenBody(
        healthProfessionalRecommendationInfos: List<HealthProfessionalRecommendationInfo>
    ): List<Section> =
        healthProfessionalRecommendationInfos.mapIndexed { index, healthProfessionalRecommendationInfo ->
            val isRecommended = index == 0
            Section(
                id = "healthcare_team_physician",
                type = SectionType.CARD_SECTION,
                minAppVersion = SectionType.CARD_SECTION.minAppVersion!!,
                data = HealthProfessionalInfoCardSection(
                    healthProfessionalInfo = HealthProfessionalInfo(
                        id = healthProfessionalRecommendationInfo.healthProfessional.id,
                        avatarUrl = healthProfessionalRecommendationInfo.healthProfessional.imageUrl,
                        name = healthProfessionalRecommendationInfo.healthProfessional.name,
                        council = healthProfessionalRecommendationInfo.healthProfessional.council.toString(),
                        tag = isRecommended.takeIf { it }
                            ?.let { Tag(text = "Recomendado para você", colorScheme = TagColorScheme.MAGENTA) }
                    ),
                    expandableCardVariant = ExpandableCardVariant(
                        metrics = buildMetrics(
                            appointmentCount = healthProfessionalRecommendationInfo.appointmentCount,
                            distanceToMember = healthProfessionalRecommendationInfo.distanceToMember
                        ),
                        allowsExpansion = false,
                        content = healthProfessionalRecommendationInfo.healthProfessional.education.takeIf { it.isNotEmpty() }
                            ?.joinToString(separator = "\n")
                            ?: buildFallbackPhysicianInformation(healthProfessionalRecommendationInfo.healthProfessional)
                    ),
                    onCardClick = RemoteAction(
                        mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                        params = mapOf(
                            "method" to RemoteActionMethod.GET,
                            "endpoint" to "/app_content/screen/healthcare_team_details/${healthProfessionalRecommendationInfo.healthcareTeam.id}?recommended=$isRecommended"
                        )
                    )
                )
            )
        }

    private fun buildRecommendationDetailsScreen(
        healthProfessional: HealthProfessional,
        personAddress: Address?,
        recommended: Boolean,
        appointmentCount: Int?,
        healthcareTeam: HealthcareTeam
    ): ScreensTransport =
        ScreensTransport(
            id = "healthcare_team_details",
            properties = ScreenProperties(safeArea = ScreenSafeArea(bottom = true, top = false)),
            layout = ScreenLayout(
                type = "single_column",
                appBar = AppBar(back = ""),
                backgroundType = ScreenBackgroundType.ACTIVE,
                body = listOf(
                    buildRecommendedPhysicianInfo(healthProfessional, recommended),
                    buildRecommendedPhysicianMetrics(
                        appointmentCount = appointmentCount,
                        healthcareTeamAddress = healthcareTeam.address,
                        personAddress = personAddress,
                        healthProfessional = healthProfessional
                    ),
                    buildRecommendedPhysicianMenu(healthProfessional)
                ),
                footer = buildRecommendedPhysicianFooter(healthProfessional.name, healthcareTeam.id)
            )
        )

    private fun buildRecommendedPhysicianInfo(healthProfessional: HealthProfessional, recommended: Boolean) =
        Section(
            id = "health_professional_info",
            type =  SectionType.HEALTH_PROFESSIONAL_INFO_SECTION,
            minAppVersion = SectionType.HEALTH_PROFESSIONAL_INFO_SECTION.minAppVersion!!,
            data = HealthProfessionalInfoSection(
                HealthProfessionalInfo(
                    id = healthProfessional.id,
                    avatarUrl = healthProfessional.imageUrl,
                    name = healthProfessional.name,
                    council = healthProfessional.council.toString(),
                    tag = if(recommended) Tag(text = "Recomendado para você", colorScheme = TagColorScheme.MAGENTA) else null
                )
            )
        )

    private fun buildRecommendedPhysicianMetrics(
        appointmentCount: Int?,
        healthcareTeamAddress: StructuredAddress?,
        personAddress: Address?,
        healthProfessional: HealthProfessional
    ) =
        Section(
            id = "health_professional_metrics",
            type = SectionType.CARD_SECTION,
            data = CardSection(
                type = CardType.EXPANDABLE,
                expandableCardVariant =  ExpandableCardVariant(
                    metrics = buildMetrics(
                        appointmentCount = appointmentCount,
                        distanceToMember = healthcareTeamAddress?.distanceInMetersTo(personAddress?.lat, personAddress?.lng)
                    ),
                    content = healthProfessional.education.takeIf { it.isNotEmpty() }
                        ?.joinToString(separator = "\n")
                        ?: buildFallbackPhysicianInformation(healthProfessional),
                    allowsExpansion = true,
                    trackingParameters = mapOf(
                        "id" to healthProfessional.staffId,
                        "name" to healthProfessional.name
                    ),
                    colorScheme = BackgroundColor.BACKGROUND_DEFAULT
                )
            ),
            minAppVersion = SectionType.CARD_SECTION.minAppVersion!!
        )

    private fun buildRecommendedPhysicianMenu(healthProfessional: HealthProfessional) =
        Section(
            id = "health_professional_menu",
            type = SectionType.LIST_MENU_SECTION,
            minAppVersion = SectionType.LIST_MENU_SECTION.minAppVersion!!,
            data = ListMenuSection(
                hasDivider = true,
                body = listOfNotNull(
                    healthProfessional.contacts?.firstOrNull { it.modality == ModalityType.REMOTE }
                        ?.let {
                            Section(
                                id = "remote",
                                type = SectionType.MENU_SECTION,
                                data = MenuSection(
                                    menuVariant = MenuVariant.EXPANDABLE,
                                    clickAffordance = false,
                                    icon = "video_on",
                                    label = "Consulta por vídeo",
                                    title = ""
                                ),
                                minAppVersion = SectionType.MENU_SECTION.minAppVersion!!
                            )
                        },
                    healthProfessional.contacts?.firstOrNull { it.modality == ModalityType.PRESENTIAL }?.address
                        ?.let {
                            Section(
                                id = "local",
                                type = SectionType.MENU_SECTION,
                                data = MenuSection(
                                    menuVariant = MenuVariant.EXPANDABLE,
                                    clickAffordance = false,
                                    icon = "local_pin",
                                    label = "Consulta presencial",
                                    title = it.formattedAddress()

                                ),
                                minAppVersion = SectionType.MENU_SECTION.minAppVersion!!
                            )
                        }
                )
            )
        )

    private fun buildRecommendedPhysicianFooter(healthProfessionalName: String, healthcareTeamId: UUID) =
        Section(
            id = "health_professional_select_button",
            type = SectionType.BUTTON_SECTION,
            minAppVersion = SectionType.BUTTON_SECTION.minAppVersion!!,
            data = ButtonSection(
                alignment = Alignment.LEFT,
                button = Button(
                    id = "health_professional_select_button",
                    label = Label(
                        text = "Escolher ${healthProfessionalName.split(" ").first()}"
                    ),
                    variant = Variant.PRIMARY,
                    shrinkWrap = false,
                    onTapAction = RemoteAction(
                        mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                        params = mapOf(
                            "method" to RemoteActionMethod.POST,
                            "endpoint" to "/person_clinical_account/${healthcareTeamId}"
                        )
                    )
                )
            )
        )

    private fun buildMetrics(
        appointmentCount: Int?,
        distanceToMember: Double?
    ): List<Metric> = listOfNotNull(
        appointmentCount?.let {
            Metric(
                value = it.toString(),
                label = "Consultas"
            )
        },
        distanceToMember?.let {
            Metric(
                value = DistanceUtils.formatDistance(it),
                label = "De você"
            )
        }
    )

    private fun buildHealthProfessionalRecommendationInfo(
        healthcareTeams: List<HealthcareTeam>,
        healthProfessionals: List<HealthProfessional>,
        appointmentCountByStaffs: List<CountByValues>?,
        personAddress: Address?
    ) =
        healthcareTeams.map { team ->
            HealthProfessionalRecommendationInfo(
                healthcareTeam = team,
                healthProfessional = healthProfessionals.first { it.staffId == team.physicianStaffId },
                appointmentCount = appointmentCountByStaffs?.firstOrNull { it.values.first() == team.physicianStaffId.toString() }?.count,
                distanceToMember = team.address?.distanceInMetersTo(personAddress?.lat, personAddress?.lng)
            )
        }

    private fun buildFallbackPhysicianInformation(healthProfessional: HealthProfessional): String =
        healthProfessional.let {
            val (treatment, vowel) = if (it.gender == Gender.FEMALE) "Dra." to "a" else "Dr." to "o"

            "$treatment ${it.name}, médic$vowel de família da Alice"
        }

    data class HealthProfessionalRecommendationInfo(
        val healthcareTeam: HealthcareTeam,
        val healthProfessional: HealthProfessional,
        val appointmentCount: Int?,
        val distanceToMember: Double?
    )

    companion object {
        const val CACHE_EXPIRATION_TIME = (60 * 60).toLong() //1h
    }
}
