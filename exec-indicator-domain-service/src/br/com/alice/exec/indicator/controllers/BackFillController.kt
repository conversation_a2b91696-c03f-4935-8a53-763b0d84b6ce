package br.com.alice.exec.indicator.controllers

import br.com.alice.common.Response
import br.com.alice.common.asyncLayer
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.toResponse
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.EXEC_INDICATOR_BACKFILL_SERVICE_NAME
import br.com.alice.data.layer.models.HealthEventLocationEnum
import br.com.alice.data.layer.models.HealthcareResourceGroupAssociation
import br.com.alice.data.layer.models.HealthcareResourceType
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.data.layer.models.TussProcedureSpecialty
import br.com.alice.data.layer.services.GuiaWithProceduresModelDataService
import br.com.alice.data.layer.services.MvAuthorizedProcedureModelDataService
import br.com.alice.data.layer.services.TotvsGuiaModelDataService
import br.com.alice.eventinder.client.HealthEventsService
import br.com.alice.exec.indicator.client.EitaHealthEventService
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleManagementService
import br.com.alice.exec.indicator.client.HealthcareBundleService
import br.com.alice.exec.indicator.client.HealthcareResourceGroupAssociationService
import br.com.alice.exec.indicator.client.HealthcareResourceGroupService
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.client.TussProcedureSpecialtyService
import br.com.alice.exec.indicator.converters.modelConverters.toTransport
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePatchRequest
import br.com.alice.exec.indicator.service.internal.InternalMvAuthorizedProcedureService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.mapError
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

class BackFillController(
    private val eitaHealthEventService: EitaHealthEventService,
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService,
    private val internalMvAuthorizedProcedureService: InternalMvAuthorizedProcedureService,
    private val mvAuthorizedProcedureDataService: MvAuthorizedProcedureModelDataService,
    private val healthEventsService: HealthEventsService,
    private val healthcareResourceService: HealthcareResourceService,
    private val healthcareResourceGroupAssociationService: HealthcareResourceGroupAssociationService,
    private val healthcareResourceGroupService: HealthcareResourceGroupService,
    private val guiaWithProceduresDataService: GuiaWithProceduresModelDataService,
    private val totvsGuiaDataService: TotvsGuiaModelDataService,
    private val healthcareBundleService: HealthcareBundleService,
    private val tussProcedureSpecialtyService: TussProcedureSpecialtyService,
    private val healthSpecialistResourceBundleManagementService: HealthSpecialistResourceBundleManagementService
) : Controller() {

    private suspend fun withBackFillEnvironment(func: suspend () -> Response) =
        asyncLayer {
            withRootServicePolicy(EXEC_INDICATOR_BACKFILL_SERVICE_NAME) {
                withUnauthenticatedTokenWithKey(EXEC_INDICATOR_BACKFILL_SERVICE_NAME) {
                    func.invoke()
                }
            }
        }

    suspend fun syncUncoordinatedProcedures(request: BackFillAuthorizeProceduresRequest): Response =
        withBackFillEnvironment {
            logger.info("BackFillController#syncUncoordinatedProcedures", "request" to request)

            healthEventsService.findByOriginReferenceLocation(
                HealthEventLocationEnum.TOTVS_GUIA,
                request.limit,
                request.offset
            ).mapEach { healthEvent ->
                logger.info("BackFillController - HealthEvent", "health_event" to healthEvent)

                val totvsGuiaId = healthEvent.originReferences?.first()?.id

                mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuiaId!!.toUUID()).map { procedures ->
                    logger.info("BackFillController - Procedures", "procedures" to procedures)

                    val procedureId = healthEvent.procedureIds?.first() ?: "0000000"
                    val proceduresToUpdate = procedures
                        .filter { it.procedureId == procedureId }
                        .map { it.copy(healthEventId = healthEvent.id) }

                    if (proceduresToUpdate.isNotEmpty()) {
                        mvAuthorizedProcedureService.updateList(proceduresToUpdate)
                    }
                }
            }.foldResponse()
        }

    suspend fun backFillUnSyncHealthEventsByRange(request: BackFillUnSyncHealthEventsRequest): Response =
        withBackFillEnvironment {
            logger.info(
                "Start findUnSyncProceduresByRange",
                "start" to request.start,
                "end" to request.end
            )

            val procedures = mvAuthorizedProcedureService.findUnSyncProceduresByRange(
                LocalDateTime.of(request.start, LocalTime.MIN),
                LocalDateTime.of(request.end, LocalTime.MAX)
            ).get()

            val groups = procedures.groupBy { procedure -> procedure.executionGroupId }

            logger.info(
                "Start findUnSyncProceduresByRange: total procedures and groups",
                "total_procedures" to procedures.size,
                "total_groups" to groups.size
            )

            groups.map {
                eitaHealthEventService.publishExecutedProceduresHealthEvent(it.value, it.key!!).get()
            }

            logger.info(
                "End findUnSyncProceduresByRange successful"
            )
            true.toResponse()
        }

    suspend fun backFillAuthorizeProcedures(request: BackFillAuthorizeProceduresRequest): Response =
        withBackFillEnvironment {
            val procedures = mvAuthorizedProcedureDataService.find {
                where {
                    this.status.eq(MvAuthorizedProcedureStatus.PENDING)
                }
                    .sortOrder { SortOrder.Descending }
                    .limit { request.limit }
                    .offset { request.offset }
            }.get()

            logger.info(
                "Starting procedures authorization",
                "request" to request
            )

            procedures.map {
                try {
                    internalMvAuthorizedProcedureService.criticizeProcedure(it.toTransport())
                } catch (ex: Exception) {
                    logger.error("Procedure not found")
                }

            }

            logger.info("Procedures authorization finished")

            true.toResponse()
        }

    suspend fun backfillResourceGroupAssociations(
        backfillGroupAssociationsRequest: BackfillGroupAssociationsRequest
    ) = withBackFillEnvironment {
        logger.info(
            "BackfillGroupAssociationsRequest",
            "request" to backfillGroupAssociationsRequest
        )

        backfillGroupAssociationsRequest.codes.chunked(50).map { codes ->
            val group = healthcareResourceGroupService.getByNameAndType(
                backfillGroupAssociationsRequest.groupName,
                backfillGroupAssociationsRequest.groupType
            ).get()

            healthcareResourceService.findByTussCodes(codes).flatMap { healthcareResources ->
                healthcareResourceGroupAssociationService.upsertList(
                    healthcareResources.map { healthcareResource ->
                        HealthcareResourceGroupAssociation(
                            healthcareResourceId = healthcareResource.id,
                            healthcareResourceGroupId = group.id,
                        )
                    }
                )
            }.map {
                Response.OK
            }.mapError {
                logger.error("Error on backfillResourceGroupAssociations", it)
                it
            }.get()
        }

        return@withBackFillEnvironment Response.OK
    }

    suspend fun backfillTussCodeAssociations(
        backfillTussCodeAssociationsRequest: BackfillTussCodeAssociationsRequest
    ) = withBackFillEnvironment {
        logger.info(
            "BackfillTussCodeAssociationsRequest",
            "request" to backfillTussCodeAssociationsRequest
        )

        backfillTussCodeAssociationsRequest.associations.chunked(50).map { associations ->
            healthcareResourceService.findByTussCodes(associations.map { it.code }).flatMap { healthcareResources ->
                val healthcareResourcesUpdated = associations.mapNotNull { association ->
                    healthcareResources.find { it.code == association.code && it.tableType == association.tableType }
                        .let { healthcareResource ->
                            healthcareResource?.copy(
                                tussCode = association.tussCode,
                                tussTableType = association.tussTableType,
                            )
                        }
                }
                healthcareResourceService.updateList(healthcareResourcesUpdated, true)
            }.map {
                Response.OK
            }.mapError {
                logger.error("Error on backfillTussCodeAssociations", it)
                it
            }.get()
        }

        return@withBackFillEnvironment Response.OK
    }

    suspend fun backfillExecuteOldPreExecutedProcedures() = withBackFillEnvironment {
        logger.info("BackfillExecuteOldPreExecutedProcedures")

        val procedures = mvAuthorizedProcedureDataService.find {
            where {
                this.status.eq(MvAuthorizedProcedureStatus.PRE_EXECUTED)
                    .and(this.executedAt.lessEq(LocalDateTime.now().minusMonths(1)))
            }
        }.get()

        logger.info("BackfillExecuteOldPreExecutedProcedures - procedures", "procedures" to procedures)

        procedures.map { it.copy(status = MvAuthorizedProcedureStatus.EXECUTED) }.chunked(50).map {
            mvAuthorizedProcedureDataService.updateList(it)
        }

        logger.info("BackfillExecuteOldPreExecutedProcedures finished")

        true.toResponse()
    }

    suspend fun backfillGuiaWithProceduresFillStatusAddIdProcedure(request: BackFillAuthorizeProceduresRequest) = withBackFillEnvironment {
        logger.info("BackfillGuiaWithProceduresFillStatusAddIdProcedure",
            "request" to request
        )

        val guias = guiaWithProceduresDataService.find {
            where { externalCode.isNotNull() }
                .orderBy { createdAt }.sortOrder { asc }
                .limit { request.limit }
                .offset { request.offset }
        }.get()

        logger.info("checking ${guias.size} guias...")

        guias.filter { it.procedures.any { p -> p.id == null } }.pmap { guia ->
            val tGuiaId = totvsGuiaDataService.findOne { where { externalCode.eq(guia.externalCode!!) } }.get().id

            guia.copy(
                closed = guia.procedures.all { it.status == MvAuthorizedProcedureStatus.EXECUTED },
                procedures = guia.procedures.map {
                    it.copy(
                        id = it.id ?: mvAuthorizedProcedureDataService.findOne {
                            where {
                                totvsGuiaId.eq(tGuiaId)
                                    .and(procedureId.eq(it.code!!))
                                    .and(personId.eq(guia.personId!!))
                            }
                        }.get().id
                    )}
        )}.also {
            logger.info("updating ${it.size} guias...")
            guiaWithProceduresDataService.updateList(it)
        }

        logger.info("BackfillGuiaWithProceduresFillStatusAddIdProcedure finished")

        true.toResponse()
    }

    suspend fun backfillRemoveDuplicatedPendingProceduresFromGuia(request: BackfillRemoveDuplicatedPendingProceduresFromGuiaRequest) = withBackFillEnvironment {
        logger.info("BackfillRemoveDuplicatedPendingProceduresFromGuia")

        val totvsGuias = totvsGuiaDataService.find {
            where { passcode.isNotNull().and(externalCode.isNotNull()) }
                .orderBy { this.createdAt }
                .sortOrder { desc }
                .limit { request.limit }
                .offset { request.offset }
        }.get()

        val duplicatedProcedures = mvAuthorizedProcedureDataService.find {
            where {
                totvsGuiaId.inList(totvsGuias.map { it.id })
                    .and(status.eq(MvAuthorizedProcedureStatus.PENDING))
            }
        }.get()

        logger.info("deleting ${duplicatedProcedures.size} procedures...")

        duplicatedProcedures.pmap {
            mvAuthorizedProcedureDataService.delete(it)
                .thenError { logger.error("Error deleting procedure", "procedure" to it) }
        }

        logger.info("BackfillRemoveDuplicatedPendingProceduresFromGuia finished",
            "deletedProcedures" to duplicatedProcedures
        )

        true.toResponse()
    }

    suspend fun backfillNullCodesOfBundles() = withBackFillEnvironment {
        logger.info("backfillNullCodesOfBundles")

        healthcareBundleService.getBundlesWithEmptyCode().flatMap {
            it.pmap { bundle ->
                healthcareBundleService.update(
                    bundle.copy(code = null)
                )
            }
            true.success()
        }.foldResponse()
    }

    suspend fun resetHealthSpecialistResourceBundleIdOfTussProcedureSpecialty(
        request: BackfillTussProcedureSpecialtyRequest
    ) = withBackFillEnvironment {
        logger.info("resetEndDateOfTussProcedureSpecialty", "request" to request)

        tussProcedureSpecialtyService.getByIds(request.ids).flatMap { tussProcedureSpecialties ->
            tussProcedureSpecialties.chunked(50).map {
                tussProcedureSpecialtyService.updateList(
                    it.map { tussProcedureSpecialty ->
                        tussProcedureSpecialty.copy(
                            healthSpecialistResourceBundleId = null
                        )
                    }
                )
            }
            true.success()
        }.foldResponse()
    }

    suspend fun populateAuthorizedAt() = withBackFillEnvironment {
        logger.info("populateAuthorizedAt :: begin process")
        mvAuthorizedProcedureDataService.find {
            where { this.authorizedAt.isNull() and this.authorizationRequestedAt.isNotNull() }
                .orderBy { createdAt }
                .sortOrder { desc }
                .limit { 50 }
        }.map { procedures ->
            procedures
                .map { mvAuthorizedProcedureDataService.update(it.copy(authorizedAt = it.authorizationRequestedAt)) }
                .toResponse()
        }.get()
    }

    suspend fun populateExecutedAt() = withBackFillEnvironment {
        logger.info("populateExecutedAt :: begin process")
        mvAuthorizedProcedureDataService.find {
            where { this.executedAt.isNull() and this.executionRequestedAt.isNotNull() }
                .orderBy { createdAt }
                .sortOrder { desc }
                .limit { 50 }
        }.map { procedures ->
            procedures
                .map { mvAuthorizedProcedureDataService.update(it.copy(executedAt = it.executionRequestedAt)) }
                .toResponse()
        }.get()
    }

    suspend fun cleanCompositionHashOfHealthcareResources(
        request: BackfillCompositionHashOfResourcesRequest
    ) = withBackFillEnvironment {
        logger.info("updateCompositionHashOfHealthcareResource")

        healthcareResourceService.getByIds(request.healthcareResourceIds).flatMap { resources ->
            resources.chunked(50).map {
                healthcareResourceService.updateList(
                    it.map { resource ->
                        resource.copy(compositionHash = null)
                    },
                    true
                )
            }
            true.success()
        }.foldResponse()
    }

    suspend fun populateBundlePrimaryTussResourceId(
        request: BackfillPopulateBundlePrimaryTussResourceIdsRequest
    ): Response {
        logger.info("populateBundlePrimaryTussResourceId", "request" to request)

        val bundles = healthcareBundleService.getByIds(
            request.primaryTussIdAssociations.map { it.healthcareBundleId }
        ).get()

        val bundlesMap = bundles.associateBy { it.id }

        val updatedBundles = request.primaryTussIdAssociations.mapNotNull { primaryTussResourceIdForBundle ->
            val bundle = bundlesMap[primaryTussResourceIdForBundle.healthcareBundleId]

            bundle?.copy(primaryTussHealthcareResourceId = primaryTussResourceIdForBundle.healthcareResourceId)
        }

        updatedBundles.chunked(50).map {
            healthcareBundleService.updateList(it)
        }

        return Response.OK
    }


    private fun List<Result<Any, Throwable>>.toResponse(): Response {
        return this.success().map { response ->

            val resultSuccess = response.count { it is Result.Success }
            val resultFailure = response.count { it is Result.Failure }

            BackfillResponse(
                quantitySuccess = resultSuccess,
                quantityFailure = resultFailure
            )
        }.then {
            logger.info(
                "Backfill BackFillController",
                "response" to it
            )
        }.foldResponse()
    }

    suspend fun backfillResourceBundleSpecialty(request: BackFillResourceBundleSpecialtyRequest) = withBackFillEnvironment {
        val results = tussProcedureSpecialtyService.findActiveByHealthSpecialistResourceBundleIds(request.ids)
            .map { tussProcedureSpecialties -> tussProcedureSpecialties.groupBy { it.healthSpecialistResourceBundleId } }
            .map { resource -> resource.map {
                    processCode(it.key, it.value)
                        .fold(
                            { null },
                            { error -> "${it.key} - ${error.message}" }
                        )
                }
            }
            .get()

        results
            .filterNotNull()
            .toResponse()
    }

    private suspend fun processCode(healthId: UUID?, tuss: List<TussProcedureSpecialty>): Result<Any, Throwable> {
        if (healthId == null) {
            logger.error("backfillResourceBundleSpecialty - processCode - healthIds is null")
            return true.success()
        }

        val medicalSpecialtyIds = tuss
            .mapNotNull { it.medicalSpecialtyId }
            .distinct()

        return healthSpecialistResourceBundleManagementService.update(
            healthId!!,
            HealthSpecialistResourceBundlePatchRequest(
                medicalSpecialtyIds = medicalSpecialtyIds,
            )
        )
    }
}

data class BackFillResourceBundleSpecialtyRequest(
    val ids: List<UUID>
)

data class BackFillAuthorizeProceduresRequest(
    val offset: Int,
    val limit: Int
)

data class BackfillRemoveDuplicatedPendingProceduresFromGuiaRequest(
    val offset: Int,
    val limit: Int
)

data class BackfillCreateSpecialistBundleForTussProcedureSpecialtyRequest(
    val ids: List<UUID>
)

data class BackfillTussCodeAssociationsRequest(
    val associations: List<BackfillTussCodeAssociationRequest>
)

data class BackfillTussCodeAssociationRequest(
    val code: String,
    val tableType: String,
    val tussTableType: String,
    val tussCode: String,
)

data class BackfillGroupAssociationsRequest(
    val groupName: String,
    val codes: List<String>,
    val groupType: HealthcareResourceType,
)

data class BackFillUnSyncHealthEventsRequest(
    val start: LocalDate,
    val end: LocalDate
)

data class BackfillCompositionHashOfResourcesRequest(
    val healthcareResourceIds: List<UUID>
)

data class BackfillPopulateBundlePrimaryTussResourceIdsRequest(
    val primaryTussIdAssociations: List<PrimaryTussResourceIdForBundle>
)

data class PrimaryTussResourceIdForBundle(
    val healthcareResourceId: UUID,
    val healthcareBundleId: UUID
)

data class BackfillTussProcedureSpecialtyRequest(
    val ids: List<UUID>,
    val date: LocalDate? = LocalDate.now(),
)
