package br.com.alice.member.api.controllers.onboarding

import br.com.alice.app.content.client.screens.MemberOnboardingScreeService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType
import br.com.alice.data.layer.models.MemberOnboardingFlowType
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.marauders.map.client.RiskService
import br.com.alice.member.api.builders.MemberOnboardingsScreenBuilder
import br.com.alice.member.api.converters.MemberOnboardingProductConverter
import br.com.alice.member.api.converters.appContent.ScreenResponseConverter
import br.com.alice.member.api.converters.onboarding.ConclusionMemberOnboardingConverter
import br.com.alice.member.api.converters.onboarding.MemberOnboardingConverter
import br.com.alice.member.api.models.currentAppVersion
import br.com.alice.member.api.services.AppState.MEMBER_ONBOARDING_V2
import br.com.alice.member.api.services.AppState.REDESIGN_UNIFIED_HEALTH
import br.com.alice.member.api.services.AppState.UNIFIED_HEALTH
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.member.api.services.MemberOnboardingConclusionService
import br.com.alice.member.api.services.MemberProductService
import br.com.alice.member.onboarding.client.MemberOnboardingService
import br.com.alice.member.onboarding.factory.AliceExperienceTemplateFactory
import br.com.alice.member.onboarding.factory.AliceExperienceTemplateV2Factory
import br.com.alice.member.onboarding.factory.OnboardingCoverTemplateFactory
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.Parameters
import java.util.UUID

class MemberOnboardingV2Controller(
    private val personService: PersonService,
    private val memberProductService: MemberProductService,
    private val memberOnboardingService: MemberOnboardingService,
    private val memberService: MemberService,
    private val riskService: RiskService,
    private val memberOnboardingConclusionService: MemberOnboardingConclusionService,
    private val beneficiaryService: BeneficiaryService,
    private val memberOnboardingScreeService: MemberOnboardingScreeService
) : Controller() {

    suspend fun getProductDetails(): Response {
        val personId = currentUid().toPersonId()

        return personService.get(personId)
            .flatMapPair { memberProductService.getProductDetails(personId) }
            .map { (productDetails, person) ->
                MemberOnboardingProductConverter.convert(person, productDetails)
            }
            .foldResponse()
    }

    suspend fun getMemberOnboarding(): Response {
        val personId = currentUid().toPersonId()

        return memberService.getCurrent(personId).flatMap { member ->
            memberOnboardingService.getByPersonIdCheckingSteps(personId, member.active)
                .flatMapPair {
                    memberOnboardingService.getOnboardingVersion(personId)
                }.map { (version, memberOnboarding) ->
                    val aliceInfoVersion = memberOnboardingService.getAliceInfoVersion(personId)
                    MemberOnboardingConverter.convert(
                        source = memberOnboarding,
                        isMemberActive = member.active,
                        version = version,
                        aliceInfoVersion = aliceInfoVersion.getOrNull(),
                        memberActivationDate = member.activationDate ?: memberOnboarding.createdAt
                    )
                }
        }.foldResponse()
    }

    fun getAliceExperience(): Response = AliceExperienceTemplateFactory.build().toResponse()

    fun getAliceExperienceV2(): Response = AliceExperienceTemplateV2Factory.build().toResponse()

    suspend fun getHealthDeclarationCover(): Response =
        memberOnboardingService.getByPersonId(currentUid().toPersonId())
            .flatMapPair { personService.get(it.personId) }
            .map { (person, memberOnboarding) ->
                OnboardingCoverTemplateFactory.build(memberOnboarding.flowType, person.firstName)
            }.foldResponse()

    suspend fun finishStep(memberOnboardingId: UUID, stepType: String): Response =
        memberOnboardingService.updateStepStatus(memberOnboardingId, MemberOnboardingStepType.valueOf(stepType))
            .flatMap { memberOnboarding ->
                memberService.getCurrent(memberOnboarding.personId)
                    .flatMapPair {
                        memberOnboardingService.getOnboardingVersion(memberOnboarding.personId)
                    }.map { (version, member) ->
                        val appState = when (MemberOnboardingStepType.valueOf(stepType)) {
                            MemberOnboardingStepType.CONCLUSION -> arrayOf(UNIFIED_HEALTH, REDESIGN_UNIFIED_HEALTH)
                            else -> arrayOf(MEMBER_ONBOARDING_V2, UNIFIED_HEALTH, REDESIGN_UNIFIED_HEALTH)
                        }

                        AppStateNotifier.updateAppState(
                            memberOnboarding.personId,
                            *appState
                        )
                        MemberOnboardingConverter.convert(
                            source = memberOnboarding,
                            isMemberActive = member.active,
                            version = version,
                            memberActivationDate = member.activationDate ?: memberOnboarding.createdAt
                        )
                    }
            }.foldResponse()

    suspend fun getConclusion(): Response {
        val personId = currentUid().toPersonId()

        return memberOnboardingService.getByPersonId(personId)
            .flatMapPair { memberOnboardingConclusionService.getTasks(memberOnboarding = it) }
            .flatMap { (tasks, memberOnboarding) ->
                val (actionPlanTasks, healthPlanTaskTemplates) = tasks

                val physicianStaff = actionPlanTasks.firstOrNull()?.let {
                    memberOnboardingConclusionService.getPhysicianStaff(it.lastRequesterStaffId).getOrNull()
                }

                val firstName = memberOnboarding.flowType.takeIf { it == MemberOnboardingFlowType.CHILD }?.let {
                    personService.get(personId).getOrNull()?.firstName
                }

                val beneficiary = beneficiaryService.findByPersonId(personId = personId)
                    .getOrNullIfNotFound()

                val memberStatus = beneficiary?.memberStatus ?: memberService.getCurrent(personId).get().status

                val isTarget = riskService.getByPerson(personId)
                    .map { it.isTarget() }
                    .getOrNullIfNotFound()
                    ?: false

                ConclusionMemberOnboardingConverter.convert(
                    actionPlanTasks = actionPlanTasks,
                    healthPlanTaskTemplates = healthPlanTaskTemplates,
                    isTarget = isTarget,
                    activationDate = beneficiary?.activatedAt,
                    physicianStaff = physicianStaff,
                    flowType = memberOnboarding.flowType,
                    firstName = firstName,
                    isMemberActive = memberStatus == MemberStatus.ACTIVE
                ).success()
            }
            .foldResponse()
    }

    suspend fun getCallToComplete(): Response =
        currentAppVersion().let {
            memberOnboardingScreeService.getCallToComplete(it)
                .foldResponse(ScreenResponseConverter::convert)
        }

    suspend fun getEmptyScreenMFC(): Response =
        memberService.getCurrent(currentUid().toPersonId()).map {
            MemberOnboardingsScreenBuilder.buildScreenEmptyMFC(it.activationDate!!)
        }.foldResponse(ScreenResponseConverter::convert)

    suspend fun getRecommendedPhysicians(): Response =
        memberOnboardingScreeService.getRecommendedPhysicians(currentUid().toPersonId())
            .foldResponse(ScreenResponseConverter::convert)

    suspend fun getRecommendedPhysicianDetails(healthcareTeamId: UUID, queryParams: Parameters): Response =
        memberOnboardingScreeService.getRecommendedPhysicianDetails(
            personId = currentUid().toPersonId(),
            healthcareTeamId = healthcareTeamId,
            recommended = queryParams["recommended"]?.toBoolean() == true
        ).foldResponse(ScreenResponseConverter::convert)

    fun getMFCCover(): Response = OnboardingCoverTemplateFactory.buildMFCCover().toResponse()
}
