package br.com.alice.schedule.model

import br.com.alice.authentication.UserType
import br.com.alice.common.core.Status
import br.com.alice.data.layer.models.AgeRatingType
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeLocation
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeMemberRisk
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.HealthcareModelType
import br.com.alice.data.layer.models.Weekday
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

data class AppointmentScheduleEventTypeWithProviderUnits(
    val id: UUID,
    val title: String,
    val specialtyId: UUID? = null,
    val subSpecialtyId: UUID? = null,
    val subSpecialtyIds: List<UUID>? = null,
    val showOnApp: Boolean,
    val category: AppointmentScheduleType,
    val duration: Int,
    val locationType: AppointmentScheduleEventTypeLocation,
    val description: String? = null,
    val userType: UserType = UserType.MEMBER,
    val status: Status = Status.ACTIVE,
    val searchTokens: String? = null,
    val minimumTimeToScheduleBeforeAppointmentTime: Int = 1,
    val healthcareModelType: HealthcareModelType,
    val isMultiProfessionalReferral: Boolean = false,
    val numberOfDaysFromNowToAllowScheduling: Int = 90,
    val internalObservation: String? = null,
    val membersRisk: List<AppointmentScheduleEventTypeMemberRisk> = emptyList(),
    val providerUnitIds: List<UUID>,
    val groupByType: AppointmentScheduleType,
    val availableWeekDays: List<Weekday> = Weekday.values().toList(),
    val locations: List<AppointmentScheduleEventTypeLocationAvailability>? = emptyList(),
    val forChildren: Boolean = false,
    val ageRating: AgeRatingType = AgeRatingType.BOTH,
    val lastUpdatedBy: UUID? = null,
    val updatedAt: LocalDateTime
)

data class AppointmentScheduleEventTypeLocationAvailability(
    val providerUnitId: UUID?,
    val type: AppointmentScheduleEventTypeLocation? = AppointmentScheduleEventTypeLocation.REMOTE,
    val duration: Int = 30,
    val minimumTimeToScheduleBeforeAppointmentTime: Int = 1,
    val numberOfDaysFromNowToAllowScheduling: Int = 90,
    val availableWeekDays: List<Weekday> = emptyList(),
    val availabilityStartTime: LocalTime? = null,
    val availabilityEndTime: LocalTime? = null,
    val providerUnitName: String? = null,
)
