package br.com.alice.api.scheduler.controllers.v1

import br.com.alice.api.scheduler.controllers.StaffController
import br.com.alice.api.scheduler.controllers.model.BulkStaffScheduleRequest
import br.com.alice.api.scheduler.controllers.model.BulkStaffScheduleUpsertResponse
import br.com.alice.api.scheduler.controllers.model.StaffScheduleEventTypeAssociationRequest
import br.com.alice.api.scheduler.controllers.model.StaffScheduleRequest
import br.com.alice.api.scheduler.controllers.model.StaffScheduleResponse
import br.com.alice.api.scheduler.controllers.model.StaffSchedulesLastUpdatedBy
import br.com.alice.api.scheduler.controllers.model.StaffSchedulesResponse
import br.com.alice.api.scheduler.controllers.model.toProviderResponse
import br.com.alice.api.scheduler.converters.StaffScheduleRequestConverter
import br.com.alice.api.scheduler.converters.StaffScheduleResponseConverter
import br.com.alice.common.Response
import br.com.alice.common.core.extensions.fromSaoPauloToUTCTimeZone
import br.com.alice.common.core.extensions.toBrazilianDateTimeFormat
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.coroutine.pmapNotNull
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.StaffSchedule
import br.com.alice.data.layer.models.StaffScheduleStatus
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.StaffScheduleService
import br.com.alice.schedule.model.exceptions.StaffScheduleOverlapException
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.util.date.WeekDay
import java.time.LocalTime
import java.util.UUID

class StaffScheduleController(
    private val staffScheduleService: StaffScheduleService,
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService,
    private val staffService: StaffService,
    private val providerUnitService: ProviderUnitService,
) : StaffController(staffService) {

    suspend fun getAll(staffId: String, queryParams: Parameters): Response =
        span("StaffScheduleController::getAll") { span ->
            span.setAttribute("staff_id", staffId)
            span.setAttribute("requester", currentStaffId())
            val appointmentScheduleEventTypeId = queryParams["appointmentScheduleEventTypeId"]
            span.setAttribute("appointment_schedule_event_type_id", appointmentScheduleEventTypeId)
            var lastStaffScheduleUpdated: StaffSchedule? = null

            staffScheduleService.getStaffSchedules(staffId.toUUID())
                .map { staffSchedules ->
                    lastStaffScheduleUpdated = staffSchedules.maxByOrNull { it.updatedAt }
                    staffSchedules.pmapNotNull { staffSchedule ->
                        if (isValidEventForStaffSchedule(appointmentScheduleEventTypeId, staffSchedule))
                            StaffScheduleResponseConverter.convert(staffSchedule)
                        else null
                    }
                }
                .map { staffSchedules -> staffSchedules.sortedBy { staffSchedule -> staffSchedule.startHour } }
                .map { staffSchedules ->
                    val providerUnitMap = getProviderUnitName(staffSchedules)

                    staffSchedules.map { staffSchedule ->
                        staffSchedule.toProviderResponse(providerUnitMap)
                    }
                }
                .map { staffSchedules ->
                    val updatedAt = lastStaffScheduleUpdated?.updatedAt
                    val lastUpdatedBy = lastStaffScheduleUpdated?.lastUpdatedBy
                    val lastUpdatedByStaff = lastUpdatedBy?.let { staffService.get(it).get() }

                    StaffSchedulesResponse(
                        staffSchedules = staffSchedules,
                        updatedAt = updatedAt?.toSaoPauloTimeZone()?.toBrazilianDateTimeFormat(),
                        lastUpdatedBy = lastUpdatedByStaff?.let {
                            StaffSchedulesLastUpdatedBy(
                                id = it.id,
                                name = it.fullName,
                            )
                        }
                    )
                }
                .foldResponse()
        }

    suspend fun get(id: String): Response {
        logger.info(
            "StaffScheduleController::get",
            "staff_schedule_id" to id,
            "requester" to currentStaffId()
        )
        return staffScheduleService.getStaffScheduleById(id.toUUID())
            .map { StaffScheduleResponseConverter.convert(it) }
            .foldResponse()
    }

    suspend fun create(staffId: String, request: StaffScheduleRequest): Response {
        logger.info(
            "StaffScheduleController::create",
            "staff_id" to staffId,
            "request" to request,
            "requester" to currentStaffId()
        )

        if (isInvalidTimeRange(request.startHour, request.untilHour)) {
            val startHour = request.startHour.toString()
            val untilHour = request.untilHour.toString()
            return Response(
                status = HttpStatusCode.BadRequest,
                message = "The end time ($untilHour) of a staff schedule must be after the start time ($startHour)"
            )
        }

        val weekDay = WeekDay.valueOf(request.weekDay)
        val staffSchedule = StaffScheduleRequestConverter.convert(request, staffId)

        return staffScheduleService.create(
            staffId.toUUID(),
            staffSchedule.startHour,
            staffSchedule.untilHour,
            weekDay,
            staffSchedule.providerUnitId,
            staffSchedule.alsoDigital,
            type = staffSchedule.type,
        ).fold(
            {
                val staffScheduleResponse = StaffScheduleResponseConverter.convert(it)
                val providerUnitMap = getProviderUnitName(listOf(staffScheduleResponse))
                staffScheduleResponse.toProviderResponse(providerUnitMap).toResponse()
            },
            { throwable ->
                logger.error(
                    "StaffScheduleController::create error",
                    throwable
                )
                if (throwable::class == StaffScheduleOverlapException::class) {
                    Response(HttpStatusCode.BadRequest, throwable.message.toString())
                } else {
                    Response(HttpStatusCode.InternalServerError)
                }
            })
    }

    suspend fun update(id: String, request: StaffScheduleRequest): Response {
        logger.info(
            "StaffScheduleController::update",
            "staff_schedule_id" to id,
            "request" to request,
            "requester" to currentStaffId()
        )

        if (isInvalidTimeRange(request.startHour, request.untilHour)) {
            val startHour = request.startHour.toString()
            val untilHour = request.untilHour.toString()
            return Response(
                status = HttpStatusCode.BadRequest,
                message = "The end time ($untilHour) of a staff schedule must be after the start time ($startHour)"
            )
        }

        return staffScheduleService.getStaffScheduleById(id.toUUID())
            .flatMap {
                staffScheduleService.update(
                    it.copy(
                        startHour = request.startHour.fromSaoPauloToUTCTimeZone(),
                        untilHour = request.untilHour.fromSaoPauloToUTCTimeZone(),
                    )
                )
            }.fold(
                {
                    StaffScheduleResponseConverter.convert(it).toResponse()
                },
                { throwable ->
                    logger.error(
                        "StaffScheduleController::update error",
                        throwable
                    )
                    if (throwable::class == StaffScheduleOverlapException::class) {
                        Response(HttpStatusCode.BadRequest, throwable.message.toString())
                    } else {
                        Response(HttpStatusCode.InternalServerError)
                    }
                })
    }

    suspend fun delete(id: UUID): Response {
        logger.info(
            "StaffScheduleController::delete",
            "staff_schedule_id" to id,
            "requester" to currentStaffId()
        )
        return staffScheduleService.delete(id, currentStaffId())
            .map { StaffScheduleResponseConverter.convert(it) }
            .thenError { throwable ->
                logger.error(
                    "StaffScheduleController::delete error",
                    throwable
                )
            }
            .foldResponse()
    }

    suspend fun bulkUpsert(staffId: String, request: BulkStaffScheduleRequest): Response {
        val toAdd = request.staffScheduleRequests
            .filter { it.id == null && isInvalidTimeRange(it.startHour, it.untilHour).not() }
            .map {
                StaffScheduleRequestConverter.convert(it, staffId, requesterId = currentStaffId())
            }
        val toUpdate = request.staffScheduleRequests
            .filter { it.id != null && isInvalidTimeRange(it.startHour, it.untilHour).not() }
            .map { StaffScheduleRequestConverter.convert(it, staffId, it.id!!.toString(), currentStaffId()) }

        logger.info(
            "-OpsLog- StaffScheduleController::bulkUpsert",
            "requester" to currentStaffId(),
            "staff_id" to staffId,
            "staff_schedules_requested" to request.staffScheduleRequests.size,
            "to_add" to toAdd.size,
            "to_update" to toUpdate.size,
            "changes" to request.staffScheduleRequests.toString()
        )

        val addedStaffSchedules = if (toAdd.isNotEmpty()) {
            staffScheduleService.addList(toAdd).fold(
                {
                    it
                },
                {
                    logger.error(
                        "StaffScheduleController::bulkUpsert error adding list",
                        it.message
                    )
                    emptyList()
                }
            )
        } else emptyList()
        val updatedStaffSchedules = if (toUpdate.isNotEmpty()) {
            staffScheduleService.updateList(toUpdate).fold(
                { it },
                {
                    logger.error(
                        "StaffScheduleController::bulkUpsert error updating list",
                        it.message
                    )
                    emptyList()
                }
            )
        } else emptyList()

        val notAddedStaffSchedules = toAdd.filter { toAddStaffSchedule ->
            addedStaffSchedules.find { addedStaffSchedule ->
                addedStaffSchedule.startHour == toAddStaffSchedule.startHour &&
                        addedStaffSchedule.untilHour == toAddStaffSchedule.untilHour &&
                        addedStaffSchedule.weekDay == toAddStaffSchedule.weekDay
            } == null
        }

        val notUpdatedStaffSchedules = toUpdate.filter { toUpdateStaffSchedule ->
            updatedStaffSchedules.find { updatedStaffSchedule ->
                updatedStaffSchedule.startHour == toUpdateStaffSchedule.startHour &&
                        updatedStaffSchedule.untilHour == toUpdateStaffSchedule.untilHour &&
                        updatedStaffSchedule.weekDay == toUpdateStaffSchedule.weekDay

            } == null
        }

        logger.info(
            "StaffScheduleController::bulkUpsert",
            "requester" to currentStaffId(),
            "created_amount" to addedStaffSchedules.size,
            "updated_amount" to updatedStaffSchedules.size,
        )

        return BulkStaffScheduleUpsertResponse(
            successfulUpsertedStaffSchedules = (addedStaffSchedules + updatedStaffSchedules).map {
                StaffScheduleResponseConverter.convert(it)
            },
            unsuccessfulUpsertedStaffSchedules = (notAddedStaffSchedules + notUpdatedStaffSchedules).map {
                StaffScheduleResponseConverter.convert(it)
            }
        ).toResponse()
    }

    suspend fun deleteAll(staffId: String): Response {
        logger.info(
            "-OpsLog- StaffScheduleController::deleteAll",
            "requester" to currentStaffId(),
            "staff_id" to staffId
        )
        return staffScheduleService.getStaffSchedules(staffId.toSafeUUID())
            .flatMap { staffSchedules ->
                staffScheduleService.updateList(
                    staffSchedules.pmap { it.copy(status = StaffScheduleStatus.INACTIVE) }
                )
            }.map { deletedStaffSchedules ->
                deletedStaffSchedules.pmap { StaffScheduleResponseConverter.convert(it) }
            }.foldResponse()
    }

    suspend fun updateStaffScheduleEventTypeAssociation(
        staffScheduleId: UUID,
        request: StaffScheduleEventTypeAssociationRequest,
    ): Response {
        logger.info(
            "-OpsLog- StaffScheduleController::updateStaffScheduleEventTypeAssociation",
            "requester" to currentStaffId(),
            "staff_schedule_id" to staffScheduleId,
            "event_type_id" to request.appointmentScheduleEventTypeId,
            "is_disassociation" to request.isDisassociation
        )
        return staffScheduleService.updateStaffScheduleEventTypeAssociation(
            appointmentScheduleEventTypeId = request.appointmentScheduleEventTypeId,
            staffScheduleId = staffScheduleId,
            isDisassociation = request.isDisassociation,
        ).then {
            appointmentScheduleEventTypeService.get(request.appointmentScheduleEventTypeId).flatMap {
                appointmentScheduleEventTypeService.update(it.copy(lastUpdatedBy = currentStaffId()))
            }
        }.foldResponse()
    }

    private fun isInvalidTimeRange(startHour: LocalTime, untilHour: LocalTime): Boolean {
        val startHourIsAfterUntilHour = startHour.isAfter(untilHour)
        val startAndUntilHoursAreEqual = startHour == untilHour
        val invalid = startHourIsAfterUntilHour || startAndUntilHoursAreEqual
        logger.info("StaffScheduleController::isInvalidTimeRange the end time ($untilHour) of a staff schedule must be after the start time ($startHour)")
        return invalid
    }

    private suspend fun isValidEventForStaffSchedule(
        appointmentScheduleEventTypeId: String?,
        staffSchedule: StaffSchedule
    ) = span("isValidEventForStaffSchedule") { span ->
        appointmentScheduleEventTypeId?.let {
            val isExceptionEventForThisStaffSchedule =
                staffSchedule.exceptionEventTypes.contains(appointmentScheduleEventTypeId.toUUID())
            span.setAttribute("staff_schedule", staffSchedule.id)
            span.setAttribute("is_exception_event_for_this_staff_schedule", isExceptionEventForThisStaffSchedule)
            isExceptionEventForThisStaffSchedule.not()
        } ?: true
    }

    private suspend fun getProviderUnitName(staffSchedules: List<StaffScheduleResponse>): Map<UUID, ProviderUnit> {
        val providerUnitIds = staffSchedules.mapNotNull { it.providerUnitId }.distinct()
        val providerUnitMap = takeIf { providerUnitIds.isNotEmpty() }?.let {
            providerUnitService.getByIds(providerUnitIds, false).get().associateBy { it.id }
        } ?: emptyMap()

        return providerUnitMap
    }


}
