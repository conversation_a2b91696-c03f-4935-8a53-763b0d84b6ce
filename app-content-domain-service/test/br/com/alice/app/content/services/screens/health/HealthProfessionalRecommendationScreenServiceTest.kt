package br.com.alice.app.content.services.screens.health

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.Alignment
import br.com.alice.app.content.model.AppBar
import br.com.alice.app.content.model.Button
import br.com.alice.app.content.model.CardType
import br.com.alice.app.content.model.Label
import br.com.alice.app.content.model.MenuVariant
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.ScreenBackgroundType
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreenProperties
import br.com.alice.app.content.model.ScreenSafeArea
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.Tag
import br.com.alice.app.content.model.TagColorScheme
import br.com.alice.app.content.model.Variant
import br.com.alice.app.content.model.section.ButtonSection
import br.com.alice.app.content.model.section.CardSection
import br.com.alice.app.content.model.section.ExpandableCardVariant
import br.com.alice.app.content.model.section.HealthProfessionalInfo
import br.com.alice.app.content.model.section.HealthProfessionalInfoCardSection
import br.com.alice.app.content.model.section.HealthProfessionalInfoSection
import br.com.alice.app.content.model.section.LargeListCardSection
import br.com.alice.app.content.model.section.ListMenuSection
import br.com.alice.app.content.model.section.MenuSection
import br.com.alice.app.content.model.section.Metric
import br.com.alice.app.content.services.screens.health.HealthProfessionalRecommendationScreenService.HealthProfessionalRecommendationInfo
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.redis.GenericCache
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.Contact
import br.com.alice.data.layer.models.ModalityType
import br.com.alice.data.layer.models.withContacts
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.sortinghat.client.RoutingClient
import br.com.alice.sortinghat.models.input.HealthcareTeamModel
import br.com.alice.sortinghat.models.output.HealthcareTeamOutputModel
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.HealthProfessionalService.FindOptions
import br.com.alice.staff.client.HealthcareTeamFilters
import br.com.alice.staff.client.HealthcareTeamService
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.reflect.KClass
import kotlin.test.Test

class HealthProfessionalRecommendationScreenServiceTest {
    private val routingClient: RoutingClient = mockk()
    private val healthcareTeamService: HealthcareTeamService = mockk()
    private val appointmentService: AppointmentScheduleService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val personService: PersonService = mockk()
    private val cache: GenericCache = mockk()

    private val healthProfessionalRecommendationScreenService = HealthProfessionalRecommendationScreenService(
        routingClient,
        healthcareTeamService,
        appointmentService,
        healthProfessionalService,
        personService,
        cache
    )

    private val personId: PersonId = PersonId()
    val healthcareTeam = TestModelFactory.buildHealthcareTeam(
        address = TestModelFactory.buildStructuredAddress(
            longitude = "-46.********",
            latitude = "-23.********"
        )
    )
    val healthProfessional = TestModelFactory.buildHealthProfessional(staffId = healthcareTeam.physicianStaffId)
    val person = TestModelFactory.buildPerson(personId = personId)

    @Test
    fun `#getRecommendedPhysicians - should return physician recommendation screen without cache`() = runBlocking {
        coEvery {
            cache.getList(
                "healthProfessionalRecommendationInfo:personId:${personId}",
                HealthProfessionalRecommendationInfo::class,
                expirationTime = 3600L,
                putFunction = any()
            )
        } coAnswers {
            arg<suspend () -> List<HealthProfessionalRecommendationInfo>>(4).invoke()
        }

        coEvery {
            routingClient.execute<HealthcareTeamOutputModel>(
                HealthcareTeamModel(id = personId.toString(), personId = personId)
            )
        } returns listOf(HealthcareTeamOutputModel(id = healthcareTeam.id.toString())).success()
        coEvery {
            healthcareTeamService.findBy(
                filters = HealthcareTeamFilters(ids = listOf(healthcareTeam.id), withAddress = true)
            )
        } returns listOf(healthcareTeam).success()
        coEvery { personService.get(personId) } returns person.success()
        coEvery {
            healthProfessionalService.findByStaffIds(staffIds = listOf(healthcareTeam.physicianStaffId))
        } returns listOf(healthProfessional).success()
        coEvery {
            appointmentService.countByStaffIds(
                staffIds = listOf(healthcareTeam.physicianStaffId),
                status = listOf(AppointmentScheduleStatus.COMPLETED)
            )
        } returns listOf(CountByValues(values = listOf(healthcareTeam.physicianStaffId.toString()), count = 80)).success()

        val expected = ScreensTransport(
            id = "healthcare_team_recommendations",
            layout = ScreenLayout(
                type = "single_column",
                appBar = AppBar(
                    back = "",
                    title = "Escolher meu médico",
                ),
                backgroundType = ScreenBackgroundType.ACTIVE,
                body = listOf(
                    Section(
                        id = "large_card_list_healthcare_team_section",
                        type = SectionType.LARGE_LIST_CARD_SECTION,
                        minAppVersion = SectionType.LARGE_LIST_CARD_SECTION.minAppVersion!!,
                        data = LargeListCardSection(
                            body = listOf(
                                Section(
                                    id = "healthcare_team_physician",
                                    type = SectionType.CARD_SECTION,
                                    minAppVersion = SectionType.CARD_SECTION.minAppVersion!!,
                                    data = HealthProfessionalInfoCardSection(
                                        healthProfessionalInfo = HealthProfessionalInfo(
                                            id = healthProfessional.id,
                                            avatarUrl = healthProfessional.imageUrl,
                                            name = healthProfessional.name,
                                            council = healthProfessional.council.let {
                                                "CRM ${it.number}/${it.state}"
                                            },
                                            tag = Tag(text = "Recomendado para você", colorScheme = TagColorScheme.MAGENTA)
                                        ),
                                        expandableCardVariant = ExpandableCardVariant(
                                            metrics = listOf(
                                                Metric(
                                                    value = "80",
                                                    label = "Consultas"
                                                ),
                                                Metric(
                                                    value = "0 m",
                                                    label = "De você"
                                                )
                                            ),
                                            allowsExpansion = false,
                                            content = "Dr. João, médico de família da Alice"
                                        ),
                                        onCardClick = RemoteAction(
                                            mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                                            params = mapOf(
                                                "method" to RemoteActionMethod.GET,
                                                "endpoint" to "/app_content/screen/healthcare_team_details/${healthcareTeam.id}?recommended=true"
                                            )
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
        )

        val result = healthProfessionalRecommendationScreenService.getRecommendedPhysicians(personId)
        ResultAssert.assertThat(result).isSuccessWithData(expected)

        coVerifyOnce {
            cache.getList(any(), any<KClass<*>>(), any(), any(), any())
            routingClient.execute<HealthcareTeamOutputModel>(any())
            healthcareTeamService.findBy(any())
            personService.get(any())
            healthProfessionalService.findByStaffIds(any())
            appointmentService.countByStaffIds(any(), any())
        }
    }

    @Test
    fun `#getRecommendedPhysicians - should return physician recommendation screen with cache`() = runBlocking {
        val healthProfessionalRecommendationInfo = HealthProfessionalRecommendationInfo(
            healthcareTeam, healthProfessional, 80, 0.0
        )

        coEvery {
            cache.getList(
                "healthProfessionalRecommendationInfo:personId:${personId}",
                HealthProfessionalRecommendationInfo::class,
                expirationTime = 3600L,
                putFunction = any()
            )
        } returns listOf(healthProfessionalRecommendationInfo)

        val expected = ScreensTransport(
            id = "healthcare_team_recommendations",
            layout = ScreenLayout(
                type = "single_column",
                appBar = AppBar(
                    back = "",
                    title = "Escolher meu médico",
                ),
                backgroundType = ScreenBackgroundType.ACTIVE,
                body = listOf(
                    Section(
                        id = "large_card_list_healthcare_team_section",
                        type = SectionType.LARGE_LIST_CARD_SECTION,
                        minAppVersion = SectionType.LARGE_LIST_CARD_SECTION.minAppVersion!!,
                        data = LargeListCardSection(
                            body = listOf(
                                Section(
                                    id = "healthcare_team_physician",
                                    type = SectionType.CARD_SECTION,
                                    minAppVersion = SectionType.CARD_SECTION.minAppVersion!!,
                                    data = HealthProfessionalInfoCardSection(
                                        healthProfessionalInfo = HealthProfessionalInfo(
                                            id = healthProfessional.id,
                                            avatarUrl = healthProfessional.imageUrl,
                                            name = healthProfessional.name,
                                            council = healthProfessional.council.let {
                                                "CRM ${it.number}/${it.state}"
                                            },
                                            tag = Tag(text = "Recomendado para você", colorScheme = TagColorScheme.MAGENTA)
                                        ),
                                        expandableCardVariant = ExpandableCardVariant(
                                            metrics = listOf(
                                                Metric(
                                                    value = "80",
                                                    label = "Consultas"
                                                ),
                                                Metric(
                                                    value = "0 m",
                                                    label = "De você"
                                                )
                                            ),
                                            allowsExpansion = false,
                                            content = "Dr. João, médico de família da Alice"
                                        ),
                                        onCardClick = RemoteAction(
                                            mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                                            params = mapOf(
                                                "method" to RemoteActionMethod.GET,
                                                "endpoint" to "/app_content/screen/healthcare_team_details/${healthcareTeam.id}?recommended=true"
                                            )
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
        )

        val result = healthProfessionalRecommendationScreenService.getRecommendedPhysicians(personId)
        ResultAssert.assertThat(result).isSuccessWithData(expected)

        coVerifyOnce {
            cache.getList(any(), any<KClass<*>>(), any(), any(), any())
        }

        coVerify {
            routingClient wasNot called
            healthcareTeamService wasNot called
            personService wasNot called
            healthProfessionalService wasNot called
            appointmentService wasNot called
        }
    }

    @Test
    fun `#getRecommendedPhysicianDetails - should return recommended physician details screen`() = runBlocking {
        val address = TestModelFactory.buildStructuredAddress()
        val contacts = listOf(
            Contact(modality = ModalityType.REMOTE),
            Contact(
                modality = ModalityType.PRESENTIAL,
                address = address
            )
        )
        val healthProfessional = healthProfessional.copy(name = "João Oliveira").withContacts(contacts)
        coEvery { healthcareTeamService.get(healthcareTeam.id) } returns healthcareTeam.success()
        coEvery { personService.get(personId) } returns person.success()
        coEvery {
            healthProfessionalService.findByStaffId(healthcareTeam.physicianStaffId, FindOptions(withStaff = false, withContact = true))
        } returns healthProfessional.success()
        coEvery {
            appointmentService.countBy(
                AppointmentScheduleFilter(
                    staffId = healthcareTeam.physicianStaffId,
                    status = listOf(AppointmentScheduleStatus.COMPLETED)
                )
            )
        } returns 80.success()

        val expected = ScreensTransport(
            id = "healthcare_team_details",
            properties = ScreenProperties(safeArea = ScreenSafeArea(bottom = true, top = false)),
            layout = ScreenLayout(
                type = "single_column",
                appBar = AppBar(back = ""),
                backgroundType = ScreenBackgroundType.ACTIVE,
                body = listOf(
                    Section(
                        id = "health_professional_info",
                        type = SectionType.HEALTH_PROFESSIONAL_INFO_SECTION,
                        minAppVersion = SectionType.HEALTH_PROFESSIONAL_INFO_SECTION.minAppVersion!!,
                        data = HealthProfessionalInfoSection(
                            HealthProfessionalInfo(
                                id = healthProfessional.id,
                                avatarUrl = healthProfessional.imageUrl,
                                name = healthProfessional.name,
                                council = healthProfessional.council.let {
                                    "CRM ${it.number}/${it.state}"
                                },
                                tag = Tag(text = "Recomendado para você", colorScheme = TagColorScheme.MAGENTA)
                            )
                        )
                    ),
                    Section(
                        id = "health_professional_metrics",
                        type = SectionType.CARD_SECTION,
                        data = CardSection(
                            type = CardType.EXPANDABLE,
                            expandableCardVariant = ExpandableCardVariant(
                                metrics = listOf(
                                    Metric(
                                        value = "80",
                                        label = "Consultas"
                                    ),
                                    Metric(
                                        value = "0 m",
                                        label = "De você"
                                    )
                                ),
                                allowsExpansion = true,
                                content = "Dr. João Oliveira, médico de família da Alice",
                                trackingParameters = mapOf(
                                    "id" to healthProfessional.staffId,
                                    "name" to healthProfessional.name
                                )
                            )
                        ),
                        minAppVersion = SectionType.CARD_SECTION.minAppVersion!!
                    ),
                    Section(
                        id = "health_professional_menu",
                        type = SectionType.LIST_MENU_SECTION,
                        minAppVersion = SectionType.LIST_MENU_SECTION.minAppVersion!!,
                        data = ListMenuSection(
                            hasDivider = true,
                            body = listOf(
                                Section(
                                    id = "remote",
                                    type = SectionType.MENU_SECTION,
                                    data = MenuSection(
                                        menuVariant = MenuVariant.EXPANDABLE,
                                        clickAffordance = false,
                                        icon = "video_on",
                                        label = "Consulta por vídeo",
                                        title = ""
                                    ),
                                    minAppVersion = SectionType.MENU_SECTION.minAppVersion!!
                                ),
                                Section(
                                    id = "local",
                                    type = SectionType.MENU_SECTION,
                                    data = MenuSection(
                                        menuVariant = MenuVariant.EXPANDABLE,
                                        clickAffordance = false,
                                        icon = "local_pin",
                                        label = "Consulta presencial",
                                        title = contacts[1].address!!.formattedAddress()
                                    ),
                                    minAppVersion = SectionType.MENU_SECTION.minAppVersion!!
                                )
                            )
                        )
                    )
                ),
                footer = Section(
                    id = "health_professional_select_button",
                    type = SectionType.BUTTON_SECTION,
                    minAppVersion = SectionType.BUTTON_SECTION.minAppVersion!!,
                    data = ButtonSection(
                        alignment = Alignment.LEFT,
                        button = Button(
                            id = "health_professional_select_button",
                            label = Label(text = "Escolher João"),
                            variant = Variant.PRIMARY,
                            shrinkWrap = false,
                            onTapAction = RemoteAction(
                                mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                                params = mapOf(
                                    "method" to RemoteActionMethod.POST,
                                    "endpoint" to "/person_clinical_account/${healthcareTeam.id}"
                                )
                            )
                        )
                    )
                )
            )
        )

        val result = healthProfessionalRecommendationScreenService.getRecommendedPhysicianDetails(personId, healthcareTeam.id, recommended = true)
        ResultAssert.assertThat(result).isSuccessWithData(expected)

        coVerifyOnce {
            healthcareTeamService.get(any())
            personService.get(any())
            healthProfessionalService.findByStaffId(any(),any())
            appointmentService.countBy(any())
        }
    }
}
