package br.com.alice.member.onboarding.factory

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.data.layer.models.MemberOnboardingFlowType
import br.com.alice.member.onboarding.MemberOnboardingClientConfiguration.memberApiUrl
import br.com.alice.member.onboarding.model.OnboardingCoverButton
import br.com.alice.member.onboarding.model.OnboardingCoverTemplate
import br.com.alice.member.onboarding.model.OnboardingCoverTemplateType
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

internal class OnboardingCoverTemplateFactoryTest {

    private val healthDeclaration = OnboardingCoverTemplate(
        type = OnboardingCoverTemplateType.HEALTH_DECLARATION,
        title = "Declaração de Saúde",
        description = "Essa declaração é para conhecer seu histórico de saúde. Essa etapa também contribui para a " +
                "definição do seu primeiro Plano de Ação.",
        imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/member_onboarding/images/v2/health_declaration_cover.png",
        agreement = "Vou responder a declaração de saúde de forma honesta e transparente. Sei que falsificar ou omitir" +
                " informações é fraude e pode resultar em sanções cíveis e criminais.",
        button = OnboardingCoverButton(
            label = "Começar",
            mobileRoute = ActionRouting.HEALTH_DECLARATION,
            params = mapOf("intro_only" to false)
        )
    )

    @Test
    fun `#build should build adult health declaration cover template`() {
        val template = OnboardingCoverTemplateFactory.build(MemberOnboardingFlowType.ADULT, firstName = "Maria")

        assertThat(template).isEqualTo(healthDeclaration)
    }

    @Test
    fun `#build should build child health declaration cover template`() {
        val childHealthDeclaration = healthDeclaration.copy(
            title = "Declaração de Saúde de Mariana",
            description = "Essa declaração é para conhecer o histórico de saúde do seu dependente. Essa etapa também " +
                    "contribui para a definição do primeiro Plano de Ação.",
        )

        val template = OnboardingCoverTemplateFactory.build(MemberOnboardingFlowType.CHILD, firstName = "Mariana")

        assertThat(template).isEqualTo(childHealthDeclaration)
    }

    @Test
    fun `#buildMFCCover should build mfc cover template`() {
        val template = OnboardingCoverTemplateFactory.buildMFCCover()

        val expected = OnboardingCoverTemplate(
            type = OnboardingCoverTemplateType.MFC,
            title = "Um médico pra te conhecer de verdade",
            description = "Na Alice, seu médico te acompanha de perto, cuidando de você de forma contínua. Agora, é hora de conhecer quem estará ao seu lado.",
            imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/member_onboarding/images/v2/mfc_cover.png",
            button = OnboardingCoverButton(
                label = "Escolher meu médico",
                mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                params = mapOf(
                    "action" to RemoteAction(
                        method = RemoteActionMethod.GET,
                        endpoint = memberApiUrl("/app_content/screen/healthcare_team_recommendations"),
                    )
                )
            )
        )

        assertThat(template).isEqualTo(expected)
    }
}
