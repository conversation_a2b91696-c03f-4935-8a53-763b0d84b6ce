package br.com.alice.data.layer.pipelines.subjects

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import org.junit.jupiter.api.Assertions.assertEquals
import kotlin.test.Test

class ChiefRiskSubjectTest {

    @Test
    fun testChiefRiskSubject() {
        val id = RangeUUID.generate()
        val role = Role.CHIEF_RISK
        val chiefRiskSubject = ChiefRiskSubject(id, role)

        assertEquals(id, chiefRiskSubject.id)
        assertEquals(role, chiefRiskSubject.role)
    }
}
