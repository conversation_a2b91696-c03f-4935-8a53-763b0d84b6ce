package br.com.alice.businessrisk.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.businessrisk.clients.MacoService
import br.com.alice.businessrisk.events.PersonContractualRiskCreatedEvent
import br.com.alice.businessrisk.exceptions.IncompleteCompanyInformationToCalculateMacoException
import br.com.alice.businessrisk.exceptions.NoValidBeneficiariesToCalculateMacoException
import br.com.alice.businessrisk.exceptions.StandardCostNotFoundForProductException
import br.com.alice.businessrisk.models.BeneficiaryMacoTransport
import br.com.alice.businessrisk.models.MacoCalculationTransport
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Adhesion
import br.com.alice.data.layer.models.FeatureNamespace
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.Test

class PersonContractualRiskConsumerTest : BaseConsumerTest() {
    companion object {
        @JvmStatic
        private fun exceptionsToSkip() = listOf(
            NoValidBeneficiariesToCalculateMacoException(RangeUUID.generate()),
            StandardCostNotFoundForProductException("123", Adhesion.OPTIONAL),
            IncompleteCompanyInformationToCalculateMacoException(RangeUUID.generate())
        )
    }

    private val companyService: CompanyService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val macoService: MacoService = mockk()

    private val consumer = PersonContractualRiskConsumer(
        companyService,
        beneficiaryService,
        macoService,
    )

    @Test
    fun `#should calculate the maco from company contract ids when some person contractual risk is created`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.BUSINESS_RISK, "should_calculate_maco", true) {
                val contractId = RangeUUID.generate()
                val company = TestModelFactory.buildCompany(contractIds = listOf(contractId))
                val beneficiary = TestModelFactory.buildBeneficiary(companyId = company.id)
                val companyContractMaco = TestModelFactory.buildCompanyContractMaco()
                val beneficiaryMaco1 = BeneficiaryMacoTransport(
                    TestModelFactory.buildBeneficiaryCompiledView(),
                    TestModelFactory.buildBeneficiaryMaco(),
                )
                val beneficiaryMaco2 = BeneficiaryMacoTransport(
                    TestModelFactory.buildBeneficiaryCompiledView(),
                    TestModelFactory.buildBeneficiaryMaco(),
                )

                val personContractualRisk = TestModelFactory.buildPersonContractualRisk(personId = beneficiary.personId)

                val event = PersonContractualRiskCreatedEvent(personContractualRisk)

                val macoCalculation =
                    MacoCalculationTransport(companyContractMaco, listOf(beneficiaryMaco1, beneficiaryMaco2))

                coEvery { companyService.get(company.id) } returns company
                coEvery { beneficiaryService.findByPersonId(beneficiary.personId) } returns beneficiary
                coEvery { macoService.calculate(contractId) } returns macoCalculation
                coEvery { macoService.saveMacos(macoCalculation) } returns macoCalculation

                val result = consumer.handleCalculateMaco(event)

                assertThat(result).isSuccess()

                coVerifyOnce {
                    beneficiaryService.findByPersonId(beneficiary.personId)
                    companyService.get(company.id)
                    macoService.calculate(contractId)
                    macoService.saveMacos(macoCalculation)
                }
            }
        }

    @ParameterizedTest(name = "#should return success when {0} is thrown")
    @MethodSource("exceptionsToSkip")
    fun skipMessagesWhenExpectedExceptions(exception: Exception) =
        runBlocking {
            withFeatureFlag(FeatureNamespace.BUSINESS_RISK, "should_calculate_maco", true) {
                val contractId = RangeUUID.generate()
                val company = TestModelFactory.buildCompany(contractIds = listOf(contractId))
                val beneficiary = TestModelFactory.buildBeneficiary(companyId = company.id)

                val personContractualRisk = TestModelFactory.buildPersonContractualRisk(personId = beneficiary.personId)

                val event = PersonContractualRiskCreatedEvent(personContractualRisk)

                coEvery { companyService.get(company.id) } returns company
                coEvery { beneficiaryService.findByPersonId(beneficiary.personId) } returns beneficiary
                coEvery { macoService.calculate(contractId) } throws exception

                val result = consumer.handleCalculateMaco(event)

                assertThat(result).isSuccessWithData(true)

                coVerifyOnce {
                    beneficiaryService.findByPersonId(beneficiary.personId)
                    companyService.get(company.id)
                    macoService.calculate(contractId)
                }
            }
        }

    @Test
    fun `#should return failure when unexpected exception is thrown`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.BUSINESS_RISK, "should_calculate_maco", true) {
                val contractId = RangeUUID.generate()
                val company = TestModelFactory.buildCompany(contractIds = listOf(contractId))
                val beneficiary = TestModelFactory.buildBeneficiary(companyId = company.id)

                val personContractualRisk = TestModelFactory.buildPersonContractualRisk(personId = beneficiary.personId)

                val event = PersonContractualRiskCreatedEvent(personContractualRisk)

                coEvery { companyService.get(company.id) } returns company
                coEvery { beneficiaryService.findByPersonId(beneficiary.personId) } returns beneficiary
                coEvery { macoService.calculate(contractId) } throws NullPointerException()

                val result = consumer.handleCalculateMaco(event)

                assertThat(result).isFailure()

                coVerifyOnce {
                    beneficiaryService.findByPersonId(beneficiary.personId)
                    companyService.get(company.id)
                    macoService.calculate(contractId)
                }
            }
        }

    @Test
    fun `#should skip the calculation of maco when the feature flag is disabled`() =
        runBlocking {
            val contractId = RangeUUID.generate()
            val company = TestModelFactory.buildCompany(contractIds = listOf(contractId))
            val beneficiary = TestModelFactory.buildBeneficiary(companyId = company.id)

            val personContractualRisk = TestModelFactory.buildPersonContractualRisk(personId = beneficiary.personId)

            val event = PersonContractualRiskCreatedEvent(personContractualRisk)

            val result = consumer.handleCalculateMaco(event)

            assertThat(result).isSuccess()

            coVerifyNone {
                beneficiaryService.findByPersonId(any())
                companyService.get(any())
                macoService.calculate(any())
                macoService.saveMacos(any())
            }
        }

    @Test
    fun `#should return auto retryable exception when beneficiary is not found`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.BUSINESS_RISK, "should_calculate_maco", true) {
                val contractId = RangeUUID.generate()
                val company = TestModelFactory.buildCompany(contractIds = listOf(contractId))
                val beneficiary = TestModelFactory.buildBeneficiary(companyId = company.id)

                val personContractualRisk = TestModelFactory.buildPersonContractualRisk(personId = beneficiary.personId)

                val event = PersonContractualRiskCreatedEvent(personContractualRisk)

                coEvery { companyService.get(company.id) } returns company
                coEvery { beneficiaryService.findByPersonId(beneficiary.personId) } returns NotFoundException()

                val result = consumer.handleCalculateMaco(event)

                assertThat(result).isFailureOfType(AutoRetryableException::class)

                coVerifyOnce {
                    beneficiaryService.findByPersonId(any())
                }
            }
        }
}
