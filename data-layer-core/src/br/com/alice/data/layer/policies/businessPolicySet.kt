package br.com.alice.data.layer.policies

import br.com.alice.data.layer.BUSINESS_ENVIRONMENT_BACKFILL
import br.com.alice.data.layer.BUSINESS_ENVIRONMENT_RECURRENT
import br.com.alice.data.layer.BUSINESS_ENVIRONMENT_SUBSCRIBERS
import br.com.alice.data.layer.BUSINESS_PLATFORM_BFF_NAME
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.CompanyActivationFilesModel
import br.com.alice.data.layer.models.CompanyStaff
import br.com.alice.data.layer.models.GenericFileVault
import br.com.alice.data.layer.models.StaffModel
import br.com.alice.data.layer.policies.features.*
import br.com.alice.data.layer.subjects.Unauthenticated

val businessPolicySet = policySet {
    match("at business-platform-bff", { rootService.name == BUSINESS_PLATFORM_BFF_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(viewCompanyStaff)
        }

        match("new CompanyStaff", { subject is CompanyStaff && subject.isMainCompanyStaff() }) {
            includes(viewCompanyStaff)
            includes(viewRefund)
            includes(viewProductComponents)
            includes(viewProviders)
            includes(viewPersonClinicalAccount)
            includes(viewAndUpdateBeneficiary)
            includes(viewBeneficiaryCompiledView)
            includes(createMemberFromBeneficiary)
            includes(viewAndUpdatePerson)
            includes(viewAndUpdateMember)
            includes(viewUpdateAndCreatePersonBillingAccountableParty)
            includes(createViewUpdateCompanyContractAndSubContract)
            includes(viewAndCreateAndUpdateCompanyActivationFiles)
            includes(viewAndCreateFileVaultCompanyActivationFiles)
            includes(viewAndCountItsOwnCompanyInvoices)
            includes(createInvoicePayment)
            includes(viewAndUpdateMemberProductChangeSchedule)
            includes(viewCreateUpdateDependentFiles)
            includes(deleteDependentFiles)
            includes(viewOngoingCompanyDeal)
            includes(viewCoPaymentCostInfo)
            includes(viewConsolidatedHRCompanyReport)
            includes(viewAndCountCompanyScoreMagenta)
            includes(viewAndUpdateCompanyStaff)
        }

        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(viewPerson)
            includes(viewProduct)
            includes(viewBeneficiary)
            includes(viewCompany)
            includes(viewMember)
            includes(viewMemberPrice)
        }
    }

    match("at business-domain-service subscribers", { rootService.name == BUSINESS_ENVIRONMENT_SUBSCRIBERS }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(moveToSpecificPhase)
            includes(createBeneficiaryCompiledView)
            includes(deleteBeneficiaryCompiledView)
            includes(viewAndUpdateBeneficiary)
            includes(createMemberFromBeneficiary)
            includes(deleteCassiMember)
            includes(createPersonLoginFromBeneficiary)
            includes(createOnboardingForBeneficiaryWithoutOnboarding)
            includes(moveToFinishedPhaseFromAppointmentSchedule)
            includes(createAndViewAndUpdateBeneficiaryHubspot)
            includes(createB2bBatchInvoiceReport)
            includes(viewUpdateAndCreatePersonBillingAccountableParty)
            includes(viewMemberContract)
            includes(createInvoicePayment)
            includes(viewHealthDeclaration)
            includes(viewAndUpdatePerson)
            includes(viewAndUpdateAnyTermFiles)
            includes(viewAndUpdateMemberContract)
            includes(viewAndUpdateCompanyContract)
            includes(viewCompanyProductConfiguration)
            includes(viewAndUpdateCompany)
            includes(viewAndCreateAndUpdateCompanyActivationFiles)
            includes(viewAndCreateFileVaultCompanyActivationFiles)
            includes(viewAndUpdateCompanyStaff)
            includes(viewAndUpdateMemberInvoiceGroup)
            includes(viewAndUpdatePreActivationPayment)
            includes(salesViewInvoicePayments)
            includes(viewAndUpdateCompanyProductPriceListing)
            includes(viewAndUpdateOngoingCompanyDeal)
            includes(viewAndUpdateMemberProductChangeSchedule)
            includes(createViewUpdateCompanyContractAndSubContract)
            includes(viewAndCreateInvoiceGroupTaxReceipt)
            includes(viewCreateAndUpdateMemberTelegramTracking)
            includes(viewRefund)
            includes(viewPreActivationPayment)
            includes(viewPersonGracePeriod)
        }
    }

    match("at business-domain-service dependents backfill", { rootService.name == BUSINESS_ENVIRONMENT_BACKFILL }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(viewPerson)
            includes(viewAndUpdateBeneficiary)
        }
    }

    match(
        "at business-domain-service delete company contract backfill",
        { rootService.name == BUSINESS_ENVIRONMENT_BACKFILL }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(viewAndUpdateBeneficiary)
            includes(deleteCompanyContract)
            includes(deleteCompanySubContract)
        }
    }

    match(
        "at business-domain-service delete b2c with b2b data backfill",
        { rootService.name == BUSINESS_ENVIRONMENT_BACKFILL }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(viewAndUpdateMember)
            includes(viewAndUpdateBeneficiary)
            includes(deleteBeneficiary)
            includes(deleteBeneficiaryOnboarding)
            includes(deleteBeneficiaryOnboardingPhase)
        }
    }

    match(
        "at business-domain-service product with national coverage backfill",
        { rootService.name == BUSINESS_ENVIRONMENT_BACKFILL }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(viewAndUpdateProviders)
            includes(viewAndUpdateProductAndComponents)
        }
    }

    match("at business-domain-service onboarding backfill", { rootService.name == BUSINESS_ENVIRONMENT_BACKFILL }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(viewPerson)
            includes(viewMember)
            includes(viewCompany)
            includes(createBeneficiaryOnboarding)
            includes(createAndViewAndUpdateBeneficiaryHubspot)
            includes(viewAndUpdateBeneficiary)
            includes(canChangeProduct)
        }
    }

    match(
        "at business-domain-service Beneficiary MemberModel backfill",
        { rootService.name == BUSINESS_ENVIRONMENT_BACKFILL }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(viewAndUpdateBeneficiary)
            includes(viewMember)
        }
    }

    match(
        "at business-domain-service BeneficiaryCompiledView backfill",
        { rootService.name == BUSINESS_ENVIRONMENT_BACKFILL }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(createBeneficiaryCompiledView)
            includes(deleteBeneficiaryCompiledView)
        }
    }

    match(
        "at business-domain-service ArchiveBeneficiary backfill",
        { rootService.name == BUSINESS_ENVIRONMENT_BACKFILL }) {
        match("Product and Tech", { subject is StaffModel && subject.isProductTech() }) {
            includes(viewAndUpdateBeneficiary)
        }
    }

    match("at business-domain-service CompanyStaff backfill", { rootService.name == BUSINESS_ENVIRONMENT_BACKFILL }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(viewAndUpdateCompanyStaff)
        }
    }

    match(
        "at business-domain-service Contract and ContractTerm backfill",
        { rootService.name == BUSINESS_ENVIRONMENT_BACKFILL }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(viewHealthDeclaration)
            includes(createAndUpdateHealthCareOpsContract)
            includes(viewAndUpdateAnyFile)
            includes(viewAndUpdateProductAndComponents)
            includes(viewAndUpdateMemberContract)
            includes(viewAndUpdateCompanyContract)
        }
    }

    at(BUSINESS_ENVIRONMENT_BACKFILL) {
        who(Unauthenticated::class) {
            includes(viewMember)
            includes(viewProduct)
            includes(createViewUpdateCassiMember)
            includes(viewUpdateAndCreatePersonBillingAccountableParty)
            includes(viewAndUpsertCompanyProductConfiguration)
            includes(viewAndUpdateCompanyProductPriceListing)
            includes(createViewUpdateCompanyContractAndSubContract)
            includes(deleteCompanyProductPriceListing)
            includes(viewAndUpdateCompanyContract)
            includes(viewAndUpdateCompany)
            includes(viewAndCreateStandardCost)
            allows(CompanyActivationFilesModel::class, View, Delete)
            match("can delete", { action is Delete }) {
                match("any company_activation_files GenericFileVault") {
                    resource is GenericFileVault && resource.domain == "business" && resource.namespace == "company_activation_files"
                }
            }
        }
    }

    match("at business-domain-service recurrent jobs", { rootService.name == BUSINESS_ENVIRONMENT_RECURRENT }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(viewPerson)
            includes(viewAndUpdateMember)
            includes(viewAndUpdateBeneficiary)
            includes(viewUpdateAndCreateBeneficiaryOnboarding)
            includes(viewUpdateAndCreateBeneficiaryOnboardingPhase)
            includes(viewCompany)
            includes(viewAndUpdateMemberInvoiceGroup)
            includes(viewAndUpdatePreActivationPayment)
            includes(createViewUpdateCompanyContractAndSubContract)
            includes(viewCreateAndUpdateMemberTelegramTracking)
            includes(viewBeneficiaryCompiledView)
        }
    }
}
