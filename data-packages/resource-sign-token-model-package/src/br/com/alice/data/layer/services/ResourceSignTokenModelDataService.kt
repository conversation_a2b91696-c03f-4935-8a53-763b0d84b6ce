package br.com.alice.data.layer.services

import br.com.alice.common.UUIDv7
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.SoftDeleter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ResourceSignTokenModel
import br.com.alice.data.layer.models.ResourceSignTokenType
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ResourceSignTokenModelDataService : Service,
    Adder<ResourceSignTokenModel>,
    Getter<ResourceSignTokenModel>,
    Updater<ResourceSignTokenModel>,
    Finder<ResourceSignTokenModelDataService.FieldOptions, ResourceSignTokenModelDataService.OrderingOptions, ResourceSignTokenModel>,
    Deleter<ResourceSignTokenModel>,
    SoftDeleter<ResourceSignTokenModel> {

    override val namespace: String
        get() = "common"
    override val serviceName: String
        get() = "resource_sign_token"

    class IdField : Field.UUIDField(ResourceSignTokenModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class SignUuidField : Field.UUIDField(ResourceSignTokenModel::signUuid) {
        fun eq(value: UUIDv7) = Predicate.eq(this, value)
    }

    class ResourceTypeField : Field.TextField(ResourceSignTokenModel::resourceType) {
        fun eq(value: ResourceSignTokenType) = Predicate.eq(this, value)
    }

    class ResourceIdField : Field.TextField(ResourceSignTokenModel::resourceId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val id = IdField()
        val signUuid = SignUuidField()
        val resourceType = ResourceTypeField()
        val resourceId = ResourceIdField()
    }

    class OrderingOptions {
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<ResourceSignTokenModel, Throwable>
    override suspend fun add(model: ResourceSignTokenModel): Result<ResourceSignTokenModel, Throwable>
    override suspend fun update(model: ResourceSignTokenModel): Result<ResourceSignTokenModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ResourceSignTokenModel>, Throwable>
    override suspend fun delete(model: ResourceSignTokenModel): Result<Boolean, Throwable>
    override suspend fun softDelete(model: ResourceSignTokenModel): Result<Boolean, Throwable>
}
