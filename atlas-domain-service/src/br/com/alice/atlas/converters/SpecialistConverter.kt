package br.com.alice.atlas.converters

import br.com.alice.atlas.converters.PhoneConverter.phoneConverter
import br.com.alice.atlas.model.SpecialistTransport
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.data.layer.models.CassiSpecialist
import br.com.alice.data.layer.models.Contact
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.SpecialistAppointmentType
import br.com.alice.data.layer.models.StructuredAddress

object SpecialistConverter {
    const val CONTRACT_ORIGIN_CASSI = "CASSI"
    const val CONTRACT_ORIGIN_ALICE = "ALICE"

    fun healthProfessionalToSpecialist(
        healthProfessional: HealthProfessional,
        includedPlans: List<String>?,
        specialty: String?,
        subSpecialties: List<String>?,
    ) = SpecialistTransport(
        name = healthProfessional.name,
        photo = healthProfessional.imageUrl,
        council = healthProfessional.council.toString(),
        specialty = specialty,
        subSpecialties = subSpecialties.takeIf { it?.isNotEmpty() == true },
        includedPlans = includedPlans,
        phones = healthProfessional.phones.takeIf { it.isNotEmpty() }?.map { phoneConverter(it) },
        address = getAddress(healthProfessional.addressesStructured, healthProfessional.contacts),
        remote = healthProfessional.appointmentTypes.contains(SpecialistAppointmentType.REMOTE),
        about = healthProfessional.profileBio,
        contractOrigin = CONTRACT_ORIGIN_ALICE,
    )

    fun providerUnitToSpecialist(
        providerUnit: ProviderUnit,
        includedPlans: List<String>?,
        specialty: String?,
        subSpecialties: List<String>?,
    ) = SpecialistTransport(
        name = providerUnit.name,
        photo = providerUnit.imageUrl,
        council = null,
        specialty = specialty,
        subSpecialties = subSpecialties.takeIf { it?.isNotEmpty() == true },
        includedPlans = includedPlans,
        phones = providerUnit.phones.takeIf { it.isNotEmpty() }?.map { phoneConverter(it) },
        address = providerUnit.address?.formattedAddress()?.let { listOf(it) },
        remote = false,
        contractOrigin = providerUnit.contractOrigin.name,
    )

    fun cassiSpecialistToSpecialist(
        cassiSpecialist: CassiSpecialist,
        includedPlans: List<String>?,
        specialty: String?,
        subSpecialties: List<String>?,
    ) = SpecialistTransport(
        name = cassiSpecialist.name,
        photo = cassiSpecialist.imageUrl,
        council = cassiSpecialist.council.toString(),
        specialty = specialty,
        subSpecialties = subSpecialties.takeIf { it?.isNotEmpty() == true },
        includedPlans = includedPlans,
        phones = cassiSpecialist.phones.takeIf { it.isNotEmpty() }?.map { phoneConverter(it) },
        address = getAddress(cassiSpecialist.address, cassiSpecialist.contacts),
        remote = cassiSpecialist.appointmentTypes.contains(SpecialistAppointmentType.REMOTE),
        contractOrigin = CONTRACT_ORIGIN_CASSI,
    )

    private fun getAddress(
        address: List<StructuredAddress>?,
        contacts: List<Contact>?,
    ): List<String>? = if (address.isNullOrEmpty()) {
        if (contacts.isNotNullOrEmpty()) {
            contacts!!.mapNotNull {
                if (it.address != null) it.address!!.formattedAddress() else null
            }
        } else null
    } else address.map { it.formattedAddress() }
}
