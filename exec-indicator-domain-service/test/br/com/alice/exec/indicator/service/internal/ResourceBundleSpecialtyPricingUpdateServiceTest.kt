package br.com.alice.exec.indicator.service.internal

import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdate
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdateStatus
import br.com.alice.data.layer.services.ResourceBundleSpecialtyPricingUpdateModelDataService
import br.com.alice.exec.indicator.client.PricingUpdateHistoryFilters
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.events.ResourceBundleSpecialtyPricingUpdateCreatedEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class ResourceBundleSpecialtyPricingUpdateServiceTest {

    private val dataService: ResourceBundleSpecialtyPricingUpdateModelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val service = ResourceBundleSpecialtyPricingUpdateService(dataService, kafkaProducerService)


    private val history = listOf(
        ResourceBundleSpecialtyPricingUpdate(
            id = UUID.randomUUID(),
            fileName = "history_update.csv",
            fileVaultId = UUID.randomUUID(),
            createdByStaffId = UUID.randomUUID(),
            processingAt = LocalDateTime.now(),
            completedAt = LocalDateTime.now(),
            rowsCount = 100,
            failedRowsCount = 0,
            failedRowsErrors = emptyList(),
            parsingError = null,
            pricesBeginAt = LocalDate.now(),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            version = 1
        )
    )

    @Test
    fun `getProcessingResourceBundleSpecialtyPricingUpdate should return processing update`() = runBlocking {
        val processingUpdate = ResourceBundleSpecialtyPricingUpdate(
            id = UUID.randomUUID(),
            fileName = "processing_update.csv",
            fileVaultId = UUID.randomUUID(),
            createdByStaffId = UUID.randomUUID(),
            processingAt = LocalDateTime.now(),
            completedAt = null,
            rowsCount = 100,
            failedRowsCount = 5,
            failedRowsErrors = emptyList(),
            parsingError = null,
            pricesBeginAt = LocalDate.now(),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            version = 1
        )

        coEvery {
            dataService.findOne(queryEq {
                where {
                    this.completedAt.isNull()
                }
            })
        } returns processingUpdate.toModel().success()

        val result = service.getProcessingResourceBundleSpecialtyPricingUpdate()

        assertEquals(Result.success(processingUpdate), result)
        coVerify(exactly = 1) { dataService.findOne(any()) }
    }

    @Test
    fun `getResourceBundleSpecialtyPricingUpdateHistory should return history - status PROCESSED`() = runBlocking {
        val filters = PricingUpdateHistoryFilters(
            status = ResourceBundleSpecialtyPricingUpdateStatus.PROCESSED,
            startDate = LocalDate.now().minusDays(1),
            endDate = LocalDate.now()
        )
        val range = 0..10

        coEvery {
            dataService.find(queryEq {
                where {
                    this.processingAt.isNotNull()
                        .and(
                            this.completedAt.isNotNull()
                                .and(this.parsingError.isNull())
                                .and(this.failedRowsCount.eq(0))
                        )
                        .and(filters.startDate?.let { this.createdAt.greaterEq(it.atBeginningOfTheDay()) })
                        .and(filters.endDate?.let { this.createdAt.lessEq(it.atEndOfTheDay()) })
                }
                    .offset { range.first() }
                    .limit { range.count() }
            })
        } returns history.map { it.toModel() }.success()

        val result = service.getResourceBundleSpecialtyPricingUpdateHistory(filters, range)

        assertEquals(Result.success(history), result)
        coVerify(exactly = 1) { dataService.find(any()) }
    }

    @Test
    fun `getResourceBundleSpecialtyPricingUpdateHistory should return history - status PROCESSED_WITH_ERRORS`() =
        runBlocking {
            val filters = PricingUpdateHistoryFilters(
                status = ResourceBundleSpecialtyPricingUpdateStatus.PROCESSED_WITH_ERRORS,
                startDate = LocalDate.now().minusDays(1),
                endDate = LocalDate.now()
            )
            val range = 0..10

            coEvery {
                dataService.find(queryEq {
                    where {
                        this.processingAt.isNotNull()
                            .and(
                                this.completedAt.isNotNull()
                                    .and(this.parsingError.isNull())
                                    .and(this.failedRowsCount.greater(0))
                            )
                            .and(filters.startDate?.let { this.createdAt.greaterEq(it.atBeginningOfTheDay()) })
                            .and(filters.endDate?.let { this.createdAt.lessEq(it.atEndOfTheDay()) })
                    }
                        .offset { range.first() }
                        .limit { range.count() }
                })
            } returns history.map { it.toModel() }.success()

            val result = service.getResourceBundleSpecialtyPricingUpdateHistory(filters, range)

            assertEquals(Result.success(history), result)
            coVerify(exactly = 1) { dataService.find(any()) }
        }

    @Test
    fun `getResourceBundleSpecialtyPricingUpdateHistory should return history - status PARSING_ERROR`() = runBlocking {
        val filters = PricingUpdateHistoryFilters(
            status = ResourceBundleSpecialtyPricingUpdateStatus.PARSING_ERROR,
            startDate = LocalDate.now().minusDays(1),
            endDate = LocalDate.now()
        )
        val range = 0..10

        coEvery {
            dataService.find(queryEq {
                where {
                    this.processingAt.isNotNull()
                        .and(this.parsingError.isNotNull())
                        .and(filters.startDate?.let { this.createdAt.greaterEq(it.atBeginningOfTheDay()) })
                        .and(filters.endDate?.let { this.createdAt.lessEq(it.atEndOfTheDay()) })
                }
                    .offset { range.first() }
                    .limit { range.count() }
            })
        } returns history.map { it.toModel() }.success()

        val result = service.getResourceBundleSpecialtyPricingUpdateHistory(filters, range)

        assertEquals(Result.success(history), result)
        coVerify(exactly = 1) { dataService.find(any()) }
    }

    @Test
    fun `getResourceBundleSpecialtyPricingUpdateHistory should return history - status PROCESSING`() = runBlocking {
        val filters = PricingUpdateHistoryFilters(
            status = ResourceBundleSpecialtyPricingUpdateStatus.PROCESSING,
            startDate = LocalDate.now().minusDays(1),
            endDate = LocalDate.now()
        )
        val range = 0..10

        coEvery {
            dataService.find(queryEq {
                where {
                    this.processingAt.isNotNull()
                        .and(
                            this.completedAt.isNull()
                                .and(this.parsingError.isNull())
                        )
                        .and(filters.startDate?.let { this.createdAt.greaterEq(it.atBeginningOfTheDay()) })
                        .and(filters.endDate?.let { this.createdAt.lessEq(it.atEndOfTheDay()) })
                }
                    .offset { range.first() }
                    .limit { range.count() }
            })
        } returns history.map { it.toModel() }.success()

        val result = service.getResourceBundleSpecialtyPricingUpdateHistory(filters, range)

        assertEquals(Result.success(history), result)
        coVerify(exactly = 1) { dataService.find(any()) }
    }

    @Test
    fun `countResourceBundleSpecialtyPricingUpdateHistory should return count - status PROCESSED`() = runBlocking {
        val filters = PricingUpdateHistoryFilters(
            status = ResourceBundleSpecialtyPricingUpdateStatus.PROCESSED,
            startDate = LocalDate.now().minusDays(1),
            endDate = LocalDate.now()
        )

        coEvery {
            dataService.count(queryEq {
                where {
                    this.processingAt.isNotNull()
                        .and(
                            this.completedAt.isNotNull()
                                .and(this.parsingError.isNull())
                                .and(this.failedRowsCount.eq(0))
                        )
                        .and(filters.startDate?.let { this.createdAt.greaterEq(it.atBeginningOfTheDay()) })
                        .and(filters.endDate?.let { this.createdAt.lessEq(it.atEndOfTheDay()) })
                }
            })
        } returns 1.success()

        val result = service.countResourceBundleSpecialtyPricingUpdateHistory(filters)

        assertEquals(Result.success(1), result)
        coVerify(exactly = 1) { dataService.count(any()) }
    }

    fun `get should get by id`() = runBlocking {
        val id = UUID.randomUUID()
        val resourceBundleSpecialtyPricingUpdate = ResourceBundleSpecialtyPricingUpdate(
            id = id,
            fileName = "file.csv",
            fileVaultId = UUID.randomUUID(),
            createdByStaffId = UUID.randomUUID(),
            processingAt = LocalDateTime.now(),
            completedAt = LocalDateTime.now(),
            rowsCount = 100,
            failedRowsCount = 0,
            failedRowsErrors = emptyList(),
            parsingError = null,
            pricesBeginAt = LocalDate.now(),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            version = 1
        )

        coEvery {
            dataService.findOne(queryEq { where { this.id.eq(id) } })
        } returns resourceBundleSpecialtyPricingUpdate.toModel().success()

        val result = service.get(id)

        assertEquals(Result.success(resourceBundleSpecialtyPricingUpdate), result)
        coVerify(exactly = 1) { dataService.findOne(any()) }
    }

    @Test
    fun `add should add to the database and publish event`() = runBlocking {
        val resourceBundleSpecialtyPricingUpdate = TestModelFactory.buildResourceBundleSpecialtyPricingUpdate()

        coEvery {
            dataService.add(resourceBundleSpecialtyPricingUpdate.toModel())
        } returns resourceBundleSpecialtyPricingUpdate.toModel().success()

        coEvery {
            kafkaProducerService.produce(match {
                (it as ResourceBundleSpecialtyPricingUpdateCreatedEvent).payload.resourceBundleSpecialtyPricingUpdateId == resourceBundleSpecialtyPricingUpdate.id
            })
        } returns ProducerResult(LocalDateTime.now(), "1", 1)

        val result = service.add(resourceBundleSpecialtyPricingUpdate)

        assertEquals(Result.success(resourceBundleSpecialtyPricingUpdate), result)

        coVerify(exactly = 1) { dataService.add(resourceBundleSpecialtyPricingUpdate.toModel()) }
        coVerify(exactly = 1) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `update should update the resource bundle specialty pricing update`() = runBlocking {
        val resourceBundleSpecialtyPricingUpdate = ResourceBundleSpecialtyPricingUpdate(
            id = UUID.randomUUID(),
            fileName = "file.csv",
            fileVaultId = UUID.randomUUID(),
            createdByStaffId = UUID.randomUUID(),
            processingAt = LocalDateTime.now(),
            completedAt = LocalDateTime.now(),
            rowsCount = 100,
            failedRowsCount = 0,
            failedRowsErrors = emptyList(),
            parsingError = null,
            pricesBeginAt = LocalDate.now(),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            version = 1
        )

        coEvery { dataService.update(any()) } returns Result.success(resourceBundleSpecialtyPricingUpdate.toModel())

        val result = service.update(resourceBundleSpecialtyPricingUpdate)

        assertEquals(Result.success(resourceBundleSpecialtyPricingUpdate), result)
        coVerify(exactly = 1) { dataService.update(any()) }
    }
}
