package br.com.alice.sales_channel.api.controllers

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.membership.client.MemberPriceService
import br.com.alice.product.client.ProductService
import br.com.alice.sales_channel.api.models.DealBeneficiaryAppointmentDetails
import br.com.alice.sales_channel.api.models.DealBeneficiaryItem
import br.com.alice.sales_channel.api.models.DealBeneficiaryListResponse
import br.com.alice.sales_channel.model.DealBeneficiary
import br.com.alice.sales_channel.model.DealBeneficiaryProduct
import br.com.alice.sales_channel.model.DealBeneficiaryStage
import br.com.alice.sales_channel.model.DealBeneficiaryStageDetails
import br.com.alice.sales_channel.service.DealBeneficiaryService
import br.com.alice.sales_channel.service.SalesAgentService
import br.com.alice.sales_channel.service.SalesFirmStaffService
import br.com.alice.schedule.client.AppointmentScheduleService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class DealBeneficiaryControllerTest: ControllerTestHelper() {
    private val salesFirmStaffService: SalesFirmStaffService = mockk()
    private val dealBeneficiaryService: DealBeneficiaryService = mockk()
    private val appointmentScheduleService: AppointmentScheduleService = mockk()
    private val productService: ProductService = mockk()
    private val salesAgentService: SalesAgentService = mockk()
    private val memberPriceService: MemberPriceService = mockk()

    private val controller = DealBeneficiaryController(
        salesFirmStaffService,
        salesAgentService,
        dealBeneficiaryService,
        appointmentScheduleService,
        productService,
        memberPriceService
    )

    private val product = TestModelFactory.buildProduct()
    private val person1 = TestModelFactory.buildPerson(firstName = "Jonathan", lastName = "Calleri")
    private val beneficiary1 = DealBeneficiary(
        id = RangeUUID.generate(),
        personId = person1.id,
        memberId = RangeUUID.generate(),
        name = "Jonathan Calleri",
        updatedAt = LocalDateTime.now(),
        stage = DealBeneficiaryStageDetails(
            name = DealBeneficiaryStage.MEDICAL_INTERVIEW_SCHEDULED.name,
            friendlyName = DealBeneficiaryStage.MEDICAL_INTERVIEW_SCHEDULED.name,
            count = DealBeneficiaryStage.totalStages(),
            completed = 3,
            owner = DealBeneficiaryStage.MEDICAL_INTERVIEW_SCHEDULED.owner.toString()
        ),
        product = DealBeneficiaryProduct(
            id = product.id,
            friendlyName = product.displayName!!
        ),
    )

    private val person2 = TestModelFactory.buildPerson(firstName = "Lucas", lastName = "Moura")
    private val beneficiary2 = beneficiary1.copy(
            id = RangeUUID.generate(),
            personId = person2.id,
            name = "Lucas Moura"
        )

    private val person3 = TestModelFactory.buildPerson(firstName = "Aloísio", lastName = "Chulapa")
    private val beneficiary3 = beneficiary1.copy(
        id = RangeUUID.generate(),
        personId = person3.id,
        name = "Aloísio Chulapa"
    )

    private val beneficiaries = listOf(
        beneficiary1,
        beneficiary2,
        beneficiary3,
    )

    private val appointmentSchedule3 = TestModelFactory.buildAppointmentSchedule(
        personId = beneficiary3.personId,
        type = AppointmentScheduleType.HEALTH_DECLARATION
    )

    private val expectedBeneficiary1 = DealBeneficiaryItem(
        id = beneficiary1.id,
        name = beneficiary1.name,
        updatedAt = beneficiary1.updatedAt,
        stage = beneficiary1.stage,
        appointment = DealBeneficiaryAppointmentDetails(),
        productPrice = 600.toBigDecimal(),
        productTitle = product.displayName!!,
    )

    private val expectedBeneficiary2 = DealBeneficiaryItem(
        id = beneficiary2.id,
        name = beneficiary2.name,
        updatedAt = beneficiary2.updatedAt,
        stage = beneficiary2.stage,
        appointment = DealBeneficiaryAppointmentDetails(),
        productPrice = 600.toBigDecimal(),
        productTitle = product.displayName!!,
    )

    private val expectedBeneficiary3 = DealBeneficiaryItem(
        id = beneficiary3.id,
        name = beneficiary3.name,
        updatedAt = beneficiary3.updatedAt,
        stage = beneficiary3.stage,
        appointment = DealBeneficiaryAppointmentDetails(
            status = appointmentSchedule3.status,
            appointmentStartTime = appointmentSchedule3.startTime.minusHours(3),
            appointmentEndTime = appointmentSchedule3.endTime?.minusHours(3),
        ),
        productPrice = 600.toBigDecimal(),
        productTitle = product.displayName!!,
    )

    private val expectedResponseBeneficiaries = listOf(
        expectedBeneficiary1,
        expectedBeneficiary2,
        expectedBeneficiary3
    )

    private val personIds = beneficiaries.map { it.personId }

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @Test
    fun `#listByDeal should get list of DealBeneficiary`() {
        val expectedResponse = DealBeneficiaryListResponse(
            beneficiaries = expectedResponseBeneficiaries,
            totalFinished = 0,
            total = beneficiaries.size,
            offset = 0,
            limit = 5
        )

        authenticatedAsSalesFirmStaff(idToken, salesCompanyStaff) {
            val dealId = RangeUUID.generate()

            coEvery {
                dealBeneficiaryService.searchByOngoingDeal(any(), any(), any(), any())
            } returns beneficiaries.success()
            coEvery {
                appointmentScheduleService.findLastForEachPersonIn(personIds)
            } returns listOf(appointmentSchedule3).success()
            coEvery {
                productService.findByIds(any())
            } returns listOf(product).success()
            coEvery { memberPriceService.getCurrentPriceForMember(any()) } returns 600.toBigDecimal().success()

            get("/deals/${dealId}/beneficiaries?range=[0,5]") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val responseObj: DealBeneficiaryListResponse = response.bodyAsJson()

                Assertions.assertThat(responseObj).isEqualTo(expectedResponse).usingRecursiveComparison()

                coVerifyOnce {
                    dealBeneficiaryService.searchByOngoingDeal(
                        id = dealId,
                        offset = 0,
                        limit = 5,
                        query = null
                    )
                }

                coVerifyOnce { appointmentScheduleService.findLastForEachPersonIn(personIds) }
                coVerify(exactly=3) { memberPriceService.getCurrentPriceForMember(any()) }
            }
        }
    }

    @Test
    fun `#listByDeal should get list of DealBeneficiary when query is provided`() {
        val expectedResponse = DealBeneficiaryListResponse(
            beneficiaries = listOf(expectedBeneficiary3),
            totalFinished = 0,
            total = 1,
            offset = 0,
            limit = 5
        )
        val query = "Chulapa"
        val appointmentPersonIds = listOf(beneficiary3.personId)

        authenticatedAsSalesFirmStaff(idToken, salesCompanyStaff) {
            val dealId = RangeUUID.generate()

            coEvery {
                dealBeneficiaryService.searchByOngoingDeal(any(), any(), any(), any())
            } returns listOf(beneficiary3).success()
            coEvery {
                appointmentScheduleService.findLastForEachPersonIn(appointmentPersonIds)
            } returns listOf(appointmentSchedule3).success()
            coEvery {
                productService.findByIds(any())
            } returns listOf(product).success()

            coEvery { memberPriceService.getCurrentPriceForMember(any()) } returns 600.toBigDecimal().success()

            get("/deals/${dealId}/beneficiaries?filter={\"q\":\"$query\"}&range=[0,5]") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val responseObj: DealBeneficiaryListResponse = response.bodyAsJson()

                Assertions.assertThat(responseObj).isEqualTo(expectedResponse).usingRecursiveComparison()

                coVerifyOnce {
                    dealBeneficiaryService.searchByOngoingDeal(
                        id = dealId,
                        offset = 0,
                        limit = 5,
                        query = query
                    )
                }

                coVerifyOnce { appointmentScheduleService.findLastForEachPersonIn(appointmentPersonIds) }
                coVerifyOnce { memberPriceService.getCurrentPriceForMember(beneficiary3.memberId) }
            }
        }
    }

    @Test
    fun `#listByDeal should return correct productPrices`() {
        authenticatedAsSalesFirmStaff(idToken, salesCompanyStaff) {
            val dealId = RangeUUID.generate()

            coEvery {
                dealBeneficiaryService.searchByOngoingDeal(any(), any(), any(), any())
            } returns beneficiaries.success()
            coEvery {
                appointmentScheduleService.findLastForEachPersonIn(personIds)
            } returns listOf(appointmentSchedule3).success()
            coEvery {
                productService.findByIds(any())
            } returns listOf(product).success()
            coEvery { memberPriceService.getCurrentPriceForMember(any()) } returns 600.toBigDecimal().success()

            get("/deals/${dealId}/beneficiaries?range=[0,5]") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val responseObj: DealBeneficiaryListResponse = response.bodyAsJson()

                Assertions.assertThat(responseObj.beneficiaries).allMatch { it.productPrice == 600.toBigDecimal() }

                coVerifyOnce {
                    dealBeneficiaryService.searchByOngoingDeal(
                        id = dealId,
                        offset = 0,
                        limit = 5,
                        query = null
                    )
                }

                coVerifyOnce { appointmentScheduleService.findLastForEachPersonIn(personIds) }
                coVerify(exactly=3) { memberPriceService.getCurrentPriceForMember(any()) }
            }
        }
    }
}
