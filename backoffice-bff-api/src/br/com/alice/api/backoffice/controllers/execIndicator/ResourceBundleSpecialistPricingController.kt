package br.com.alice.api.backoffice.controllers.execIndicator

import br.com.alice.api.backoffice.mappers.execIndicator.ResourceSpecialistPricingOutputMapper.toPaginatedResponse
import br.com.alice.api.backoffice.transfers.execIndicator.DownloadCSVRequest
import br.com.alice.api.backoffice.transfers.execIndicator.EffectiveDatesResponse
import br.com.alice.common.ErrorResponse
import br.com.alice.common.MultipartRequest
import br.com.alice.common.Response
import br.com.alice.common.coFoldResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.logging.logger
import br.com.alice.common.mappers.CommonInputMapper
import br.com.alice.common.toResponse
import br.com.alice.common.withContexts
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdateStatus
import br.com.alice.exec.indicator.client.CSVGenerationResponse
import br.com.alice.exec.indicator.client.PricingUpdateHistoryFilters
import br.com.alice.exec.indicator.client.ResourceSpecialtyPricingCSVService
import br.com.alice.exec.indicator.client.UploadPriceChangesRequest
import br.com.alice.filevault.models.FileType
import com.github.kittinunf.result.map
import io.ktor.http.ContentDisposition
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respondFile
import io.ktor.util.pipeline.PipelineContext
import java.io.File
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import kotlin.reflect.KClass

class ResourceBundleSpecialistPricingController(
    private val resourceSpecialistCSVService: ResourceSpecialtyPricingCSVService,
): Controller() {
    private object ErrorsCode {
        const val ALREADY_PROCESSING_PRICING_UPDATE = "already_processing_pricing_update"
    }

    private val mapFailures: Array<Pair<KClass<*>, suspend (Throwable) -> ErrorResponse>> = arrayOf(
        InvalidArgumentException::class to {
            ErrorResponse(
                ErrorsCode.ALREADY_PROCESSING_PRICING_UPDATE,
                i18n(ErrorsCode.ALREADY_PROCESSING_PRICING_UPDATE)
            )
        },
    )

    fun effectiveDates(): Response {
        val formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")
        val dates = getEffectiveDaysCheckingWorkDays().map { it.format(formatter) }

        logger.info(
            "ResourceBundleSpecialistPricingController effectiveDates",
            "currentMonth" to dates[0],
            "nextMonth" to dates[1]
        )

        return EffectiveDatesResponse(dates = dates).toResponse()
    }

    suspend fun downloadCSV(call: PipelineContext<Unit, ApplicationCall>, request: DownloadCSVRequest) {
        call.withContexts {
            logger.info(
                "ResourceBundleSpecialistPricingController downloadCSV",
                "request" to request,
            )

            val resourceBundleSpecialtyIds = request.resourceBundleSpecialtyIds
            resourceSpecialistCSVService.generate(resourceBundleSpecialtyIds)
                .map {
                    val (file, fileName) = it.toFile()
                    respondWithFile(call, file, fileName)
                }
        }
    }

    suspend fun getProcessingResourceBundleSpecialtyPricingUpdate(): Response =
        resourceSpecialistCSVService.getProcessingResourceBundleSpecialtyPricingUpdate()
            .coFoldResponse(
                { it },
                *mapFailures
            )

    suspend fun downloadFailedLinesFile(
        call: PipelineContext<Unit, ApplicationCall>,
        resourceBundleSpecialtyPricingUpdateId: String,
    ) {
        call.withContexts {
            val uuid = resourceBundleSpecialtyPricingUpdateId.toSafeUUID()
            resourceSpecialistCSVService.generateFailedLinesFile(uuid)
                .map {
                    val (file, fileName) = it.toFile()
                    respondWithFile(call, file, fileName)
                }
        }
    }

    suspend fun upload(
        multipartRequest: MultipartRequest,
    ): Response {
        logger.info(
            "ResourceBundleSpecialtyPricingController::upload",
            "multipartRequest" to multipartRequest
        )

        val effectiveDate = multipartRequest.parameters["effectiveDate"]?.toLocalDate() ?: run {
            logger.info("ResourceBundleSpecialtyPricingController::upload Effective date not provided")
            return Response(HttpStatusCode.BadRequest, "Effective date must be provided")
        }

        val possibleEffectiveDate = getEffectiveDaysCheckingWorkDays()
        if (effectiveDate !in possibleEffectiveDate) {
            logger.info("ResourceBundleSpecialtyPricingController::upload Effective date not in range")
            return Response(HttpStatusCode.BadRequest, "Effective date must be in range: $possibleEffectiveDate")
        }

        val metadata = multipartRequest.file!!

        val fileType = FileType.fromExtension(metadata.extension) ?: run {
            logger.info("ResourceBundleSpecialtyPricingController::upload Unsupported file extension: ${metadata.extension}")
            return Response(HttpStatusCode.BadRequest, "Unsupported file extension: ${metadata.extension}")
        }

        val staffId = (currentUserIdKey() as String).toSafeUUID()

        return resourceSpecialistCSVService.uploadPriceChanges(
            UploadPriceChangesRequest(
                fileType = fileType,
                content = multipartRequest.fileContent!!.readBytes(),
                pricesBeginAt = effectiveDate,
                staffId = staffId,
                fileName = metadata.name,
            )
        ).coFoldResponse(
            {
                it
            },
            *mapFailures
        )
    }

    suspend fun pricingUpdateIndex(queryParams: Parameters): Response {
        val range = CommonInputMapper.getPaginationParams(queryParams)
        val startDate = CommonInputMapper.getFilterParams<String>(queryParams, "startDate")?.toLocalDate()
        val endDate = CommonInputMapper.getFilterParams<String>(queryParams, "endDate")?.toLocalDate()
        val status = CommonInputMapper.getFilterParams<String>(queryParams, "status")?.let {
            ResourceBundleSpecialtyPricingUpdateStatus.valueOf(it)
        }

        logger.info(
            "ResourceBundleSpecialistPricingController - pricingUpdateIndex",
            "query_params" to queryParams.entries(),
            "range" to range,
            "startDate" to startDate,
            "endDate" to endDate,
            "status" to status
        )

        val filters = PricingUpdateHistoryFilters(
            startDate = startDate,
            endDate = endDate,
            status = status
        )

        return resourceSpecialistCSVService.getPricingUpdateHistory(filters, range)
            .coFoldResponse(
                { it.toPaginatedResponse(queryParams) },
                *mapFailures
            )
    }

    private fun CSVGenerationResponse.toFile(): Pair<File, String> {
        val (fileName, bytes) = this
        val file = File.createTempFile(fileName, ".csv")
        file.writeBytes(bytes)

        logger.info("ResourceBundleSpecialistPricingController file generated - $fileName.csv")

        return file to "$fileName.csv"
    }

    private suspend fun respondWithFile(call: PipelineContext<Unit, ApplicationCall>, file: File, fileName: String) {
        call.context.respondFile(file) {
            call.context.response.headers.append(
                HttpHeaders.ContentDisposition,
                ContentDisposition.Attachment.withParameter(
                    ContentDisposition.Parameters.FileName,
                    fileName
                ).toString()
            )
        }
    }

    fun getEffectiveDaysCheckingWorkDays(): List<LocalDate> {
        val today = LocalDate.now()
        val workDays = calculateWorkDays(
            startDate = today.atBeginningOfTheMonth(),
            endDate = today
        )

        logger.info(
            "ResourceBundleSpecialistPricingController getEffectiveDaysCheckingWorkDays",
            "workDays" to workDays
        )

        val dates = if (workDays > 5) {
            listOf(
                today.atBeginningOfTheMonth(),
                today.plusMonths(1).atBeginningOfTheMonth()
            )
        } else {
            listOf(
                today.minusMonths(1).atBeginningOfTheMonth(),
                today.atBeginningOfTheMonth()
            )
        }

        return dates
    }

    private fun calculateWorkDays(startDate: LocalDate, endDate: LocalDate): Int {
        var workDays = 0
        var currentDate = startDate

        while (currentDate <= endDate) {
            if (currentDate.dayOfWeek.value <= 5) {  // Monday to Friday
                workDays++
            }
            currentDate = currentDate.plusDays(1)
        }

        return workDays
    }
}
