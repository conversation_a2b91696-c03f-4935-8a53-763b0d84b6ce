package app.membership_domain_service_backfill_test

import data.app.membership_domain_service_backfill
import rego.v1

test_view_count_any_contract_terms_allow if {
	{1, 2} == membership_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "FileVault"},
			"resource": {
				"opaType": "FileVault",
				"domain": "member",
				"namespace": "term",
			},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "FileVault"},
			"resource": {
				"opaType": "FileVault",
				"domain": "member",
				"namespace": "term",
			},
		},
	]}
}

test_unauthenticated_view_count_resources_allow if {
	resources = [
		"ProductOrderModel",
		"ProductBundleModel",
		"ProductPriceListingModel",
		"PriceListingModel",
		"ProviderModel",
		"InsurancePortabilityRequestModel",
		"CompanyModel",
		"CompanyContractModel",
		"MemberContractModel",
		"MemberContractTermModel",
		"PersonGracePeriod",
		"HealthCondition",
		"CompanySubContractModel"
	]

	every resource in resources {
		{1, 2} == membership_domain_service_backfill.allow with input as {"cases": [
			{
				"index": 1,
				"action": "view",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "count",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_unauthenticated_view_create_PersonRegistration_allow if {
	{1, 2} == membership_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
	]}
}

test_unauthenticated_view_count_update_resources_allow if {
	resources = [
		"MemberModel",
		"OnboardingContractModel",
		"ProductModel",
		"PersonModel",
		"BeneficiaryModel",
	]

	every resource in resources {
		{1, 2, 3} == membership_domain_service_backfill.allow with input as {"cases": [
			{
				"index": 1,
				"action": "view",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "count",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 3,
				"action": "update",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_unauthenticated_view_update_PromoCode_allow if {
	{1, 2} == membership_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PromoCodeModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PromoCodeModel"},
		},
	]}
}

test_unauthenticated_CRU_OnboardingBackgroundCheckModel_allow if {
	{1, 2, 3} == membership_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
	]}
}

test_unauthenticated_CRU_PersonIdentityValidationModel_allow if {
	{1, 2, 3} == membership_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonIdentityValidationModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonIdentityValidationModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonIdentityValidationModel"},
		},
	]}
}

test_unauthenticated_CRU_count_PersonOnboarding_allow if {
	{1, 2, 3, 4} == membership_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
	]}
}

test_unauthenticated_CRU_count_HealthDeclaration_allow if {
	{1, 2, 3, 4} == membership_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "HealthDeclaration"},
		},
	]}
}
