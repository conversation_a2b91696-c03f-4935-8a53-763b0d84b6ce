package br.com.alice.business.converters

import br.com.alice.business.converters.MemberConverter.toBeneficiaryModel
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.BeneficiaryContractType
import br.com.alice.data.layer.models.BeneficiaryModel
import br.com.alice.data.layer.models.Contract
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberBeneficiary
import br.com.alice.data.layer.models.MemberProduct
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.ProductType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.LocalDate
import java.time.LocalDateTime

class MemberConverterTest {

    private val personId = PersonId()
    private val beneficiaryId = RangeUUID.generate()
    private val memberId = RangeUUID.generate()
    private val companyId = RangeUUID.generate()
    private val companySubContractId = RangeUUID.generate()
    private val now = LocalDateTime.now()
    
    private val validMember = Member(
        id = memberId,
        personId = personId,
        contract = Contract("http://example.com/contract"),
        status = MemberStatus.ACTIVE,
        selectedProduct = MemberProduct(
            id = RangeUUID.generate(),
            type = ProductType.B2B,
            prices = emptyList(),
            priceListing = null,
            productPriceListingId = null
        ),
        brand = Brand.ALICE,
        beneficiaryId = beneficiaryId,
        companyId = companyId,
        companySubContractId = companySubContractId,
        archived = false,
        beneficiary = MemberBeneficiary(
            type = BeneficiaryType.EMPLOYEE,
            contractType = BeneficiaryContractType.CLT,
            activatedAt = now,
            version = 1,
            gracePeriodBaseDate = LocalDate.now()
        ),
        createdAt = now,
        updatedAt = now
    )

    @Test
    fun `toBeneficiaryModel should convert Member to BeneficiaryModel correctly`() {
        // Given/When/Then
        assertThat(validMember.toBeneficiaryModel()).isEqualTo(validMember.beneficiary?.let {
            BeneficiaryModel(
                id = this.beneficiaryId,
                parentBeneficiary = it.parentBeneficiary,
                personId = this.personId,
                memberId = validMember.id,
                companyId = this.companyId,
                type = it.type,
                contractType = it.contractType,
                parentBeneficiaryRelationType = it.parentBeneficiaryRelationType,
                activatedAt = it.activatedAt,
                canceledAt = it.canceledAt,
                hiredAt = it.hiredAt,
                parentBeneficiaryRelatedAt = it.parentBeneficiaryRelatedAt,
                canceledReason = it.canceledReason,
                canceledDescription = it.canceledDescription,
                cnpj = it.cnpj,
                hasContributed = it.hasContributed,
                archived = validMember.archived,
                version = it.version,
                brand = validMember.brand,
                companySubContractId = validMember.companySubContractId,
                memberStatus = validMember.status,
                parentPerson = validMember.parentPerson,
                gracePeriodType = it.gracePeriodType,
                gracePeriodTypeReason = it.gracePeriodTypeReason,
                gracePeriodBaseDate = it.gracePeriodBaseDate,
                createdAt = validMember.createdAt,
                updatedAt = validMember.updatedAt,
            )
        })
    }

    @Test
    fun `validateMember should return null when beneficiary is null`() {
        // Given
        val invalidMember = validMember.copy(beneficiary = null)

        // When/Then
        assertThat(invalidMember.toBeneficiaryModel()).isNull()
    }

    @Test
    fun `validateMember should throw exception when beneficiaryId is null`() {
        // Given
        val invalidMember = validMember.copy(beneficiaryId = null)

        // When/Then
        val exception = assertThrows<IllegalStateException> {
            invalidMember.toBeneficiaryModel()
        }

        assertThat(exception.message)
            .isEqualTo("Member has Beneficiary but beneficiaryId is null. Member id: ${invalidMember.id}")
    }

    @Test
    fun `validateMember should throw exception when companyId is null`() {
        // Given
        val invalidMember = validMember.copy(companyId = null)

        // When/Then
        val exception = assertThrows<IllegalStateException> {
            invalidMember.toBeneficiaryModel()
        }

        assertThat(exception.message)
            .isEqualTo("Member has Beneficiary but companyId is null. Member id: ${invalidMember.id}")
    }

    @Test
    fun `validateMember should throw exception when companySubContractId is null`() {
        // Given
        val invalidMember = validMember.copy(companySubContractId = null)

        // When/Then
        val exception = assertThrows<IllegalStateException> {
            invalidMember.toBeneficiaryModel()
        }

        assertThat(exception.message)
            .isEqualTo("Member has Beneficiary but companySubContractId is null. Member id: ${invalidMember.id}")
    }
}
