package br.com.alice.api.backoffice.transfers

import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.models.Gender
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.ProviderUnit
import java.time.LocalDate
import java.util.UUID

data class StaffShortResponse(
    val id: UUID,
    val firstName: String,
    val lastName: String,
    val email: String,
    val active: Boolean,
    val role: Role
)

data class StaffFullResponse(
    val id: UUID,
    val firstName: String,
    val lastName: String,
    val fullName: String,
    val email: String,
    val nationalId: String? = null,
    val birthDate: LocalDate? = null,
    val gender: Gender,
    val profileImageUrl: String? = null,
    val role: Role,
    val type: StaffType,
    val active: Boolean,
    val version: Int
)

data class StaffRolesResponse(
    val id: String,
    val name: String,
    val value: String
)

data class StaffTiersResponse(
    val id: String,
    val name: String,
    val value: String
)

data class StaffScoreResponse(
    val id: String,
    val name: String,
    val value: String
)

data class StaffFormResponse(
    val specialties: List<MedicalSpecialty>? = null,
    val providerUnits: List<ProviderUnit>? = null,
    val staffRoles: List<StaffRolesResponse>? = null,
    val staffTiers: List<StaffTiersResponse>? = null,
    val staffScore: List<StaffScoreResponse>? = null,
    val councilTypes: List<CouncilTypeResponse>? = null
)

data class CouncilTypeResponse(
    val id: Int,
    val name: String
)
