package br.com.alice.person.routes

import br.com.alice.business.events.BeneficiaryChangedEvent
import br.com.alice.business.events.BeneficiaryCreatedEvent
import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.interfaces.AdditionalProperties
import br.com.alice.common.kafka.internals.ConsumerJob
import br.com.alice.membership.model.events.PersonOnboardingStartedEvent
import br.com.alice.onboarding.model.events.PersonOnboardingArchivedEvent
import br.com.alice.person.br.com.alice.person.events.PersonProductInfoUpsertEvent
import br.com.alice.person.br.com.alice.person.events.PersonUpdateSusEvent
import br.com.alice.person.consumers.BeneficiaryChangedConsumer
import br.com.alice.person.consumers.MemberChangeConsumer
import br.com.alice.person.consumers.OnboardingArchivedConsumer
import br.com.alice.person.consumers.PersonProductInfoUpdateConsumer
import br.com.alice.person.consumers.ProductChangedConsumer
import br.com.alice.person.consumers.ProductUpdateConsumer
import br.com.alice.person.consumers.SusCheckConsumers
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.person.model.events.MemberCancelledEvent
import br.com.alice.person.model.events.MemberCreatedEvent
import br.com.alice.person.model.events.ProductChangedEvent
import br.com.alice.product.model.events.ProductUpdatedEvent
import java.time.Duration

fun ConsumerJob.Configuration.kafkaRoutes() {

    val susCheckConsumers by inject<SusCheckConsumers>()
    consume(
        "handle-person-check-on-sus-by-beneficiary-created",
        BeneficiaryCreatedEvent.name,
        susCheckConsumers::handlePersonCheckOnSUSByBeneficiaryCreated
    )

    consume(
        "handle-person-update-by-recurrent-or-backfill-event",
        PersonUpdateSusEvent.name,
        susCheckConsumers::handlePersonCheckOnSUSByPersonOnRecurrentOrBackfill
    )

    consume(
        "handle-person-check-on-sus-by-person-onboarding-created",
        PersonOnboardingStartedEvent.name,
        susCheckConsumers::handlePersonCheckOnSUSByPersonOnboardingStarted
    )

    val onboardingArchivedConsumer by inject<OnboardingArchivedConsumer>()
    consume(
        "onboarding-archive-membership",
        PersonOnboardingArchivedEvent.name,
        onboardingArchivedConsumer::archiveMembership
    )

    val productChangedConsumer by inject<ProductChangedConsumer>()
    consume(
        "product-change-reset-cpts-end-date",
        ProductChangedEvent.name,
        productChangedConsumer::resetCptsEndDate
    )

    val memberChangeConsumer by inject<MemberChangeConsumer>()
    consume(
        "update-after-activated",
        MemberActivatedEvent.name,
        memberChangeConsumer::consumerMemberActivated
    )
    consume(
        "update-after-cancelled",
        MemberCancelledEvent.name,
        memberChangeConsumer::consumerMemberCancelled
    )
    consume(
        "update-after-member-product-change",
        ProductChangedEvent.name,
        memberChangeConsumer::consumerMemberProductChange
    )
    consume(
        "update-after-member-created",
        MemberCreatedEvent.name,
        memberChangeConsumer::consumeMemberCreated
    )
    val personProductInfoUpdateConsumer by inject<PersonProductInfoUpdateConsumer>()
    consume(
        "update-person-product-info",
        PersonProductInfoUpsertEvent.name,
        personProductInfoUpdateConsumer::fillPersonProductInfo,
        additionalProperties = AdditionalProperties(delay = Duration.ofSeconds(1)),
    )

    val productUpdateConsumer by inject<ProductUpdateConsumer>()
    consume(
        "send-event-person-product-update",
        ProductUpdatedEvent.name,
        productUpdateConsumer::sendEventPersonProductChange
    )

    val beneficiaryChangedConsumer by inject<BeneficiaryChangedConsumer>()
    consume(
        "update-beneficiary-on-member",
        BeneficiaryChangedEvent.NAME,
        beneficiaryChangedConsumer::updateBeneficiaryOnMember
    )
}
