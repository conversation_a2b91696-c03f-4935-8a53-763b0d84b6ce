package br.com.alice.provider.client

import br.com.alice.common.core.Status
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.StructuredAddress
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ProviderUnitService : Service {
    override val namespace get() = "provider"
    override val serviceName get() = "provider_unit"

    suspend fun getByName(providerName: String): Result<List<ProviderUnit>, Throwable>
    suspend fun getByFilterWithRange(filter: ProviderUnitFilter, range: IntRange): Result<List<ProviderUnit>, Throwable>
    suspend fun countByFilter(filter: ProviderUnitFilter): Result<Int, Throwable>
    suspend fun getByType(providerUnitType: ProviderUnit.Type): Result<List<ProviderUnit>, Throwable>
    suspend fun getByProviderIds(
        providerIds: List<UUID>,
        withAddress: Boolean = true
    ): Result<List<ProviderUnit>, Throwable>

    suspend fun getByProviderIdsPaginated(
        providerIds: List<UUID>,
        offset: Int,
        limit: Int
    ): Result<List<ProviderUnit>, Throwable>

    suspend fun getByProviderIdsWithTypes(
        providerIds: List<UUID>,
        types: List<ProviderUnit.Type>,
        withAddress: Boolean = true
    ): Result<List<ProviderUnit>, Throwable>

    suspend fun getByProviderIdsBrandWithTypes(
        providerIds: List<UUID>,
        brand: Brand,
        types: List<ProviderUnit.Type>,
        withAddress: Boolean = true
    ): Result<List<ProviderUnit>, Throwable>

    suspend fun getByIds(list: List<UUID>, withAddress: Boolean = true): Result<List<ProviderUnit>, Throwable>
    suspend fun getByAdministrativeStaffId(
        administrativeStaffId: UUID,
        withAddress: Boolean = true
    ): Result<List<ProviderUnit>, Throwable>

    suspend fun getByClinicalStaffId(
        clinicalStaffId: UUID,
        withAddress: Boolean = true
    ): Result<List<ProviderUnit>, Throwable>

    suspend fun get(id: UUID): Result<ProviderUnit, Throwable>
    suspend fun getActiveById(id: UUID): Result<ProviderUnit, Throwable>
    suspend fun getActiveAndShowOnScheduler(range: IntRange?, query: String?): Result<List<ProviderUnit>, Throwable>
    suspend fun addWithAddress(unit: ProviderUnit, address: StructuredAddress): Result<ProviderUnit, Throwable>
    suspend fun updateList(request: List<ProviderUnit>): Result<List<ProviderUnit>, Throwable>
    suspend fun update(providerUnit: ProviderUnit, shouldPublishEvent: Boolean = true): Result<ProviderUnit, Throwable>
    suspend fun updateWithAddress(request: ProviderUnit, address: StructuredAddress): Result<ProviderUnit, Throwable>
    suspend fun getDefaultCasaAlice(): Result<ProviderUnit, Throwable>
    suspend fun getByCnpjs(cnpjs: List<String>): Result<List<ProviderUnit>, Throwable>
    suspend fun getByProviderIdsAndTypes(
        providerIds: List<UUID>,
        types: List<ProviderUnit.Type>,
        range: IntRange,
        searchTerm: String? = null,
        specialtyIdsFilter: List<UUID>? = null,
    ): Result<List<ProviderUnit>, Throwable>

    suspend fun countByProviderIdsAndType(
        providerIds: List<UUID>,
        types: List<ProviderUnit.Type>,
        searchTerm: String? = null,
        specialtyIdsFilter: List<UUID>? = null,
    ): Result<Int, Throwable>

    suspend fun getByProvidersTypeAndSpecialty(
        providerIds: List<UUID> = emptyList(),
        type: ProviderUnit.Type,
        specialtyIds: List<UUID>,
        subSpecialtyIds: List<UUID>? = emptyList(),
        brand: Brand? = null,
    ): Result<List<ProviderUnit>, Throwable>

    suspend fun getByProviderIdsAndSpecialties(
        providerIds: List<UUID>,
        specialtyIds: List<UUID>
    ): Result<List<ProviderUnit>, Throwable>

    suspend fun getByGroupId(id: UUID): Result<List<ProviderUnit>, Throwable>
    suspend fun getByCnpj(cnpj: String): Result<ProviderUnit, Throwable>
    suspend fun getWithEmptyCnes(offset: Int, limit: Int): Result<List<ProviderUnit>, Throwable>
    suspend fun getByUrlSlug(urlSlug: String, withAddress: Boolean = true): Result<ProviderUnit, Throwable>
    suspend fun findAllClinicalDuquesa(withAddress: Boolean = true): Result<List<ProviderUnit>, Throwable>
    suspend fun existsClinicalStaffIds(staffId: UUID, type: ProviderUnit.Type): Result<Boolean, Throwable>
}

data class ProviderUnitFilter(
    val searchToken: String? = null,
    val status: List<Status> = emptyList(),
    val type: List<ProviderUnit.Type> = emptyList(),
    val ids: List<UUID> = emptyList(),
)
