package br.com.alice.membership.route

import br.com.alice.common.coHandler
import br.com.alice.membership.controllers.ProductChangeScheduleJobController
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.recurringRoutes() {
    route("/recurring_subscribers") {
        val productChangeScheduleJobController by inject<ProductChangeScheduleJobController>()

        post("/apply_scheduled_product_changes_requests") {
            coHandler(productChangeScheduleJobController::applyScheduledProductChanges)
        }
    }
}
