package br.com.alice.api.backoffice.routes

import br.com.alice.api.backoffice.controllers.execIndicator.HealthSpecialistResourceBundleController
import br.com.alice.api.backoffice.controllers.execIndicator.HealthcareResourceController
import br.com.alice.api.backoffice.controllers.execIndicator.ResourceBundleSpecialistPricingController
import br.com.alice.api.backoffice.transfers.execIndicator.DownloadCSVRequest
import br.com.alice.common.coHandler
import br.com.alice.common.multipartHandler
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.request.receive
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.patch
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.execIndicatorRoutes() {

    authenticate {
        val healthSpecialistResourceBundleController by inject<HealthSpecialistResourceBundleController>()
        val healthcareResourceController by inject<HealthcareResourceController>()
        val resourceBundleSpecialistPricingController by inject<ResourceBundleSpecialistPricingController>()

        route("healthSpecialistResourceBundle") {
            post("/") { coHandler(healthSpecialistResourceBundleController::upsert) }

            get("/") { coHandler(healthSpecialistResourceBundleController::index) }

            put("/{id}") { coHandler("id", healthSpecialistResourceBundleController::update) }

            get("/{id}") { coHandler("id", healthSpecialistResourceBundleController::get) }

            patch("/{id}") { coHandler("id", healthSpecialistResourceBundleController::patch) }

            get("/{id}/medicalSpecialties") {
                coHandler("id", healthSpecialistResourceBundleController::getMedicalSpecialtiesRelatedToResourceBundle)
            }

            get("/pricing") {
                coHandler(healthSpecialistResourceBundleController::getResourceBundleSpecialtyPricingList)
            }
        }

        route("healthcareResource") {
            get("/tussResources") { coHandler(healthcareResourceController::getTussResources) }
        }

        route("resourceBundleSpecialty") {
            get("/pending") {
                coHandler(healthSpecialistResourceBundleController::getPendingResourceBundleSpecialtiesForPricing)
            }
        }

        route("resourceBundleSpecialtyPricing") {
            post("/csv") {
                val request = call.receive<DownloadCSVRequest>()
                resourceBundleSpecialistPricingController.downloadCSV(this, request)
            }

            get("/effectiveDates") {
                coHandler(resourceBundleSpecialistPricingController::effectiveDates)
            }

            get("/processing") {
                coHandler(resourceBundleSpecialistPricingController::getProcessingResourceBundleSpecialtyPricingUpdate)
            }

            post("/upload") {
                multipartHandler(resourceBundleSpecialistPricingController::upload)
            }
        }

        route("resourceBundleSpecialtyPricingUpdate") {
            get("/{id}/failedLinesFile") {
                val id = call.parameters["id"]
                resourceBundleSpecialistPricingController.downloadFailedLinesFile(this, id.toString())
            }

            get("/") {
                coHandler(resourceBundleSpecialistPricingController::pricingUpdateIndex)
            }
        }
    }
}
