package br.com.alice.atlas.ioc

import br.com.alice.atlas.AtlasDomainConfiguration
import br.com.alice.atlas.SERVICE_NAME
import br.com.alice.atlas.services.AccreditedNetworkFlagshipService
import br.com.alice.atlas.services.AccreditedNetworkFlagshipServiceClient
import br.com.alice.atlas.services.AccreditedProviderDetailsService
import br.com.alice.atlas.services.AccreditedProviderDetailsServiceClient
import br.com.alice.atlas.services.AccreditedSearchListService
import br.com.alice.atlas.services.AccreditedSearchListServiceClient
import br.com.alice.atlas.services.AccreditedService
import br.com.alice.atlas.services.AccreditedServiceClient
import br.com.alice.atlas.services.AccreditedSpecialistService
import br.com.alice.atlas.services.AccreditedSpecialistServiceClient
import br.com.alice.atlas.services.FlagshipsAccreditedLogicService
import br.com.alice.atlas.services.FlagshipsAccreditedLogicServiceClient
import br.com.alice.atlas.services.SiteAccreditedNetworkCategoryService
import br.com.alice.atlas.services.SiteAccreditedNetworkCategoryServiceClient
import br.com.alice.atlas.services.SiteAccreditedNetworkFiltersService
import br.com.alice.atlas.services.SiteAccreditedNetworkFiltersServiceClient
import br.com.alice.atlas.services.SiteAccreditedNetworkService
import br.com.alice.atlas.services.SiteAccreditedNetworkServiceClient
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.rfc.HttpInvoker
import br.com.alice.common.rfc.Invoker
import org.koin.core.qualifier.named
import org.koin.dsl.module

val AtlasDomainClientModule = module(createdAtStart = true) {

    val baseUrl = AtlasDomainConfiguration.baseUrl
    val invoker = HttpInvoker(DefaultHttpClient(), "$baseUrl/rfc")

    single<Invoker>(named(SERVICE_NAME)) { invoker }

    single<AccreditedService> { AccreditedServiceClient(get(named(SERVICE_NAME))) }
    single<AccreditedSpecialistService> { AccreditedSpecialistServiceClient(get(named(SERVICE_NAME))) }
    single<SiteAccreditedNetworkService> { SiteAccreditedNetworkServiceClient(get(named(SERVICE_NAME))) }
    single<SiteAccreditedNetworkCategoryService> { SiteAccreditedNetworkCategoryServiceClient(get(named(SERVICE_NAME))) }
    single<AccreditedProviderDetailsService> { AccreditedProviderDetailsServiceClient(get(named(SERVICE_NAME))) }
    single<SiteAccreditedNetworkFiltersService> { SiteAccreditedNetworkFiltersServiceClient(get(named(SERVICE_NAME))) }
    single<AccreditedNetworkFlagshipService> { AccreditedNetworkFlagshipServiceClient(get(named(SERVICE_NAME))) }
    single<FlagshipsAccreditedLogicService> { FlagshipsAccreditedLogicServiceClient(get(named(SERVICE_NAME))) }
    single<AccreditedSearchListService> { AccreditedSearchListServiceClient(get(named(SERVICE_NAME))) }
}
