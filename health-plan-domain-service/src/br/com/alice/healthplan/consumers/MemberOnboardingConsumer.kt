package br.com.alice.healthplan.consumers

import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberOnboarding
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType
import br.com.alice.data.layer.models.MemberOnboardingFlowType
import br.com.alice.data.layer.models.MemberOnboardingReferencedLink
import br.com.alice.data.layer.models.MemberOnboardingReferencedLinkModel
import br.com.alice.healthplan.services.internal.member_onboarding.MemberOnboardingTaskService
import br.com.alice.member.onboarding.client.MemberOnboardingService
import br.com.alice.member.onboarding.notifier.MemberOnboardingCreatedEvent
import br.com.alice.member.onboarding.notifier.MemberOnboardingStepUpdatedEvent
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.opentelemetry.api.trace.Span
import java.util.UUID

class MemberOnboardingConsumer(
    private val memberOnboardingTaskService: MemberOnboardingTaskService,
    private val memberOnboardingService: MemberOnboardingService,
    private val memberService: MemberService
) : Spannable, Consumer() {

    suspend fun createTaskFromUpdate(event: MemberOnboardingStepUpdatedEvent) =
        span("createTaskFromUpdate") { span ->
            withSubscribersEnvironment {
                event.payload.let { (memberOnboarding, _) ->
                    span.setCommonAttributes(memberOnboarding)

                    if (!memberOnboarding.shouldProcess()) return@let false.success()

                    processEvent(memberOnboarding)
                }
            }
        }

    suspend fun processFromCreate(event: MemberOnboardingCreatedEvent) =
        span("processFromCreate") { span ->
            withSubscribersEnvironment {
                event.payload.let { (memberOnboarding) ->
                    span.setCommonAttributes(memberOnboarding)

                    if (!memberOnboarding.shouldProcess()) return@let false.success()

                    processEvent(memberOnboarding)
                }
            }
        }

    private suspend fun processEvent(memberOnboarding: MemberOnboarding): Result<Any, Throwable> =
        span("processEvent") { span ->
            memberService.getCurrent(memberOnboarding.personId).flatMap { member ->
                if (memberOnboarding.alreadyHasHealthPlanTaskTemplate() && !memberOnboarding.canCreateTask(member))
                    return@flatMap false.success()

                val templates = memberOnboarding.getTemplate()
                span.setAttribute("referenced_link_templates", templates.toString())

                if (!memberOnboarding.canCreateTask(member)) return@flatMap  addReferencedLinks(memberOnboarding.id, templates)

                val ids = templates.map { it.id }
                memberOnboardingTaskService.getIfDontExistTaskByTemplate(memberOnboarding.personId, ids)
                    .takeIf { it.isNotNullOrEmpty() }
                    ?.let {
                        memberOnboardingTaskService.createTaskReferencedLink(memberOnboarding.personId, ids).flatMap {
                            span.setAttribute("referenced_link_tasks", it.toString())
                            addReferencedLinks(memberOnboarding.id, templates + it)
                        }
                    } ?: false.success()
            }
        }

    private suspend fun addReferencedLinks(
        memberOnboardingId: UUID,
        referencedLinks: List<MemberOnboardingReferencedLink>
    ) =
        span("addReferencedLinks") { span ->
            span.setAttribute("referenced_links", referencedLinks.toString())
            memberOnboardingService.addReferencedLink(memberOnboardingId, referencedLinks, shouldOverride = true)
        }

    private suspend fun MemberOnboarding.shouldProcess() =
        memberOnboardingService.getByPersonId(this.personId)
            .map { !it.alreadyHasHealthPlanTask() && it.isScoreMagentaAndHealthDeclarationAndCoverMfcCompleted() }.get()

    private suspend fun MemberOnboarding.getTemplate() =
        if (this.alreadyHasHealthPlanTaskTemplate()) {
            this.referencedLinks
        } else {
            memberOnboardingTaskService.createTaskTemplateReferencedLink(this)
        }

    private fun MemberOnboarding.alreadyHasHealthPlanTask() =
        this.referencedLinks.any { it.model == MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK }

    private fun MemberOnboarding.alreadyHasHealthPlanTaskTemplate() =
        this.referencedLinks.any { it.model == MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK_TEMPLATE }

    private fun MemberOnboarding.isScoreMagentaAndHealthDeclarationAndCoverMfcCompleted() =
        when (this.flowType) {
            MemberOnboardingFlowType.CHILD -> this.isStepCompleted(MemberOnboardingStepType.COVER)
                    && this.isCoverMFCStepCompleted()
            else -> this.isStepCompleted(MemberOnboardingStepType.SCORE_MAGENTA)
                    && this.isStepCompleted(MemberOnboardingStepType.COVER)
                    && this.isCoverMFCStepCompleted()
        }

    private fun MemberOnboarding.canCreateTask(member: Member) =
        member.active && this.finishedSteps >= this.steps.size - 1

    private fun Span.setCommonAttributes(memberOnboarding: MemberOnboarding) =
        this.let {
            it.setAttribute("person_id", memberOnboarding.personId.toString())
            it.setAttribute("member_onboarding_id", memberOnboarding.id.toString())
            it.setAttribute("member_onboarding_referenced_links", memberOnboarding.referencedLinks.toString())
        }
}
