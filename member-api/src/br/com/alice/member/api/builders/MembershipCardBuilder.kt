package br.com.alice.member.api.builders

import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.Product
import br.com.alice.member.api.models.MemberCardResponse
import br.com.alice.member.api.models.MemberCardType

object MembershipCardBuilder {

    private const val DEFAULT_CASSI_ANS_NUMBER = "34665-9"

    private fun isCassiMember(member: Member) = member.cassiMember != null && member.cassiMember?.accountNumber != null

    fun buildCassiMembershipCard(member: Member): MemberCardResponse = MemberCardResponse(
        number = member.cassiMember?.accountNumber ?: "-",
        type = MemberCardType.CASSI,
        ansNumber = DEFAULT_CASSI_ANS_NUMBER,
        startDate = member.cassiMember?.startDate,
        expirationDate = member.cassiMember?.expirationDate,
    )

    fun buildAliceMembershipCard(
        person: Person,
        product: Product,
    ): MemberCardResponse = MemberCardResponse(
        number = person.nationalId,
        productDisplayName = product.title,
        displayName = product.displayName,
        complementName = product.complementName,
        type = MemberCardType.ALICE,
        ansNumber = product.ansNumber ?: "-",
    )

    fun buildMembershipCards(
        person: Person,
        member: Member,
        product: Product,
    ): List<MemberCardResponse> {
        val thirdPartyMemberCards = if (isCassiMember(member))
            listOf(buildCassiMembershipCard(member))
        else emptyList()

        return listOf(buildAliceMembershipCard(person, product)) + thirdPartyMemberCards
    }
}
