# Members API

[![Coverage](https://sonarcloud.io/api/project_badges/measure?project=mono%3Amember-api&metric=coverage&token=bcf117b14e2c55ebefbff7e05a960c1aa4066226)](https://sonarcloud.io/summary/new_code?id=mono%3Amember-api)

[![Code Smells](https://sonarcloud.io/api/project_badges/measure?project=mono%3Amember-api&metric=code_smells&token=bcf117b14e2c55ebefbff7e05a960c1aa4066226)](https://sonarcloud.io/summary/new_code?id=mono%3Amember-api)

[![Duplicated Lines (%)](https://sonarcloud.io/api/project_badges/measure?project=mono%3Amember-api&metric=duplicated_lines_density&token=bcf117b14e2c55ebefbff7e05a960c1aa4066226)](https://sonarcloud.io/summary/new_code?id=mono%3Amember-api)

[![Bugs](https://sonarcloud.io/api/project_badges/measure?project=mono%3Amember-api&metric=bugs&token=bcf117b14e2c55ebefbff7e05a960c1aa4066226)](https://sonarcloud.io/summary/new_code?id=mono%3Amember-api)

Handle all HTTP calls from member mobile apps

### Responsible Team
Platform
- Find us on ``#eng-gestao-de-saude`` on Slack ;)
- [Platform Notion](https://www.notion.so/alicehealth/Platform-Team-b789312d8f064ce0a599010450ea22d8)

### Local development

Requirements
* [docker](https://www.docker.com) 
* [docker-compose](https://docs.docker.com/compose/)

Operations using ``make <operation> <parameters>``

* ``clean`` - delete build files
* ``tests`` - run all tests
* ``run`` - run project on 8080 port 

### Run locally
``make run`` and then you can access http://localhost:8080
