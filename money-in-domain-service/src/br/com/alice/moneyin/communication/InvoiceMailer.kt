package br.com.alice.moneyin.communication

import br.com.alice.common.PaymentMethod
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.core.extensions.toCustomFormat
import br.com.alice.common.core.extensions.toMoneyString
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapOfNotNull
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.communication.email.EmailSender
import br.com.alice.communication.email.model.EmailAddress
import br.com.alice.communication.email.model.EmailAttachment
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.communication.email.model.SendEmailRequest
import br.com.alice.data.layer.models.BolepixPaymentDetail
import br.com.alice.data.layer.models.BoletoPaymentDetail
import br.com.alice.data.layer.models.InvoiceLiquidation
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.models.PixPaymentDetail
import br.com.alice.moneyin.ServiceConfig
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.InvoiceLiquidationService
import br.com.alice.moneyin.client.InvoicePdfService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PortalUrlGeneratorService
import br.com.alice.moneyin.model.InvalidPaymentMethodAndReasonException
import br.com.alice.moneyin.model.InvalidPaymentMethodException
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.time.format.TextStyle
import java.util.Locale
import java.util.UUID

class InvoiceMailer(
    private val sender: EmailSender,
    private val billingAccountablePartyService: BillingAccountablePartyService,
    private val memberInvoiceGroupService: MemberInvoiceGroupService,
    private val invoiceLiquidationService: InvoiceLiquidationService,
    private val invoicePdfService: InvoicePdfService,
    private val portalUrlGeneratorService: PortalUrlGeneratorService
) {

    companion object {
        internal const val BOLETO_FIRST_INVOICE_TEMPLATE = "Template_Boleto_Primeira_Mensalidade"
        internal const val BOLETO_RECURRING_INVOICE_TEMPLATE = "Template_Boleto_Mensalidade_Recorrente"
        internal const val BOLETO_DEFAULTING_INVOICE_TEMPLATE = "Template_Boleto_Inadimplentes"
        internal const val B2B_BOLETO_RECURRING_INVOICE_TEMPLATE = "Template_B2B_Boleto_Mensalidade_Recorrente"
        internal const val INVOICE_DETAILS_B2B_XLSX_FILE_DOWNLOAD =
            "https://money-in-domain-service.wonderland.engineering/download-invoice-b2b"

        internal const val PIX_FIRST_INVOICE_TEMPLATE = "Template_PIX_Primeira_Mensalidade"
        internal const val PIX_RECURRING_INVOICE_TEMPLATE = "Template_PIX_Mensalidade_Recorrente"

        internal const val BOLEPIX_RECURRING_INVOICE_TEMPLATE = "Template_Bolepix_Mensalidade_Recorrente"
        internal const val BOLEPIX_DEFAULTING_INVOICE_TEMPLATE = "Template_Bolepix_Inadimplentes"

        internal const val B2B_FIRST_INVOICE_TEMPLATE = "Template_B2B_Primeira_Mensalidade"
        internal const val B2C_LIQUIDATION_TEMPLATE = "Template_B2C_Boleto_Liquidacao"
        internal const val B2B_LIQUIDATION_TEMPLATE = "Template_B2B_Boleto_Liquidacao"
    }

    suspend fun sendInvoicePaidEmail(
        invoicePayment: InvoicePayment
    ): Result<EmailReceipt, Throwable> = coroutineScope {
        if (invoicePayment.billingAccountablePartyId == null) {
            return@coroutineScope InvalidArgumentException(
                "invoicePayment.billingAccountablePartyId",
                "invoicePayment.billingAccountablePartyId cannot be null"
            ).failure()
        }

        val billingAccountablePartyDeferred = async {
            billingAccountablePartyService.get(
                invoicePayment.billingAccountablePartyId!!
            ).get()
        }
        val referenceDateDeferred = async {
            getReferenceDate(invoicePayment)
        }

        val billingAccountableParty = billingAccountablePartyDeferred.await()
        val referenceDate = referenceDateDeferred.await()

        sender.send(
            SendEmailRequest(
                from = EmailAddress(
                    ServiceConfig.Mailer.defaultB2BSenderName,
                    ServiceConfig.Mailer.defaultB2BSenderEmail
                ),
                to = listOf(
                    EmailAddress(
                        billingAccountableParty.fullNameConsideringType,
                        billingAccountableParty.email
                    )
                ),
                templateName = "Comprovante_pagamento_mensalidade",
                campaignId = ServiceConfig.Mailer.pinPointCampaignId,
                replaceVariables = mapOf(
                    "Attributes.amount" to (invoicePayment.amountPaid ?: invoicePayment.amount).toMoneyString(),
                    "Attributes.month" to referenceDate.toBrazilianDateFormat(),
                    "Attributes.nickname" to billingAccountableParty.firstName,
                ),
            )
        ).success()
    }

    suspend fun send(
        invoicePayment: InvoicePayment,
        paymentReason: PaymentReason,
        billingAccountablePartyId: UUID? = null
    ): Result<EmailReceipt, Throwable> = coroutineScope {
        coResultOf<EmailReceipt, Throwable> {
            val templateName = selectTemplate(invoicePayment, paymentReason)
                ?: throw InvalidPaymentMethodAndReasonException("No e-mail template found for payment method: ${invoicePayment.method} or reason: $paymentReason")

            val invoiceDetailsB2bXlsxFileDownload =
                if (invoicePayment.reason?.isB2B() == true && invoicePayment.reason?.isLiquidation() == false)
                    "$INVOICE_DETAILS_B2B_XLSX_FILE_DOWNLOAD/${invoicePayment.invoiceGroupId}"
                else ""

            val invoiceLiquidationDef = async {
                invoicePayment.invoiceLiquidationId?.let {
                    invoiceLiquidationService.get(it).get()
                }
            }

            val billingAccountablePartyDef = async {
                billingAccountablePartyService.get(
                    billingAccountablePartyId ?: invoicePayment.billingAccountablePartyId!!
                ).get()
            }

            val invoiceFileDef = async {
                invoicePayment.invoiceLiquidationId?.let {
                    invoicePdfService.generateInvoice(invoicePayment.id, true).get()
                }
            }

            val referenceDateDef = async { getReferenceDate(invoicePayment) }

            val paymentUrlDeferred = async {
                portalUrlGeneratorService.mountPortalUrl(invoicePayment.id).getOrNull()
            }

            val billingAccountableParty = billingAccountablePartyDef.await()
            val referenceDate = referenceDateDef.await()
            val invoiceLiquidation = invoiceLiquidationDef.await()
            val invoiceFile = invoiceFileDef.await()
            val paymentUrl = paymentUrlDeferred.await()

            send(
                invoicePayment = invoicePayment,
                contactName = billingAccountableParty.fullName,
                email = billingAccountableParty.email,
                paymentUrl = paymentUrl,
                templateName = templateName,
                referenceDate = invoiceLiquidation?.dueDate ?: referenceDate,
                invoiceDetailsB2bXlsxFileDownload = invoiceDetailsB2bXlsxFileDownload,
                paymentReason = paymentReason,
                invoiceLiquidation = invoiceLiquidation,
                invoiceFile = invoiceFile,
            )
        }.thenError {
            logger.error("Deu ruim", it)
        }
    }

    private suspend fun getReferenceDate(invoicePayment: InvoicePayment) = invoicePayment.invoiceGroupId?.let { id ->
        memberInvoiceGroupService.get(id).map { it.referenceDate }.getOrNullIfNotFound()
    }
        ?: invoicePayment.memberInvoiceId?.let {
            memberInvoiceGroupService.getByMemberInvoices(it)
                .map { it.referenceDate }
                .getOrNullIfNotFound()
        }
        ?: invoicePayment.createdAt.toLocalDate()

    private suspend fun send(
        invoicePayment: InvoicePayment,
        contactName: String,
        email: String,
        paymentUrl: String?,
        templateName: String,
        referenceDate: LocalDate,
        invoiceDetailsB2bXlsxFileDownload: String,
        paymentReason: PaymentReason,
        invoiceLiquidation: InvoiceLiquidation? = null,
        invoiceFile: ByteArray? = null
    ): EmailReceipt {
        val recipientParams = email.split("@")

        val emailSender = if (paymentReason != PaymentReason.B2B_REGULAR_PAYMENT) EmailAddress(
            ServiceConfig.Mailer.defaultSenderName,
            ServiceConfig.Mailer.defaultSenderEmail,
        ) else EmailAddress(
            ServiceConfig.Mailer.defaultB2BSenderName,
            ServiceConfig.Mailer.defaultB2BSenderEmail,
        )
        val pinPointCampaignId = ServiceConfig.Mailer.pinPointCampaignId

        val fromParams = emailSender.email.split("@")

        logger.info(
            "Sending email for invoice payment",
            "invoice_payment" to invoicePayment,
            "contact_name" to contactName,
            "email" to email,
            "recipient_owner" to recipientParams.first(),
            "recipient_domain" to recipientParams.last(),
            "from_username" to fromParams.first(),
            "from_domain" to fromParams.last(),
            "pinpoint_campaign_id" to pinPointCampaignId,
            "payment_url" to paymentUrl,
            "template_name" to templateName,
        )

        val sendEmailRequest = SendEmailRequest(
            from = emailSender,
            to = listOf(EmailAddress(contactName, email)),
            templateName = templateName,
            campaignId = pinPointCampaignId,
            replaceVariables = createReplaceVariables(
                invoicePayment,
                contactName,
                paymentUrl,
                referenceDate,
                invoiceDetailsB2bXlsxFileDownload,
                invoiceLiquidation,
            ),
            attachments = invoiceFile?.let {
                listOf(
                    EmailAttachment(
                        "fatura.pdf",
                        content = it,
                        type = "application/pdf"
                    )
                )
            }
        )

        logger.info("request", sendEmailRequest)
        return sender.send(sendEmailRequest)
    }

    private fun createReplaceVariables(
        invoicePayment: InvoicePayment,
        contactName: String,
        paymentUrl: String?,
        referenceDate: LocalDate,
        invoiceDetailsB2bXlsxFileDownload: String,
        invoiceLiquidation: InvoiceLiquidation? = null,
    ): Map<String, String> {
        val year = referenceDate.year.toString()

        val defaultAttributes = mapOf(
            "Attributes.boleto" to paymentUrl.orEmpty(),
            "Attributes.invoiceDetailsB2bXlsxFileDownload" to invoiceDetailsB2bXlsxFileDownload,
            "Attributes.month" to referenceDate.month.getDisplayName(TextStyle.FULL, Locale("pt", "BR")),
            "Attributes.year" to year,
        )

        return when (invoicePayment.method) {
            PaymentMethod.BOLETO ->
                defaultAttributes + mapOf(
                    "Attributes.nickname" to contactName,
                    "Attributes.digitableLine" to (invoicePayment.paymentDetail as BoletoPaymentDetail).barcode!!,
                    "Attributes.dueDate" to (invoicePayment.paymentDetail as BoletoPaymentDetail).dueDate.toBrazilianDateFormat(),
                    "Attributes.referenceDate" to referenceDate.toCustomFormat("yyyy-MM-01"),
                    "Attributes.amount" to invoicePayment.amount.toMoneyString(),
                    "Attributes.boletoPaymentUrl" to (invoicePayment.paymentDetail as BoletoPaymentDetail).paymentUrl!!,
                )

            PaymentMethod.BOLEPIX ->
                defaultAttributes + mapOf(
                    "Attributes.nickname" to contactName,
                    "Attributes.paymentCodePix" to (invoicePayment.paymentDetail as BolepixPaymentDetail).paymentCodePix!!,
                    "Attributes.pixPaymentUrl" to (invoicePayment.paymentDetail as BolepixPaymentDetail).pixPaymentUrl!!,
                    "Attributes.barcodeBoleto" to (invoicePayment.paymentDetail as BolepixPaymentDetail).barcodeBoleto!!,
                    "Attributes.boletoPaymentUrl" to (invoicePayment.paymentDetail as BolepixPaymentDetail).boletoPaymentUrl!!,
                    "Attributes.dueDate" to (invoicePayment.paymentDetail as BolepixPaymentDetail).dueDate!!.toBrazilianDateFormat(),
                    "Attributes.amount" to invoicePayment.amount.toMoneyString(),
                )

            PaymentMethod.PIX ->
                defaultAttributes + mapOf(
                    "Attributes.nickname" to contactName,
                    "Attributes.barcode" to (invoicePayment.paymentDetail as PixPaymentDetail).paymentCode!!,
                )

            PaymentMethod.SIMPLE_CREDIT_CARD ->
                throw InvalidPaymentMethodException("Credit Card is not supported yet")
        }.let {
            if (invoiceLiquidation != null) {
                it + mapOfNotNull(
                    "Attributes.currentParcel" to invoiceLiquidation.installment.toString(),
                    "Attributes.totalParcel" to invoiceLiquidation.totalInstallments.toString(),
                    Pair(
                        "Attributes.dueDate",
                        invoiceLiquidation.dueDate.toBrazilianDateFormat()
                    ).takeIf { invoicePayment.method == PaymentMethod.PIX },
                )
            } else it
        }
    }

    suspend fun sendTemplateToEmail(
        email: String,
        template: String,
        params: Map<String, String>,
        attachments: List<EmailAttachment> = emptyList()
    ) =
        sender.send(
            SendEmailRequest(
                from = EmailAddress(
                    ServiceConfig.Mailer.defaultB2BSenderName,
                    ServiceConfig.Mailer.defaultB2BSenderEmail
                ),
                to = listOf(EmailAddress("Cliente Alice", email)),
                templateName = template,
                campaignId = ServiceConfig.Mailer.pinPointCampaignId,
                replaceVariables = params,
                attachments = attachments
            )
        ).success()


    private fun selectTemplate(invoicePayment: InvoicePayment, paymentReason: PaymentReason) =
        if (paymentReason.isLiquidation()) {
            if (paymentReason.isB2B())
                B2B_LIQUIDATION_TEMPLATE
            else
                B2C_LIQUIDATION_TEMPLATE

        } else
            when (invoicePayment.method) {
                PaymentMethod.BOLETO -> selectBoletoTemplate(paymentReason)
                PaymentMethod.PIX -> selectPixTemplate(paymentReason)
                PaymentMethod.BOLEPIX -> selectBolepixTemplate(paymentReason)
                else -> null
            }

    private fun selectBoletoTemplate(paymentReason: PaymentReason) =
        when (paymentReason) {
            PaymentReason.OVERDUE_PAYMENT -> BOLETO_DEFAULTING_INVOICE_TEMPLATE
            PaymentReason.FIRST_PAYMENT, PaymentReason.B2C_PRE_ACTIVATION_PAYMENT -> BOLETO_FIRST_INVOICE_TEMPLATE
            PaymentReason.REGULAR_PAYMENT, PaymentReason.B2C_PRE_ACTIVATION_PAYMENT -> BOLETO_RECURRING_INVOICE_TEMPLATE
            PaymentReason.B2B_REGULAR_PAYMENT -> B2B_BOLETO_RECURRING_INVOICE_TEMPLATE
            PaymentReason.B2B_FIRST_PAYMENT, PaymentReason.B2B_PRE_ACTIVATION_PAYMENT -> B2B_FIRST_INVOICE_TEMPLATE
            PaymentReason.B2B_LIQUIDATION -> B2B_BOLETO_RECURRING_INVOICE_TEMPLATE //TODO: Create specific templates
            PaymentReason.B2C_LIQUIDATION -> BOLETO_RECURRING_INVOICE_TEMPLATE //TODO: Create specific templates
        }

    private fun selectPixTemplate(paymentReason: PaymentReason) =
        when (paymentReason) {
            PaymentReason.FIRST_PAYMENT, PaymentReason.B2C_PRE_ACTIVATION_PAYMENT -> PIX_FIRST_INVOICE_TEMPLATE
            PaymentReason.REGULAR_PAYMENT -> PIX_RECURRING_INVOICE_TEMPLATE
            PaymentReason.B2B_FIRST_PAYMENT, PaymentReason.B2B_PRE_ACTIVATION_PAYMENT -> B2B_FIRST_INVOICE_TEMPLATE
            else -> null
        }

    private fun selectBolepixTemplate(paymentReason: PaymentReason) =
        when (paymentReason) {
            PaymentReason.OVERDUE_PAYMENT -> BOLEPIX_DEFAULTING_INVOICE_TEMPLATE
            PaymentReason.REGULAR_PAYMENT -> BOLEPIX_RECURRING_INVOICE_TEMPLATE
            PaymentReason.B2B_FIRST_PAYMENT, PaymentReason.B2B_PRE_ACTIVATION_PAYMENT -> B2B_FIRST_INVOICE_TEMPLATE
            PaymentReason.B2B_REGULAR_PAYMENT, PaymentReason.B2C_PRE_ACTIVATION_PAYMENT -> B2B_BOLETO_RECURRING_INVOICE_TEMPLATE
            else -> throw InvalidPaymentMethodAndReasonException("The payment reason $paymentReason is not supported by Bolepix")
        }

}
