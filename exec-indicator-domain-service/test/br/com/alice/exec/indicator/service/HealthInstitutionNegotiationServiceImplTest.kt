package br.com.alice.exec.indicator.service

import br.com.alice.common.MvUtil
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.services.HealthInstitutionNegotiationModelDataService
import br.com.alice.exec.indicator.client.HealthInstitutionNegotationFilters
import br.com.alice.exec.indicator.client.HealthcareBundleService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.provider.client.ProviderUnitGroupService
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class HealthInstitutionNegotiationServiceImplTest {

    private val data: HealthInstitutionNegotiationModelDataService = mockk()
    private val healthcareBundleService: HealthcareBundleService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val providerUnitGroupService: ProviderUnitGroupService = mockk()

    private val service = HealthInstitutionNegotiationServiceImpl(
        data,
        healthcareBundleService,
        providerUnitService,
        providerUnitGroupService
    )

    private val healthInstitutionNegotiation = TestModelFactory.buildHealthInstitutionNegotiation()
    private val healthInstitutionNegotiationList = listOf(
        TestModelFactory.buildHealthInstitutionNegotiation(),
        TestModelFactory.buildHealthInstitutionNegotiation()
    )

    private val providerUnitGroup = TestModelFactory.buildProviderUnitGroup()
    private val providerUnit = TestModelFactory.buildProviderUnit(providerUnitGroupId = providerUnitGroup.id)

    private val healthcareBundle = TestModelFactory.buildHealthcareBundle(primaryTuss = "**********")

    @AfterTest
    fun clear() = clearAllMocks()

    @Test
    fun `#add should call data service correctly`() = runBlocking {
        coEvery {
            data.add(healthInstitutionNegotiation.toModel())
        } returns healthInstitutionNegotiation.toModel().success()

        val result = service.add(healthInstitutionNegotiation)
        assertThat(result).isSuccessWithData(healthInstitutionNegotiation)
    }

    @Test
    fun `#add should call data service correctly when tableType is DEFAULT_BUNDLE_TABLE`() = runBlocking {
        val negotiation = healthInstitutionNegotiation.copy(
            tableType = HealthInstitutionNegotiationServiceImpl.DEFAULT_BUNDLE_TABLE
        )

        coEvery {
            healthcareBundleService.getByCodeAndProviderUnitGroup(
                healthInstitutionNegotiation.code,
                healthInstitutionNegotiation.providerUnitGroupId
            )
        } returns healthcareBundle.success()

        coEvery {
            data.add(negotiation.copy(bundlePrimaryTuss = healthcareBundle.primaryTuss).toModel())
        } returns healthInstitutionNegotiation.toModel().success()

        val result = service.add(negotiation)

        assertThat(result).isSuccessWithData(healthInstitutionNegotiation)

        coVerifyOnce { healthcareBundleService.getByCodeAndProviderUnitGroup(any(), any()) }
    }

    @Test
    fun `#update should call data service correctly`() = runBlocking {
        coEvery {
            data.update(healthInstitutionNegotiation.toModel())
        } returns healthInstitutionNegotiation.toModel().success()

        val result = service.update(healthInstitutionNegotiation)
        assertThat(result).isSuccessWithData(healthInstitutionNegotiation)
    }

    @Test
    fun `#update should run correctly even if healthcare bundle is not found`() = runBlocking {
        val negotiation = healthInstitutionNegotiation.copy(
            tableType = HealthInstitutionNegotiationServiceImpl.DEFAULT_BUNDLE_TABLE
        )

        coEvery {
            healthcareBundleService.getByCodeAndProviderUnitGroup(
                healthInstitutionNegotiation.code,
                healthInstitutionNegotiation.providerUnitGroupId
            )
        } returns NotFoundException().failure()

        coEvery {
            data.update(negotiation.toModel())
        } returns healthInstitutionNegotiation.toModel().success()

        val result = service.update(negotiation)
        assertThat(result).isSuccessWithData(healthInstitutionNegotiation)

        coVerifyOnce { healthcareBundleService.getByCodeAndProviderUnitGroup(any(), any()) }
    }

    @Test
    fun `#getByExternalId should call data service correctly`() = runBlocking {
        coEvery {
            data.findOne(queryEq {
                where { this.externalId.eq(healthInstitutionNegotiation.externalId) }
            })
        } returns healthInstitutionNegotiation.toModel().success()

        val result = service.getByExternalId(healthInstitutionNegotiation.externalId)
        assertThat(result).isSuccessWithData(healthInstitutionNegotiation)
    }

    @Test
    fun `#addMany should call data service correctly`() = runBlocking {
        coEvery {
            data.add(healthInstitutionNegotiationList[0].toModel())
        } returns healthInstitutionNegotiationList[0].toModel().success()
        coEvery {
            data.add(healthInstitutionNegotiationList[1].toModel())
        } returns healthInstitutionNegotiationList[1].toModel().success()

        val result = service.addMany(healthInstitutionNegotiationList)
        assertThat(result).isSuccessWithData(healthInstitutionNegotiationList)

        coVerify(exactly = 2) { data.add(any()) }
    }

    @Test
    fun `#updateMany should call data service correctly`() = runBlocking {
        coEvery {
            data.update(healthInstitutionNegotiationList[0].toModel())
        } returns healthInstitutionNegotiationList[0].toModel().success()
        coEvery {
            data.update(healthInstitutionNegotiationList[1].toModel())
        } returns healthInstitutionNegotiationList[1].toModel().success()

        val result = service.updateMany(healthInstitutionNegotiationList)
        assertThat(result).isSuccessWithData(healthInstitutionNegotiationList)

        coVerify(exactly = 2) { data.update(any()) }
    }

    @Test
    fun `#findByHealthcareResourceId should call data service correctly`() = runBlocking {
        val healthcareResourceId = healthInstitutionNegotiation.healthcareResourceId

        coEvery {
            data.find(queryEq {
                where { this.healthcareResourceId.eq(healthcareResourceId) }
            })
        } returns healthInstitutionNegotiationList.toModel().success()

        val result = service.findByHealthcareResourceId(healthcareResourceId)
        assertThat(result).isSuccessWithData(healthInstitutionNegotiationList)

    }

    @Test
    fun `#list should call data service correctly with all fields`() = runBlocking {
        val filters = HealthInstitutionNegotationFilters(
            query = "query",
            providerUnitGroupId = providerUnitGroup.id,
            providerUnitId = providerUnit.id,
            guiaType = MvUtil.TISS.PS,
            validAfter = LocalDate.now().minusDays(3),
            validBefore = LocalDate.now().plusDays(3),
            limit = 10,
            offset = 0
        )

        coEvery {
            providerUnitService.get(providerUnit.id)
        } returns providerUnit.success()

        coEvery {
            data.find(queryEq {
                where {
                    this.activeHealthcareResource.eq(true)
                        .and(this.searchTokens.search(filters.query!!))
                        .and(this.providerUnitGroupId.eq(filters.providerUnitGroupId!!))
                        .and(this.validAfter.lessEq(filters.validAfter!!))
                        .and(this.validBefore.greater(filters.validBefore!!))
                        .and(this.code.inList(listOf("123", "321")))
                }.orderBy { this.description }
                    .sortOrder { asc }
                    .limit { filters.limit }
                    .offset { filters.offset }
            })
        } returns healthInstitutionNegotiationList.toModel().success()

        withFeatureFlag(FeatureNamespace.EXEC_INDICATOR, "emergency_room_procedures", listOf("123", "321")) {
            val result = service.list(filters)
            assertThat(result).isSuccessWithData(healthInstitutionNegotiationList)
        }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#list should call data service correctly with only query`() = runBlocking {
        val filters = HealthInstitutionNegotationFilters(query = "query")

        coEvery {
            data.find(queryEq {
                where {
                    this.activeHealthcareResource.eq(true)
                        .and(this.searchTokens.search(filters.query!!))
                        .and(this.validAfter.lessEq(LocalDate.now()))
                        .and(
                            scope(this.validBefore.greater(LocalDate.now()).or(this.validBefore.isNull()))
                        )
                        .and(this.code.notInList(listOf("123", "321")))
                }.orderBy { description }
                    .sortOrder { asc }
                    .offset { filters.offset }
                    .limit { filters.limit }
            })
        } returns healthInstitutionNegotiationList.toModel().success()

        withFeatureFlag(FeatureNamespace.EXEC_INDICATOR, "emergency_room_procedures", listOf("123", "321")) {
            val result = service.list(filters)
            assertThat(result).isSuccessWithData(healthInstitutionNegotiationList)
        }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findByCodeAndProviderUnitGroup should call data service correctly`() = runBlocking {
        val code = "123456"
        val providerUnitGroupId = RangeUUID.generate()
        val negotiations = listOf(
            healthInstitutionNegotiation.copy(
                code = code,
                providerUnitGroupId = providerUnitGroupId,
            )
        )

        coEvery {
            data.find(queryEq {
                where {
                    this.code.eq(code)
                        .and(this.providerUnitGroupId.eq(providerUnitGroupId))
                        .and(this.validAfter.lessEq(LocalDate.now()))
                        .and(scope(this.validBefore.isNull().or(this.validBefore.greater(LocalDate.now()))))
                }
            })
        } returns negotiations.toModel().success()

        val result = service.findByCodeAndProviderUnitGroup(code, providerUnitGroupId)
        assertThat(result).isSuccessWithData(negotiations)
    }
}
