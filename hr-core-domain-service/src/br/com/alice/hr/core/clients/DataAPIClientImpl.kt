package br.com.alice.hr.core.clients

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.hr.core.clients.interfaces.DataAPIClient
import br.com.alice.hr.core.ServiceConfig
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMapError
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.get
import io.ktor.client.statement.bodyAsText
import java.util.UUID


const val TIMEOUT_LIMIT = 20_000L

class DataAPIClientImpl(
    private val client: HttpClient = DefaultHttpClient(
        {
            expectSuccess = true
            install(ContentNegotiation) {
                gsonSnakeCase {
                    registerTypeAdapter(PopulationDataResponse::class.java, PopulationDataResponseDeserializer())
                    registerTypeAdapter(FrequencyCoordinationDataResponse::class.java, FrequencyCoordinationDataResponseDeserializer())
                }

            }
        },
        timeoutInMillis = TIMEOUT_LIMIT
    )
) : DataAPIClient {
    private val baseUrl = ServiceConfig.DataAPI.baseUrl

    override suspend fun getPopulationDataByCompany(companyId: UUID): PopulationDataResponse =
        execRequest {
            client.get("$baseUrl/$companyId/population-data").body<PopulationDataResponse>()
        }

    override suspend fun getFrequencyCoordinationByCompany(companyId: UUID): FrequencyCoordinationDataResponse =
        execRequest {
            client.get("$baseUrl/$companyId/frequency-coordination").body<FrequencyCoordinationDataResponse>()
        }

    private suspend fun <T : Any> execRequest(body: suspend () -> T): T = coResultOf<T, Throwable>(body)
        .flatMapError {
            logger.error(
                "[DataAPIClient::execRequest] Error in Data API request",
                "error" to it,
                "message" to it.message,
            )

            if (it is ClientRequestException) {
                val responseString = it.response.bodyAsText()
                return@flatMapError DataAPIClientException(message = responseString, cause = it).failure()
            }

            it.failure()
        }.get()
}
