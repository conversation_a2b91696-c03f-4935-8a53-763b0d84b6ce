package br.com.alice.api.memberwannabe.ioc

import br.com.alice.api.memberwannabe.ServiceConfig
import br.com.alice.api.memberwannabe.controllers.CacheController
import br.com.alice.api.memberwannabe.controllers.FeatureConfigController
import br.com.alice.api.memberwannabe.controllers.HealthProductSimulationController
import br.com.alice.api.memberwannabe.controllers.HealthProductSimulationV2Controller
import br.com.alice.api.memberwannabe.controllers.HealthTeamController
import br.com.alice.api.memberwannabe.controllers.ProductController
import br.com.alice.api.memberwannabe.controllers.ProviderController
import br.com.alice.api.memberwannabe.controllers.UpdateContactController
import br.com.alice.api.memberwannabe.controllers.VicController
import br.com.alice.api.memberwannabe.controllers.site_accredited_network.AccreditedController
import br.com.alice.api.memberwannabe.controllers.site_accredited_network.AccreditedSpecialistController
import br.com.alice.api.memberwannabe.controllers.site_accredited_network.AccreditedV2Controller
import br.com.alice.api.memberwannabe.controllers.site_accredited_network.MapsAddressController
import br.com.alice.api.memberwannabe.controllers.site_accredited_network.SiteAccreditedNetworkFiltersController
import br.com.alice.api.memberwannabe.controllers.site_accredited_network.SiteAccreditedNetworkFiltersV2Controller
import br.com.alice.api.memberwannabe.controllers.site_accredited_network.SiteAccreditedNetworkProviderDetailsController
import br.com.alice.api.memberwannabe.services.accredited_network.AccreditedNetworkProviderDetailsService
import br.com.alice.api.memberwannabe.services.simulation_result.VicSimulationResultService
import br.com.alice.api.memberwannabe.services.site_accredited_network.AccreditedAndPriorityListLogicService
import br.com.alice.api.memberwannabe.services.site_accredited_network.AccreditedFilterService
import br.com.alice.api.memberwannabe.services.site_accredited_network.AccreditedLocationService
import br.com.alice.api.memberwannabe.services.site_accredited_network.AccreditedService
import br.com.alice.api.memberwannabe.services.site_accredited_network.FindAccreditedService
import br.com.alice.api.memberwannabe.services.site_accredited_network.SiteAccreditedNetworkFiltersV2Service
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.googlemaps.clients.GoogleMapsClient
import br.com.alice.common.redis.CacheFactory
import br.com.alice.data.layer.MEMBER_WANNABE_API_ROOT_SERVICE_NAME
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig
import org.koin.dsl.module

val ServiceModule = module(createdAtStart = true) {
    single { HoconApplicationConfig(ConfigFactory.load("application.conf")) }

    val cache = CacheFactory.newInstance("member-wannabe-api-cache")

    // API internal service
    single { AccreditedNetworkProviderDetailsService(get(), get(), get()) }
    single { VicSimulationResultService(get(), get(), get()) }
    single { SiteAccreditedNetworkFiltersV2Service(get()) }
    single { AccreditedFilterService(get()) }
    single { AccreditedService(get(), get(), get(), get(), get()) }
    single { FindAccreditedService(get(), get(), get()) }
    single { AccreditedLocationService(get()) }
    single { AccreditedAndPriorityListLogicService(get(), get(), get()) }

    // Controllers
    single { HealthProductSimulationController(get(), get(), get(), get(), get()) }
    single { HealthProductSimulationV2Controller(get()) }
    single { VicController(get()) }
    single { HealthController(MEMBER_WANNABE_API_ROOT_SERVICE_NAME) }
    single { FeatureConfigController(get()) }
    single { HealthTeamController(get(), get()) }
    single { ProductController(get(), get(), get(), get()) }
    single { ProviderController(get(), get()) }
    single { UpdateContactController(get()) }
    single { CacheController(cache, get(), get()) }
    single { SiteAccreditedNetworkFiltersController(get()) }
    single { MapsAddressController(get(), get()) }
    single { SiteAccreditedNetworkFiltersV2Controller(get()) }
    single { AccreditedController(get()) }
    single { AccreditedSpecialistController(get()) }
    single { SiteAccreditedNetworkProviderDetailsController(get()) }
    single { AccreditedV2Controller(get()) }

    // Using custom Google Maps Key
    single { GoogleMapsClient(ServiceConfig.googleMapsKey) }
}
