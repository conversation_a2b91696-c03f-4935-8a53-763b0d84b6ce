package br.com.alice.api.backoffice.mappers.execIndicator

import br.com.alice.api.backoffice.ServiceConfig
import br.com.alice.api.backoffice.transfers.Colors
import br.com.alice.api.backoffice.transfers.FriendlyEnumResponse
import br.com.alice.api.backoffice.transfers.execIndicator.ResourceSpecialistPricingIndexResponse
import br.com.alice.api.backoffice.transfers.execIndicator.ResourceSpecialistPricingProcessingDetailsResponse
import br.com.alice.common.core.extensions.toCustomFormat
import br.com.alice.common.mappers.CommonOutputMapper
import br.com.alice.common.transfers.ListPaginatedResponse
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdateStatus
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyPricingUpdateHistoryItem
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyPricingUpdateHistoryWithCount
import io.ktor.http.Parameters
import java.util.UUID

object ResourceSpecialistPricingOutputMapper {

    fun ResourceBundleSpecialtyPricingUpdateHistoryWithCount.toPaginatedResponse(queryParams: Parameters): ListPaginatedResponse<ResourceSpecialistPricingIndexResponse> =
        ListPaginatedResponse(
            pagination = CommonOutputMapper.toPaginationResponse(queryParams, count),
            results = items.map { it.toIndexResponse() }
        )

    private fun ResourceBundleSpecialtyPricingUpdateHistoryItem.toIndexResponse(): ResourceSpecialistPricingIndexResponse {
        val status = determineStatus(this)

        return ResourceSpecialistPricingIndexResponse(
            id = id,
            filename = fileName,
            downloadFileUrl = fileUrl,
            downloadFailedLinesFileUrl = getDownloadFailedLinesFileUrl(id, failedRowsCount),
            createdAt = createdAt.toCustomFormat("dd/MM/yyyy | HH:mm:ss"),
            createdBy = createdByStaff?.fullName ?: "",
            status = status.toFriendlyEnumResponse(),
            processingDetails = ResourceSpecialistPricingProcessingDetailsResponse(
                totalItems = rowsCount,
                errorItems = failedRowsCount,
                friendlyDescription = getProcessingDetailsFriendlyName(this)
            )
        )
    }

    private fun determineStatus(item: ResourceBundleSpecialtyPricingUpdateHistoryItem): ResourceBundleSpecialtyPricingUpdateStatus {
        return when {
            item.parsingError != null -> ResourceBundleSpecialtyPricingUpdateStatus.PARSING_ERROR
            item.completedAt == null -> ResourceBundleSpecialtyPricingUpdateStatus.PROCESSING
            item.failedRowsCount > 0 -> ResourceBundleSpecialtyPricingUpdateStatus.PROCESSED_WITH_ERRORS
            else -> ResourceBundleSpecialtyPricingUpdateStatus.PROCESSED
        }
    }

    private fun getDownloadFailedLinesFileUrl(id: UUID, failedRowsCount: Int) =
        if (failedRowsCount > 0) "${ServiceConfig.baseUrl}/resourceBundleSpecialtyPricingUpdate/$id/failedLines" else null

    private fun getProcessingDetailsFriendlyName(item: ResourceBundleSpecialtyPricingUpdateHistoryItem): String {
        return when {
            item.parsingError != null -> "Arquivo inválido"
            item.failedRowsCount > 0 -> "${item.rowsCount - item.failedRowsCount}/${item.rowsCount} linhas cadastradas"
            else -> "${item.rowsCount}/${item.rowsCount} linhas cadastradas"
        }
    }

    private fun ResourceBundleSpecialtyPricingUpdateStatus.toFriendlyEnumResponse() =
        FriendlyEnumResponse(
            friendlyName = description,
            value = this,
            color = when (this) {
                ResourceBundleSpecialtyPricingUpdateStatus.PROCESSED -> Colors.GREEN
                ResourceBundleSpecialtyPricingUpdateStatus.PROCESSED_WITH_ERRORS -> Colors.YELLOW
                ResourceBundleSpecialtyPricingUpdateStatus.PARSING_ERROR -> Colors.RED
                ResourceBundleSpecialtyPricingUpdateStatus.PROCESSING -> Colors.GRAY
            }
        )
}
