package br.com.alice.api.healthcareops.converters.business

import br.com.alice.api.healthcareops.converters.OngoingCompanyDealConverter
import br.com.alice.api.healthcareops.converters.ProductConverter
import br.com.alice.api.healthcareops.models.BillingAccountablePartyResponse
import br.com.alice.api.healthcareops.models.CompanyBillingAccountableParty
import br.com.alice.api.healthcareops.models.CompanyInfoResponse
import br.com.alice.api.healthcareops.models.CompanyInvoiceResponse
import br.com.alice.api.healthcareops.models.CompanyMemberInvoiceGroupInfo
import br.com.alice.api.healthcareops.models.CompanyMemberInvoiceGroupPayment
import br.com.alice.api.healthcareops.models.CreateCompanyRequest
import br.com.alice.api.healthcareops.models.ProductInfo
import br.com.alice.api.healthcareops.models.UpdateCompanyRequest
import br.com.alice.common.convertTo
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.models.State
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.BillingAccountableParty
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyAddress
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.data.layer.models.OngoingCompanyDeal
import br.com.alice.data.layer.models.Product
import br.com.alice.data.layer.models.SalesFirm
import br.com.alice.moneyin.models.InvoicePaymentWithPortalUrl
import br.com.alice.moneyin.models.getPortalUrl
import java.time.LocalDate

object CompanyConverter {
    private fun String.toLocalDateStartOfDay() = LocalDate.parse(this).atStartOfDay()

    fun Company.toCompanyInfoResponse(
        products: List<Product>? = null,
        billingAccountableParty: BillingAccountableParty? = null,
        deals: List<OngoingCompanyDeal> = emptyList(),
        salesFirms: List<SalesFirm> = emptyList()
    ): CompanyInfoResponse {
        val availableProducts = products?.map { ProductConverter.convert(it) } ?: emptyList()
        val defaultProduct = availableProducts?.firstOrNull { it.id == this?.defaultProductId?.toString() }

        return CompanyInfoResponse(
            id = this.id,
            name = this.name,
            legalName = this.legalName,
            cnpj = this.cnpj,
            email = this.email,
            phoneNumber = this.phoneNumber,
            address = this.address,
            billingAccountablePartyId = this.billingAccountablePartyId,
            availableProducts = availableProducts,
            defaultProductId = defaultProduct,
            billingAccountableParty = billingAccountableParty?.convertTo(
                BillingAccountablePartyResponse::class
            ),
            totalEmployees = this.totalEmployees,
            status = this.status,
            companySize = this.companySize,
            deals = deals.toCompanyDealResponse(salesFirms),
            companyBusinessUnit = this.companyBusinessUnit
        )
    }

    suspend fun Company.toCompanyInvoiceResponse(
        billingAccountableParty: BillingAccountableParty,
        invoiceGroups: List<MemberInvoiceGroup>,
        invoicePayments: List<InvoicePayment>,
        invoicePaymentsWithPortalUrl: List<InvoicePaymentWithPortalUrl>
    ) = CompanyInvoiceResponse(
        id = this.id,
        name = this.name,
        legalName = this.legalName,
        cnpj = this.cnpj,
        email = this.email,
        phoneNumber = this.phoneNumber,
        address = this.address,
        billingAccountableParty = billingAccountableParty.toCompanyBillingAccountableParty(),
        memberInvoiceGroup = invoiceGroups.pmap { memberInvoiceGroup ->
            memberInvoiceGroup.toCompanyMemberInvoiceGroupInfo(
                invoicePayments.filter { it.invoiceGroupId == memberInvoiceGroup.id },
                invoicePaymentsWithPortalUrl
            )
        }.sortedBy { it.referenceDate }
    )

    private fun BillingAccountableParty.toCompanyBillingAccountableParty() =
        CompanyBillingAccountableParty(
            id = this.id,
            firstName = this.firstName,
            lastName = this.lastName,
            type = this.type,
            nationalId = this.nationalId,
            email = this.email,
        )

    private suspend fun MemberInvoiceGroup.toCompanyMemberInvoiceGroupInfo(
        payments: List<InvoicePayment>,
        invoicePaymentWithPortalUrl: List<InvoicePaymentWithPortalUrl>
    ) =
        CompanyMemberInvoiceGroupInfo(
            id = this.id,
            externalId = this.externalId ?: "-",
            billingAccountablePartyId = this.billingAccountablePartyId,
            referenceDate = this.referenceDate,
            dueDate = this.dueDate,
            status = this.status,
            payments = payments.pmap { invoicePayment ->
                invoicePayment.toCompanyMemberInvoiceGroupPayment(
                    portalUrl = invoicePaymentWithPortalUrl.getPortalUrl(invoicePayment.id)
                )
            },
            type = this.type,
        )


    private fun InvoicePayment.toCompanyMemberInvoiceGroupPayment(portalUrl: String?) =
        CompanyMemberInvoiceGroupPayment(
            id = this.id,
            totalAmount = this.amount,
            status = this.status,
            invoiceGroupId = this.invoiceGroupId,
            externalId = this.externalId,
            paymentDetail = this.paymentDetail,
            createdAt = this.createdAt,
            canceledReason = this.canceledReason,
            approvedAt = this.approvedAt,
            reason = this.reason,
            method = this.method,
            portalUrl = portalUrl
        )

    private fun List<Product>.productInfoConverter(): List<ProductInfo> =
        this.map { product ->
            ProductInfo(
                productId = product.id,
                title = product.title,
                ansNumber = product.ansNumber,
                displayName = product.displayName,
                priceListing = product.priceListing,
                type = product.type,
                accommodation = product.accommodation,
                hasNationalCoverage = product.hasNationalCoverage,
            )
        }

    fun CreateCompanyRequest.toBillingAccountableParty() = BillingAccountableParty(
        firstName = this.name,
        lastName = this.legalName,
        type = BillingAccountablePartyType.LEGAL_PERSON,
        nationalId = this.cnpj,
        email = this.email,
        address = Address(
            state = State.valueOf(this.addressState),
            city = this.addressCity,
            street = this.addressStreet,
            number = this.addressNumber.toString(),
            complement = this.addressComplement,
            postalCode = this.addressPostalCode,
            neighbourhood = this.addressNeighborhood,
        ),
    )

    fun CreateCompanyRequest.toCompany() = Company(
        parentId = this.parentId,
        externalCode = this.externalCode,
        name = this.name,
        legalName = this.legalName,
        cnpj = this.cnpj,
        email = this.email,
        phoneNumber = this.phoneNumber,
        totalEmployees = this.totalEmployees ?: this.totalEmployees,
        address = CompanyAddress(
            postalCode = this.addressPostalCode,
            street = this.addressStreet,
            number = this.addressNumber,
            complement = this.addressComplement,
            neighborhood = this.addressNeighborhood,
            city = this.addressCity,
            State = this.addressState,
        ),
        billingAccountablePartyId = this.billingAccountablePartyId,
        defaultProductId = this.defaultProductId,
        availableProducts = this.availableProducts,
        companySize = this.companySize,
        companyBusinessUnit = this.companyBusinessUnit,
    )

    fun Company.updateFieldsFromRequest(request: UpdateCompanyRequest) = this.copy(
        parentId = request.parentId ?: this.parentId,
        externalCode = request.externalCode ?: this.externalCode,
        name = request.name ?: this.name,
        legalName = request.legalName ?: this.legalName,
        cnpj = request.cnpj ?: this.cnpj,
        email = request.email ?: this.email,
        phoneNumber = request.phoneNumber ?: this.phoneNumber,
        contractIds = request.contractId?.let { this.contractIds.plus(it) } ?: this.contractIds,
        companySize = request.companySize ?: this.companySize,
        totalEmployees = request.totalEmployees ?: this.totalEmployees,
        address = CompanyAddress(
            postalCode = request.addressPostalCode ?: this.address.postalCode,
            street = request.addressStreet ?: this.address.street,
            number = request.addressNumber ?: this.address.number,
            complement = request.addressComplement ?: this.address.complement,
            neighborhood = request.addressNeighborhood ?: this.address.neighborhood,
            city = request.addressCity ?: this.address.city,
            State = request.addressState ?: this.address.State,
        ),
        billingAccountablePartyId = request.billingAccountablePartyId,
        defaultProductId = request.defaultProductId,
        availableProducts = request.availableProducts,
        companyBusinessUnit = request.companyBusinessUnit,
    )

    fun List<OngoingCompanyDeal>.toCompanyDealResponse(salesFirms: List<SalesFirm>) =
        this.map { deal ->
            OngoingCompanyDealConverter.convert(
                source = deal,
                salesFirm = salesFirms.first { it.id == deal.salesFirmId }
            )
        }
}
