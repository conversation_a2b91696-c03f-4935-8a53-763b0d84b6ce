package br.com.alice.appointment.consumers

import br.com.alice.appointment.client.AppointmentEventService
import br.com.alice.appointment.controller.AppointmentEventReferralBackfillEvent
import br.com.alice.appointment.event.AppointmentCompletedEvent
import br.com.alice.channel.notifier.ChannelUpsertedEvent
import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.AppointmentEvent
import br.com.alice.data.layer.models.AppointmentEventReferenceModel
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.data.layer.models.PersonHealthEventCategory
import br.com.alice.data.layer.models.PersonHealthEventStatus
import br.com.alice.data.layer.models.Referral
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthplan.events.HealthPlanTaskUpsertedEvent
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.schedule.model.events.AppointmentScheduleCancelledEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCompletedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import br.com.alice.wanda.event.PersonHealthEventCreatedEvent
import br.com.alice.wanda.event.PersonHealthEventUpdatedEvent
import br.com.alice.wanda.event.TestResultFeedbackCreatedEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDateTime

class AppointmentEventConsumer(
    private val appointmentEventService: AppointmentEventService,
    private val medicalSpecialtyService: MedicalSpecialtyService
) : Consumer() {

    private val wandaTaskCategoriesAllowed = listOf(
        PersonHealthEventCategory.SUMMARY_HOSPITALIZATION,
        PersonHealthEventCategory.AA_HEALTH_FOLLOW_UP,
        PersonHealthEventCategory.AA_IMMERSION_HEALTH_FOLLOW_UP,
        PersonHealthEventCategory.AA_INDIRECT_HEALTH_FOLLOW_UP,
        PersonHealthEventCategory.HEALTH_PLAN_FOLLOW_UP,
        PersonHealthEventCategory.EXTENDED_HUDDLE
    )

    private val healthPlanTaskTypesAllowed = listOf(
        HealthPlanTaskType.REFERRAL,
        HealthPlanTaskType.FOLLOW_UP_REQUEST
    )

    suspend fun createAppointmentEventByChannelUpserted(event: ChannelUpsertedEvent) =
        withSubscribersEnvironment {
            val channelUpserted = event.payload

            if (channelUpserted.status != ChannelStatus.ACTIVE) {
                return@withSubscribersEnvironment deleteByReferenceId(
                    channelUpserted.channelId,
                    AppointmentEventReferenceModel.CHANNEL
                )
            }

            val clinicalRecordName = when {
                channelUpserted.subCategory != null -> channelUpserted.subCategory!!.description
                channelUpserted.category != null -> channelUpserted.category!!.description
                else -> ""
            }.lowercase()

            appointmentEventService.upsert(
                AppointmentEvent(
                    referenceModel = AppointmentEventReferenceModel.CHANNEL,
                    referenceModelId = channelUpserted.channelId,
                    referenceModelDate = LocalDateTime.now(),
                    name = channelUpserted.name ?: "Chat em andamento",
                    personId = channelUpserted.personId,
                    clinicalRecordName = "Atendimento $clinicalRecordName".trim()
                )
            )
        }

    suspend fun createAppointmentEventByPersonHealthEventCreated(event: PersonHealthEventCreatedEvent) =
        withSubscribersEnvironment {
            processWandaTask(event.payload.personHealthEvent)
        }

    suspend fun createAppointmentEventByAppointmentScheduleCreated(event: AppointmentScheduleCreatedEvent) =
        withSubscribersEnvironment {
            event.payload.appointmentSchedule.let { appointmentSchedule ->
                createEvent(
                    AppointmentEvent(
                        referenceModel = AppointmentEventReferenceModel.SCHEDULING,
                        referenceModelId = appointmentSchedule.id.toString(),
                        referenceModelDate = appointmentSchedule.startTime,
                        name = appointmentSchedule.eventName,
                        personId = appointmentSchedule.personId,
                        clinicalRecordName = appointmentSchedule.generateName()
                    )
                )
            }
        }

    suspend fun createAppointmentEventByTestResultFeedbackCreated(event: TestResultFeedbackCreatedEvent) =
        withSubscribersEnvironment {
            event.payload.testResultFeedback.let { feedback ->
                createEvent(
                    AppointmentEvent(
                        referenceModel = AppointmentEventReferenceModel.TEST_RESULT,
                        referenceModelId = feedback.id.toString(),
                        referenceModelDate = feedback.addedAt,
                        name = "Feedback de resultado de exame",
                        personId = feedback.personId,
                        clinicalRecordName = "Feedback de resultado de exame"
                    )
                )
            }
        }

    suspend fun createOrDeleteAppointmentEventByHealthPlanTaskCreated(event: HealthPlanTaskUpsertedEvent) =
        withSubscribersEnvironment {
            processTasks(event.payload.task)
        }

    suspend fun createOrDeleteAppointmentEventByHealthPlanTaskCreatedBackfill(event: AppointmentEventReferralBackfillEvent) =
        withSubscribersEnvironment {
            processTasks(event.payload.task)
        }

    suspend fun deleteAppointmentEventByAppointmentScheduleCancelled(event: AppointmentScheduleCancelledEvent) =
        withSubscribersEnvironment {
            deleteByReferenceId(event.payload.id.toString(), AppointmentEventReferenceModel.SCHEDULING)
        }

    suspend fun deleteAppointmentEventByAppointmentScheduleCompleted(event: AppointmentScheduleCompletedEvent) =
        withSubscribersEnvironment {
            deleteByReferenceId(
                event.payload.appointmentSchedule.id.toString(),
                AppointmentEventReferenceModel.SCHEDULING
            )
        }

    suspend fun updateAppointmentEventByPersonHealthEventUpdated(event: PersonHealthEventUpdatedEvent) =
        withSubscribersEnvironment {
            processWandaTask(event.payload.personHealthEvent)
        }

    suspend fun deleteEventByAppointmentCompleted(event: AppointmentCompletedEvent) = withSubscribersEnvironment {
        if (!canDeleteEventByAppointment()) return@withSubscribersEnvironment false.success()

        event.payload.appointment.event?.let { appointmentEvent ->
            if (appointmentEvent.referenceModel == AppointmentEventReferenceModel.CHANNEL || appointmentEvent.referenceModel == AppointmentEventReferenceModel.HEALTH_PLAN_TASK) false.success()
            else deleteByReferenceId(appointmentEvent.referenceModelId, appointmentEvent.referenceModel)
        } ?: false.success()
    }

    private suspend fun deleteByReferenceId(referenceId: String, referenceModel: AppointmentEventReferenceModel) =
        appointmentEventService.deleteByReferenceId(referenceId)
            .coFoldNotFound {
                logger.info(
                    "AppointmentEventConsumer::deleteByReferenceId no AppointmentEvent to delete",
                    "reference_model" to referenceModel,
                    "reference_model_id" to referenceId
                )

                false.success()
            }

    private suspend fun createEvent(appointmentEvent: AppointmentEvent) =
        appointmentEventService.create(appointmentEvent)
            .coFoldDuplicated {
                logger.info(
                    "AppointmentEventConsumer::createEvent will skip due to an existing AppointmentEvent",
                    "reference_model" to appointmentEvent.referenceModel,
                    "reference_model_id" to appointmentEvent.referenceModelId
                )

                false.success()
            }

    private suspend fun processWandaTask(personHealthEvent: PersonHealthEvent) =
        if (PersonHealthEventStatus.inProgress.contains(personHealthEvent.status)) {
            if (wandaTaskCategoriesAllowed.contains(personHealthEvent.category) && canProcessWandaTask())
                appointmentEventService.upsert(
                    AppointmentEvent(
                        referenceModel = AppointmentEventReferenceModel.WANDA_TASK,
                        referenceModelId = personHealthEvent.id.toString(),
                        referenceModelDate = personHealthEvent.dueDate ?: personHealthEvent.eventDate,
                        name = personHealthEvent.title,
                        personId = personHealthEvent.personId,
                        clinicalRecordName = "Wanda | ${personHealthEvent.category.description}"
                    )
                )
            else false.success()
        } else deleteByReferenceId(personHealthEvent.id.toString(), AppointmentEventReferenceModel.WANDA_TASK)


    private suspend fun processTasks(task: HealthPlanTask): Result<Any, Throwable> {
        if (task.type !in healthPlanTaskTypesAllowed)
            return false.success()

        return if (task.isActive() && canProcessHealthPlanTasks()) {
            val name = when {
                task.isReferral() -> {
                    val referral = task.specialize<Referral>()
                    referral.specialty?.let {
                        medicalSpecialtyService.getById(it.id!!)
                            .map { specialty -> specialty.getAppointmentName() }
                            .get()
                    }
                }
                else -> task.title

            } ?: "Consulta"

            createEvent(
                AppointmentEvent(
                    referenceModel = AppointmentEventReferenceModel.HEALTH_PLAN_TASK,
                    referenceModelId = task.id.toString(),
                    referenceModelDate = task.createdAt,
                    name = name,
                    personId = task.personId,
                    clinicalRecordName = name
                )
            )
        } else deleteByReferenceId(
            task.id.toString(),
            AppointmentEventReferenceModel.HEALTH_PLAN_TASK
        )
    }

    private fun canProcessWandaTask() = FeatureService.get(
        namespace = FeatureNamespace.APPOINTMENT,
        key = "can_import_wanda_to_appointment_event",
        defaultValue = false
    )

    private fun canDeleteEventByAppointment() = FeatureService.get(
        namespace = FeatureNamespace.APPOINTMENT,
        key = "can_delete_event_by_appointment",
        defaultValue = false
    )

    private fun canProcessHealthPlanTasks() = FeatureService.get(
        namespace = FeatureNamespace.APPOINTMENT,
        key = "can_import_health_plan_tasks_to_appointment_event",
        defaultValue = false
    )
}

fun AppointmentSchedule.generateName() =
    when (type) {
        AppointmentScheduleType.COMMUNITY -> "Consulta"
        AppointmentScheduleType.CONNECTION -> "Consulta"
        AppointmentScheduleType.FOLLOW_UP -> "Retorno ${this.locationName()}"
        AppointmentScheduleType.FOLLOW_UP_NUTRITIONIST -> "Retorno | Nutrição"
        AppointmentScheduleType.FOLLOW_UP_PHYSICAL_EDUCATOR -> "Retorno | Preparação física"
        AppointmentScheduleType.HEALTH_DECLARATION -> "Declaração de saúde"
        AppointmentScheduleType.HEALTHCARE_TEAM -> "Retorno | Méd. ${this.locationName()}"
        AppointmentScheduleType.IMMERSION -> "Imersão"
        AppointmentScheduleType.NURSE -> "Consulta | Enf."
        AppointmentScheduleType.NURSE_ONSITE -> "Consulta | Enf. OnSite"
        AppointmentScheduleType.NUTRITIONIST -> "Consulta | Nutrição"
        AppointmentScheduleType.OTHER -> "Consulta"
        AppointmentScheduleType.PHYSICAL_EDUCATOR -> "Consulta | Preparação física"
        AppointmentScheduleType.PHYSICIAN_ONSITE -> "Avaliação Presencial Aguda"
        AppointmentScheduleType.PROC_NURSE -> "Procedimento | Enf. OnSite"
        AppointmentScheduleType.PROC_ONSITE -> "Procedimento | Méd. OnSite"
        AppointmentScheduleType.PSYCHOLOGIST -> "Consulta | Psicologia"
        AppointmentScheduleType.TEST -> "Coleta"
        else -> "Consulta"
    }

private fun AppointmentSchedule.locationName() =
    if (this.providerUnitId == null) "(Digital)" else "(Presencial)"
