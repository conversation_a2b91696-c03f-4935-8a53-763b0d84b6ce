package br.com.alice.business.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.events.CompanyContractCreatedEvent
import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ContractFile
import br.com.alice.data.layer.models.ContractType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.moneyin.client.CreateInvoiceResponse
import br.com.alice.moneyin.client.PreActivationCompanyInvoiceService
import br.com.alice.moneyin.client.PreActivationCompanyInvoiceType
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.failure
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import kotlin.test.BeforeTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CompanyContractConsumerTest : ConsumerTest() {
    private val subcontractService: CompanySubContractService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val preActivationCompanyInvoiceService: PreActivationCompanyInvoiceService = mockk()
    private val memberService: MemberService = mockk()
    private val companyService: CompanyService = mockk()

    private val consumer =
        CompanyContractConsumer(
            subcontractService,
            beneficiaryService,
            preActivationCompanyInvoiceService,
            memberService,
            companyService
        )

    private val startedAt = LocalDate.now().plusDays(3)

    private val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

    private val fileContractId = RangeUUID.generate()

    private val contract = TestModelFactory.buildCompanyContract(
        externalId = null,
        groupCompany = "0001",
        startedAt = startedAt,
        contractFileIds = listOf(
            ContractFile(
                id = fileContractId,
                type = ContractType.MAIN,
            )
        )
    )

    private val company = TestModelFactory.buildCompany(contractIds = listOf(contract.id))

    private val subcontract = TestModelFactory.buildCompanySubContract(
        contractId = contract.id,
        companyId = company.id,
        externalId = null,
        billingAccountablePartyId = billingAccountableParty.id
    )

    private val member = TestModelFactory.buildMember()
    private val beneficiary =
        TestModelFactory.buildBeneficiary(
            memberId = member.id,
            companyId = company.id,
            companySubContractId = subcontract.id,
            memberStatus = MemberStatus.PENDING
        )

    private val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()

    companion object {
        @JvmStatic
        fun activationDates() = listOf(
            arrayOf(null, LocalDate.now().plusDays(1)),
            arrayOf(LocalDate.now().plusDays(3), LocalDate.now().plusDays(2)),
            arrayOf(LocalDate.now(), LocalDate.now().plusDays(1)),
            arrayOf(LocalDate.now().minusDays(1), LocalDate.now().plusDays(1)),
            arrayOf(LocalDate.now().plusDays(1), LocalDate.now().plusDays(1)),
        )
    }

    @BeforeTest
    fun setup() {
        coEvery { companyService.get(company.id) } returns company
    }

    @ParameterizedTest(name = "when started at is {0} the due date will be {1}")
    @MethodSource("br.com.alice.business.consumers.CompanyContractConsumerTest#activationDates")
    fun `#should create the first invoice group payment`(startedAt: LocalDate?, dueDate: LocalDate) =
        runBlocking<Unit> {
            withFeatureFlag(FeatureNamespace.BUSINESS, "should_create_first_invoice_group_payment", listOf("0001")) {

                val canceledBeneficiary =
                    TestModelFactory.buildBeneficiary(
                        memberStatus = MemberStatus.CANCELED,
                        companyId = company.id,
                        companySubContractId = subcontract.id,
                    )

                val contract = contract.copy(startedAt = startedAt)

                val beneficiary = beneficiary.copy(companySubContractId = null)
                val beneficiaryUpdated = beneficiary.copy(companySubContractId = subcontract.id)

                val referenceDate = (startedAt ?: LocalDate.now()).atBeginningOfTheMonth()

                coEvery { beneficiaryService.findByCompanyId(company.id) } returns listOf(
                    beneficiary,
                    canceledBeneficiary
                )
                coEvery { subcontractService.findByContractId(contract.id) } returns listOf(subcontract)

                coEvery { beneficiaryService.updateInBatch(listOf(beneficiaryUpdated)) } returns listOf(
                    beneficiaryUpdated
                )

                coEvery {
                    beneficiaryService
                        .findByCompanySubContractId(subcontract.id)
                } returns listOf(beneficiaryUpdated, canceledBeneficiary)

                coEvery {
                    memberService.findByIdsAndStatus(
                        listOf(member.id, canceledBeneficiary.memberId),
                        listOf(MemberStatus.ACTIVE, MemberStatus.PENDING),
                    )
                } returns listOf(member)

                coEvery {
                    preActivationCompanyInvoiceService.get(
                        subcontract.id,
                        contract.groupCompany,
                    )
                } returns NotFoundException("")

                coEvery {
                    preActivationCompanyInvoiceService.create(
                        PreActivationCompanyInvoiceService.CreatePayload(
                            companyId = subcontract.companyId,
                            companySubContractId = subcontract.id,
                            billingAccountablePartyId = subcontract.billingAccountablePartyId!!,
                            members = listOf(member),
                            paymentMethod = PaymentMethod.BOLEPIX,
                            dueDate = dueDate,
                            referenceDate = referenceDate,
                            origin = InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS,
                            groupCompany = contract.groupCompany,
                        )
                    )
                } returns CreateInvoiceResponse(
                    memberInvoiceGroup.id,
                    PreActivationCompanyInvoiceType.MEMBER_INVOICE_GROUP
                )

                val event = CompanyContractCreatedEvent(contract)

                val result = consumer.handleCreateCompanyFirstPayment(event)

                assertThat(result).isSuccess()

                coVerifyOnce {
                    beneficiaryService.findByCompanyId(any())
                    beneficiaryService.updateInBatch(any())
                    subcontractService.findByContractId(any())
                    beneficiaryService
                        .findByCompanySubContractId(any())
                    memberService.findByIdsAndStatus(any(), any())
                    preActivationCompanyInvoiceService.create(any())
                    preActivationCompanyInvoiceService.get(any(), any())
                }
            }
        }

    @Test
    fun `#should create the first invoice group payment fail`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.BUSINESS, "should_create_first_invoice_group_payment", listOf("0001")) {

                val canceledBeneficiary =
                    TestModelFactory.buildBeneficiary(
                        memberStatus = MemberStatus.CANCELED,
                        companyId = company.id,
                        companySubContractId = subcontract.id,
                    )

                val contract = contract.copy(startedAt = startedAt)

                val beneficiary = beneficiary.copy(companySubContractId = null)
                val beneficiaryUpdated = beneficiary.copy(companySubContractId = subcontract.id)

                val referenceDate = (startedAt ?: LocalDate.now()).atBeginningOfTheMonth()

                coEvery { beneficiaryService.findByCompanyId(company.id) } returns listOf(
                    beneficiary,
                    canceledBeneficiary
                )
                coEvery { subcontractService.findByContractId(contract.id) } returns listOf(subcontract)

                coEvery { beneficiaryService.updateInBatch(listOf(beneficiaryUpdated)) } returns listOf(
                    beneficiaryUpdated
                )

                coEvery {
                    beneficiaryService
                        .findByCompanySubContractId(subcontract.id)
                } returns listOf(beneficiaryUpdated, canceledBeneficiary)

                coEvery {
                    memberService.findByIdsAndStatus(
                        listOf(member.id, canceledBeneficiary.memberId),
                        listOf(MemberStatus.ACTIVE, MemberStatus.PENDING),
                    )
                } returns listOf(member)

                coEvery {
                    preActivationCompanyInvoiceService.get(
                        subcontract.id,
                        contract.groupCompany,
                    )
                } returns NotFoundException("")

                coEvery {
                    preActivationCompanyInvoiceService.create(
                        PreActivationCompanyInvoiceService.CreatePayload(
                            companyId = subcontract.companyId,
                            companySubContractId = subcontract.id,
                            billingAccountablePartyId = subcontract.billingAccountablePartyId!!,
                            members = listOf(member),
                            paymentMethod = PaymentMethod.BOLEPIX,
                            dueDate = startedAt.minusDays(1),
                            referenceDate = referenceDate,
                            origin = InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS,
                            groupCompany = contract.groupCompany,
                        )
                    )
                } returns InvalidArgumentException().failure()

                val event = CompanyContractCreatedEvent(contract)

                val result = consumer.handleCreateCompanyFirstPayment(event)

                assertThat(result).isFailureOfType(InvalidArgumentException::class)

                coVerifyOnce {
                    beneficiaryService.findByCompanyId(any())
                    beneficiaryService.updateInBatch(any())
                    subcontractService.findByContractId(any())
                    beneficiaryService
                        .findByCompanySubContractId(any())
                    memberService.findByIdsAndStatus(any(), any())
                    preActivationCompanyInvoiceService.create(any())
                    preActivationCompanyInvoiceService.get(any(), any())
                }
            }
        }

    @Test
    fun `#should not create the first invoice group payment when does not exists a valid beneficiary`() =
        runBlocking<Unit> {
            withFeatureFlag(FeatureNamespace.BUSINESS, "should_create_first_invoice_group_payment", listOf("0001")) {

                val canceledBeneficiary = TestModelFactory.buildBeneficiary(
                    memberStatus = MemberStatus.CANCELED,
                    companySubContractId = subcontract.id,
                )

                coEvery { beneficiaryService.findByCompanyId(company.id) } returns listOf(canceledBeneficiary)

                coEvery { subcontractService.findByContractId(contract.id) } returns listOf(subcontract)

                coEvery {
                    beneficiaryService
                        .findByCompanySubContractId(subcontract.id)
                } returns listOf(canceledBeneficiary)

                coEvery {
                    preActivationCompanyInvoiceService.get(
                        subcontract.id,
                        contract.groupCompany
                    )
                } returns NotFoundException("")
                coEvery {
                    memberService.findByIdsAndStatus(
                        listOf(canceledBeneficiary.memberId),
                        listOf(MemberStatus.ACTIVE, MemberStatus.PENDING),
                    )
                } returns emptyList()

                val event = CompanyContractCreatedEvent(contract)

                val result = consumer.handleCreateCompanyFirstPayment(event)

                assertThat(result).isSuccess()

                coVerifyOnce {
                    beneficiaryService.findByCompanyId(company.id)
                    subcontractService.findByContractId(contract.id)
                    beneficiaryService
                        .findByCompanySubContractId(subcontract.id)
                    preActivationCompanyInvoiceService.get(subcontract.id, contract.groupCompany)
                    memberService.findByIdsAndStatus(
                        listOf(canceledBeneficiary.memberId),
                        listOf(MemberStatus.ACTIVE, MemberStatus.PENDING),
                    )
                }

                coVerifyNone {
                    beneficiaryService.updateInBatch(any())
                    preActivationCompanyInvoiceService.create(any())
                }
            }
        }

    @Test
    fun `#should not create the first invoice group payment when the company should be skipped because the cnpj`() =
        runBlocking<Unit> {
            withFeatureFlags(
                FeatureNamespace.BUSINESS,
                mapOf(
                    "should_create_first_invoice_group_payment" to listOf("0001"),
                    "skip_creation_first_invoice_group_payment" to listOf(
                        "${company.cnpj},1212121212"
                    )
                )
            ) {
                coEvery { beneficiaryService.findByCompanyId(company.id) } returns listOf(beneficiary)
                coEvery { subcontractService.findByContractId(contract.id) } returns listOf(subcontract)

                coEvery {
                    preActivationCompanyInvoiceService.get(
                        subcontract.id,
                        contract.groupCompany,
                    )
                } returns NotFoundException("")

                val event = CompanyContractCreatedEvent(contract)

                val result = consumer.handleCreateCompanyFirstPayment(event)

                assertThat(result).isSuccess()

                coVerifyOnce {
                    beneficiaryService.findByCompanyId(company.id)
                    subcontractService.findByContractId(contract.id)

                    preActivationCompanyInvoiceService.get(subcontract.id, contract.groupCompany)
                }

                coVerifyNone {
                    beneficiaryService.updateInBatch(any())
                    memberService.findByIdsAndStatus(any(), any())
                    beneficiaryService
                        .findByCompanySubContractId(any())
                    preActivationCompanyInvoiceService.create(any())
                }
            }
        }

    @Test
    fun `#should not create the first invoice group payment when the group company does not match with FF`() =
        runBlocking<Unit> {
            withFeatureFlag(FeatureNamespace.BUSINESS, "should_create_first_invoice_group_payment", listOf("0002")) {
                val event = CompanyContractCreatedEvent(contract)

                val result = consumer.handleCreateCompanyFirstPayment(event)

                assertThat(result).isSuccess()

                coVerifyNone {
                    beneficiaryService.findByCompanyId(any())
                    beneficiaryService.updateInBatch(any())
                    subcontractService.findByContractId(contract.id)
                    beneficiaryService
                        .findByCompanySubContractId(subcontract.id)
                    memberService.findByIdsAndStatus(any(), any())
                    preActivationCompanyInvoiceService.create(any())
                    preActivationCompanyInvoiceService.get(any(), null)
                }
            }
        }

    @Test
    fun `#should not create the first invoice group payment when the contract does not have a contract file`() =
        runBlocking<Unit> {
            withFeatureFlag(FeatureNamespace.BUSINESS, "should_create_first_invoice_group_payment", listOf("0001")) {
                val contract = contract.copy(contractFileIds = emptyList())
                val event = CompanyContractCreatedEvent(contract)

                val result = consumer.handleCreateCompanyFirstPayment(event)

                assertThat(result).isSuccess()

                coVerifyNone {
                    beneficiaryService.findByCompanyId(any())
                    beneficiaryService.updateInBatch(any())
                    subcontractService.findByContractId(contract.id)
                    beneficiaryService
                        .findByCompanySubContractId(subcontract.id)
                    memberService.findByIdsAndStatus(any(), any())
                    preActivationCompanyInvoiceService.create(any())

                    preActivationCompanyInvoiceService.get(any(), null)
                }
            }
        }

    @Test
    fun `#should not create the first invoice group payment when FF is not enabled`() =
        runBlocking<Unit> {
            val event = CompanyContractCreatedEvent(contract)

            val result = consumer.handleCreateCompanyFirstPayment(event)

            assertThat(result).isSuccess()

            coVerifyNone {
                beneficiaryService.findByCompanyId(any())
                beneficiaryService.updateInBatch(any())
                subcontractService.findByContractId(contract.id)
                beneficiaryService
                    .findByCompanySubContractId(subcontract.id)
                memberService.findByIdsAndStatus(any(), any())
                preActivationCompanyInvoiceService.create(any())
                preActivationCompanyInvoiceService.get(any(), null)
            }
        }
}
