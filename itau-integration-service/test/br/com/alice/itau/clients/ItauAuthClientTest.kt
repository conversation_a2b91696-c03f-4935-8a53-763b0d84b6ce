package br.com.alice.itau.clients

import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.readFile
import br.com.alice.common.redis.GenericCache
import br.com.alice.common.serialization.gson
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.itau.ITAU_ACCESS_TOKEN_CACHE_KEY
import br.com.alice.itau.clients.models.ItauAuthErrorResponse
import br.com.alice.itau.clients.models.ItauAuthException
import br.com.alice.itau.clients.models.ItauAuthResponse
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals


class ItauAuthClientTest {
    private val cache: GenericCache = mockk()

    private fun mockClient(response: String, statusCode: HttpStatusCode = HttpStatusCode.OK) =
        HttpClient(MockEngine) {
            install(ContentNegotiation) {
                gsonSnakeCase()
            }
            expectSuccess = true
            engine {
                addHandler {
                    respond(
                        content = response,
                        status = statusCode,
                        headers = headersOf("Content-Type", "application/json")
                    )
                }
            }
        }

    @Test
    fun `#getAccessToken should return success without cache`(): Unit = runBlocking {
        val successResponseJSON =
            readFile("testResources/itau-auth-success-response.json")
        val expectedResponse = gson.fromJson<ItauAuthResponse>(successResponseJSON)

        val httpClientMock = mockClient(successResponseJSON)

        val clientMock = ItauAuthClient(cache, httpClientMock)
        coEvery {
            cache.get(
                ITAU_ACCESS_TOKEN_CACHE_KEY,
                String::class,
                any(),
                any(),
                any()
            )
        } coAnswers {
            arg<suspend () -> String>(4).invoke()
        }

        val result = clientMock.getAccessToken()

        assertThat(result).isSuccessWithData(expectedResponse.accessToken)
    }

    @Test
    fun `#getAccessToken should return success with cache`(): Unit = runBlocking {
        val httpClientMock = mockClient("{}")

        val clientMock = ItauAuthClient(cache, httpClientMock)
        coEvery {
            cache.get(
                ITAU_ACCESS_TOKEN_CACHE_KEY,
                String::class,
                any(),
                any(),
                any()
            )
        } returns "123"

        val result = clientMock.getAccessToken()

        assertThat(result).isSuccessWithData("123")
    }


    @Test
    fun `#getAccessToken should return failure`(): Unit = runBlocking {
        val failureResponse =
            readFile("testResources/itau-auth-failure-response.json")

        val errorResponse = gson.fromJson<ItauAuthErrorResponse>(failureResponse)
        val expectedFailure = ItauAuthException(message = errorResponse.message, statusCode = HttpStatusCode.BadRequest)

        val httpClientMock = mockClient(failureResponse, HttpStatusCode.BadRequest)

        val clientMock = ItauAuthClient(cache, httpClientMock)
        coEvery {
            cache.get(
                ITAU_ACCESS_TOKEN_CACHE_KEY,
                String::class,
                any(),
                any(),
                any()
            )
        } coAnswers {
            arg<suspend () -> String>(4).invoke()
        }

        val result = clientMock.getAccessToken()

        assertThat(result).isFailureOfType(ItauAuthException::class)
        val exception = result.failure() as ItauAuthException
        assertEquals(expectedFailure.message, exception.message)
        assertEquals(HttpStatusCode.BadRequest, exception.statusCode)
    }

}
