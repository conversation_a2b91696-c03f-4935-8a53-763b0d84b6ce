package br.com.alice.moneyin.services

import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.extensions.money
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.observability.MetricAssert.Companion.assertThatCounter
import br.com.alice.common.observability.metrics.MetricsTest
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.data.layer.models.BolepixPaymentDetailModel
import br.com.alice.data.layer.models.CancelPaymentOnAcquirerSchedule
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.InvoicePaymentSource
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.MemberInvoiceGroupModel
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.services.InvoicePaymentModelDataService
import br.com.alice.data.layer.services.MemberInvoiceGroupModelDataService
import br.com.alice.data.layer.services.MemberInvoiceModelDataService
import br.com.alice.moneyin.builder.InvoicePaymentBuilder
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.CancelPaymentOnAcquirerScheduleService
import br.com.alice.moneyin.client.InvalidAmountException
import br.com.alice.moneyin.client.InvoicePaymentAlreadyApprovedException
import br.com.alice.moneyin.client.InvoicePaymentAmountNotAllowed
import br.com.alice.moneyin.client.InvoicePaymentCanceledException
import br.com.alice.moneyin.client.InvoicePaymentDetailsNotFoundException
import br.com.alice.moneyin.client.InvoicePaymentInvalidAmountPaid
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.MemberInvoiceNotFoundException
import br.com.alice.moneyin.client.MemberInvoiceNotOpenedException
import br.com.alice.moneyin.client.PendingInvoicePaymentException
import br.com.alice.moneyin.converters.toModel
import br.com.alice.moneyin.event.CreateExternalPaymentRequestEvent
import br.com.alice.moneyin.event.ExternalInvoiceCreatedEvent
import br.com.alice.moneyin.event.InvoicePaymentApprovedEvent
import br.com.alice.moneyin.event.InvoicePaymentCanceledEvent
import br.com.alice.moneyin.event.InvoicePaymentCreatedEvent
import br.com.alice.moneyin.event.InvoicePaymentCreatedEventPayload
import br.com.alice.moneyin.event.PaymentDetailCreatedEvent
import br.com.alice.moneyin.model.InvalidPaymentMethodAndReasonException
import br.com.alice.moneyin.model.PaymentValidationException
import br.com.alice.moneyin.models.AcquirerCreatePaymentResponse
import br.com.alice.moneyin.models.BankSlipInfo
import br.com.alice.moneyin.models.PaymentStatus
import br.com.alice.moneyin.models.PixInfo
import br.com.alice.moneyin.services.internal.AcquirerOrchestratorService
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.spyk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class InvoicePaymentServiceImplTest : MetricsTest() {

    private val invoicePaymentDataService: InvoicePaymentModelDataService = mockk()
    private val paymentDetailService: PaymentDetailService = mockk()
    private val paymentService: PaymentService = mockk()
    private val memberInvoiceDataService: MemberInvoiceModelDataService = mockk()
    private val memberInvoiceGroupDataService: MemberInvoiceGroupModelDataService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val memberService: MemberService = mockk()
    private val acquirerOrchestratorService: AcquirerOrchestratorService = mockk()
    private val cancelPaymentOnAcquirerScheduleService: CancelPaymentOnAcquirerScheduleService = mockk()
    private val moneyInResourceSignTokenServiceImpl: MoneyInResourceSignTokenServiceImpl = mockk()

    private val service = InvoicePaymentServiceImpl(
        invoicePaymentDataService,
        memberInvoiceGroupDataService,
        kafkaProducerService,
        paymentDetailService,
        paymentService,
        memberInvoiceDataService,
        billingAccountablePartyService,
        acquirerOrchestratorService,
        memberService,
        cancelPaymentOnAcquirerScheduleService,
        moneyInResourceSignTokenServiceImpl
    )

    private val serviceSpy = spyk(service)

    private val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
    private val memberInvoiceGroupModel = memberInvoiceGroup.toModel()

    @AfterTest
    fun tearDown() = clearAllMocks()

    @Nested
    inner class Approve {
        @Test
        fun `#approve should return success when payment is already paid`() = runBlocking {
            val invoicePayment = TestModelFactory.buildInvoicePayment()
            val approvedPayment = invoicePayment.approve()
            val approvedPaymentModel = approvedPayment.toModel()

            coEvery { invoicePaymentDataService.get(approvedPayment.id) } returns approvedPaymentModel


            val result = service.approve(approvedPayment.id)
            assertThat(result).isSuccessWithData(approvedPayment)
        }

        @Test
        fun `#approve should return an exception when the amount paid is less then the invoice payment total amount`() =
            runBlocking {
                val invoicePayment = TestModelFactory.buildInvoicePayment(amount = BigDecimal("2.0"))
                val invoicePaymentModel = invoicePayment.toModel()

                coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel

                val result = service.approve(invoicePayment.id, amountPaid = BigDecimal("1.0"))
                assertThat(result).isFailureOfType(InvoicePaymentInvalidAmountPaid::class)
            }

        @Test
        fun `#approve should return success when payment is canceled`() = runBlocking<Unit> {
            val invoicePayment = TestModelFactory.buildInvoicePayment()
            val canceledPayment = invoicePayment.cancel(cancellationReason = CancellationReason.INVALID)
            val canceledPaymentModel = canceledPayment.toModel()
            val expectedPayment = invoicePayment.approve()
            val expectedPaymentModel = expectedPayment.toModel()

            coEvery { invoicePaymentDataService.get(canceledPayment.id) } returns canceledPaymentModel
            coEvery { invoicePaymentDataService.update(match { it.id == invoicePayment.id && it.isApproved }) } returns expectedPaymentModel
            coEvery {
                kafkaProducerService.produce(match {
                    it.name == InvoicePaymentApprovedEvent.name
                })
            } returns ProducerResult(LocalDateTime.now(), InvoicePaymentApprovedEvent.name, 0)

            val result = service.approve(canceledPayment.id)
            assertThat(result).isSuccessWithData(expectedPayment)

            coVerifyOnce { kafkaProducerService.produce(any()) }

            assertThatCounter("invoice_payment_status_changes_total").withLabels(
                "status" to "APPROVED",
                "method" to "BOLETO",
            ).hasCountOf(1)
        }

        @Test
        fun `#approve should return success when payment was approved successfully`() = runBlocking<Unit> {
            val invoicePayment = TestModelFactory.buildInvoicePayment()
            val invoicePaymentModel = invoicePayment.toModel()
            val expectedPayment = invoicePayment.approve()
            val expectedPaymentModel = expectedPayment.toModel()

            coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
            coEvery { invoicePaymentDataService.update(match { it.id == invoicePayment.id && it.isApproved }) } returns expectedPaymentModel
            coEvery {
                kafkaProducerService.produce(match {
                    it.name == InvoicePaymentApprovedEvent.name
                })
            } returns ProducerResult(LocalDateTime.now(), InvoicePaymentApprovedEvent.name, 0)

            val result = service.approve(invoicePayment.id)
            assertThat(result).isSuccessWithData(expectedPayment)

            coVerifyOnce { kafkaProducerService.produce(any()) }

            assertThatCounter("invoice_payment_status_changes_total").withLabels(
                "status" to "APPROVED",
                "method" to "BOLETO",
            ).hasCountOf(1)
        }

        @Test
        fun `#approve should return success when payment was approved successfully with given approved at date`() =
            runBlocking<Unit> {
                val invoicePayment = TestModelFactory.buildInvoicePayment()
                val invoicePaymentModel = invoicePayment.toModel()
                val expectedPayment = invoicePayment.approve()
                val expectedPaymentModel = expectedPayment.toModel()
                val approvedAt = LocalDateTime.now().minusDays(3)

                coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
                coEvery { invoicePaymentDataService.update(any()) } returns expectedPaymentModel
                coEvery {
                    kafkaProducerService.produce(any())
                } returns ProducerResult(LocalDateTime.now(), InvoicePaymentApprovedEvent.name, 0)


                val result = service.approve(invoicePayment.id, approvedAt = approvedAt)
                assertThat(result).isSuccessWithData(expectedPayment)

                coVerifyOnce {
                    invoicePaymentDataService.update(match {
                        it.id == invoicePayment.id && it.isApproved && it.approvedAt == approvedAt
                    })
                }
                coVerifyOnce {
                    kafkaProducerService.produce(match {
                        it.name == InvoicePaymentApprovedEvent.name
                    })
                }

                assertThatCounter("invoice_payment_status_changes_total").withLabels(
                    "status" to "APPROVED",
                    "method" to "BOLETO",
                ).hasCountOf(1)
            }

        @Test
        fun `#approve should return success when the payment was approved successfully with given approved at date`() =
            runBlocking<Unit> {
                val invoicePayment = TestModelFactory.buildInvoicePayment(method = PaymentMethod.BOLEPIX)
                val invoicePaymentModel = invoicePayment.toModel()
                val expectedPayment = invoicePayment.copy(
                    status = InvoicePaymentStatus.APPROVED,
                    approvedAt = LocalDateTime.now(),
                )
                val expectedPaymentModel = expectedPayment.toModel()
                val approvedAt = LocalDateTime.now().minusDays(3)

                coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
                coEvery {
                    invoicePaymentDataService.update(any())
                } returns expectedPaymentModel
                coEvery {
                    kafkaProducerService.produce(match {
                        it.name == InvoicePaymentApprovedEvent.name
                    })
                } returns ProducerResult(LocalDateTime.now(), InvoicePaymentApprovedEvent.name, 0)

                val result = service.approve(invoicePayment.id, approvedAt)
                assertThat(result).isSuccessWithData(expectedPayment)

                coVerifyOnce { kafkaProducerService.produce(any()) }
                coVerifyOnce { invoicePaymentDataService.get(invoicePayment.id) }
                coVerifyOnce {
                    invoicePaymentDataService.update(match {
                        it.id == invoicePayment.id && it.isApproved && it.approvedAt == approvedAt
                    })
                }

                assertThatCounter("invoice_payment_status_changes_total").withLabels(
                    "status" to "APPROVED",
                    "method" to "BOLEPIX",
                ).hasCountOf(1)
            }
    }

    @Test
    fun `#getByExternalId should call dataService with correct parameters`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val invoicePaymentModel = invoicePayment.toModel()
        val externalId = "external-id"

        coEvery { invoicePaymentDataService.findOne(queryEq { where { this.externalId.eq(externalId) } }) } returns invoicePaymentModel

        val result = service.getByExternalId(externalId)
        assertThat(result).isSuccessWithData(invoicePayment)
    }

    @Test
    fun `#getByExternalId should call dataService with correct parameters when need paymentDetails`() = runBlocking {
        val paymentDetail = TestModelFactory.buildBoletoPaymentDetail()
        val externalId = "external-id"
        val invoicePayment = TestModelFactory.buildInvoicePayment(paymentDetail = null)
        val invoicePaymentModel = invoicePayment.toModel()

        coEvery { invoicePaymentDataService.findOne(queryEq { where { this.externalId.eq(externalId) } }) } returns invoicePaymentModel
        coEvery { paymentDetailService.getPaymentDetail(invoicePayment) } returns paymentDetail


        val result = service.getByExternalId(externalId, true)
        assertThat(result).isSuccessWithData(invoicePayment.withPaymentDetail(paymentDetail))
    }

    @Test
    fun `#getByExternalIdAndSource should call dataService with correct parameters`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val source = InvoicePaymentSource.ITAU
        val invoicePaymentModel = invoicePayment.toModel()
        val externalId = "external-id"

        coEvery {
            invoicePaymentDataService.findOne(queryEq {
                where {
                    this.externalId.eq(externalId) and this.source.eq(
                        source
                    )
                }
            })
        } returns invoicePaymentModel

        val result = service.getByExternalIdAndSource(externalId, source)
        assertThat(result).isSuccessWithData(invoicePayment)
    }

    @Test
    fun `#getByExternalIdAndSource should call dataService with correct parameters when need paymentDetails`() =
        runBlocking {
            val paymentDetail = TestModelFactory.buildBoletoPaymentDetail()
            val source = InvoicePaymentSource.ITAU
            val externalId = "external-id"
            val invoicePayment = TestModelFactory.buildInvoicePayment(paymentDetail = null)
            val invoicePaymentModel = invoicePayment.toModel()

            coEvery {
                invoicePaymentDataService.findOne(queryEq {
                    where {
                        this.externalId.eq(externalId) and this.source.eq(
                            source
                        )
                    }
                })
            } returns invoicePaymentModel
            coEvery { paymentDetailService.getPaymentDetail(invoicePayment) } returns paymentDetail


            val result = service.getByExternalIdAndSource(externalId, source, true)
            assertThat(result).isSuccessWithData(invoicePayment.withPaymentDetail(paymentDetail))
        }

    @Test
    fun `#getLastByCompanyId should call dataService with correct parameters`() = runBlocking {
        val invoicePayment =
            TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup.id, paymentDetail = null)
        val invoicePaymentModel = invoicePayment.toModel()
        val paymentDetail = TestModelFactory.buildBoletoPaymentDetail()

        coEvery {
            memberInvoiceGroupDataService.find(queryEq {
                where { this.companyId.eq(memberInvoiceGroup.companyId!!) }.orderByList({
                    listOf(
                        this.referenceDate, this.createdAt
                    )
                }, { listOf(desc, desc) }).limit { 1 }
            })
        } returns listOf(memberInvoiceGroupModel)

        coEvery {
            invoicePaymentDataService.find(queryEq {
                where { this.invoiceGroupId.eq(memberInvoiceGroup.id) }.orderBy { createdAt }
                    .sortOrder { SortOrder.Descending }
            })
        } returns listOf(invoicePaymentModel)

        coEvery { paymentDetailService.getPaymentDetail(invoicePayment) } returns paymentDetail

        val result = service.getLastByCompanyId(memberInvoiceGroup.companyId!!)
        assertThat(result).isSuccessWithData(invoicePayment.withPaymentDetail(paymentDetail))
    }

    @Test
    fun `#getLastByCompanyId should call dataService with correct parameters with no paymentDetails`() = runBlocking {
        val invoicePayment =
            TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup.id, paymentDetail = null)
        val invoicePaymentModel = invoicePayment.toModel()

        coEvery {
            memberInvoiceGroupDataService.find(queryEq {
                where { this.companyId.eq(memberInvoiceGroup.companyId!!) }.orderByList({
                    listOf(
                        this.referenceDate, this.createdAt
                    )
                }, { listOf(desc, desc) }).limit { 1 }
            })
        } returns listOf(memberInvoiceGroupModel)

        coEvery {
            invoicePaymentDataService.find(queryEq {
                where { this.invoiceGroupId.eq(memberInvoiceGroup.id) }.orderBy { createdAt }
                    .sortOrder { SortOrder.Descending }
            })
        } returns listOf(invoicePaymentModel)


        val result = service.getLastByCompanyId(memberInvoiceGroup.companyId!!, false)
        assertThat(result).isSuccessWithData(invoicePayment)
    }

    @Test
    fun `#getLastByCompanyId should call dataService and could not found any register`() = runBlocking {
        coEvery {
            memberInvoiceGroupDataService.find(queryEq {
                where { this.companyId.eq(memberInvoiceGroup.companyId!!) }.orderByList({
                    listOf(
                        this.referenceDate, this.createdAt
                    )
                }, { listOf(desc, desc) }).limit { 1 }
            })
        } returns emptyList<MemberInvoiceGroupModel>()


        val result = service.getLastByCompanyId(memberInvoiceGroup.companyId!!, false)
        assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#getLastByCompanyId should call dataService and receive an exception`() = runBlocking {
        coEvery {
            memberInvoiceGroupDataService.find(queryEq {
                where { this.companyId.eq(memberInvoiceGroup.companyId!!) }.orderByList({
                    listOf(
                        this.referenceDate, this.createdAt
                    )
                }, { listOf(desc, desc) }).limit { 1 }
            })
        } returns NotFoundException("")


        val result = service.getLastByCompanyId(memberInvoiceGroup.companyId!!, false)
        assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#getByInvoiceGroupIds should call dataService with correct parameters`() = runBlocking {
        val invoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
        val invoicePayment = TestModelFactory.buildInvoicePayment(invoiceGroupId = invoiceGroup.id)
        val invoicePaymentModel = invoicePayment.toModel()

        coEvery {
            invoicePaymentDataService.find(queryEq { where { this.invoiceGroupId.inList(listOf(invoiceGroup.id)) } })
        } returns listOf(invoicePaymentModel)


        val result = service.getByInvoiceGroupIds(listOf(invoiceGroup.id))
        assertThat(result).isSuccessWithData(listOf(invoicePayment))
    }

    @Test
    fun `#getByInvoiceLiquidationIds should call dataService with correct parameters`() = runBlocking {
        val invoiceLiquidationId = RangeUUID.generate()
        val invoicePayment = TestModelFactory.buildInvoicePayment(invoiceLiquidationId = invoiceLiquidationId)
        val invoicePaymentModel = invoicePayment.toModel()

        coEvery {
            invoicePaymentDataService.find(queryEq {
                where {
                    this.invoiceLiquidationId.inList(
                        listOf(
                            invoiceLiquidationId
                        )
                    )
                }
            })
        } returns listOf(invoicePaymentModel)


        val result = service.getByInvoiceLiquidationIds(listOf(invoiceLiquidationId))
        assertThat(result).isSuccessWithData(listOf(invoicePayment))
    }

    @Test
    fun `#get should call dataService with correct parameters`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val invoicePaymentModel = invoicePayment.toModel()

        coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel


        val result = service.get(invoicePayment.id, false)
        assertThat(result).isSuccessWithData(invoicePayment)
    }

    @Test
    fun `#get should call dataService with correct parameters when need paymentDetails`() = runBlocking {
        val paymentDetail = TestModelFactory.buildBoletoPaymentDetail()
        val invoicePayment = TestModelFactory.buildInvoicePayment(paymentDetail = null)
        val invoicePaymentModel = invoicePayment.toModel()

        coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
        coEvery { paymentDetailService.getPaymentDetail(invoicePayment) } returns paymentDetail


        val result = service.get(invoicePayment.id, true)
        assertThat(result).isSuccessWithData(invoicePayment.withPaymentDetail(paymentDetail))
    }

    @Test
    fun `#decline should return success when payment is already declined`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val declinedPayment = invoicePayment.decline()
        val declinedPaymentModel = declinedPayment.toModel()

        coEvery { invoicePaymentDataService.get(declinedPayment.id) } returns declinedPaymentModel


        val result = service.decline(declinedPayment.id)
        assertThat(result).isSuccessWithData(declinedPayment)
    }

    @Test
    fun `#decline should return failure when payment is canceled`() = runBlocking<Unit> {
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val canceledPayment = invoicePayment.cancel(cancellationReason = CancellationReason.INVALID)
        val canceledPaymentModel = canceledPayment.toModel()

        coEvery { invoicePaymentDataService.get(canceledPayment.id) } returns canceledPaymentModel


        val result = service.decline(canceledPayment.id)
        assertThat(result).isFailureOfType(InvoicePaymentCanceledException::class)
    }

    @Test
    fun `#decline should return failure when payment is already paid`() = runBlocking<Unit> {
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val approvedPayment = invoicePayment.approve()
        val approvedPaymentModel = approvedPayment.toModel()

        coEvery { invoicePaymentDataService.get(approvedPayment.id) } returns approvedPaymentModel


        val result = service.decline(approvedPayment.id)
        assertThat(result).isFailureOfType(InvoicePaymentAlreadyApprovedException::class)
    }

    @Test
    fun `#decline should return success when payment was declined successfully`() = runBlocking<Unit> {
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val invoicePaymentModel = invoicePayment.toModel()
        val expectedPayment = invoicePayment.decline()
        val expectedPaymentModel = expectedPayment.toModel()


        coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
        coEvery { invoicePaymentDataService.update(match { it.id == invoicePayment.id && it.isDeclined }) } returns expectedPaymentModel


        val result = service.decline(invoicePayment.id)
        assertThat(result).isSuccessWithData(expectedPayment)

        assertThatCounter("invoice_payment_status_changes_total").withLabels(
            "status" to "DECLINED",
            "method" to "BOLETO",
        ).hasCountOf(1)
    }

    @Test
    fun `#cancel should return success when payment is already canceled`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val canceledPayment = invoicePayment.cancel(CancellationReason.PAYMENT_PROCESSOR_CANCELED)
        val canceledPaymentModel = canceledPayment.toModel()

        coEvery { invoicePaymentDataService.get(canceledPayment.id) } returns canceledPaymentModel


        val result = service.cancel(canceledPayment.id, canceledPayment.canceledReason!!)

        coVerifyNone {
            kafkaProducerService.produce(match { it.name == InvoicePaymentCanceledEvent.name })
        }
        assertThat(result).isSuccessWithData(canceledPayment)
    }


    @Test
    fun `#cancel should return failure when payment is already paid`() = runBlocking<Unit> {
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val approvedPayment = invoicePayment.approve()
        val approvedPaymentModel = approvedPayment.toModel()

        coEvery { invoicePaymentDataService.get(approvedPayment.id) } returns approvedPaymentModel


        val result = service.cancel(approvedPayment.id, CancellationReason.PAYMENT_PROCESSOR_CANCELED)
        assertThat(result).isFailureOfType(InvoicePaymentAlreadyApprovedException::class)
        coVerifyNone { kafkaProducerService.produce(match { it.name == InvoicePaymentCanceledEvent.name }) }
    }

    @Test
    fun `#cancel should return success when payment was canceled successfully`() = runBlocking<Unit> {
        val reason = CancellationReason.PAYMENT_PROCESSOR_CANCELED
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val invoicePaymentModel = invoicePayment.toModel()
        val expectedPayment = invoicePayment.cancel(reason)
        val expectedPaymentModel = expectedPayment.toModel()

        coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
        coEvery { invoicePaymentDataService.update(match { it.id == invoicePayment.id && it.isCanceled }) } returns expectedPaymentModel
        coEvery {
            kafkaProducerService.produce(any())
        } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCanceledEvent.name, 0)


        val result = service.cancel(invoicePayment.id, reason)
        assertThat(result).isSuccessWithData(expectedPayment)

        coVerifyOnce { kafkaProducerService.produce(match { it.name == InvoicePaymentCanceledEvent.name }) }

        assertThatCounter("invoice_payment_status_changes_total").withLabels(
            "status" to "CANCELED",
            "method" to "BOLETO",
        ).hasCountOf(1)
    }

    @Test
    fun `#cancel should return success when schedule cancel cancel payment for itau`() = runBlocking {
        val reason = CancellationReason.PAYMENT_PROCESSOR_CANCELED
        val invoicePayment =
            TestModelFactory.buildInvoicePayment(
                source = InvoicePaymentSource.ITAU,
                method = PaymentMethod.BOLEPIX,
                externalId = "externalId"
            )
        val invoicePaymentModel = invoicePayment.toModel()
        val expectedPayment = invoicePayment.cancel(reason)
        val expectedPaymentModel = expectedPayment.toModel()
        val event = InvoicePaymentCanceledEvent(
            invoicePayment = expectedPayment
        )
        val cancelSchedule = CancelPaymentOnAcquirerSchedule(
            invoicePaymentId = invoicePayment.id,
            requestedAt = LocalDateTime.now()
        )

        coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
        coEvery { cancelPaymentOnAcquirerScheduleService.scheduleCancelPaymentOnAcquirer(invoicePayment.id) } returns cancelSchedule
        coEvery { invoicePaymentDataService.update(expectedPaymentModel) } returns expectedPaymentModel
        coEvery {
            kafkaProducerService.produce(event)
        } returns mockk()

        val result = service.cancel(invoicePayment.id, reason)
        assertThat(result).isSuccessWithData(expectedPayment)

        coVerifyOnce { invoicePaymentDataService.get(any()) }
        coVerifyOnce { cancelPaymentOnAcquirerScheduleService.scheduleCancelPaymentOnAcquirer(any()) }
        coVerifyOnce { invoicePaymentDataService.update(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#secondCopy should return success when payment is Approved`() = runBlocking {
        val payment = TestModelFactory.buildInvoicePayment()
        val person = TestModelFactory.buildPerson()
        val approvedPayment = payment.approve()
        val approvedPaymentModel = approvedPayment.toModel()

        coEvery { invoicePaymentDataService.get(payment.id) } returns approvedPaymentModel


        val result = service.secondCopy(approvedPayment.id, person.id)
        assertThat(result).isFailureOfType(InvoicePaymentAlreadyApprovedException::class)

        coVerifyNone { paymentDetailService.getPaymentDetail(approvedPayment) }
        coVerifyOnce { invoicePaymentDataService.get(approvedPayment.id) }
        coVerifyNone { invoicePaymentDataService.update(match { it.id == approvedPayment.id && it.isCanceled }) }
        coVerifyNone {
            memberInvoiceDataService.find(queryEq { where { this.id.inList(approvedPayment.memberInvoiceIds) } })
        }
        coVerifyNone { memberInvoiceGroupDataService.get(approvedPayment.invoiceGroupId!!) }
        coVerifyNone {
            billingAccountablePartyService.get(any())
        }
        coVerifyNone { billingAccountablePartyService.getCurrent(any()) }
        coVerifyNone { memberService.get(any()) }
        coVerifyNone { invoicePaymentDataService.add(match { approvedPayment.memberInvoiceIds == it.memberInvoiceIds }) }
        coVerifyNone { kafkaProducerService.produce(any()) }
        coVerifyNone { kafkaProducerService.produce(any(), approvedPayment.id.toString()) }
    }

    @Test
    fun `#secondCopy should return success when payment was pending and has no memberInvoiceGroup`() = runBlocking {
        val now = LocalDate.now().atEndOfTheDay()
        val person = TestModelFactory.buildPerson()
        val dueDate = now.plusDays(3L)

        val firstMember = TestModelFactory.buildMember()
        val memberInvoices = listOf(TestModelFactory.buildMemberInvoice(member = firstMember))
        val memberInvoiceModels = memberInvoices.map { it.toModel() }
        val memberInvoiceIds = memberInvoices.map { it.id }
        val reason = PaymentReason.B2B_REGULAR_PAYMENT
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val payment = TestModelFactory.buildInvoicePayment(
            memberInvoiceIds = memberInvoiceIds,
            invoiceGroupId = null,
            billingAccountablePartyId = billingAccountableParty.id
        )
        val paymentModel = payment.toModel()
        val canceledPayment = payment.cancel(CancellationReason.PAYMENT_PROCESSOR_CANCELED)
        val canceledPaymentModel = canceledPayment.toModel()

        coEvery { invoicePaymentDataService.get(payment.id) } returns paymentModel
        coEvery { invoicePaymentDataService.update(match { it.id == payment.id && it.isCanceled }) } returns canceledPaymentModel

        coEvery {
            memberInvoiceDataService.find(queryEq { where { this.id.inList(canceledPayment.memberInvoiceIds) } })
        } returns memberInvoiceModels

        coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty

        val invoicePayment = InvoicePaymentBuilder.buildPendingInvoicePaymentWithDetails(
            canceledPayment.method,
            memberInvoices,
            dueDate,
            reason,
            billingAccountableParty,
        )
        val invoicePaymentModel = invoicePayment.toModel()

        coEvery {
            invoicePaymentDataService.findOneOrNull(queryEq {
                where {
                    this.memberInvoiceIds.containsAny(memberInvoiceIds)
                        .and(this.status.eq(InvoicePaymentStatus.PENDING))
                }
            })
        } returns null

        coEvery { memberService.get(memberInvoices.first().memberId) } returns firstMember

        coEvery {
            invoicePaymentDataService.add(match {
                invoicePayment.memberInvoiceIds == it.memberInvoiceIds && invoicePayment.billingAccountablePartyId == it.billingAccountablePartyId
            })
        } returns invoicePaymentModel
        coEvery {
            acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
        } returns InvoicePaymentSource.IUGU
        coEvery {
            moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
        } returns RangeUUID.generateUUIDv7()
        coEvery {
            kafkaProducerService.produce(match { it.name == InvoicePaymentCreatedEvent.name })
        } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCreatedEvent.name, 0)
        coEvery {
            kafkaProducerService.produce(
                any<CreateExternalPaymentRequestEvent>(), billingAccountableParty.id.toString()
            )
        } returns ProducerResult(LocalDateTime.now(), CreateExternalPaymentRequestEvent.name, 0)

        coEvery {
            kafkaProducerService.produce(match { it.name == InvoicePaymentCanceledEvent.name })
        } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCanceledEvent.name, 0)


        val result = service.secondCopy(canceledPayment.id, person.id)
        assertThat(result).isSuccessWithData(true)

        coVerifyNone { paymentDetailService.getPaymentDetail(invoicePayment) }
        coVerifyOnce { invoicePaymentDataService.get(canceledPayment.id) }
        coVerifyOnce { invoicePaymentDataService.update(match { it.id == canceledPayment.id && it.isCanceled }) }
        coVerifyOnce {
            memberInvoiceDataService.find(queryEq { where { this.id.inList(canceledPayment.memberInvoiceIds) } })
        }
        coVerifyNone { memberInvoiceGroupDataService.get(any()) }

        coVerifyOnce { billingAccountablePartyService.getCurrent(person.id) }
        coVerifyNone { memberService.get(memberInvoices.first().memberId) }
        coVerifyOnce { invoicePaymentDataService.add(match { invoicePayment.memberInvoiceIds == it.memberInvoiceIds }) }
        coVerifyOnce { kafkaProducerService.produce(match { it.name == InvoicePaymentCreatedEvent.name }) }
        coVerifyOnce {
            kafkaProducerService.produce(
                any<CreateExternalPaymentRequestEvent>(), billingAccountableParty.id.toString()
            )
        }
        coVerifyOnce { kafkaProducerService.produce(match { it.name == InvoicePaymentCanceledEvent.name }) }
    }

    @Test
    fun `#secondCopy should return success  when payment could be canceled at iugu`() = runBlocking {
        val now = LocalDate.now().atEndOfTheDay()
        val person = TestModelFactory.buildPerson()
        val dueDate = now.plusDays(3L)

        val firstMember = TestModelFactory.buildMember()
        val memberInvoices = listOf(TestModelFactory.buildMemberInvoice(member = firstMember))
        val memberInvoiceModels = memberInvoices.map { it.toModel() }
        val memberInvoiceIds = memberInvoices.map { it.id }
        val reason = PaymentReason.B2B_REGULAR_PAYMENT
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val payment = TestModelFactory.buildInvoicePayment(
            externalId = "externalId",
            memberInvoiceIds = memberInvoiceIds,
            invoiceGroupId = null,
            billingAccountablePartyId = billingAccountableParty.id
        )
        val paymentModel = payment.toModel()
        val canceledPayment = payment.cancel(CancellationReason.PAYMENT_PROCESSOR_CANCELED)
        val canceledPaymentModel = canceledPayment.toModel()

        coEvery { invoicePaymentDataService.get(payment.id) } returns paymentModel
        coEvery { acquirerOrchestratorService.cancel(payment) } returns true
        coEvery { invoicePaymentDataService.update(match { it.id == payment.id && it.isCanceled }) } returns canceledPaymentModel

        coEvery {
            memberInvoiceDataService.find(queryEq { where { this.id.inList(canceledPayment.memberInvoiceIds) } })
        } returns memberInvoiceModels

        coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty

        val invoicePayment = InvoicePaymentBuilder.buildPendingInvoicePaymentWithDetails(
            canceledPayment.method,
            memberInvoices,
            dueDate,
            reason,
            billingAccountableParty,
        )
        val invoicePaymentModel = invoicePayment.toModel()

        coEvery {
            invoicePaymentDataService.findOneOrNull(queryEq {
                where {
                    this.memberInvoiceIds.containsAny(memberInvoiceIds)
                        .and(this.status.eq(InvoicePaymentStatus.PENDING))
                }
            })
        } returns null

        coEvery { memberService.get(memberInvoices.first().memberId) } returns firstMember

        coEvery {
            invoicePaymentDataService.add(match {
                invoicePayment.memberInvoiceIds == it.memberInvoiceIds && invoicePayment.billingAccountablePartyId == it.billingAccountablePartyId
            })
        } returns invoicePaymentModel

        coEvery {
            acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
        } returns InvoicePaymentSource.IUGU

        coEvery {
            moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
        } returns RangeUUID.generateUUIDv7()
        coEvery {
            kafkaProducerService.produce(match { it.name == InvoicePaymentCreatedEvent.name })
        } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCreatedEvent.name, 0)
        coEvery {
            kafkaProducerService.produce(
                any<CreateExternalPaymentRequestEvent>(), billingAccountableParty.id.toString()
            )
        } returns ProducerResult(LocalDateTime.now(), CreateExternalPaymentRequestEvent.name, 0)
        coEvery {
            kafkaProducerService.produce(match { it.name == InvoicePaymentCanceledEvent.name })
        } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCanceledEvent.name, 0)


        val result = service.secondCopy(canceledPayment.id, person.id)
        assertThat(result).isSuccessWithData(true)

        coVerifyNone { paymentDetailService.getPaymentDetail(invoicePayment) }
        coVerifyOnce { invoicePaymentDataService.get(canceledPayment.id) }
        coVerifyOnce { invoicePaymentDataService.update(match { it.id == canceledPayment.id && it.isCanceled }) }
        coVerifyOnce {
            memberInvoiceDataService.find(queryEq { where { this.id.inList(canceledPayment.memberInvoiceIds) } })
        }
        coVerifyNone { memberInvoiceGroupDataService.get(any()) }

        coVerifyOnce { billingAccountablePartyService.getCurrent(person.id) }
        coVerifyNone { memberService.get(memberInvoices.first().memberId) }
        coVerifyOnce { invoicePaymentDataService.add(match { invoicePayment.memberInvoiceIds == it.memberInvoiceIds }) }
        coVerifyOnce { kafkaProducerService.produce(match { it.name == InvoicePaymentCreatedEvent.name }) }
        coVerifyOnce {
            kafkaProducerService.produce(
                any<CreateExternalPaymentRequestEvent>(), billingAccountableParty.id.toString()
            )
        }
        coVerifyOnce { kafkaProducerService.produce(match { it.name == InvoicePaymentCanceledEvent.name }) }
    }

    @Test
    fun `#secondCopy should return success  when payment could not be canceled at iugu`() = runBlocking {
        val now = LocalDate.now().atEndOfTheDay()
        val person = TestModelFactory.buildPerson()
        val dueDate = now.plusDays(3L)

        val firstMember = TestModelFactory.buildMember()
        val memberInvoices = listOf(TestModelFactory.buildMemberInvoice(member = firstMember))
        val memberInvoiceModels = memberInvoices.map { it.toModel() }
        val memberInvoiceIds = memberInvoices.map { it.id }
        val reason = PaymentReason.B2B_REGULAR_PAYMENT
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val payment = TestModelFactory.buildInvoicePayment(
            externalId = "externalId",
            memberInvoiceIds = memberInvoiceIds,
            invoiceGroupId = null,
            billingAccountablePartyId = billingAccountableParty.id
        )
        val paymentModel = payment.toModel()
        val canceledPayment = payment.cancel(CancellationReason.PAYMENT_PROCESSOR_CANCELED)
        val canceledPaymentModel = canceledPayment.toModel()

        coEvery { invoicePaymentDataService.get(payment.id) } returns paymentModel
        coEvery { acquirerOrchestratorService.cancel(payment) } returns NotFoundException("Payment could not be cancelled")
        coEvery { invoicePaymentDataService.update(match { it.id == payment.id && it.isCanceled }) } returns canceledPaymentModel

        coEvery {
            memberInvoiceDataService.find(queryEq { where { this.id.inList(canceledPayment.memberInvoiceIds) } })
        } returns memberInvoiceModels

        coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty

        val invoicePayment = InvoicePaymentBuilder.buildPendingInvoicePaymentWithDetails(
            canceledPayment.method,
            memberInvoices,
            dueDate,
            reason,
            billingAccountableParty,
        )
        val invoicePaymentModel = invoicePayment.toModel()

        coEvery {
            invoicePaymentDataService.findOneOrNull(queryEq {
                where {
                    this.memberInvoiceIds.containsAny(memberInvoiceIds)
                        .and(this.status.eq(InvoicePaymentStatus.PENDING))
                }
            })
        } returns null

        coEvery { memberService.get(memberInvoices.first().memberId) } returns firstMember

        coEvery {
            invoicePaymentDataService.add(match {
                invoicePayment.memberInvoiceIds == it.memberInvoiceIds && invoicePayment.billingAccountablePartyId == it.billingAccountablePartyId
            })
        } returns invoicePaymentModel
        coEvery {
            acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
        } returns InvoicePaymentSource.IUGU
        coEvery {
            moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
        } returns RangeUUID.generateUUIDv7()
        coEvery {
            kafkaProducerService.produce(match { it.name == InvoicePaymentCreatedEvent.name })
        } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCreatedEvent.name, 0)
        coEvery {
            kafkaProducerService.produce(
                any<CreateExternalPaymentRequestEvent>(), billingAccountableParty.id.toString()
            )
        } returns ProducerResult(LocalDateTime.now(), CreateExternalPaymentRequestEvent.name, 0)

        coEvery {
            kafkaProducerService.produce(match { it.name == InvoicePaymentCanceledEvent.name })
        } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCanceledEvent.name, 0)


        val result = service.secondCopy(canceledPayment.id, person.id)
        assertThat(result).isSuccessWithData(true)

        coVerifyNone { paymentDetailService.getPaymentDetail(invoicePayment) }
        coVerifyOnce { invoicePaymentDataService.get(canceledPayment.id) }
        coVerifyOnce { invoicePaymentDataService.update(match { it.id == canceledPayment.id && it.isCanceled }) }
        coVerifyOnce {
            memberInvoiceDataService.find(queryEq { where { this.id.inList(canceledPayment.memberInvoiceIds) } })
        }
        coVerifyNone { memberInvoiceGroupDataService.get(any()) }

        coVerifyOnce { billingAccountablePartyService.getCurrent(person.id) }
        coVerifyNone { memberService.get(memberInvoices.first().memberId) }
        coVerifyOnce { invoicePaymentDataService.add(match { invoicePayment.memberInvoiceIds == it.memberInvoiceIds }) }
        coVerifyOnce { kafkaProducerService.produce(match { it.name == InvoicePaymentCreatedEvent.name }) }
        coVerifyOnce {
            kafkaProducerService.produce(
                any<CreateExternalPaymentRequestEvent>(), billingAccountableParty.id.toString()
            )
        }
        coVerifyOnce { kafkaProducerService.produce(match { it.name == InvoicePaymentCanceledEvent.name }) }
    }

    @Test
    fun `#secondCopy should return success when payment was pending and has memberInvoiceGroup`() = runBlocking {
        val now = LocalDate.now().atEndOfTheDay()
        val person = TestModelFactory.buildPerson()
        val dueDate = now.plusDays(3L)
        val firstMember = TestModelFactory.buildMember()
        val memberInvoices =
            listOf(TestModelFactory.buildMemberInvoice(member = firstMember), TestModelFactory.buildMemberInvoice())
        val memberInvoiceModels = memberInvoices.map { it.toModel() }
        val memberInvoiceIds = memberInvoices.map { it.id }
        val reason = PaymentReason.B2B_REGULAR_PAYMENT
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
            memberInvoiceIds = memberInvoiceIds, type = MemberInvoiceType.B2B_REGULAR_PAYMENT
        )
        val memberInvoiceGroupModel = memberInvoiceGroup.toModel()
        val payment = TestModelFactory.buildInvoicePayment(
            memberInvoiceIds = memberInvoiceIds, invoiceGroupId = memberInvoiceGroup.id
        )
        val paymentModel = payment.toModel()
        val canceledPayment = payment.cancel(CancellationReason.CANCELED_BY_REISSUE)
        val canceledPaymentModel = canceledPayment.toModel()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        coEvery { invoicePaymentDataService.get(payment.id) } returns paymentModel
        coEvery { invoicePaymentDataService.update(canceledPaymentModel) } returns canceledPaymentModel

        coEvery {
            memberInvoiceDataService.find(queryEq { where { this.id.inList(canceledPayment.memberInvoiceIds) } })
        } returns memberInvoiceModels
        coEvery { memberInvoiceGroupDataService.get(canceledPayment.invoiceGroupId!!) } returns memberInvoiceGroupModel

        coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty

        val invoicePayment = InvoicePaymentBuilder.buildPendingInvoicePaymentWithDetails(
            canceledPayment.method,
            memberInvoices,
            dueDate,
            reason,
            billingAccountableParty,
        ).copy(invoiceGroupId = memberInvoiceGroup.id)
        val invoicePaymentModel = invoicePayment.toModel()

        coEvery {
            invoicePaymentDataService.findOneOrNull(queryEq {
                where {
                    this.memberInvoiceIds.containsAny(memberInvoiceIds)
                        .and(this.status.eq(InvoicePaymentStatus.PENDING))
                }
            })
        } returns null

        coEvery { memberService.get(memberInvoices.first().memberId) } returns firstMember

        coEvery { invoicePaymentDataService.add(match { invoicePayment.memberInvoiceIds == it.memberInvoiceIds }) } returns invoicePaymentModel
        coEvery {
            acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
        } returns InvoicePaymentSource.IUGU
        coEvery {
            moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
        } returns RangeUUID.generateUUIDv7()
        coEvery {
            kafkaProducerService.produce(match { it.name == InvoicePaymentCreatedEvent.name })
        } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCreatedEvent.name, 0)
        coEvery {
            kafkaProducerService.produce(
                any<CreateExternalPaymentRequestEvent>(), billingAccountableParty.id.toString()
            )
        } returns ProducerResult(LocalDateTime.now(), CreateExternalPaymentRequestEvent.name, 0)

        coEvery {
            kafkaProducerService.produce(match { it.name == InvoicePaymentCanceledEvent.name })
        } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCanceledEvent.name, 0)


        val result = service.secondCopy(canceledPayment.id, person.id)
        assertThat(result).isSuccessWithData(true)

        coVerifyNone { paymentDetailService.getPaymentDetail(invoicePayment) }
        coVerifyOnce { invoicePaymentDataService.get(canceledPayment.id) }
        coVerifyOnce { invoicePaymentDataService.update(match { it.id == canceledPayment.id && it.isCanceled }) }
        coVerifyOnce {
            memberInvoiceDataService.find(queryEq { where { this.id.inList(canceledPayment.memberInvoiceIds) } })
        }
        coVerifyOnce { memberInvoiceGroupDataService.get(canceledPayment.invoiceGroupId!!) }
        coVerifyOnce { billingAccountablePartyService.getCurrent(person.id) }
        coVerifyNone { memberService.get(memberInvoices.first().memberId) }
        coVerifyOnce { invoicePaymentDataService.add(match { invoicePayment.memberInvoiceIds == it.memberInvoiceIds }) }
        coVerifyOnce { kafkaProducerService.produce(match { it.name == InvoicePaymentCreatedEvent.name }) }
        coVerifyOnce {
            kafkaProducerService.produce(
                any<CreateExternalPaymentRequestEvent>(), billingAccountableParty.id.toString()
            )
        }
        coVerifyOnce { kafkaProducerService.produce(match { it.name == InvoicePaymentCanceledEvent.name }) }
    }

    @Test
    fun `#fail should return success when payment is already failed`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val reason = "reason"
        val failedPayment = invoicePayment.fail(reason)
        val failedPaymentModel = failedPayment.toModel()

        coEvery { invoicePaymentDataService.get(failedPayment.id) } returns failedPaymentModel


        val result = service.fail(failedPayment.id, reason)
        assertThat(result).isSuccessWithData(failedPayment)
    }

    @Test
    fun `#fail should return failure when payment is already paid`() = runBlocking<Unit> {
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val approvedPayment = invoicePayment.approve()
        val approvedPaymentModel = approvedPayment.toModel()
        val reason = "reason"

        coEvery { invoicePaymentDataService.get(approvedPayment.id) } returns approvedPaymentModel


        val result = service.fail(approvedPayment.id, reason)
        assertThat(result).isFailureOfType(InvoicePaymentAlreadyApprovedException::class)
    }

    @Test
    fun `#fail should return success when payment was failed successfully`() = runBlocking<Unit> {
        val reason = "reason"
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val invoicePaymentModel = invoicePayment.toModel()
        val expectedPayment = invoicePayment.fail(reason)
        val expectedPaymentModel = expectedPayment.toModel()

        coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
        coEvery { invoicePaymentDataService.update(match { it.id == invoicePayment.id && it.isFailed }) } returns expectedPaymentModel


        val result = service.fail(invoicePayment.id, reason)
        assertThat(result).isSuccessWithData(expectedPayment)


        assertThatCounter("invoice_payment_status_changes_total").withLabels(
            "status" to "FAILED",
            "method" to "BOLETO",
        ).hasCountOf(1)
    }

    @Test
    fun `#listInvoicePayments should return all invoice payments with member invoice id, but without payment details`() =
        runBlocking {
            val memberInvoiceId = RangeUUID.generate()
            val invoicePayments = listOf(
                TestModelFactory.buildInvoicePayment(memberInvoiceIds = listOf(memberInvoiceId)),
            )
            val invoicePaymentModels = invoicePayments.map { it.toModel() }

            coEvery { invoicePaymentDataService.find(any()) } returns invoicePaymentModels

            val result = service.listInvoicePayments(memberInvoiceId, withPaymentDetails = false)

            assertThat(result).isSuccessWithData(invoicePayments)

            coVerifyNone { paymentDetailService.getPaymentDetail(any()) }
        }

    @Test
    fun `#getAllPaymentDetails should return all method invoice payments`() = runBlocking {
        val memberInvoiceId = RangeUUID.generate()
        val invoicePaymentId = RangeUUID.generate()
        val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail().copy(
            paymentId = invoicePaymentId
        )
        val pixPaymentDetail = TestModelFactory.buildPixPaymentDetail()
        val simpleCreditCardPaymentDetail = TestModelFactory.buildSimpleCreditCardPaymentDetail()
        val invoicePayment = TestModelFactory.buildInvoicePayment(
            id = invoicePaymentId, memberInvoiceIds = listOf(memberInvoiceId), paymentDetail = boletoPaymentDetail
        )

        coEvery {
            paymentDetailService.getPaymentDetails(
                listOf(invoicePayment.id), PaymentMethod.BOLETO
            )
        } returns listOf(boletoPaymentDetail)
        coEvery { paymentDetailService.getPaymentDetails(listOf(invoicePayment.id), PaymentMethod.PIX) } returns listOf(
            pixPaymentDetail
        )
        coEvery {
            paymentDetailService.getPaymentDetails(
                listOf(invoicePayment.id), PaymentMethod.SIMPLE_CREDIT_CARD
            )
        } returns listOf(simpleCreditCardPaymentDetail)

        val resultBoleto = paymentDetailService.getPaymentDetails(listOf(invoicePayment.id), PaymentMethod.BOLETO)
        val resultPix = paymentDetailService.getPaymentDetails(listOf(invoicePayment.id), PaymentMethod.PIX)
        val resultSimpleCreditCard =
            paymentDetailService.getPaymentDetails(listOf(invoicePayment.id), PaymentMethod.SIMPLE_CREDIT_CARD)

        assertThat(resultBoleto).isSuccessWithData(listOf(boletoPaymentDetail))
        assertThat(resultPix).isSuccessWithData(listOf(pixPaymentDetail))
        assertThat(resultSimpleCreditCard).isSuccessWithData(listOf(simpleCreditCardPaymentDetail))
    }

    @Test
    fun `#listInvoicePayments should return all invoice payments with member invoice id and payment details`(): Unit =
        runBlocking {
            val memberInvoiceId = RangeUUID.generate()
            val invoicePaymentId = RangeUUID.generate()
            val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail().copy(
                paymentId = invoicePaymentId
            )
            val pixPaymentDetail = TestModelFactory.buildPixPaymentDetail()
            val bolepixPaymentDetail = TestModelFactory.buildBolepixPaymentDetail()
            val simpleCreditCardPaymentDetail = TestModelFactory.buildSimpleCreditCardPaymentDetail()
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                id = invoicePaymentId, memberInvoiceIds = listOf(memberInvoiceId), paymentDetail = boletoPaymentDetail
            )

            val invoicePayments = listOf(invoicePayment)
            val invoicePaymentModels = invoicePayments.map { it.toModel() }

            coEvery { invoicePaymentDataService.find(any()) } returns invoicePaymentModels

            coEvery {
                paymentDetailService.getPaymentDetails(
                    listOf(invoicePayment.id), PaymentMethod.BOLETO
                )
            } returns listOf(boletoPaymentDetail)
            coEvery {
                paymentDetailService.getPaymentDetails(
                    listOf(invoicePayment.id), PaymentMethod.PIX
                )
            } returns listOf(pixPaymentDetail)
            coEvery {
                paymentDetailService.getPaymentDetails(
                    listOf(invoicePayment.id), PaymentMethod.BOLEPIX
                )
            } returns listOf(bolepixPaymentDetail)
            coEvery {
                paymentDetailService.getPaymentDetails(
                    listOf(invoicePayment.id), PaymentMethod.SIMPLE_CREDIT_CARD
                )
            } returns listOf(simpleCreditCardPaymentDetail)

            val result = service.listInvoicePayments(memberInvoiceId, withPaymentDetails = true)

            assertThat(result).isSuccessWithData(invoicePayments)

            val paymentDetail = result.get().first().paymentDetail

            Assertions.assertThat(paymentDetail).isEqualTo(boletoPaymentDetail)

        }

    @Nested
    @DisplayName("createExternalPaymentForInvoicePayment")
    inner class CreateExternalPaymentForInvoicePayment {
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(personId = person.id)
        val memberInvoice = TestModelFactory.buildMemberInvoice(member = member, totalAmount = BigDecimal("1200.00"))
        val memberInvoiceModel = memberInvoice.toModel()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty(
            type = BillingAccountablePartyType.LEGAL_PERSON
        )

        val bankSlipInfo = BankSlipInfo(
            digitableLine = "****************",
            barcodeImageUrl = "www.imagem.com.br",
            barcodeData = "base64"
        )
        val pixInfo = PixInfo(
            qrCodeImageUrl = "www.imagem.com.br",
            qrCodeBase64 = "base64",
            copyAndPaste = "asdaiusdhasiuh@gov.1231"
        )
        val bolepixPaymentDetail = TestModelFactory.buildBolepixPaymentDetail()
            .copy(
                paymentUrl = "www.payment.com.br",
                barcodeBoleto = bankSlipInfo.digitableLine,
                boletoPaymentUrl = bankSlipInfo.barcodeImageUrl,
                paymentCodePix = pixInfo.copyAndPaste,
                pixPaymentUrl = pixInfo.qrCodeImageUrl,
                pixQrCode = pixInfo.qrCodeBase64,
                pixCopyAndPaste = pixInfo.copyAndPaste,
                bankSlipBarcodeData = bankSlipInfo.barcodeData,
                bankSlipDigitableLine = bankSlipInfo.digitableLine
            )

        val pixPaymentDetail = TestModelFactory.buildPixPaymentDetail()
            .copy(
                paymentUrl = "www.payment.com.br",
                paymentCode = pixInfo.copyAndPaste,
                qrCode = pixInfo.qrCodeBase64,
                copyAndPaste = pixInfo.copyAndPaste
            )

        val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail()
            .copy(
                paymentUrl = "www.payment.com.br",
                barcode = bankSlipInfo.digitableLine,
                barcodeData = bankSlipInfo.barcodeData,
                digitableLine = bankSlipInfo.digitableLine
            )

        @Test
        fun `IssueBoleto, save externalId and produce to kafka`() = runBlocking {
            val paymentDetail = TestModelFactory.buildBoletoPaymentDetail().copy(
                barcode = "842738974287384729734",
                paymentUrl = "www.imagem.com.br",
                barcodeData = "base64",
                digitableLine = "842738974287384729734"
            )
            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
            val memberInvoiceGroupModel = memberInvoiceGroup.toModel()
            val billingAccountableParty =
                billingAccountableParty.copy(type = BillingAccountablePartyType.NATURAL_PERSON)
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = PaymentMethod.BOLETO,
                paymentDetail = paymentDetail,
                billingAccountablePartyId = billingAccountableParty.id,
                memberInvoiceIds = listOf(memberInvoice.id),
                invoiceGroupId = memberInvoiceGroup.id,
            )
            val invoicePaymentModel = invoicePayment.toModel()
            val issuedBoleto = AcquirerCreatePaymentResponse(
                id = RangeUUID.generate().toString(),
                status = PaymentStatus.PENDING,
                bankSlip = BankSlipInfo(
                    digitableLine = paymentDetail.digitableLine,
                    barcodeImageUrl = paymentDetail.paymentUrl,
                    barcodeData = paymentDetail.barcodeData
                ),
                externalUrl = "http://urlissuedBoleto",
            )
            val updatedPaymentDetails = paymentDetail.copy(
                barcode = issuedBoleto.bankSlip!!.digitableLine,
                paymentUrl = issuedBoleto.externalUrl,
                barcodeData = issuedBoleto.bankSlip!!.barcodeData,
                digitableLine = issuedBoleto.bankSlip!!.digitableLine
            )
            val invoicePaymentWithExternalId =
                invoicePayment.copy(externalId = issuedBoleto.id).withPaymentDetail(updatedPaymentDetails)
            val invoicePaymentWithExternalIdModel = invoicePaymentWithExternalId.toModel()

            coEvery { memberInvoiceDataService.find(any()) } returns listOf(memberInvoiceModel)
            coEvery {
                memberInvoiceGroupDataService.get(invoicePayment.invoiceGroupId!!)
            } returns memberInvoiceGroupModel
            coEvery { paymentService.issueInvoicePayment(invoicePayment, billingAccountableParty) } returns issuedBoleto
            coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
            coEvery {
                invoicePaymentDataService.update(match { it.id == invoicePayment.id && it.externalId == issuedBoleto.id })
            } returns invoicePaymentWithExternalIdModel
            coEvery {
                paymentDetailService.createPaymentDetail(match { it.id == updatedPaymentDetails.id })
            } returns updatedPaymentDetails
            coEvery {
                kafkaProducerService.produce(match { it: ExternalInvoiceCreatedEvent -> it.payload.invoicePayment == invoicePaymentWithExternalId })
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(any(), any())
            } returns mockk()


            val result = service.createExternalPaymentForInvoicePayment(
                invoicePayment, billingAccountableParty
            )
            assertThat(result).isSuccessWithData(invoicePaymentWithExternalId)
        }

        @Test
        fun `IssueBoleto, save externalId and produce to kafka when the billing is legal person`() = runBlocking {
            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
            val memberInvoiceGroupModel = memberInvoiceGroup.toModel()
            val billingAccountableParty = billingAccountableParty.copy(type = BillingAccountablePartyType.LEGAL_PERSON)
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = PaymentMethod.BOLETO,
                paymentDetail = boletoPaymentDetail,
                billingAccountablePartyId = billingAccountableParty.id,
                memberInvoiceIds = listOf(memberInvoice.id),
                invoiceGroupId = memberInvoiceGroup.id,
            )
            val invoicePaymentModel = invoicePayment.toModel()
            val issuedBoleto = AcquirerCreatePaymentResponse(
                id = RangeUUID.generate().toString(),
                status = PaymentStatus.PENDING,
                bankSlip = bankSlipInfo,
                externalUrl = "http://urlissuedBoleto",
            )
            val updatedPaymentDetails = boletoPaymentDetail.copy(
                barcode = issuedBoleto.bankSlip!!.digitableLine,
                paymentUrl = issuedBoleto.externalUrl,
            )
            val invoicePaymentWithExternalId =
                invoicePayment.copy(externalId = issuedBoleto.id).withPaymentDetail(updatedPaymentDetails)
            val invoicePaymentWithExternalIdModel = invoicePaymentWithExternalId.toModel()

            coEvery { memberInvoiceDataService.find(any()) } returns listOf(memberInvoiceModel)
            coEvery {
                memberInvoiceGroupDataService.get(invoicePayment.invoiceGroupId!!)
            } returns memberInvoiceGroupModel
            coEvery { paymentService.issueInvoicePayment(invoicePayment, billingAccountableParty) } returns issuedBoleto
            coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
            coEvery {
                invoicePaymentDataService.update(match { it.id == invoicePayment.id && it.externalId == issuedBoleto.id })
            } returns invoicePaymentWithExternalIdModel
            coEvery {
                paymentDetailService.createPaymentDetail(match { it.id == updatedPaymentDetails.id })
            } returns updatedPaymentDetails
            coEvery {
                kafkaProducerService.produce(match { it: ExternalInvoiceCreatedEvent -> it.payload.invoicePayment == invoicePaymentWithExternalId })
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(any(), any())
            } returns mockk()


            val result = service.createExternalPaymentForInvoicePayment(
                invoicePayment, billingAccountableParty
            )
            assertThat(result).isSuccessWithData(
                invoicePayment.withPaymentDetail(updatedPaymentDetails).copy(externalId = issuedBoleto.id)
            )
        }

        @Test
        fun `IssuePix, save externalId and produce to kafka`() = runBlocking() {
            val dueDate = LocalDate.now().plusDays(5).atEndOfTheDay()
            val paymentDetail = pixPaymentDetail.copy(dueDate = dueDate)
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = PaymentMethod.PIX,
                paymentDetail = paymentDetail,
                billingAccountablePartyId = billingAccountableParty.id,
                memberInvoiceIds = listOf(memberInvoice.id)
            )
            val invoicePaymentModel = invoicePayment.toModel()
            val issuedPix = AcquirerCreatePaymentResponse(
                id = RangeUUID.generate().toString(),
                status = PaymentStatus.PENDING,
                pix = pixInfo,
                externalUrl = "http://url",
            )

            val updatedPaymentDetails = paymentDetail.copy(
                paymentUrl = issuedPix.externalUrl,
                paymentCode = issuedPix.pix!!.copyAndPaste,
                dueDate = dueDate,
            )

            val invoicePaymentWithExternalId =
                invoicePayment.copy(externalId = issuedPix.id).withPaymentDetail(updatedPaymentDetails)
            val invoicePaymentWithExternalIdModel = invoicePaymentWithExternalId.toModel()

            coEvery { memberInvoiceDataService.get(memberInvoice.id) } returns memberInvoiceModel
            coEvery { paymentService.issueInvoicePayment(invoicePayment, billingAccountableParty) } returns issuedPix
            coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
            coEvery {
                invoicePaymentDataService.update(match { it.id == invoicePayment.id && it.externalId == issuedPix.id })
            } returns invoicePaymentWithExternalIdModel
            coEvery {
                paymentDetailService.createPaymentDetail(match { it.id == updatedPaymentDetails.id })
            } returns updatedPaymentDetails
            coEvery {
                kafkaProducerService.produce(match { it: ExternalInvoiceCreatedEvent -> it.payload.invoicePayment == invoicePaymentWithExternalId })
            } returns mockk()


            val result = service.createExternalPaymentForInvoicePayment(
                invoicePayment, billingAccountableParty
            )
            assertThat(result).isSuccessWithData(
                invoicePayment.withPaymentDetail(updatedPaymentDetails).copy(externalId = issuedPix.id)
            )
        }

        @Test
        fun `IssueBolepix, save externalId and produce to kafka`() = runBlocking() {
            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
            val memberInvoiceGroupModel = memberInvoiceGroup.toModel()
            val billingAccountableParty =
                billingAccountableParty.copy(type = BillingAccountablePartyType.NATURAL_PERSON)
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = PaymentMethod.BOLEPIX,
                paymentDetail = bolepixPaymentDetail,
                billingAccountablePartyId = billingAccountableParty.id,
                memberInvoiceIds = listOf(memberInvoice.id),
                invoiceGroupId = memberInvoiceGroup.id,
            )
            val invoicePaymentModel = invoicePayment.toModel()
            val issuedBolepix = AcquirerCreatePaymentResponse(
                id = RangeUUID.generate().toString(),
                status = PaymentStatus.PENDING,
                bankSlip = bankSlipInfo,
                pix = pixInfo,
                externalUrl = "http://url",
            )
            val updatedPaymentDetails = bolepixPaymentDetail.copy(
                paymentUrl = issuedBolepix.externalUrl,
                barcodeBoleto = issuedBolepix.bankSlip!!.digitableLine,
                boletoPaymentUrl = issuedBolepix.externalUrl,
                paymentCodePix = issuedBolepix.pix!!.copyAndPaste,
                pixPaymentUrl = issuedBolepix.externalUrl,
            )
            val invoicePaymentWithExternalId =
                invoicePayment.copy(externalId = issuedBolepix.id).withPaymentDetail(updatedPaymentDetails)
            val invoicePaymentWithExternalIdModel = invoicePaymentWithExternalId.toModel()

            coEvery { memberInvoiceDataService.find(any()) } returns listOf(memberInvoiceModel)
            coEvery {
                paymentService.issueInvoicePayment(
                    invoicePayment,
                    billingAccountableParty
                )
            } returns issuedBolepix
            coEvery { memberInvoiceGroupDataService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroupModel
            coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
            coEvery {
                invoicePaymentDataService.update(match { it.id == invoicePayment.id && it.externalId == issuedBolepix.id })
            } returns invoicePaymentWithExternalIdModel
            coEvery {
                kafkaProducerService.produce(match { it: ExternalInvoiceCreatedEvent -> it.payload.invoicePayment == invoicePaymentWithExternalId })
            } returns mockk()


            val result = service.createExternalPaymentForInvoicePayment(
                invoicePayment, billingAccountableParty
            )
            assertThat(result).isSuccessWithData(invoicePaymentWithExternalId)
        }

        @Test
        fun `IssueBolepix, save externalId and produce to kafka when the billing is legal person`() = runBlocking {
            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
            val memberInvoiceGroupModel = memberInvoiceGroup.toModel()
            val billingAccountableParty = billingAccountableParty.copy(type = BillingAccountablePartyType.LEGAL_PERSON)
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = PaymentMethod.BOLEPIX,
                paymentDetail = bolepixPaymentDetail,
                billingAccountablePartyId = billingAccountableParty.id,
                memberInvoiceIds = listOf(memberInvoice.id),
                invoiceGroupId = memberInvoiceGroup.id,
            )
            val invoicePaymentModel = invoicePayment.toModel()
            val issuedBolepix = AcquirerCreatePaymentResponse(
                id = RangeUUID.generate().toString(),
                status = PaymentStatus.PENDING,
                bankSlip = bankSlipInfo,
                pix = pixInfo,
                externalUrl = "http://url"
            )
            val updatedPaymentDetails = bolepixPaymentDetail.copy(
                paymentUrl = issuedBolepix.externalUrl,
                barcodeBoleto = issuedBolepix.bankSlip!!.digitableLine,
                boletoPaymentUrl = issuedBolepix.externalUrl,
                paymentCodePix = issuedBolepix.pix!!.copyAndPaste,
                pixPaymentUrl = issuedBolepix.externalUrl,
            )
            val invoicePaymentWithExternalId =
                invoicePayment.copy(externalId = issuedBolepix.id).withPaymentDetail(updatedPaymentDetails)
            val invoicePaymentWithExternalIdModel = invoicePaymentWithExternalId.toModel()

            coEvery { memberInvoiceDataService.find(any()) } returns listOf(memberInvoiceModel)

            coEvery {
                paymentService.issueInvoicePayment(
                    invoicePayment,
                    billingAccountableParty
                )
            } returns issuedBolepix
            coEvery {
                memberInvoiceGroupDataService.get(invoicePayment.invoiceGroupId!!)
            } returns memberInvoiceGroupModel
            coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
            coEvery {
                invoicePaymentDataService.update(match { it.id == invoicePayment.id && it.externalId == issuedBolepix.id })
            } returns invoicePaymentWithExternalIdModel
            coEvery {
                kafkaProducerService.produce(match { it: ExternalInvoiceCreatedEvent -> it.payload.invoicePayment == invoicePaymentWithExternalId })
            } returns mockk()


            val result = service.createExternalPaymentForInvoicePayment(
                invoicePayment, billingAccountableParty
            )
            assertThat(result).isSuccessWithData(invoicePaymentWithExternalId)

        }

        @Test
        fun `IssueBolepix, save externalId and produce to kafka when the billing is legal person when payment belongs to some liquidation`() =
            runBlocking {
                val billingAccountableParty =
                    billingAccountableParty.copy(type = BillingAccountablePartyType.LEGAL_PERSON)
                val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()
                val invoicePayment = TestModelFactory.buildInvoicePayment(
                    method = PaymentMethod.BOLEPIX,
                    paymentDetail = bolepixPaymentDetail,
                    billingAccountablePartyId = billingAccountableParty.id,
                    memberInvoiceIds = listOf(memberInvoice.id),
                    invoiceLiquidationId = invoiceLiquidation.id,
                    invoiceGroupId = null,
                )
                val invoicePaymentModel = invoicePayment.toModel()
                val issuedBolepix = AcquirerCreatePaymentResponse(
                    id = RangeUUID.generate().toString(),
                    status = PaymentStatus.PENDING,
                    bankSlip = bankSlipInfo,
                    pix = pixInfo,
                    externalUrl = "http://url",
                )
                val updatedPaymentDetails = bolepixPaymentDetail.copy(
                    paymentUrl = issuedBolepix.externalUrl,
                    barcodeBoleto = issuedBolepix.bankSlip!!.digitableLine,
                    boletoPaymentUrl = issuedBolepix.externalUrl,
                    paymentCodePix = issuedBolepix.pix!!.copyAndPaste,
                    pixPaymentUrl = issuedBolepix.externalUrl,
                )
                val invoicePaymentWithExternalId =
                    invoicePayment.copy(externalId = issuedBolepix.id).withPaymentDetail(updatedPaymentDetails)
                val invoicePaymentWithExternalIdModel = invoicePaymentWithExternalId.toModel()

                coEvery { memberInvoiceDataService.find(any()) } returns listOf(memberInvoiceModel)
                coEvery {
                    paymentService.issueInvoicePayment(
                        invoicePayment,
                        billingAccountableParty
                    )
                } returns issuedBolepix
                coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
                coEvery {
                    invoicePaymentDataService.update(match { it.id == invoicePayment.id && it.externalId == issuedBolepix.id })
                } returns invoicePaymentWithExternalIdModel
                coEvery {
                    kafkaProducerService.produce(match { it: ExternalInvoiceCreatedEvent -> it.payload.invoicePayment == invoicePaymentWithExternalId })
                } returns mockk()


                val result = service.createExternalPaymentForInvoicePayment(
                    invoicePayment, billingAccountableParty
                )
                assertThat(result).isSuccessWithData(invoicePaymentWithExternalId)
            }

        @Test
        fun `should return an error when the due date is before then now`() =
            runBlocking() {
                val dueDate = LocalDate.now().minusDays(5).atEndOfTheDay()
                val paymentDetail = TestModelFactory.buildBolepixPaymentDetail(dueDate = dueDate)

                val billingAccountableParty =
                    billingAccountableParty.copy(type = BillingAccountablePartyType.LEGAL_PERSON)
                val invoicePayment = TestModelFactory.buildInvoicePayment(
                    method = PaymentMethod.BOLEPIX,
                    paymentDetail = paymentDetail,
                    billingAccountablePartyId = billingAccountableParty.id,
                    memberInvoiceIds = listOf(memberInvoice.id),
                    invoiceGroupId = null,
                )

                val result = service.createExternalPaymentForInvoicePayment(
                    invoicePayment, billingAccountableParty
                )
                assertThat(result).isFailureOfType(PaymentValidationException::class)
            }

        @Test
        fun `Return error when paymentDetail is null`() = runBlocking {
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = PaymentMethod.BOLEPIX,
                paymentDetail = null,
                billingAccountablePartyId = billingAccountableParty.id,
                memberInvoiceIds = listOf(memberInvoice.id)
            )

            val result = service.createExternalPaymentForInvoicePayment(
                invoicePayment, billingAccountableParty
            )
            assertThat(result).isFailureOfType(InvoicePaymentDetailsNotFoundException::class)
        }
    }


    @Nested
    @DisplayName("createInvoicePayment")
    inner class CreateInvoicePayment {
        @Test
        fun `#Should create payment invoice and produce to kafka request to create external payment`() =
            runBlocking<Unit> {
                val person = TestModelFactory.buildPerson()
                val member = TestModelFactory.buildMember(personId = person.id)
                val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
                val memberInvoice =
                    TestModelFactory.buildMemberInvoice(member = member, totalAmount = BigDecimal("1200.00"))
                val memberInvoiceModel = memberInvoice.toModel()
                val invoicePayment = TestModelFactory.buildInvoicePayment(
                    memberInvoiceIds = listOf(memberInvoice.id),
                    amount = BigDecimal("1200.00"),
                    billingAccountablePartyId = billingAccountableParty.id,
                    reason = PaymentReason.REGULAR_PAYMENT,
                    source = InvoicePaymentSource.IUGU,
                )
                val invoicePaymentModel = invoicePayment.toModel()
                val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail(paymentId = invoicePayment.id)
                val invoicePaymentWithDetails = invoicePayment.withPaymentDetail(boletoPaymentDetail)
                val invoicePaymentWithDetailsModel = invoicePaymentWithDetails.toModel()
                val issuedBoleto = AcquirerCreatePaymentResponse(
                    id = RangeUUID.generate().toString(),
                    status = PaymentStatus.PENDING,
                    bankSlip = BankSlipInfo("842738974287384729734", "PENDING", "barcodeImage"),
                    externalUrl = "http://url"
                )
                val invoicePaymentWithExternalId = invoicePayment.copy(externalId = issuedBoleto.id)
                val invoicePaymentWithExternalIdModel = invoicePaymentWithExternalId.toModel()

                coEvery { memberInvoiceDataService.get(memberInvoice.id) } returns memberInvoiceModel
                coEvery {
                    invoicePaymentDataService.find(queryEq {
                        where {
                            this.memberInvoiceIds.containsAny(
                                listOf(
                                    memberInvoice.id
                                )
                            )
                        }
                    })
                } returns NotFoundException("Invoice not found")
                coEvery { invoicePaymentDataService.add(match { it.id == invoicePayment.id }) } returns invoicePaymentModel
                coEvery {
                    acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
                } returns InvoicePaymentSource.IUGU
                coEvery {
                    moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
                } returns RangeUUID.generateUUIDv7()
                coEvery { paymentDetailService.createPaymentDetail(match { it.paymentId == boletoPaymentDetail.paymentId }) } returns boletoPaymentDetail
                coEvery {
                    paymentService.issueInvoicePayment(
                        invoicePayment,
                        billingAccountableParty
                    )
                } returns issuedBoleto

                coEvery { billingAccountablePartyService.get(billingAccountableParty.id) } returns billingAccountableParty
                coEvery { invoicePaymentDataService.update(match { it.id == invoicePayment.id && it.externalId == issuedBoleto.id }) } returns invoicePaymentWithExternalIdModel
                coEvery {
                    kafkaProducerService.produce(any(), any())
                } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCreatedEvent.name, 0)


                val result = service.createInvoicePayment(invoicePaymentWithDetails, member)

                assertThat(result).isSuccessWithData(invoicePaymentWithDetails)
                coVerifyOnce { invoicePaymentDataService.add(invoicePaymentWithDetailsModel) }
                coVerifyNone { paymentDetailService.createPaymentDetail(any()) }

                coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }

                assertThatCounter("invoice_payment_creations_total").withLabels("method" to "BOLETO").hasCountOf(1)
            }


        @Test
        fun `#Should create payment invoice and pix payment details when InvoicePayment is created`() =
            runBlocking<Unit> {
                val person = TestModelFactory.buildPerson()
                val member = TestModelFactory.buildMember(personId = person.id)
                val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
                val memberInvoice = TestModelFactory.buildMemberInvoice(
                    member = member,
                    totalAmount = BigDecimal("1200.00"),
                    type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
                )
                val memberInvoiceModel = memberInvoice.toModel()
                val invoicePayment = TestModelFactory.buildInvoicePayment(
                    memberInvoiceIds = listOf(memberInvoice.id),
                    amount = BigDecimal("1200.00"),
                    billingAccountablePartyId = billingAccountableParty.id,
                    method = PaymentMethod.PIX,
                    reason = PaymentReason.FIRST_PAYMENT,
                    source = InvoicePaymentSource.IUGU,
                )
                val invoicePaymentModel = invoicePayment.toModel()

                val pixPaymentDetail = TestModelFactory.buildPixPaymentDetail(paymentId = invoicePayment.id)
                val pixPaymentDetailModel = pixPaymentDetail.toModel()
                val invoicePaymentWithDetails = invoicePayment.withPaymentDetail(pixPaymentDetail)
                val issuedPix = AcquirerCreatePaymentResponse(
                    id = RangeUUID.generate().toString(),
                    status = PaymentStatus.PENDING,
                    pix = PixInfo("842738974287384729734", "http://url", "PENDING"),
                    externalUrl = "http://url"
                )
                val invoicePaymentWithExternalId = invoicePayment.copy(externalId = issuedPix.id)
                val invoicePaymentWithExternalIdModel = invoicePaymentWithExternalId.toModel()

                coEvery { memberInvoiceDataService.get(memberInvoice.id) } returns memberInvoiceModel
                coEvery {
                    invoicePaymentDataService.find(queryEq {
                        where {
                            this.memberInvoiceIds.containsAny(
                                listOf(
                                    memberInvoice.id
                                )
                            )
                        }
                    })
                } returns NotFoundException("Invoice not found")
                coEvery {
                    invoicePaymentDataService.add(match {
                        it.id == invoicePayment.id && it.reason == PaymentReason.B2B_REGULAR_PAYMENT
                    })
                } returns invoicePaymentModel
                coEvery {
                    acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
                } returns InvoicePaymentSource.IUGU
                coEvery {
                    moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
                } returns RangeUUID.generateUUIDv7()
                coEvery { paymentDetailService.createPaymentDetail(match { it.paymentId == pixPaymentDetail.paymentId }) } returns pixPaymentDetail
                coEvery {
                    paymentService.issueInvoicePayment(
                        invoicePayment,
                        billingAccountableParty
                    )
                } returns issuedPix
                coEvery { billingAccountablePartyService.get(billingAccountableParty.id) } returns billingAccountableParty
                coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
                coEvery { invoicePaymentDataService.update(match { it.id == invoicePayment.id && it.externalId == issuedPix.id }) } returns invoicePaymentWithExternalIdModel
                coEvery {
                    kafkaProducerService.produce(any(), any())
                } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCreatedEvent.name, 0)

                val result = service.createInvoicePayment(invoicePaymentWithDetails, member)

                assertThat(result).isSuccessWithData(invoicePayment.withPaymentDetail(pixPaymentDetail))
                coVerifyOnce {
                    invoicePaymentDataService.add(
                        invoicePaymentModel.copy(reason = PaymentReason.B2B_REGULAR_PAYMENT).withPaymentDetail(
                            pixPaymentDetailModel
                        )
                    )
                }
                coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }

                assertThatCounter("invoice_payment_creations_total").withLabels("method" to "PIX").hasCountOf(1)
            }

        @Test
        fun `#Should create payment invoice and bolepix payment details when InvoicePayment is created`() =
            runBlocking<Unit> {
                val person = TestModelFactory.buildPerson()
                val member = TestModelFactory.buildMember(personId = person.id)
                val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
                val memberInvoice =
                    TestModelFactory.buildMemberInvoice(member = member, totalAmount = BigDecimal("1500.00"))
                val memberInvoiceModel = memberInvoice.toModel()
                val invoicePayment = TestModelFactory.buildInvoicePayment(
                    method = PaymentMethod.BOLEPIX,
                    memberInvoiceIds = listOf(memberInvoice.id),
                    amount = BigDecimal("1500.00"),
                    billingAccountablePartyId = billingAccountableParty.id,
                    reason = PaymentReason.REGULAR_PAYMENT,
                    source = InvoicePaymentSource.IUGU,
                )
                val invoicePaymentModel = invoicePayment.toModel()
                val bolepixPaymentDetail = TestModelFactory.buildBolepixPaymentDetail(paymentId = invoicePayment.id)
                val invoicePaymentWithDetails = invoicePayment.withPaymentDetail(bolepixPaymentDetail)
                val invoicePaymentWithDetailsModel = invoicePaymentWithDetails.toModel()
                val issuedBolepix = AcquirerCreatePaymentResponse(
                    id = RangeUUID.generate().toString(),
                    status = PaymentStatus.PENDING,
                    bankSlip = BankSlipInfo("842738974287384729734", "PENDING", "barcodeImage"),
                    pix = PixInfo("842738974287384729734", "http://url", "PENDING"),
                    externalUrl = "http://url",
                )

                val invoicePaymentWithExternalId = invoicePayment.copy(externalId = issuedBolepix.id)
                val invoicePaymentWithExternalIdModel = invoicePaymentWithExternalId.toModel()

                coEvery { memberInvoiceDataService.get(memberInvoice.id) } returns memberInvoiceModel
                coEvery {
                    invoicePaymentDataService.find(queryEq {
                        where {
                            this.memberInvoiceIds.containsAny(
                                listOf(
                                    memberInvoice.id
                                )
                            )
                        }
                    })
                } returns NotFoundException("Invoice not found")
                coEvery { invoicePaymentDataService.add(match { it.id == invoicePayment.id }) } returns invoicePaymentModel
                coEvery {
                    acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
                } returns InvoicePaymentSource.IUGU
                coEvery {
                    moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
                } returns RangeUUID.generateUUIDv7()
                coEvery { paymentDetailService.createPaymentDetail(match { it.paymentId == bolepixPaymentDetail.paymentId }) } returns bolepixPaymentDetail
                coEvery {
                    paymentService.issueInvoicePayment(
                        invoicePayment,
                        billingAccountableParty
                    )
                } returns issuedBolepix
                coEvery { billingAccountablePartyService.get(billingAccountableParty.id) } returns billingAccountableParty
                coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
                coEvery {
                    invoicePaymentDataService.update(match { it.id == invoicePayment.id && it.externalId == issuedBolepix.id })
                } returns invoicePaymentWithExternalIdModel
                coEvery {
                    kafkaProducerService.produce(any())
                } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCreatedEvent.name, 0)
                coEvery {
                    kafkaProducerService.produce(any(), any())
                } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCreatedEvent.name, 0)


                val result = service.createInvoicePayment(invoicePaymentWithDetails, member)

                assertThat(result).isSuccessWithData(invoicePaymentWithDetails)
                coVerifyOnce { invoicePaymentDataService.add(invoicePaymentWithDetailsModel) }
                coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }

                assertThatCounter("invoice_payment_creations_total").withLabels("method" to "BOLEPIX").hasCountOf(1)
            }

        @Test
        fun `#Should not create boleto details when InvoicePayment is not created`() = runBlocking<Unit> {
            val member = TestModelFactory.buildMember()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val memberInvoice =
                TestModelFactory.buildMemberInvoice(member = member, totalAmount = BigDecimal("1200.00"))
            val memberInvoiceModel = memberInvoice.toModel()
            val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail()
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                memberInvoiceIds = listOf(memberInvoice.id),
                id = boletoPaymentDetail.paymentId,
                paymentDetail = boletoPaymentDetail,
                amount = BigDecimal("1200.00"),
                billingAccountablePartyId = billingAccountableParty.id,
            )

            coEvery { memberInvoiceDataService.get(memberInvoice.id) } returns memberInvoiceModel
            coEvery {
                invoicePaymentDataService.find(queryEq {
                    where {
                        this.memberInvoiceIds.containsAny(
                            listOf(
                                memberInvoice.id
                            )
                        )
                    }
                })
            } returns NotFoundException("Invoice not found")
            coEvery {
                acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
            } returns InvoicePaymentSource.IUGU
            coEvery { billingAccountablePartyService.get(billingAccountableParty.id) } returns billingAccountableParty
            coEvery { invoicePaymentDataService.add(any()) } returns Exception()

            val result = service.createInvoicePayment(invoicePayment, member)
            assertThat(result).isFailure()

            coVerifyOnce { invoicePaymentDataService.add(any()) }
            coVerifyNone { paymentDetailService.createPaymentDetail(any()) }

            assertThatCounter("invoice_payment_creations_total").withLabels("method" to "BOLETO").hasCountOf(0)
        }

        @Test
        fun `#Should not create payment when there is already an pending payment for the same invoice`() =
            runBlocking<Unit> {
                val member = TestModelFactory.buildMember()
                val memberInvoice =
                    TestModelFactory.buildMemberInvoice(member = member, totalAmount = BigDecimal("1200.00"))
                val memberInvoiceModel = memberInvoice.toModel()
                val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail()
                val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

                val existingPayment = TestModelFactory.buildInvoicePayment(
                    memberInvoiceIds = listOf(memberInvoice.id),
                    id = boletoPaymentDetail.paymentId,
                    paymentDetail = boletoPaymentDetail,
                    method = PaymentMethod.SIMPLE_CREDIT_CARD,
                    amount = BigDecimal("1200.00"),
                    status = InvoicePaymentStatus.PENDING,
                    billingAccountablePartyId = billingAccountableParty.id,
                )
                val existingPaymentModel = existingPayment.toModel()

                coEvery { memberInvoiceDataService.get(memberInvoice.id) } returns memberInvoiceModel
                coEvery { billingAccountablePartyService.get(billingAccountableParty.id) } returns billingAccountableParty
                coEvery {
                    invoicePaymentDataService.find(queryEq {
                        where {
                            this.memberInvoiceIds.containsAny(
                                listOf(
                                    memberInvoice.id
                                )
                            )
                        }
                    })
                } returns listOf(existingPaymentModel)

                val invoicePayment = existingPayment.copy(method = PaymentMethod.BOLETO)


                val result = service.createInvoicePayment(invoicePayment, member)
                assertThat(result).isFailureOfType(PendingInvoicePaymentException::class)

                coVerifyNone { invoicePaymentDataService.add(any()) }
                coVerifyNone { paymentDetailService.createPaymentDetail(any()) }

                assertThatCounter("invoice_payment_creations_total").withLabels("method" to "BOLETO").hasCountOf(0)
            }

        @Test
        fun `#Should not create payment when member invoice is not found`() = runBlocking<Unit> {
            val member = TestModelFactory.buildMember()
            val memberInvoice = TestModelFactory.buildMemberInvoice(member = member, status = InvoiceStatus.PAID)
            val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

            val method = PaymentMethod.BOLETO
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                memberInvoiceIds = listOf(memberInvoice.id),
                id = boletoPaymentDetail.paymentId,
                paymentDetail = boletoPaymentDetail,
                method = method,
                billingAccountablePartyId = billingAccountableParty.id,
            )

            coEvery { billingAccountablePartyService.get(billingAccountableParty.id) } returns billingAccountableParty
            coEvery { memberInvoiceDataService.get(memberInvoice.id) } returns NotFoundException()


            val result = service.createInvoicePayment(invoicePayment, member)
            assertThat(result).isFailureOfType(MemberInvoiceNotFoundException::class)

            coVerifyNone { invoicePaymentDataService.add(any()) }
            coVerifyNone { paymentDetailService.createPaymentDetail(any()) }
        }

        @Test
        fun `#Should not create payment when member invoice is not OPEN`() = runBlocking<Unit> {
            val member = TestModelFactory.buildMember()
            val memberInvoice = TestModelFactory.buildMemberInvoice(member = member, status = InvoiceStatus.PAID)
            val memberInvoiceModel = memberInvoice.toModel()
            val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

            val method = PaymentMethod.BOLETO
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                memberInvoiceIds = listOf(memberInvoice.id),
                id = boletoPaymentDetail.paymentId,
                paymentDetail = boletoPaymentDetail,
                method = method,
                billingAccountablePartyId = billingAccountableParty.id,
            )
            val invoicePaymentModel = invoicePayment.toModel()

            coEvery { memberInvoiceDataService.get(memberInvoice.id) } returns memberInvoiceModel
            coEvery { billingAccountablePartyService.get(billingAccountableParty.id) } returns billingAccountableParty
            coEvery {
                invoicePaymentDataService.find(queryEq {
                    where {
                        this.memberInvoiceIds.containsAny(
                            listOf(
                                memberInvoice.id
                            )
                        )
                    }
                })
            } returns listOf(invoicePaymentModel)


            val result = service.createInvoicePayment(invoicePayment, member)
            assertThat(result).isFailureOfType(MemberInvoiceNotOpenedException::class)

            coVerifyNone { invoicePaymentDataService.add(any()) }
            coVerifyNone { paymentDetailService.createPaymentDetail(any()) }
        }

        @Test
        fun `#Should not create payment when member invoice total amount is different of invoice payment amount`() =
            runBlocking<Unit> {
                val member = TestModelFactory.buildMember()
                val memberInvoice = TestModelFactory.buildMemberInvoice(
                    member = member, status = InvoiceStatus.OPEN, totalAmount = BigDecimal("1200.00")
                )
                val memberInvoiceModel = memberInvoice.toModel()
                val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail()
                val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

                val method = PaymentMethod.BOLETO
                val invoicePayment = TestModelFactory.buildInvoicePayment(
                    memberInvoiceIds = listOf(memberInvoice.id),
                    id = boletoPaymentDetail.paymentId,
                    paymentDetail = boletoPaymentDetail,
                    method = method,
                    amount = BigDecimal(1200.0),
                    billingAccountablePartyId = billingAccountableParty.id,
                )
                val invoicePaymentModel = invoicePayment.toModel()

                coEvery { memberInvoiceDataService.get(memberInvoice.id) } returns memberInvoiceModel
                coEvery { billingAccountablePartyService.get(billingAccountableParty.id) } returns billingAccountableParty
                coEvery {
                    invoicePaymentDataService.find(queryEq {
                        where {
                            this.memberInvoiceIds.containsAny(
                                listOf(
                                    memberInvoice.id
                                )
                            )
                        }
                    })
                } returns listOf(invoicePaymentModel)


                val result = service.createInvoicePayment(invoicePayment, member)
                assertThat(result).isFailureOfType(InvoicePaymentAmountNotAllowed::class)

                coVerifyNone { invoicePaymentDataService.add(any()) }
                coVerifyNone { paymentDetailService.createPaymentDetail(any()) }

            }

        @Test
        fun `#getPendingInvoicePayment should return NotFoundException when there is no other pending Payment`() =
            runBlocking {
                val invoiceId = RangeUUID.generate()
                val paymentMethod = PaymentMethod.BOLETO

                val payment = TestModelFactory.buildInvoicePayment(
                    status = InvoicePaymentStatus.PENDING, method = PaymentMethod.SIMPLE_CREDIT_CARD
                )
                val paymentModel = payment.toModel()

                coEvery {
                    invoicePaymentDataService.find(queryEq {
                        where {
                            this.memberInvoiceIds.containsAny(
                                listOf(
                                    invoiceId
                                )
                            )
                        }
                    })
                } returns listOf(paymentModel)


                val result = service.getPendingInvoicePayment(
                    invoiceId, paymentMethod, withPaymentDetails = false
                )
                assertThat(result).isFailureOfType(NotFoundException::class)
            }


        @Test
        fun `#getPendingInvoicePayment should return expected payment when there is another pending Payment`() =
            runBlocking {
                val invoiceId = RangeUUID.generate()
                val paymentMethod = PaymentMethod.BOLETO

                val payment = TestModelFactory.buildInvoicePayment(
                    status = InvoicePaymentStatus.PENDING, method = PaymentMethod.BOLETO
                )
                val paymentModel = payment.toModel()

                coEvery {
                    invoicePaymentDataService.find(queryEq {
                        where {
                            this.memberInvoiceIds.containsAny(
                                listOf(
                                    invoiceId
                                )
                            )
                        }
                    })
                } returns listOf(paymentModel)


                val result = service.getPendingInvoicePayment(
                    invoiceId, paymentMethod, withPaymentDetails = false
                )
                assertThat(result).isSuccessWithData(payment)
            }
    }

    @Test
    fun `#createInvoicePaymentForMemberInvoices should just return already created pending payment`() = runBlocking {
        val firstInvoice = TestModelFactory.buildMemberInvoice(
            status = InvoiceStatus.OPEN, totalAmount = BigDecimal.valueOf(350).money
        )
        val secondInvoice = TestModelFactory.buildMemberInvoice(
            status = InvoiceStatus.OPEN, totalAmount = BigDecimal.valueOf(500).money
        )
        val memberInvoices = listOf(firstInvoice, secondInvoice)
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val invoicePaymentModel = invoicePayment.toModel()
        val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail()
        val dueDate = LocalDateTime.now().plusDays(1)
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        coEvery {
            invoicePaymentDataService.findOneOrNull(queryEq {
                where {
                    this.memberInvoiceIds.containsAny(memberInvoices.map { it.id })
                        .and(this.status.eq(InvoicePaymentStatus.PENDING))
                }
            })
        } returns invoicePaymentModel

        coEvery { paymentDetailService.getPaymentDetail(invoicePayment) } returns boletoPaymentDetail


        val result = service.createInvoicePaymentForMemberInvoices(
            InvoicePaymentService.CreateInvoicePaymentForMemberInvoicesPayload(
                PaymentMethod.BOLETO, memberInvoices, dueDate, billingAccountableParty
            )
        )
        assertThat(result).isSuccessWithData(invoicePayment.withPaymentDetail(boletoPaymentDetail))
    }

    @Test
    fun `#createInvoicePaymentForMemberInvoices should create payment given a list of invoices`() = runBlocking {
        val firstPerson = TestModelFactory.buildPerson()
        val secondPerson = TestModelFactory.buildPerson()
        val firstInvoice = TestModelFactory.buildMemberInvoice(
            status = InvoiceStatus.OPEN, totalAmount = BigDecimal.valueOf(350).money, personId = firstPerson.id
        )
        val secondInvoice = TestModelFactory.buildMemberInvoice(
            status = InvoiceStatus.OPEN, totalAmount = BigDecimal.valueOf(500).money, personId = secondPerson.id
        )
        val memberInvoices = listOf(firstInvoice, secondInvoice)
        val dueDate = LocalDateTime.now().plusDays(1)
        val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail(dueDate = dueDate)
        val invoicePayment = TestModelFactory.buildInvoicePayment(
            paymentDetail = boletoPaymentDetail,
            memberInvoiceIds = memberInvoices.map { it.id },
            origin = InvoicePaymentOrigin.ISSUED_BY_MEMBER
        )
        val invoicePaymentModel = invoicePayment.toModel()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        coEvery {
            invoicePaymentDataService.findOneOrNull(queryEq {
                where {
                    this.memberInvoiceIds.containsAny(memberInvoices.map { it.id })
                        .and(this.status.eq(InvoicePaymentStatus.PENDING))
                }
            })
        } returns null

        coEvery {
            invoicePaymentDataService.add(match {
                it.isBoleto && it.isPending && it.amount == BigDecimal.valueOf(
                    850
                ).money
            })
        } returns invoicePaymentModel
        coEvery {
            acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
        } returns InvoicePaymentSource.IUGU
        coEvery {
            moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
        } returns RangeUUID.generateUUIDv7()
        coEvery {
            kafkaProducerService.produce(match {
                it.payload == InvoicePaymentCreatedEventPayload(
                    invoicePayment, null, PaymentReason.REGULAR_PAYMENT
                ) && it.name == InvoicePaymentCreatedEvent.name
            })
        } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCreatedEvent.name, 0)
        coEvery {
            kafkaProducerService.produce(
                match { it: CreateExternalPaymentRequestEvent -> it.payload.invoicePayment.id == invoicePayment.id },
                billingAccountableParty.id.toString()
            )
        } returns mockk()


        val result = service.createInvoicePaymentForMemberInvoices(
            InvoicePaymentService.CreateInvoicePaymentForMemberInvoicesPayload(
                PaymentMethod.BOLETO,
                memberInvoices,
                dueDate,
                billingAccountableParty,
                origin = InvoicePaymentOrigin.ISSUED_BY_MEMBER,
            )
        )
        assertThat(result).isSuccessWithDataIgnoringGivenFields(
            invoicePayment, "paymentDetailString"
        )
    }

    @Test
    fun `#createInvoicePaymentForMemberInvoices should create payment and get the membership when the reason is first payment`() =
        runBlocking {
            val firstPerson = TestModelFactory.buildPerson()
            val member = TestModelFactory.buildMember(personId = firstPerson.id)
            val firstInvoice = TestModelFactory.buildMemberInvoice(
                status = InvoiceStatus.OPEN,
                totalAmount = BigDecimal.valueOf(350).money,
                personId = firstPerson.id,
                memberId = member.id,
            )
            val memberInvoices = listOf(firstInvoice)
            val dueDate = LocalDateTime.now().plusDays(1)
            val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail(dueDate = dueDate)
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                paymentDetail = boletoPaymentDetail,
                memberInvoiceIds = memberInvoices.map { it.id })
            val invoicePaymentModel = invoicePayment.toModel()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

            coEvery {
                invoicePaymentDataService.findOneOrNull(queryEq {
                    where {
                        this.memberInvoiceIds.containsAny(memberInvoices.map { it.id })
                            .and(this.status.eq(InvoicePaymentStatus.PENDING))
                    }
                })
            } returns null

            coEvery { memberService.get(member.id) } returns member

            coEvery {
                invoicePaymentDataService.add(match {
                    it.isBoleto && it.isPending && it.amount == BigDecimal.valueOf(
                        350
                    ).money
                })
            } returns invoicePaymentModel

            coEvery {
                acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
            } returns InvoicePaymentSource.IUGU

            coEvery {
                moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePaymentModel.id)
            } returns RangeUUID.generateUUIDv7()

            coEvery {
                kafkaProducerService.produce(match {
                    it.payload == InvoicePaymentCreatedEventPayload(
                        invoicePayment,
                        member,
                        PaymentReason.FIRST_PAYMENT,
                    ) && it.name == InvoicePaymentCreatedEvent.name
                })
            } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCreatedEvent.name, 0)
            coEvery {
                kafkaProducerService.produce(
                    match { it: CreateExternalPaymentRequestEvent -> it.payload.invoicePayment.id == invoicePayment.id },
                    billingAccountableParty.id.toString()
                )
            } returns mockk()


            val result = service.createInvoicePaymentForMemberInvoices(
                InvoicePaymentService.CreateInvoicePaymentForMemberInvoicesPayload(
                    PaymentMethod.BOLETO,
                    memberInvoices,
                    dueDate,
                    billingAccountableParty,
                    PaymentReason.FIRST_PAYMENT,
                )
            )
            assertThat(result).isSuccessWithDataIgnoringGivenFields(
                invoicePayment, "paymentDetailString"
            )
        }

    @Test
    fun `#createInvoicePaymentForInvoiceGroup should just return already created pending payment`() = runBlocking {
        val firstInvoice = TestModelFactory.buildMemberInvoice(
            status = InvoiceStatus.OPEN, totalAmount = BigDecimal.valueOf(350).money
        )
        val secondInvoice = TestModelFactory.buildMemberInvoice(
            status = InvoiceStatus.OPEN, totalAmount = BigDecimal.valueOf(500).money
        )
        val memberInvoices = listOf(firstInvoice, secondInvoice)
        val memberInvoiceModels = memberInvoices.map { it.toModel() }
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val invoicePaymentModel = invoicePayment.toModel()
        val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
            memberInvoiceIds = memberInvoices.map { it.id },
        )

        coEvery {
            invoicePaymentDataService.findOneOrNull(queryEq {
                where {
                    this.memberInvoiceIds.containsAny(memberInvoices.map { it.id })
                        .and(this.status.eq(InvoicePaymentStatus.PENDING))
                }
            })
        } returns invoicePaymentModel

        coEvery { paymentDetailService.getPaymentDetail(invoicePayment) } returns boletoPaymentDetail
        coEvery {
            memberInvoiceDataService.find(queryEq { where { this.id.inList(memberInvoiceGroup.memberInvoiceIds) } })
        } returns memberInvoiceModels
        coEvery {
            billingAccountablePartyService.get(memberInvoiceGroup.billingAccountablePartyId)
        } returns billingAccountableParty

        val result = service.createInvoicePaymentForInvoiceGroup(
            memberInvoiceGroup, PaymentMethod.BOLETO
        )
        assertThat(result).isSuccessWithData(
            invoicePayment.withPaymentDetail(
                boletoPaymentDetail
            )
        )
    }

    @Test
    fun `#createInvoicePaymentForInvoiceGroup should just return already created pending payment finding by member invoice group`() =
        runBlocking {
            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                memberInvoiceIds = emptyList(),
            )

            val firstInvoice = TestModelFactory.buildMemberInvoice(
                status = InvoiceStatus.OPEN,
                totalAmount = BigDecimal.valueOf(350).money,
                memberInvoiceGroupId = memberInvoiceGroup.id
            )
            val secondInvoice = TestModelFactory.buildMemberInvoice(
                status = InvoiceStatus.OPEN,
                totalAmount = BigDecimal.valueOf(500).money,
                memberInvoiceGroupId = memberInvoiceGroup.id
            )
            val memberInvoices = listOf(firstInvoice, secondInvoice)
            val memberInvoiceModels = memberInvoices.map { it.toModel() }
            val invoicePayment = TestModelFactory.buildInvoicePayment()
            val invoicePaymentModel = invoicePayment.toModel()
            val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

            val memberInvoiceGroupUpdated = memberInvoiceGroup.copy(
                memberInvoiceIds = memberInvoices.map { it.id },
            )
            val memberInvoiceGroupUpdatedModel = memberInvoiceGroupUpdated.toModel()

            coEvery {
                invoicePaymentDataService.findOneOrNull(queryEq {
                    where {
                        this.memberInvoiceIds.containsAny(memberInvoices.map { it.id })
                            .and(this.status.eq(InvoicePaymentStatus.PENDING))
                    }
                })
            } returns invoicePaymentModel

            coEvery { paymentDetailService.getPaymentDetail(invoicePayment) } returns boletoPaymentDetail
            coEvery {
                memberInvoiceDataService.find(queryEq {
                    where {
                        this.memberInvoiceGroupId.eq(
                            memberInvoiceGroup.id
                        )
                    }
                })
            } returns memberInvoiceModels

            coEvery {
                memberInvoiceGroupDataService.update(memberInvoiceGroupUpdatedModel)
            } returns memberInvoiceGroupUpdatedModel
            coEvery {
                billingAccountablePartyService.get(memberInvoiceGroup.billingAccountablePartyId)
            } returns billingAccountableParty

            val result = service.createInvoicePaymentForInvoiceGroup(
                memberInvoiceGroup, PaymentMethod.BOLETO
            )
            assertThat(result).isSuccessWithData(
                invoicePayment.withPaymentDetail(
                    boletoPaymentDetail
                )
            )
        }

    @Test
    fun `#createInvoicePaymentForInvoiceGroup should just return already created pending payment and create external payment info sync`() =
        runBlocking {
            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                memberInvoiceIds = emptyList(),
                totalAmount = 1000.50.money
            )

            val firstInvoice = TestModelFactory.buildMemberInvoice(
                status = InvoiceStatus.OPEN,
                totalAmount = BigDecimal.valueOf(350).money,
                memberInvoiceGroupId = memberInvoiceGroup.id
            )
            val secondInvoice = TestModelFactory.buildMemberInvoice(
                status = InvoiceStatus.OPEN,
                totalAmount = BigDecimal.valueOf(500).money,
                memberInvoiceGroupId = memberInvoiceGroup.id
            )
            val memberInvoices = listOf(firstInvoice, secondInvoice)
            val memberInvoiceModels = memberInvoices.map { it.toModel() }
            val invoicePayment = TestModelFactory.buildInvoicePayment()
            val invoicePaymentModel = invoicePayment.toModel()
            val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val memberInvoiceGroupUpdated = memberInvoiceGroup.copy(
                memberInvoiceIds = memberInvoices.map { it.id },
            )
            val memberInvoiceGroupUpdatedModel = memberInvoiceGroupUpdated.toModel()
            val invoicePaymentWithSourceIugu = invoicePayment.copy(source = InvoicePaymentSource.IUGU)

            coEvery {
                memberInvoiceDataService.find(queryEq {
                    where {
                        this.memberInvoiceGroupId.eq(
                            memberInvoiceGroup.id
                        )
                    }
                })
            } returns memberInvoiceModels

            coEvery {
                memberInvoiceGroupDataService.update(memberInvoiceGroupUpdatedModel)
            } returns memberInvoiceGroupUpdatedModel

            coEvery {
                billingAccountablePartyService.get(memberInvoiceGroup.billingAccountablePartyId)
            } returns billingAccountableParty
            coEvery {
                invoicePaymentDataService.findOneOrNull(queryEq {
                    where {
                        this.memberInvoiceIds.containsAny(memberInvoices.map { it.id })
                            .and(this.status.eq(InvoicePaymentStatus.PENDING))
                    }
                })
            } returns null

            coEvery {
                invoicePaymentDataService.add(match {
                    it.isBoleto && it.isPending && it.amount == BigDecimal.valueOf(
                        1000.50
                    ).money
                })
            } returns invoicePaymentModel

            coEvery {
                acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
            } returns InvoicePaymentSource.IUGU

            coEvery {
                moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
            } returns RangeUUID.generateUUIDv7()

            coEvery {
                kafkaProducerService.produce(match {
                    it.payload == InvoicePaymentCreatedEventPayload(
                        invoicePayment,
                        null,
                        PaymentReason.B2B_REGULAR_PAYMENT
                    ) && it.name == InvoicePaymentCreatedEvent.name
                })
            } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCreatedEvent.name, 0)

            coEvery {
                serviceSpy.createExternalPaymentForInvoicePayment(
                    match {
                        it.id == invoicePayment.id
                    },
                    billingAccountableParty,
                    null
                )
            } returns invoicePaymentWithSourceIugu.withPaymentDetail(boletoPaymentDetail)

            val result = serviceSpy.createInvoicePaymentForInvoiceGroup(
                memberInvoiceGroup, PaymentMethod.BOLETO, syncProcess = true
            )

            assertThat(result).isSuccessWithData(
                invoicePaymentWithSourceIugu.withPaymentDetail(
                    boletoPaymentDetail
                )
            )

            coVerifyOnce { memberInvoiceDataService.find(any()) }
            coVerifyOnce { memberInvoiceGroupDataService.update(any()) }
            coVerifyOnce { billingAccountablePartyService.get(any()) }
            coVerifyOnce {
                invoicePaymentDataService.findOneOrNull(any())
            }
            coVerifyOnce { invoicePaymentDataService.add(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
            coVerifyOnce { serviceSpy.createExternalPaymentForInvoicePayment(any(), any(), any()) }

        }

    @Nested
    inner class CreateFromMemberInvoiceGroup {
        @Test
        fun `#should request creation of external payment from member invoice group`() = runBlocking {
            val dueDate = LocalDateTime.now().plusDays(1)

            val bolepixPaymentDetail = TestModelFactory.buildBolepixPaymentDetail(dueDate = dueDate)
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                totalAmount = BigDecimal.valueOf(350).money,
                billingAccountablePartyId = billingAccountableParty.id,
                dueDate = dueDate.toLocalDate(),
            )
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                paymentDetail = bolepixPaymentDetail,
                invoiceGroupId = memberInvoiceGroup.id,
                method = PaymentMethod.BOLEPIX,
            )
            val invoicePaymentModel = invoicePayment.toModel()

            coEvery {
                billingAccountablePartyService.get(billingAccountableParty.id)
            } returns billingAccountableParty

            coEvery {
                invoicePaymentDataService.add(match {
                    it.isBolepix && it.isPending && it.amount == 350.money && it.invoiceGroupId == memberInvoiceGroup.id
                })
            } returns invoicePaymentModel

            coEvery {
                acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
            } returns InvoicePaymentSource.IUGU

            coEvery {
                moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
            } returns RangeUUID.generateUUIDv7()

            coEvery {
                kafkaProducerService.produce(match {
                    it.payload == InvoicePaymentCreatedEventPayload(
                        invoicePayment,
                        null,
                        PaymentReason.B2B_REGULAR_PAYMENT,
                    ) && it.name == InvoicePaymentCreatedEvent.name
                })
            } returns ProducerResult(
                LocalDateTime.now(), InvoicePaymentCreatedEvent.name, 0
            )
            coEvery {
                kafkaProducerService.produce(
                    match { it: CreateExternalPaymentRequestEvent -> it.payload.invoicePayment.id == invoicePayment.id },
                    billingAccountableParty.id.toString()
                )
            } returns mockk()


            val result = service.createFromMemberInvoiceGroup(
                memberInvoiceGroup,
                PaymentMethod.BOLEPIX,
                reason = PaymentReason.B2B_REGULAR_PAYMENT,
                origin = InvoicePaymentOrigin.ISSUED_BY_NULLVS,
                dueDate = dueDate.toLocalDate(),
                sendEmail = true,
            )
            assertThat(result).isSuccessWithDataIgnoringGivenFields(
                invoicePayment, "paymentDetailString"
            )
        }

        @Test
        fun `#should create external payment from member invoice group`() = runBlocking {
            val dueDate = LocalDateTime.now().plusDays(1)
            val bolepixPaymentDetail = TestModelFactory.buildBolepixPaymentDetail(dueDate = dueDate)
            val billingAccountableParty =
                TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.LEGAL_PERSON)
            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                totalAmount = BigDecimal.valueOf(350).money,
                billingAccountablePartyId = billingAccountableParty.id,
                dueDate = dueDate.toLocalDate(),
            )
            val memberInvoice1 = TestModelFactory.buildMemberInvoice(
                status = InvoiceStatus.OPEN,
                totalAmount = BigDecimal.valueOf(350).money,
                memberInvoiceGroupId = memberInvoiceGroup.id
            )
            val memberInvoice2 = TestModelFactory.buildMemberInvoice(
                status = InvoiceStatus.OPEN,
                totalAmount = BigDecimal.valueOf(500).money,
                memberInvoiceGroupId = memberInvoiceGroup.id
            )
            val memberInvoices = listOf(memberInvoice1, memberInvoice2)
            val memberInvoiceModels = memberInvoices.map { it.toModel() }
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                paymentDetail = bolepixPaymentDetail,
                invoiceGroupId = memberInvoiceGroup.id,
                method = PaymentMethod.BOLEPIX,
                memberInvoiceIds = emptyList(),
                billingAccountablePartyId = billingAccountableParty.id,
                amount = 350.money,
            )

            val invoicePaymentModel = invoicePayment.toModel()
            val issuedBolepix = AcquirerCreatePaymentResponse(
                id = RangeUUID.generate().toString(),
                status = PaymentStatus.PENDING,
                bankSlip = BankSlipInfo("842738974287384729734", "PENDING", "barcodeImage"),
                pix = PixInfo("842738974287384729734", "http://url", "PENDING"),
                externalUrl = "http://url",
            )
            coEvery {
                billingAccountablePartyService.get(billingAccountableParty.id)
            } returns billingAccountableParty

            coEvery {
                invoicePaymentDataService.add(match {
                    it.isBolepix && it.isPending && it.amount == 350.money && it.invoiceGroupId == memberInvoiceGroup.id
                })
            } returns invoicePaymentModel

            coEvery {
                acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
            } returns InvoicePaymentSource.IUGU

            coEvery {
                moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
            } returns RangeUUID.generateUUIDv7()

            coEvery {
                memberInvoiceDataService.find(queryEq {
                    where {
                        this.id.inList(
                            listOf(memberInvoice1.id, memberInvoice2.id)
                        )
                    }
                })
            } returns memberInvoiceModels

            coEvery {
                kafkaProducerService.produce(match { it is InvoicePaymentCreatedEvent })
            } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCreatedEvent.name, 0)

            coEvery { memberInvoiceGroupDataService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroup.toModel()

            coEvery {
                paymentService.issueInvoicePayment(match {
                    it.isBolepix && it.isPending && it.amount == 350.money && it.invoiceGroupId == memberInvoiceGroup.id
                }, billingAccountableParty)
            } returns issuedBolepix
            coEvery { invoicePaymentDataService.get(any()) } returns invoicePaymentModel
            coEvery { invoicePaymentDataService.update(any()) } returns invoicePaymentModel
            coEvery {
                kafkaProducerService.produce(match { it is PaymentDetailCreatedEvent }, any())
            } returns ProducerResult(LocalDateTime.now(), PaymentDetailCreatedEvent.name, 0)
            coEvery {
                kafkaProducerService.produce(match { it is ExternalInvoiceCreatedEvent })
            } returns ProducerResult(LocalDateTime.now(), ExternalInvoiceCreatedEvent.name, 0)

            val result = service.createFromMemberInvoiceGroup(
                memberInvoiceGroup,
                PaymentMethod.BOLEPIX,
                reason = PaymentReason.B2B_REGULAR_PAYMENT,
                origin = InvoicePaymentOrigin.ISSUED_BY_NULLVS,
                dueDate = dueDate.toLocalDate(),
                sendEmail = true,
                syncProcess = true
            )
            assertThat(result).isSuccessWithDataIgnoringGivenFields(
                invoicePayment, "paymentDetailString"
            )
        }

        @Test
        fun `#should not create the payment when the member invoice group total amount is null`() = runBlocking {
            val dueDate = LocalDateTime.now().plusDays(1)

            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                dueDate = dueDate.toLocalDate(),
            ).copy(totalAmount = null)


            val result = service.createFromMemberInvoiceGroup(
                memberInvoiceGroup,
                PaymentMethod.BOLEPIX,
                reason = PaymentReason.B2B_REGULAR_PAYMENT,
                origin = InvoicePaymentOrigin.ISSUED_BY_NULLVS,
                dueDate = dueDate.toLocalDate(),
                sendEmail = true,

                )
            assertThat(result).isFailureOfType(InvalidAmountException::class)
        }

        @Test
        fun `#should fetch the payment when it already exists`() = runBlocking {
            val dueDate = LocalDateTime.now().plusDays(1)

            val bolepixPaymentDetail = TestModelFactory.buildBolepixPaymentDetail(dueDate = dueDate)
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                totalAmount = BigDecimal.valueOf(350).money,
                billingAccountablePartyId = billingAccountableParty.id,
                dueDate = dueDate.toLocalDate(),
            )
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                paymentDetail = bolepixPaymentDetail,
                invoiceGroupId = memberInvoiceGroup.id,
                method = PaymentMethod.BOLEPIX,
            )
            val invoicePaymentModel = invoicePayment.toModel()

            coEvery {
                billingAccountablePartyService.get(billingAccountableParty.id)
            } returns billingAccountableParty

            coEvery {
                invoicePaymentDataService.add(match {
                    it.isBolepix && it.isPending && it.amount == 350.money && it.invoiceGroupId == memberInvoiceGroup.id
                })
            } returns DuplicatedItemException("Already exists")

            coEvery {
                acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
            } returns InvoicePaymentSource.IUGU

            coEvery {
                invoicePaymentDataService.find(queryEq {
                    where {
                        this.invoiceGroupId.inList(
                            listOf(
                                memberInvoiceGroup.id
                            )
                        )
                    }
                })
            } returns listOf(invoicePaymentModel)

            coEvery {
                paymentDetailService.getPaymentDetails(
                    listOf(invoicePayment.id), PaymentMethod.BOLEPIX
                )
            } returns listOf(
                bolepixPaymentDetail,
            )

            coEvery {
                paymentDetailService.getPaymentDetails(
                    listOf(invoicePayment.id), PaymentMethod.BOLETO
                )
            } returns emptyList()

            coEvery {
                paymentDetailService.getPaymentDetails(
                    listOf(invoicePayment.id), PaymentMethod.PIX
                )
            } returns emptyList()

            coEvery {
                paymentDetailService.getPaymentDetails(
                    listOf(invoicePayment.id), PaymentMethod.SIMPLE_CREDIT_CARD
                )
            } returns emptyList()


            val result = service.createFromMemberInvoiceGroup(
                memberInvoiceGroup,
                PaymentMethod.BOLEPIX,
                reason = PaymentReason.B2B_REGULAR_PAYMENT,
                origin = InvoicePaymentOrigin.ISSUED_BY_NULLVS,
                dueDate = dueDate.toLocalDate(),
                sendEmail = true,
            )
            assertThat(result).isSuccessWithDataIgnoringGivenFields(
                invoicePayment, "paymentDetailString"
            )
        }
    }


    @Nested
    inner class CreateFromPreActivationPayment {
        @Test
        fun `#should request creation of external payment from pre activation payment`() = runBlocking {
            val dueDate = LocalDateTime.now().plusDays(1)

            val bolepixPaymentDetail = TestModelFactory.buildBolepixPaymentDetail(dueDate = dueDate)
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val preActivationPayment = TestModelFactory.buildPreActivationPayment(
                totalAmount = BigDecimal.valueOf(350).money,
                billingAccountablePartyId = billingAccountableParty.id,
                dueDate = dueDate.toLocalDate(),
            )
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                paymentDetail = bolepixPaymentDetail,
                preActivationPaymentId = preActivationPayment.id,
                method = PaymentMethod.BOLEPIX,
                reason = PaymentReason.B2B_PRE_ACTIVATION_PAYMENT,
            )
            val invoicePaymentModel = invoicePayment.toModel()

            coEvery {
                billingAccountablePartyService.get(billingAccountableParty.id)
            } returns billingAccountableParty

            coEvery {
                invoicePaymentDataService.add(match {
                    it.isBolepix && it.isPending && it.amount == 350.money && it.preActivationPaymentId == preActivationPayment.id
                })
            } returns invoicePaymentModel

            coEvery {
                acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
            } returns InvoicePaymentSource.IUGU

            coEvery {
                moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
            } returns RangeUUID.generateUUIDv7()

            coEvery {
                kafkaProducerService.produce(match {
                    it.payload == InvoicePaymentCreatedEventPayload(
                        invoicePayment,
                        null,
                        PaymentReason.B2B_PRE_ACTIVATION_PAYMENT,
                    ) && it.name == InvoicePaymentCreatedEvent.name
                })
            } returns ProducerResult(
                LocalDateTime.now(), InvoicePaymentCreatedEvent.name, 0
            )
            coEvery {
                kafkaProducerService.produce(
                    match { it: CreateExternalPaymentRequestEvent -> it.payload.invoicePayment.id == invoicePayment.id },
                    billingAccountableParty.id.toString()
                )
            } returns mockk()


            val result = service.createFromPreActivationPayment(
                preActivationPayment,
                PaymentMethod.BOLEPIX,
                origin = InvoicePaymentOrigin.ISSUED_BY_NULLVS,
                dueDate = dueDate.toLocalDate(),
                sendEmail = true,
            )

            assertThat(result).isSuccessWithDataIgnoringGivenFields(
                invoicePayment, "paymentDetailString"
            )
        }

        @Test
        fun `#should create external payment from pre activation payment`() = runBlocking {
            val dueDate = LocalDateTime.now().plusDays(1)
            val boletoDetail = TestModelFactory.buildBolepixPaymentDetail(dueDate = dueDate)
            val billingAccountableParty =
                TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.LEGAL_PERSON)

            val preActivationPayment = TestModelFactory.buildPreActivationPayment(
                totalAmount = BigDecimal.valueOf(350).money,
                billingAccountablePartyId = billingAccountableParty.id,
                dueDate = dueDate.toLocalDate(),
            )

            val invoicePayment = TestModelFactory.buildInvoicePayment(
                paymentDetail = boletoDetail,
                preActivationPaymentId = preActivationPayment.id,
                invoiceGroupId = null,
                method = PaymentMethod.BOLETO,
                amount = preActivationPayment.totalAmount,
                memberInvoiceIds = emptyList(),
                billingAccountablePartyId = billingAccountableParty.id,
                reason = PaymentReason.B2B_PRE_ACTIVATION_PAYMENT,
                sendEmail = true,
                origin = InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS
            )

            val invoicePaymentModel = invoicePayment.toModel()
            val issuedBoleto = AcquirerCreatePaymentResponse(
                id = RangeUUID.generate().toString(),
                status = PaymentStatus.PENDING,
                bankSlip = BankSlipInfo("842738974287384729734", "PENDING", "barcodeImage"),
                externalUrl = "http://url"
            )

            coEvery {
                billingAccountablePartyService.get(billingAccountableParty.id)
            } returns billingAccountableParty

            coEvery {
                invoicePaymentDataService.add(match {
                    it.isBoleto &&
                            it.isPending &&
                            it.amount == 350.money &&
                            it.preActivationPaymentId == preActivationPayment.id &&
                            it.reason == PaymentReason.B2B_PRE_ACTIVATION_PAYMENT
                })
            } returns invoicePaymentModel

            coEvery {
                acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
            } returns InvoicePaymentSource.IUGU

            coEvery {
                moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
            } returns RangeUUID.generateUUIDv7()

            coEvery {
                kafkaProducerService.produce(match { it is InvoicePaymentCreatedEvent })
            } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCreatedEvent.name, 0)

            coEvery {
                paymentService.issueInvoicePayment(
                    match { it.id == invoicePayment.id && it.amount == invoicePayment.amount }, billingAccountableParty
                )
            } returns issuedBoleto

            coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
            coEvery { invoicePaymentDataService.update(match { it.externalId == issuedBoleto.id }) } returns invoicePaymentModel
            coEvery {
                kafkaProducerService.produce(match { it is PaymentDetailCreatedEvent }, any())
            } returns ProducerResult(LocalDateTime.now(), PaymentDetailCreatedEvent.name, 0)

            coEvery {
                kafkaProducerService.produce(match { it is ExternalInvoiceCreatedEvent })
            } returns ProducerResult(LocalDateTime.now(), ExternalInvoiceCreatedEvent.name, 0)

            val result = service.createFromPreActivationPayment(
                preActivationPayment,
                PaymentMethod.BOLETO,
                origin = InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS,
                dueDate = dueDate.toLocalDate(),
                sendEmail = true,
                syncProcess = true
            )

            assertThat(result).isSuccessWithDataIgnoringGivenFields(
                invoicePayment, "paymentDetailString"
            )

            coVerifyOnce {
                billingAccountablePartyService.get(billingAccountableParty.id)
                invoicePaymentDataService.add(match {
                    it.isBoleto &&
                            it.isPending &&
                            it.amount == 350.money &&
                            it.preActivationPaymentId == preActivationPayment.id &&
                            it.reason == PaymentReason.B2B_PRE_ACTIVATION_PAYMENT
                })
                paymentService.issueInvoicePayment(
                    match { it.id == invoicePayment.id && it.amount == invoicePayment.amount }, billingAccountableParty
                )
                invoicePaymentDataService.get(invoicePayment.id)
                invoicePaymentDataService.update(match { it.externalId == issuedBoleto.id })
                kafkaProducerService.produce(match { it is PaymentDetailCreatedEvent }, any())
                kafkaProducerService.produce(match { it is ExternalInvoiceCreatedEvent })
            }
            coVerifyNone { invoicePaymentDataService.find(any()) }
        }

        @Test
        fun `#should not create the payment when the pre activation total amount is smaller than zero`() = runBlocking {
            val dueDate = LocalDateTime.now().plusDays(1)

            val preActivationPayment = TestModelFactory.buildPreActivationPayment(
                totalAmount = (-1).money,
                dueDate = dueDate.toLocalDate(),
            )


            val result = service.createFromPreActivationPayment(
                preActivationPayment,
                PaymentMethod.BOLETO,
                origin = InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS,
                dueDate = dueDate.toLocalDate(),
                sendEmail = true,
                syncProcess = true
            )

            assertThat(result).isFailureOfType(InvalidAmountException::class)

            coVerifyNone {
                billingAccountablePartyService.get(any())
                invoicePaymentDataService.add(any())
                paymentService.issueInvoicePayment(any(), any())
                invoicePaymentDataService.get(any())
                invoicePaymentDataService.update(any())
                kafkaProducerService.produce(any(), any())
                kafkaProducerService.produce(any())
            }
        }

        @Test
        fun `#should fetch the payment when it already exists`() = runBlocking {
            val dueDate = LocalDateTime.now().plusDays(1)
            val boletoDetail = TestModelFactory.buildBolepixPaymentDetail(dueDate = dueDate)
            val billingAccountableParty =
                TestModelFactory.buildBillingAccountableParty(type = BillingAccountablePartyType.LEGAL_PERSON)

            val preActivationPayment = TestModelFactory.buildPreActivationPayment(
                totalAmount = BigDecimal.valueOf(350).money,
                billingAccountablePartyId = billingAccountableParty.id,
                dueDate = dueDate.toLocalDate(),
            )

            val invoicePayment = TestModelFactory.buildInvoicePayment(
                paymentDetail = boletoDetail,
                preActivationPaymentId = preActivationPayment.id,
                invoiceGroupId = null,
                method = PaymentMethod.BOLETO,
                amount = preActivationPayment.totalAmount,
                memberInvoiceIds = emptyList(),
                billingAccountablePartyId = billingAccountableParty.id,
                reason = PaymentReason.B2B_PRE_ACTIVATION_PAYMENT,
                sendEmail = true,
                origin = InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS
            )

            val invoicePaymentModel = invoicePayment.toModel()

            coEvery {
                billingAccountablePartyService.get(billingAccountableParty.id)
            } returns billingAccountableParty

            coEvery {
                invoicePaymentDataService.add(match {
                    it.isBoleto &&
                            it.isPending &&
                            it.amount == 350.money &&
                            it.preActivationPaymentId == preActivationPayment.id &&
                            it.reason == PaymentReason.B2B_PRE_ACTIVATION_PAYMENT
                })
            } returns DuplicatedItemException("Already exists")

            coEvery {
                acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
            } returns InvoicePaymentSource.IUGU

            coEvery {
                invoicePaymentDataService.find(queryEq {
                    where {
                        this.preActivationPaymentId.inList(
                            listOf(
                                preActivationPayment.id
                            )
                        )
                    }
                })
            } returns listOf(invoicePaymentModel)

            coEvery {
                paymentDetailService.getPaymentDetails(
                    listOf(invoicePayment.id), PaymentMethod.BOLEPIX
                )
            } returns emptyList()

            coEvery {
                paymentDetailService.getPaymentDetails(
                    listOf(invoicePayment.id), PaymentMethod.BOLETO
                )
            } returns listOf(boletoDetail)

            coEvery {
                paymentDetailService.getPaymentDetails(
                    listOf(invoicePayment.id), PaymentMethod.PIX
                )
            } returns emptyList()

            coEvery {
                paymentDetailService.getPaymentDetails(
                    listOf(invoicePayment.id), PaymentMethod.SIMPLE_CREDIT_CARD
                )
            } returns emptyList()

            val result = service.createFromPreActivationPayment(
                preActivationPayment,
                PaymentMethod.BOLETO,
                origin = InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS,
                dueDate = dueDate.toLocalDate(),
                sendEmail = true,
                syncProcess = true
            )

            assertThat(result).isSuccessWithDataIgnoringGivenFields(
                invoicePayment, "paymentDetailString"
            )

            coVerifyOnce {
                billingAccountablePartyService.get(billingAccountableParty.id)
                invoicePaymentDataService.add(match {
                    it.isBoleto &&
                            it.isPending &&
                            it.amount == 350.money &&
                            it.preActivationPaymentId == preActivationPayment.id &&
                            it.reason == PaymentReason.B2B_PRE_ACTIVATION_PAYMENT
                })
                invoicePaymentDataService.find(queryEq {
                    where {
                        this.preActivationPaymentId.inList(
                            listOf(
                                preActivationPayment.id
                            )
                        )
                    }
                })
            }

            coVerifyNone {
                paymentService.issueInvoicePayment(any(), any())
                invoicePaymentDataService.get(any())
                invoicePaymentDataService.update(any())
                kafkaProducerService.produce(any(), any())
                kafkaProducerService.produce(any())
            }
        }
    }

    @Test
    fun `#listInvoicePaymentsByBillingAccountablePartyId should return the payments list by the billingAccountableParty id`() =
        runBlocking {
            val firstPerson = TestModelFactory.buildPerson()
            val secondPerson = TestModelFactory.buildPerson()
            val firstInvoice = TestModelFactory.buildMemberInvoice(
                status = InvoiceStatus.OPEN,
                totalAmount = BigDecimal.valueOf(350).money,
                personId = firstPerson.id
            )
            val secondInvoice = TestModelFactory.buildMemberInvoice(
                status = InvoiceStatus.OPEN,
                totalAmount = BigDecimal.valueOf(500).money,
                personId = secondPerson.id
            )
            val memberInvoices = listOf(firstInvoice, secondInvoice)
            val dueDate = LocalDateTime.now().plusDays(1)
            val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail(dueDate = dueDate)
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                paymentDetail = boletoPaymentDetail,
                memberInvoiceIds = memberInvoices.map { it.id })
            val invoicePaymentModel = invoicePayment.toModel()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

            coEvery {
                invoicePaymentDataService.find(queryEq {
                    where {
                        this.billingAccountablePartyId.eq(billingAccountableParty.id.toString())
                    }
                })
            } returns listOf(invoicePaymentModel)


            val result = service.listInvoicePaymentsByBillingAccountablePartyId(
                billingAccountableParty.id
            )
            assertThat(result).isSuccessWithData(listOf(invoicePayment))
        }

    @Test
    fun `#reissueBoleto should just return if it already has an external id`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment(externalId = "aassddffee")
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()


        val result = service.reissueBoleto(
            invoicePayment, billingAccountableParty, LocalDateTime.now()
        )
        assertThat(result).isSuccessWithData(invoicePayment)
    }

    @Test
    fun `#createPaymentForLiquidation should create payment for b2b`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment(externalId = "aassddffee", sendEmail = true)
        val invoicePaymentModel = invoicePayment.toModel()
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        coEvery { billingAccountablePartyService.get(invoiceLiquidation.billingAccountablePartyId) } returns billingAccountableParty
        coEvery {
            invoicePaymentDataService.findOneOrNull(queryEq {
                where {
                    invoiceLiquidationId.eq(
                        invoiceLiquidation.id
                    ).and(status.eq(InvoicePaymentStatus.PENDING))
                }
            })
        } returns null
        coEvery { invoicePaymentDataService.add(any()) } returns invoicePaymentModel
        coEvery {
            acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
        } returns InvoicePaymentSource.IUGU
        coEvery {
            moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
        } returns RangeUUID.generateUUIDv7()
        coEvery { kafkaProducerService.produce(any()) } returns mockk()
        coEvery { kafkaProducerService.produce(any(), any()) } returns mockk()

        val result = service.createPaymentForLiquidation(
            invoiceLiquidation,
            paymentMethod = PaymentMethod.BOLEPIX,
            reason = PaymentReason.B2B_LIQUIDATION,
            origin = InvoicePaymentOrigin.ISSUED_BY_NULLVS,
        )

        assertThat(result).isSuccessWithDataIgnoringGivenFields(
            invoicePayment, "paymentDetailString"
        )

        coVerifyOnce { billingAccountablePartyService.get(any()) }
        coVerifyOnce { invoicePaymentDataService.add(any()) }
        coVerifyOnce {
            invoicePaymentDataService.findOneOrNull(queryEq {
                where {
                    invoiceLiquidationId.eq(
                        invoiceLiquidation.id
                    ).and(status.eq(InvoicePaymentStatus.PENDING))
                }
            })
        }
    }

    @Test
    fun `#createPaymentForLiquidation should get the payment when it already exists for the invoice liquidation`() =
        runBlocking {
            val invoicePayment = TestModelFactory.buildInvoicePayment(externalId = "aassddffee", sendEmail = true)
            val invoicePaymentModel = invoicePayment.toModel()
            val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()
            val boletoPaymentDetail = TestModelFactory.buildBoletoPaymentDetail().copy(
                paymentId = invoicePayment.id,
            )

            coEvery {
                invoicePaymentDataService.findOneOrNull(queryEq {
                    where {
                        invoiceLiquidationId.eq(
                            invoiceLiquidation.id
                        ).and(status.eq(InvoicePaymentStatus.PENDING))
                    }
                })
            } returns invoicePaymentModel

            coEvery { paymentDetailService.getPaymentDetail(invoicePayment) } returns boletoPaymentDetail

            val result = service.createPaymentForLiquidation(
                invoiceLiquidation,
                paymentMethod = PaymentMethod.BOLEPIX,
                reason = PaymentReason.B2B_LIQUIDATION,
                origin = InvoicePaymentOrigin.ISSUED_BY_NULLVS,
            )

            assertThat(result).isSuccessWithDataIgnoringGivenFields(
                invoicePayment, "paymentDetailString"
            )

            coVerifyOnce {
                invoicePaymentDataService.findOneOrNull(queryEq {
                    where {
                        invoiceLiquidationId.eq(
                            invoiceLiquidation.id
                        ).and(status.eq(InvoicePaymentStatus.PENDING))
                    }
                })
            }
            coVerifyNone {
                billingAccountablePartyService.get(any())
                invoicePaymentDataService.add(any())
            }
        }

    @Test
    fun `#createPaymentForLiquidation should create payment for b2c`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment(externalId = "aassddffee")
        val invoicePaymentModel = invoicePayment.toModel()
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        coEvery { billingAccountablePartyService.get(invoiceLiquidation.billingAccountablePartyId) } returns billingAccountableParty
        coEvery { invoicePaymentDataService.add(any()) } returns invoicePaymentModel
        coEvery {
            acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
        } returns InvoicePaymentSource.IUGU
        coEvery {
            moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
        } returns RangeUUID.generateUUIDv7()
        coEvery {
            invoicePaymentDataService.findOneOrNull(queryEq {
                where {
                    invoiceLiquidationId.eq(
                        invoiceLiquidation.id
                    ).and(status.eq(InvoicePaymentStatus.PENDING))
                }
            })
        } returns null

        service.createPaymentForLiquidation(
            invoiceLiquidation,
            paymentMethod = PaymentMethod.BOLEPIX,
            reason = PaymentReason.B2B_LIQUIDATION,
            origin = InvoicePaymentOrigin.ISSUED_BY_NULLVS,
        )

        coVerifyOnce { billingAccountablePartyService.get(any()) }
        coVerifyOnce { invoicePaymentDataService.add(any()) }
        coVerifyOnce {
            invoicePaymentDataService.findOneOrNull(queryEq {
                where {
                    invoiceLiquidationId.eq(
                        invoiceLiquidation.id
                    ).and(status.eq(InvoicePaymentStatus.PENDING))
                }
            })
        }
    }

    @Test
    fun `Should cancel payments by preActivationPaymentId successfully`() = runBlocking() {
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(person.id)

        val preActivationPaymentId = RangeUUID.generate()

        val firstInvoiceWithPayment = TestModelFactory.buildMemberInvoiceWithPayments(
            memberId = member.id,
            invoiceGroupId = null,
            preActivationPaymentId = preActivationPaymentId,
            paymentStatus = InvoicePaymentStatus.CANCELED,
            canceledReason = CancellationReason.PAYMENT_PROCESSOR_CANCELED
        )
        val secondInvoiceWithPayment = TestModelFactory.buildMemberInvoiceWithPayments(
            memberId = member.id,
            invoiceGroupId = null,
            preActivationPaymentId = preActivationPaymentId,
            paymentStatus = InvoicePaymentStatus.CANCELED,
            canceledReason = CancellationReason.PAYMENT_PROCESSOR_CANCELED
        )

        val memberInvoices = listOf(
            firstInvoiceWithPayment, secondInvoiceWithPayment
        )

        val preActivationPayment =
            TestModelFactory.buildPreActivationPayment(memberInvoiceIds = memberInvoices.map { it.id })

        val invoicePayments = firstInvoiceWithPayment.invoicePayments?.plus(
            secondInvoiceWithPayment.invoicePayments ?: emptyList()
        ) ?: emptyList()
        val invoicePaymentModels = invoicePayments.map { it.toModel() }

        val reason = CancellationReason.PAYMENT_PROCESSOR_CANCELED
        val firstExpectedPayment = invoicePayments[0].cancel(reason)
        val secondExpectedPayment = invoicePayments[1].cancel(reason)

        coEvery { invoicePaymentDataService.get(invoicePayments[0].id) } returns invoicePaymentModels[0].success()
        coEvery { invoicePaymentDataService.update(match { it.id == invoicePayments[0].id && it.isCanceled }) } returns firstExpectedPayment.toModel()
            .success()

        coEvery { invoicePaymentDataService.get(invoicePayments[1].id) } returns invoicePaymentModels[1].success()
        coEvery { invoicePaymentDataService.update(match { it.id == invoicePayments[1].id && it.isCanceled }) } returns secondExpectedPayment.toModel()
            .success()

        coEvery {
            invoicePaymentDataService.find(queryEq {
                where {
                    this.preActivationPaymentId.eq(preActivationPayment.id)
                }
            })
        } returns invoicePaymentModels.success()


        val result = service.cancelByPreActivationPaymentId(
            preActivationPayment.id, CancellationReason.PAYMENT_PROCESSOR_CANCELED
        )
        assertThat(result).isSuccessWithData(invoicePayments)
    }

    @Test
    fun `#createPaymentForLiquidation should create payment with due date if provided`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment(externalId = "aassddffee")
        val invoicePaymentModel = invoicePayment.toModel()
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(dueDate = LocalDate.now())
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val futureDueDate = LocalDate.now().plusDays(15)

        coEvery { billingAccountablePartyService.get(invoiceLiquidation.billingAccountablePartyId) } returns billingAccountableParty
        coEvery { invoicePaymentDataService.add(any()) } returns invoicePaymentModel
        coEvery {
            acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
        } returns InvoicePaymentSource.IUGU
        coEvery {
            moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
        } returns RangeUUID.generateUUIDv7()
        coEvery {
            invoicePaymentDataService.findOneOrNull(queryEq {
                where {
                    invoiceLiquidationId.eq(
                        invoiceLiquidation.id
                    ).and(status.eq(InvoicePaymentStatus.PENDING))
                }
            })
        } returns null

        service.createPaymentForLiquidation(
            invoiceLiquidation,
            paymentMethod = PaymentMethod.BOLEPIX,
            reason = PaymentReason.B2B_LIQUIDATION,
            origin = InvoicePaymentOrigin.ISSUED_BY_NULLVS,
            futureDueDate
        )

        coVerifyOnce { billingAccountablePartyService.get(any()) }
        coVerifyOnce {
            invoicePaymentDataService.add(match {
                (it.paymentDetail as BolepixPaymentDetailModel?)?.dueDate?.toLocalDate()
                    ?.isEqual(futureDueDate) == true
            })
        }
        coVerifyOnce {
            invoicePaymentDataService.findOneOrNull(queryEq {
                where {
                    invoiceLiquidationId.eq(
                        invoiceLiquidation.id
                    ).and(status.eq(InvoicePaymentStatus.PENDING))
                }
            })
        }
    }

    @Test
    fun `#createPaymentForLiquidation should not send email for other than first installment`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment(externalId = "aassddffee", sendEmail = false)
        val invoicePaymentModel = invoicePayment.toModel()
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(installment = 2)
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        coEvery { billingAccountablePartyService.get(invoiceLiquidation.billingAccountablePartyId) } returns billingAccountableParty
        coEvery { invoicePaymentDataService.add(any()) } returns invoicePaymentModel
        coEvery {
            acquirerOrchestratorService.getDefaultPaymentSource(billingAccountableParty.id)
        } returns InvoicePaymentSource.IUGU
        coEvery {
            moneyInResourceSignTokenServiceImpl.createSignTokenForMoneyInBff(invoicePayment.id)
        } returns RangeUUID.generateUUIDv7()
        coEvery { kafkaProducerService.produce(any()) } returns mockk()
        coEvery { kafkaProducerService.produce(any(), any()) } returns mockk()
        coEvery {
            invoicePaymentDataService.findOneOrNull(queryEq {
                where {
                    invoiceLiquidationId.eq(
                        invoiceLiquidation.id
                    ).and(status.eq(InvoicePaymentStatus.PENDING))
                }
            })
        } returns null

        val result = service.createPaymentForLiquidation(
            invoiceLiquidation,
            paymentMethod = PaymentMethod.BOLEPIX,
            reason = PaymentReason.B2B_LIQUIDATION,
            origin = InvoicePaymentOrigin.ISSUED_BY_NULLVS,
        )

        assertThat(result).isSuccessWithDataIgnoringGivenFields(
            invoicePayment, "paymentDetailString"
        )


        coVerifyOnce { billingAccountablePartyService.get(any()) }
        coVerifyOnce { invoicePaymentDataService.add(any()) }
        coVerifyOnce {
            invoicePaymentDataService.findOneOrNull(queryEq {
                where {
                    invoiceLiquidationId.eq(
                        invoiceLiquidation.id
                    ).and(status.eq(InvoicePaymentStatus.PENDING))
                }
            })
        }
    }

    @Test
    fun `#createPaymentForLiquidation should not create payment when reason is invalid`() = runBlocking {
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()

        val result = service.createPaymentForLiquidation(
            invoiceLiquidation,
            paymentMethod = PaymentMethod.BOLEPIX,
            reason = PaymentReason.REGULAR_PAYMENT,
            origin = InvoicePaymentOrigin.ISSUED_BY_NULLVS,
        )
        assertThat(result).isFailureOfType(InvalidPaymentMethodAndReasonException::class)

    }

    @Test
    fun `Should cancel payments by invoiceGroupId successfully`() = runBlocking() {
        val person = TestModelFactory.buildPerson()
        val member = TestModelFactory.buildMember(person.id)

        val invoiceGroupId = RangeUUID.generate()

        val firstInvoiceWithPayment = TestModelFactory.buildMemberInvoiceWithPayments(
            memberId = member.id,
            invoiceGroupId = invoiceGroupId,
            paymentStatus = InvoicePaymentStatus.CANCELED,
            canceledReason = CancellationReason.PAYMENT_PROCESSOR_CANCELED
        )
        val secondInvoiceWithPayment = TestModelFactory.buildMemberInvoiceWithPayments(
            memberId = member.id,
            invoiceGroupId = invoiceGroupId,
            paymentStatus = InvoicePaymentStatus.CANCELED,
            canceledReason = CancellationReason.PAYMENT_PROCESSOR_CANCELED
        )

        val memberInvoices = listOf(
            firstInvoiceWithPayment, secondInvoiceWithPayment
        )

        val memberInvoiceGroup =
            TestModelFactory.buildMemberInvoiceGroup(memberInvoiceIds = memberInvoices.map { it.id })

        val invoicePayments = firstInvoiceWithPayment.invoicePayments?.plus(
            secondInvoiceWithPayment.invoicePayments ?: emptyList()
        ) ?: emptyList()
        val invoicePaymentModels = invoicePayments.map { it.toModel() }

        val reason = CancellationReason.PAYMENT_PROCESSOR_CANCELED
        val firstExpectedPayment = invoicePayments[0].cancel(reason)
        val secondExpectedPayment = invoicePayments[1].cancel(reason)

        coEvery { invoicePaymentDataService.get(invoicePayments[0].id) } returns invoicePaymentModels[0]
        coEvery { invoicePaymentDataService.update(match { it.id == invoicePayments[0].id && it.isCanceled }) } returns firstExpectedPayment.toModel()


        coEvery { invoicePaymentDataService.get(invoicePayments[1].id) } returns invoicePaymentModels[1]
        coEvery { invoicePaymentDataService.update(match { it.id == invoicePayments[1].id && it.isCanceled }) } returns secondExpectedPayment.toModel()


        coEvery {
            invoicePaymentDataService.find(queryEq {
                where {
                    this.invoiceGroupId.eq(memberInvoiceGroup.id)
                }
            })
        } returns invoicePaymentModels


        val result = service.cancelByInvoiceGroupId(
            memberInvoiceGroup.id, CancellationReason.PAYMENT_PROCESSOR_CANCELED
        )
        assertThat(result).isSuccessWithData(invoicePayments)
    }

    @Test
    fun `#update should return invoicePayment updated`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val invoicePaymentModel = invoicePayment.toModel()

        coEvery { invoicePaymentDataService.update(any()) } returns invoicePaymentModel


        val result = service.update(invoicePayment)
        assertThat(result).isSuccessWithData(invoicePayment)
    }

    @Test
    fun `#expire should return success when payment was expired successfully`() = runBlocking<Unit> {
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val invoicePaymentModel = invoicePayment.toModel()
        val expectedPayment = invoicePayment.expire()
        val expectedPaymentModel = expectedPayment.toModel()

        coEvery { invoicePaymentDataService.get(invoicePayment.id) } returns invoicePaymentModel
        coEvery { invoicePaymentDataService.update(match { it.id == invoicePayment.id && it.isExpired }) } returns expectedPaymentModel


        val result = service.expire(invoicePayment.id)
        assertThat(result).isSuccessWithData(expectedPayment)

        assertThatCounter("invoice_payment_status_changes_total").withLabels(
            "status" to "EXPIRED",
            "method" to "BOLETO",
        ).hasCountOf(1)
    }


    @Test
    fun `#expire should return success when payment is already expired`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val expiredPayment = invoicePayment.expire()
        val expiredPaymentModel = expiredPayment.toModel()

        coEvery { invoicePaymentDataService.get(expiredPayment.id) } returns expiredPaymentModel


        val result = service.expire(expiredPayment.id)
        assertThat(result).isSuccessWithData(expiredPayment)
    }

    @Test
    fun `#expire should ignore event when payment is approved`() = runBlocking<Unit> {
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val approvedPayment = invoicePayment.approve()
        val approvedPaymentModel = approvedPayment.toModel()

        coEvery { invoicePaymentDataService.get(approvedPayment.id) } returns approvedPaymentModel


        val result = service.expire(approvedPayment.id)
        assertThat(result).isSuccessWithData(approvedPayment)
    }

    @Test
    fun `#cancelByLiquidationId should cancel payments by liquidation id successfully`() = runBlocking() {
        val liquidationId = RangeUUID.generate()

        val invoicePayment1 = TestModelFactory.buildInvoicePayment(invoiceLiquidationId = liquidationId)
        val invoicePaymentModel1 = invoicePayment1.toModel()
        val invoicePayment2 = TestModelFactory.buildInvoicePayment(invoiceLiquidationId = liquidationId)
        val invoicePaymentModel2 = invoicePayment2.toModel()
        val invoicePayments = listOf(invoicePayment1, invoicePayment2)
        val invoicePaymentModels = invoicePayments.map { it.toModel() }
        val canceledInvoicePayment1 = invoicePayment1.copy(
            status = InvoicePaymentStatus.CANCELED,
            canceledReason = CancellationReason.CANCELED_BY_LIQUIDATION
        )
        val canceledInvoicePaymentModel1 = canceledInvoicePayment1.toModel()
        val canceledInvoicePayment2 = invoicePayment2.copy(
            status = InvoicePaymentStatus.CANCELED,
            canceledReason = CancellationReason.CANCELED_BY_LIQUIDATION
        )
        val canceledInvoicePaymentModel2 = canceledInvoicePayment2.toModel()
        val canceledPayments = listOf(canceledInvoicePayment1, canceledInvoicePayment2)

        coEvery { invoicePaymentDataService.find(queryEq { where { this.invoiceLiquidationId.eq(liquidationId) } }) } returns invoicePaymentModels

        coEvery { invoicePaymentDataService.get(invoicePayment1.id) } returns invoicePaymentModel1

        coEvery { invoicePaymentDataService.update(canceledInvoicePaymentModel1) } returns canceledInvoicePaymentModel1

        coEvery { invoicePaymentDataService.get(invoicePayment2.id) } returns invoicePaymentModel2

        coEvery { invoicePaymentDataService.update(canceledInvoicePaymentModel2) } returns canceledInvoicePaymentModel2

        coEvery {
            kafkaProducerService.produce(any())
        } returns ProducerResult(LocalDateTime.now(), InvoicePaymentCanceledEvent.name, 0)

        val result = service.cancelByInvoiceLiquidationId(liquidationId, CancellationReason.CANCELED_BY_LIQUIDATION)

        assertThat(result).isSuccessWithData(canceledPayments)

        coVerifyOnce { invoicePaymentDataService.update(canceledInvoicePaymentModel1) }

        coVerifyOnce { invoicePaymentDataService.update(canceledInvoicePaymentModel2) }
    }
}
