package br.com.alice.staff.controllers

import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.common.notification.NotificationEventAction
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Council
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.services.HealthProfessionalModelDataService
import br.com.alice.staff.converters.toModel
import br.com.alice.staff.event.HealthProfessionalChangedEvent
import br.com.alice.staff.event.HealthProfessionalUpdatedEvent
import br.com.alice.staff.models.BackfillResponse
import br.com.alice.staff.models.SetCouncilRequest
import br.com.alice.staff.models.SetSpecialtiesRequest
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import kotlin.test.AfterTest
import kotlin.test.BeforeTest

class HealthProfessionalBackfillControllerTest : ControllerTestHelper() {

    private val healthProfessionalDataService: HealthProfessionalModelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val controller = HealthProfessionalBackfillController(healthProfessionalDataService, kafkaProducerService)

    private val staffId = "28a120b3-0ede-4764-854d-0399382fe270".toUUID()
    private val specialtyId = "a3901cee-af26-41f8-b05c-75f6373ab480".toUUID()
    private val subSpecialtyIds = listOf(
        "b6c743d4-ab00-49ae-8390-82dd427dc410".toUUID(),
        "89ff9c71-**************-e1a016fbd950".toUUID()
    )
    private val healthProfessional = TestModelFactory.buildHealthProfessional(
        staffId = staffId,
        specialtyId = null,
        subSpecialtyIds = emptyList(),
        internalSpecialtyId = "7731295e-5f01-45a6-a0c8-3da23dd36ed0".toUUID(),
        internalSubSpecialtyIds = listOf("7731295e-5f01-45a6-a0c8-3da23dd36ed0".toUUID()),
        council = Council(
            number = "123456",
            state = State.SP,
        )
    )
    private val healthProfessionalModel = healthProfessional.toModel()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @AfterTest
    fun tearDown() = confirmVerified(healthProfessionalDataService, kafkaProducerService)

    @Test
    fun `should set new specialties`() {
        val staffIds = listOf(staffId)
        val request = SetSpecialtiesRequest(
            staffIds = staffIds,
            specialtyId = specialtyId,
            subSpecialtyIds = subSpecialtyIds
        )
        val expected = BackfillResponse(quantitySuccess = 1, quantityFailure = 0)

        coEvery {
            healthProfessionalDataService.find(queryEq { where { this.staffId.inList(staffIds) } })
        } returns listOf(healthProfessionalModel).success()

        val updatedHealthProfessional = healthProfessional.copy(
            specialtyId = specialtyId,
            subSpecialtyIds = subSpecialtyIds,
            internalSpecialtyId = null,
            internalSubSpecialtyIds = emptyList()
        )
        val updatedHealthProfessionalModel = updatedHealthProfessional.toModel()
        coEvery {
            healthProfessionalDataService.update(updatedHealthProfessionalModel)
        } returns updatedHealthProfessionalModel.success()

        val healthProfessionalUpdatedEvent = HealthProfessionalUpdatedEvent(updatedHealthProfessional.id)
        val healthProfessionalChangedEvent =
            HealthProfessionalChangedEvent(updatedHealthProfessional, NotificationEventAction.UPDATED)
        coEvery {
            kafkaProducerService.produce(match { it.payload == healthProfessionalUpdatedEvent.payload })
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(match { it.payload == healthProfessionalChangedEvent.payload })
        } returns mockk()

        internalAuthentication {
            post(
                to = "/backfill/set_specialties",
                body = request
            ) { response ->
                val result = response.bodyAsJson<BackfillResponse>()
                val status = response.status()
                assertThat(result).isEqualTo(expected)
                assertThat(status!!.value).isEqualTo(200)

                coVerifyOnce { healthProfessionalDataService.find(any()) }
                coVerifyOnce { healthProfessionalDataService.update(any()) }
                coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
            }
        }
    }

    @Test
    fun `should not set new specialties when given empty list of staff ids`() {
        val request = SetSpecialtiesRequest(
            staffIds = emptyList(),
            specialtyId = specialtyId,
            subSpecialtyIds = subSpecialtyIds
        )
        val expected = BackfillResponse(quantitySuccess = 0, quantityFailure = 0)

        internalAuthentication {
            post(
                to = "/backfill/set_specialties",
                body = request
            ) { response ->
                val result = response.bodyAsJson<BackfillResponse>()
                val status = response.status()
                assertThat(result).isEqualTo(expected)
                assertThat(status!!.value).isEqualTo(200)

                coVerifyNone { healthProfessionalDataService.find(any()) }
                coVerifyNone { healthProfessionalDataService.update(any()) }
                coVerifyNone { kafkaProducerService.produce(any()) }
            }
        }
    }

    @Test
    fun `should set council type when specialtyId is null`() {
        val expected = BackfillResponse(quantitySuccess = 1, quantityFailure = 0)

        val request = SetCouncilRequest(
            limit = 1,
            offset = 0
        )

        coEvery {
            healthProfessionalDataService.find(queryEq {
                limit { request.limit }
                    .offset { request.offset }
                    .orderBy { this.createdAt }
                    .sortOrder { desc }
            })
        } returns listOf(healthProfessionalModel).success()

        val updatedHealthProfessional = healthProfessional.copy(
            council = healthProfessional.council.copy(type = CouncilType.CRM)
        )

        val updatedHealthProfessionalModel = updatedHealthProfessional.toModel()

        coEvery {
            healthProfessionalDataService.update(updatedHealthProfessionalModel)
        } returns updatedHealthProfessionalModel.success()

        val healthProfessionalUpdatedEvent = HealthProfessionalUpdatedEvent(updatedHealthProfessional.id)
        val healthProfessionalChangedEvent =
            HealthProfessionalChangedEvent(updatedHealthProfessional, NotificationEventAction.UPDATED)
        coEvery {
            kafkaProducerService.produce(match { it.payload == healthProfessionalUpdatedEvent.payload })
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(match { it.payload == healthProfessionalChangedEvent.payload })
        } returns mockk()

        internalAuthentication {
            post(
                to = "/backfill/set_council_type",
                body = request
            ) { response ->
                val result = response.bodyAsJson<BackfillResponse>()
                val status = response.status()
                assertThat(result).isEqualTo(expected)
                assertThat(status!!.value).isEqualTo(200)

                coVerifyOnce { healthProfessionalDataService.find(any()) }
                coVerifyOnce { healthProfessionalDataService.update(any()) }
                coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
            }
        }
    }

    @Test
    fun `should set council type when specialtyId is not null and mapped in specialtyCouncilMapping`() {
        val healthProfessional = healthProfessional.copy(
            specialtyId = "0ca9baf7-ef25-4bdc-b08e-1fc0563e4600".toUUID(),
        )

        val healthProfessionalModel = healthProfessional.toModel()

        val expected = BackfillResponse(quantitySuccess = 1, quantityFailure = 0)

        val request = SetCouncilRequest(
            limit = 1,
            offset = 0
        )

        coEvery {
            healthProfessionalDataService.find(queryEq {
                limit { request.limit }
                    .offset { request.offset }
                    .orderBy { this.createdAt }
                    .sortOrder { desc }
            })
        } returns listOf(healthProfessionalModel).success()

        val updatedHealthProfessional = healthProfessional.copy(
            council = healthProfessional.council.copy(type = CouncilType.CRO)
        )

        val updatedHealthProfessionalModel = updatedHealthProfessional.toModel()

        coEvery {
            healthProfessionalDataService.update(updatedHealthProfessionalModel)
        } returns updatedHealthProfessionalModel.success()

        val healthProfessionalUpdatedEvent = HealthProfessionalUpdatedEvent(updatedHealthProfessional.id)
        val healthProfessionalChangedEvent =
            HealthProfessionalChangedEvent(updatedHealthProfessional, NotificationEventAction.UPDATED)
        coEvery {
            kafkaProducerService.produce(match { it.payload == healthProfessionalUpdatedEvent.payload })
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(match { it.payload == healthProfessionalChangedEvent.payload })
        } returns mockk()

        internalAuthentication {
            post(
                to = "/backfill/set_council_type",
                body = request
            ) { response ->
                val result = response.bodyAsJson<BackfillResponse>()
                val status = response.status()
                assertThat(result).isEqualTo(expected)
                assertThat(status!!.value).isEqualTo(200)

                coVerifyOnce { healthProfessionalDataService.find(any()) }
                coVerifyOnce { healthProfessionalDataService.update(any()) }
                coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
            }
        }
    }

    @Test
    fun `should not set council type when council type is already set`() {
        val healthProfessional = healthProfessional.copy(
            specialtyId = "0ca9baf7-ef25-4bdc-b08e-1fc0563e4600".toUUID(),
            council = Council(
                number = "123456",
                state = State.SP,
                type = CouncilType.CRM
            )
        )

        val healthProfessionalModel = healthProfessional.toModel()

        val expected = BackfillResponse(quantitySuccess = 0, quantityFailure = 0)

        val request = SetCouncilRequest(
            limit = 1,
            offset = 0
        )

        coEvery {
            healthProfessionalDataService.find(queryEq {
                limit { request.limit }
                    .offset { request.offset }
                    .orderBy { this.createdAt }
                    .sortOrder { desc }
            })
        } returns listOf(healthProfessionalModel).success()

        internalAuthentication {
            post(
                to = "/backfill/set_council_type",
                body = request
            ) { response ->
                val result = response.bodyAsJson<BackfillResponse>()
                val status = response.status()
                assertThat(result).isEqualTo(expected)
                assertThat(status!!.value).isEqualTo(200)

                coVerifyOnce { healthProfessionalDataService.find(any()) }
                coVerifyNone { healthProfessionalDataService.update(any()) }
                coVerifyNone { kafkaProducerService.produce(any()) }
            }
        }
    }

}
