package br.com.alice.tiss.clients.v4_01_00

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.data.layer.models.AttendanceCharacter
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.exec.indicator.models.RequesterData
import br.com.alice.tiss.builders.v4_01_00.ObservacaoJson
import br.com.alice.tiss.builders.v4_01_00.TissTransactionHeaderBuilder
import br.com.alice.tiss.models.AttachmentHeader
import br.com.alice.tiss.models.BeneficiaryAdditionalData
import br.com.alice.tiss.models.ChemotherapyOncologicalDiagnosis
import br.com.alice.tiss.models.ChemotherapyRequestAttachment
import br.com.alice.tiss.models.CreateGuiaHospitalizationExtensionRequest
import br.com.alice.tiss.models.CreateGuiaHospitalizationRequest
import br.com.alice.tiss.models.CreateGuiaRequest
import br.com.alice.tiss.models.OncologicalDiagnosis
import br.com.alice.tiss.models.OncologicalRadioDiagnosis
import br.com.alice.tiss.models.RadioRequestAttachment
import br.com.alice.tiss.models.RequestedDrug
import br.com.alice.tiss.models.RequestedDrugs
import br.com.alice.tiss.totvs.v4_01_00.commom.AutorizacaoProcedimentoWS
import br.com.alice.tiss.totvs.v4_01_00.commom.CtMotivoGlosa
import br.com.alice.tiss.totvs.v4_01_00.commom.CtSituacaoAutorizacao
import br.com.alice.tiss.totvs.v4_01_00.commom.DmTipoTransacao
import br.com.alice.tiss.totvs.v4_01_00.guia.TissSolicitacaoProcedimento
import br.com.alice.tiss.utils.TissTransactionTypeEnum
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

class GuiaClientTest {

    private val createGuiaWebService: TissSolicitacaoProcedimento = mockk()
    private val client = GuiaClient(createGuiaWebService)

    @AfterTest
    fun clean() {
        clearAllMocks()
    }

    private val createGuiaRequest = CreateGuiaRequest(
        procedures = listOf(
            GuiaProcedure(table = "22", code = "123", quantity = 1, description = ""),
            GuiaProcedure(table = "22", code = "545", quantity = 1, description = "")
        ),
        requester = RequesterData(name = "Alice Operadora", providerCode = "34266553000102"),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE
        ),
        guiaNumber = "************",
        requestDate = LocalDate.now(),
        beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        ansCode = "1234",
        authorizationType = "1",
        attendanceCharacter = "1",
        origin = GuiaOrigin.EHR,
        attendanceRegime = "9",
        accidentIndication = "01"
    )

    @Test
    fun `#createGuiaExam - should call createGuiaWebService endpoint correctly`() = runBlocking {

        val expectedResponse = AutorizacaoProcedimentoWS()

        coEvery {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(any())
        } returns expectedResponse

        client.createGuiaExam(createGuiaRequest)

        val transactionType = DmTipoTransacao.valueOf(TissTransactionTypeEnum.SOLICITACAO_PROCEDIMENTOS.toString())
        coVerifyOnce {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(
                match {
                    it.cabecalho.identificacaoTransacao.tipoTransacao == transactionType &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.procedimentosSolicitados.first().procedimento.codigoProcedimento == createGuiaRequest.procedures[0].code &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.dadosBeneficiario.numeroCarteira == createGuiaRequest.beneficiary.nationalId &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.dadosSolicitante.contratadoSolicitante.codigoPrestadorNaOperadora == TissTransactionHeaderBuilder.genericRDA &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.observacao == ObservacaoJson(
                        false,
                        "01",
                        "9"
                    ).toJson()
                }
            )
        }
    }

    @Test
    fun `#createGuiaExam - should call createGuiaWebService endpoint correctly with trim on codigoProcedimento`() = runBlocking {

        val expectedResponse = AutorizacaoProcedimentoWS()

        coEvery {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(any())
        } returns expectedResponse

        client.createGuiaExam(createGuiaRequest.copy(procedures = listOf(
            GuiaProcedure(table = "22", code = "123 ", quantity = 1, description = ""),
            GuiaProcedure(table = "22", code = "545", quantity = 1, description = "")
        )))

        val transactionType = DmTipoTransacao.valueOf(TissTransactionTypeEnum.SOLICITACAO_PROCEDIMENTOS.toString())
        coVerifyOnce {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(
                match {
                    it.cabecalho.identificacaoTransacao.tipoTransacao == transactionType &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.procedimentosSolicitados.first().procedimento.codigoProcedimento == "123" &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.dadosBeneficiario.numeroCarteira == createGuiaRequest.beneficiary.nationalId &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.dadosSolicitante.contratadoSolicitante.codigoPrestadorNaOperadora == TissTransactionHeaderBuilder.genericRDA &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.observacao == ObservacaoJson(
                        false,
                        "01",
                        "9"
                    ).toJson()
                }
            )
        }
    }

    @Test
    fun `#createGuiaExam - should call createGuiaWebService endpoint correctly using flag to force authorization`() =
        runBlocking {

            val createGuiaRequest = CreateGuiaRequest(
                procedures = listOf(
                    GuiaProcedure(
                        table = "22",
                        code = "123",
                        quantity = 1,
                        description = "",
                        status = MvAuthorizedProcedureStatus.AUTHORIZED
                    ),
                    GuiaProcedure(
                        table = "22",
                        code = "545",
                        quantity = 1,
                        description = "",
                        status = MvAuthorizedProcedureStatus.AUTHORIZED
                    )
                ),
                requester = RequesterData(name = "Alice Operadora", providerCode = "34266553000102"),
                professional = Professional(
                    name = "Robson",
                    council = CouncilType.CRM,
                    councilNumber = "123",
                    councilState = State.CE
                ),
                guiaNumber = "************",
                requestDate = LocalDate.now(),
                beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
                ansCode = "1234",
                authorizationType = "1",
                attendanceCharacter = "1",
                origin = GuiaOrigin.EHR,
                attendanceRegime = "4",
                accidentIndication = "02"
            )

            val expectedResponse = AutorizacaoProcedimentoWS()

            coEvery {
                createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(any())
            } returns expectedResponse

            client.createGuiaExam(createGuiaRequest)

            val transactionType = DmTipoTransacao.valueOf(TissTransactionTypeEnum.SOLICITACAO_PROCEDIMENTOS.toString())
            coVerifyOnce {
                createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(
                    match {
                        it.cabecalho.identificacaoTransacao.tipoTransacao == transactionType &&
                                it.solicitacaoProcedimento.solicitacaoSPSADT.procedimentosSolicitados.first().procedimento.codigoProcedimento == createGuiaRequest.procedures[0].code &&
                                it.solicitacaoProcedimento.solicitacaoSPSADT.dadosBeneficiario.numeroCarteira == createGuiaRequest.beneficiary.nationalId &&
                                it.solicitacaoProcedimento.solicitacaoSPSADT.dadosSolicitante.contratadoSolicitante.codigoPrestadorNaOperadora == TissTransactionHeaderBuilder.genericRDA &&
                                it.solicitacaoProcedimento.solicitacaoSPSADT.observacao == ObservacaoJson(
                            true,
                            "02",
                            "4"
                        ).toJson()
                    }
                )
            }
        }

    @Test
    fun `#createGuiaExam - returns with errors`() = runBlocking {
        val glosa = CtMotivoGlosa()
        glosa.codigoGlosa = "1001"
        glosa.descricaoGlosa = "Error"

        val situacao = CtSituacaoAutorizacao()
        situacao.mensagemErro = glosa

        val expectedResponse = AutorizacaoProcedimentoWS()
        expectedResponse.autorizacaoProcedimento = situacao

        coEvery {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(any())
        } returns expectedResponse

        val result = client.createGuiaExam(createGuiaRequest)

        ResultAssert.assertThat(result).isFailure()

        coVerifyOnce { createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(any()) }
    }

    @Test
    fun `#createGuiaHospitalization - should call createGuiaWebService endpoint correctly`() = runBlocking {
        val expectedResponse = AutorizacaoProcedimentoWS()
        val createGuiaRequest = CreateGuiaHospitalizationRequest(
            procedures = listOf(
                GuiaProcedure(table = "22", code = "123", quantity = 1, description = ""),
                GuiaProcedure(table = "22", code = "545", quantity = 1, description = "")
            ),
            requester = RequesterData(name = "Alice Operadora", providerCode = "00000000000"),
            professional = Professional(
                name = "Robson",
                council = CouncilType.CRM,
                councilNumber = "123",
                councilState = State.CE
            ),
            guiaNumber = "************",
            requestDate = LocalDate.now(),
            beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
            ansCode = "1234",
            authorizationType = "1",
            attendanceCharacter = AttendanceCharacter.ELECTIVE.toString(),
            origin = GuiaOrigin.EHR,
            attendanceRegime = "03",
            accidentIndication = "01",
            recommendedAdmissionDate = LocalDate.now(),
            admissionDaysRequested = BigInteger.ONE,
            opmeIndicator = false,
            quimioterapyIndicator = false,
            clinicalIndication = "Apendicite",
            diagnosisCID = "A01",
            admissionType = "1"
        )

        coEvery {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(any())
        } returns expectedResponse

        client.createGuiaHospitalization(createGuiaRequest)

        val transactionType = DmTipoTransacao.valueOf(TissTransactionTypeEnum.SOLICITACAO_PROCEDIMENTOS.toString())
        coVerifyOnce {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(
                match {
                    it.cabecalho.identificacaoTransacao.tipoTransacao == transactionType &&
                            it.solicitacaoProcedimento.solicitacaoInternacao.procedimentosSolicitados.first().procedimento.codigoProcedimento == createGuiaRequest.procedures[0].code &&
                            it.solicitacaoProcedimento.solicitacaoInternacao.dadosBeneficiario.numeroCarteira == createGuiaRequest.beneficiary.nationalId &&
                            it.solicitacaoProcedimento.solicitacaoInternacao.dadosInternacao.caraterAtendimento == "1" &&
                            it.solicitacaoProcedimento.solicitacaoInternacao.dadosInternacao.tipoInternacao == createGuiaRequest.admissionType &&
                            it.solicitacaoProcedimento.solicitacaoInternacao.dadosInternacao.regimeInternacao == "03" &&
                            it.solicitacaoProcedimento.solicitacaoInternacao.dadosInternacao.indicacaoClinica == createGuiaRequest.clinicalIndication
                }
            )
        }
    }

    @Test
    fun `#createGuiaHospitalization - should call createGuiaWebService endpoint correctly with trim on codigoProcedimento and indicacaoClinica`() = runBlocking {
        val expectedResponse = AutorizacaoProcedimentoWS()
        val createGuiaRequest = CreateGuiaHospitalizationRequest(
            procedures = listOf(
                GuiaProcedure(table = "22", code = "123 ", quantity = 1, description = ""),
                GuiaProcedure(table = "22", code = "545", quantity = 1, description = "")
            ),
            requester = RequesterData(name = "Alice Operadora", providerCode = "00000000000"),
            professional = Professional(
                name = "Robson",
                council = CouncilType.CRM,
                councilNumber = "123",
                councilState = State.CE
            ),
            guiaNumber = "************",
            requestDate = LocalDate.now(),
            beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
            ansCode = "1234",
            authorizationType = "1",
            attendanceCharacter = AttendanceCharacter.ELECTIVE.toString(),
            origin = GuiaOrigin.EHR,
            attendanceRegime = "03",
            accidentIndication = "01",
            recommendedAdmissionDate = LocalDate.now(),
            admissionDaysRequested = BigInteger.ONE,
            opmeIndicator = false,
            quimioterapyIndicator = false,
            clinicalIndication = "Apendicite ",
            diagnosisCID = "A01",
            admissionType = "1"
        )

        coEvery {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(any())
        } returns expectedResponse

        client.createGuiaHospitalization(createGuiaRequest)

        val transactionType = DmTipoTransacao.valueOf(TissTransactionTypeEnum.SOLICITACAO_PROCEDIMENTOS.toString())
        coVerifyOnce {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(
                match {
                    it.cabecalho.identificacaoTransacao.tipoTransacao == transactionType &&
                            it.solicitacaoProcedimento.solicitacaoInternacao.procedimentosSolicitados.first().procedimento.codigoProcedimento == "123" &&
                            it.solicitacaoProcedimento.solicitacaoInternacao.dadosBeneficiario.numeroCarteira == createGuiaRequest.beneficiary.nationalId &&
                            it.solicitacaoProcedimento.solicitacaoInternacao.dadosInternacao.caraterAtendimento == "1" &&
                            it.solicitacaoProcedimento.solicitacaoInternacao.dadosInternacao.tipoInternacao == createGuiaRequest.admissionType &&
                            it.solicitacaoProcedimento.solicitacaoInternacao.dadosInternacao.regimeInternacao == "03" &&
                            it.solicitacaoProcedimento.solicitacaoInternacao.dadosInternacao.indicacaoClinica == "Apendicite"
                }
            )
        }
    }

    @Test
    fun `#createGuiaHospitalizationExtension - should call createGuiaWebService endpoint correctly`() = runBlocking {
        val expectedResponse = AutorizacaoProcedimentoWS()
        val request = CreateGuiaHospitalizationExtensionRequest(
            procedures = listOf(
                GuiaProcedure(table = "22", code = "123", quantity = 1, description = ""),
                GuiaProcedure(table = "22", code = "545", quantity = 1, description = "")
            ),
            requester = RequesterData(name = "Alice Operadora", providerCode = "00000000000"),
            professional = Professional(
                name = "Robson",
                council = CouncilType.CRM,
                councilNumber = "123",
                councilState = State.CE
            ),
            guiaNumber = "************",
            requestDate = LocalDate.now(),
            beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
            ansCode = "1234",
            origin = GuiaOrigin.EHR,
            clinicalIndication = "Apendicite",
            additionalDays = 1,
            referenceGuiaNumber = "123"
        )

        coEvery {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(any())
        } returns expectedResponse

        client.createGuiaHospitalizationExtension(request)

        val transactionType = DmTipoTransacao.valueOf(TissTransactionTypeEnum.SOLICITACAO_PROCEDIMENTOS.toString())
        coVerifyOnce {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(
                match {
                    it.cabecalho.identificacaoTransacao.tipoTransacao == transactionType &&
                            it.solicitacaoProcedimento.solicitacaoProrrogacao.procedimentosAdicionais.first().procedimento.codigoProcedimento == createGuiaRequest.procedures[0].code &&
                            it.solicitacaoProcedimento.solicitacaoProrrogacao.dadosBeneficiario.numeroCarteira == request.beneficiary.nationalId &&
                            it.solicitacaoProcedimento.solicitacaoProrrogacao.dadosInternacao.qtDiariasAdicionais == "1".toBigInteger() &&
                            it.solicitacaoProcedimento.solicitacaoProrrogacao.dadosInternacao.indicacaoClinica == request.clinicalIndication &&
                            it.solicitacaoProcedimento.solicitacaoProrrogacao.nrGuiaReferenciada == request.referenceGuiaNumber
                })
        }
    }

    @Test
    fun `#createGuiaHospitalizationExtension - should call createGuiaWebService endpoint correctly with trim on codigoProcedimento and indicacaoClinica`() = runBlocking {
        val expectedResponse = AutorizacaoProcedimentoWS()
        val request = CreateGuiaHospitalizationExtensionRequest(
            procedures = listOf(
                GuiaProcedure(table = "22", code = "123 ", quantity = 1, description = ""),
                GuiaProcedure(table = "22", code = "545", quantity = 1, description = "")
            ),
            requester = RequesterData(name = "Alice Operadora", providerCode = "00000000000"),
            professional = Professional(
                name = "Robson",
                council = CouncilType.CRM,
                councilNumber = "123",
                councilState = State.CE
            ),
            guiaNumber = "************",
            requestDate = LocalDate.now(),
            beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
            ansCode = "1234",
            origin = GuiaOrigin.EHR,
            clinicalIndication = "Apendicite ",
            additionalDays = 1,
            referenceGuiaNumber = "123"
        )

        coEvery {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(any())
        } returns expectedResponse

        client.createGuiaHospitalizationExtension(request)

        val transactionType = DmTipoTransacao.valueOf(TissTransactionTypeEnum.SOLICITACAO_PROCEDIMENTOS.toString())
        coVerifyOnce {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(
                match {
                    it.cabecalho.identificacaoTransacao.tipoTransacao == transactionType &&
                            it.solicitacaoProcedimento.solicitacaoProrrogacao.procedimentosAdicionais.first().procedimento.codigoProcedimento == "123" &&
                            it.solicitacaoProcedimento.solicitacaoProrrogacao.dadosBeneficiario.numeroCarteira == request.beneficiary.nationalId &&
                            it.solicitacaoProcedimento.solicitacaoProrrogacao.dadosInternacao.qtDiariasAdicionais == "1".toBigInteger() &&
                            it.solicitacaoProcedimento.solicitacaoProrrogacao.dadosInternacao.indicacaoClinica == "Apendicite" &&
                            it.solicitacaoProcedimento.solicitacaoProrrogacao.nrGuiaReferenciada == request.referenceGuiaNumber
                })
        }
    }

    @Test
    fun `#createGuiaWithChemotherapy - should call createGuiaWebService endpoint correctly`() = runBlocking {
        val chemoRequest = ChemotherapyRequestAttachment(
            requestedDrugs = RequestedDrugs(
                listOf(
                    RequestedDrug(
                        probableDate = LocalDate.now(),
                        identification = GuiaProcedure(table = "22", code = "545", quantity = 1, description = ""),
                        dosesQuantity = 12.0,
                        unitMeasure = "01",
                        administrationRoute = "02",
                        frequency = BigInteger.valueOf(12)
                    ),
                    RequestedDrug(
                        probableDate = LocalDate.now(),
                        identification = GuiaProcedure(
                            table = "22",
                            code = "545",
                            quantity = 1,
                            description = "OHMY GOD"
                        ),
                        dosesQuantity = 12.0,
                        unitMeasure = "01",
                        administrationRoute = "02",
                        frequency = BigInteger.valueOf(12)
                    ),
                )
            ),
            professional = Professional(
                name = "Robson",
                council = CouncilType.CRM,
                councilNumber = "123",
                councilState = State.CE,
                phoneNumber = "123",
                email = "<EMAIL>"
            ),
            beneficiaryData = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
            origin = GuiaOrigin.EHR,
            attachmentHeader = AttachmentHeader(
                ansCode = "1234",
                attachmentGuideNumber = "************",
                referencedGuideNumber = "123",
                requestDate = LocalDate.now()
            ),
            providerCnpj = "87145534000194",
            observation = "Teste",
            beneficiaryAdditionalData = BeneficiaryAdditionalData(
                weight = BigDecimal.valueOf(180),
                height = BigDecimal.valueOf(100),
                bodySurface = BigDecimal.valueOf(23),
                age = BigInteger.valueOf(12),
                sex = "01"
            ),
            chemotherapyOncologicalDiagnosis = ChemotherapyOncologicalDiagnosis(
                chemoDiagnosis = OncologicalDiagnosis(
                    diagnosisDate = LocalDate.now(),
                    diagnosisCID = listOf("C50"),
                    local = "Teste",
                    purpose = "Teste",
                    ecog = "Teste"
                ),
                tumor = "Teste",
                nodule = "Teste",
                metastasis = "Teste",
                chemotherapyType = "Teste",
                therapeuticPlan = "Teste"
            ),
            cyclesNumber = BigInteger.valueOf(1),
            currentCycle = BigInteger.valueOf(1),
            currentCycleDays = BigInteger.valueOf(1),
            cyclesInterval = BigInteger.valueOf(1)
        )

        val expectedResponse = AutorizacaoProcedimentoWS()

        coEvery {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(any())
        } returns expectedResponse

        client.createGuiaWithChemotherapy(createGuiaRequest, chemoRequest)

        val transactionType = DmTipoTransacao.valueOf(TissTransactionTypeEnum.SOLICITACAO_PROCEDIMENTOS.toString())
        coVerifyOnce {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(
                match {
                    it.cabecalho.identificacaoTransacao.tipoTransacao == transactionType &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.procedimentosSolicitados.first().procedimento.codigoProcedimento == createGuiaRequest.procedures[0].code &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.dadosBeneficiario.numeroCarteira == createGuiaRequest.beneficiary.nationalId &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.dadosSolicitante.contratadoSolicitante.codigoPrestadorNaOperadora == TissTransactionHeaderBuilder.genericRDA &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.anexoClinico.solicitacaoQuimioterapia.diasCicloAtual == chemoRequest.currentCycleDays
                }
            )
        }
    }

    @Test
    fun `#createGuiaWithRadiotherapy - should call createGuiaWebService endpoint correctly`() = runBlocking {
        val radioRequest = RadioRequestAttachment(
            professional = Professional(
                name = "Robson",
                council = CouncilType.CRM,
                councilNumber = "123",
                councilState = State.CE,
                phoneNumber = "123",
                email = "<EMAIL>"
            ),
            beneficiaryData = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
            origin = GuiaOrigin.EHR,
            attachmentHeader = AttachmentHeader(
                ansCode = "1234",
                attachmentGuideNumber = "************",
                referencedGuideNumber = "123",
                requestDate = LocalDate.now()
            ),
            providerCnpj = "87145534000194",
            observation = "Teste",
            beneficiaryAdditionalData = BeneficiaryAdditionalData(
                weight = BigDecimal.valueOf(180),
                height = BigDecimal.valueOf(100),
                bodySurface = BigDecimal.valueOf(23),
                age = BigInteger.valueOf(12),
                sex = "01"
            ),
            oncologicalDiagnosisRadio = OncologicalRadioDiagnosis(
                radioDiagnosis = OncologicalDiagnosis(
                    diagnosisDate = LocalDate.now(),
                    diagnosisCID = listOf("C50"),
                    local = "Teste",
                    purpose = "Teste",
                    ecog = "Teste"
                )
            ),
            numberFields = BigInteger.valueOf(1),
            fieldDose = BigInteger.valueOf(1),
            totalDose = BigInteger.valueOf(1),
            numberDays = BigInteger.valueOf(1),
            expectedStartDate = LocalDate.now(),

            )

        val expectedResponse = AutorizacaoProcedimentoWS()

        coEvery {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(any())
        } returns expectedResponse

        client.createGuiaWithRadiotherapy(createGuiaRequest, radioRequest)

        val transactionType = DmTipoTransacao.valueOf(TissTransactionTypeEnum.SOLICITACAO_PROCEDIMENTOS.toString())
        coVerifyOnce {
            createGuiaWebService.tissSolicitacaoProcedimentoPort.tissSolicitacaoProcedimentoOperation(
                match {
                    it.cabecalho.identificacaoTransacao.tipoTransacao == transactionType &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.procedimentosSolicitados.first().procedimento.codigoProcedimento == createGuiaRequest.procedures[0].code &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.dadosBeneficiario.numeroCarteira == createGuiaRequest.beneficiary.nationalId &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.dadosSolicitante.contratadoSolicitante.codigoPrestadorNaOperadora == TissTransactionHeaderBuilder.genericRDA &&
                            it.solicitacaoProcedimento.solicitacaoSPSADT.anexoClinico.solicitacaoRadioterapia.doseCampo == radioRequest.fieldDose
                }
            )
        }
    }
}
