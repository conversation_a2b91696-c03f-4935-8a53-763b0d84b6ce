package br.com.alice.exec.indicator.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdate
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdateStatus
import br.com.alice.exec.indicator.models.ProcessingResourceBundleSpecialtyPricingUpdateResponse
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyPricingUpdateHistoryWithCount
import br.com.alice.filevault.models.FileType
import com.github.kittinunf.result.Result
import java.io.File
import java.time.LocalDate
import java.util.UUID

@RemoteService
interface ResourceSpecialtyPricingCSVService : Service {

    override val namespace get() = "exec_indicator"
    override val serviceName get() = "resource_specialty_pricing_csv"

    suspend fun generate(resourceBundleSpecialtyIds: List<UUID>): Result<CSVGenerationResponse, Throwable>

    suspend fun generateFailedLinesFile(
        resourceBundleSpecialtyPricingUpdateId: UUID,
    ): Result<CSVGenerationResponse, Throwable>

    suspend fun getProcessingResourceBundleSpecialtyPricingUpdate(): Result<ProcessingResourceBundleSpecialtyPricingUpdateResponse, Throwable>

    suspend fun getPricingUpdateHistory(
        filters: PricingUpdateHistoryFilters,
        range: IntRange,
    ): Result<ResourceBundleSpecialtyPricingUpdateHistoryWithCount, Throwable>

    suspend fun uploadPriceChanges(
        request: UploadPriceChangesRequest,
    ): Result<ResourceBundleSpecialtyPricingUpdate, Throwable>
}

data class CSVGenerationResponse(
    val fileName: String,
    val bytes: ByteArray
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as CSVGenerationResponse

        if (fileName != other.fileName) return false
        if (!bytes.contentEquals(other.bytes)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = fileName.hashCode()
        result = 31 * result + bytes.contentHashCode()
        return result
    }
}

data class PricingUpdateHistoryFilters(
    val status: ResourceBundleSpecialtyPricingUpdateStatus? = null,
    val startDate: LocalDate? = null,
    val endDate: LocalDate? = null,
)

data class UploadPriceChangesRequest(
    val content: ByteArray,
    val fileType: FileType,
    val pricesBeginAt: LocalDate,
    val staffId: UUID,
    val fileName: String,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as UploadPriceChangesRequest

        if (!content.contentEquals(other.content)) return false
        if (fileType != other.fileType) return false
        if (pricesBeginAt != other.pricesBeginAt) return false
        if (staffId != other.staffId) return false
        if (fileName != other.fileName) return false

        return true
    }

    override fun hashCode(): Int {
        var result = content.contentHashCode()
        result = 31 * result + fileType.hashCode()
        result = 31 * result + pricesBeginAt.hashCode()
        result = 31 * result + staffId.hashCode()
        result = 31 * result + fileName.hashCode()
        return result
    }
}
