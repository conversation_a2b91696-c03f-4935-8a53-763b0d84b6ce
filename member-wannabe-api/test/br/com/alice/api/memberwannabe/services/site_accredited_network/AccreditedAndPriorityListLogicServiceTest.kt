package br.com.alice.api.memberwannabe.services.site_accredited_network

import br.com.alice.atlas.model.Accredited
import br.com.alice.atlas.model.AccreditedCategory
import br.com.alice.atlas.services.AccreditedNetworkFlagshipService
import br.com.alice.atlas.services.AccreditedService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.SiteAccreditedNetworkFlagship
import br.com.alice.data.layer.models.SiteAccreditedNetworkFlagshipData
import br.com.alice.data.layer.models.SiteAccreditedNetworkFlagshipTypes
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals

class AccreditedAndPriorityListLogicServiceTest: MockedTestHelper() {
    private val atlasAccreditedService = mockk<AccreditedService>()
    private val accreditedNetworkFlagshipService = mockk<AccreditedNetworkFlagshipService>()
    private val accreditedFilterService = mockk<AccreditedFilterService>()

    private val accreditedAndPriorityListLogicService = AccreditedAndPriorityListLogicService(
        atlasAccreditedService,
        accreditedNetworkFlagshipService,
        accreditedFilterService,
    )

    private val priorityId = RangeUUID.generate()
    private val priorityId2 = RangeUUID.generate()
    private val accredited = Accredited(
        id = RangeUUID.generate(),
        name = "Hospital",
        category = AccreditedCategory.HOSPITAL,
        referencedId = priorityId,
    )
    private val accredited2 = Accredited(
        id = RangeUUID.generate(),
        name = "Hospital 2",
        category = AccreditedCategory.HOSPITAL,
        referencedId = RangeUUID.generate(),
    )
    private val accredited3 = Accredited(
        id = RangeUUID.generate(),
        name = "Lab 1",
        category = AccreditedCategory.LABORATORY,
        referencedId = priorityId2,
    )
    private val placeId = "ChIJ0WGkg4FEzpQRrlsz_whLqZs"
    private val categories = listOf(
        AccreditedCategory.HOSPITAL,
    )
    private val specialityId = RangeUUID.generate()
    private val range = IntRange(0, 9)
    private val radiusInMeters = 1000
    private val lat = "123.456"
    private val lon = "789.012"
    private val siteAccreditedNetworkId = RangeUUID.generate()
    private val filter = AccreditedService.Filter(
        geoLocation = AccreditedService.GeoLocationFilter(
            radiusInMeters = radiusInMeters,
            latitude = lat,
            longitude = lon,
        ),
        siteAccreditedNetwork = null,
        categories = categories,
        specialtyId = specialityId,
        providerName = null,
        referencedIds = null,
        rejectedReferencedIds = null,
        limit = range.count(),
        offset = range.first,
    )

    @Test
    fun `#getPrioritizedAccreditedList should return the prioritized list using flagship service when enabled and flagships are returned`() = runBlocking {
        val range = 0..2
        val flagships = SiteAccreditedNetworkFlagship(
            data = listOf(
                SiteAccreditedNetworkFlagshipData(
                    providerUnitId = priorityId,
                    type = SiteAccreditedNetworkFlagshipTypes.HOSPITAL,
                )
            ),
            googlePlaceId = placeId,
        )
        val required = listOf(accredited, accredited2)

        // Given
        coEvery {
            accreditedFilterService.createFilter(
                siteAccreditedNetworkId = siteAccreditedNetworkId,
                categories = categories,
                specialityId = specialityId,
                lat = lat,
                lon = lon,
                radiusInMeters = radiusInMeters,
                limit = null,
                offset = null,
            )
        } returns filter.success()
        coEvery { accreditedNetworkFlagshipService.getFlagship(placeId) } returns flagships.success()
        coEvery { atlasAccreditedService.getByReferencedIds(listOf(priorityId)) } returns listOf(accredited).success()

        // When
        val result = accreditedAndPriorityListLogicService.getPrioritizedAccreditedList(
            range,
            listOf(accredited, accredited2),
            placeId,
            categories,
            specialityId = specialityId,
            siteAccreditedNetworkId = siteAccreditedNetworkId,
            lat = lat,
            lon = lon,
            radiusInMeters = radiusInMeters,
        ).get()

        // Then
        assertEquals(required, result)

        coVerifyOnce { accreditedNetworkFlagshipService.getFlagship(placeId) }
        coVerifyOnce { atlasAccreditedService.getByReferencedIds(any()) }
    }

    @Test
    fun `#getPrioritizedAccreditedList should return the prioritized list sorted correctly using flagship service when enabled and flagships are returned`() = runBlocking {
        val providerUnitId1 = RangeUUID.generate()
        val providerUnitId2 = RangeUUID.generate()

        val range = 0..2
        val flagships = SiteAccreditedNetworkFlagship(
            data = listOf(
                SiteAccreditedNetworkFlagshipData(
                    providerUnitId = providerUnitId2,
                    type = SiteAccreditedNetworkFlagshipTypes.HOSPITAL,
                ),
                SiteAccreditedNetworkFlagshipData(
                    providerUnitId = providerUnitId1,
                    type = SiteAccreditedNetworkFlagshipTypes.HOSPITAL,
                ),
            ),
            googlePlaceId = placeId,
        )
        val accredited1 = Accredited(
            id = RangeUUID.generate(),
            name = "Hospital",
            category = AccreditedCategory.HOSPITAL,
            referencedId = providerUnitId1,
        )
        val accredited2 = Accredited(
            id = RangeUUID.generate(),
            name = "Specialist",
            category = AccreditedCategory.HOSPITAL,
            referencedId = providerUnitId2,
        )
        val required = listOf(accredited2, accredited1)

        // Given
        coEvery {
            accreditedFilterService.createFilter(
                siteAccreditedNetworkId = siteAccreditedNetworkId,
                categories = categories,
                specialityId = specialityId,
                lat = lat,
                lon = lon,
                radiusInMeters = radiusInMeters,
                limit = null,
                offset = null,
            )
        } returns filter.success()
        coEvery { accreditedNetworkFlagshipService.getFlagship(placeId) } returns flagships.success()
        coEvery { atlasAccreditedService.getByReferencedIds(listOf(providerUnitId2, providerUnitId1)) } returns listOf(accredited1, accredited2).success()

        // When
        val result = accreditedAndPriorityListLogicService.getPrioritizedAccreditedList(
            range,
            listOf(accredited1, accredited2),
            placeId,
            categories,
            specialityId = specialityId,
            siteAccreditedNetworkId = siteAccreditedNetworkId,
            lat = lat,
            lon = lon,
            radiusInMeters = radiusInMeters,
        ).get()

        // Then
        assertEquals(required, result)

        coVerifyOnce { accreditedNetworkFlagshipService.getFlagship(placeId) }
        coVerifyOnce { atlasAccreditedService.getByReferencedIds(any()) }
    }

    @Test
    fun `#getPrioritizedAccreditedList should return the original list when flagship service is enabled but no flagships are returned`() = runBlocking {
        val range = 0..2
        val required = listOf(accredited, accredited2)

        // Given
        coEvery { accreditedNetworkFlagshipService.getFlagship(placeId) } returns NotFoundException().failure()

        // When
        val result = accreditedAndPriorityListLogicService.getPrioritizedAccreditedList(
            range,
            listOf(accredited, accredited2),
            placeId,
            categories,
            specialityId = specialityId,
            siteAccreditedNetworkId = siteAccreditedNetworkId,
            lat = lat,
            lon = lon,
            radiusInMeters = radiusInMeters,
        ).get()

        // Then
        assertEquals(required, result)

        coVerifyOnce { accreditedNetworkFlagshipService.getFlagship(placeId) }
    }
}
