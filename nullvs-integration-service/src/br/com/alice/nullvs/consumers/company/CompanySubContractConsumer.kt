package br.com.alice.nullvs.consumers.company

import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.events.CompanySubContractCreatedEvent
import br.com.alice.business.events.CompanySubContractUpdatedEvent
import br.com.alice.common.extensions.foldError
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.moneyin.client.InvoiceItemService
import br.com.alice.moneyin.event.InvoiceItemCreatedEvent
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.consumers.AutoRetryableConsumer
import br.com.alice.nullvs.events.NullvsCompanySubContractActivatedEvent
import br.com.alice.nullvs.events.NullvsCompanySubContractBatchRequestEvent
import br.com.alice.nullvs.events.NullvsSyncCompanySubcontractRequestEvent
import br.com.alice.nullvs.exceptions.ContractDoesNotHaveStartAtDate
import br.com.alice.nullvs.exceptions.DependsOnModelException
import br.com.alice.nullvs.exceptions.ExternalClientNotFound
import br.com.alice.nullvs.exceptions.ExternalCompanyContractNotFound
import br.com.alice.nullvs.exceptions.ExternalCompanySubContractNotFound
import br.com.alice.nullvs.exceptions.NatureRequiredException
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.services.internals.CompanyInfoCacheService
import br.com.alice.nullvs.services.internals.TotvsCompanyIntegrationService
import br.com.alice.nullvs.services.internals.dependency.NullvsDependencyService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDate

class CompanySubContractConsumer(
    private val totvsCompanyIntegrationService: TotvsCompanyIntegrationService,
    private val kafkaProducerService: KafkaProducerService,
    private val contractService: CompanyContractService,
    private val invoiceItemService: InvoiceItemService,
    private val nullvsDependencyService: NullvsDependencyService,
    private val companyInfoCacheService: CompanyInfoCacheService,
) : AutoRetryableConsumer(
    ExternalCompanySubContractNotFound::class
) {
    suspend fun syncSubcontractCreated(event: CompanySubContractCreatedEvent) = generateNullvsRequestFromEvent(event)

    suspend fun syncSubcontractUpdated(event: CompanySubContractUpdatedEvent) =
        generateNullvsRequestFromEvent(event)

    suspend fun syncSubcontract(event: NullvsSyncCompanySubcontractRequestEvent) = withSubscribersEnvironment {
        val subcontract = event.payload.subcontract

        val metadata = Meta(
            eventId = event.messageId,
            eventName = event.name,
            internalModelName = InternalModelType.SUBCONTRACT,
            internalId = subcontract.id,
            externalModelName = ExternalModelType.SUBCONTRACT,
            integratedAt = event.eventDate,
            originalTopic = "backfill-sync",
            integrationEventName = NullvsCompanySubContractBatchRequestEvent.name,
        )

        logger.info(
            "Creating NullvsCompanySubContractBatchRequestEvent from NullvsSyncCompanySubContractRequestEvent",
            "metadata" to metadata,
        )
        when (event.payload.action) {
            NullvsActionType.CREATE -> totvsCompanyIntegrationService.createSubContract(metadata, subcontract)
            NullvsActionType.UPDATE -> totvsCompanyIntegrationService.updateSubContract(metadata, subcontract)
            else -> logger.info("Payload action invalid for CompanySubcontractSync")
                .let { Exception("Payload action invalid for CompanySubcontractSync").failure() }
        }
            .map {
                logger.info(
                    "NullvsCompanySubContractBatchRequestEvent generated",
                    "payload" to it
                )

                kafkaProducerService.produce(NullvsCompanySubContractBatchRequestEvent(it))

                true
            }.foldError(
                ExternalCompanyContractNotFound::class to { false.success() },
                DependsOnModelException::class to { error ->
                    event.payload.isFromDependencySystem?.takeIf { it }
                        ?.let { AutoRetryableException(error).failure() }
                        ?: false.success()
                },
            )
    }

    suspend fun syncInvoiceItemFromSubcontract(event: CompanySubContractUpdatedEvent) = withSubscribersEnvironment {
        val subcontract = event.payload.subcontract

        if (subcontract.externalId != null) {
            val contract = contractService.get(subcontract.contractId).get()
            if (contract.startedAt != null && LocalDate.now() >= contract.startedAt) {
                logger.info("Sync invoice items from active subcontract", "subcontract" to subcontract)
                invoiceItemService.listInvoiceItemsByCompanyAndSubcontract(subcontract.companyId, subcontract.id)
                    .mapEach { invoiceItem ->
                        kafkaProducerService.produce(InvoiceItemCreatedEvent(invoiceItem))
                    }
            }
        }
        true.success()
    }

    private suspend fun <T> generateNullvsRequestFromEvent(event: NotificationEvent<T>) = withSubscribersEnvironment {
        val subcontract = when (event) {
            is CompanySubContractCreatedEvent -> event.payload.subcontract
            is CompanySubContractUpdatedEvent -> event.payload.subcontract
            else -> null
        }!!

        logger.info(
            "Consuming event ${event.name}",
            "subcontract" to subcontract,
        )

        val metadata = Meta(
            eventId = event.messageId,
            eventName = event.name,
            internalModelName = InternalModelType.SUBCONTRACT,
            internalId = subcontract.id,
            externalModelName = ExternalModelType.SUBCONTRACT,
            integratedAt = event.eventDate,
            originalTopic = event.name,
            integrationEventName = NullvsCompanySubContractBatchRequestEvent.name,
        )

        val request = if (event is CompanySubContractCreatedEvent) {
            totvsCompanyIntegrationService.createSubContract(metadata, subcontract)
        } else {
            totvsCompanyIntegrationService.updateSubContract(
                metadata,
                subcontract,
                (event as CompanySubContractUpdatedEvent).payload.shouldUpdateMemberPriceListing
            )
        }

        request.map {
            logger.info("Nullvs payload generated", "payload" to it)
            kafkaProducerService.produce(NullvsCompanySubContractBatchRequestEvent(it))

            true
        }.foldError(
            ExternalClientNotFound::class to {
                logger.info(
                    "The external id is not found at billing accountable party",
                    "subcontract_id" to subcontract.id,
                    "billing_accountable_party_id" to subcontract.billingAccountablePartyId,
                    "is_billing_level" to subcontract.isBillingLevel,
                    "error_message" to it.message,
                )
                false.success()
            },
            ExternalCompanyContractNotFound::class to {
                logger.info(
                    "The external id is not found at company subcontract",
                    "subcontract_id" to subcontract.id,
                    "error_message" to it.message,
                )

                false.success()
            },
            NatureRequiredException::class to {
                logger.info(
                    "The nature is required to company subcontract",
                    "subcontract_id" to subcontract.id,
                    "error_message" to it.message,
                )

                false.success()
            },
            ContractDoesNotHaveStartAtDate::class to {
                logger.info(
                    "The contract does not have start at date",
                    "subcontract_id" to subcontract.id,
                    "error_message" to it.message,
                )

                false.success()
            },
            DependsOnModelException::class to { false.success() }
        )
    }

    private fun shouldUseNullvsDependencySystem() =
        FeatureService.get(FeatureNamespace.NULLVS, "use_dependency_service", true)

    suspend fun reprocessSubcontractDependencies(event: NullvsCompanySubContractActivatedEvent) =
        withSubscribersEnvironment {
            if (shouldUseNullvsDependencySystem()) {
                logger.info(
                    "NullvsCompanySubContractActivatedEvent::reprocessContractDependencies - reprocessing subcontract dependencies",
                )

                companyInfoCacheService.invalidate(event.payload.companySubContractId)
                    .flatMap {
                        nullvsDependencyService.reprocessItsDependents(
                            event.payload.companySubContractId,
                            InternalModelType.SUBCONTRACT,
                        )
                    }
            } else {
                logger.info(
                    "NullvsCompanySubContractActivatedEvent::reprocessContractDependencies - disabled by FF.use_dependency_service",
                )

                true.success()
            }
        }
}
