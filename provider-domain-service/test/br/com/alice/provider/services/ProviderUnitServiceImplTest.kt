package br.com.alice.provider.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.service.data.dsl.and
import br.com.alice.coverage.client.AddressService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.MedicalSpecialtyProfile
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.StructuredAddressReferenceModel
import br.com.alice.data.layer.services.ProviderUnitModelDataService
import br.com.alice.provider.client.ProviderUnitFilter
import br.com.alice.provider.converters.modelConverters.toModel
import br.com.alice.provider.model.ProviderUnitCreatedEvent
import br.com.alice.provider.model.ProviderUnitUpdatedEvent
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDateTime.now
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ProviderUnitServiceImplTest {
    private val data: ProviderUnitModelDataService = mockk()
    private val addressService: AddressService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val service = ProviderUnitServiceImpl(
        data,
        addressService,
        kafkaProducerService
    )

    @AfterTest
    fun clear() = clearAllMocks()

    private val providerUnit = TestModelFactory.buildProviderUnit()
    private val structuredAddress = TestModelFactory.buildStructuredAddress(
        referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT,
        referencedModelId = providerUnit.id
    )
    private val medicalSpecialtyProfile =
        listOf(MedicalSpecialtyProfile(RangeUUID.generate()), MedicalSpecialtyProfile(RangeUUID.generate()))
    private val specialty = medicalSpecialtyProfile.map { it.specialtyId }
    private val defaultRange = IntRange(0, 19)

    @Test
    fun `#getByName - calls dataservice`() = runBlocking {
        coEvery { data.find(any()) } returns listOf(providerUnit.toModel()).success()
        val result = service.getByName("name")
        assertThat(result).isSuccessWithData(listOf(providerUnit))

        coVerify { data.find(queryEq { where { name.like("name").and(status.eq(Status.ACTIVE)) } }) }
    }

    @Test
    fun `#get - calls dataservice`() = runBlocking {
        coEvery { data.get(providerUnit.id) } returns providerUnit.toModel().success()
        coEvery {
            addressService.findByReferencedObject(
                providerUnit.id,
                StructuredAddressReferenceModel.PROVIDER_UNIT
            )
        } returns structuredAddress.success()
        val result = service.get(providerUnit.id)
        assertThat(result).isSuccessWithData(providerUnit.copy(address = structuredAddress))
    }

    @Test
    fun `#addWithAddress - calls dataservice`() = runBlocking {
        val unit = providerUnit.copy(cnpj = "06.125.296/0001-53")
        coEvery { data.add(unit.toModel()) } returns unit.toModel().success()
        coEvery { addressService.upsert(structuredAddress) } returns structuredAddress.success()
        coEvery { kafkaProducerService.produce(ProviderUnitCreatedEvent(unit.copy(address = structuredAddress))) } returns
                ProducerResult(now(), "", 1)
        val result = service.addWithAddress(unit, structuredAddress)
        assertThat(result).isSuccessWithData(unit.copy(address = structuredAddress))

        coVerifyOnce {
            data.add(any())
            kafkaProducerService.produce(any())
        }
    }

    @Test
    fun `#updateWithAddress - calls dataservice`() = runBlocking {
        val unit = providerUnit.copy(cnpj = "06.125.296/0001-53")
        coEvery { data.update(unit.toModel()) } returns unit.toModel().success()
        coEvery { addressService.upsert(structuredAddress) } returns structuredAddress.success()
        coEvery { kafkaProducerService.produce(ProviderUnitUpdatedEvent(unit.copy(address = structuredAddress))) } returns
                ProducerResult(now(), "", 1)
        val result = service.updateWithAddress(unit, structuredAddress)
        assertThat(result).isSuccessWithData(unit.copy(address = structuredAddress))

        coVerifyOnce {
            data.update(any())
            kafkaProducerService.produce(any())
        }
    }

    @Test
    fun `#getByType - calls dataservice`() = runBlocking {
        val type = providerUnit.type
        coEvery { data.find(any()) } returns listOf(providerUnit.toModel()).success()
        val result = service.getByType(type)
        assertThat(result).isSuccessWithData(listOf(providerUnit))

        coVerify { data.find(queryEq { where { this.type.eq(type).and(status.eq(Status.ACTIVE)) } }) }
    }

    @Test
    fun `#getActiveById - calls dataservice`() = runBlocking {
        coEvery { data.findOne(queryEq { where { this.id.eq(providerUnit.id) and this.status.eq(Status.ACTIVE) } }) } returns providerUnit.toModel().success()
        coEvery {
            addressService.findByReferencedObject(
                providerUnit.id,
                StructuredAddressReferenceModel.PROVIDER_UNIT
            )
        } returns structuredAddress.success()

        val result = service.getActiveById(providerUnit.id)
        assertThat(result).isSuccessWithData(providerUnit.copy(address = structuredAddress))

        coVerify { data.findOne(any()) }
        coVerify { addressService.findByReferencedObject(any(), any()) }
    }

    @Test
    fun `#getActiveAndShowOnScheduler - calls dataservice`() = runBlocking {
        coEvery {
            data.find(queryEq {
                where { this.status.eq(Status.ACTIVE) and this.showOnScheduler.eq(true) }
                    .orderBy { this.name }
                    .offset { 0 }
                    .limit { 2 }
            })
        } returns listOf(providerUnit.toModel()).success()
        coEvery {
            addressService.findByReferencedObject(
                providerUnit.id,
                StructuredAddressReferenceModel.PROVIDER_UNIT
            )
        } returns structuredAddress.success()

        val result = service.getActiveAndShowOnScheduler(IntRange(0, 1), null)
        assertThat(result).isSuccessWithData(listOf(providerUnit.copy(address = structuredAddress)))

        coVerifyOnce { data.find(any()) }
        coVerifyOnce { addressService.findByReferencedObject(any(), any()) }
    }

    @Test
    fun `#getActiveAndShowOnScheduler - calls dataservice with query`() = runBlocking {
        coEvery {
            data.find(queryEq {
                where { this.status.eq(Status.ACTIVE) and this.showOnScheduler.eq(true) and searchTokens.search("q") }
                    .orderBy { this.name }
            })
        } returns listOf(providerUnit.toModel()).success()
        coEvery {
            addressService.findByReferencedObject(
                providerUnit.id,
                StructuredAddressReferenceModel.PROVIDER_UNIT
            )
        } returns structuredAddress.success()

        val result = service.getActiveAndShowOnScheduler(null, "q")
        assertThat(result).isSuccessWithData(listOf(providerUnit.copy(address = structuredAddress)))

        coVerifyOnce { data.find(any()) }
        coVerifyOnce { addressService.findByReferencedObject(any(), any()) }
    }

    @Test
    fun `#getByIds - calls dataservice`() = runBlocking {
        val list = listOf(RangeUUID.generate())
        coEvery { data.find(any()) } returns listOf(providerUnit.toModel()).success()
        val result = service.getByIds(list, false)
        assertThat(result).isSuccessWithData(listOf(providerUnit))

        coVerify { data.find(queryEq { where { id.inList(list).and(status.eq(Status.ACTIVE)) } }) }
    }

    @Test
    fun `#getByIdsWithAddress - calls dataservice`() = runBlocking {
        val list = listOf(providerUnit.id)
        val structuredAddress = TestModelFactory.buildStructuredAddress(
            referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT,
            referencedModelId = providerUnit.id
        )
        coEvery { data.find(any()) } returns listOf(providerUnit.toModel()).success()
        coEvery { addressService.findByReferencedModelIds(list) } returns listOf(structuredAddress).success()

        val result = service.getByIds(list, true)
        assertThat(result).isSuccessWithData(listOf(providerUnit.copy(address = structuredAddress)))

        coVerify { data.find(any()) }
        coVerify { addressService.findByReferencedModelIds(any()) }
    }

    @Test
    fun `#getByAdministrativeEmployeeId - calls dataservice with address`() = runBlocking {
        val administrativeStaffId = RangeUUID.generate()
        coEvery { data.find(any()) } returns listOf(providerUnit.toModel()).success()
        coEvery { addressService.findByReferencedModelIds(listOf(providerUnit.id)) } returns listOf(structuredAddress).success()

        val result = service.getByAdministrativeStaffId(administrativeStaffId, true)
        assertThat(result).isSuccessWithData(listOf(providerUnit.copy(address = structuredAddress)))

        coVerify {
            data.find(queryEq {
                where {
                    administrativeStaff.contains(administrativeStaffId).and(status.eq(Status.ACTIVE))
                }
            })
        }
        coVerify { addressService.findByReferencedModelIds(any()) }
    }

    @Test
    fun `#getByClinicalEmployeeId with StaffId - calls dataservice`() = runBlocking {
        val staffId = RangeUUID.generate()

        coEvery { data.find(any()) } returns listOf(providerUnit.toModel()).success()

        val result = service.getByClinicalStaffId(staffId, false)
        assertThat(result).isSuccessWithData(listOf(providerUnit))

        coVerify {
            data.find(queryEq {
                where {
                    clinicalStaffId.contains(staffId).and(status.eq(Status.ACTIVE))
                }
            })
        }
    }

    @Test
    fun `#getByClinicalEmployeeId with StaffId - calls dataservice with address`() = runBlocking {
        val staffId = RangeUUID.generate()

        coEvery { data.find(any()) } returns listOf(providerUnit.toModel()).success()
        coEvery { addressService.findByReferencedModelIds(listOf(providerUnit.id)) } returns listOf(structuredAddress).success()

        val result = service.getByClinicalStaffId(staffId, true)
        assertThat(result).isSuccessWithData(listOf(providerUnit.copy(address = structuredAddress)))

        coVerify {
            data.find(queryEq {
                where {
                    clinicalStaffId.contains(staffId).and(status.eq(Status.ACTIVE))
                }
            })
        }
        coVerify { addressService.findByReferencedModelIds(any()) }
    }

    @Test
    fun `#getByProviderIdsBrandWithTypes should call dataservice`() = runBlocking {
        val types = listOf(providerUnit.type)
        val brand = Brand.ALICE
        val providerIds = listOf(RangeUUID.generate())

        coEvery {
            data.find(queryEq {
                where {
                    this.providerId.inList(providerIds) and
                            status.eq(Status.ACTIVE) and
                            this.type.inList(types) and
                            this.brand.inList(listOf(brand, Brand.ALICE_DUQUESA))
                }
            })
        } returns listOf(providerUnit.toModel()).success()

        val result = service.getByProviderIdsBrandWithTypes(providerIds, brand, types, false)

        assertThat(result).isSuccessWithData(listOf(providerUnit))

        coVerifyOnce { data.find(any()) }
    }


    @Test
    fun `#getByProviderIdsBrandWithTypes should call dataservice with address`() = runBlocking {
        val types = listOf(providerUnit.type)
        val brand = Brand.ALICE
        val providerIds = listOf(RangeUUID.generate())

        coEvery {
            data.find(queryEq {
                where {
                    this.providerId.inList(providerIds) and status.eq(Status.ACTIVE) and
                            this.type.inList(types) and
                            this.brand.inList(listOf(brand, Brand.ALICE_DUQUESA))
                }
            })
        } returns listOf(providerUnit.toModel()).success()
        coEvery { addressService.findByReferencedModelIds(listOf(providerUnit.id)) } returns listOf(structuredAddress).success()

        val result = service.getByProviderIdsBrandWithTypes(providerIds, brand, types, true)

        assertThat(result).isSuccessWithData(listOf(providerUnit.copy(address = structuredAddress)))

        coVerifyOnce { data.find(any()) }
        coVerifyOnce { addressService.findByReferencedModelIds(any()) }
    }

    @Test
    fun `#getByProviderIdsWithTypes should call dataservice`() = runBlocking {
        val types = listOf(providerUnit.type)
        val providerIds = listOf(RangeUUID.generate())

        coEvery {
            data.find(queryEq {
                where { this.providerId.inList(providerIds).and(this.type.inList(types)).and(status.eq(Status.ACTIVE)) }
            })
        } returns listOf(providerUnit.toModel()).success()

        val result = service.getByProviderIdsWithTypes(providerIds, types, false)

        assertThat(result).isSuccessWithData(listOf(providerUnit))

        coVerifyOnce { data.find(any()) }
        coVerifyNone { addressService.findByReferencedModelIds(any()) }
    }

    @Test
    fun `#getByProviderIdsWithTypes should call dataservice with address`() = runBlocking {
        val types = listOf(providerUnit.type)
        val providerIds = listOf(RangeUUID.generate())

        coEvery {
            data.find(queryEq {
                where { this.providerId.inList(providerIds).and(this.type.inList(types)).and(status.eq(Status.ACTIVE)) }
            })
        } returns listOf(providerUnit.toModel()).success()
        coEvery { addressService.findByReferencedModelIds(listOf(providerUnit.id)) } returns listOf(structuredAddress).success()

        val result = service.getByProviderIdsWithTypes(providerIds, types, true)

        assertThat(result).isSuccessWithData(listOf(providerUnit.copy(address = structuredAddress)))

        coVerifyOnce { data.find(any()) }
        coVerifyOnce { addressService.findByReferencedModelIds(any()) }
    }

    @Test
    fun `#getByProviderIdsAndTypes should get by provider ids and type`() = runBlocking<Unit> {
        val providerIds = listOf(RangeUUID.generate(), RangeUUID.generate())
        val expectedProviderUnits = listOf(
            TestModelFactory.buildProviderUnit(
                type = ProviderUnit.Type.CLINICAL,
                providerId = providerIds[0],
            ),
            TestModelFactory.buildProviderUnit(
                type = ProviderUnit.Type.CLINICAL,
                providerId = providerIds[1],
            ),
        )
        val searchTerm = null
        val range = IntRange(0, 2)

        coEvery {
            data.find(queryEq {
                where {
                    this.providerId.inList(providerIds) and
                            this.type.inList(listOf(ProviderUnit.Type.CLINICAL)).and(status.eq(Status.ACTIVE))
                }.orderBy { this.name }.offset { range.first }.limit { range.count() }
            })
        } returns expectedProviderUnits.map { it.toModel() }.success()

        val result = service.getByProviderIdsAndTypes(
            providerIds,
            listOf(ProviderUnit.Type.CLINICAL),
            range,
            searchTerm,
        )

        assertThat(result).isEqualTo(expectedProviderUnits.success())
    }

    @Test
    fun `#getByProviderIdsAndTypes should get by provider ids and type with search term`() = runBlocking<Unit> {
        val providerIds = listOf(RangeUUID.generate(), RangeUUID.generate())
        val expectedProviderUnits = listOf(
            TestModelFactory.buildProviderUnit(
                name = "abc",
                type = ProviderUnit.Type.CLINICAL,
                providerId = providerIds[0],
            ),
            TestModelFactory.buildProviderUnit(
                name = "defg",
                type = ProviderUnit.Type.CLINICAL,
                providerId = providerIds[1],
            ),
        )
        val searchTerm = "ab"
        val range = IntRange(0, 2)

        coEvery {
            data.find(queryEq {
                where {
                    this.providerId.inList(providerIds) and
                            this.type.inList(listOf(ProviderUnit.Type.CLINICAL))
                                .and(status.eq(Status.ACTIVE))
                                .and(this.name.search(searchTerm))
                }.orderBy { this.name }.offset { range.first }.limit { range.count() }
            })
        } returns listOf(expectedProviderUnits[0].toModel()).success()

        val result = service.getByProviderIdsAndTypes(
            providerIds,
            listOf(ProviderUnit.Type.CLINICAL),
            range,
            searchTerm,
        )

        assertThat(result).isEqualTo(listOf(expectedProviderUnits[0]).success())
    }

    @Test
    fun `#getByProviderIdsAndType should get by provider ids and type with search term and specialist`() = runBlocking {
        val providerIds = listOf(RangeUUID.generate(), RangeUUID.generate())
        val expectedProviderUnits = listOf(
            TestModelFactory.buildProviderUnit(
                name = "abc",
                type = ProviderUnit.Type.CLINICAL,
                providerId = providerIds[0],
                medicalSpecialtyProfile = medicalSpecialtyProfile

            ),
            TestModelFactory.buildProviderUnit(
                name = "defg",
                type = ProviderUnit.Type.CLINICAL,
                providerId = providerIds[1],
                medicalSpecialtyProfile = medicalSpecialtyProfile
            ),
        )
        val searchTerm = "ab"
        val range = IntRange(0, 2)

        coEvery {
            data.find(queryEq {
                where {
                    this.providerId.inList(providerIds) and
                            this.type.inList(listOf(ProviderUnit.Type.CLINICAL))
                                .and(status.eq(Status.ACTIVE))
                                .and(this.name.search(searchTerm))
                                .and(this.medicalSpecialist.containsAnySpecialtyIds(specialty))
                }.orderBy { this.name }.offset { range.first }.limit { range.count() }
            })
        } returns listOf(expectedProviderUnits[0].toModel()).success()

        val result = service.getByProviderIdsAndTypes(
            providerIds,
            listOf(ProviderUnit.Type.CLINICAL),
            range,
            searchTerm,
            specialty
        )

        assertThat(result).isEqualTo(listOf(expectedProviderUnits[0]).success())

        coVerifyOnce { data.find(any()) }
    }

    @Test
    fun `#getByProvidersTypeAndSpecialty should get by provider ids and type, specialty and sub specialty`() =
        runBlocking {
            val providerIds = listOf(RangeUUID.generate(), RangeUUID.generate())
            val subSpecialty = listOf(RangeUUID.generate(), RangeUUID.generate())
            val expectedProviderUnits = listOf(
                TestModelFactory.buildProviderUnit(
                    name = "Clinical",
                    type = ProviderUnit.Type.CLINICAL_COMMUNITY,
                    providerId = providerIds[0],
                    medicalSpecialtyProfile = specialty.map { MedicalSpecialtyProfile(it, subSpecialty) }

                )
            )

            coEvery {
                data.find(queryEq {
                    where {
                        this.type.eq(ProviderUnit.Type.CLINICAL_COMMUNITY)
                            .and(status.eq(Status.ACTIVE))
                            .and(this.providerId.inList(providerIds))
                            .and(this.medicalSpecialist.containsAnySpecialtyIds(specialty))
                            .and(this.medicalSpecialist.containsAnySubSpecialtyIds(subSpecialty))
                    }.orderBy { this.name }
                })
            } returns listOf(expectedProviderUnits[0].toModel()).success()

            val result = service.getByProvidersTypeAndSpecialty(
                providerIds,
                ProviderUnit.Type.CLINICAL_COMMUNITY,
                specialty,
                subSpecialty
            )

            assertThat(result).isEqualTo(listOf(expectedProviderUnits[0]).success())

            coVerifyOnce { data.find(any()) }

        }

    @Test
    fun `#getByProvidersTypeAndSpecialty should get by provider ids and type, specialty`() = runBlocking {
        val providerIds = listOf(RangeUUID.generate(), RangeUUID.generate())
        val subSpecialty = listOf(RangeUUID.generate(), RangeUUID.generate())
        val expectedProviderUnits = listOf(
            TestModelFactory.buildProviderUnit(
                name = "Clinical",
                type = ProviderUnit.Type.CLINICAL_COMMUNITY,
                providerId = providerIds[0],
                medicalSpecialtyProfile = specialty.map { MedicalSpecialtyProfile(it, subSpecialty) }

            )
        )

        coEvery {
            data.find(queryEq {
                where {
                    this.type.eq(ProviderUnit.Type.CLINICAL_COMMUNITY)
                        .and(status.eq(Status.ACTIVE))
                        .and(this.providerId.inList(providerIds))
                        .and(this.medicalSpecialist.containsAnySpecialtyIds(specialty))
                }.orderBy { this.name }
            })
        } returns listOf(expectedProviderUnits[0].toModel()).success()

        val result = service.getByProvidersTypeAndSpecialty(
            providerIds,
            ProviderUnit.Type.CLINICAL_COMMUNITY,
            specialty
        )

        assertThat(result).isEqualTo(listOf(expectedProviderUnits[0]).success())

        coVerifyOnce { data.find(any()) }

    }

    @Test
    fun `#getByProvidersTypeAndSpecialty should get by provider ids, type, specialty and brand`() = runBlocking {
        val providerIds = listOf(RangeUUID.generate(), RangeUUID.generate())
        val subSpecialty = listOf(RangeUUID.generate(), RangeUUID.generate())
        val expectedProviderUnits = listOf(
            TestModelFactory.buildProviderUnit(
                name = "Clinical",
                type = ProviderUnit.Type.CLINICAL_COMMUNITY,
                providerId = providerIds[0],
                medicalSpecialtyProfile = specialty.map { MedicalSpecialtyProfile(it, subSpecialty) }

            )
        )

        coEvery {
            data.find(queryEq {
                where {
                    this.type.eq(ProviderUnit.Type.CLINICAL_COMMUNITY)
                        .and(status.eq(Status.ACTIVE))
                        .and(this.providerId.inList(providerIds))
                        .and(this.medicalSpecialist.containsAnySpecialtyIds(specialty))
                        .and(this.brand.inList(listOf(Brand.ALICE, Brand.ALICE_DUQUESA)))
                }.orderBy { this.name }
            })
        } returns listOf(expectedProviderUnits[0].toModel()).success()

        val result = service.getByProvidersTypeAndSpecialty(
            providerIds,
            ProviderUnit.Type.CLINICAL_COMMUNITY,
            specialty,
            brand = Brand.ALICE
        )

        assertThat(result).isEqualTo(listOf(expectedProviderUnits[0]).success())

        coVerifyOnce { data.find(any()) }

    }

    @Test
    fun `#getByProvidersTypeAndSpecialty should get by type and specialty`() = runBlocking {
        val providerIds = listOf(RangeUUID.generate(), RangeUUID.generate())
        val specialty = listOf(RangeUUID.generate(), RangeUUID.generate())
        val subSpecialty = listOf(RangeUUID.generate(), RangeUUID.generate())
        val expectedProviderUnits = listOf(
            TestModelFactory.buildProviderUnit(
                name = "Clinical",
                type = ProviderUnit.Type.CLINICAL_COMMUNITY,
                providerId = providerIds[0],
                medicalSpecialtyProfile = specialty.map { MedicalSpecialtyProfile(it, subSpecialty) }
            )
        )

        coEvery {
            data.find(queryEq {
                where {
                    this.type.eq(ProviderUnit.Type.CLINICAL_COMMUNITY)
                        .and(status.eq(Status.ACTIVE))
                        .and(this.medicalSpecialist.containsAnySpecialtyIds(specialty))
                }.orderBy { this.name }
            })
        } returns listOf(expectedProviderUnits[0].toModel()).success()

        val result = service.getByProvidersTypeAndSpecialty(
            emptyList(),
            ProviderUnit.Type.CLINICAL_COMMUNITY,
            specialty
        )

        assertThat(result).isEqualTo(listOf(expectedProviderUnits[0]).success())

        coVerifyOnce { data.find(any()) }
    }

    @Test
    fun `#getByProviderIdsAndSpecialties should get by provider ids and specialties`() =
        runBlocking {
            val providerIds = listOf(RangeUUID.generate(), RangeUUID.generate())
            val expectedProviderUnits = listOf(
                TestModelFactory.buildProviderUnit(
                    name = "Clinical",
                    type = ProviderUnit.Type.CLINICAL_COMMUNITY,
                    providerId = providerIds[0],
                    medicalSpecialtyProfile = specialty.map { MedicalSpecialtyProfile(it) }

                )
            )

            coEvery {
                data.find(queryEq {
                    where {
                        this.providerId.inList(providerIds)
                            .and(status.eq(Status.ACTIVE))
                            .and(this.medicalSpecialist.containsAnySpecialtyIds(specialty))
                    }
                })
            } returns listOf(expectedProviderUnits[0].toModel()).success()

            val result = service.getByProviderIdsAndSpecialties(providerIds, specialty)

            assertThat(result).isEqualTo(listOf(expectedProviderUnits[0]).success())

            coVerifyOnce { data.find(any()) }
        }

    @Test
    fun `#getByProviderIds should return units by providers ids`() =
        runBlocking {
            val providerIds = listOf(RangeUUID.generate(), RangeUUID.generate())
            val expectedProviderUnits = listOf(
                TestModelFactory.buildProviderUnit(
                    name = "Clinical",
                    type = ProviderUnit.Type.CLINICAL_COMMUNITY,
                    providerId = providerIds[0],
                    medicalSpecialtyProfile = specialty.map { MedicalSpecialtyProfile(it) }
                )
            )
            coEvery {
                data.find(queryEq {
                    where {
                        this.providerId.inList(providerIds).and(status.eq(Status.ACTIVE))
                    }
                })
            } returns expectedProviderUnits.map { it.toModel() }.success()
            val result = service.getByProviderIds(providerIds, false)

            assertThat(result).isSuccessWithData(expectedProviderUnits)

            coVerifyOnce { data.find(any()) }
            coVerifyNone { addressService.findByReferencedModelIds(any()) }
        }

    @Test
    fun `#getByProviderIds should return units by providers ids with address`() =
        runBlocking {
            val providerIds = listOf(RangeUUID.generate(), RangeUUID.generate())

            coEvery {
                data.find(queryEq {
                    where {
                        this.providerId.inList(providerIds)
                            .and(status.eq(Status.ACTIVE))
                    }
                })
            } returns listOf(providerUnit.toModel()).success()
            coEvery { addressService.findByReferencedModelIds(listOf(providerUnit.id)) } returns listOf(
                structuredAddress
            ).success()

            val result = service.getByProviderIds(providerIds, true)

            assertThat(result).isSuccessWithData(listOf(providerUnit.copy(address = structuredAddress)))

            coVerifyOnce { data.find(any()) }
            coVerifyOnce { addressService.findByReferencedModelIds(any()) }
        }

    @Test
    fun `#getByProviderIdsPaginated should return units by providers ids paginated`() =
        runBlocking {
            val providerIds = listOf(RangeUUID.generate(), RangeUUID.generate())
            val offset = 0
            val limit = 5
            val expectedProviderUnits = listOf(
                TestModelFactory.buildProviderUnit(
                    name = "Clinical",
                    type = ProviderUnit.Type.CLINICAL_COMMUNITY,
                    providerId = providerIds[0],
                    medicalSpecialtyProfile = specialty.map { MedicalSpecialtyProfile(it) }
                )
            )
            coEvery {
                data.find(queryEq {
                    where {
                        this.providerId.inList(providerIds)
                            .and(status.eq(Status.ACTIVE))
                    }.offset { offset }.limit { limit }
                })
            } returns expectedProviderUnits.map { it.toModel() }.success()
            val result = service.getByProviderIdsPaginated(providerIds, offset, limit)

            assertThat(result).isSuccessWithData(expectedProviderUnits)

            coVerifyOnce { data.find(any()) }
        }

    @Test
    fun `#getByFilterWithRange should return units with filter by type and status`() =
        runBlocking {
            val filter = ProviderUnitFilter(
                status = listOf(Status.ACTIVE, Status.INACTIVE),
                type = listOf(ProviderUnit.Type.CLINICAL_COMMUNITY)
            )
            coEvery {
                data.find(
                    queryEq {
                        where {
                            this.status.inList(filter.status).and(type.inList(filter.type))
                        }.offset { defaultRange.first }
                            .limit { defaultRange.count() }
                            .orderBy { name }
                    }
                )
            } returns listOf(providerUnit.toModel()).success()
            val result = service.getByFilterWithRange(filter, defaultRange)

            assertThat(result).isSuccessWithData(listOf(providerUnit))

            coVerifyOnce { data.find(any()) }
        }

    @Test
    fun `#getByFilterWithRange should return units with filter by ids`() =
        runBlocking {
            val filter = ProviderUnitFilter(
                ids = listOf(providerUnit.id)
            )
            coEvery {
                data.find(
                    queryEq {
                        where {
                            this.status.inList(listOf(Status.ACTIVE)).and(id.inList(filter.ids))
                        }.offset { defaultRange.first }
                            .limit { defaultRange.count() }
                            .orderBy { name }
                    }
                )
            } returns listOf(providerUnit.toModel()).success()
            val result = service.getByFilterWithRange(filter, defaultRange)

            assertThat(result).isSuccessWithData(listOf(providerUnit))

            coVerifyOnce { data.find(any()) }
        }

    @Test
    fun `#getByFilterWithRange should return units with filter by name`() =
        runBlocking {
            val filter = ProviderUnitFilter(
                searchToken = "Oswaldo"
            )
            coEvery {
                data.find(
                    queryEq {
                        where {
                            this.status.inList(listOf(Status.ACTIVE)).and(name.like(filter.searchToken!!))
                        }.offset { defaultRange.first }
                            .limit { defaultRange.count() }
                            .orderBy { name }
                    }
                )
            } returns listOf(providerUnit.toModel()).success()
            val result = service.getByFilterWithRange(filter, defaultRange)

            assertThat(result).isSuccessWithData(listOf(providerUnit))

            coVerifyOnce { data.find(any()) }
        }

    @Test
    fun `#countByFilters should return units count with filter by name`() =
        runBlocking {
            val filter = ProviderUnitFilter(
                searchToken = "Oswaldo"
            )
            coEvery {
                data.count(queryEq {
                    where {
                        this.status.inList(listOf(Status.ACTIVE)).and(name.like(filter.searchToken!!))
                    }
                })
            } returns 1.success()
            val result = service.countByFilter(filter)

            assertThat(result).isSuccessWithData(1)

            coVerifyOnce { data.count(any()) }
        }

    @Test
    fun `#countByFilters should return units count with filter by type and status`() =
        runBlocking {
            val filter = ProviderUnitFilter(
                status = listOf(Status.ACTIVE, Status.INACTIVE),
                type = listOf(ProviderUnit.Type.CLINICAL_COMMUNITY)
            )
            coEvery {
                data.count(queryEq {
                    where {
                        this.status.inList(filter.status).and(type.inList(filter.type))
                    }
                })
            } returns 1.success()
            val result = service.countByFilter(filter)

            assertThat(result).isSuccessWithData(1)

            coVerifyOnce { data.count(any()) }
        }

    @Test
    fun `#countByFilters should return units count with filter by ids`() =
        runBlocking {
            val filter = ProviderUnitFilter(
                ids = listOf(providerUnit.providerId)
            )
            coEvery {
                data.count(queryEq {
                    where {
                        this.status.inList(listOf(Status.ACTIVE)).and(id.inList(filter.ids))
                    }
                })
            } returns 1.success()
            val result = service.countByFilter(filter)

            assertThat(result).isSuccessWithData(1)

            coVerifyOnce { data.count(any()) }
        }

    @Test
    fun `#update should update provider unit and publish event`() =
        runBlocking {
            coEvery {
                data.update(providerUnit.toModel())
            } returns providerUnit.toModel().success()

            coEvery {
                kafkaProducerService.produce(ProviderUnitUpdatedEvent(providerUnit))
            } returns ProducerResult(now(), "", 1)

            val result = service.update(providerUnit)
            assertThat(result).isSuccessWithData(providerUnit)

            coVerifyOnce { data.update(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#update should update provider unit and not publish event`() =
        runBlocking {
            coEvery {
                data.update(providerUnit.toModel())
            } returns providerUnit.toModel().success()

            val result = service.update(providerUnit, false)
            assertThat(result).isSuccessWithData(providerUnit)

            coVerifyOnce { data.update(any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `existsClinicalStaffIds should return true if exists`() = runBlocking {
        val staffId = RangeUUID.generate()
        val type = ProviderUnit.Type.CLINICAL_COMMUNITY
        coEvery {
            data.exists(queryEq {
                where {
                    this.clinicalStaffId.contains(staffId) and this.type.eq(type)
                }
            })
        } returns true.success()

        val result = service.existsClinicalStaffIds(staffId, ProviderUnit.Type.CLINICAL_COMMUNITY)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { data.exists(any()) }
    }
}
