package app.nullvs_integration_service_entry_test

import data.app.nullvs_integration_service_entry
import rego.v1

test_unauth_CRU_count_NullvsIntegrationLogModel_allow if {
	{1, 2, 3, 4} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "NullvsIntegrationLogModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "NullvsIntegrationLogModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "NullvsIntegrationLogModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "NullvsIntegrationLogModel"},
		},
	]}
}

test_unauth_CRU_count_NullvsIntegrationRecordModel_allow if {
	{1, 2, 3, 4} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "NullvsIntegrationRecordModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "NullvsIntegrationRecordModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "NullvsIntegrationRecordModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "NullvsIntegrationRecordModel"},
		},
	]}
}

test_unauth_CRU_count_MemberInvoiceGroupModel_allow if {
	{1, 2, 3, 4} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
	]}
}

test_unauth_CRU_count_InvoiceLiquidationModel_allow if {
	{1, 2, 3, 4} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
	]}
}

test_unauth_CRU_count_MemberInvoiceModel_allow if {
	{1, 2, 3, 4} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
	]}
}

test_unauth_CRU_count_InvoicePaymentModel_allow if {
	{1, 2, 3, 4} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
	]}
}

test_unauth_CRU_count_PaymentDetailModel_allow if {
	{1, 2, 3, 4} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
	]}
}

test_unauth_CRU_count_BoletoPaymentDetailModel_allow if {
	{1, 2, 3, 4} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
	]}
}

test_unauth_CRU_count_SimpleCreditCardPaymentDetailModel_allow if {
	{1, 2, 3, 4} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
	]}
}

test_unauth_CRU_count_PixPaymentDetailModel_allow if {
	{1, 2, 3, 4} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
	]}
}

test_unauth_CRU_count_BolepixPaymentDetailModel_allow if {
	{1, 2, 3, 4} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
	]}
}

test_unauth_CRU_count_InvoiceItemModel_allow if {
	{1, 2, 3, 4} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
	]}
}

test_unauth_CRU_count_ResourceSignTokenModel_allow if {
	{1, 2, 3, 4} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
	]}
}

test_unauth_view_MemberModel_allow if {
	{1} == nullvs_integration_service_entry.allow with input as {"cases": [{
		"index": 1,
		"action": "view",
		"subject": {"opaType": "Unauthenticated"},
		"resource": {"opaType": "MemberModel"},
	}]}
}

test_unauth_view_create_TrackPersonABModel_allow if {
	{1, 2} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "TrackPersonABModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "TrackPersonABModel"},
		},
	]}
}

test_unauth_view_count_CompanyContract_allow if {
	{1, 2} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanyContractModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanyContractModel"},
		},
	]}
}

test_unauth_view_count_CompanySubContract_allow if {
	{1, 2} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
	]}
}

test_unauth_view_count_Company_allow if {
	{1, 2} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanyModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanyModel"},
		},
	]}
}

test_unauth_view_count_GenericFileVault_allow if {
	{1, 2} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "GenericFileVault"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "GenericFileVault"},
		},
	]}
}

test_unauth_view_count_BillingAccountablePartyModel_allow if {
	{1, 2} == nullvs_integration_service_entry.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
	]}
}
