package br.com.alice.hr.core.services.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InternalServiceErrorException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.hr.core.clients.interfaces.DataAPIClient
import br.com.alice.hr.core.clients.PopulationDataResponse
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals

class PopulationDataServiceTest {
    private val dataAPIClient: DataAPIClient = mockk()

    private val populationDataService = PopulationDataService(
        dataAPIClient,
    )
    private val companyId = RangeUUID.generate()
    private val populationDataResponse = PopulationDataResponse(
        companyId = companyId.toString(),
        companyName = "Sr. Barriga S.A.",
        activeMembers = 20,
        medianAge = 30,
        holders =
        PopulationDataResponse.GenericNamedCountPercentage(
            name = "name",
            count = 1,
            percentage = 0.90
        ),
        dependents =
        PopulationDataResponse.GenericNamedCountPercentage(
            name = "name",
            count = 1,
            percentage = 0.10
        ),
        sexDemographics = listOf(
            PopulationDataResponse.GenericNamedCountPercentage(
                name = "Masculino",
                count = 10,
                percentage = 0.50
            ),
            PopulationDataResponse.GenericNamedCountPercentage(
                name = "Feminino",
                count = 10,
                percentage = 0.50
            )
        ),
        riskClassification = listOf(
            PopulationDataResponse.GenericNamedCountPercentage(
                name = "Baixo Risco",
                count = 20,
                percentage = 1.0
            )
        ),
        healthConditions = listOf(
            PopulationDataResponse.GenericNamedCountPercentage(
                name = "Hipertensão",
                count = 10,
                percentage = 0.50
            ),
            PopulationDataResponse.GenericNamedCountPercentage(
                name = "Diabetes",
                count = 10,
                percentage = 0.50
            )
        )
    ) 

    @Test
    fun `#should get from api successfully`() = runBlocking {
        coEvery {
            dataAPIClient.getPopulationDataByCompany(companyId)
        } returns populationDataResponse

        val result = populationDataService.getPopulationData(companyId)

        assertEquals(populationDataResponse, result.get())

        coVerifyOnce { dataAPIClient.getPopulationDataByCompany(any()) }
    }

    @Test
    fun `#should raise the exception when an error occurs`(): Unit = runBlocking {
        coEvery {
            dataAPIClient.getPopulationDataByCompany(companyId)
        } throws InternalServiceErrorException("Internal Service Error")

        val result = populationDataService.getPopulationData(companyId)
        assertThat(result).isFailureOfType(InternalServiceErrorException::class)
    }
}
