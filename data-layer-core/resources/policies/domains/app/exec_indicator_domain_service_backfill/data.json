{"rules": [{"conditions": [], "allow": [{"resources": ["TotvsGuiaModel", "TussProcedureSpecialtyModel"], "actions": ["view", "create", "update"]}, {"resources": ["ProviderUnitModel", "ProviderUnitGroupModel", "HealthcareResourceModel"], "actions": ["view"]}, {"resources": ["HealthcareBundleModel"], "actions": ["view", "update"]}, {"resources": ["HealthSpecialistResourceBundleModel", "ResourceBundleSpecialtyModel", "ResourceBundleSpecialtyPricingModel"], "actions": ["view", "create", "update", "delete"]}]}, {"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["MvAuthorizedProcedureModel", "ExecutionGroupModel", "HealthEventsModel", "ExecIndicatorAuthorizerModel", "TotvsGuiaModel", "HealthCommunitySpecialistModel", "EligibilityCheckModel", "HealthcareResourceGroupModel", "HealthcareResourceModel"], "actions": ["view"]}, {"resources": ["ExecutionGroupModel", "MvAuthorizedProcedureModel", "EligibilityCheckModel", "HealthcareResourceModel"], "actions": ["update"]}, {"actions": ["view", "update", "create"], "resources": ["GlossAuthorizationInfoModel", "GuiaWithProceduresModel", "HealthcareResourceGroupAssociationModel"]}, {"actions": ["count"], "resources": ["ExecutionGroupModel"]}, {"actions": ["delete"], "resources": ["MvAuthorizedProcedureModel", "TotvsGuiaModel"]}]}]}