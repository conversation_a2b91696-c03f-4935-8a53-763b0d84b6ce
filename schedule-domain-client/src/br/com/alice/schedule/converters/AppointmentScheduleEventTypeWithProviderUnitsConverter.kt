package br.com.alice.schedule.converters

import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.AppointmentScheduleEventType
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeLocation.ON_SITE
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeLocation.REMOTE
import br.com.alice.data.layer.models.EventTypeProviderUnit
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.schedule.model.AppointmentScheduleEventTypeLocationAvailability
import br.com.alice.schedule.model.AppointmentScheduleEventTypeWithProviderUnits
import java.util.UUID

object AppointmentScheduleEventTypeWithProviderUnitsConverter :
    Converter<AppointmentScheduleEventType, AppointmentScheduleEventTypeWithProviderUnits>(
        AppointmentScheduleEventType::class,
        AppointmentScheduleEventTypeWithProviderUnits::class
    ) {

    fun convert(
        source: AppointmentScheduleEventType,
        eventTypeProviderUnits: List<EventTypeProviderUnit>,
        providerUnitMap: Map<UUID, ProviderUnit>
    ): AppointmentScheduleEventTypeWithProviderUnits {
        val providerUnitIds = eventTypeProviderUnits.mapNotNull { it.providerUnitId }.distinct()
        val locations =
            eventTypeProviderUnits.map {
                AppointmentScheduleEventTypeLocationAvailability(
                    providerUnitId = it.providerUnitId,
                    type = if (it.providerUnitId != null) ON_SITE else REMOTE,
                    duration = it.duration,
                    minimumTimeToScheduleBeforeAppointmentTime = it.minimumTimeToScheduleBeforeAppointmentTime,
                    numberOfDaysFromNowToAllowScheduling = it.numberOfDaysFromNowToAllowScheduling,
                    availableWeekDays = it.availableWeekDays,
                    availabilityStartTime = it.availabilityStartTime,
                    availabilityEndTime = it.availabilityEndTime,
                    providerUnitName = providerUnitMap[it.providerUnitId]?.name
                )
            }
        return convert(
            source,
            map(AppointmentScheduleEventTypeWithProviderUnits::providerUnitIds) from providerUnitIds,
            map(AppointmentScheduleEventTypeWithProviderUnits::locations) from locations
        )
    }

}
