package br.com.alice.moneyin.services

import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.moneyin.ServiceConfig
import br.com.alice.moneyin.client.MoneyInResourceSignTokenService
import br.com.alice.moneyin.client.PortalUrlGeneratorService
import br.com.alice.moneyin.models.InvoicePaymentWithPortalUrl
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.lift
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class PortalUrlGeneratorServiceImpl(
    private val moneyInResourceSignTokenService: MoneyInResourceSignTokenService,
) : PortalUrlGeneratorService {
    override suspend fun mountPortalUrl(invoicePaymentId: UUID): Result<String, Throwable> =
        moneyInResourceSignTokenService.getSignTokenForMoneyInBff(invoicePaymentId)
            .map { signToken ->
                "${ServiceConfig.Payment.invoiceUrl()}/${invoicePaymentId}?token=${signToken}"
            }

    override suspend fun mountPortalUrlForInvoicePayments(
        invoicePayments: List<InvoicePayment>
    ): Result<List<InvoicePaymentWithPortalUrl>, Throwable> =
        invoicePayments.pmap { invoicePayment ->
            mountPortalUrl(
                invoicePaymentId = invoicePayment.id
            ).map { portalUrl ->
                InvoicePaymentWithPortalUrl(
                    invoicePaymentId = invoicePayment.id,
                    portalUrl = portalUrl
                )
            }.coFoldNotFound {
                logger.error("Error generating portal URL for invoice payment: ${invoicePayment.id}", it)

                InvoicePaymentWithPortalUrl(
                    invoicePaymentId = invoicePayment.id,
                    portalUrl = null
                ).success()
            }
        }.lift()
}

