package br.com.alice.api.memberwannabe.services.site_accredited_network

import br.com.alice.atlas.model.Accredited
import br.com.alice.atlas.model.AccreditedCategory
import br.com.alice.atlas.services.AccreditedNetworkFlagshipService
import br.com.alice.atlas.services.AccreditedService
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.SiteAccreditedNetworkFlagship
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.success
import java.util.UUID

class AccreditedAndPriorityListLogicService(
    private val accreditedService: AccreditedService,
    private val accreditedNetworkFlagshipService: AccreditedNetworkFlagshipService,
    private val accreditedFilterService: AccreditedFilterService,
) {
    suspend fun getPrioritizedAccreditedList(
        range: IntRange,
        accredited: List<Accredited>,
        placeId: String,
        categories: List<AccreditedCategory>? = null,
        siteAccreditedNetworkId: UUID? = null,
        specialityId: UUID?,
        lat: String?,
        lon: String?,
        radiusInMeters: Int?,
    ): Result<List<Accredited>, Throwable> =
        priorityListLogic(
            range,
            accredited,
            placeId,
            categories,
            siteAccreditedNetworkId,
            specialityId,
            lat,
            lon,
            radiusInMeters,
        ).success()

    private suspend fun AccreditedAndPriorityListLogicService.priorityListLogic(
        range: IntRange,
        accredited: List<Accredited>,
        placeId: String,
        categories: List<AccreditedCategory>?,
        siteAccreditedNetworkId: UUID? = null,
        specialityId: UUID?,
        lat: String?,
        lon: String?,
        radiusInMeters: Int?,
    ): List<Accredited> {
        logger.info(
            "Flagship service enabled",
            "placeId" to placeId,
        )

        val flagships = accreditedNetworkFlagshipService
            .getFlagship(placeId)
            .getOrNullIfNotFound()
            .let { flagship ->
                filterAccordingToCategories(categories, flagship)
            }

        logger.info(
            "Got flagships",
            "placeId" to placeId,
            "flagships" to flagships,
        )

        return when {
            isFirstPageInRange(range) && flagships != null -> getAccreditedFlagships(
                flagship = flagships,
                accreditedList = accredited,
                siteAccreditedNetworkId = siteAccreditedNetworkId,
                categories = categories,
                specialityId = specialityId,
                lat = lat,
                lon = lon,
                radiusInMeters = radiusInMeters ?: 0,
            )
            flagships != null -> removeFlagshipsFromAccreditedList(accredited, flagships)
            else -> accredited
        }
    }

    private fun filterAccordingToCategories(
        categories: List<AccreditedCategory>?,
        flagship: SiteAccreditedNetworkFlagship?
    ): SiteAccreditedNetworkFlagship? =
        if (categories?.isNotEmpty() == true) {
            val categoriesName = categories.map { it.name }
            flagship?.let {
                it.copy(data = it.data.filter { dataItem ->
                    dataItem.type.name in categoriesName
                })
            }
        } else {
            flagship
        }

    private fun removeFlagshipsFromAccreditedList(
        accredited: List<Accredited>,
        flagships: SiteAccreditedNetworkFlagship? = null,
    ): List<Accredited> {
        val flagshipsReferencedIds = flagships?.data?.map { it.providerUnitId }
        return accredited.filter { flagshipsReferencedIds?.contains(it.referencedId) != true }
    }

    private suspend fun getAccreditedFlagships(
        flagship: SiteAccreditedNetworkFlagship,
        accreditedList: List<Accredited>,
        siteAccreditedNetworkId: UUID? = null,
        categories: List<AccreditedCategory>? = null,
        specialityId: UUID? = null,
        lat: String? = null,
        lon: String? = null,
        radiusInMeters: Int? = null,
        range: IntRange? = null,
    ): List<Accredited> {
        logger.info(
            "Getting flagships from consolidated",
            "flagship" to flagship,
        )

        val filter = accreditedFilterService.createFilter(
            siteAccreditedNetworkId = siteAccreditedNetworkId,
            categories = categories,
            specialityId = specialityId,
            lat = lat,
            lon = lon,
            radiusInMeters = radiusInMeters ?: 0,
            limit = range?.count(),
            offset = range?.first,
        ).get()

        val flagshipsReferencedIds = flagship.data.map { it.providerUnitId }

        val priority = accreditedService
            .getByReferencedIds(flagshipsReferencedIds)
            .getOrNull()

        val priorityFiltered = if (filter.siteAccreditedNetwork?.bundleIds?.isNotEmpty() == true) {
            priority?.filter {
                filter.siteAccreditedNetwork?.bundleIds?.intersect(it.bundleIds)?.toList()?.isNotEmpty() == true
            }
        } else priority

        logger.info(
            "Got flagships from consolidated",
            "flagship" to flagship,
            "priority" to priorityFiltered,
        )

        val notPriority = removeFlagshipsFromAccreditedList(accreditedList, flagship)

        val sortMap = flagship.data
            .withIndex()
            .associate { (index, item) -> item.providerUnitId to index }

        val priorityListSorted = priorityFiltered?.sortedWith(compareBy {
            sortMap[it.referencedId] ?: Int.MAX_VALUE
        })

        return (priorityListSorted.orEmpty() + notPriority)
    }

    private fun isFirstPageInRange(range: IntRange) = range.first == 0
}
