package br.com.alice.common.models

enum class CouncilType(val code: Int) {
    CRAS(1),
    COREN(2),
    CRF(3),
    CRFA(4),
    CREFITO(5),
    CRM(6),
    CRN(7),
    CRO(8),
    CRP(9),
    CREFONO(10),
    CRESS(11),
    OUT(12);

    companion object {
        fun fromCode(code: Int) = values().firstOrNull { it.code == code }
        fun fromName(name: String) = values().firstOrNull { it.name == name.uppercase() }
    }
}
