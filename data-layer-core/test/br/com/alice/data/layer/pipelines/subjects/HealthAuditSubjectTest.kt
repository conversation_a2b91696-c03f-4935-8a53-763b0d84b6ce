package br.com.alice.data.layer.pipelines.subjects

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import org.junit.jupiter.api.Assertions.assertEquals
import kotlin.test.Test

class HealthAuditSubjectTest {

    @Test
    fun `should create a HealthAuditSubject from StaffModel`() {
        val id = RangeUUID.generate()
        val role = Role.HEALTH_OPS
        val staffModel = DataLayerTestModelFactory.buildStaff(id = id, role = role)

        val healthAuditSubject = HealthAuditSubject(staffModel)

        assertEquals(id, healthAuditSubject.id)
        assertEquals(role, healthAuditSubject.role)
    }
}
