package br.com.alice.sales_channel.api.converters

import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.Product
import br.com.alice.sales_channel.api.models.DealBeneficiaryAppointmentDetails
import br.com.alice.sales_channel.api.models.DealBeneficiaryItem
import br.com.alice.sales_channel.model.DealBeneficiary
import java.math.BigDecimal
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.UUID

object DealBeneficiaryConverter {
    fun convert(
        beneficiaries: List<DealBeneficiary>,
        appointments: List<AppointmentSchedule>,
        products: List<Product>,
        memberPrices: Map<UUID, BigDecimal?>
    ): List<DealBeneficiaryItem> =
        beneficiaries.map { beneficiary ->
            val appointment = appointments.find { it.personId == beneficiary.personId }
            val product = products.find { it.id == beneficiary.product.id }

            DealBeneficiaryItem(
                id = beneficiary.id,
                name = beneficiary.name,
                updatedAt = beneficiary.updatedAt,
                stage = beneficiary.stage,
                appointment = convertAppointment(appointment),
                productPrice = memberPrices[beneficiary.memberId],
                productTitle = product?.displayName ?: beneficiary.product.friendlyName,
            )
        }

    private fun convertAppointment(appointment: AppointmentSchedule?): DealBeneficiaryAppointmentDetails {
        val utcZone = ZoneId.of("UTC")
        val startTimeGmtMinus3 = appointment?.startTime?.let {
            val startTimeUTCZoned = ZonedDateTime.of(it, utcZone)
            startTimeUTCZoned.withZoneSameInstant(ZoneId.of("GMT-3"))
        }
        val endTimeGmtMinus3 = appointment?.endTime?.let {
            val endTimeUTCZoned = ZonedDateTime.of(it, utcZone)
            endTimeUTCZoned.withZoneSameInstant(ZoneId.of("GMT-3"))
        }

        return DealBeneficiaryAppointmentDetails(
            status = appointment?.status,
            appointmentStartTime = startTimeGmtMinus3?.toLocalDateTime(),
            appointmentEndTime = endTimeGmtMinus3?.toLocalDateTime(),
        )
    }
}
