package br.com.alice.tiss.consumers.converters

import br.com.alice.common.MvUtil
import br.com.alice.common.core.extensions.nullIfBlank
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.AnsAccidentIndication
import br.com.alice.data.layer.models.AnsAttendanceCharacter
import br.com.alice.data.layer.models.AnsAttendanceRegime
import br.com.alice.data.layer.models.AnsAuthorizationType
import br.com.alice.data.layer.models.AnsRegisterCode
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.DEFAULT_CBO
import br.com.alice.exec.indicator.models.DEFAULT_COUNCIL_NUMBER
import br.com.alice.exec.indicator.models.DEFAULT_PROFESSIONAL_NAME
import br.com.alice.exec.indicator.models.ExecutorData
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.exec.indicator.models.RequesterData
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.tiss.models.CreateGuiaRequest
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.util.UUID

class GuiaCreatedRequestBuilder(
    private val providerUnitService: ProviderUnitService,
    private val healthcareResourceService: HealthcareResourceService
) {
    companion object {
        const val ALICE_CNPJ = "34266553000102"
        const val ALICE_NAME = "Alice Operadora"
        const val DEFAULT_CNES = "9999999"
    }

    suspend fun build(
        procedures: List<MvAuthorizedProcedure>,
        totvsGuia: TotvsGuia,
        beneficiary: Beneficiary,
        executorCnpj: String? = null,
        origin: GuiaOrigin,
        executorProviderUnitId: UUID? = null,
    ) = coResultOf<CreateGuiaRequest, Throwable> {
        val firstProcedure = procedures.first()
        val procedureCodes = procedures.mapNotNull { it.procedureId }
        val healthcareResources = healthcareResourceService.findByCodes(procedureCodes).get()
        val healthcareResourcesByCode = healthcareResources.associateBy { it.code }

        CreateGuiaRequest(
            guiaNumber = totvsGuia.code,
            ansCode = AnsRegisterCode.ALICE.code,
            procedures = procedures.map { procedure ->
                GuiaProcedure(
                    code = procedure.procedureId!!,
                    description = healthcareResourcesByCode[procedure.procedureId!!]?.description ?: "NO DESCRIPTION",
                    quantity = procedure.extraGuiaInfo.quantity ?: 1,
                    status = procedure.status,
                    table = healthcareResourcesByCode[procedure.procedureId!!]?.tableType ?: "98"
                )
            },
            beneficiary = beneficiary,
            requester = getRequester(totvsGuia.providerUnitId),
            authorizationType = AnsAuthorizationType.PREVIOUS_AUTHORIZATION_REQUEST.code,
            attendanceCharacter =  AnsAttendanceCharacter.ELECTIVE.code,
            professional = Professional(
                name = firstProcedure.requestedByProfessional.fullName?.unaccent().nullIfBlank() ?: DEFAULT_PROFESSIONAL_NAME,
                councilNumber = firstProcedure.requestedByProfessional.councilNumber.nullIfBlank() ?: DEFAULT_COUNCIL_NUMBER,
                council = firstProcedure.requestedByProfessional.council,
                councilState = firstProcedure.requestedByProfessional.councilState,
                phoneNumber = firstProcedure.requestedByProfessional.phone?.onlyNumbers(),
                email = firstProcedure.requestedByProfessional.email,
                cbo = firstProcedure.requestedByProfessional.cboCode ?: DEFAULT_CBO,
            ),
            requestDate = LocalDate.now(),
            executor = getExecutor(executorCnpj, executorProviderUnitId),
            guiaExternalCode = totvsGuia.externalCode,
            attendanceRegime = firstProcedure.extraGuiaInfo.procedureType.toAnsAttendanceRegime(),
            accidentIndication = firstProcedure.extraGuiaInfo.accidentIndication.toAnsAccidentIndication(),
            origin = origin,

        )
    }

private suspend fun getRequester(providerUnitId: UUID?): RequesterData {
    val defaultRequester = RequesterData(
        name = ALICE_NAME,
        providerCode = ALICE_CNPJ
    )

    if (providerUnitId == null) return defaultRequester

    return providerUnitService.get(providerUnitId)
        .map { providerUnit ->
            if (providerUnit.isHospital()) {
                RequesterData(
                    name = providerUnit.name,
                    providerCode = providerUnit.cnpj ?: ALICE_CNPJ
                )
            } else defaultRequester
        }.get()
}

    private suspend fun getExecutor(executorCnpj: String?, executorProviderUnitId: UUID?): ExecutorData? {
        return if (executorCnpj == null) {
            null
        } else if (executorProviderUnitId != null) {
            logger.info("ExecutorProviderUnitId is not null, using it to get ExecutorData")
            providerUnitService.get(executorProviderUnitId).flatMap { providerUnit ->
                ExecutorData(
                    providerCode = providerUnit.cnpj
                        ?: return@flatMap IllegalArgumentException("ProviderUnit CNPJ is null for ID: ${providerUnit.id}").failure(),
                    name = providerUnit.name,
                    cnes = getCnes(providerUnit.cnes)
                ).success()
            }.get()
        } else {
            logger.info("ExecutorProviderUnitId is null, using ExecutorCnpj to get ExecutorData")
            providerUnitService.getByCnpj(executorCnpj).flatMap { providerUnit ->
                ExecutorData(
                    providerCode = providerUnit.cnpj
                        ?: return@flatMap IllegalArgumentException("ProviderUnit CNPJ is null for ID: ${providerUnit.id}").failure(),
                    name = providerUnit.name,
                    cnes = getCnes(providerUnit.cnes)
                ).success()
            }.get()
        }
    }


    private fun MvUtil.TISS.toAnsAttendanceRegime() =
        when (this) {
            MvUtil.TISS.HOSPITALIZATION -> AnsAttendanceRegime.HOSPITALIZATION.code
            MvUtil.TISS.PS -> AnsAttendanceRegime.EMERGENCY_ROOM.code
            else -> AnsAttendanceRegime.AMBULATORY.code
        }

    private fun String?.toAnsAccidentIndication() = runCatching {
        if (this == null) AnsAccidentIndication.NOT_ACCIDENT.code
        else AnsAccidentIndication.valueOf(this).code
    }.getOrElse { AnsAccidentIndication.NOT_ACCIDENT.code }

    private fun getCnes(cnes: String?) = if (cnes == null || cnes.length > 7) DEFAULT_CNES else cnes


}
