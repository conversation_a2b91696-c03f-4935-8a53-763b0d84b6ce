package br.com.alice.business.services.client.cassi

import br.com.alice.business.client.CassiMemberInfo
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.verifyNone
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.common.logging.logger
import br.com.alice.common.redis.GenericCache
import br.com.alice.data.layer.models.FeatureNamespace
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.fullPath
import io.ktor.http.headersOf
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import redis.clients.jedis.exceptions.JedisConnectionException
import redis.clients.jedis.exceptions.JedisException
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class CassiClientImplTest {
    private val cassiConfig = CassiConfiguration(
        baseUrl = "http://localhost",
        baseUrlV1 = "http://localhost/v1",
        authUrl = "http://localhost/auth",
        clientId = "1234",
        secret = "secret",
        userKey = "key",
        userKeyV1 = "keyV1",
    )

    private val cache: GenericCache = mockk()

    private fun loadJson(file: String) = javaClass.classLoader
        .getResource("$file.json")!!
        .readText()

    private fun withInvalidCache(block: suspend () -> Unit) = runBlocking {
        coEvery { cache.get("auth-token", String::class, any(), 36000, any()) } coAnswers {
            arg<suspend () -> String>(4).invoke()
        }

        block()
    }

    private fun withValidCache(block: suspend () -> Unit) = runBlocking {
        coEvery { cache.get("auth-token", String::class, any(), 36000, any()) } returns "access_token"

        block()
    }

    private fun defineEngineResponse(path: String) = MockEngine.create {
        addHandler { request ->
            val headers = headersOf(
                HttpHeaders.ContentType to listOf(ContentType.Application.Json.toString())
            )

            val (jsonFile, status) = when (path) {
                "/auth" -> Pair("client/cassi/auth/response_success", HttpStatusCode.OK)
                "/auth/gail" -> Pair("client/cassi/auth/response_fail", HttpStatusCode.BadRequest)
                "/auth/grant/fail" -> Pair("client/cassi/auth/response_grant_fail", HttpStatusCode.BadRequest)
                "/auth/unauthorized" -> Pair(null, HttpStatusCode.Unauthorized)
                "/auth/forbidden" -> Pair(null, HttpStatusCode.Forbidden)
                "/card-detail/v1" -> Pair("client/cassi/card-detail/response_success", HttpStatusCode.OK)
                "/card-detail/v1/not-found" -> Pair("client/cassi/card-detail/v1/response_not_found", HttpStatusCode.NotFound)
                "/card-detail/v1/bad-request" -> Pair(
                    "client/cassi/card-detail/v1/response_bad_request",
                    HttpStatusCode.BadRequest
                )
                "/card-detail/v1/unauthorized" -> Pair(null, HttpStatusCode.Unauthorized)
                "/card-detail/v1/forbidden" -> Pair(null, HttpStatusCode.Forbidden)

                "/card-detail" -> Pair("client/cassi/card-detail/response_success", HttpStatusCode.OK)
                "/card-detail/not-found" -> Pair("client/cassi/card-detail/response_not_found", HttpStatusCode.OK)
                "/card-detail/bad-request" -> Pair("client/cassi/card-detail/response_bad_request", HttpStatusCode.OK)
                "/card-detail/unauthorized" -> Pair(null, HttpStatusCode.Unauthorized)
                "/card-detail/forbidden" -> Pair(null, HttpStatusCode.Forbidden)
                else -> Pair(error("unhandled path ${request.url.fullPath}"), HttpStatusCode.NotFound)
            }

            val json = jsonFile?.let { loadJson(jsonFile) } ?: ""
            respond(json, headers = headers, status = status)
        }
    }

    @BeforeTest
    fun setup() {
        mockkObject(logger)
    }

    @AfterTest
    fun clear() {
        unmockkAll()
    }

    @Test
    fun `#getAuthenticationToken - should try to get the token directly to cassi when some jedis exception is caught`() =
        runBlocking {
            coEvery {
                cache.get(
                    "auth-token",
                    String::class,
                    any(),
                    36000,
                    any()
                )
            } answers { throw JedisConnectionException("") }
            val client = CassiClientImpl(cassiConfig, defineEngineResponse("/auth"), cache)

            val result = client.getAuthenticationToken()

            assertThat(result).isSuccessWithData(
                "access_token"
            )

            verifyOnce {
                logger.error("It's not possible use the cache now",
                    match { it.first == "reason" && it.second is JedisException })
            }
        }

    @Test
    fun `#getAuthenticationToken - should forward any exception with without cassi's retry`() = runBlocking {
        coEvery { cache.get("auth-token", String::class, any(), 36000, any()) } answers {
            throw Exception("")
        }

        val client = CassiClientImpl(cassiConfig, defineEngineResponse("/auth"), cache)

        val result = client.getAuthenticationToken()

        assertThat(result).isFailureOfType(Exception::class)

        verifyNone {
            logger.error("It's not possible use the cache now",
                match { it.first == "reason" && it.second is JedisException })
        }
    }

    @Test
    fun `#getAuthenticationToken - should get the access token by cache`() = withValidCache {
        val client = CassiClientImpl(cassiConfig, defineEngineResponse("/auth"), cache)

        val result = client.getAuthenticationToken()

        assertThat(result).isSuccessWithData(
            "access_token"
        )
    }

    @Test
    fun `#getAuthenticationToken - should get the access token when API returns 200`() = withInvalidCache {
        val client = CassiClientImpl(cassiConfig, defineEngineResponse("/auth"), cache)

        val result = client.getAuthenticationToken()

        assertThat(result).isSuccessWithData(
            "access_token"
        )
    }

    @Test
    fun `#getAuthenticationToken - should fail when API returns 400`() = withInvalidCache {
        val client = CassiClientImpl(cassiConfig, defineEngineResponse("/auth/grant/fail"), cache)

        val result = client.getAuthenticationToken()

        assertThat(result).isFailureOfType(UnauthorizedException::class)
    }


    @Test
    fun `#getAuthenticationToken - should credential fail when API returns 400`() = withInvalidCache {
        val client = CassiClientImpl(cassiConfig, defineEngineResponse("/auth/grant/fail"), cache)

        val result = client.getAuthenticationToken()

        assertThat(result).isFailureOfType(UnauthorizedException::class)
    }


    @Test
    fun `#getAuthenticationToken - should credential fail when API returns 401`() = withInvalidCache {
        val client = CassiClientImpl(cassiConfig, defineEngineResponse("/auth/unauthorized"), cache)

        val result = client.getAuthenticationToken()

        assertThat(result).isFailureOfType(UnauthorizedException::class)

    }

    @Test
    fun `#getAuthenticationToken - should credential fail when API returns 403`() = withInvalidCache {
        val client = CassiClientImpl(cassiConfig, defineEngineResponse("/auth/forbidden"), cache)

        val result = client.getAuthenticationToken()

        assertThat(result).isFailureOfType(ForbiddenException::class)
    }


    @Test
    fun `#getCardDetailsByNationalId - should get the access token when API returns 200`() = withValidCache {
        val client = CassiClientImpl(cassiConfig, defineEngineResponse("/card-detail"), cache)

        val result = client.getCardDetailsByNationalId("111111")

        assertThat(result).isSuccessWithData(
            CassiMemberInfo(
                accountNumber = "123456",
                startDate = "2022-06-27",
                expirationDate = "2023-03-31"
            )
        )
    }

    @Test
    fun `#getCardDetailsByNationalId - should get an error when the CASSI response not found the beneficiary`() =
        withValidCache {
            val client = CassiClientImpl(cassiConfig, defineEngineResponse("/card-detail/not-found"), cache)

            val result = client.getCardDetailsByNationalId("111111")

            assertThat(result).isFailureOfType(NotFoundException::class)
        }


    @Test
    fun `#getCardDetailsByNationalId - should get an error when the CASSI response bad request`() = withValidCache {

        val client = CassiClientImpl(cassiConfig, defineEngineResponse("/card-detail/bad-request"), cache)

        val result = client.getCardDetailsByNationalId("111111")

        assertThat(result).isFailureOfType(CassiException::class)
    }


    @Test
    fun `#getCardDetailsByNationalId - should get an error when the CASSI response unauthorized`() = withValidCache {
        val client = CassiClientImpl(cassiConfig, defineEngineResponse("/card-detail/unauthorized"), cache)

        val result = client.getCardDetailsByNationalId("111111")

        assertThat(result).isFailureOfType(UnauthorizedException::class)

    }

    @Test
    fun `#getCardDetailsByNationalId - should get an error when the CASSI response forbidden`() = withValidCache {
        val client = CassiClientImpl(cassiConfig, defineEngineResponse("/card-detail/forbidden"), cache)

        val result = client.getCardDetailsByNationalId("111111")

        assertThat(result).isFailureOfType(ForbiddenException::class)
    }

    @Nested
    inner class GetCardDetailsByNationalIdV1API {
        @Test
        fun `#should get the access token when API returns 200`() = runBlocking {
            withFeatureFlag(FeatureNamespace.BUSINESS, "should_use_new_cassi_api", true) {
                withValidCache {
                    val client = CassiClientImpl(cassiConfig, defineEngineResponse("/card-detail/v1"), cache)

                    val result = client.getCardDetailsByNationalId("111111")

                    assertThat(result).isSuccessWithData(
                        CassiMemberInfo(
                            accountNumber = "123456",
                            startDate = "2022-06-27",
                            expirationDate = "2023-03-31"
                        )
                    )
                }
            }
        }

        @Test
        fun `#should get an error when the CASSI response not found the beneficiary`() = runBlocking {
            withFeatureFlag(FeatureNamespace.BUSINESS, "should_use_new_cassi_api", true) {
                withValidCache {
                    val client = CassiClientImpl(cassiConfig, defineEngineResponse("/card-detail/v1/not-found"), cache)

                    val result = client.getCardDetailsByNationalId("111111")

                    assertThat(result).isFailureOfType(NotFoundException::class)
                }
            }
        }


        @Test
        fun `#should get an error when the CASSI response bad request`() = runBlocking {
            withFeatureFlag(FeatureNamespace.BUSINESS, "should_use_new_cassi_api", true) {
                withValidCache {

                    val client =
                        CassiClientImpl(cassiConfig, defineEngineResponse("/card-detail/v1/bad-request"), cache)

                    val result = client.getCardDetailsByNationalId("111111")

                    assertThat(result).isFailureOfType(CassiException::class)
                }
            }
        }


        @Test
        fun `#should get an error when the CASSI response unauthorized`() = runBlocking {
            withFeatureFlag(FeatureNamespace.BUSINESS, "should_use_new_cassi_api", true) {
                withValidCache {
                    val client = CassiClientImpl(cassiConfig, defineEngineResponse("/card-detail/v1/unauthorized"), cache)

                    val result = client.getCardDetailsByNationalId("111111")

                    assertThat(result).isFailureOfType(UnauthorizedException::class)

                }
            }
        }


        @Test
        fun `#should get an error when the CASSI response forbidden`() = runBlocking {
            withFeatureFlag(FeatureNamespace.BUSINESS, "should_use_new_cassi_api", true) {
                withValidCache {
                    val client = CassiClientImpl(cassiConfig, defineEngineResponse("/card-detail/v1/forbidden"), cache)

                    val result = client.getCardDetailsByNationalId("111111")

                    assertThat(result).isFailureOfType(ForbiddenException::class)
                }
            }
        }
    }
}
