package br.com.alice.member.api.controllers.onboarding

import br.com.alice.app.content.client.screens.MemberOnboardingScreeService
import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.ScreenBackgroundType
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ActionPlanTask
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.data.layer.models.CoPaymentChargeType
import br.com.alice.data.layer.models.CoPaymentTierPrice
import br.com.alice.data.layer.models.HealthPlanTaskTemplate
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType
import br.com.alice.data.layer.models.MemberOnboardingFlowType
import br.com.alice.data.layer.models.MemberOnboardingReferencedLink
import br.com.alice.data.layer.models.MemberOnboardingReferencedLinkModel
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.data.layer.models.RiskDescription
import br.com.alice.data.layer.models.Step
import br.com.alice.data.layer.models.TierType
import br.com.alice.marauders.map.client.RiskService
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.builders.MemberOnboardingsScreenBuilder
import br.com.alice.member.api.converters.MemberOnboardingProductConverter
import br.com.alice.member.api.converters.onboarding.ConclusionMemberOnboardingConverter
import br.com.alice.member.api.models.MemberProductDetails
import br.com.alice.member.api.models.appContent.ScreenLayout
import br.com.alice.member.api.models.appContent.ScreensResponse
import br.com.alice.member.api.models.onboarding.v2.MemberOnboardingResponse
import br.com.alice.member.api.models.onboarding.v2.MemberOnboardingStepResponse
import br.com.alice.member.api.services.AppState.MEMBER_ONBOARDING_V2
import br.com.alice.member.api.services.AppState.REDESIGN_UNIFIED_HEALTH
import br.com.alice.member.api.services.AppState.UNIFIED_HEALTH
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.member.api.services.MemberOnboardingConclusionService
import br.com.alice.member.api.services.MemberProductService
import br.com.alice.member.onboarding.client.MemberOnboardingService
import br.com.alice.member.onboarding.factory.OnboardingFactory
import br.com.alice.member.onboarding.model.AliceExperienceNumbers
import br.com.alice.member.onboarding.model.AliceExperienceNumbersSection
import br.com.alice.member.onboarding.model.AliceExperienceSection
import br.com.alice.member.onboarding.model.AliceExperienceTemplate
import br.com.alice.member.onboarding.model.OnboardingCoverButton
import br.com.alice.member.onboarding.model.OnboardingCoverTemplate
import br.com.alice.member.onboarding.model.OnboardingCoverTemplateType
import br.com.alice.member.onboarding.model.OnboardingCoverTemplateType.HEALTH_DECLARATION
import br.com.alice.member.onboarding.model.OnboardingTemplate
import br.com.alice.member.onboarding.model.OnboardingVersion
import br.com.alice.member.onboarding.model.PhysicianStaff
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.PersonCoPaymentCostInfo
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test
import br.com.alice.app.content.model.ScreenLayout as AppContentScreenLayout

class MemberOnboardingV2ControllerTest : RoutesTestHelper() {

    private val personService: PersonService = mockk()
    private val memberProductService: MemberProductService = mockk()
    private val memberOnboardingService: MemberOnboardingService = mockk()
    private val memberService: MemberService = mockk()
    private val memberOnboardingConclusionService: MemberOnboardingConclusionService = mockk()
    private val riskService: RiskService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val memberOnboardingScreeService: MemberOnboardingScreeService = mockk()

    private val controller = MemberOnboardingV2Controller(
        personService,
        memberProductService,
        memberOnboardingService,
        memberService,
        riskService,
        memberOnboardingConclusionService,
        beneficiaryService,
        memberOnboardingScreeService
    )

    private val person = TestModelFactory.buildPerson(dateOfBirth = LocalDateTime.parse("1993-01-01T11:00:00"))
    private val token = person.id.toString()
    private val appVersion = SemanticVersion("1.0.0")

    private val member = TestModelFactory.buildMember(
        personId = person.id,
        activationDate = LocalDateTime.parse("2024-01-01T11:00:00")
    )
    val step = Step(
        templateType = MemberOnboardingStepType.ALICE_INFO,
        status = MemberOnboardingStepStatus.PENDING
    )
    private val memberOnboarding = TestModelFactory.buildMemberOnboarding(
        personId = person.id,
        steps = listOf(step),
        flowType = MemberOnboardingFlowType.ADULT
    )

    private val template = OnboardingFactory[step.templateType, memberOnboarding.flowType, OnboardingVersion.V1, null, member.activationDate!!]
    private val stepResponse = MemberOnboardingStepResponse(
        type = step.templateType,
        status = step.status,
        title = template.title,
        timeToComplete = template.timeToComplete,
        url = template.url,
        path = template.path,
        bottomSheet = template.bottomSheet,
        backgroundImage = template.backgroundImage
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }

        coEvery { personService.get(person.id) } returns person.success()
    }

    @Test
    fun `#getProductDetails should return info about product member`() = mockRangeUUID {
        val product = TestModelFactory.buildProduct(tier = TierType.TIER_1)
        val hospitalProvider = TestModelFactory.buildProvider(
            flagship = true,
            type = ProviderType.HOSPITAL
        )
        val laboratoryProvider = TestModelFactory.buildProvider(
            flagship = true,
            type = ProviderType.LABORATORY
        )
        val coPaymentCostInfo = PersonCoPaymentCostInfo(
            event = "Consulta",
            chargeType = CoPaymentChargeType.FIXED_VALUE,
            price = CoPaymentTierPrice(
                tier = product.tier!!,
                value = BigDecimal.TEN
            )
        )

        val productDetails = MemberProductDetails(
            product = product,
            providers = listOf(hospitalProvider, laboratoryProvider),
            coPayment = listOf(coPaymentCostInfo),
            refund = emptyList()
        )

        coEvery {
            memberProductService.getProductDetails(person.id)
        } returns productDetails.success()

        val expectedResponse = MemberOnboardingProductConverter.convert(person, productDetails)

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/member_onboarding/product_details") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { memberProductService.getProductDetails(any()) }
    }

    @Test
    fun `#getMemberOnboarding should get memberOnboarding and return MemberOnboardingResponse`() {
        coEvery { memberService.getCurrent(person.id) } returns member.success()
        coEvery {
            memberOnboardingService.getByPersonIdCheckingSteps(person.id, member.active)
        } returns memberOnboarding.success()
        coEvery { memberOnboardingService.getOnboardingVersion(person.id) } returns OnboardingVersion.V1.success()
        coEvery { memberOnboardingService.getAliceInfoVersion(person.id) } returns Exception().failure()

        val expected = MemberOnboardingResponse(
            id = memberOnboarding.id,
            title = "Complete as etapas",
            description = "Conclua as etapas para desbloquear seu primeiro Plano de Ação. As tarefas serão personalizadas de acordo com suas respostas.",
            stepsDone = 0,
            isMemberActive = false,
            steps = listOf(stepResponse)
        )

        authenticatedAs(token, toTestPerson(person)) {
            get("v2/member_onboarding/steps") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)

                coVerifyOnce { memberService.getCurrent(any()) }
                coVerifyOnce { memberOnboardingService.getByPersonIdCheckingSteps(any(), any()) }
                coVerifyOnce { memberOnboardingService.getOnboardingVersion(any()) }
            }
        }
    }

    @Test
    fun `#getMemberOnboarding should return data version 2 on alice info when member is on AB test`() {
        coEvery { memberService.getCurrent(person.id) } returns member.success()
        coEvery {
            memberOnboardingService.getByPersonIdCheckingSteps(person.id, member.active)
        } returns memberOnboarding.success()
        coEvery { memberOnboardingService.getOnboardingVersion(person.id) } returns OnboardingVersion.V1.success()
        coEvery { memberOnboardingService.getAliceInfoVersion(person.id) } returns 2.success()

        val stepResponse = MemberOnboardingStepResponse(
            type = step.templateType,
            status = step.status,
            title = template.title,
            timeToComplete = template.timeToComplete,
            url = template.url,
            path = template.path,
            bottomSheet = template.bottomSheet,
            backgroundImage = template.backgroundImage,
            data = OnboardingTemplate.Data(version = 2)
        )

        val expected = MemberOnboardingResponse(
            id = memberOnboarding.id,
            title = "Complete as etapas",
            description = "Conclua as etapas para desbloquear seu primeiro Plano de Ação. As tarefas serão personalizadas de acordo com suas respostas.",
            stepsDone = 0,
            isMemberActive = false,
            steps = listOf(stepResponse)
        )

        authenticatedAs(token, toTestPerson(person)) {
            get("v2/member_onboarding/steps") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)

                coVerifyOnce { memberService.getCurrent(any()) }
                coVerifyOnce { memberOnboardingService.getByPersonIdCheckingSteps(any(), any()) }
                coVerifyOnce { memberOnboardingService.getOnboardingVersion(any()) }
            }
        }
    }

    @Test
    fun `#getAliceExperience should get and return AliceExperienceTemplate`() {
        val expected = AliceExperienceTemplate(
            experienceSection = AliceExperienceSection(
                title = "Experiência Alice na prática",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/member_onboarding/images/v2/alice_experience.png",
                subTitle = "Atendimento médico completo em menos de 20 minutos através do aclamado Alice Agora",
                description = "Assim que nosso time te atende pelo chat, você passa por uma consulta por vídeo com um " +
                        "médico do Alice Agora se for necessário. Em poucos minutos, você já está com sua receita ou " +
                        "encaminhamento disponível no seu Plano de Ação, sem papelada e sem ficar horas esperando em um " +
                        "pronto-socorro."
            ),
            numbersSection = AliceExperienceNumbersSection(
                title = "Como isso se reflete em números:",
                numbers = listOf(
                    AliceExperienceNumbers(
                        id = 1,
                        title = "10 min",
                        description = "É o tempo médio de espera por uma consulta com um médico após o atendimento pelo chat."
                    ),
                    AliceExperienceNumbers(
                        id = 2,
                        title = "71%",
                        description = "É o índice de casos resolvidos pelo app, sem precisar ir ao pronto-socorro."
                    ),
                    AliceExperienceNumbers(
                        id = 3,
                        title = "89%",
                        description = "É a porcentagem de membros Alice que estão com a saúde boa ou excelente."
                    )
                )
            )
        )

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/member_onboarding/alice_info") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)
            }
        }
    }

    @Test
    fun `#finishStep should update memberOnboarding with finished step and return MemberOnboardingResponse`() {
        mockkObject(AppStateNotifier)

        val updatedStep = step.copy(status = MemberOnboardingStepStatus.COMPLETED)
        val memberOnboardingUpdated = memberOnboarding.copy(steps = listOf(updatedStep))

        val template = OnboardingFactory[step.templateType, memberOnboarding.flowType, OnboardingVersion.V1, null, member.activationDate!!]
        val stepResponse = MemberOnboardingStepResponse(
            type = updatedStep.templateType,
            status = updatedStep.status,
            title = template.title,
            timeToComplete = template.timeToComplete,
            url = template.url,
            path = template.path,
            bottomSheet = template.bottomSheet,
            backgroundImage = template.backgroundImage
        )

        val expected = MemberOnboardingResponse(
            id = memberOnboardingUpdated.id,
            title = "Complete as etapas",
            description = "Conclua as etapas para desbloquear seu primeiro Plano de Ação. As tarefas serão personalizadas de acordo com suas respostas.",
            stepsDone = 1,
            isMemberActive = false,
            steps = listOf(stepResponse)
        )

        coEvery {
            memberOnboardingService.updateStepStatus(memberOnboarding.id, step.templateType)
        } returns memberOnboardingUpdated.success()
        coEvery { memberService.getCurrent(person.id) } returns member.success()
        coEvery { memberOnboardingService.getOnboardingVersion(person.id) } returns OnboardingVersion.V1.success()
        coEvery {
            AppStateNotifier.updateAppState(
                person.id,
                MEMBER_ONBOARDING_V2, UNIFIED_HEALTH, REDESIGN_UNIFIED_HEALTH
            )
        } returns Unit

        authenticatedAs(token, toTestPerson(person)) {
            put("v2/member_onboarding/${memberOnboarding.id}/step_finished/${step.templateType}") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)

                coVerifyOnce { memberOnboardingService.updateStepStatus(any(), any()) }
                coVerifyOnce { memberService.getCurrent(any()) }
                coVerifyOnce {memberOnboardingService.getOnboardingVersion(any()) }
                coVerifyOnce{ AppStateNotifier.updateAppState(any(), any(), any(), any()) }
            }
        }
    }

    @Test
    fun `#finishStep should update memberOnboarding with finished step and don't update MEMBER_ONBOARDING_V2 appState when type is conclusion`() {
        mockkObject(AppStateNotifier)

        val step = Step(
            templateType = MemberOnboardingStepType.CONCLUSION,
            status = MemberOnboardingStepStatus.PENDING
        )
        val updatedStep = step.copy(status = MemberOnboardingStepStatus.COMPLETED)
        val memberOnboardingUpdated = memberOnboarding.copy(steps = listOf(updatedStep))

        val template = OnboardingFactory[step.templateType, memberOnboarding.flowType, OnboardingVersion.V1, null, member.activationDate!!]
        val stepResponse = MemberOnboardingStepResponse(
            type = updatedStep.templateType,
            status = updatedStep.status,
            title = template.title,
            timeToComplete = template.timeToComplete,
            url = template.url,
            path = template.path,
            bottomSheet = template.bottomSheet,
            backgroundImage = template.backgroundImage
        )

        val expected = MemberOnboardingResponse(
            id = memberOnboardingUpdated.id,
            title = "Complete as etapas",
            description = "Conclua as etapas para desbloquear seu primeiro Plano de Ação. As tarefas serão personalizadas de acordo com suas respostas.",
            stepsDone = 1,
            isMemberActive = false,
            steps = listOf(stepResponse)
        )

        coEvery {
            memberOnboardingService.updateStepStatus(memberOnboarding.id, step.templateType)
        } returns memberOnboardingUpdated.success()
        coEvery { memberService.getCurrent(person.id) } returns member.success()
        coEvery {
            AppStateNotifier.updateAppState(
                person.id,
                UNIFIED_HEALTH, REDESIGN_UNIFIED_HEALTH
            )
        } returns Unit
        coEvery { memberOnboardingService.getOnboardingVersion(person.id) } returns OnboardingVersion.V1.success()

        authenticatedAs(token, toTestPerson(person)) {
            put("v2/member_onboarding/${memberOnboarding.id}/step_finished/${step.templateType}") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)

                coVerifyOnce { memberOnboardingService.updateStepStatus(any(), any()) }
                coVerifyOnce { memberService.getCurrent(any()) }
                coVerifyOnce { memberOnboardingService.getOnboardingVersion(any()) }
                coVerifyOnce{ AppStateNotifier.updateAppState(any(), any(), any()) }
            }
        }
    }

    @Test
    fun `#getHealthDeclarationCover should get and return OnboardingCoverTemplate`() {
        val expected = OnboardingCoverTemplate(
            type = HEALTH_DECLARATION,
            title = "Declaração de Saúde",
            description = "Essa declaração é para conhecer seu histórico de saúde. Essa etapa também contribui para a " +
                    "definição do seu primeiro Plano de Ação.",
            imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/member_onboarding/images/v2/health_declaration_cover.png",
            agreement = "Vou responder a declaração de saúde de forma honesta e transparente. Sei que falsificar ou omitir" +
                    " informações é fraude e pode resultar em sanções cíveis e criminais.",
            button = OnboardingCoverButton(
                label = "Começar",
                mobileRoute = ActionRouting.HEALTH_DECLARATION,
                params = mapOf("intro_only" to false)
            )
        )

        coEvery { memberOnboardingService.getByPersonId(person.id) } returns memberOnboarding.success()
        coEvery { personService.get(person.id) } returns person.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/member_onboarding/health_declaration_cover") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)

                coVerifyOnce {
                    memberOnboardingService.getByPersonId(any())
                    personService.get(any())
                }
            }
        }
    }

    @Test
    fun `#getConclusion should return correct screen when member is not active`() {
        val title = "Agende sua consulta com Médico de Familia"
        val taskTemplate = TestModelFactory.buildHealthPlanTaskTemplate(
            title = title
        )
        val memberOnboarding = memberOnboarding.copy(
            referencedLinks = listOf(
                MemberOnboardingReferencedLink(
                    id = taskTemplate.id,
                    model = MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK_TEMPLATE
                )
            )
        )
        val risk = TestModelFactory.buildRisk(riskDescription = RiskDescription.NO_RISK)
        val beneficiary = TestModelFactory.buildBeneficiary(memberStatus = MemberStatus.PENDING)

        coEvery {
            memberOnboardingService.getByPersonId(person.id)
        } returns memberOnboarding.success()
        coEvery {
            memberOnboardingConclusionService.getTasks(memberOnboarding)
        } returns (emptyList<ActionPlanTask>() to listOf(taskTemplate)).success()
        coEvery { riskService.getByPerson(person.id) } returns risk.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns beneficiary.success()

        val expected = ConclusionMemberOnboardingConverter.convert(
            actionPlanTasks = emptyList(),
            healthPlanTaskTemplates = listOf(taskTemplate),
            isTarget = risk.isTarget(),
            activationDate = beneficiary.activatedAt,
            flowType = memberOnboarding.flowType,
            isMemberActive = false
        )

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/member_onboarding/conclusion_screen") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)

                coVerifyOnce {
                    memberOnboardingService.getByPersonId(any())
                    memberOnboardingConclusionService.getTasks(any())
                    riskService.getByPerson(any())
                    beneficiaryService.findByPersonId(any())
                }

                coVerifyNone {
                    memberOnboardingConclusionService.getPhysicianStaff(any())
                     memberService.getCurrent(any())
                }
            }
        }
    }

    @Test
    fun `#getConclusion should return correct screen when member is active`() {
        val title = "Participe de um encontro de Mindfulness"
        val description = "Com base no seu pilar de Sono em 350 pontos, enviaremos uma tarefa de próximos passos"
        val beneficiary = TestModelFactory.buildBeneficiary(memberStatus = MemberStatus.ACTIVE)
        val task = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            title = title,
            description = description,
            type = ActionPlanTaskType.OTHERS,
        )
        val memberOnboarding = memberOnboarding.copy(
            referencedLinks = listOf(
                MemberOnboardingReferencedLink(
                    id = task.id,
                    model = MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK
                )
            )
        )
        val risk = TestModelFactory.buildRisk(riskDescription = RiskDescription.MEDIUM_RISK)
        val physicianStaff = PhysicianStaff(
            name = "João Silva",
            council = "CRM: 0001/SP",
            profileImageUrl = "https://teste"
        )

        coEvery {
            memberOnboardingService.getByPersonId(person.id)
        } returns memberOnboarding.success()
        coEvery {
            memberOnboardingConclusionService.getTasks(memberOnboarding)
        } returns (listOf(task) to emptyList<HealthPlanTaskTemplate>()).success()
        coEvery { memberOnboardingConclusionService.getPhysicianStaff(task.lastRequesterStaffId) } returns physicianStaff.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns beneficiary.success()
        coEvery { riskService.getByPerson(person.id) } returns risk.success()

        val expected = ConclusionMemberOnboardingConverter.convert(
            actionPlanTasks = listOf(task),
            healthPlanTaskTemplates = emptyList(),
            isTarget = risk.isTarget(),
            flowType = memberOnboarding.flowType,
            physicianStaff = physicianStaff,
            isMemberActive = true
        )

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/member_onboarding/conclusion_screen") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)

                coVerifyOnce {
                    memberOnboardingService.getByPersonId(any())
                    memberOnboardingConclusionService.getTasks(any())
                    memberOnboardingConclusionService.getPhysicianStaff(any())
                    riskService.getByPerson(any())
                    beneficiaryService.findByPersonId(any())
                }

                coVerifyNone { memberService.getCurrent(any()) }
            }
        }
    }

    @Test
    fun `#getConclusion should return correct screen when member is not active and has no Beneficiary`() {
        val title = "Agende sua consulta com Médico de Familia"
        val taskTemplate = TestModelFactory.buildHealthPlanTaskTemplate(
            title = title
        )
        val memberOnboarding = memberOnboarding.copy(
            referencedLinks = listOf(
                MemberOnboardingReferencedLink(
                    id = taskTemplate.id,
                    model = MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK_TEMPLATE
                )
            )
        )
        val risk = TestModelFactory.buildRisk(riskDescription = RiskDescription.NO_RISK)
        val beneficiary = TestModelFactory.buildBeneficiary(memberStatus = MemberStatus.PENDING)

        coEvery {
            memberOnboardingService.getByPersonId(person.id)
        } returns memberOnboarding.success()
        coEvery {
            memberOnboardingConclusionService.getTasks(memberOnboarding)
        } returns (emptyList<ActionPlanTask>() to listOf(taskTemplate)).success()
        coEvery { riskService.getByPerson(person.id) } returns risk.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns NotFoundException().failure()
        coEvery { memberService.getCurrent(person.id) } returns member.success()

        val expected = ConclusionMemberOnboardingConverter.convert(
            actionPlanTasks = emptyList(),
            healthPlanTaskTemplates = listOf(taskTemplate),
            isTarget = risk.isTarget(),
            flowType = memberOnboarding.flowType,
            isMemberActive = false
        )

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/member_onboarding/conclusion_screen") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)

                coVerifyOnce {
                    memberOnboardingService.getByPersonId(any())
                    memberOnboardingConclusionService.getTasks(any())
                    riskService.getByPerson(any())
                    beneficiaryService.findByPersonId(any())
                    memberService.getCurrent(any())
                }

                coVerifyNone {
                    memberOnboardingConclusionService.getPhysicianStaff(any())
                }
            }
        }
    }

    @Test
    fun `#getCallToComplete - should return screen of call to complete`() = runBlocking {
        val screen = ScreensTransport(
            id = "1",
            layout = AppContentScreenLayout(
                type = "single_column",
                body = emptyList()
            )
        )

        val expected = ScreensResponse(
            id = screen.id,
            layout = ScreenLayout(
                type = screen.layout.type,
                appBar = null,
                body = emptyList(),
                footer = null,
                listItemHasPadding = null,
                backgroundType = ScreenBackgroundType.ACTIVE
            ),
            properties = null
        )

        coEvery { memberOnboardingScreeService.getCallToComplete(appVersion) } returns screen.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/member_onboarding/screen/call_to_complete") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)
            }

            coVerifyOnce { memberOnboardingScreeService.getCallToComplete(any()) }
        }
    }

    @Test
    fun `#getAliceExperienceV2 should get version 2 of Alice Info screen`() {
        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/member_onboarding/v2/alice_info") { response ->
                ResponseAssert(response).isSuccessfulJson()
            }
        }
    }

    @Test
    fun `#getEmptyScreenMFC - should return empty screen of MFC`() = runBlocking {
        mockkObject(MemberOnboardingsScreenBuilder)
        val screen = ScreensTransport(
            id = "1",
            layout = AppContentScreenLayout(
                type = "single_column",
                body = emptyList()
            )
        )

        val expected = ScreensResponse(
            id = screen.id,
            layout = ScreenLayout(
                type = screen.layout.type,
                appBar = null,
                body = emptyList(),
                footer = null,
                listItemHasPadding = null,
                backgroundType = ScreenBackgroundType.ACTIVE
            ),
            properties = null
        )

        coEvery { memberService.getCurrent(person.id) } returns member.success()
        every { MemberOnboardingsScreenBuilder.buildScreenEmptyMFC(member.activationDate!!) } returns screen

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/empty_mfc") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)
            }

            coVerifyOnce { memberService.getCurrent(any()) }
        }
    }

    @Test
    fun `#getRecommendedPhysicians - should return recommended physicians screen`() = runBlocking {
        val screen = ScreensTransport(
            id = "1",
            layout = AppContentScreenLayout(
                type = "single_column",
                body = emptyList()
            )
        )

        val expected = ScreensResponse(
            id = screen.id,
            layout = ScreenLayout(
                type = screen.layout.type,
                appBar = null,
                body = emptyList(),
                footer = null,
                listItemHasPadding = null,
                backgroundType = ScreenBackgroundType.ACTIVE
            ),
            properties = null
        )

        coEvery { memberOnboardingScreeService.getRecommendedPhysicians(person.id) } returns screen.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/healthcare_team_recommendations") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)
            }

            coVerifyOnce { memberOnboardingScreeService.getRecommendedPhysicians(any()) }
        }
    }    

    @Test
    fun `#getRecommendedPhysicianDetails - should return recommended physician details screen`() = runBlocking {
        val healthcareTeamId = RangeUUID.generate()
        val screen = ScreensTransport(
            id = "1",
            layout = AppContentScreenLayout(
                type = "single_column",
                body = emptyList()
            )
        )

        val expected = ScreensResponse(
            id = screen.id,
            layout = ScreenLayout(
                type = screen.layout.type,
                appBar = null,
                body = emptyList(),
                footer = null,
                listItemHasPadding = null,
                backgroundType = ScreenBackgroundType.ACTIVE
            ),
            properties = null
        )

        coEvery {
            memberOnboardingScreeService.getRecommendedPhysicianDetails(
                personId = person.id,
                healthcareTeamId = healthcareTeamId,
                recommended = true
            )
        } returns screen.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/healthcare_team_details/$healthcareTeamId?recommended=true") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)
            }

            coVerifyOnce { memberOnboardingScreeService.getRecommendedPhysicianDetails(any(), any(), any()) }
        }
    }

    @Test
    fun `#getMFCCover should get and return OnboardingCoverTemplate of mfc`() {
        val expected = OnboardingCoverTemplate(
            type = OnboardingCoverTemplateType.MFC,
            title = "Um médico pra te conhecer de verdade",
            description = "Na Alice, seu médico te acompanha de perto, cuidando de você de forma contínua. Agora, é hora de conhecer quem estará ao seu lado.",
            imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/member_onboarding/images/v2/mfc_cover.png",
            button = OnboardingCoverButton(
                label = "Escolher meu médico",
                mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                params = mapOf(
                    "action" to mapOf(
                        "method" to "GET",
                        "endpoint" to "http://localhost:8824/app_content/screen/healthcare_team_recommendations",
                    )
                )
            )
        )

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/member_onboarding/mfc_cover") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)
            }
        }
    }
}
