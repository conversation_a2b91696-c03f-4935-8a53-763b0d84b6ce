package br.com.alice.data.layer.pipelines

import br.com.alice.authentication.RootService
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonReference
import br.com.alice.common.core.Subject
import br.com.alice.common.core.exceptions.AccessForbiddenException
import br.com.alice.common.core.extensions.classSimpleName
import br.com.alice.common.extensions.then
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.data.layer.authorization.Action
import br.com.alice.data.layer.authorization.Authorization
import br.com.alice.data.layer.authorization.AuthorizationRequest
import br.com.alice.data.layer.authorization.AuthorizationService
import br.com.alice.data.layer.authorization.BatchAuthorizationRequest
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.pipelines.context.ContextService
import br.com.alice.data.layer.pipelines.subjects.StaffSubject
import br.com.alice.data.layer.pipelines.subjects.SubjectBuilder
import br.com.alice.data.layer.services.AnonymizerService
import br.com.alice.data.layer.tables.Anonymizable
import br.com.alice.data.layer.tables.Table
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import io.opentelemetry.api.trace.Span
import kotlin.reflect.KClass
import kotlin.reflect.full.isSubclassOf

internal class AuthorizationPipeline<M : Model, T : Table<T>>(
    private val modelClass: KClass<M>,
    private val tableClass: KClass<T>,
    private val databasePipeline: DatabasePipeline<M>,
    private val authorizationService: AuthorizationService,
    private val contextService: ContextService,
    private val onlyLog: Boolean,
    private val factory: DatabasePipelineFactory
) : DatabasePipeline<M> {

    override suspend fun get(id: Any): Result<M, Throwable> = span {
        databasePipeline.get(id).flatMap { item ->
            val (subject, authorization) = getAuthorization(View, item)
            if (authorization.granted) anonymizeIfNeeded(subject, item).success()
            else authorization.toForbiddenResult()
        }
    }

    override suspend fun add(item: M): Result<M, Throwable> = span { span ->
        val (subject, authorization) = getAuthorization(Create, item)

        if (authorization.granted && canEditModel(subject, item))
            databasePipeline.add(item).then { span.setAttributes(it, subject) }
        else authorization.toForbiddenResult()
    }

    override suspend fun update(item: M): Result<M, Throwable> = span { span ->
        val (subject, authorization) = getAuthorization(Update, item)

        if (authorization.granted && canEditModel(subject, item))
            databasePipeline.update(item).then { span.setAttributes(it, subject) }
        else authorization.toForbiddenResult()
    }

    override suspend fun softDelete(item: M): Result<Boolean, Throwable> = span { span ->
        val (subject, authorization) = getAuthorization(Delete, item)

        if (authorization.granted && canEditModel(subject, item))
            databasePipeline.softDelete(item).then { span.setAttributes(item, subject) }
        else authorization.toForbiddenResult()
    }

    override suspend fun delete(item: M): Result<Boolean, Throwable> = span { span ->
        val (subject, authorization) = getAuthorization(Delete, item)

        if (authorization.granted && canEditModel(subject, item))
            databasePipeline.delete(item).then { span.setAttributes(item, subject) }
        else authorization.toForbiddenResult()
    }

    override suspend fun findByQuery(query: Query): Result<List<M>, Throwable> = span {
        databasePipeline.findByQuery(query).flatMap { resources ->
            val (subjects, authorizations) = getAuthorizations(View, resources)

            authorizations
                .filter { (_, authorization) -> !authorization.granted }
                .firstOrNull()
                ?.let { (_, authorization) -> authorization.toForbiddenResult() }
                ?: resources.map { model -> anonymizeIfNeeded(subjects.first { it.first == model }.second, model) }
                    .success()
        }
    }

    @Suppress("UNCHECKED_CAST")
    override suspend fun findAuthorizedByQuery(query: Query): Result<List<M>, Throwable> = span {
        databasePipeline.findByQuery(query).flatMap { resources ->
            val (subjects, authorizations) = getAuthorizations(View, resources)

            authorizations
                .filter { (_, authorization) -> authorization.granted }
                .map { (model, _) -> model }
                .toList()
                .map { model -> anonymizeIfNeeded(subjects.first { it.first == model }.second, model as M) }
                .success()
        }
    }

    override suspend fun countByQuery(query: Query): Result<Int, Throwable> = span {
        val (_, authorization) = getAuthorization(Count, null)

        if (authorization.granted) databasePipeline.countByQuery(query)
        else authorization.toForbiddenResult()
    }

    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable> = span {
        val (_, authorization) = getAuthorization(Count, null)

        if (authorization.granted) databasePipeline.countGroupedByQuery(query)
        else authorization.toForbiddenResult()
    }

    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable> = span {
        val (_, authorization) = getAuthorization(Count, null)

        if (authorization.granted) databasePipeline.existsByQuery(query)
        else authorization.toForbiddenResult()
    }

    override suspend fun anonymize(id: Any): Result<Boolean, Throwable> = span {
        databasePipeline.get(id).flatMap { item ->
            val (_, authorization) = getAuthorization(Update, item)

            if (authorization.granted) databasePipeline.anonymize(id)
            else authorization.toForbiddenResult()
        }
    }

    private suspend fun getAuthorization(action: Action, resource: M?): Pair<Subject, Authorization> {
        val claims = contextService.getClaims()
        val environment = contextService.getRootService()
        val baseSubject = SubjectBuilder.subject(factory, claims)
        val subject = SubjectBuilder.enrichSubject(factory, baseSubject, resource, environment)

        return subject to authorize(subject, action, resource, environment)
    }

    private suspend fun getAuthorizations(
        action: Action,
        resources: List<M>
    ): Pair<List<Pair<M, Subject>>, Sequence<Pair<Model, Authorization>>> {
        val claims = contextService.getClaims()
        val environment = contextService.getRootService()
        val baseSubject = SubjectBuilder.subject(factory, claims)
        val subjects = SubjectBuilder.enrichSubjects(factory, baseSubject, resources, environment)

        return subjects to authorizes(subjects, action, resources, environment).asSequence()
    }

    private suspend fun authorize(
        subject: Subject,
        action: Action,
        resource: M?,
        rootService: RootService
    ): Authorization =
        if (onlyLog) Authorization(true, "onlyLog")
        else authorizationService.authorize(AuthorizationRequest(subject, action, resource, rootService, modelClass))

    private suspend fun authorizes(
        subjects: List<Pair<M, Subject>>,
        action: Action,
        resources: List<M>,
        rootService: RootService
    ): List<Pair<Model, Authorization>> =
        if (onlyLog) resources.map { it to Authorization(true, "onlyLog") }
        else authorizationService.authorizeBatch(
            BatchAuthorizationRequest(
                subjects.map { (resource, enrichedSubject) ->
                    AuthorizationRequest(enrichedSubject, action, resource, rootService, modelClass)
                },
                rootService
            )
        )

    private fun canEditModel(subject: Subject, model: M): Boolean =
        if (model !is HealthInformation && model !is PersonReference) true
        else if (subject !is StaffSubject) true
        else !subject.viewAnonymized()

    private fun anonymizeIfNeeded(subject: Subject, model: M): M =
        if (subject is StaffSubject && subject.viewAnonymized() && tableClass.isSubclassOf(Anonymizable::class))
            AnonymizerService.anonymizeModel(model)
        else model

    private fun Authorization.toForbiddenResult() = AccessForbiddenException("Access forbidden: $description").failure()

    private fun Span.setAttributes(item: M, subject: Subject) {
        this.setAttribute("item_id", item.id.toString())
        this.setAttribute("requester_type", subject.classSimpleName())
    }

}

