package br.com.alice.schedule.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.basePredicateForFilters
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeLocation
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.services.AppointmentScheduleEventTypeModelDataService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeFilters
import br.com.alice.schedule.converters.toModel
import br.com.alice.schedule.exceptions.ExistingGenericEventTypeWithSameSubSpecialtyException
import br.com.alice.schedule.model.AppointmentScheduleEventTypeLocationAvailability
import br.com.alice.schedule.model.AppointmentScheduleEventTypeWithProviderUnits
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.Test

class AppointmentScheduleEventTypeServiceImplTest {

    private val dataService: AppointmentScheduleEventTypeModelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val eventTypeProviderUnitService: EventTypeProviderUnitServiceImpl = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val service = AppointmentScheduleEventTypeServiceImpl(
        dataService,
        kafkaProducerService,
        eventTypeProviderUnitService,
        providerUnitService
    )
    private val providerUnitId = RangeUUID.generate()
    private val appointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType()
    private val availabilityStartTime = LocalTime.parse("08:00")
    private val availabilityEndTime = LocalTime.parse("20:00")
    private val eventTypeProviderUnit = TestModelFactory.buildEventTypeProviderUnit(
        providerUnitId = providerUnitId,
        appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
        availabilityStartTime = availabilityStartTime,
        availabilityEndTime = availabilityEndTime
    )

    private val providerUnit = TestModelFactory.buildProviderUnit(id = eventTypeProviderUnit.providerUnitId!!)

    private val appointmentScheduleEventTypeWithProviderUnits = AppointmentScheduleEventTypeWithProviderUnits(
        id = appointmentScheduleEventType.id,
        title = appointmentScheduleEventType.title,
        specialtyId = appointmentScheduleEventType.specialtyId,
        subSpecialtyIds = appointmentScheduleEventType.subSpecialtyIds,
        showOnApp = appointmentScheduleEventType.showOnApp,
        category = appointmentScheduleEventType.category,
        duration = appointmentScheduleEventType.duration,
        locationType = appointmentScheduleEventType.locationType,
        description = appointmentScheduleEventType.description,
        userType = appointmentScheduleEventType.userType,
        status = appointmentScheduleEventType.status,
        searchTokens = appointmentScheduleEventType.searchTokens,
        healthcareModelType = appointmentScheduleEventType.healthcareModelType,
        minimumTimeToScheduleBeforeAppointmentTime = appointmentScheduleEventType
            .minimumTimeToScheduleBeforeAppointmentTime,
        isMultiProfessionalReferral = appointmentScheduleEventType.isMultiProfessionalReferral,
        numberOfDaysFromNowToAllowScheduling = appointmentScheduleEventType.numberOfDaysFromNowToAllowScheduling,
        internalObservation = appointmentScheduleEventType.internalObservation,
        membersRisk = appointmentScheduleEventType.membersRisk,
        providerUnitIds = listOfNotNull(eventTypeProviderUnit.providerUnitId),
        groupByType = appointmentScheduleEventType.groupByType,
        locations = listOf(
            AppointmentScheduleEventTypeLocationAvailability(
                providerUnitId = eventTypeProviderUnit.providerUnitId,
                type = AppointmentScheduleEventTypeLocation.ON_SITE,
                duration = eventTypeProviderUnit.duration,
                minimumTimeToScheduleBeforeAppointmentTime = eventTypeProviderUnit.minimumTimeToScheduleBeforeAppointmentTime,
                numberOfDaysFromNowToAllowScheduling = eventTypeProviderUnit.numberOfDaysFromNowToAllowScheduling,
                availableWeekDays = eventTypeProviderUnit.availableWeekDays,
                availabilityStartTime = availabilityStartTime,
                availabilityEndTime = availabilityEndTime,
                providerUnitName = providerUnit.name,
            )
        ),
        updatedAt = appointmentScheduleEventType.updatedAt,
    )

    private val statuses = listOf(Status.ACTIVE, Status.INACTIVE)
    private val healthcareModelType = appointmentScheduleEventType.healthcareModelType

    @Test
    fun `#get should return existing AppointmentScheduleEventType if any`() = runBlocking {
        coEvery {
            dataService.get(appointmentScheduleEventType.id)
        } returns appointmentScheduleEventType.toModel()
        val actual = service.get(appointmentScheduleEventType.id)
        assertThat(actual).isSuccessWithData(appointmentScheduleEventType)
    }

    @Test
    fun `#get should return not found exception when there is no appointment schedule event type for given id`() =
        runBlocking {
            coEvery {
                dataService.get(appointmentScheduleEventType.id)
            } returns NotFoundException("not found")
            val actual = service.get(appointmentScheduleEventType.id)
            assertThat(actual).isFailureOfType(NotFoundException::class)
        }

    @Test
    fun `#createWithProviderUnits should add new appointment schedule event type without provider unit association`() =
        runBlocking {
            coEvery {
                dataService.add(appointmentScheduleEventType.toModel())
            } returns appointmentScheduleEventType.toModel()

            val result = service.createWithProviderUnits(
                appointmentScheduleEventType = appointmentScheduleEventType,
                eventTypeProviderUnits = emptyList()
            )

            assertThat(result).isSuccessWithData(appointmentScheduleEventType)

            coVerify { eventTypeProviderUnitService wasNot called }
        }

    @Test
    fun `#createWithProviderUnits should create new generic event type if there is no existing event type with same sub specialty`() =
        runBlocking {
            val subSpecialtyIds = listOf(RangeUUID.generate())
            val appointmentScheduleEventType = appointmentScheduleEventType.copy(
                isMultiProfessionalReferral = true,
                subSpecialtyIds = subSpecialtyIds,
            )

            coEvery {
                dataService.add(appointmentScheduleEventType.toModel())
            } returns appointmentScheduleEventType.toModel()

            coEvery {
                dataService.find(queryEq {
                    where {
                        this.subSpecialtyIds.containsAny(subSpecialtyIds) and
                                this.status.eq(Status.ACTIVE) and
                                this.isMultiProfessionalReferral.eq(true) and
                                this.modelType.eq(healthcareModelType)
                    }
                })
            } returns emptyList()

            val result = service.createWithProviderUnits(
                appointmentScheduleEventType = appointmentScheduleEventType,
                eventTypeProviderUnits = emptyList()
            )

            assertThat(result).isSuccessWithData(appointmentScheduleEventType)

            coVerify { eventTypeProviderUnitService wasNot called }
        }

    @Test
    fun `#createWithProviderUnits should not create new generic event type if there is a existing event type with same sub specialty`() =
        runBlocking {
            val subSpecialtyIds = listOf(RangeUUID.generate())
            val appointmentScheduleEventType = appointmentScheduleEventType.copy(
                isMultiProfessionalReferral = true,
                subSpecialtyIds = subSpecialtyIds,
            )
            val model = appointmentScheduleEventType.toModel()

            coEvery {
                dataService.add(model)
            } returns model

            coEvery {
                dataService.find(queryEq {
                    where {
                        this.subSpecialtyIds.containsAny(subSpecialtyIds) and
                                this.status.eq(Status.ACTIVE) and
                                this.isMultiProfessionalReferral.eq(true) and
                                this.modelType.eq(healthcareModelType)
                    }
                })
            } returns listOf(model, model)

            val result = service.createWithProviderUnits(
                appointmentScheduleEventType = appointmentScheduleEventType,
                eventTypeProviderUnits = emptyList()
            )

            assertThat(result).isFailureOfType(ExistingGenericEventTypeWithSameSubSpecialtyException::class)

            coVerify { eventTypeProviderUnitService wasNot called }
        }

    @Test
    fun `#createWithProviderUnits should add new appointment schedule event type and associate with provider unit`() =
        runBlocking {
            val eventTypeProviderUnits = listOf(
                AppointmentScheduleEventTypeLocationAvailability(
                    providerUnitId = providerUnitId,
                    type = AppointmentScheduleEventTypeLocation.REMOTE,
                    availabilityStartTime = availabilityStartTime,
                    availabilityEndTime = availabilityEndTime
                )
            )

            coEvery {
                dataService.add(appointmentScheduleEventType.toModel())
            } returns appointmentScheduleEventType.toModel()
            coEvery {
                eventTypeProviderUnitService.associateEventTypeWithProviderUnits(
                    appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                    existingActiveEventTypeProviderUnits = emptyList(),
                    eventTypeProviderUnits = eventTypeProviderUnits
                )
            } returns listOf(eventTypeProviderUnit)

            val result = service.createWithProviderUnits(
                appointmentScheduleEventType,
                eventTypeProviderUnits
            )
            assertThat(result).isSuccessWithData(appointmentScheduleEventType)
        }

    @Test
    fun `#updateWithProviderUnits should update existing event type if there is no other event type with same sub specialty ids`() =
        runBlocking {
            val subSpecialtyIds = listOf(RangeUUID.generate())
            val appointmentScheduleEventType = appointmentScheduleEventType.copy(
                isMultiProfessionalReferral = true,
                subSpecialtyIds = subSpecialtyIds,
            )
            val model = appointmentScheduleEventType.toModel()

            coEvery {
                dataService.get(appointmentScheduleEventType.id)
            } returns model

            coEvery {
                dataService.find(queryEq {
                    where {
                        this.subSpecialtyIds.containsAny(subSpecialtyIds) and
                                this.status.eq(Status.ACTIVE) and
                                this.isMultiProfessionalReferral.eq(true) and
                                this.modelType.eq(healthcareModelType)
                    }
                })
            } returns listOf(model)

            coEvery {
                dataService.update(model)
            } returns model
            coEvery {
                eventTypeProviderUnitService.updateEventTypeProviderUnitsAssociations(
                    appointmentScheduleEventType,
                    emptyList()
                )
            } returns true
            coEvery {
                kafkaProducerService.produce(match { it.name == "APPOINTMENT-SCHEDULE-EVENT-TYPE-UPDATED" })
            } returns mockk()

            val result = service.updateWithProviderUnits(
                appointmentScheduleEventType = appointmentScheduleEventType,
                eventTypeProviderUnits = emptyList()
            )
            assertThat(result).isSuccessWithData(appointmentScheduleEventType)
        }

    @Test
    fun `#updateWithProviderUnits should not update existing event type if there is other event type with same sub specialty ids`() =
        runBlocking {
            val subSpecialtyIds = listOf(RangeUUID.generate())
            val appointmentScheduleEventType = appointmentScheduleEventType.copy(
                isMultiProfessionalReferral = false,
                subSpecialtyIds = subSpecialtyIds,
            )
            val model = appointmentScheduleEventType.toModel()

            coEvery {
                dataService.get(appointmentScheduleEventType.id)
            } returns model

            coEvery {
                dataService.find(queryEq {
                    where {
                        this.subSpecialtyIds.containsAny(subSpecialtyIds) and
                                this.status.eq(Status.ACTIVE) and
                                this.isMultiProfessionalReferral.eq(false) and
                                this.modelType.eq(healthcareModelType)
                    }
                })
            } returns listOf(model.copy(id = RangeUUID.generate()))

            coEvery {
                dataService.update(model)
            } returns model
            coEvery {
                eventTypeProviderUnitService.updateEventTypeProviderUnitsAssociations(
                    appointmentScheduleEventType,
                    emptyList()
                )
            } returns true
            coEvery {
                kafkaProducerService.produce(match { it.name == "APPOINTMENT-SCHEDULE-EVENT-TYPE-UPDATED" })
            } returns mockk()

            val result = service.updateWithProviderUnits(
                appointmentScheduleEventType = appointmentScheduleEventType,
                eventTypeProviderUnits = emptyList()
            )
            assertThat(result).isFailureOfType(ExistingGenericEventTypeWithSameSubSpecialtyException::class)
        }

    @Test
    fun `#updateWithProviderUnits should update existing appointment schedule event type`() = runBlocking {
        coEvery {
            dataService.get(appointmentScheduleEventType.id)
        } returns appointmentScheduleEventType.toModel()
        coEvery {
            dataService.update(appointmentScheduleEventType.toModel())
        } returns appointmentScheduleEventType.toModel()
        coEvery {
            eventTypeProviderUnitService.updateEventTypeProviderUnitsAssociations(
                appointmentScheduleEventType,
                emptyList()
            )
        } returns true
        coEvery {
            kafkaProducerService.produce(match { it.name == "APPOINTMENT-SCHEDULE-EVENT-TYPE-UPDATED" })
        } returns mockk()

        val result = service.updateWithProviderUnits(
            appointmentScheduleEventType = appointmentScheduleEventType,
            eventTypeProviderUnits = emptyList()
        )
        assertThat(result).isSuccessWithData(appointmentScheduleEventType)
    }

    @Test
    fun `#query should query appointment schedule event types with params`() = runBlocking {
        coEvery {
            dataService.find(
                queryEq {
                    where {
                        this.title.isNotNull()
                            .and(this.searchTokens.search("asd"))
                            .and(this.category.eq(AppointmentScheduleType.COMMUNITY))
                            .and(this.status.inList(statuses))
                    }
                        .limit { 10 }
                        .offset { 1 }
                        .orderBy { createdAt }
                        .sortOrder { SortOrder.Descending }
                }
            )
        } returns listOf(appointmentScheduleEventType.toModel())

        coEvery {
            eventTypeProviderUnitService.getForEventTypes(listOf(appointmentScheduleEventType.id))
        } returns listOf(eventTypeProviderUnit)

        coEvery {
            providerUnitService.getByIds(listOf(providerUnit.id), false)
        } returns listOf(providerUnit)

        val result = service.query(
            "asd",
            AppointmentScheduleType.COMMUNITY,
            statuses,
            IntRange(1, 10)
        )

        assertThat(result).isSuccessWithData(listOf(appointmentScheduleEventTypeWithProviderUnits))
    }

    @Test
    fun `#query should query appointment schedule event types without params`() = runBlocking {
        coEvery {
            dataService.find(
                queryEq {
                    where {
                        this.title.isNotNull()
                    }
                        .limit { 10 }
                        .offset { 1 }
                        .orderBy { createdAt }
                        .sortOrder { SortOrder.Descending }
                }
            )
        } returns listOf(appointmentScheduleEventType.toModel())

        coEvery {
            eventTypeProviderUnitService.getForEventTypes(listOf(appointmentScheduleEventType.id))
        } returns listOf(eventTypeProviderUnit)

        coEvery {
            providerUnitService.getByIds(listOf(providerUnit.id), false)
        } returns listOf(providerUnit)

        val result = service.query(
            range = IntRange(1, 10),
            status = emptyList()
        )

        assertThat(result).isSuccessWithData(listOf(appointmentScheduleEventTypeWithProviderUnits))
    }

    @Test
    fun `#getBySpecialties should get event types by specialties`() = runBlocking {
        val specialtyIds = listOf(RangeUUID.generate())
        coEvery {
            dataService.find(queryEq {
                where {
                    this.specialty.inList(specialtyIds).and(status.eq(Status.ACTIVE))
                }
            })
        } returns listOf(appointmentScheduleEventType.toModel())
        val result = service.getBySpecialties(specialtyIds)
        assertThat(result).isSuccessWithData(listOf(appointmentScheduleEventType))
    }

    @Test
    fun `#getBySubSpecialties should get event types by specialties`() = runBlocking {
        val specialtyIds = listOf(RangeUUID.generate())
        coEvery {
            dataService.find(queryEq {
                where {
                    this.subSpecialtyIds.containsAny(specialtyIds).and(status.eq(Status.ACTIVE))
                }
            })
        } returns listOf(appointmentScheduleEventType.toModel())
        val result = service.getBySubSpecialties(specialtyIds)
        assertThat(result).isSuccessWithData(listOf(appointmentScheduleEventType))
    }

    @Test
    fun `#getWithProviderUnits should return event type with its provider units`() = runBlocking {
        coEvery {
            dataService.get(
                appointmentScheduleEventType.id,
            )
        } returns appointmentScheduleEventType.toModel()
        val eventTypeProviderUnit = TestModelFactory.buildEventTypeProviderUnit(
            providerUnitId = RangeUUID.generate(),
            appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
            availabilityStartTime = availabilityStartTime,
            availabilityEndTime = availabilityEndTime
        )
        val eventTypeProviderUnits = listOf(eventTypeProviderUnit)
        val providerUnit = TestModelFactory.buildProviderUnit(id = eventTypeProviderUnit.providerUnitId!!)
        val appointmentScheduleEventTypeWithProviderUnits = AppointmentScheduleEventTypeWithProviderUnits(
            id = appointmentScheduleEventType.id,
            title = appointmentScheduleEventType.title,
            specialtyId = appointmentScheduleEventType.specialtyId,
            subSpecialtyIds = appointmentScheduleEventType.subSpecialtyIds,
            showOnApp = appointmentScheduleEventType.showOnApp,
            category = appointmentScheduleEventType.category,
            duration = appointmentScheduleEventType.duration,
            locationType = appointmentScheduleEventType.locationType,
            description = appointmentScheduleEventType.description,
            userType = appointmentScheduleEventType.userType,
            status = appointmentScheduleEventType.status,
            searchTokens = appointmentScheduleEventType.searchTokens,
            healthcareModelType = appointmentScheduleEventType.healthcareModelType,
            minimumTimeToScheduleBeforeAppointmentTime = appointmentScheduleEventType
                .minimumTimeToScheduleBeforeAppointmentTime,
            isMultiProfessionalReferral = appointmentScheduleEventType.isMultiProfessionalReferral,
            numberOfDaysFromNowToAllowScheduling = appointmentScheduleEventType.numberOfDaysFromNowToAllowScheduling,
            internalObservation = appointmentScheduleEventType.internalObservation,
            membersRisk = appointmentScheduleEventType.membersRisk,
            providerUnitIds = eventTypeProviderUnits.mapNotNull { it.providerUnitId },
            groupByType = appointmentScheduleEventType.groupByType,
            locations = listOf(
                AppointmentScheduleEventTypeLocationAvailability(
                    providerUnitId = eventTypeProviderUnit.providerUnitId,
                    type = AppointmentScheduleEventTypeLocation.ON_SITE,
                    duration = eventTypeProviderUnit.duration,
                    minimumTimeToScheduleBeforeAppointmentTime = eventTypeProviderUnit.minimumTimeToScheduleBeforeAppointmentTime,
                    numberOfDaysFromNowToAllowScheduling = eventTypeProviderUnit.numberOfDaysFromNowToAllowScheduling,
                    availableWeekDays = eventTypeProviderUnit.availableWeekDays,
                    availabilityStartTime = eventTypeProviderUnit.availabilityStartTime,
                    availabilityEndTime = eventTypeProviderUnit.availabilityEndTime,
                    providerUnitName = providerUnit.name,
                )
            ),
            updatedAt = appointmentScheduleEventType.updatedAt,
        )
        coEvery {
            eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
        } returns eventTypeProviderUnits
        coEvery {
            providerUnitService.getByIds(listOf(eventTypeProviderUnit.providerUnitId!!), false)
        } returns listOf(providerUnit).success()
        val result = service.getWithProviderUnits(appointmentScheduleEventType.id)
        assertThat(result).isSuccessWithData(appointmentScheduleEventTypeWithProviderUnits)
    }

    @Test
    fun `#findByRange should return event types sorted by its createdAt`() = runBlocking<Unit> {
        val appointmentScheduleEventType01 = appointmentScheduleEventType.copy(title = "1")
        val appointmentScheduleEventType02 =
            appointmentScheduleEventType.copy(title = "2", createdAt = LocalDateTime.now().plusDays(2))
        val appointmentScheduleEventType03 =
            appointmentScheduleEventType.copy(title = "3", createdAt = LocalDateTime.now().plusDays(1))
        val appointmentScheduleEventTypes = listOf(
            appointmentScheduleEventType01.toModel(),
            appointmentScheduleEventType02.toModel(),
            appointmentScheduleEventType03.toModel()
        )
        coEvery {
            dataService.find(
                queryEq {
                    orderBy { this.createdAt }
                        .sortOrder { this.asc }
                        .offset { 0 }
                        .limit { 2 }
                }
            )
        } returns appointmentScheduleEventTypes.sortedBy { it.createdAt }.take(2)

        val result = service.findByRange(IntRange(0, 1))
        assertThat(result).isSuccess()
        assertThat(result.get()).containsExactlyInAnyOrder(
            appointmentScheduleEventType01,
            appointmentScheduleEventType03
        )
    }

    @Test
    fun `#findBy should return event types by filters`() = runBlocking {
        val filters = AppointmentScheduleEventTypeFilters(
            ids = listOf(appointmentScheduleEventType.id),
            statuses = listOf(Status.ACTIVE),
            title = "title",
            categories = listOf(AppointmentScheduleType.OTHER),
            searchQuery = "search",
            specialtyIds = listOfNotNull(appointmentScheduleEventType.specialtyId),
            subSpecialtyIds = listOfNotNull(appointmentScheduleEventType.specialtyId),
            isMultiProfessionalReferral = true,
            range = IntRange(0, 10),
            sortOrder = SortOrder.Descending
        )

        coEvery {
            dataService.find(
                queryEq {
                    where { this.buildPredicates(filters) }
                        .offset { 0 }
                        .limit { 10 }
                        .orderBy { createdAt }
                        .sortOrder { SortOrder.Descending }
                }
            )
        } returns listOf(appointmentScheduleEventType.toModel())

        val result = service.findBy(filters)
        assertThat(result).isSuccess()

        coVerifyOnce { dataService.find(any()) }
    }

    @Test
    fun `#findBy returns error when filters are empty`() = runBlocking {
        val filters = AppointmentScheduleEventTypeFilters()

        val result = service.findBy(filters)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerify { dataService wasNot called }
    }

    @Test
    fun `#countBy should return event types by filters`() = runBlocking {
        val filters = AppointmentScheduleEventTypeFilters(
            ids = listOf(appointmentScheduleEventType.id),
            statuses = listOf(Status.ACTIVE),
            title = "title",
            categories = listOf(AppointmentScheduleType.OTHER),
            searchQuery = "search",
            specialtyIds = listOfNotNull(appointmentScheduleEventType.specialtyId),
            subSpecialtyIds = listOfNotNull(appointmentScheduleEventType.specialtyId),
            isMultiProfessionalReferral = true,
            range = IntRange(0, 10),
            sortOrder = SortOrder.Descending
        )

        coEvery {
            dataService.count(queryEq { where { this.buildPredicates(filters) } })
        } returns 1

        val result = service.countBy(filters)
        assertThat(result).isSuccess()

        coVerifyOnce { dataService.count(any()) }
    }

    @Test
    fun `#countBy returns error when filters are empty`() = runBlocking {
        val filters = AppointmentScheduleEventTypeFilters()

        val result = service.countBy(filters)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerify { dataService wasNot called }
    }

    @OptIn(WithFilterPredicateUsage::class)
    private fun AppointmentScheduleEventTypeModelDataService.FieldOptions.buildPredicates(filters: AppointmentScheduleEventTypeFilters) =
        basePredicateForFilters()
            .withFilter(filters.ids) { this.id.inList(it) }
            .withFilter(filters.statuses) { this.status.inList(it) }
            .withFilter(filters.title) { this.title.like(it) }
            .withFilter(filters.searchQuery) { this.searchTokens.search(it) }
            .withFilter(filters.categories) { this.category.inList(it) }
            .withFilter(filters.specialtyIds) { this.specialty.inList(it) }
            .withFilter(filters.subSpecialtyIds) { this.subSpecialtyIds.containsAny(it) }
            .withFilter(filters.isMultiProfessionalReferral) { this.isMultiProfessionalReferral.eq(it) }!!

}
