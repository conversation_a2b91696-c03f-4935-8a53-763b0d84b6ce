package br.com.alice.tiss.services.internal

import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.tiss.clients.v4_01_00.AttachmentClient
import br.com.alice.tiss.models.AttachmentHeader
import br.com.alice.tiss.models.OPMERequestAttachment
import br.com.alice.tiss.models.RequestedOPME
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.math.BigInteger
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

class AttachmentServiceTest {

    private val attachmentClient: AttachmentClient = mockk()

    private val service = AttachmentService(attachmentClient)

    private val opmeRequest = OPMERequestAttachment(
        requestedOPMEs = listOf(
            RequestedOPME(
                opmeIdentification = GuiaProcedure(table = "22", code = "123", quantity = 1, description = ""),
                manufacturerOption = "1",
                requestedQuantity = BigInteger.valueOf(2),
                requestedValue = 10.25.toBigDecimal()
            ),
            RequestedOPME(
                opmeIdentification = GuiaProcedure(table = "22", code = "653", quantity = 1, description = ""),
                manufacturerOption = "1",
                requestedQuantity = BigInteger.valueOf(1),
                requestedValue = 2.25.toBigDecimal()
            )
        ),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE,
            phoneNumber = "123",
            email = "<EMAIL>"
        ),
        beneficiaryData = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        origin = GuiaOrigin.EHR,
        attachmentHeader = AttachmentHeader(
            ansCode = "1234",
            attachmentGuideNumber = "************",
            referencedGuideNumber = "123",
            requestDate = LocalDate.now()
        ),
        providerCnpj = "87145534000194",
        observation = "Teste",
        materialSpecification = "Teste",
        technicalJustification = "Teste"
    )

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    @Test
    fun `#createOpme should throw error if procedures is empty`() = runBlocking {
        val result = service.createOpme(opmeRequest.copy(requestedOPMEs = emptyList()))

        ResultAssert.assertThat(result).isFailureOfType(NotFoundException::class)
        coVerifyNone { attachmentClient.createOpme(any()) }
    }

    @Test
    fun `#createOpme should throw error if cnpj is invalid`() = runBlocking {
        val result = service.createOpme(opmeRequest.copy(providerCnpj = "123"))

        ResultAssert.assertThat(result).isFailureOfType(InvalidArgumentException::class)
        coVerifyNone { attachmentClient.createOpme(any()) }
    }

    @Test
    fun `#createOpme should call createGuiaExam correctly`() = runBlocking {
        coEvery {
            attachmentClient.createOpme(opmeRequest)
        } returns true.success()

        val result = service.createOpme(opmeRequest)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { attachmentClient.createOpme(opmeRequest) }
    }

}
