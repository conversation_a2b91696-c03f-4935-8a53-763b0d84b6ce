package br.com.alice.data.layer.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.models.AIModel
import br.com.alice.data.layer.models.AIModelParams
import br.com.alice.data.layer.models.AIOperationType
import kotlinx.coroutines.runBlocking
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.Test

class AIModelDataServiceTest : DataServiceTestHelper() {
    private val aiModelDataService = AIModelDataServiceClient(httpInvoker)

    @Test
    fun `#add and get new entity`() = runBlocking {
        val model = buildAIModel()
        val resultAdd = aiModelDataService.add(model)

        assertThat(resultAdd).isSuccessWithDataIgnoringGivenFields(
            model,
            "createdAt",
            "updatedAt"
        )

        val resultGet = aiModelDataService.get(model.id)
        assertThat(resultGet).isSuccessWithDataIgnoringGivenFields(
            model,
            "createdAt",
            "updatedAt"
        )
    }

    @Test
    fun `#find by operation type`() = runBlocking {
        val model = buildAIModel()
        aiModelDataService.add(model)

        val resultFind = aiModelDataService.find {
            where { this.operationType.eq(AIOperationType.TEXT_TEXT) }
        }
        assertThat(resultFind).isSuccessWithDataIgnoringGivenFields(
            listOf(model),
            "createdAt",
            "updatedAt"
        )
    }

    private fun buildAIModel(
        id: UUID = RangeUUID.generate(),
        operationType: AIOperationType = AIOperationType.TEXT_TEXT,
        provider: String = "openai",
        name: String = "gpt-4",
        params: List<AIModelParams> = listOf(
            AIModelParams(
                name = "temperature",
                value = "0.7",
                type = "float"
            )
        ),
        apiUrl: String? = "https://api.openai.com/v1",
        internal: Boolean = false,
        costPerKToken: BigDecimal? = BigDecimal("0.03"),
        interfaceHash: String? = "abc123",
        interfaceContract: Map<String, Any>? = mapOf("input" to "string", "output" to "string")
    ) = AIModel(
        id = id,
        operationType = operationType,
        provider = provider,
        name = name,
        params = params,
        apiUrl = apiUrl,
        internal = internal,
        costPerKToken = costPerKToken,
        interfaceHash = interfaceHash,
        interfaceContract = interfaceContract
    )
} 
