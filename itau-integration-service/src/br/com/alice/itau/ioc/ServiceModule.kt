package br.com.alice.itau.ioc

import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.redis.CacheFactory
import br.com.alice.itau.client.AcquirerItauPaymentService
import br.com.alice.itau.clients.ItauAuthClient
import br.com.alice.itau.clients.ItauBolecodeClient
import br.com.alice.itau.clients.ItauBoletoClient
import br.com.alice.itau.clients.ItauPixClient
import br.com.alice.itau.consumers.ItauPaymentWebhookReceivedConsumer
import br.com.alice.itau.controllers.ItauAuthController
import br.com.alice.itau.services.AcquirerItauPaymentServiceImpl
import br.com.alice.itau.services.internal.ItauPaymentService
import br.com.alice.itau.webhooks.ItauPaymentWebhookReceiver
import org.koin.dsl.module

val ServiceModule = module(createdAtStart = true) {
    loadServiceServers("br.com.alice.itau.services")

    // Clients
    val cache = CacheFactory.newInstance("itau-integration-service-cache")
    single { ItauAuthClient(cache) }
    single { ItauPixClient(get()) }
    single { ItauBoletoClient(get()) }
    single { ItauBolecodeClient(get()) }

    //Controllers
    single { ItauAuthController() }

    // Internal Services
    single<ItauPaymentService> { ItauPaymentService(get()) }

    // Exposed Services
    single<AcquirerItauPaymentService> { AcquirerItauPaymentServiceImpl(get(), get(), get(), get()) }

    //Webhooks
    single { ItauPaymentWebhookReceiver(get()) }

    //Consumers
    single { ItauPaymentWebhookReceivedConsumer(get(), get()) }
}
