ktor {
    deployment {
        port = 8080
        port = ${?PORT}
    }
    application {
        modules = [br.com.alice.hr.core.ApplicationKt.module]
    }
}

systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}

test {
    dataapi {
        baseUrl = "https://company-demographic-gtw.staging.datalake.alice.tools"
    }

    emailTemplate {
        firebaseSignInMagicLinkTemplate = "Firebase_Sign_In_Portal_Rh_New_Team_Member_Magic_Link"
        firebaseSignInMagicLinkTemplate = ${?FIREBASE_SIGN_IN_MAGIC_LINK_TEMPLATE}
    }

    pinPoint {
        campaignId = "2c06b675e5e7457cb239591fc2edf3be"
    }

    s3 {
        templateSheetBucketUrl = "https://s3.us-east-1.amazonaws.com/web.assets.staging.alice.com.br/portal-rh-vendas/test-sheet.xlsx"
    }
}

development {
    dataapi {
        baseUrl = "https://company-demographic-gtw.staging.datalake.alice.tools"
    }

    emailTemplate {
        firebaseSignInMagicLinkTemplate = "Firebase_Sign_In_Portal_Rh_New_Team_Member_Magic_Link"
        firebaseSignInMagicLinkTemplate = ${?FIREBASE_SIGN_IN_MAGIC_LINK_TEMPLATE}
    }

    pinPoint {
        campaignId = "2c06b675e5e7457cb239591fc2edf3be"
    }

    s3 {
        templateSheetBucketUrl = "https://s3.us-east-1.amazonaws.com/web.assets.staging.alice.com.br/portal-rh-vendas/base-dados.xlsx"
        templateSheetBucketUrl = ${?S3_TEMPLATE_SHEET_BUCKET_URL}
    }
}

production {
    dataapi {
        baseUrl = "https://company-demographic-gtw.datalake.alice.tools"
        baseUrl = ${?DATA_API_BASE_URL}
    }

    emailTemplate {
        firebaseSignInMagicLinkTemplate = "Firebase_Sign_In_Portal_Rh_New_Team_Member_Magic_Link"
        firebaseSignInMagicLinkTemplate = ${?FIREBASE_SIGN_IN_MAGIC_LINK_TEMPLATE}
    }

    pinPoint {
        campaignId = "2c06b675e5e7457cb239591fc2edf3be"
        campaignId = ${?MAIL_PINPOINT_CAMPAIGN_ID}
    }

    s3 {
        templateSheetBucketUrl = "https://s3.us-east-1.amazonaws.com/web.assets.staging.alice.com.br/portal-rh-vendas/base-dados.xlsx"
        templateSheetBucketUrl = ${?S3_TEMPLATE_SHEET_BUCKET_URL}
    }
}
