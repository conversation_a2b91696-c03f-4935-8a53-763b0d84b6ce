package br.com.alice.staff.routes

import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.staff.controllers.HealthProfessionalBackfillController
import br.com.alice.staff.controllers.HealthcareTeamBackfillController
import br.com.alice.staff.controllers.StaffBackfillController
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Routing.backfillRouting() {

    val staffBackfillController by inject<StaffBackfillController>()
    val healthProfessionalBackfillController by inject<HealthProfessionalBackfillController>()
    val healthcareTeamBackfillController by inject<HealthcareTeamBackfillController>()

    route("/backfill") {
        post("/schedule_availability_days") { coHandler(staffBackfillController::changeScheduleDays) }
        post("/fill_national_id") { coHandler(staffBackfillController::fillNationalIdByEmail) }
        post("/set_specialties") { coHandler(healthProfessionalBackfillController::setSpecialties) }
        post("/set_council_type") { coHandler(healthProfessionalBackfillController::setCouncilTypeBasedOnSpecialty) }
        post("/update_demographic_count") { coHandler(healthcareTeamBackfillController::updateDemographicCount) }
    }

}
