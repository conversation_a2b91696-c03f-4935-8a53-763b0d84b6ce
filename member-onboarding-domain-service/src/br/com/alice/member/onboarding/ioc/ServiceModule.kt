package br.com.alice.member.onboarding.ioc

import br.com.alice.common.controllers.HealthController
import br.com.alice.member.onboarding.SERVICE_NAME
import br.com.alice.member.onboarding.client.MemberOnboardingService
import br.com.alice.member.onboarding.consumers.MemberActivatedConsumer
import br.com.alice.member.onboarding.consumers.MemberOnboardingDroppedConsumer
import br.com.alice.member.onboarding.consumers.MoveToPhaseConsumer
import br.com.alice.member.onboarding.consumers.PersonHealthcareTeamAssociationConsumer
import br.com.alice.member.onboarding.controllers.RecurringController
import br.com.alice.member.onboarding.services.MemberOnboardingServiceImpl
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig
import org.koin.dsl.module

val ServiceModule = module(createdAtStart = true) {
    // Configuration
    single { HoconApplicationConfig(ConfigFactory.load("application.conf")) }

    // Controllers
    single { HealthController(SERVICE_NAME) }
    single { RecurringController(get(), get(), get()) }

    // Kafka Consumers
    single { MemberOnboardingDroppedConsumer(get()) }
    single { MemberActivatedConsumer(get<MemberOnboardingService>() as MemberOnboardingServiceImpl, get(), get()) }
    single { MoveToPhaseConsumer(get(), get(), get()) }
    single { PersonHealthcareTeamAssociationConsumer(get()) }
}
