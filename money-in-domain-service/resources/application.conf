ktor {
    deployment {
        port = 8080
        port = ${?PORT}
    }
    application {
        modules = [ br.com.alice.moneyin.ApplicationKt.module ]
    }
}

systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}

development {
    mailer {
        senderName = "Alice"
        senderEmail = "<EMAIL>"

        b2BSenderName = "Alice"
        b2BSenderEmail = "<EMAIL>"

        pinPoint {
            campaignId = "2c06b675e5e7457cb239591fc2edf3be"
        }
    }

    crm {
        hubspot {
            apiKey = ""
            baseUrl = "https://api.hubapi.com",
            accessToken = ""
            dealPipeline = "65166618"
            dealStage = "126082640"

            staging {
                dealPipeline = "65166618"
                dealStage = "126082640"
            }
        }
    }

    payment {
        invoiceUrl = "https://pagamentos-dev2.dev.alice.com.br"

        iugu {
            apiKey = "A30711BD51784EAC047F6BF66914B7A160C352E020C30519B0CB1FE875C57B16"
            baseUrl = "https://api.iugu.com"
        }
    }

    auth {
        user = "test",
        pass = "test"
    }
}

test {
    mailer {
        senderName = "Alice"
        senderEmail = "<EMAIL>"

        b2BSenderName = "Alice"
        b2BSenderEmail = "<EMAIL>"

        pinPoint {
            campaignId = "2c06b675e5e7457cb239591fc2edf3be"
        }
    }

    crm {
         hubspot {
             apiKey = ""
             baseUrl = "https://api.hubapi.com"
             accessToken = ""
             dealPipeline = "65166618"
             dealStage = "126082640"

             staging {
                 dealPipeline = "65166618"
                 dealStage = "126082640"
             }
         }
    }

    payment {
        invoiceUrl = "https://pagamentos-dev2.dev.alice.com.br"

        iugu {
            apiKey = "A30711BD51784EAC047F6BF66914B7A160C352E020C30519B0CB1FE875C57B16"
            baseUrl = "https://api.iugu.com"
        }
    }

    auth {
        user = "test",
        pass = "test"
    }
}

production {
    mailer {
        senderName = "Alice"
        senderName = ${?DEFAULT_EMAIL_SENDER_NAME}

        senderEmail = "<EMAIL>"
        senderEmail = ${?DEFAULT_EMAIL_SENDER_ADDRESS}

        b2BSenderName = "Alice"
        b2BSenderName = ${?DEFAULT_B2B_EMAIL_SENDER_NAME}

        b2BSenderEmail = "<EMAIL>"
        b2BSenderEmail = ${?DEFAULT_B2B_EMAIL_SENDER_ADDRESS}

        pinPoint {
            campaignId = "2c06b675e5e7457cb239591fc2edf3be"
            campaignId = ${?MAIL_PINPOINT_CAMPAIGN_ID}
        }
    }

    crm {
        hubspot {
            apiKey = ""
            apiKey = ${?HUBSPOT_API_KEY}

            baseUrl = "https://api.hubapi.com"
            baseUrl = ${?HUBSPOT_BASE_URL}

            accessToken = ""
            accessToken = ${?HUBSPOT_ACCESS_TOKEN}

            dealPipeline = "65166618"
            dealPipeline = ${?HUBSPOT_DEAL_PIPELINE}

            dealStage = ${?HUBSPOT_DEAL_STAGE}
            dealStage = "126082640"

            staging {
                dealPipeline = "65166618"
                dealPipeline = ${?HUBSPOT_DEAL_PIPELINE}

                dealStage = "126082640"
                dealStage = ${?HUBSPOT_DEAL_STAGE}
            }
        }
    }

    payment {
        invoiceUrl = "https://pagamentos.alice.com.br"
        invoiceUrl = ${?PAYMENT_LINK_URL}

        iugu {
            apiKey = ${?IUGU_API_KEY}
            baseUrl = ${?IUGU_BASE_URL}
        }
    }

    auth {
        user = ${?DOMAIN_AUTH_USER}
        pass = ${?DOMAIN_AUTH_PASS}
    }
}
