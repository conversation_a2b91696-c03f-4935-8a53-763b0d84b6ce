package br.com.alice.api.backoffice.services

import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.models.HealthSpecialistScoreEnum
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class StaffBackofficeServiceTest {

    private lateinit var service: StaffBackofficeService

    @BeforeEach
    fun setup() {
        service = StaffBackofficeService()
    }

    @Test
    fun `getStaffRoles should return roles filtered by staff type`() {
        val staffType = StaffType.COMMUNITY_SPECIALIST
        val result = service.getStaffRoles(staffType)

        assertTrue(result.isNotEmpty())

        result.forEach { roleResponse ->
            val role = Role.valueOf(roleResponse.id)
            assertTrue(role.types.contains(staffType), "Role ${role.name} should contain staff type $staffType")
        }

        result.forEach { roleResponse ->
            val role = Role.valueOf(roleResponse.id)
            assertEquals(role.name, roleResponse.id)
            assertEquals(role.description, roleResponse.name)
            assertEquals(role.name, roleResponse.value)
        }
    }

    @Test
    fun `getStaffRoles should return different roles for different staff types`() {
        val communitySpecialistRoles = service.getStaffRoles(StaffType.COMMUNITY_SPECIALIST)
        val healthProfessionalRoles = service.getStaffRoles(StaffType.HEALTH_PROFESSIONAL)
        val pitayaRoles = service.getStaffRoles(StaffType.PITAYA)

        assertTrue(communitySpecialistRoles.isNotEmpty())
        assertTrue(healthProfessionalRoles.isNotEmpty())
        assertTrue(pitayaRoles.isNotEmpty())

        val communitySpecialistRoleIds = communitySpecialistRoles.map { it.id }.toSet()
        val healthProfessionalRoleIds = healthProfessionalRoles.map { it.id }.toSet()
        val pitayaRoleIds = pitayaRoles.map { it.id }.toSet()

        assertTrue(communitySpecialistRoleIds != healthProfessionalRoleIds ||
                 communitySpecialistRoleIds != pitayaRoleIds ||
                 healthProfessionalRoleIds != pitayaRoleIds)
    }

    @Test
    fun `getStaffTiers should return all specialist tiers`() {
        val result = service.getStaffTiers()

        assertEquals(SpecialistTier.values().size, result.size)

        val resultTierIds = result.map { it.id }.toSet()
        val expectedTierIds = SpecialistTier.values().map { it.name }.toSet()
        assertEquals(expectedTierIds, resultTierIds)

        result.forEach { tierResponse ->
            val tier = SpecialistTier.valueOf(tierResponse.id)
            assertEquals(tier.name, tierResponse.id)
            assertEquals(tier.description, tierResponse.name)
            assertEquals(tier.name, tierResponse.value)
        }
    }

    @Test
    fun `getStaffScore should return all health specialist scores`() {
        val result = service.getStaffScore()

        assertEquals(HealthSpecialistScoreEnum.values().size, result.size)

        val resultScoreIds = result.map { it.id }.toSet()
        val expectedScoreIds = HealthSpecialistScoreEnum.values().map { it.name }.toSet()
        assertEquals(expectedScoreIds, resultScoreIds)

        result.forEach { scoreResponse ->
            val score = HealthSpecialistScoreEnum.valueOf(scoreResponse.id)
            assertEquals(score.name, scoreResponse.id)
            assertEquals(score.description, scoreResponse.name)
            assertEquals(score.name, scoreResponse.value)
        }
    }

    @Test
    fun `getCouncilTypes should return all council types`() {
        val result = service.getCouncilTypes()

        assertEquals(CouncilType.values().size, result.size)

        val resultCouncilTypeIds = result.map { it.id }.toSet()
        val expectedCouncilTypeIds = CouncilType.values().map { it.code }.toSet()
        assertEquals(expectedCouncilTypeIds, resultCouncilTypeIds)

        result.forEach { councilTypeResponse ->
            val councilType = CouncilType.values().find { it.code == councilTypeResponse.id }
            assertEquals(councilType?.code, councilTypeResponse.id)
            assertEquals(councilType?.name, councilTypeResponse.name)
        }
    }

    @Test
    fun `getStaffRoles should return empty list for unknown staff type`() {
        val mockStaffType = StaffType.values().first()
        val rolesWithoutType = Role.values().filter { !it.types.contains(mockStaffType) }

        if (rolesWithoutType.isNotEmpty()) {
            val result = service.getStaffRoles(mockStaffType)

            result.forEach { roleResponse ->
                val role = Role.valueOf(roleResponse.id)
                assertTrue(role.types.contains(mockStaffType))
            }
        }
    }
}
