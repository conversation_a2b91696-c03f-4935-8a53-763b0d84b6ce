package br.com.alice.app.content.services.screens

import br.com.alice.app.content.client.AppointmentScheduleEventService
import br.com.alice.app.content.client.screens.AppointmentHubScreenService
import br.com.alice.app.content.model.Accessory
import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.Alignment
import br.com.alice.app.content.model.AppBar
import br.com.alice.app.content.model.Button
import br.com.alice.app.content.model.CardColorScheme
import br.com.alice.app.content.model.CardSize
import br.com.alice.app.content.model.CardType
import br.com.alice.app.content.model.Label
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.RemoteActionTransition
import br.com.alice.app.content.model.ScreenAlignment
import br.com.alice.app.content.model.ScreenAlignmentType
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreenModule
import br.com.alice.app.content.model.ScreenProperties
import br.com.alice.app.content.model.ScreenType
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.SectionPadding
import br.com.alice.app.content.model.SectionTextLayout
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.Size
import br.com.alice.app.content.model.Variant
import br.com.alice.app.content.model.appointmentDetails
import br.com.alice.app.content.model.getCheshireScreenFromBudProtocolId
import br.com.alice.app.content.model.getCheshireScreenNavigationAction
import br.com.alice.app.content.model.healthMeetingsList
import br.com.alice.app.content.model.labs
import br.com.alice.app.content.model.redesignAliceAgora
import br.com.alice.app.content.model.saraCalendar
import br.com.alice.app.content.model.section.CardSection
import br.com.alice.app.content.model.section.GridGutter
import br.com.alice.app.content.model.section.GridItemSection
import br.com.alice.app.content.model.section.GridSection
import br.com.alice.app.content.model.section.GridVariant
import br.com.alice.app.content.model.section.SheetSection
import br.com.alice.app.content.model.specialists
import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.person.client.MemberService
import br.com.alice.schedule.model.AppointmentScheduleWithStaff
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import kotlin.test.AfterTest

class AppointmentHubScreenServiceImplTest : MockedTestHelper() {
    private val appointmentScheduleEventService: AppointmentScheduleEventService = mockk()
    private val personClinicalAccountService: PersonClinicalAccountService = mockk()
    private val memberService: MemberService = mockk()


    private val service: AppointmentHubScreenService =
        AppointmentHubScreenServiceImpl(
            appointmentScheduleEventService,
            personClinicalAccountService,
            memberService,
        )

    private val personId = PersonId()

    private val healthCareTeamAppointment = TestModelFactory.buildAppointmentSchedule(
        type = AppointmentScheduleType.HEALTHCARE_TEAM,
    )

    private val multiTeamAppointment = TestModelFactory.buildAppointmentSchedule(
        type = AppointmentScheduleType.PSYCHOLOGIST,
        eventName = "Consulta com psicólogo",
        startTime = LocalDateTime.of(2020, 1, 2, 10, 10)
    )

    private val staff = TestModelFactory.buildStaff(profileImageUrl = "profileImageUrl")
    private val personClinicalAccount = TestModelFactory.buildPersonClinicalAccount(personId = personId)
    private val member = TestModelFactory.buildMember(
        personId = personId,
        activationDate = LocalDateTime.now()
    )

    private val healthCareTeamAppointmentWithStaff = AppointmentScheduleWithStaff(healthCareTeamAppointment, staff)
    private val multiTeamAppointmentWithStaff = AppointmentScheduleWithStaff(multiTeamAppointment, staff)

    private val prescriptionProtocol = "2b531a26-7d1a-4dc3-b4df-905e2a605100"

    private val saraUrl =
        "https://webview.alice.com.br/scheduler/?appointmentScheduleEventTypeId=********-4f24-4067-916a-1d1777ed1300&v=1#/schedule"

    @AfterTest
    fun after() {
        confirmVerified(
            appointmentScheduleEventService
        )
    }

    @Test
    fun `#getScreen should return sections with Agendar title and ScheduleCard when appointmentSchedules is null`() =
        runBlocking {
            withFeatureFlag(
                namespace = FeatureNamespace.ALICE_APP,
                key = "should_show_empty_mfc",
                value = false
            ) {
                val expected = getScreen(
                    titleText = "Agendar",
                    cardSection = cardSection(
                        cardId = "schedule_card",
                        cardType = CardType.MAIN,
                        cardTitle = "Agendar com meu médico de família"
                    )
                )
                coEvery { appointmentScheduleEventService.buildSaraScheduleUrlVersion(personId) } returns saraUrl.success()

                val result = service.getScreen(personId, null)
                assertThat(result).isSuccessWithData(expected)
                coVerifyOnce {
                    appointmentScheduleEventService.buildSaraScheduleUrlVersion(any())
                }

                coVerifyNone {
                    personClinicalAccountService.getByPersonId(any())
                }
            }
        }


    @Test
    fun `#getScreen should return sections with Agendar title and ScheduleCard when appointmentSchedules is empty`() =
        runBlocking {
            withFeatureFlag(
                namespace = FeatureNamespace.ALICE_APP,
                key = "should_show_empty_mfc",
                value = false
            ) {

                val expected = getScreen(
                    titleText = "Agendar",
                    cardSection = cardSection(
                        cardId = "schedule_card",
                        cardType = CardType.MAIN,
                        cardTitle = "Agendar com meu médico de família",
                    ),
                )

                coEvery { appointmentScheduleEventService.buildSaraScheduleUrlVersion(personId) } returns saraUrl.success()

                val result = service.getScreen(personId, emptyList())
                assertThat(result).isSuccessWithData(expected)
                coVerifyOnce {
                    appointmentScheduleEventService.buildSaraScheduleUrlVersion(any())
                }

                coVerifyNone {
                    personClinicalAccountService.getByPersonId(any())
                }

            }
        }

    @Test
    fun `#getScreen should return sections with title and appointment card when appointmentSchedules has one HealthCareTeam appointment`() =
        runBlocking {
            withFeatureFlag(
                namespace = FeatureNamespace.ALICE_APP,
                key = "should_show_empty_mfc",
                value = false
            ) {
                val expected = getScreen(
                    titleText = "Meus agendamentos",
                    cardSection = cardSection(
                        cardId = "appointment_card_0",
                        cardType = CardType.APPOINTMENT,
                        cardTitle = "Quarta, 01 de janeiro",
                        cardSubtitle = "07:10",
                        cardDescription = "Primeira consulta com seu time de saúde com José",
                        cardIcon = null,
                        cardImageUrl = "profileImageUrl",
                        cardAction = RemoteAction.appointmentDetails(healthCareTeamAppointment.id),
                        cardActionId = "appointment_detail_action",
                        cardActionLabel = "Detalhes do agendamento",
                    )
                )

                val result = service.getScreen(personId, listOf(healthCareTeamAppointmentWithStaff))
                assertThat(result).isSuccessWithData(expected)

                coVerifyNone {
                    personClinicalAccountService.getByPersonId(any())
                }

            }
        }

    @Test
    fun `#getScreen should return with empty MFC card`() =
        runBlocking {
            withFeatureFlag(
                namespace = FeatureNamespace.ALICE_APP,
                key = "should_show_empty_mfc",
                value = true
            ) {
                val expected = getScreen(
                    titleText = "Agendar",
                    cardSection = Section(
                        id = "empty_mfc_card",
                        type = SectionType.CARD_SECTION,
                        minAppVersion = SectionType.CARD_SECTION.minAppVersion!!,
                        data = CardSection(
                            title = "Você ainda não escolheu seu Médico",
                            description = "Após ${
                                member.activationDate!!.plusDays(60L).toBrazilianDateFormat()
                            } seu médico será atribuído automaticamente",
                            type = CardType.MAIN,
                            size = CardSize.MEDIUM,
                            colorScheme = CardColorScheme.BRAND,
                            alignment = Alignment.RIGHT_BOTTOM,
                            accessoryIcon = "doctor",
                            onCardClick = RemoteAction(
                                mobileRoute = ActionRouting.MEMBER_ONBOARDING_V2_COVER,
                                params = mapOf(
                                    "path" to "mfc_cover",
                                )
                            ),
                            buttonPrimary = Button(
                                id = "health_professional_select_cover",
                                label = Label(
                                    text = "Escolher meu médico",
                                    icon = "right-arrow",
                                    accessory = Accessory.RIGHT
                                ),
                                variant = Variant.CUSTOM,
                                size = Size.MEDIUM,
                                enabled = true,
                                loading = false,
                                shrinkWrap = false,
                                onTapAction = RemoteAction(
                                    mobileRoute = ActionRouting.MEMBER_ONBOARDING_V2_COVER,
                                    params = mapOf(
                                        "path" to "mfc_cover",
                                    )
                                )
                            )
                        )
                    )

                )

                coEvery { personClinicalAccountService.getByPersonId(personId) } returns NotFoundException("").failure()
                coEvery { memberService.getCurrent(personId) } returns member.success()

                val result = service.getScreen(personId, emptyList())
                assertThat(result).isSuccessWithData(expected)
                coVerifyOnce {
                    personClinicalAccountService.getByPersonId(any())
                    memberService.getCurrent(any())
                }
            }
        }

    @Test
    fun `#getScreen should return appointment card carousel with scheduled cards when appointmentSchedules has HealthCareTeam and multi appointment`() =
        runBlocking {
            withFeatureFlag(
                namespace = FeatureNamespace.ALICE_APP,
                key = "should_show_empty_mfc",
                value = false
            ) {
                val expected = getScreen(
                    titleText = "Meus agendamentos",
                    carouselSection(
                        listOf(
                            cardSection(
                                cardId = "appointment_card_0",
                                cardType = CardType.APPOINTMENT,
                                cardTitle = "Quarta, 01 de janeiro",
                                cardSubtitle = "07:10",
                                cardDescription = "Primeira consulta com seu time de saúde com José",
                                cardIcon = null,
                                cardImageUrl = "profileImageUrl",
                                cardAction = RemoteAction.appointmentDetails(healthCareTeamAppointment.id),
                                cardActionId = "appointment_detail_action",
                                cardActionLabel = "Detalhes do agendamento",
                            ),
                            cardSection(
                                cardId = "appointment_card_1",
                                cardType = CardType.APPOINTMENT,
                                cardTitle = "Quinta, 02 de janeiro",
                                cardSubtitle = "07:10",
                                cardDescription = "Consulta com psicólogo com José",
                                cardIcon = null,
                                cardImageUrl = "profileImageUrl",
                                cardAction = RemoteAction.appointmentDetails(multiTeamAppointment.id),
                                cardActionId = "appointment_detail_action",
                                cardActionLabel = "Detalhes do agendamento",
                            ),
                        )
                    )
                )

                val result = service.getScreen(
                    personId,
                    listOf(
                        healthCareTeamAppointmentWithStaff,
                        multiTeamAppointmentWithStaff
                    )
                )
                assertThat(result).isSuccessWithData(expected)

                coVerifyNone {
                    personClinicalAccountService.getByPersonId(any())
                }
            }
        }

    @Test
    fun `#getScreen should return appointment card carousel with scheduled multi appointment and schedule card with sara url when appointmentSchedules has only multi appointment`() =
        runBlocking {
            withFeatureFlag(
                namespace = FeatureNamespace.ALICE_APP,
                key = "should_show_empty_mfc",
                value = false
            ) {
                val expected = getScreen(
                    titleText = "Meus agendamentos",
                    carouselSection(
                        listOf(
                            cardSection(
                                cardId = "appointment_card_0",
                                cardType = CardType.APPOINTMENT,
                                cardTitle = "Quinta, 02 de janeiro",
                                cardSubtitle = "07:10",
                                cardDescription = "Consulta com psicólogo com José",
                                cardIcon = null,
                                cardImageUrl = "profileImageUrl",
                                cardAction = RemoteAction.appointmentDetails(multiTeamAppointment.id),
                                cardActionId = "appointment_detail_action",
                                cardActionLabel = "Detalhes do agendamento",
                            ),
                            cardSection(
                                cardId = "schedule_card",
                                cardType = CardType.MAIN,
                                cardTitle = "Agendar com meu médico de família",
                                cardAction = RemoteAction.saraCalendar(saraUrl),
                            ),
                        )
                    )

                )

                coEvery { appointmentScheduleEventService.buildSaraScheduleUrlVersion(personId) } returns saraUrl.success()

                val result = service.getScreen(
                    personId,
                    listOf(
                        multiTeamAppointmentWithStaff
                    )
                )
                assertThat(result).isSuccessWithData(expected)
                coVerifyOnce {
                    appointmentScheduleEventService.buildSaraScheduleUrlVersion(any())
                }

                coVerifyNone {
                    personClinicalAccountService.getByPersonId(any())
                }
            }
        }

    @Test
    fun `#getAliceAgoraSheet should returns a AliceAgora sheet`() =
        runBlocking {
            val expected = getAliceAgoraSheet()

            val result = service.getAliceAgoraSheet()
            assertThat(result).isSuccessWithData(expected)
        }

    private fun getAliceAgoraSheet() = ScreensTransport(
        id = ScreenType.APPOINTMENT_HUB_ALICE_AGORA_SHEET.value,
        properties = ScreenProperties(
            alignment = ScreenAlignment(
                vertical = ScreenAlignmentType.END,
                horizontal = ScreenAlignmentType.CENTER
            )
        ),
        layout = ScreenLayout(
            type = "single_column",
            appBar = null,
            body = listOf(
                Section(
                    type = SectionType.SHEET_SECTION,
                    id = "appointment_hub_alice_agora_sheet",
                    data = SheetSection(
                        title = "Atendimento no Alice Agora",
                        content = SheetSection.Content(
                            text = "Iremos te redirecionar para o chat com nossa equipe para triagem dos sintomas para receber o atendimento necessário.",
                        ),
                        confirmationButton = Button(
                            id = "appointment_hub_alice_agora_sheet_confirm_button",
                            variant = Variant.PRIMARY,
                            shrinkWrap = false,
                            onTapAction = RemoteAction.redesignAliceAgora(),
                            label = Label(
                                text = "Iniciar atendimento",
                                icon = "right-arrow",
                                accessory = Accessory.RIGHT
                            )
                        ),
                        illustrationUrl = "",
                    ),
                    minAppVersion = SectionType.SHEET_SECTION.minAppVersion!!
                )
            )
        )
    )

    private fun getScreen(
        titleText: String = "Agendar",
        cardSection: Section? = null,
        carouselSection: Section? = null,
    ) =
        ScreensTransport(
            id = ScreenType.APPOINTMENT_HUB.value,
            layout = ScreenLayout(
                type = "single_column",
                appBar = AppBar(
                    title = "Agendamentos",
                    back = "",
                ),
                body = listOf(
                    ScreenModule.getInfiniteScrollModuleSection(
                        ScreenModule.GROUPED_COLUMN_MODULE,
                        ScreenType.APPOINTMENT_HUB,
                        subSections = listOf(
                            ScreenModule.getTextSectionWithContent(
                                sectionId = "text_schedule",
                                text = titleText,
                                layout = SectionTextLayout.TITLE_MEDIUM_HIGHLIGHT,
                                alignment = Alignment.LEFT,
                                verticalPadding = SectionPadding.P0,
                            ),
                            cardSection ?: carouselSection!!,
                            ScreenModule.getListMenuSectionWithBody(
                                sectionId = "schedule_menu",
                                body = listOf(
                                    ScreenModule.getMenuSection(
                                        sectionId = "test_and_procedures_schedule",
                                        title = "Exames ou procedimentos",
                                        icon = "lab",
                                        onTapAction = RemoteAction.labs(),
                                    ),
                                    ScreenModule.getMenuSection(
                                        sectionId = "specialists_schedule",
                                        title = "Especialistas e clínicas",
                                        icon = "doctor",
                                        onTapAction = RemoteAction.specialists(),
                                    ),
                                    ScreenModule.getMenuSection(
                                        sectionId = "class_schedule",
                                        title = "Aulas ou encontros",
                                        icon = "team",
                                        onTapAction = RemoteAction.healthMeetingsList(),
                                    ),
                                ),
                            ),
                            ScreenModule.getTextSectionWithContent(
                                sectionId = "text_help",
                                text = "Precisa de ajuda?",
                                layout = SectionTextLayout.TITLE_MEDIUM_HIGHLIGHT,
                                alignment = Alignment.LEFT,
                                verticalPadding = SectionPadding.P0,
                            ),
                            ScreenModule.getListMenuSectionWithBody(
                                sectionId = "help_menu",
                                body = listOf(
                                    ScreenModule.getMenuSection(
                                        sectionId = "prescription_renewal",
                                        title = "Quero renovar minha receita",
                                        icon = "pill",
                                        onTapAction = RemoteAction.getCheshireScreenFromBudProtocolId(
                                            prescriptionProtocol,
                                            screenId = "appointment_hub_prescription"
                                        ),
                                    ),
                                    ScreenModule.getMenuSection(
                                        sectionId = "alice_agora",
                                        title = "Preciso de atendimento agora",
                                        icon = "alert_rotating_light",
                                        onTapAction = RemoteAction.Companion.getCheshireScreenNavigationAction(
                                            ScreenType.APPOINTMENT_HUB_ALICE_AGORA_SHEET,
                                            transition = RemoteActionTransition.BOTTOM_SHEET,
                                        ),
                                    ),
                                )
                            )
                        ),
                        trackUserBehavior = true
                    )
                ),
            ),
        )

    private fun cardSection(
        cardId: String = "schedule_card",
        cardType: CardType = CardType.MAIN,
        cardTitle: String = "Agendar com meu médico de família",
        cardSubtitle: String? = null,
        cardDescription: String? = null,
        cardActionId: String = "schedule_action",
        cardActionLabel: String = "Agendar",
        cardAction: RemoteAction = RemoteAction.saraCalendar(saraUrl),
        cardIcon: String? = "calendar",
        cardImageUrl: String? = null,
    ) = Section(
        id = cardId,
        type = SectionType.CARD_SECTION,
        data = CardSection(
            type = cardType,
            subtitle = cardSubtitle,
            description = cardDescription,
            title = cardTitle,
            alignment = Alignment.RIGHT_BOTTOM,
            colorScheme = CardColorScheme.BRAND,
            size = CardSize.MEDIUM,
            accessoryIcon = cardIcon,
            accessoryImage = cardImageUrl,
            buttonPrimary = Button(
                id = cardActionId,
                label = Label(
                    text = cardActionLabel,
                    icon = "right-arrow",
                    accessory = Accessory.RIGHT
                ),
                onTapAction = cardAction
            ),
            onCardClick = cardAction
        ),
        minAppVersion = SectionType.CARD_SECTION.minAppVersion!!,
    )

    private fun carouselSection(
        children: List<Section>,
    ) = Section(
        id = "appointment_carousel_grid",
        type = SectionType.GRID_SECTION,
        data = GridSection(
            children = listOf(
                Section(
                    id = "appointment_carousel_grid_item",
                    type = SectionType.GRID_ITEM_SECTION,
                    data = GridItemSection(
                        child = ScreenModule.stackedCarousel(
                            sectionId = "appointments_carousel",
                            children = children,
                        ),
                        span = 4,
                    ),
                    minAppVersion = SectionType.GRID_ITEM_SECTION.minAppVersion!!
                ),
            ),
            gutter = GridGutter.GUTTER_8,
            gridVariant = GridVariant.AUTOMATIC,
        ),
        minAppVersion = SectionType.GRID_SECTION.minAppVersion!!,
    )
}
