package br.com.alice.nullvs.services.internals

import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.foldNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.CompanyContract
import br.com.alice.data.layer.models.CompanyContractIntegrationStatus
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.CompanySubContractIntegrationStatus
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.data.layer.models.NullvsIntegrationLog
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.converters.NullvsCompanyConverter.toCompanyProductPriceListingData
import br.com.alice.nullvs.exceptions.BillingAccountablePartyIdRequiredException
import br.com.alice.nullvs.exceptions.BillingGroupRequiredException
import br.com.alice.nullvs.exceptions.ContractDoesNotHaveStartAtDate
import br.com.alice.nullvs.exceptions.ContractFileDoesNotExist
import br.com.alice.nullvs.exceptions.DependsOnModelException
import br.com.alice.nullvs.exceptions.ExternalClientNotFound
import br.com.alice.nullvs.exceptions.ExternalCompanyContractNotFound
import br.com.alice.nullvs.exceptions.NatureRequiredException
import br.com.alice.nullvs.featureFlags.NullvsIntegrationServiceFeatureFlags
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.company.CompanyContractData
import br.com.alice.nullvs.models.company.CompanySubContractData
import br.com.alice.nullvs.models.company.NullvsCompanyContractBatchRequest
import br.com.alice.nullvs.models.company.NullvsCompanySubContractBatchRequest
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class TotvsCompanyIntegrationService(
    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService,
    private val contractService: CompanyContractService,
    private val companyService: CompanyService,
    private val companyProductPriceListingService: CompanyProductPriceListingService,
    private val nullvsIntegrationLogService: NullvsIntegrationLogService,
    private val subContracService: CompanySubContractService
) {

    suspend fun createContract(
        meta: Meta,
        contract: CompanyContract
    ): Result<NullvsCompanyContractBatchRequest, Throwable> {
        return createContractRequest(meta, contract, NullvsActionType.CREATE)
            .thenError { error ->
                if (error !is ContractFileDoesNotExist) {
                    treatValidationError(meta, error, NullvsActionType.CREATE)
                    updateContractIntegrationStatus(contract.id, CompanyContractIntegrationStatus.FAILED)
                }
            }
    }


    suspend fun updateContract(
        meta: Meta,
        contract: CompanyContract
    ): Result<NullvsCompanyContractBatchRequest, Throwable> {
        val action = if (contract.externalId == null) NullvsActionType.CREATE else NullvsActionType.UPDATE
        return createContractRequest(meta, contract, action)
            .thenError {
                if (it !is ContractFileDoesNotExist) {
                    treatValidationError(meta, it, action)
                }
            }
    }

    private suspend fun createContractRequest(meta: Meta, contract: CompanyContract, action: NullvsActionType) =
        if (action == NullvsActionType.CREATE && contract.contractFileIds.isEmpty()) {
            ContractFileDoesNotExist(contract.id).failure()
        } else if (contract.startedAt == null) {
            ContractDoesNotHaveStartAtDate(contract.id).failure()
        } else {
            findClientForClient(
                contract.id,
                contract.isBillingLevel,
                contract.billingAccountablePartyId,
                contract.nature,
                null,
                "Contract",
            ).map { client ->
                NullvsCompanyContractBatchRequest(
                    meta,
                    action,
                    contractData = CompanyContractData(
                        number = contract.externalId,
                        isBillingLevel = contract.isBillingLevel,
                        codeClient = client,
                        dueDate = contract.dueDate,
                        id = contract.id,
                        groupCompany = contract.groupCompany!!,
                        isProRata = contract.isProRata,
                        startedAt = contract.startedAt!!,
                        nature = contract.nature,
                    ),
                )
            }
        }

    private fun shouldUseNullvsDependencySystem() =
        FeatureService.get(FeatureNamespace.NULLVS, "use_dependency_service", true)

    private suspend fun findClientForClient(
        id: UUID,
        isBillingLevel: Boolean,
        billingAccountablePartyId: UUID?,
        nature: String?,
        billingGroup: String?,
        model: String
    ) = if (isBillingLevel) {
        when {
            billingAccountablePartyId == null -> BillingAccountablePartyIdRequiredException(id, model).failure()
            nature == null -> NatureRequiredException(id, model).failure()
            billingGroup == null && model == "SubContract" -> BillingGroupRequiredException(id, model).failure()
            else ->
                findInternalIdAndModel(
                    billingAccountablePartyId,
                    InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                )
                    .foldNotFound {
                        if (shouldUseNullvsDependencySystem())
                            DependsOnModelException(
                                billingAccountablePartyId,
                                InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                            ).failure()
                        else ExternalClientNotFound(billingAccountablePartyId).failure()
                    }
                    .map { it.externalId }
        }
    } else {
        Result.success(null)
    }

    private suspend fun findInternalIdAndModel(internalId: UUID, internalModelType: InternalModelType) =
        nullvsIntegrationRecordService.findByInternalIdAndModel(internalId, internalModelType)
            .then {
                logger.info(
                    "found integration record",
                    "id" to it.id,
                    "internal_id" to it.internalId,
                    "internal_model_name" to it.internalModelName,
                    "external_id" to it.externalId,
                    "external_model_name" to it.externalModelName,
                    "integrated_at" to it.integratedAt
                )
            }.map {
                it.copy(externalId = it.externalId.trim())
            }

    suspend fun createSubContract(meta: Meta, subContract: CompanySubContract) =
        createSubContractRequest(meta, subContract, NullvsActionType.CREATE)
            .thenError {
                treatValidationError(meta, it, NullvsActionType.CREATE)
                updateSubContractIntegrationStatus(subContract.id, CompanySubContractIntegrationStatus.FAILED)
            }

    suspend fun updateSubContract(
        meta: Meta,
        subContract: CompanySubContract,
        shouldUpdateMemberPriceListing: Boolean? = null
    ): Result<NullvsCompanySubContractBatchRequest, Throwable> {
        val action = if (subContract.externalId == null) NullvsActionType.CREATE else NullvsActionType.UPDATE
        return createSubContractRequest(meta, subContract, action, shouldUpdateMemberPriceListing)
            .thenError {
                treatValidationError(meta, it, action)
            }
    }

    private suspend fun createSubContractRequest(
        meta: Meta,
        subcontract: CompanySubContract,
        action: NullvsActionType,
        shouldUpdateMemberPriceListing: Boolean? = null
    ) = coResultOf<NullvsCompanySubContractBatchRequest, Throwable> {
        coroutineScope {
            val contractDeferred =
                async { contractService.get(subcontract.contractId) }
            val companyDeferred = async { companyService.get(subcontract.companyId).get() }
            val companyProductPriceListingDeferred = async {
                companyProductPriceListingService.findCurrentBySubContractId(
                    subcontract.id,
                    CompanyProductPriceListingService.FindOptions(withProduct = true, withPriceListing = true)
                ).get()
            }

            val contract = contractDeferred.await().get()
            val company = companyDeferred.await()
            val companyProductPriceListing = companyProductPriceListingDeferred.await()

            if (contract.externalId == null) {
                if (shouldUseNullvsDependencySystem())
                    throw DependsOnModelException(contract.id, InternalModelType.CONTRACT)
                else
                    throw ExternalCompanyContractNotFound(contract.id)
            }

            if (contract.startedAt == null) {
                throw ContractDoesNotHaveStartAtDate(contract.id)
            }

            val client =
                findClientForClient(
                    subcontract.id,
                    subcontract.isBillingLevel,
                    subcontract.billingAccountablePartyId,
                    subcontract.nature,
                    subcontract.billingGroup,
                    "SubContract",
                ).get()

            logger.info(
                "Creating NullvsCompanySubContractBatchRequest",
                "contract_id" to "ID-${contract.id}-ID",
                "contract_start_at" to contract.startedAt,
                "subcontract" to subcontract,
                "contract" to contract,
                "company" to company,
                "client" to client,
                "companyProductPriceListing" to companyProductPriceListing
            )

            NullvsCompanySubContractBatchRequest(
                meta,
                action,
                subContractData = CompanySubContractData(
                    number = subcontract.externalId,
                    isBillingLevel = subcontract.isBillingLevel,
                    codeClient = client,
                    dueDate = subcontract.dueDate,
                    contractNumber = contract.externalId!!,
                    id = subcontract.id,
                    cnpj = company.cnpj,
                    description = company.legalName,
                    shortDescription = company.name,
                    groupCompany = contract.groupCompany!!,
                    billingGroup = subcontract.billingGroup,
                    hasProRata = subcontract.isProRata ?: contract.isProRata ?: false,
                    hasRetroactiveCharge = subcontract.hasRetroactiveCharge ?: contract.hasRetroactiveCharge ?: false,
                    startedAt = contract.startedAt!!,
                    contractId = contract.id,
                    readjustmentMonth = contract.startedAt?.month ?: subcontract.createdAt.month,
                    nature = subcontract.nature,
                    availableCompanyProductPriceListing = companyProductPriceListing.map { it.toCompanyProductPriceListingData() },
                    shouldUpdateMemberPriceListing = shouldUpdateMemberPriceListing,
                ),
            )
        }
    }

    private suspend fun treatValidationError(meta: Meta, ex: Throwable, action: NullvsActionType) {

        val batchType = when (action) {
            NullvsActionType.CREATE -> BatchType.CREATE
            NullvsActionType.UPDATE -> BatchType.UPDATE
            NullvsActionType.CANCEL -> BatchType.CANCEL
            else -> BatchType.CREATE
        }

        var errorLog = NullvsIntegrationLog(
            eventId = meta.eventId,
            eventName = meta.eventName,
            integrationEventName = meta.integrationEventName,
            internalId = meta.internalId,
            internalModelName = meta.internalModelName,
            externalModelName = meta.externalModelName,
            batchId = "",
            idSoc = "",
            batchType = batchType,
            payloadSequenceId = 1,
            description = ex.message,
            status = LogStatus.TOTVS_NOT_CALLED
        )

        if (ex is DependsOnModelException) {
            errorLog = errorLog.copy(
                dependsOnId = ex.id,
                dependsOnModel = ex.model,
                status = LogStatus.WAITING,
            )
        }

        if (NullvsIntegrationServiceFeatureFlags.isCheckErrorValidationEnabled()) {
            nullvsIntegrationLogService.checkToCreateErrorLog(
                errorLog
            )
        } else {
            nullvsIntegrationLogService.add(errorLog)
        }
    }

    private suspend fun updateSubContractIntegrationStatus(
        subContractId: UUID,
        status: CompanySubContractIntegrationStatus
    ) =
        subContracService.get(subContractId)
            .flatMap { subContracService.update(it.copy(integrationStatus = status), false) }
            .then {
                logger.info("Updated subcontract status to $status", "subcontractId" to it.id)
            }.thenError {
                logger.error("Error updating subcontract status", "subcontractId" to subContractId, it)
            }

    private suspend fun updateContractIntegrationStatus(contractId: UUID, status: CompanyContractIntegrationStatus) =
        contractService.get(contractId).flatMap { contractService.update(it.copy(integrationStatus = status), false) }
            .then {
                logger.info("Updated contract status to $status", "contractId" to it.id)
            }.thenError {
                logger.error("Error updating contract status", "contractId" to contractId, it)
            }
}
