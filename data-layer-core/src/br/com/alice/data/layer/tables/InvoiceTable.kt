package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.data.layer.models.InvoiceExpenseType
import br.com.alice.data.layer.models.InvoiceType
import br.com.alice.data.layer.models.TissInvoiceStatus
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class InvoiceTable(
    override val id: UUID = RangeUUID.generate(),

    val code: String,
    val execIndicatorAuthorizerId: UUID? = null,
    val providerUnitId: UUID? = null,
    val userEmail: String,
    val status: TissInvoiceStatus,
    val type: InvoiceType? = InvoiceType.HEALTH_INSTITUTION,
    val staffId: UUID? = null,
    var searchTokens: TsVector? = null,
    val expenseType: InvoiceExpenseType = InvoiceExpenseType.UNDEFINED,
    val automaticGenerated: Boolean = false,
    val referenceDate: LocalDate? = LocalDate.now(),
    val bonusPercent: Int? = 0,

    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),

    ) : Table<InvoiceTable> {

    override fun copyTable(
        version: Int,
        updatedAt: LocalDateTime,
        createdAt: LocalDateTime
    ) =
        copy(
            version = version,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
}
