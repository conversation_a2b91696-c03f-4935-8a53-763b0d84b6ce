package br.com.alice.data.layer.policies.features

import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.CassiSpecialistModel
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetwork
import br.com.alice.data.layer.models.ContactModel
import br.com.alice.data.layer.models.HealthCommunitySpecialistModel
import br.com.alice.data.layer.models.HealthProfessionalModel
import br.com.alice.data.layer.models.MedicalSpecialtyModel
import br.com.alice.data.layer.models.ProductBundleModel
import br.com.alice.data.layer.models.ProductModel
import br.com.alice.data.layer.models.ProviderModel
import br.com.alice.data.layer.models.ProviderUnitModel
import br.com.alice.data.layer.models.SiteAccreditedNetwork
import br.com.alice.data.layer.models.SiteAccreditedNetworkAddress
import br.com.alice.data.layer.models.SiteAccreditedNetworkCategory
import br.com.alice.data.layer.models.SiteAccreditedNetworkProvider
import br.com.alice.data.layer.models.StaffModel
import br.com.alice.data.layer.models.StructuredAddress

val atlasNetworkProviderReferencedModelsPolicySet = policySet {
    describe("can view") {
        allows(ProviderModel::class, View)
        allows(ProviderUnitModel::class, View)
        allows(StructuredAddress::class, View)
        allows(ContactModel::class, View)
        allows(CassiSpecialistModel::class, View)
        allows(HealthCommunitySpecialistModel::class, View)
        allows(HealthProfessionalModel::class, View)
        allows(StaffModel::class, View)
    }
}

val atlasNetworkReferencedModelsPolicySet = policySet {
    describe("can view") {
        allows(ProductModel::class, View)
        allows(ProductBundleModel::class, View)
    }
}

val atlasNetworkAddressReferencedModelsPolicySet = policySet {
    describe("can view") {
        allows(StructuredAddress::class, View)
        allows(ContactModel::class, View)
    }
}

val atlasReadAccreditedNetworkPolicySet = policySet {
    describe("can view") {
        allows(SiteAccreditedNetwork::class, View)
        allows(SiteAccreditedNetworkProvider::class, View)
        allows(SiteAccreditedNetworkAddress::class, View)
    }
}

val atlasWriteAccreditedNetworkPolicySet = policySet {
    describe("can view, update, create, count and delete") {
        allows(SiteAccreditedNetwork::class, View, Update, Create, Count, Delete)
        allows(SiteAccreditedNetworkProvider::class, View, Update, Create, Count, Delete)
        allows(SiteAccreditedNetworkAddress::class, View, Update, Create, Count, Delete)
    }
}

val atlasWriteAccreditedNetworkCategoryPolicySet = policySet {
    describe("can view, update, create and count") {
        allows(SiteAccreditedNetworkCategory::class, View, Update, Create, Count)
    }
}

val atlasViewUpdateDeleteAccreditedNetworkCategoryPolicySet = policySet {
    describe("can view, delete, update and count") {
        allows(SiteAccreditedNetworkCategory::class, View, Delete, Update, Count)
    }
}

val atlasViewMedicalSpecialtyPolicySet = policySet {
    describe("can view and count") {
        allows(MedicalSpecialtyModel::class, View, Count)
    }
}

val atlasViewProviderUnitPolicySet = policySet {
    describe("can view and count") {
        allows(ProviderUnitModel::class, View, Count)
    }
}

val atlasViewProviderPolicySet = policySet {
    describe("can view and count") {
        allows(ProviderModel::class, View, Count)
    }
}

val atlasViewProductBundlePolicySet = policySet {
    describe("can view and count") {
        allows(ProductBundleModel::class, View, Count)
    }
}

val atlasViewConsolidatedAccreditedNetworkPolicySet = policySet {
    describe("can view and count") {
        allows(ConsolidatedAccreditedNetwork::class, View, Count)
    }
}
