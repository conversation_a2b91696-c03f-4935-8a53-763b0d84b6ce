package app.data_layer

import rego.v1

rules_matched_level_1 contains rule if {
	some rule in data.rules

	validate_conditions(rule.conditions)
}

rules_matched_level_2 contains rule if {
	some rule in rules_matched_level_1[_].branches

	validate_conditions(rule.conditions)
}

rules_matched_level_3 contains rule if {
	some rule in rules_matched_level_2[_].branches

	validate_conditions(rule.conditions)
}

rules_matched_level_4 contains rule if {
	some rule in rules_matched_level_3[_].branches

	validate_conditions(rule.conditions)
}

rules_matched_level_5 contains rule if {
	some rule in rules_matched_level_4[_].branches

	validate_conditions(rule.conditions)
}

levels := [rules_matched_level_1, rules_matched_level_2, rules_matched_level_3, rules_matched_level_4, rules_matched_level_5]

allowed contains level[_].allow[_] if {
	some level in levels
	count(level) > 0
}

allow contains case.index if {
	is_aggregate
    case1 := input.cases[0]
    action_allowed(case1)
	some case in input.cases
}

allow contains case.index if {
    not is_aggregate
    some case in input.cases
    action_allowed(case)
}

action_allowed(case) if {
	case.action in resource_by_action[case.resource.opaType] with input as case
}

action_allowed(case) if {
	some super_type in case.resource.opaSuperTypes
	case.action in resource_by_action[super_type] with input as case
}

is_aggregate if {
	case1 := input.cases[0]
    aggs := [x | some x in data.aggregate]
    case1.resource.opaType in aggs
}

resource_by_action[resource] := action if {
	resource := allowed[_].resources[_]
	action := {actions |
		allowed[i].resources[_] == resource
		actions = allowed[i].actions[_]
	}
}

### condition validation logics
validate_conditions(conditions) if {
	every condition in conditions {
		condition_validation(condition)
	}
}

condition_validation(condition) if {
	contains(condition, "||")
    splitted := split(condition, "||")
    condition_validation_or(splitted)
} else if {
	not contains(condition, "||")
	condition_validation_compare(condition)
}

condition_validation_or(conditions) if {
	some cond in conditions
    condition_validation_compare(trim_space(cond))
}

condition_validation_compare(condition) if {
	contains(condition, "@=")
    splitted := split(condition, "@=")
    reachable_roles := graph.reachable(data.app.roles_graph, [validate_conditions_value(trim_space(splitted[0]))])
    trim_space(splitted[1]) in reachable_roles
} else if {
    contains(condition, " in ")
    splitted := split(condition, " in ")
    left := validate_conditions_value(trim_space(splitted[0]))
    right := validate_conditions_value(trim_space(splitted[1]))
    some v in right
    v == left
} else if {
	contains(condition, "==")
    splitted := split(condition, "==")
    validate_conditions_value(trim_space(splitted[0])) == validate_conditions_value(trim_space(splitted[1]))
} else if {
	contains(condition, "!=")
    splitted := split(condition, "!=")
    validate_conditions_value(trim_space(splitted[0])) != validate_conditions_value(trim_space(splitted[1]))
}

validate_conditions_value(conditions) := value if {
	contains(conditions, "${")
    value := object.get(input, split(regex.replace(conditions, `[\${]|[}]`, ""), "."), "null")
} else := conditions

#screen_permissions contains screen_permission.name if {
#	some screen_permission in data.screen_permissions
#	every requirement in screen_permission.requires {
#		every action in requirement.actions {
#			action in resource_by_action[requirement.resource]
#		}
#	}
#}
