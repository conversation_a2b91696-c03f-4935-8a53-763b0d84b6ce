package br.com.alice.api.ops.converters

import br.com.alice.api.ops.models.AddressRequest
import br.com.alice.api.ops.models.ClinicalStaffResponse
import br.com.alice.api.ops.models.ClinicalStaffWithAddresses
import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.StructuredAddress

object ClinicalStaffResponseConverter :
    Converter<ClinicalStaffWithAddresses, ClinicalStaffResponse>(
        ClinicalStaffWithAddresses::class, ClinicalStaffResponse::class
    ) {
    fun convert(source: ClinicalStaffWithAddresses): ClinicalStaffResponse {
        val healthProfessional = source.healthProfessional
        val council = healthProfessional.council
        val addresses = source.addresses

        return ClinicalStaffResponseConverter.convert(
            source,
            map(ClinicalStaffResponse::id) from healthProfessional.id,
            map(ClinicalStaffResponse::name) from healthProfessional.name,
            map(ClinicalStaffResponse::specialtyId) from healthProfessional.specialtyId,
            map(ClinicalStaffResponse::subSpecialtyIds) from healthProfessional.subSpecialtyIds,
            map(ClinicalStaffResponse::email) from healthProfessional.email,
            map(ClinicalStaffResponse::phones) from healthProfessional.phones,
            map(ClinicalStaffResponse::qualifications) from healthProfessional.qualifications,
            map(ClinicalStaffResponse::imageUrl) from healthProfessional.imageUrl,
            map(ClinicalStaffResponse::education) from healthProfessional.education,
            map(ClinicalStaffResponse::tier) from healthProfessional.tier,
            map(ClinicalStaffResponse::providerUnitIds) from healthProfessional.providerUnitIds,
            map(ClinicalStaffResponse::scheduleAvailabilityDays) from healthProfessional.scheduleAvailabilityDays,
            map(ClinicalStaffResponse::appointmentTypes) from healthProfessional.appointmentTypes,
            map(ClinicalStaffResponse::curiosity) from healthProfessional.curiosity,
            map(ClinicalStaffResponse::showOnApp) from healthProfessional.showOnApp,
            map(ClinicalStaffResponse::status) from healthProfessional.status,
            map(ClinicalStaffResponse::urlSlug) from healthProfessional.urlSlug,
            map(ClinicalStaffResponse::councilNumber) from council.number,
            map(ClinicalStaffResponse::councilState) from council.state.name,
            map(ClinicalStaffResponse::addressList) from buildAddressList(addresses),
            map(ClinicalStaffResponse::gender) from healthProfessional.gender,
            map(ClinicalStaffResponse::staffId) from healthProfessional.staffId
        )
    }

    private fun buildAddressList(addresses: List<StructuredAddress>?): List<AddressRequest> {
        return addresses?.map {
            AddressRequest(
                id = it.id,
                street = it.street,
                number = it.number,
                complement = it.complement,
                neighborhood = it.neighborhood,
                state = it.state,
                city = it.city,
                zipcode = it.zipcode,
                label = it.label,
                active = it.active,
                latitude = it.latitude,
                longitude = it.longitude
            )
        } ?: emptyList()
    }
}
