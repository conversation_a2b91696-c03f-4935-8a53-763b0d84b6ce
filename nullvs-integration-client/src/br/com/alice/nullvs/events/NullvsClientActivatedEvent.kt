package br.com.alice.nullvs.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.nullvs.SERVICE_NAME
import java.util.UUID

class NullvsClientActivatedEvent(billingAccountablePartyId: UUID, externalId: String) :
    NotificationEvent<NullvsClientActivatedEvent.Payload>(
        producer = SERVICE_NAME,
        name = name,
        payload = Payload(billingAccountablePartyId, externalId),
    ) {

    companion object {
        const val name = "nullvs-client-activated"
    }

    data class Payload(
        val billingAccountablePartyId: UUID,
        val externalId: String,
    )
}
