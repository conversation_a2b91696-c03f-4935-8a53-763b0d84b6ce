package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.api.ops.converters.ProviderResponseConverter
import br.com.alice.api.ops.models.ProductBundleIdRequest
import br.com.alice.api.ops.models.ProductBundleRequest
import br.com.alice.api.ops.models.ProductBundleResponse
import br.com.alice.api.ops.models.SpecialistResponse
import br.com.alice.common.ErrorResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.data.layer.models.SpecialtyTiers
import br.com.alice.product.client.ProductBundleService
import br.com.alice.provider.client.ProviderFilter
import br.com.alice.provider.client.ProviderService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class ProductBundleControllerTest : ControllerTestHelper() {

    private val productBundleService: ProductBundleService = mockk()
    private val providerService: ProviderService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val productBundleController =
        ProductBundleController(productBundleService, providerService, healthProfessionalService)

    private val firstProviderId = RangeUUID.generate()
    private val dateTime = LocalDateTime.now()

    private val bundleId = RangeUUID.generate()
    private val specialistBundle = TestModelFactory.buildProductBundle().copy(
        id = bundleId,
        type = ProductBundleType.SPECIALIST,
        specialistIds = listOf(RangeUUID.generate(), RangeUUID.generate())
    )
    private val providerBundle = TestModelFactory.buildProductBundle().copy(
        id = bundleId,
        type = ProductBundleType.HOSPITAL,
        providerIds = listOf(RangeUUID.generate(), RangeUUID.generate())
    )
    private val cassiSpecialistBundle = TestModelFactory.buildProductBundle().copy(
        id = bundleId,
        type = ProductBundleType.CASSI_SPECIALIST,
        externalSpecialists = listOf(RangeUUID.generate(), RangeUUID.generate())
    ).copy(createdAt = dateTime, updatedAt = dateTime)

    private val newSpecialistBundle = TestModelFactory.buildProductBundle().copy(
        id = bundleId,
        type = ProductBundleType.SPECIALITY_TIERS,
        specialtyTiers = listOf(
            SpecialtyTiers(
                listOf(SpecialistTier.TALENTED),
                RangeUUID.generate()
            )
        )
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { productBundleController }
    }

    @Test
    fun `#get should return 200 OK with new specialist bundle`() {
        val specialistBundleResponse = ProductBundleResponse(
            id = newSpecialistBundle.id,
            specialtyTiers = newSpecialistBundle.specialtyTiers,
            type = newSpecialistBundle.type,
            name = specialistBundle.name,
            imageUrl = specialistBundle.imageUrl,
            active = specialistBundle.active,
            priceScale = specialistBundle.priceScale,
        )
        coEvery {
            productBundleService.get(newSpecialistBundle.id)
        } returns newSpecialistBundle.success()

        authenticatedAs(idToken, staff) {
            get("/productBundle/${bundleId}") { response ->
                assertThat(response).isOKWithData(specialistBundleResponse)
            }
        }
    }

    @Test
    fun `#get should return 200 OK with specialist bundle`() {
        val specialistBundleResponse = ProductBundleResponse(
            id = bundleId,
            specialistIds = specialistBundle.specialistIds.map { ProductBundleIdRequest(it.toString()) },
            type = specialistBundle.type,
            name = specialistBundle.name,
            imageUrl = specialistBundle.imageUrl,
            active = specialistBundle.active,
            priceScale = specialistBundle.priceScale,
        )
        coEvery {
            productBundleService.get(bundleId)
        } returns specialistBundle.success()

        authenticatedAs(idToken, staff) {
            get("/productBundle/${bundleId}") { response ->
                assertThat(response).isOKWithData(specialistBundleResponse)
            }
        }
    }

    @Test
    fun `#get should return 200 OK with providers`() {
        val providerBundleResponse = ProductBundleResponse(
            id = bundleId,
            providerIds = providerBundle.providerIds.map { ProductBundleIdRequest(it.toString()) },
            type = providerBundle.type,
            name = providerBundle.name,
            imageUrl = providerBundle.imageUrl,
            active = providerBundle.active,
            priceScale = providerBundle.priceScale,
        )
        coEvery {
            productBundleService.get(bundleId)
        } returns providerBundle.success()

        authenticatedAs(idToken, staff) {
            get("/productBundle/${bundleId}") { response ->
                assertThat(response).isOKWithData(providerBundleResponse)
            }
        }
    }

    @Test
    fun `#create should return 200 OK with specialist bundle`() = runBlocking {
        val request = ProductBundleRequest(
            name = "Bundle Especialista Teste",
            providerIds = null,
            specialistIds = specialistBundle.specialistIds.map { ProductBundleIdRequest(it.toString()) },
            type = ProductBundleType.SPECIALIST,
            imageUrl = "image_url",
            priceScale = 1,
            active = true
        )
        val specialistBundleResponse = ProductBundleResponse(
            id = bundleId,
            specialistIds = specialistBundle.specialistIds.map { ProductBundleIdRequest(it.toString()) },
            type = specialistBundle.type,
            name = specialistBundle.name,
            imageUrl = specialistBundle.imageUrl,
            active = specialistBundle.active,
            priceScale = specialistBundle.priceScale,
        )
        coEvery {
            productBundleService.add(match { it.name == request.name })
        } returns specialistBundle.success()

        withFeatureFlag(FeatureNamespace.AOS, "can_edit_product", listOf(staff.email)) {
            authenticatedAs(idToken, staff) {
                post(to = "/productBundle", body = request) { response ->
                    assertThat(response).isOKWithData(specialistBundleResponse)
                }
            }
        }
    }

    @Test
    fun `#create should return 200 OK with new specialist bundle`() = runBlocking {
        val request = ProductBundleRequest(
            name = "Bundle Novo Especialista Teste",
            providerIds = null,
            specialtyTiers = newSpecialistBundle.specialtyTiers,
            type = ProductBundleType.SPECIALITY_TIERS,
            imageUrl = "image_url",
            priceScale = 1,
            active = true
        )
        val specialistBundleResponse = ProductBundleResponse(
            id = bundleId,
            specialtyTiers = request.specialtyTiers,
            type = newSpecialistBundle.type,
            name = newSpecialistBundle.name,
            imageUrl = newSpecialistBundle.imageUrl,
            active = newSpecialistBundle.active,
            priceScale = newSpecialistBundle.priceScale,
        )
        coEvery {
            productBundleService.add(match { it.name == request.name })
        } returns newSpecialistBundle.success()

        withFeatureFlag(FeatureNamespace.AOS, "can_edit_product", listOf(staff.email)) {
            authenticatedAs(idToken, staff) {
                post(to = "/productBundle", body = request) { response ->
                    assertThat(response).isOKWithData(specialistBundleResponse)
                }
            }
        }
    }

    @Test
    fun `#create should return 200 OK with external specialist bundle`() = runBlocking {
        val request = ProductBundleRequest(
            name = cassiSpecialistBundle.name,
            providerIds = null,
            externalSpecialistIds = cassiSpecialistBundle.externalSpecialists.map { ProductBundleIdRequest(it.toString()) },
            type = ProductBundleType.CASSI_SPECIALIST,
            imageUrl = cassiSpecialistBundle.imageUrl,
            priceScale = cassiSpecialistBundle.priceScale,
            active = cassiSpecialistBundle.active
        )
        val cassiSpecialistBundleResponse = ProductBundleResponse(
            id = bundleId,
            externalSpecialistIds = cassiSpecialistBundle.externalSpecialists.map { ProductBundleIdRequest(it.toString()) },
            type = cassiSpecialistBundle.type,
            name = cassiSpecialistBundle.name,
            imageUrl = cassiSpecialistBundle.imageUrl,
            active = cassiSpecialistBundle.active,
            priceScale = cassiSpecialistBundle.priceScale,
        )

        mockRangeUUID(bundleId) {
            mockLocalDateTime(dateTime) {
                coEvery {
                    productBundleService.add(cassiSpecialistBundle)
                } returns cassiSpecialistBundle.success()

                withFeatureFlag(FeatureNamespace.AOS, "can_edit_product", listOf(staff.email)) {
                    authenticatedAs(idToken, staff) {
                        post(to = "/productBundle", body = request) { response ->
                            assertThat(response).isOKWithData(cassiSpecialistBundleResponse)
                        }
                    }
                }
            }
        }

        coVerifyOnce { productBundleService.add(any()) }
    }

    @Test
    fun `#create should return 200 OK with ordered providers`() = runBlocking {
        val provider1 = TestModelFactory.buildProvider(name = "Provider 1")
        val provider2 = TestModelFactory.buildProvider(name = "Provider 2")
        val providersRequest = listOf(
            ProductBundleIdRequest(provider1.id.toString()),
            ProductBundleIdRequest(provider2.id.toString()),
        )
        val providerBundle = TestModelFactory.buildProductBundle().copy(
            id = bundleId,
            type = ProductBundleType.HOSPITAL,
            providerIds = listOf(provider1.id, provider2.id)
        )

        val request = ProductBundleRequest(
            name = "Bundle Teste",
            providerIds = providersRequest,
            specialistIds = null,
            type = ProductBundleType.LABORATORY,
            imageUrl = "",
            priceScale = 1,
            active = true
        )

        coEvery {
            productBundleService.add(match {
                it.name == request.name && it.providerIds == listOf(
                    provider1,
                    provider2
                ).map { p -> p.id }
            })
        } returns providerBundle.success()

        withFeatureFlag(FeatureNamespace.AOS, "can_edit_product", listOf(staff.email)) {
            authenticatedAs(idToken, staff) {
                post(to = "/productBundle", body = request) { response ->
                    assertThat(response).isSuccessfulJson()
                    val content: ProductBundleResponse = response.bodyAsJson()
                    assertThat(content.providerIds).isEqualTo(providersRequest)
                }
            }
        }
    }

    @Test
    fun `#create should return Bad Request if specialistIds and providerIds are passed`() = runBlocking {
        val request = ProductBundleRequest(
            name = "Bundle Especialista Teste",
            providerIds = listOf(ProductBundleIdRequest(firstProviderId.toString())),
            specialistIds = specialistBundle.specialistIds.map { ProductBundleIdRequest(it.toString()) },
            type = ProductBundleType.SPECIALIST,
            imageUrl = "image_url",
            priceScale = 1,
            active = true
        )

        withFeatureFlag(FeatureNamespace.AOS, "can_edit_product", listOf(staff.email)) {
            authenticatedAs(idToken, staff) {
                post(to = "/productBundle", body = request) { response ->
                    assertThat(response).isBadRequest()
                    val content: ErrorResponse = response.bodyAsJson()
                    assertThat(content.code).isEqualTo("product_bundle_with_provider_and_specialist_ids")
                    assertThat(content.message).isEqualTo(
                        "Informe providerIds ou specialistIds. Não é permitido preencher ambos os campos."
                    )
                }
            }
        }
    }

    @Test
    fun `#create should return Bad Request if specialistIds and providerIds are not passed`() = runBlocking {
        val request = ProductBundleRequest(
            name = "Bundle Especialista Teste",
            providerIds = null,
            specialistIds = null,
            type = ProductBundleType.SPECIALIST,
            imageUrl = "image_url",
            priceScale = 1,
            active = true
        )

        withFeatureFlag(FeatureNamespace.AOS, "can_edit_product", listOf(staff.email)) {
            authenticatedAs(idToken, staff) {
                post(to = "/productBundle", body = request) { response ->
                    assertThat(response).isBadRequest()
                    val content: ErrorResponse = response.bodyAsJson()
                    assertThat(content.code).isEqualTo("product_bundle_without_provider_and_specialist_ids")
                    assertThat(content.message).isEqualTo(
                        "Um dos campos providerIds ou specialistIds deve ser informado."
                    )
                }
            }
        }

        coVerifyNone { productBundleService.get(any()) }
        coVerifyNone { productBundleService.update(any(), any()) }
    }

    @Test
    fun `#update should return 200 OK with specialist bundle`() = runBlocking {
        val request = ProductBundleRequest(
            name = "Bundle Especialista Teste",
            providerIds = null,
            specialistIds = specialistBundle.specialistIds.map { ProductBundleIdRequest(it.toString()) },
            type = ProductBundleType.SPECIALIST,
            imageUrl = "image_url",
            priceScale = 1,
            active = true
        )
        val specialistBundleResponse = ProductBundleResponse(
            id = bundleId,
            specialistIds = specialistBundle.specialistIds.map { ProductBundleIdRequest(it.toString()) },
            type = specialistBundle.type,
            name = specialistBundle.name,
            imageUrl = specialistBundle.imageUrl,
            active = specialistBundle.active,
            priceScale = specialistBundle.priceScale,
        )
        coEvery {
            productBundleService.get(bundleId)
        } returns specialistBundle.success()
        coEvery {
            productBundleService.update(
                model = match { it.name == request.name },
                oldModel = specialistBundle
            )
        } returns specialistBundle.success()

        withFeatureFlag(FeatureNamespace.AOS, "can_edit_product", listOf(staff.email)) {
            authenticatedAs(idToken, staff) {
                put(to = "/productBundle/${bundleId}", body = request) { response ->
                    assertThat(response).isOKWithData(specialistBundleResponse)
                }
            }
        }

        coVerifyOnce { productBundleService.get(any()) }
        coVerifyOnce { productBundleService.update(any(), any()) }
    }

    @Test
    fun `#update should return 200 OK with external specialist bundle`() = runBlocking {
        val request = ProductBundleRequest(
            name = cassiSpecialistBundle.name,
            providerIds = null,
            externalSpecialistIds = cassiSpecialistBundle.externalSpecialists.map { ProductBundleIdRequest(it.toString()) },
            type = ProductBundleType.CASSI_SPECIALIST,
            imageUrl = cassiSpecialistBundle.imageUrl,
            priceScale = cassiSpecialistBundle.priceScale,
            active = cassiSpecialistBundle.active
        )
        val specialistBundleResponse = ProductBundleResponse(
            id = bundleId,
            externalSpecialistIds = cassiSpecialistBundle.externalSpecialists.map { ProductBundleIdRequest(it.toString()) },
            type = cassiSpecialistBundle.type,
            name = cassiSpecialistBundle.name,
            imageUrl = cassiSpecialistBundle.imageUrl,
            active = cassiSpecialistBundle.active,
            priceScale = cassiSpecialistBundle.priceScale,
        )

        mockRangeUUID(bundleId) {
            mockLocalDateTime(dateTime) {
                coEvery {
                    productBundleService.get(bundleId)
                } returns cassiSpecialistBundle.success()
                coEvery {
                    productBundleService.update(
                        model = cassiSpecialistBundle,
                        oldModel = cassiSpecialistBundle
                    )
                } returns cassiSpecialistBundle.success()

                withFeatureFlag(FeatureNamespace.AOS, "can_edit_product", listOf(staff.email)) {
                    authenticatedAs(idToken, staff) {
                        put(to = "/productBundle/${bundleId}", body = request) { response ->
                            assertThat(response).isOKWithData(specialistBundleResponse)
                        }
                    }
                }
            }
        }

        coVerifyOnce { productBundleService.get(any()) }
        coVerifyOnce { productBundleService.update(any(), any()) }
    }

    @Test
    fun `#update should return 200 OK with new specialist bundle`() = runBlocking {
        val request = ProductBundleRequest(
            name = "Bundle Novo Especialista Teste",
            providerIds = null,
            specialtyTiers = newSpecialistBundle.specialtyTiers,
            type = ProductBundleType.SPECIALITY_TIERS,
            imageUrl = "image_url",
            priceScale = 1,
            active = true
        )
        val specialistBundleResponse = ProductBundleResponse(
            id = bundleId,
            specialtyTiers = newSpecialistBundle.specialtyTiers,
            type = newSpecialistBundle.type,
            name = newSpecialistBundle.name,
            imageUrl = newSpecialistBundle.imageUrl,
            active = newSpecialistBundle.active,
            priceScale = newSpecialistBundle.priceScale,
        )
        coEvery {
            productBundleService.get(bundleId)
        } returns newSpecialistBundle.success()
        coEvery {
            productBundleService.update(
                model = match { it.name == request.name },
                oldModel = newSpecialistBundle
            )
        } returns newSpecialistBundle.success()

        withFeatureFlag(FeatureNamespace.AOS, "can_edit_product", listOf(staff.email)) {
            authenticatedAs(idToken, staff) {
                put(to = "/productBundle/${bundleId}", body = request) { response ->
                    assertThat(response).isOKWithData(specialistBundleResponse)
                }
            }
        }

        coVerifyOnce { productBundleService.get(any()) }
        coVerifyOnce { productBundleService.update(any(), any()) }
    }

    @Test
    fun `#update should return Bad Request if specialistIds and providerIds are passed`() = runBlocking {
        val request = ProductBundleRequest(
            name = "Bundle Especialista Teste",
            providerIds = listOf(ProductBundleIdRequest(firstProviderId.toString())),
            specialistIds = specialistBundle.specialistIds.map { ProductBundleIdRequest(it.toString()) },
            type = ProductBundleType.SPECIALIST,
            imageUrl = "image_url",
            priceScale = 1,
            active = true
        )

        withFeatureFlag(FeatureNamespace.AOS, "can_edit_product", listOf(staff.email)) {
            authenticatedAs(idToken, staff) {
                put(to = "/productBundle/${bundleId}", body = request) { response ->
                    assertThat(response).isBadRequest()
                    val content: ErrorResponse = response.bodyAsJson()
                    assertThat(content.code).isEqualTo("product_bundle_with_provider_and_specialist_ids")
                    assertThat(content.message).isEqualTo(
                        "Informe providerIds ou specialistIds. Não é permitido preencher ambos os campos."
                    )
                }
            }
        }

        coVerifyNone { productBundleService.get(any()) }
        coVerifyNone { productBundleService.update(any(), any()) }
    }

    @Test
    fun `#update should return Bad Request if specialistIds and providerIds are not passed`() = runBlocking {
        val request = ProductBundleRequest(
            name = "Bundle Especialista Teste",
            providerIds = null,
            specialistIds = null,
            type = ProductBundleType.SPECIALIST,
            imageUrl = "image_url",
            priceScale = 1,
            active = true
        )

        withFeatureFlag(FeatureNamespace.AOS, "can_edit_product", listOf(staff.email)) {
            authenticatedAs(idToken, staff) {
                put(to = "/productBundle/${bundleId}", body = request) { response ->
                    assertThat(response).isBadRequest()
                    val content: ErrorResponse = response.bodyAsJson()
                    assertThat(content.code).isEqualTo("product_bundle_without_provider_and_specialist_ids")
                    assertThat(content.message).isEqualTo(
                        "Um dos campos providerIds ou specialistIds deve ser informado."
                    )
                }
            }
        }

        coVerifyNone { productBundleService.get(any()) }
        coVerifyNone { productBundleService.update(any(), any()) }
    }

    @Test
    fun `#getSpecialistsOfBundle should return specialists of bundle`() {
        val range = IntRange(0, 9)
        val namePrefix = "Ze"
        val specialist = TestModelFactory.buildHealthProfessional()
        val productBundle = TestModelFactory.buildProductBundle().copy(specialistIds = listOf(specialist.id))
        coEvery {
            productBundleService.get(productBundle.id)
        } returns productBundle.success()
        coEvery {
            healthProfessionalService.searchByNameAndIdWithRange(range, namePrefix, listOf(specialist.id))
        } returns listOf(specialist).success()
        coEvery {
            healthProfessionalService.countByNameAndId(namePrefix, listOf(specialist.id))
        } returns 1.success()
        val expectedResponse = SpecialistResponse(specialist.id, specialist.name, specialist.email)

        authenticatedAs(idToken, staff) {
            get(
                "/productBundle/specialists?filter={\"q\":\"${namePrefix}\"," +
                        " \"productBundleId\":\"${productBundle.id}\"}&range=[0,9]"
            ) { response ->
                assertThat(response).isOKWithData(listOf(expectedResponse))
            }
        }
    }
    @Test
    fun `#getProviders should return providers of bundle`() {
        val range = IntRange(0, 9)
        val namePrefix = "Ze"
        val provider = TestModelFactory.buildProvider()
        val productBundle = TestModelFactory.buildProductBundle().copy(providerIds = listOf(provider.id))
        val filter = ProviderFilter(
            searchToken = namePrefix,
            ids = listOf(provider.id),
            status = listOf(Status.ACTIVE, Status.INACTIVE)
        )
        coEvery {
            productBundleService.get(productBundle.id)
        } returns productBundle.success()
        coEvery {
            providerService.getByFiltersWithRange(filter,range)
        } returns listOf(provider).success()
        coEvery {
            providerService.countByFilters(filter)
        } returns 1.success()
        val expectedResponse = ProviderResponseConverter.convert(provider)

        authenticatedAs(idToken, staff) {
            get(
                "/productBundle/providers?filter={\"q\":\"${namePrefix}\"," +
                        " \"productBundleId\":\"${productBundle.id}\"}&range=[0,9]"
            ) { response ->
                assertThat(response).isOKWithData(listOf(expectedResponse))
                assertThat(response).containsHeaderWithValue("Content-Range", "1")
            }
        }
    }
}
