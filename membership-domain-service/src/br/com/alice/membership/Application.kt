package br.com.alice.membership

import br.com.alice.appointment.ioc.AppointmentDomainClientModule
import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.bottini.ioc.BottiniClientModule
import br.com.alice.business.ioc.BusinessDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.ioc.NotificationModule
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.notification.installNotificationSubscriptionAutoConfirm
import br.com.alice.communication.ioc.CommunicationModule
import br.com.alice.data.layer.MEMBERSHIP_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.duquesa.ioc.DuquesaDomainClientModule
import br.com.alice.ehr.ioc.EhrDomainClientModule
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.filevault.ioc.FileVaultClientModule
import br.com.alice.healthcondition.ioc.HealthConditionDomainClientModule
import br.com.alice.healthplan.ioc.HealthPlanDomainClientModule
import br.com.alice.marauders.map.ioc.MaraudersMapDomainClientModule
import br.com.alice.membership.ioc.DataLayerServiceModule
import br.com.alice.membership.ioc.ServiceModule
import br.com.alice.membership.metrics.Metrics.registerMetrics
import br.com.alice.membership.route.backFillRoutes
import br.com.alice.membership.route.kafkaRoutes
import br.com.alice.membership.route.recurringRoutes
import br.com.alice.membership.route.webhookRoutes
import br.com.alice.moneyin.ioc.MoneyInClientModule
import br.com.alice.onboarding.ioc.OnboardingClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.product.ioc.ProductDomainClientModule
import br.com.alice.provider.ioc.ProviderDomainClientModule
import br.com.alice.schedule.ioc.AppointmentScheduleDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import io.ktor.server.application.Application
import io.ktor.server.auth.Authentication
import io.ktor.server.routing.routing
import org.koin.core.module.Module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    val dependencyInjectionModules = listOf(
        FeatureConfigDomainClientModule,
        DataLayerServiceModule,
        NotificationModule,
        CommunicationModule,
        MoneyInClientModule,
        EhrDomainClientModule,
        AppointmentScheduleDomainClientModule,
        HealthConditionDomainClientModule,
        FileVaultClientModule,
        OnboardingClientModule,
        BottiniClientModule,
        BusinessDomainClientModule,
        KafkaProducerModule,
        StaffDomainClientModule,
        ServiceModule,
        PersonDomainClientModule,
        FileVaultClientModule,
        MaraudersMapDomainClientModule,
        ProviderDomainClientModule,
        HealthPlanDomainClientModule,
        AppointmentDomainClientModule,
        ProductDomainClientModule,
        DuquesaDomainClientModule,
    )
}

@JvmOverloads
fun Application.module(
    dependencyInjectionModules: List<Module> =
        ApplicationModule.dependencyInjectionModules
) {
    setupDomainService(dependencyInjectionModules) {
        install(Authentication) {
            <EMAIL>()
        }

        routing {
            application.attributes.put(PolicyRootServiceKey, MEMBERSHIP_ROOT_SERVICE_NAME)

            backFillRoutes()

            webhookRoutes()
            recurringRoutes()
        }

        kafkaConsumer(startRoutesSync = true) {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }

        installNotificationSubscriptionAutoConfirm()
        featureConfigBootstrap(
            FeatureNamespace.ALICE_APP,
            FeatureNamespace.DUQUESA,
            FeatureNamespace.MEMBERSHIP,
            FeatureNamespace.ONBOARDING,
            FeatureNamespace.SCHEDULE
        )
        registerMetrics()
    }
}
