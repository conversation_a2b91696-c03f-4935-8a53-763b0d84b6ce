package br.com.alice.hr.core.clients.interfaces

import br.com.alice.hr.core.clients.PopulationDataResponse
import br.com.alice.hr.core.clients.FrequencyCoordinationDataResponse
import java.util.UUID

interface DataAPIClient {
    suspend fun getPopulationDataByCompany(companyId: UUID): PopulationDataResponse
    suspend fun getFrequencyCoordinationByCompany(companyId: UUID): FrequencyCoordinationDataResponse
}
