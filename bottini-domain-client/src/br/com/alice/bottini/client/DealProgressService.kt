package br.com.alice.bottini.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.InsurancePortabilityRequest
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.PersonLogin
import br.com.alice.data.layer.models.ProductOrder
import br.com.alice.person.model.events.PersonPayload
import br.com.alice.schedule.model.events.AppointmentSchedulePayload
import com.github.kittinunf.result.Result
import java.util.UUID

interface DealProgressService  : Service {

    override val namespace get() = "bottini"
    override val serviceName get() = "deal_progress"


    suspend fun registerFirstAccessOnDeal(login: PersonLogin): Result<Any, Throwable>

    suspend fun moveDealStageOnShoppingFinished(
        productOrder: ProductOrder,
        promoCodeId: UUID?
    ): Result<Any, Throwable>

    suspend fun moveDealOnPortabilitySubmitted(request: InsurancePortabilityRequest): Result<Any, Throwable>

    suspend fun moveDealOnPortabilityDecline(request: InsurancePortabilityRequest): Result<Any, Throwable>

    suspend fun moveDealOnPortabilityInProgress(request: InsurancePortabilityRequest): Result<Any, Throwable>

    suspend fun moveDealStageOnRegistrationFinished(personId: PersonId): Result<Any, Throwable>

    suspend fun addCancellationDataToDeal(member: Member): Result<Any, Throwable>

    suspend fun moveDealOnAppointmentCreated(
        appointmentSchedule: AppointmentSchedule,
        personId: PersonId
    ): Result<Any, Throwable>

    suspend fun moveDealStageOnContractStarted(personId: PersonId): Result<Any, Throwable>

    suspend fun moveDealOnInvoiceSent(personId: PersonId): Result<Any, Throwable>

    suspend fun moveDealOnMemberActivated(member: Member): Result<Any, Throwable>

    suspend fun moveDealToNoShow(schedule: AppointmentSchedulePayload): Result<Any, Throwable>

}
