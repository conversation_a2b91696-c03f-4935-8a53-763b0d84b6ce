package br.com.alice.ehr.services

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.Cpt
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.GracePeriod
import br.com.alice.data.layer.models.GracePeriodType
import br.com.alice.data.layer.models.HealthCondition
import br.com.alice.data.layer.models.HealthConditionCodeType.CID_10
import br.com.alice.data.layer.models.InsurancePortabilityRequestDeclineReason.OTHER
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.PersonGracePeriod
import br.com.alice.data.layer.models.PersonGracePeriodStatus
import br.com.alice.data.layer.models.PersonGracePeriodType
import br.com.alice.data.layer.models.ProductType
import br.com.alice.ehr.client.GracePeriods
import br.com.alice.ehr.model.CptCondition
import br.com.alice.ehr.model.CptGracePeriod
import br.com.alice.ehr.model.HealthInstitution
import br.com.alice.ehr.model.MemberCpt
import br.com.alice.ehr.model.removeOthers
import br.com.alice.ehr.services.internal.product_enrichment.ProductWithProvidersService
import br.com.alice.healthcondition.client.HealthConditionService
import br.com.alice.membership.client.onboarding.GracePeriodService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.product.model.ProductWithProviders
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test
import br.com.alice.ehr.model.GracePeriodType as EhrGracePeriodType

class MemberCptsServiceImplTest {

    private val memberService: MemberService = mockk()
    private val personService: PersonService = mockk()
    private val gracePeriodService: GracePeriodService = mockk()
    private val healthConditionService: HealthConditionService = mockk()
    private val productService: ProductWithProvidersService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()

    private val memberCptsService = MemberCptsServiceImpl(
        memberService,
        gracePeriodService,
        personService,
        healthConditionService,
        productService,
        beneficiaryService
    )

    private val personId = PersonId()
    private val person = TestModelFactory.buildPerson(
        personId = personId,
        productInfo = TestModelFactory.buildProductInfo()
    )
    private val healthDeclaration = TestModelFactory.buildHealthDeclaration(personId = personId)
    private val finalCpt1 = healthDeclaration.finalCpts[0]
    private val finalCpt2 = healthDeclaration.finalCpts[1]
    private val personGracePeriod = TestModelFactory.buildPersonGracePeriod(personId = personId)
    private val portability = TestModelFactory.buildInsurancePortabilityRequest(personId = personId)
    private val gracePeriod = GracePeriod(
        personId = personId,
        cpts = healthDeclaration.finalCpts,
        portabilityRequest = portability,
        personGracePeriods = listOf(
            personGracePeriod.copy(value = finalCpt1.cids.first()),
            personGracePeriod.copy(value = finalCpt2.cids.first()),
        ),
        isFullRiskB2B = false,
    )
    private val healthCondition1 = TestModelFactory.buildHealthCondition(
        code = finalCpt1.cids.first(),
        specialities = listOf("Oftalmologia")
    )
    private val healthCondition2 = TestModelFactory.buildHealthCondition(
        code = finalCpt2.cids.first(),
        specialities = null
    )
    private val healthConditions = listOf(healthCondition1, healthCondition2)
    private val cidCodes = healthDeclaration.finalCpts.mapNotNull { it.cids.firstOrNull() }

    private val conditionGracePeriodDays = 730L
    private val surgeryGracePeriodDays = 180L
    private val birthGracePeriodDays = 300L
    private val therapyGracePeriodDays = 60L
    private val pacGracePeriodDays = 180L
    private val emergencyGracePeriodDays = 1L
    private val specialExamsPeriodDays = 60L
    private val hospitalizationDueToPersonalAccidentInDays = 0L
    private val hospitalizationInDays = 0L
    private val birthExpiredGraceDate = LocalDateTime.now().minusDays(birthGracePeriodDays + 10)
    private val conditionPeriodExpiredDate = LocalDateTime.now().minusDays(conditionGracePeriodDays + 1)

    private val product = TestModelFactory.buildProduct(ansNumber = "ansNumber")
    private val product2 = TestModelFactory.buildProduct()
    private val productB2B = TestModelFactory.buildProduct(type = ProductType.B2B)

    private val member = TestModelFactory.buildMember(
        personId = personId,
        status = MemberStatus.ACTIVE,
        activationDate = LocalDateTime.now(),
        product = product
    )
    private val memberActivationDate = member.activationDate!!
    private val memberB2B = TestModelFactory.buildMember(
        personId = personId,
        productType = ProductType.B2B,
        status = MemberStatus.ACTIVE,
        activationDate = LocalDateTime.now(),
        product = productB2B
    )

    private val commonHealthConditions = listOf(
        buildCptCondition(healthCondition1, finalCpt1, memberActivationDate),
        buildCptCondition(healthCondition2, finalCpt2, memberActivationDate)
    )

    private val surgeryGracePeriod = buildCptGracePeriod(
        type = EhrGracePeriodType.ELECTIVE_SURGERY,
        activationDate = memberActivationDate,
        gracePeriodDays = surgeryGracePeriodDays
    )

    private val birthGracePeriod = buildCptGracePeriod(
        type = EhrGracePeriodType.BIRTH,
        activationDate = memberActivationDate,
        gracePeriodDays = birthGracePeriodDays
    )

    private val therapyGracePeriod = buildCptGracePeriod(
        type = EhrGracePeriodType.THERAPY,
        activationDate = memberActivationDate,
        gracePeriodDays = therapyGracePeriodDays
    )

    private val pacGracePeriod = buildCptGracePeriod(
        type = EhrGracePeriodType.PAC,
        activationDate = memberActivationDate,
        gracePeriodDays = pacGracePeriodDays
    )

    private val emergencyGracePeriod = buildCptGracePeriod(
        type = EhrGracePeriodType.EMERGENCY,
        activationDate = memberActivationDate,
        gracePeriodDays = emergencyGracePeriodDays
    )

    private val specialExamPeriod = buildCptGracePeriod(
        type = EhrGracePeriodType.SPECIAL_EXAMS,
        activationDate = memberActivationDate,
        gracePeriodDays = specialExamsPeriodDays,
    )

    private val hospitalizationGracePeriod = buildCptGracePeriod(
        type = EhrGracePeriodType.HOSPITALIZATION,
        activationDate = memberActivationDate,
        gracePeriodDays = hospitalizationInDays
    )

    private val hospitalizationDueToPersonalAccidentGracePeriod = buildCptGracePeriod(
        type = EhrGracePeriodType.HOSPITALIZATION_DUE_TO_PERSONAL_ACCIDENT,
        activationDate = memberActivationDate,
        gracePeriodDays = hospitalizationDueToPersonalAccidentInDays,
    )

    private val othersGracePeriod = buildCptGracePeriod(
        type = EhrGracePeriodType.OTHERS,
        activationDate = memberActivationDate,
        gracePeriodDays = 0,
    )

    private val cptsGracePeriods = listOf(
        surgeryGracePeriod,
        birthGracePeriod,
        therapyGracePeriod,
        specialExamPeriod,
        pacGracePeriod,
        emergencyGracePeriod,
        hospitalizationGracePeriod,
        hospitalizationDueToPersonalAccidentGracePeriod,
        othersGracePeriod
    )
    private val baseGracePeriods = GracePeriods()

    private val expectedMale = MemberCpt(
        conditions = commonHealthConditions,
        gracePeriod = cptsGracePeriods.minus(birthGracePeriod),
        baseGracePeriods = baseGracePeriods
    )
    private val expectedMaleWithoutOthers = expectedMale.removeOthers()
    private val expectedMaleForFullRiskB2B = MemberCpt(
        conditions = commonHealthConditions,
        gracePeriod = cptsGracePeriods.minus(birthGracePeriod),
        baseGracePeriods = baseGracePeriods
    )

    private val expectedMaleForFullRiskB2BWithoutOthers = expectedMaleForFullRiskB2B.removeOthers()
    private val expectedFemale = MemberCpt(
        conditions = commonHealthConditions,
        gracePeriod = cptsGracePeriods,
        baseGracePeriods = baseGracePeriods
    )
    private val expectedFemaleWithoutOthers = expectedFemale.removeOthers()
    private val expectedFemaleForFullRiskB2B = MemberCpt(
        conditions = commonHealthConditions,
        gracePeriod = cptsGracePeriods,
        baseGracePeriods = baseGracePeriods
    )
    private val expectedFemaleForFullRiskB2BWithoutOthers = expectedFemaleForFullRiskB2B.removeOthers()

    private val expectedFemaleExpiredBirthGracePeriodMember = MemberCpt(
        conditions = listOf(
            buildCptCondition(
                healthCondition1,
                finalCpt1,
                birthExpiredGraceDate
            ),
            buildCptCondition(
                healthCondition2,
                finalCpt2,
                birthExpiredGraceDate
            )
        ),
        gracePeriod = listOf(
            surgeryGracePeriod.reCalculate(birthExpiredGraceDate, surgeryGracePeriodDays),
            birthGracePeriod.reCalculate(birthExpiredGraceDate, birthGracePeriodDays),
            therapyGracePeriod.reCalculate(birthExpiredGraceDate, therapyGracePeriodDays),
            specialExamPeriod.reCalculate(birthExpiredGraceDate, specialExamsPeriodDays),
            pacGracePeriod.reCalculate(birthExpiredGraceDate, pacGracePeriodDays),
            emergencyGracePeriod.reCalculate(birthExpiredGraceDate, emergencyGracePeriodDays),
            hospitalizationGracePeriod.reCalculate(birthExpiredGraceDate, hospitalizationInDays),
            hospitalizationDueToPersonalAccidentGracePeriod.reCalculate(
                birthExpiredGraceDate,
                hospitalizationDueToPersonalAccidentInDays
            )
        ),
        baseGracePeriods = baseGracePeriods
    )

    private val beneficiary = TestModelFactory.buildBeneficiary(personId = personId)
    private val provider1 = TestModelFactory.buildProvider(cnpj = "01")
    private val provider2 = TestModelFactory.buildProvider(cnpj = "02")

    @AfterTest
    fun confirmMocks() = confirmVerified(
        memberService,
        gracePeriodService,
        personService,
        healthConditionService,
        productService,
        beneficiaryService
    )

    @Nested
    inner class BuildPersonCpts {

        @Test
        fun `#must return empty CTPS if does not have health declaration`() = runBlocking {
            coEvery { memberService.findByPerson(personId) } returns emptyList()
            coEvery { gracePeriodService.getGracePeriod(personId) } returns NotFoundException()

            val result = memberCptsService.buildPersonCpts(person)
            assertThat(result).isSuccessWithData(MemberCpt())

            coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
            coVerifyOnce { memberService.findByPerson(any()) }
        }

        @Test
        fun `#must return CTPS even when the grace period is expired when should get date base is true`() =
            runBlocking {
                val expiredGracePeriodMember = member.copy(activationDate = conditionPeriodExpiredDate)
                val male = person.copy(sex = Sex.MALE)

                val expectedGracePeriod = listOf(
                    surgeryGracePeriod.reCalculate(conditionPeriodExpiredDate, surgeryGracePeriodDays),
                    therapyGracePeriod.reCalculate(conditionPeriodExpiredDate, therapyGracePeriodDays),
                    specialExamPeriod.reCalculate(conditionPeriodExpiredDate, specialExamsPeriodDays),
                    pacGracePeriod.reCalculate(conditionPeriodExpiredDate, pacGracePeriodDays),
                    emergencyGracePeriod.reCalculate(conditionPeriodExpiredDate, emergencyGracePeriodDays),
                    hospitalizationGracePeriod.reCalculate(conditionPeriodExpiredDate, hospitalizationInDays),
                    hospitalizationDueToPersonalAccidentGracePeriod.reCalculate(
                        conditionPeriodExpiredDate,
                        hospitalizationDueToPersonalAccidentInDays
                    )
                )

                val expectedConditions = commonHealthConditions.map {
                    it.copy(
                        validUntil = LocalDateTime.now().minusDays(1).toBrazilianDateFormat(),
                        baseDate = conditionPeriodExpiredDate.toLocalDate()
                    )
                }

                val expected = MemberCpt(
                    conditions = expectedConditions,
                    gracePeriod = expectedGracePeriod
                )

                coEvery { productService.get(product.id) } returns product
                coEvery { memberService.findByPerson(personId) } returns listOf(expiredGracePeriodMember)
                coEvery { gracePeriodService.getGracePeriod(personId, expiredGracePeriodMember) } returns gracePeriod
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(male)
                assertThat(result).isSuccessWithData(expected)

                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
            }

        @Test
        fun `#must return CTPS even when beneficiary GracePeriodType is TOTAL_GRACE_PERIOD`() =
            runBlocking {
                val male = person.copy(sex = Sex.MALE)
                val beneficiary = beneficiary.copy(gracePeriodType = GracePeriodType.TOTAL_GRACE_PERIOD)

                coEvery { productService.get(product.id) } returns product
                coEvery { memberService.findByPerson(personId) } returns listOf(member)
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(male)
                assertThat(result).isSuccessWithData(expectedMaleWithoutOthers)

                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
            }

        @Test
        fun `#must return CTPS even when beneficiary GracePeriodType is TOTAL_GRACE_PERIOD_202405`() =
            runBlocking {
                val male = person.copy(sex = Sex.MALE)
                val beneficiary = beneficiary.copy(gracePeriodType = GracePeriodType.TOTAL_GRACE_PERIOD_202405)

                coEvery { productService.get(product.id) } returns product
                coEvery { memberService.findByPerson(personId) } returns listOf(member)
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(male)
                val expectedMale = expectedMale.copy(
                    gracePeriod = listOf(
                        surgeryGracePeriod,
                        therapyGracePeriod,
                        specialExamPeriod,
                        pacGracePeriod,
                        emergencyGracePeriod,
                        hospitalizationGracePeriod.reCalculate(memberActivationDate, 90, 90),
                        hospitalizationDueToPersonalAccidentGracePeriod.reCalculate(memberActivationDate, 1, 1),
                    ),
                    baseGracePeriods = baseGracePeriods.getCalculatedPeriodsDiscount(GracePeriodType.TOTAL_GRACE_PERIOD_202405)
                )
                assertThat(result).isSuccessWithData(expectedMale)

                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
            }

        @Test
        fun `#must return CTPS even when beneficiary GracePeriodType is PARTIAL_GRACE_PERIOD`() =
            runBlocking {
                val male = person.copy(sex = Sex.MALE)
                val beneficiary = beneficiary.copy(gracePeriodType = GracePeriodType.PARTIAL_GRACE_PERIOD)

                val expectedMale = expectedMale.copy(
                    gracePeriod = listOf(
                        surgeryGracePeriod.reCalculate(memberActivationDate, 90, 90),
                        therapyGracePeriod.reCalculate(memberActivationDate, 30, 30),
                        specialExamPeriod,
                        pacGracePeriod.reCalculate(memberActivationDate, 120, 120),
                        emergencyGracePeriod,
                        hospitalizationGracePeriod.reCalculate(memberActivationDate, hospitalizationInDays),
                        hospitalizationDueToPersonalAccidentGracePeriod.reCalculate(
                            memberActivationDate,
                            hospitalizationDueToPersonalAccidentInDays
                        )
                    ),
                    baseGracePeriods = baseGracePeriods.getCalculatedPeriodsDiscount(GracePeriodType.PARTIAL_GRACE_PERIOD)
                )

                coEvery { productService.get(product.id) } returns product
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { memberService.findByPerson(personId) } returns listOf(member)
                coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(male)
                assertThat(result).isSuccessWithData(expectedMale)

                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
            }

        @Test
        fun `#must return CTPS even when beneficiary GracePeriodType is PARTIAL_GRACE_PERIOD_202405`() =
            runBlocking {
                val male = person.copy(sex = Sex.MALE)
                val beneficiary = beneficiary.copy(gracePeriodType = GracePeriodType.PARTIAL_GRACE_PERIOD_202405)

                val expectedMale = expectedMale.copy(
                    gracePeriod = listOf(
                        surgeryGracePeriod.reCalculate(memberActivationDate, 90, 90),
                        therapyGracePeriod.reCalculate(memberActivationDate, 30, 30),
                        specialExamPeriod,
                        pacGracePeriod.reCalculate(memberActivationDate, 120, 120),
                        emergencyGracePeriod,
                        hospitalizationGracePeriod.reCalculate(memberActivationDate, 60, 60),
                        hospitalizationDueToPersonalAccidentGracePeriod.reCalculate(memberActivationDate, 1, 1)
                    ),
                    baseGracePeriods = baseGracePeriods.getCalculatedPeriodsDiscount(GracePeriodType.PARTIAL_GRACE_PERIOD_202405)
                )

                coEvery { productService.get(product.id) } returns product
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { memberService.findByPerson(personId) } returns listOf(member)
                coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(male)
                assertThat(result).isSuccessWithData(expectedMale)

                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
            }

        @Test
        fun `#must return CTPS and not load beneficiary when product is B2C`() =
            runBlocking {
                val male = person.copy(
                    sex = Sex.MALE,
                    productInfo = person.productInfo!!.copy(productType = ProductType.B2C)
                )

                coEvery { productService.get(product.id) } returns product
                coEvery { memberService.findByPerson(personId) } returns listOf(member)
                coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(male)
                assertThat(result).isSuccessWithData(expectedMaleWithoutOthers)

                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
            }

        @Test
        fun `#must return only surgery CTPS even when beneficiary GracePeriodType is TOTAL_EXEMPTION and product is V2`() =
            runBlocking {
                val male = person.copy(sex = Sex.MALE)
                val beneficiary = beneficiary.copy(gracePeriodType = GracePeriodType.GRACE_PERIOD_EXEMPTION)

                val expectedMale = expectedMale.copy(
                    gracePeriod = expectedMale.gracePeriod.map { it.reCalculate(memberActivationDate, 0, 0) },
                    baseGracePeriods = baseGracePeriods.getCalculatedPeriodsDiscount(GracePeriodType.GRACE_PERIOD_EXEMPTION)
                )

                coEvery { productService.get(product.id) } returns product
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { memberService.findByPerson(personId) } returns listOf(member)
                coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                withFeatureFlag(
                    FeatureNamespace.EHR,
                    "old_portfolio_product_ans_numbers",
                    listOf(product.ansNumber!!)
                ) {
                    val result = memberCptsService.buildPersonCpts(male)
                    assertThat(result).isSuccessWithData(expectedMale)

                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                }
            }

        @Test
        fun `#must return CTPS even when beneficiary GracePeriodType is GRACE_PERIOD_EXEMPTION`() =
            runBlocking {
                val male = person.copy(sex = Sex.MALE)
                val beneficiary = beneficiary.copy(gracePeriodType = GracePeriodType.GRACE_PERIOD_EXEMPTION)

                val expectedMale = expectedMale.copy(
                    gracePeriod = expectedMale.gracePeriod.map { it.reCalculate(memberActivationDate, 0, 0) },
                    baseGracePeriods = baseGracePeriods.getCalculatedPeriodsDiscount(GracePeriodType.GRACE_PERIOD_EXEMPTION)
                )

                coEvery { productService.get(product.id) } returns product
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { memberService.findByPerson(personId) } returns listOf(member)
                coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(male)
                assertThat(result).isSuccessWithData(expectedMale)

                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
            }

        @Test
        fun `#must return CTPS even when beneficiary GracePeriodType is GRACE_PERIOD_EXEMPTION for ADESAO`() =
            runBlocking {
                val male = person.copy(
                    sex = Sex.MALE,
                    productInfo = TestModelFactory.buildProductInfo(productType = ProductType.ADESAO)
                )
                val beneficiary = beneficiary.copy(gracePeriodType = GracePeriodType.GRACE_PERIOD_EXEMPTION)

                val expectedMale = expectedMale.copy(
                    gracePeriod = expectedMale.gracePeriod.map { it.reCalculate(memberActivationDate, 0, 0) },
                    baseGracePeriods = baseGracePeriods.getCalculatedPeriodsDiscount(GracePeriodType.GRACE_PERIOD_EXEMPTION)
                )

                coEvery { productService.get(product.id) } returns product
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { memberService.findByPerson(personId) } returns listOf(member)
                coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(male)
                assertThat(result).isSuccessWithData(expectedMale)

                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
            }

        @Test
        fun `#must return CTPS even when beneficiary GracePeriodType is TOTAL_EXEMPTION`() =
            runBlocking {
                val male = person.copy(sex = Sex.MALE)
                val beneficiary = beneficiary.copy(gracePeriodType = GracePeriodType.TOTAL_EXEMPTION)

                val expectedMale = expectedMale.copy(
                    conditions = expectedMale.conditions.map { it.reCalculate(memberActivationDate, 0, 0) },
                    gracePeriod = expectedMale.gracePeriod.map { it.reCalculate(memberActivationDate, 0, 0) },
                    baseGracePeriods = baseGracePeriods.getCalculatedPeriodsDiscount(GracePeriodType.TOTAL_EXEMPTION)
                )

                coEvery { productService.get(product.id) } returns product
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { memberService.findByPerson(personId) } returns listOf(member)
                coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(male)
                assertThat(result).isSuccessWithData(expectedMale)

                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
            }

        @Test
        fun `#must return empty CTPS if health declaration has not been started yet`() = runBlocking {
            coEvery { memberService.findByPerson(personId) } returns emptyList()
            coEvery { gracePeriodService.getGracePeriod(personId) } returns NotFoundException()

            val result = memberCptsService.buildPersonCpts(person)
            assertThat(result).isSuccessWithData(MemberCpt())

            coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
            coVerifyOnce { memberService.findByPerson(any()) }
        }

        @Test
        fun `#must return exception CTPS if it fails to get the health declaration`() = runBlocking {
            coEvery { memberService.findByPerson(personId) } returns emptyList()
            coEvery { gracePeriodService.getGracePeriod(personId) } returns Exception()

            val result = memberCptsService.buildPersonCpts(person)
            assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
            coVerifyOnce { memberService.findByPerson(any()) }
        }

        @Test
        fun `#must return CTPS for person without sex set with Birth grace period`() = runBlocking {
            val fallback = person.copy(sex = null)

            coEvery { productService.get(product.id) } returns product
            coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
            coEvery { memberService.findByPerson(personId) } returns listOf(member)
            coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
            coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

            val result = memberCptsService.buildPersonCpts(fallback)
            assertThat(result).isSuccessWithData(expectedFemaleWithoutOthers)

            coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
            coVerifyOnce { memberService.findByPerson(any()) }
            coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
            coVerifyOnce { productService.get(any()) }
            coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
        }


        @Test
        fun `#must return PersonGracePeriod for duquesa members`() = runBlocking {
            val now = LocalDateTime.now()
            val memberDuquesa = member.copy(brand = Brand.DUQUESA)
            val personGracePeriods =
                healthDeclaration.finalCpts.map { cpt ->
                    PersonGracePeriod(
                        value = cpt.cids.first(),
                        startDate = now,
                        endDate = now.plusDays(730),
                        type = PersonGracePeriodType.CPT_CID_10,
                        status = PersonGracePeriodStatus.ACTIVE,
                        personId = personId,
                    )
                }

            val gracePeriod = GracePeriod(
                personId = personId,
                cpts = healthDeclaration.finalCpts.map { cpt -> Cpt(condition = "", cids = cpt.cids) },
                personGracePeriods = personGracePeriods,
                portabilityRequest = null,
                isFullRiskB2B = false,
            )

            val response = MemberCpt(
                conditions = gracePeriod.cpts.mapIndexed { index, cpt ->
                    CptCondition(
                        name = cpt.condition,
                        cid = cpt.cids.joinToString(),
                        validUntil = personGracePeriods[index].endDate.toBrazilianDateFormat(),
                        healthCondition = healthConditions.find { it.code == cpt.cids.firstOrNull() },
                        baseDate = now.toLocalDate(),
                        periodInDays = 730,
                    )
                },
                gracePeriod = emptyList()
            )

            coEvery { productService.get(product.id) } returns product
            coEvery { memberService.findByPerson(personId) } returns listOf(memberDuquesa)
            coEvery { gracePeriodService.getGracePeriod(personId, memberDuquesa) } returns gracePeriod
            coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
            coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

            val result = memberCptsService.buildPersonCpts(person)
            assertThat(result).isSuccessWithData(response)

            coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
            coVerifyOnce { memberService.findByPerson(any()) }
            coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
            coVerifyOnce { productService.get(any()) }
            coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
        }

        @Nested
        inner class MemberFemale {
            @Test
            fun `#must return CTPS for female person with Birth grace period`() = runBlocking {
                val female = person.copy(sex = Sex.FEMALE)

                coEvery { productService.get(product.id) } returns product
                coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                coEvery { memberService.findByPerson(personId) } returns listOf(member)
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(female)
                assertThat(result).isSuccessWithData(expectedFemaleWithoutOthers)

                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
            }

            @Test
            fun `#must return CTPS for female person with Birth grace period with health institutions`() =
                runBlocking {
                    val female = person.copy(sex = Sex.FEMALE)
                    val oldMembership = member.copy(
                        id = RangeUUID.generate(),
                        status = MemberStatus.CANCELED,
                        canceledAt = member.activationDate,
                        selectedProduct = TestModelFactory.buildMemberProduct(product2.id),
                    )

                    val expected = expectedFemale.copy(
                        healthInstitutions = listOf(
                            HealthInstitution(startedAt = memberActivationDate, cnpj = listOf("02"))
                        )
                    ).removeOthers()

                    coEvery { productService.get(product.id) } returns product
                    coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                    coEvery { memberService.findByPerson(personId) } returns listOf(member, oldMembership)
                    coEvery {
                        productService.findByIdsWithProviders(listOf(product.id, product2.id))
                    } returns listOf(
                        ProductWithProviders(
                            product = product,
                            providers = listOf(provider1, provider2)
                        ),
                        ProductWithProviders(
                            product = product2,
                            providers = listOf(provider1)
                        )
                    )
                    coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(female)
                    assertThat(result).isSuccessWithData(expected)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                    coVerifyOnce { productService.findByIdsWithProviders(any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                }

            @Test
            fun `#must get person first`() = runBlocking {
                val female = person.copy(sex = Sex.FEMALE)

                coEvery { personService.get(personId) } returns person
                coEvery { productService.get(product.id) } returns product
                coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                coEvery { memberService.findByPerson(personId) } returns listOf(member)
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(female)
                assertThat(result).isSuccessWithData(expectedFemaleWithoutOthers)

                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
            }

            @Test
            fun `#should return gracePeriod when person has a created portability request`() =
                runBlocking {
                    val female = person.copy(sex = Sex.FEMALE)

                    coEvery { productService.get(product.id) } returns product
                    coEvery { personService.get(personId) } returns person
                    coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                    coEvery { memberService.findByPerson(personId) } returns listOf(member)
                    coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(female)
                    assertThat(result).isSuccessWithData(expectedFemaleWithoutOthers)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                }

            @Test
            fun `#should return gracePeriod when person has a declined portability request`() =
                runBlocking {
                    val female = person.copy(sex = Sex.FEMALE)
                    val gracePeriod = gracePeriod.copy(portabilityRequest = portability.decline(listOf(OTHER)))

                    coEvery { productService.get(product.id) } returns product
                    coEvery { personService.get(personId) } returns person
                    coEvery { memberService.findByPerson(personId) } returns listOf(member)
                    coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                    coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(female)
                    assertThat(result).isSuccessWithData(expectedFemaleWithoutOthers)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                }

            @Test
            fun `#should return reset gracePeriod when person has a approved portability request`() =
                runBlocking {
                    val female = person.copy(
                        sex = Sex.FEMALE,
                        productInfo = TestModelFactory.buildProductInfo(
                            productType = ProductType.B2C
                        )
                    )
                    val expectedEmptyFemale = expectedFemale.copy(
                        conditions = commonHealthConditions.map { it.reCalculate(memberActivationDate, 0, 0) },
                        gracePeriod = cptsGracePeriods.map { it.reCalculate(memberActivationDate, 0, 0) },
                        baseGracePeriods = baseGracePeriods.getCalculatedPeriodsDiscount(GracePeriodType.TOTAL_EXEMPTION)
                    )

                    val gracePeriod = gracePeriod.copy(
                        portabilityRequest = portability.copy(status = InsurancePortabilityRequestStatus.APPROVED)
                    )

                    coEvery { productService.get(product.id) } returns product
                    coEvery { personService.get(personId) } returns person
                    coEvery { memberService.findByPerson(personId) } returns listOf(member)
                    coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(female)
                    assertThat(result).isSuccessWithData(expectedEmptyFemale)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                }

            @Test
            fun `#must return CTPS with grace for female person when product is B2B but membership is canceled`() =
                runBlocking {
                    val female = person.copy(sex = Sex.FEMALE)
                    val canceledMember = memberB2B.copy(status = MemberStatus.CANCELED)
                    val expected = expectedFemale.copy(
                        conditions = expectedFemale.conditions.map { it.copy(validUntil = "") },
                        gracePeriod = expectedFemale.gracePeriod.map { it.copy(validUntil = "") }
                    ).removeOthers()

                    coEvery { productService.get(productB2B.id) } returns productB2B
                    coEvery { memberService.findByPerson(personId) } returns listOf(canceledMember)
                    coEvery { gracePeriodService.getGracePeriod(personId, canceledMember) } returns gracePeriod
                    coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(female)
                    assertThat(result).isSuccessWithData(expected)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                }

            @Test
            fun `#must return CTPS for female with only Birth grace if is expired`() = runBlocking {
                val expiredGracePeriodMember = member.copy(activationDate = birthExpiredGraceDate)
                val female = person.copy(sex = Sex.FEMALE)

                coEvery { productService.get(product.id) } returns product
                coEvery { memberService.findByPerson(personId) } returns listOf(expiredGracePeriodMember)
                coEvery { gracePeriodService.getGracePeriod(personId, expiredGracePeriodMember) } returns gracePeriod
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(female)
                assertThat(result).isSuccessWithData(expectedFemaleExpiredBirthGracePeriodMember)

                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
            }

            @Test
            fun `#must return CTPS for female with only Birth grace if is expired without health institutions when the new membership does not have a new provider`() =
                runBlocking {
                    val expiredGracePeriodMember = member.copy(activationDate = birthExpiredGraceDate)
                    val female = person.copy(sex = Sex.FEMALE)
                    val oldMembershipActivationDate = expiredGracePeriodMember.activationDate?.minusDays(7)!!
                    val oldMembership = member.copy(
                        id = RangeUUID.generate(),
                        status = MemberStatus.CANCELED,
                        activationDate = oldMembershipActivationDate,
                        canceledAt = expiredGracePeriodMember.activationDate,
                        selectedProduct = TestModelFactory.buildMemberProduct(product2.id),
                    )

                    val expected = expectedFemale.copy(
                        conditions = expectedFemale.conditions.map { it.reCalculate(oldMembershipActivationDate) },
                        gracePeriod = expectedFemale.gracePeriod.map { it.reCalculate(oldMembershipActivationDate) }
                    ).removeOthers()

                    coEvery { productService.get(product.id) } returns product
                    coEvery { memberService.findByPerson(personId) } returns listOf(
                        expiredGracePeriodMember,
                        oldMembership
                    )
                    coEvery {
                        gracePeriodService.getGracePeriod(
                            personId,
                            expiredGracePeriodMember
                        )
                    } returns gracePeriod
                    coEvery {
                        productService.findByIdsWithProviders(listOf(product.id, product2.id))
                    } returns listOf(
                        ProductWithProviders(
                            product = product,
                            providers = listOf(provider1)
                        ),
                        ProductWithProviders(
                            product = product2,
                            providers = listOf(provider1, provider2)
                        )
                    )
                    coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(female)
                    assertThat(result).isSuccessWithData(expected)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                    coVerifyOnce { productService.findByIdsWithProviders(any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                }
        }

        @Nested
        inner class MemberMale {
            @Test
            fun `#must return empty validUntil if does not have active membership`() = runBlocking {
                val malePerson = person.copy(sex = Sex.MALE)
                val expected = expectedMale.copy(
                    conditions = expectedMale.conditions.map { it.copy(validUntil = "") },
                    gracePeriod = expectedMale.gracePeriod.map { it.copy(validUntil = "") }
                ).removeOthers()

                coEvery { memberService.findByPerson(personId) } returns emptyList()
                coEvery { gracePeriodService.getGracePeriod(personId) } returns gracePeriod
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(malePerson)
                assertThat(result).isSuccessWithData(expected)

                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
            }

            @Test
            fun `#must return empty validUntil if membership was not activated`() = runBlocking {
                val malePerson = person.copy(sex = Sex.MALE)
                val member = member.copy(activationDate = null)
                val expected = expectedMale.copy(
                    conditions = expectedMale.conditions.map { it.copy(validUntil = "") },
                    gracePeriod = expectedMale.gracePeriod.map { it.copy(validUntil = "") }
                ).removeOthers()

                coEvery { productService.get(product.id) } returns product
                coEvery { memberService.findByPerson(personId) } returns listOf(member)
                coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(malePerson)
                assertThat(result).isSuccessWithData(expected)

                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
            }

            @Test
            fun `#must return empty validUntil if membership is pending`() = runBlocking {
                val malePerson = person.copy(sex = Sex.MALE)
                val member = member.copy(status = MemberStatus.PENDING)
                val expected = expectedMale.copy(
                    conditions = expectedMale.conditions.map { it.copy(validUntil = "") },
                    gracePeriod = expectedMale.gracePeriod.map { it.copy(validUntil = "") }
                ).removeOthers()

                coEvery { productService.get(product.id) } returns product
                coEvery { memberService.findByPerson(personId) } returns listOf(member)
                coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(malePerson)
                assertThat(result).isSuccessWithData(expected)

                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
            }

            @Test
            fun `#must return CTPS for male person without Birth grace period`() = runBlocking {
                val male = person.copy(sex = Sex.MALE)

                coEvery { productService.get(product.id) } returns product
                coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                coEvery { memberService.findByPerson(personId) } returns listOf(member)
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(male)
                assertThat(result).isSuccessWithData(expectedMaleWithoutOthers)

                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
            }

            @Test
            fun `#must return CTPS for male person without Birth grace period with HI and started at date`() =
                runBlocking {
                    val male = person.copy(sex = Sex.MALE)
                    val oldMembership = member.copy(
                        id = RangeUUID.generate(),
                        status = MemberStatus.CANCELED,
                        canceledAt = member.activationDate,
                        selectedProduct = TestModelFactory.buildMemberProduct(product2.id),
                    )
                    val expected = expectedMale.copy(
                        healthInstitutions = listOf(
                            HealthInstitution(startedAt = memberActivationDate, cnpj = listOf("02"))
                        )
                    ).removeOthers()

                    coEvery { productService.get(product.id) } returns product
                    coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                    coEvery { memberService.findByPerson(personId) } returns listOf(member, oldMembership)
                    coEvery {
                        productService.findByIdsWithProviders(listOf(product.id, product2.id))
                    } returns listOf(
                        ProductWithProviders(
                            product = product,
                            providers = listOf(provider2)
                        ),
                        ProductWithProviders(
                            product = product2,
                            providers = listOf(provider1)
                        )
                    )
                    coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(male)
                    assertThat(result).isSuccessWithData(expected)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                    coVerifyOnce { productService.findByIdsWithProviders(any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                }

            @Test
            fun `#must return CTPS if grace period is expired`() = runBlocking {
                val expiredGracePeriodMember = member.copy(activationDate = conditionPeriodExpiredDate)
                val male = person.copy(sex = Sex.MALE)
                val expected = expectedMale.copy(
                    conditions = expectedMale.conditions.map { it.reCalculate(conditionPeriodExpiredDate) },
                    gracePeriod = expectedMale.gracePeriod.map { it.reCalculate(conditionPeriodExpiredDate) },
                    baseGracePeriods = baseGracePeriods
                ).removeOthers()

                coEvery { productService.get(product.id) } returns product
                coEvery { memberService.findByPerson(personId) } returns listOf(expiredGracePeriodMember)
                coEvery { gracePeriodService.getGracePeriod(personId, expiredGracePeriodMember) } returns gracePeriod
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(male)
                assertThat(result).isSuccessWithData(expected)

                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { productService.get(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
            }

            @Test
            fun `#must return health institution started at even when the CPTs and grace period is expired`() =
                runBlocking {
                    val oldExpiredGracePeriodMember = member.copy(
                        id = RangeUUID.generate(),
                        status = MemberStatus.CANCELED,
                        activationDate = conditionPeriodExpiredDate,
                        canceledAt = member.activationDate,
                        selectedProduct = TestModelFactory.buildMemberProduct(product2.id),
                    )
                    val male = person.copy(sex = Sex.MALE)
                    val expected = expectedMale.copy(
                        conditions = expectedMale.conditions.map { it.reCalculate(conditionPeriodExpiredDate) },
                        gracePeriod = expectedMale.gracePeriod.map { it.reCalculate(conditionPeriodExpiredDate) },
                        healthInstitutions = listOf(
                            HealthInstitution(
                                startedAt = memberActivationDate,
                                cnpj = listOf("02")
                            )
                        ),
                        baseGracePeriods = baseGracePeriods
                    ).removeOthers()

                    coEvery { productService.get(product.id) } returns product
                    coEvery { memberService.findByPerson(personId) } returns listOf(member, oldExpiredGracePeriodMember)
                    coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                    coEvery {
                        productService.findByIdsWithProviders(listOf(product.id, product2.id))
                    } returns listOf(
                        ProductWithProviders(
                            product = product,
                            providers = listOf(provider2)
                        ),
                        ProductWithProviders(
                            product = product2,
                            providers = listOf(provider1)
                        )
                    )
                    coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(male)
                    assertThat(result).isSuccessWithData(expected)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { productService.findByIdsWithProviders(any()) }
                    coVerifyOnce { productService.get(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                }

            @Test
            fun `#must return CTPS with no grace for male person when product is B2B but membership is canceled`() =
                runBlocking {
                    val male = person.copy(sex = Sex.MALE)
                    val canceledMember = member.copy(status = MemberStatus.CANCELED)
                    val expected = expectedMale.copy(
                        conditions = expectedMale.conditions.map { it.copy(validUntil = "") },
                        gracePeriod = expectedMale.gracePeriod.map { it.copy(validUntil = "") }
                    ).removeOthers()

                    coEvery { productService.get(product.id) } returns product
                    coEvery { memberService.findByPerson(personId) } returns listOf(canceledMember)
                    coEvery { gracePeriodService.getGracePeriod(personId, canceledMember) } returns gracePeriod
                    coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(male)
                    assertThat(result).isSuccessWithData(expected)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                }


            @Test
            fun `#must not check is if b2b is flag is disabled`() = runBlocking {
                val male = person.copy(sex = Sex.MALE)

                coEvery { productService.get(product.id) } returns product
                coEvery { memberService.findByPerson(personId) } returns listOf(member)
                coEvery { gracePeriodService.getGracePeriod(personId, member) } returns gracePeriod
                coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                val result = memberCptsService.buildPersonCpts(male)
                assertThat(result).isSuccessWithData(expectedMaleWithoutOthers)

                coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                coVerifyOnce { memberService.findByPerson(any()) }
                coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                coVerifyOnce { productService.get(any()) }
                coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
            }
        }

        @Nested
        inner class MemberB2B {
            @Test
            fun `#must return CTPS and graces reset for male person if person is NO_RISK_FLOW b2b`() =
                runBlocking {
                    val beneficiary = beneficiary.copy(
                        onboarding = TestModelFactory.buildBeneficiaryOnboarding(
                            flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW
                        )
                    )

                    val expectedMaleForFullRiskB2B = expectedMaleForFullRiskB2B.copy(
                        conditions = commonHealthConditions.map { it.reCalculate(memberActivationDate, 0, 0) },
                        gracePeriod = cptsGracePeriods.map { it.reCalculate(memberActivationDate, 0, 0) },
                        baseGracePeriods = baseGracePeriods.getCalculatedPeriodsDiscount(GracePeriodType.TOTAL_EXEMPTION)
                    )

                    coEvery { productService.get(productB2B.id) } returns productB2B
                    coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                    coEvery { memberService.findByPerson(personId) } returns listOf(memberB2B)
                    coEvery { gracePeriodService.getGracePeriod(personId, memberB2B) } returns gracePeriod
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(person)
                    assertThat(result).isSuccessWithData(expectedMaleForFullRiskB2B)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                }

            @Test
            fun `#must return CTPS with all graces for male person if person is FULL_RISK_FLOW b2b`() =
                runBlocking {
                    val male = person.copy(sex = Sex.MALE)

                    val beneficiary = beneficiary.copy(
                        onboarding = TestModelFactory.buildBeneficiaryOnboarding(
                            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
                        )
                    )

                    coEvery { productService.get(productB2B.id) } returns productB2B
                    coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                    coEvery { memberService.findByPerson(personId) } returns listOf(memberB2B)
                    coEvery { gracePeriodService.getGracePeriod(personId, memberB2B) } returns gracePeriod
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(male)
                    assertThat(result).isSuccessWithData(expectedMaleForFullRiskB2BWithoutOthers)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                }

            @Test
            fun `#must return CTPS with all graces for male person if person is PARTIAL_RISK_FLOW b2b`() =
                runBlocking {
                    val male = person.copy(sex = Sex.MALE)

                    val beneficiary = beneficiary.copy(
                        onboarding = TestModelFactory.buildBeneficiaryOnboarding(
                            flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW
                        )
                    )

                    coEvery { productService.get(productB2B.id) } returns productB2B
                    coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                    coEvery { memberService.findByPerson(personId) } returns listOf(memberB2B)
                    coEvery { gracePeriodService.getGracePeriod(personId, memberB2B) } returns gracePeriod
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(male)
                    assertThat(result).isSuccessWithData(expectedMaleForFullRiskB2BWithoutOthers)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                }

            @Test
            fun `#must return CTPS with all graces for female person if person is FULL_RISK_FLOW b2b`() =
                runBlocking {
                    val female = person.copy(sex = Sex.FEMALE)

                    val beneficiary = beneficiary.copy(
                        onboarding = TestModelFactory.buildBeneficiaryOnboarding(
                            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
                        )
                    )

                    coEvery { productService.get(productB2B.id) } returns productB2B
                    coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                    coEvery { memberService.findByPerson(personId) } returns listOf(memberB2B)
                    coEvery { gracePeriodService.getGracePeriod(personId, memberB2B) } returns gracePeriod
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(female)
                    assertThat(result).isSuccessWithData(expectedFemaleForFullRiskB2BWithoutOthers)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                }

            @Test
            fun `#must return CTPS with all graces for female person if person is FULL_RISK_FLOW b2b base date from beneficiary grace period`() =
                runBlocking {
                    val baseDate = LocalDateTime.now().minusDays(10)

                    val commonHealthConditions = listOf(
                        buildCptCondition(healthCondition1, finalCpt1, baseDate),
                        buildCptCondition(healthCondition2, finalCpt2, baseDate)
                    )

                    val surgeryGracePeriod = buildCptGracePeriod(
                        type = EhrGracePeriodType.ELECTIVE_SURGERY,
                        activationDate = baseDate,
                        gracePeriodDays = surgeryGracePeriodDays
                    )

                    val birthGracePeriod = buildCptGracePeriod(
                        type = EhrGracePeriodType.BIRTH,
                        activationDate = baseDate,
                        gracePeriodDays = birthGracePeriodDays
                    )

                    val therapyGracePeriod = buildCptGracePeriod(
                        type = EhrGracePeriodType.THERAPY,
                        activationDate = baseDate,
                        gracePeriodDays = therapyGracePeriodDays
                    )

                    val specialExamPeriod = buildCptGracePeriod(
                        type = EhrGracePeriodType.SPECIAL_EXAMS,
                        activationDate = baseDate,
                        gracePeriodDays = specialExamsPeriodDays,
                    )

                    val pacGracePeriod = buildCptGracePeriod(
                        type = EhrGracePeriodType.PAC,
                        activationDate = baseDate,
                        gracePeriodDays = pacGracePeriodDays
                    )

                    val emergencyGracePeriod = buildCptGracePeriod(
                        type = EhrGracePeriodType.EMERGENCY,
                        activationDate = baseDate,
                        gracePeriodDays = emergencyGracePeriodDays
                    )

                    val cptsGracePeriods = listOf(
                        surgeryGracePeriod,
                        birthGracePeriod,
                        therapyGracePeriod,
                        specialExamPeriod,
                        pacGracePeriod,
                        emergencyGracePeriod,
                        hospitalizationGracePeriod.reCalculate(baseDate, hospitalizationInDays),
                        hospitalizationDueToPersonalAccidentGracePeriod.reCalculate(
                            baseDate,
                            hospitalizationDueToPersonalAccidentInDays
                        )
                    )
                    val expectedFemaleForFullRiskB2B = MemberCpt(
                        conditions = commonHealthConditions,
                        gracePeriod = cptsGracePeriods,
                        baseGracePeriods = baseGracePeriods
                    )

                    val female = person.copy(sex = Sex.FEMALE)

                    val beneficiary = beneficiary.copy(
                        gracePeriodBaseDate = baseDate.toLocalDate(),
                        onboarding = TestModelFactory.buildBeneficiaryOnboarding(
                            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
                        )
                    )

                    coEvery { productService.get(productB2B.id) } returns productB2B
                    coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                    coEvery { memberService.findByPerson(personId) } returns listOf(memberB2B)
                    coEvery { gracePeriodService.getGracePeriod(personId, memberB2B) } returns gracePeriod
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(female)
                    assertThat(result).isSuccessWithData(expectedFemaleForFullRiskB2B)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                }

            @Test
            fun `#must return CTPS with all graces for female person if person is PARTIAL_RISK_FLOW b2b`() =
                runBlocking {
                    val female = person.copy(sex = Sex.FEMALE)

                    val beneficiary = beneficiary.copy(
                        onboarding = TestModelFactory.buildBeneficiaryOnboarding(
                            flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW
                        )
                    )

                    coEvery { productService.get(productB2B.id) } returns productB2B
                    coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                    coEvery { memberService.findByPerson(personId) } returns listOf(memberB2B)
                    coEvery { gracePeriodService.getGracePeriod(personId, memberB2B) } returns gracePeriod
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(female)
                    assertThat(result).isSuccessWithData(expectedFemaleForFullRiskB2BWithoutOthers)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                }

            @Test
            fun `#must return CTPS and graces reset for female person if person is NO_RISK_FLOW b2b`() =
                runBlocking {
                    val female = person.copy(sex = Sex.FEMALE)

                    val beneficiary = beneficiary.copy(
                        onboarding = TestModelFactory.buildBeneficiaryOnboarding(
                            flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                        )
                    )

                    val expectedFemaleForFullRiskB2B = expectedFemaleForFullRiskB2B.copy(
                        conditions = commonHealthConditions.map { it.reCalculate(memberActivationDate, 0, 0) },
                        gracePeriod = cptsGracePeriods.map { it.reCalculate(memberActivationDate, 0, 0) },
                        baseGracePeriods = baseGracePeriods.getCalculatedPeriodsDiscount(GracePeriodType.TOTAL_EXEMPTION)
                    )

                    coEvery { productService.get(productB2B.id) } returns productB2B
                    coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary
                    coEvery { memberService.findByPerson(personId) } returns listOf(memberB2B)
                    coEvery { gracePeriodService.getGracePeriod(personId, memberB2B) } returns gracePeriod
                    coEvery { healthConditionService.findByCodesAndType(cidCodes, CID_10) } returns healthConditions

                    val result = memberCptsService.buildPersonCpts(female)
                    assertThat(result).isSuccessWithData(expectedFemaleForFullRiskB2B)

                    coVerifyOnce { gracePeriodService.getGracePeriod(any(), any()) }
                    coVerifyOnce { memberService.findByPerson(any()) }
                    coVerifyOnce { healthConditionService.findByCodesAndType(any(), any()) }
                    coVerifyOnce { productService.get(any()) }
                    coVerifyOnce { beneficiaryService.findByPersonId(any(), any()) }
                }
        }
    }

    private fun buildCptCondition(
        healthCondition: HealthCondition,
        cpt: Cpt,
        activationDate: LocalDateTime,
        gracePeriodDays: Long = conditionGracePeriodDays
    ) = CptCondition(
        healthCondition = healthCondition,
        name = cpt.condition,
        cid = cpt.cids.joinToString(),
        validUntil = activationDate
            .plusDays(gracePeriodDays)
            .toLocalDate()
            .toBrazilianDateFormat(),
        baseDate = activationDate.toLocalDate(),
        periodInDays = gracePeriodDays
    )

    private fun buildCptGracePeriod(
        type: EhrGracePeriodType,
        activationDate: LocalDateTime,
        gracePeriodDays: Long
    ) = CptGracePeriod(
        condition = type.description,
        validUntil = activationDate
            .plusDays(gracePeriodDays)
            .toLocalDate()
            .toBrazilianDateFormat(),
        type = type,
        baseDate = activationDate.toLocalDate(),
        periodInDays = gracePeriodDays
    )

    private fun CptCondition.reCalculate(
        activationDate: LocalDateTime,
        gracePeriodDays: Long? = null,
        periodInDays: Long? = null
    ) = copy(
        validUntil = activationDate.plusDays(gracePeriodDays ?: this.periodInDays).toBrazilianDateFormat(),
        baseDate = activationDate.toLocalDate(),
        periodInDays = periodInDays ?: this.periodInDays
    )

    private fun CptGracePeriod.reCalculate(
        activationDate: LocalDateTime,
        gracePeriodDays: Long? = null,
        periodInDays: Long? = null
    ) = copy(
        validUntil = activationDate.plusDays(gracePeriodDays ?: this.periodInDays).toBrazilianDateFormat(),
        baseDate = activationDate.toLocalDate(),
        periodInDays = periodInDays ?: this.periodInDays
    )
}
