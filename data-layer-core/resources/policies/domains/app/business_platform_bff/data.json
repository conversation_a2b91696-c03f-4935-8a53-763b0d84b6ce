{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["CompanyStaffModel", "CompanySubContractModel", "StaffModel", "CompanyModel", "PersonModel", "ProductModel", "Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "CassiMemberModel", "CompanyContractModel", "CompanySubContractModel", "Generic<PERSON><PERSON><PERSON><PERSON>", "MemberModel", "MemberProductPriceModel", "PriceListingModel", "ProductPriceListingModel", "CompanyProductPriceListingModel"], "actions": ["view", "count"]}]}, {"conditions": ["${subject.opaType} == CompanyStaff"], "allow": [{"resources": ["CompanyActivationFilesModel", "MemberProductChangeScheduleModel", "PersonModel"], "actions": ["create", "update"]}, {"resources": ["BeneficiaryCompiledViewModel", "CoPaymentCostInfoModel", "CompanyProductPriceListingModel", "CompanyRefundCostInfoModel", "CompanyScoreMagenta", "ConsolidatedHRCompanyReport", "InvoiceGroupTaxReceiptModel", "InvoiceLiquidationModel", "OngoingCompanyDeal", "PersonClinicalAccount", "PersonOnboardingModel", "PriceListingModel", "ProductModel", "ProductBundleModel", "ProductGroupModel", "ProductPriceListingModel", "ProviderModel", "RefundCostInfoModel", "StaffModel"], "actions": ["view", "count"]}, {"resources": ["MemberInvoiceGroupModel", "PreActivationPaymentModel"], "actions": ["count"]}, {"resources": ["HealthDeclaration"], "actions": ["create"]}, {"resources": ["Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "BillingAccountablePartyModel", "BoletoPaymentDetailModel", "BolepixPaymentDetailModel", "CassiMemberModel", "CompanyModel", "CompanyContractModel", "CompanyStaffModel", "CompanySubContractModel", "InvoicePaymentModel", "CancelPaymentOnAcquirerScheduleModel", "ResourceSignTokenModel", "MemberModel", "MemberInvoiceModel", "MemberProductPriceModel", "PersonModel", "PersonBillingAccountablePartyModel", "PixPaymentDetailModel"], "actions": ["view", "count", "create", "update"]}, {"resources": ["PersonRegistrationModel", "PersonOnboardingModel"], "actions": ["view", "count", "create"]}, {"resources": ["CompanyActivationFilesModel", "MemberProductChangeScheduleModel"], "actions": ["view", "create", "update"]}], "branches": [{"conditions": ["${resource.namespace} == company_activation_files"], "allow": [{"resources": ["Generic<PERSON><PERSON><PERSON><PERSON>"], "actions": ["create", "view"]}]}, {"conditions": ["${subject.companyId} == ${resource.companyId}"], "allow": [{"resources": ["MemberInvoiceGroupModel", "PreActivationPaymentModel"], "actions": ["view"]}]}, {"conditions": ["${resource.domain} == business", "${resource.namespace} == dependent_files"], "allow": [{"resources": ["FileVault"], "actions": ["view", "create", "update", "delete"]}]}]}]}