package br.com.alice.business.consumers

import br.com.alice.business.client.BeneficiaryOnboardingService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.events.BeneficiaryCreatedEvent
import br.com.alice.business.logics.BeneficiaryOnboardingLogic
import br.com.alice.business.services.internal.BeneficiaryHubspotService
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.isSameAs
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.person.client.MemberProductPriceService
import br.com.alice.person.client.MemberService
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class BeneficiaryCreatedConsumerTest : ConsumerTest() {

    private val beneficiaryOnboardingService: BeneficiaryOnboardingService = mockk()
    private val beneficiaryHubspotService: BeneficiaryHubspotService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val memberService: MemberService = mockk()
    private val memberProductPriceService: MemberProductPriceService = mockk()

    private val consumer = BeneficiaryCreatedConsumer(
        beneficiaryOnboardingService,
        beneficiaryHubspotService,
        beneficiaryService,
        memberService,
        memberProductPriceService,
    )

    @BeforeTest
    fun setup() {
        super.before()
        mockkObject(BeneficiaryOnboardingLogic)
    }

    @AfterTest
    override fun clear() {
        super.clear()
        unmockkAll()
    }

    @Test
    fun `#createOnboarding should add BeneficiaryOnboarding as expected`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary()
        val initialProductId = RangeUUID.generate()
        val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW

        val event = BeneficiaryCreatedEvent(
            beneficiary = beneficiary,
            initialProductId = initialProductId,
            flowType = flowType,
        )
        val company = TestModelFactory.buildCompany(
            id = beneficiary.companyId,
            availableProducts = listOf(initialProductId),
        )
        val expectedBeneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding()

        coEvery {
            beneficiaryOnboardingService.addWithPhase(
                any(),
                any(),
                any(),
                any(),
                any()
            )
        } returns expectedBeneficiaryOnboarding

        val result = consumer.createOnboarding(event)
        assertThat(result).isSuccessWithData(expectedBeneficiaryOnboarding)

        coVerifyOnce {
            beneficiaryOnboardingService.addWithPhase(
                beneficiary.id,
                flowType,
                initialProductId,
                READY_TO_ONBOARD,
                event.eventDate,
            )
        }
    }

    @Test
    fun `#create should add BeneficiaryHubspot as expected`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary()
        val initialProductId = RangeUUID.generate()
        val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW

        val event = BeneficiaryCreatedEvent(
            beneficiary = beneficiary,
            initialProductId = initialProductId,
            flowType = flowType,
        )

        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(
            beneficiaryId = beneficiary.id,
            externalDealId = "123",
            externalContactId = "12341",
        )
        coEvery {
            beneficiaryHubspotService.create(any(), any(), any())
        } returns beneficiaryHubspot

        val result = consumer.createBeneficiaryHubspot(event)

        assertThat(result).isSuccessWithData(beneficiaryHubspot)

        coVerifyOnce {
            beneficiaryHubspotService.create(
                beneficiary,
                initialProductId,
                flowType
            )
        }
    }

    @Test
    fun `#create should not add BeneficiaryHubspot if brand is not ALICE`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary(brand = Brand.DUQUESA)
        val initialProductId = RangeUUID.generate()
        val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW

        val event = BeneficiaryCreatedEvent(
            beneficiary = beneficiary,
            initialProductId = initialProductId,
            flowType = flowType,
        )

        val result = consumer.createBeneficiaryHubspot(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyNone {
            beneficiaryHubspotService.create(
                beneficiary,
                initialProductId,
                flowType
            )
        }
    }

    @Nested
    inner class CreateNewDependents {

        @Test
        fun `#should skip if dependent`() = runBlocking {
            val beneficiary = TestModelFactory.buildBeneficiary(
                parentBeneficiary = RangeUUID.generate(),
                type = BeneficiaryType.DEPENDENT
            )
            val initialProductId = RangeUUID.generate()
            val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
            val event = BeneficiaryCreatedEvent(
                beneficiary = beneficiary,
                initialProductId = initialProductId,
                flowType = flowType,
            )
            val result = consumer.createNewDependents(event)

            assertThat(result).isSuccessWithData(true)
        }

        @Test
        fun `#should do nothing if doesnt have dependents`() = runBlocking {
            val personId = PersonId()
            val beneficiary = TestModelFactory.buildBeneficiary(personId = personId)
            val initialProductId = RangeUUID.generate()
            val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
            val event = BeneficiaryCreatedEvent(
                beneficiary = beneficiary,
                initialProductId = initialProductId,
                flowType = flowType,
            )

            coEvery { beneficiaryService.findByParentPerson(any(), any(), any()) } returns emptyList()
            coEvery { memberService.findByIds(emptyList()) } returns emptyList()

            val result = consumer.createNewDependents(event)

            coVerifyOnce {
                beneficiaryService.findByParentPerson(
                    beneficiary.personId,
                    listOf(MemberStatus.ACTIVE, MemberStatus.PENDING, MemberStatus.CANCELED),
                    BeneficiaryService.FindOptions(withOnboarding = true)
                )
            }
            coVerifyNone { beneficiaryService.updateDependentWithNewParent(any(), any()) }

            assertThat(result).isSuccessWithData(emptyList<Beneficiary>())
        }

        @Test
        fun `#should find and recreate dependents as expected`(): Unit = runBlocking {
            val personId = PersonId()
            val beneficiary = TestModelFactory.buildBeneficiary(personId = personId)

            val dependents = listOf(
                TestModelFactory.buildBeneficiary(
                    parentBeneficiary = RangeUUID.generate(),
                    type = BeneficiaryType.DEPENDENT
                ), TestModelFactory.buildBeneficiary(
                    parentBeneficiary = RangeUUID.generate(),
                    type = BeneficiaryType.DEPENDENT
                )
            )

            val dependentsMembers = listOf(
                TestModelFactory.buildMember(
                    id = dependents[0].memberId,
                    status = MemberStatus.ACTIVE
                ),
                TestModelFactory.buildMember(
                    id = dependents[1].memberId,
                    status = MemberStatus.ACTIVE
                ),
            )
            val initialProductId = RangeUUID.generate()
            val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
            val event = BeneficiaryCreatedEvent(
                beneficiary = beneficiary,
                initialProductId = initialProductId,
                flowType = flowType,
            )

            coEvery { beneficiaryService.findByParentPerson(any(), any(), any()) } returns dependents
            coEvery { memberService.findByIds(dependents.map { it.memberId }) } returns dependentsMembers
            coEvery { beneficiaryService.updateDependentWithNewParent(dependents[0], any()) } returns dependents[0]
            coEvery { beneficiaryService.updateDependentWithNewParent(dependents[1], any()) } returns dependents[1]

            val response = consumer.createNewDependents(event).get()

            coVerifyOnce {
                beneficiaryService.findByParentPerson(
                    beneficiary.personId,
                    listOf(MemberStatus.ACTIVE, MemberStatus.PENDING, MemberStatus.CANCELED),
                    BeneficiaryService.FindOptions(withOnboarding = true)
                )
            }
            coVerifyOnce { beneficiaryService.updateDependentWithNewParent(dependents[0], beneficiary) }
            coVerifyOnce { beneficiaryService.updateDependentWithNewParent(dependents[1], beneficiary) }

            Assertions.assertThat(dependents.isSameAs(response as List<*>)).isTrue()
        }

        @Test
        fun `#should filter out canceled dependents`(): Unit = runBlocking {
            val personId = PersonId()
            val beneficiary = TestModelFactory.buildBeneficiary(personId = personId)

            val dependents = listOf(
                TestModelFactory.buildBeneficiary(
                    parentBeneficiary = RangeUUID.generate(),
                    type = BeneficiaryType.DEPENDENT
                ), TestModelFactory.buildBeneficiary(
                    parentBeneficiary = RangeUUID.generate(),
                    type = BeneficiaryType.DEPENDENT
                )
            )

            val dependentsMembers = listOf(
                TestModelFactory.buildMember(
                    id = dependents[0].memberId,
                    status = MemberStatus.CANCELED
                ),
                TestModelFactory.buildMember(
                    id = dependents[1].memberId,
                    status = MemberStatus.CANCELED
                ),
            )
            val initialProductId = RangeUUID.generate()
            val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
            val event = BeneficiaryCreatedEvent(
                beneficiary = beneficiary,
                initialProductId = initialProductId,
                flowType = flowType,
            )

            coEvery { beneficiaryService.findByParentPerson(any(), any(), any()) } returns dependents
            coEvery { memberService.findByIds(dependents.map { it.memberId }) } returns dependentsMembers

            consumer.createNewDependents(event).get()

            coVerifyOnce {
                beneficiaryService.findByParentPerson(
                    beneficiary.personId,
                    listOf(MemberStatus.ACTIVE, MemberStatus.PENDING, MemberStatus.CANCELED),
                    BeneficiaryService.FindOptions(withOnboarding = true)
                )
            }
            coVerifyNone { beneficiaryService.updateDependentWithNewParent(any(), any()) }
        }
    }

    @Nested
    inner class CreateMemberProductPrice {
        @Test
        fun `#should create a member product price when the beneficiary belongs to a company into FF`(): Unit =
            runBlocking {
                val companyId = RangeUUID.generate()
                withFeatureFlag(
                    FeatureNamespace.MEMBERSHIP,
                    "should_create_mpp_for_this_companies",
                    listOf(companyId.toString())
                ) {
                    val personId = PersonId()
                    val beneficiary = TestModelFactory.buildBeneficiary(personId = personId, companyId = companyId)

                    val initialProductId = RangeUUID.generate()
                    val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
                    val event = BeneficiaryCreatedEvent(
                        beneficiary = beneficiary,
                        initialProductId = initialProductId,
                        flowType = flowType,
                    )

                    val member = TestModelFactory.buildMember(id = beneficiary.memberId)
                    val memberProductPrice = TestModelFactory.buildMemberProductPrice(memberId = member.id)

                    coEvery { memberService.get(beneficiary.memberId) } returns member
                    coEvery { memberProductPriceService.findCurrent(member.id) } returns NotFoundException("")
                    coEvery {
                        memberProductPriceService.setCurrent(
                            member.id,
                            member.productId,
                            any(),
                        )
                    } returns memberProductPrice


                    val result = consumer.createMemberProductPrice(event)

                    coVerifyOnce {
                        memberService.get(beneficiary.memberId)
                        memberProductPriceService.findCurrent(member.id)
                        memberProductPriceService.setCurrent(
                            member.id,
                            member.productId,
                            any(),
                        )
                    }

                    assertThat(result).isSuccess()
                }
            }

        @Test
        fun `#should skip the consumer when the beneficiary does not belong to a company into FF`(): Unit =
            runBlocking {
                val companyId = RangeUUID.generate()
                withFeatureFlag(
                    FeatureNamespace.MEMBERSHIP,
                    "should_create_mpp_for_this_companies",
                    listOf(companyId.toString())
                ) {
                    val personId = PersonId()
                    val beneficiary = TestModelFactory.buildBeneficiary(personId = personId)

                    val initialProductId = RangeUUID.generate()
                    val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
                    val event = BeneficiaryCreatedEvent(
                        beneficiary = beneficiary,
                        initialProductId = initialProductId,
                        flowType = flowType,
                    )

                    val result = consumer.createMemberProductPrice(event)

                    coVerifyNone {
                        memberService.get(any())
                        memberProductPriceService.findCurrent(any())
                        memberProductPriceService.setCurrent(
                            any(),
                            any(),
                            any(),
                        )
                    }

                    assertThat(result).isSuccess()
                }
            }

        @Test
        fun `#should skip the consumer when the membership already has a MPP`(): Unit =
            runBlocking {
                val companyId = RangeUUID.generate()
                withFeatureFlag(
                    FeatureNamespace.MEMBERSHIP,
                    "should_create_mpp_for_this_companies",
                    listOf(companyId.toString())
                ) {
                    val personId = PersonId()
                    val beneficiary = TestModelFactory.buildBeneficiary(personId = personId, companyId = companyId)

                    val initialProductId = RangeUUID.generate()
                    val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
                    val event = BeneficiaryCreatedEvent(
                        beneficiary = beneficiary,
                        initialProductId = initialProductId,
                        flowType = flowType,
                    )

                    val member = TestModelFactory.buildMember(id = beneficiary.memberId)
                    val memberProductPrice = TestModelFactory.buildMemberProductPrice(memberId = member.id)

                    coEvery { memberService.get(beneficiary.memberId) } returns member
                    coEvery { memberProductPriceService.findCurrent(member.id) } returns memberProductPrice


                    val result = consumer.createMemberProductPrice(event)

                    coVerifyOnce {
                        memberService.get(beneficiary.memberId)
                        memberProductPriceService.findCurrent(member.id)
                    }

                    coVerifyNone {
                        memberProductPriceService.setCurrent(
                            any(),
                            any(),
                            any(),
                        )
                    }

                    assertThat(result).isSuccess()
                }
            }
    }

}
