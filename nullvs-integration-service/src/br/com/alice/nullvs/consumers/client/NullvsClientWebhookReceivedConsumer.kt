package br.com.alice.nullvs.consumers.client

import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.data.layer.models.NullvsIntegrationLog
import br.com.alice.data.layer.models.NullvsIntegrationRecord
import br.com.alice.nullvs.consumers.Consumer
import br.com.alice.nullvs.events.NullvsClientActivatedEvent
import br.com.alice.nullvs.events.NullvsClientWebhookReceivedEvent
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.Method
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.Status
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.countNullvsWebhookConsumer
import br.com.alice.nullvs.models.NullvsClientWebhookReceived
import br.com.alice.nullvs.models.TotvsStatus
import br.com.alice.nullvs.services.internals.NullvsIntegrationLogService
import br.com.alice.nullvs.services.internals.NullvsIntegrationRecordService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

class NullvsClientWebhookReceivedConsumer(
    private val nullvsIntegrationLogService: NullvsIntegrationLogService,
    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService,
    private val kafkaProducerService: KafkaProducerService,
) : Consumer() {
    suspend fun processIntegratedClientAtTotvs(event: NullvsClientWebhookReceivedEvent) = withSubscribersEnvironment {
        val response = event.payload.response

        logger.info(
            "Consume the NullvsClientWebhookReceivedEvent message",
            "message_event_id" to event.messageId,
            "event_date" to event.eventDate,
            "nullvs_webhook_response" to response,
        )

        nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
            response.batchId,
            response.idSoc,
            response.posBatchPayload
        )
            .flatMap {
                logger.info("found the integration log", "model" to it)
                callEventExecution(it, response)
            }.then {
                logger.info("The ${it.batchType} integration worked", "nullvs_log_id" to it.id)
                countNullvsWebhookConsumer(Method.CLIENT, Status.SUCCESS)
            }
            .coFoldNotFound {
                when (response.status) {
                    TotvsStatus.SUCCESS -> {
                        fallbackWhenNotExistsNullvsLogClient(response)
                            .flatMap {
                                callEventExecution(it, response)
                            }
                            .then { countNullvsWebhookConsumer(Method.CLIENT, Status.SUCCESS) }
                    }

                    TotvsStatus.FAILURE -> {
                        logger.info(
                            "Client integration to Totvs failed",
                            "error_message" to response.payload.errorMessage,
                            "billing_id" to response.payload.internalId
                        )
                        countNullvsWebhookConsumer(Method.CLIENT, Status.FAILURE)
                    }
                }
                true.success()
            }
    }

    private suspend fun callEventExecution(
        log: NullvsIntegrationLog,
        event: NullvsClientWebhookReceived
    ): Result<NullvsIntegrationLog, Throwable> =
        when (log.batchType) {
            BatchType.CREATE -> createRecord(event, log)
            BatchType.CANCEL -> cancelRecord(log)
            BatchType.UPDATE -> updateRecord(event, log)
            else -> Exception("TODO").failure()
        }

    private suspend fun createRecord(webhook: NullvsClientWebhookReceived, log: NullvsIntegrationLog) =
        when (webhook.status) {
            TotvsStatus.SUCCESS ->
                nullvsIntegrationRecordService.add(webhook.toNullvsIntegrationRecord(log.id))
                    .coFoldDuplicated {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            webhook.payload.internalId!!,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    }
                    .flatMap { nullvsIntegrationLogService.update(log.copy(status = LogStatus.FINISHED)) }
                    .then {
                        kafkaProducerService.produce(
                            NullvsClientActivatedEvent(
                                webhook.payload.internalId!!,
                                webhook.payload.clientCode!!
                            )
                        )
                    }

            TotvsStatus.FAILURE -> nullvsIntegrationLogService.update(
                log.copy(
                    status = LogStatus.FAILURE,
                    description = webhook.payload.errorMessage,
                )
            )
        }

    private suspend fun cancelRecord(log: NullvsIntegrationLog) =
        nullvsIntegrationRecordService.get(log.internalId)
            .flatMap { nullvsIntegrationRecordService.update(it.copy(canceledAt = LocalDateTime.now())) }
            .map { log }

    private suspend fun updateRecord(webhook: NullvsClientWebhookReceived, log: NullvsIntegrationLog) =
        when (webhook.status) {
            TotvsStatus.SUCCESS -> nullvsIntegrationLogService.update(log.copy(status = LogStatus.FINISHED))
            TotvsStatus.FAILURE -> nullvsIntegrationLogService.update(
                log.copy(
                    status = LogStatus.FAILURE,
                    description = webhook.payload.errorMessage,
                )
            )
        }

    private suspend fun fallbackWhenNotExistsNullvsLogClient(payload: NullvsClientWebhookReceived) =
        nullvsIntegrationLogService.add(
            NullvsIntegrationLog(
                batchId = payload.batchId,
                idSoc = payload.idSoc,
                payloadSequenceId = payload.posBatchPayload,
                status = LogStatus.PENDING,
                description = payload.payload.errorMessage,
                batchType = payload.type,
                internalId = payload.payload.internalId!!,
                internalModelName = InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                externalModelName = ExternalModelType.CLIENT,
                integrationEventName = NullvsClientWebhookReceivedEvent.name,
                eventId = UUID.randomUUID(),
                eventName = NullvsClientWebhookReceivedEvent.name,
            )
        )

}

fun NullvsClientWebhookReceived.toNullvsIntegrationRecord(logId: UUID) =
    NullvsIntegrationRecord(
        internalId = this.payload.internalId!!,
        internalModelName = this.payload.internalModelType!!,
        externalId = this.payload.clientCode!!,
        externalModelName = ExternalModelType.CLIENT,
        integratedAt = LocalDateTime.now()
    )
