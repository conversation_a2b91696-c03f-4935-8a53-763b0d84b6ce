package app.member_wannabe_api_test

import rego.v1

import data.app.member_wannabe_api

test_VicProductOption_allowed if {
	{1,2,3,4,5} == member_wannabe_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "VicProductOption",
            },
		},
		{
            "index": 2,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "VicProductOption",
            },
        },
        {
            "index": 3,
            "action": "update",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "VicProductOption",
            },
        },
        {
            "index": 4,
            "action": "delete",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "VicProductOption",
            },
        },
        {
            "index": 5,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "VicProductOption",
            },
        }]
	}
}

test_SiteAccredited_view_allowed if {
	{1,2,3,4} == member_wannabe_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "SiteAccreditedNetwork",
            },
		},
		{
            "index": 2,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "SiteAccreditedNetworkProvider",
            },
        },
        {
            "index": 3,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "SiteAccreditedNetworkAddress",
            },
        },
        {
            "index": 4,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "SiteAccreditedNetworkCategory",
            },
        }]
	}
}

test_SiteAccredited_count_allowed if {
	{1,2,3,4} == member_wannabe_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "SiteAccreditedNetwork",
            },
		},
		{
            "index": 2,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "SiteAccreditedNetworkProvider",
            },
        },
        {
            "index": 3,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "SiteAccreditedNetworkAddress",
            },
        },
        {
            "index": 4,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "SiteAccreditedNetworkCategory",
            },
        }]
	}
}

test_entities_count_allowed if {
	{1,2,3,4,5,6,7,8,9,10,11} == member_wannabe_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "PersonModel",
            },
		},
		{
            "index": 2,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "StaffModel",
            },
        },
        {
            "index": 3,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "HealthProfessionalModel",
            },
        },
        {
            "index": 4,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "HealthCommunitySpecialistModel",
            },
        },
        {
            "index": 5,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ProviderUnitModel",
            },
        },
        {
            "index": 6,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "StructuredAddress",
            },
        },
        {
            "index": 7,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ContactModel",
            },
        },
        {
            "index": 8,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ProviderModel",
            },
        },
        {
            "index": 9,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ProductRecommendation",
            },
        },
        {
            "index": 10,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ConsolidatedAccreditedNetwork",
            },
        },
        {
            "index": 11,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "SiteAccreditedNetworkFlagship",
            },
        }]
	}
}

test_entities_view_allowed if {
	{1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22} == member_wannabe_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ProductModel",
            },
		},
		{
            "index": 2,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "PersonSalesInfo",
            },
        },
        {
            "index": 3,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ProductBundleModel",
            },
        },
        {
            "index": 4,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "PromoCodeModel",
            },
        },
        {
            "index": 5,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ProductPriceListingModel",
            },
        },
        {
            "index": 6,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "PriceListingModel",
            },
        },
        {
            "index": 7,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ProviderModel",
            },
        },
        {
            "index": 8,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ProviderModel",
            },
        },
        {
            "index": 9,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ProviderUnitModel",
            },
        },
        {
            "index": 10,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "StructuredAddress",
            },
        },
        {
            "index": 11,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ContactModel",
            },
        },
        {
            "index": 12,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ProviderUnitGroupModel",
            },
        },
        {
            "index": 13,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "StaffModel",
            },
        },
        {
            "index": 14,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "HealthProfessionalModel",
            },
        },
        {
            "index": 15,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "MedicalSpecialtyModel",
            },
        },
        {
            "index": 16,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "HealthcareTeamModel",
            },
        },
        {
            "index": 17,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "HealthCommunitySpecialistModel",
            },
        },
        {
            "index": 18,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ProductRecommendation",
            },
        },
        {
            "index": 19,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "CoveredGeoRegionModel",
            },
        },
        {
            "index": 20,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ConsolidatedAccreditedNetwork",
            },
        },
        {
            "index": 21,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "CassiSpecialistModel",
            },
        },
        {
            "index": 22,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "SiteAccreditedNetworkFlagship",
            },
        }]
	}
}

test_entities_create_allowed if {
    {1,2,3,4,5,6,7,8,9,10,11,12,13,14,15} == member_wannabe_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "PersonModel",
            },
        },
        {
            "index": 2,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "PersonLoginModel",
            },
        },
        {
            "index": 3,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ProductOrderModel",
            },
        },
        {
            "index": 4,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "PersonOnboardingModel",
            },
        },
        {
            "index": 5,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "Lead",
            },
        },
        {
            "index": 6,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "HealthDeclaration",
            },
        },
        {
            "index": 7,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "PersonRegistrationModel",
            },
        },
        {
            "index": 8,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "InsurancePortabilityRequestModel",
            },
        },
        {
            "index": 9,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "HealthProductSimulation",
            },
        },
        {
            "index": 10,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ShoppingCartModel",
            },
        },
        {
            "index": 11,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "Opportunity",
            },
        },
        {
            "index": 12,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "HealthProductSimulationGroup",
            },
        },
        {
            "index": 13,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "UpdatedPersonContactInfoTempModel",
            },
        },
        {
            "index": 14,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "ZipcodeAddress",
            },
        },
        {
            "index": 15,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated"
            },
            "resource": {
                "opaType": "CoveredGeoRegionModel",
            },
        }]
    }
}
