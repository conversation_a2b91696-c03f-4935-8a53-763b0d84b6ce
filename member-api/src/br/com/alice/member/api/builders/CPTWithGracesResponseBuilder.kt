package br.com.alice.member.api.builders

import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.data.layer.models.Member
import br.com.alice.ehr.model.CptCondition
import br.com.alice.ehr.model.CptGracePeriod
import br.com.alice.ehr.model.MemberCpt
import br.com.alice.member.api.MobileApp
import br.com.alice.member.api.models.CPTTransport
import br.com.alice.member.api.models.CPTWithGracesResponse
import br.com.alice.member.api.models.Grace
import br.com.alice.member.api.models.GraceTransport
import br.com.alice.member.api.models.ProcedureTransport
import br.com.alice.member.api.models.RemainingTime
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

object CPTWithGracesResponseBuilder {

    private const val CONTRACT_EXPIRATION_MONTHS = 24L
    private val dateTimeFormat = DateTimeFormatter.ofPattern("dd/MM/yyyy")

    fun buildCPTWithGracesResponse(
        member: Member,
        memberCpt: MemberCpt,
    ): CPTWithGracesResponse {
        val activationDate = member.activationDate ?: throw Exception("Member not active")

        val cpt = buildCPTTransport(memberCpt.conditions)
        val grace = buildGraceTransport(memberCpt.gracePeriod, activationDate)

        return CPTWithGracesResponse(cpt, grace)
    }

    private fun buildCPTTransport(conditions: List<CptCondition>): CPTTransport? =
        if (conditions.isEmpty()) null
        else {
            val expirationDate = conditions.minOf { it.validUntil.toLocalDate(dateTimeFormat) }.atEndOfTheDay()
            val remainingTime = buildRemainingTime(expirationDate)
            val procedures = buildProcedures(remainingTime)
            CPTTransport(remainingTime, conditions.map { it.name }, procedures, MobileApp.Links.ALL_PROCEDURES_URL)
        }

    private fun buildRemainingTime(expirationDate: LocalDateTime): RemainingTime {
        val today = LocalDateTime.now()
        val unit = ChronoUnit.DAYS
        val remaining = unit.between(today, expirationDate)
        val total = unit.between(expirationDate.minusMonths(CONTRACT_EXPIRATION_MONTHS), expirationDate)
        return RemainingTime(total.toInt(), if (remaining < 0) 0 else remaining.toInt(), unit.name)
    }

    private fun buildProcedures(remainingTime: RemainingTime): List<ProcedureTransport> =
        listOf(
            ProcedureTransport(
                "Exames de alta complexidade",
                "Ex: Ressonância magnética, tomografia computadorizada, entre outros.",
                remainingTime
            ),
            ProcedureTransport(
                "Procedimentos cirúrgicos",
                "Seja em casos pré-agendados, de urgência ou emergência.",
                remainingTime
            ),
            ProcedureTransport(
                "Internações em leitos de CTI e/ou UTI",
                "Seja em casos pré-agendados, de urgência ou emergência.",
                remainingTime
            ),
            ProcedureTransport(
                "Outros procedimentos de alta complexidade",
                "Ex: Sessões de quimioterapia, radioterapia, hemodiálise crônica, entre outros.",
                remainingTime
            )
        )

    private fun buildGraceTransport(gracePeriod: List<CptGracePeriod>, signedAt: LocalDateTime): GraceTransport {
        val graces = gracePeriod.map {
            Grace(it.condition, buildRemainingTime(it.validUntil.toLocalDate(dateTimeFormat).atEndOfTheDay()))
        }
        return GraceTransport(signedAt.toBrazilianDateFormat(), graces)
    }
}
