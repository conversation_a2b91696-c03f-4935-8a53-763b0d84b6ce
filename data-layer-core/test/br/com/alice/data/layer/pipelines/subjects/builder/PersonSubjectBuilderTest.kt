package br.com.alice.data.layer.pipelines.subjects.builder

import br.com.alice.authentication.TokenVerifier
import br.com.alice.common.TestApplication
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.localDateTimeComparator
import br.com.alice.data.layer.authorization.AuthorizationService
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestTableFactory
import br.com.alice.data.layer.models.FinancialDataModel
import br.com.alice.data.layer.models.MemberModel
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.pipelines.ConverterExtension
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.pipelines.context.ContextService
import br.com.alice.data.layer.pipelines.subjects.PersonSubject
import br.com.alice.data.layer.pipelines.subjects.builders.PersonSubjectBuilder
import br.com.alice.data.layer.repositories.JdbiRepository
import br.com.alice.data.layer.services.PersonTokenService
import br.com.alice.data.layer.services.PersonTokenServiceImpl
import br.com.alice.data.layer.services.ReplicationLagService
import br.com.alice.data.layer.tables.BeneficiaryTable
import br.com.alice.data.layer.tables.LegalGuardianAssociationTable
import br.com.alice.data.layer.tables.MemberTable
import br.com.alice.data.layer.tables.PersonTable
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class PersonSubjectBuilderTest {

    private val mainJdbi = TestApplication.mainJdbi
    private val tokenJdbi = TestApplication.tokenJdbi

    private val personRepository = JdbiRepository(mainJdbi, PersonTable::class)
    private val beneficiaryRepository = JdbiRepository(mainJdbi, BeneficiaryTable::class)
    private val memberRepository = JdbiRepository(mainJdbi, MemberTable::class)
    private val legalGuardianAssociationRepository =
        JdbiRepository(mainJdbi, LegalGuardianAssociationTable::class)

    private val personTokenService = PersonTokenServiceImpl(tokenJdbi) as PersonTokenService
    private val authorizationService: AuthorizationService = mockk(relaxUnitFun = true)
    private val tokenVerifier: TokenVerifier = mockk(relaxUnitFun = true)
    private val contextService: ContextService = mockk(relaxUnitFun = true)
    private val replicationLagService: ReplicationLagService = mockk(relaxUnitFun = true)

    private val personConverter = ConverterExtension.converter<PersonModel, PersonTable>(personTokenService)
    private val memberConverter = ConverterExtension.converter<MemberModel, MemberTable>(personTokenService)

    private val factory = DatabasePipelineFactory(
        mainJdbi,
        mainJdbi,
        authorizationService,
        tokenVerifier,
        personTokenService,
        contextService,
        replicationLagService,
        true
    )

    private val personId = PersonId()
    private val person = DataLayerTestModelFactory.buildPerson(personId = personId)
    private val subject = PersonSubject(id = person.id, person = person)
    private val builder = PersonSubjectBuilder(factory, subject)

    @AfterTest
    fun clean() {
        personRepository.truncate()
        beneficiaryRepository.truncate()
        memberRepository.truncate()
        legalGuardianAssociationRepository.truncate()
    }

    @Test
    fun `#build should return same subject when resource is not PersonModel, PersonReference, nor MemberReference`() =
        runBlocking<Unit> {
            val resource = TestModelFactory.buildCompany()
            val actual = builder.build(resource)

            assertThat(actual).isEqualTo(subject)
        }

    @Test
    fun `#build should return same subject when has no Beneficiary`() = runBlocking<Unit> {
        val personToken = personTokenService.createForPersonId(personId).get()
        val personTable = TestTableFactory.buildPersonTable(personToken.personPiiToken)
        personRepository.add(personTable)

        val resource = DataLayerTestModelFactory.buildPerson()
        val actual = builder.build(resource)

        assertThat(actual).isEqualTo(subject)
    }

    @Test
    fun `#build should return same subject when has Beneficiary but no dependents`() = runBlocking<Unit> {
        val personToken = personTokenService.createForPersonId(personId).get()
        val personTable = TestTableFactory.buildPersonTable(personToken.personPiiToken)
        personRepository.add(personTable)

        val beneficiaryTable = TestTableFactory.buildBeneficiaryTable(personId = personToken.personPiiToken)
        beneficiaryRepository.add(beneficiaryTable)

        val resource = DataLayerTestModelFactory.buildPerson()
        val actual = builder.build(resource)

        assertThat(actual).isEqualTo(subject)
    }

    @Test
    fun `#build should return same subject when has Beneficiary and parent PersonModel`() = runBlocking<Unit> {
        val parentPersonToken = personTokenService.createForPersonId(PersonId()).get()
        val parentPersonTable = TestTableFactory.buildPersonTable(
            parentPersonToken.personPiiToken,
            nationalId = "733.905.120-12"
        )
        val parentPersonTableResult = personRepository.add(parentPersonTable).get()

        val personToken = personTokenService.createForPersonId(personId).get()
        val personTable = TestTableFactory.buildPersonTable(personToken.personPiiToken)
        personRepository.add(personTable)

        val memberTable = TestTableFactory.buildMemberTable(
            personId = personToken.personPiiToken,
            parentPerson = parentPersonToken.personPiiToken
        ).copy(status = MemberStatus.ACTIVE)
        val memberTableResult = memberRepository.add(memberTable).get()
        val member = memberConverter.unconvert(memberTableResult)

        val beneficiaryTable = TestTableFactory.buildBeneficiaryTable(
            personId = personToken.personPiiToken,
            memberId = member.id,
            parentPerson = parentPersonToken.personPiiToken
        )
        beneficiaryRepository.add(beneficiaryTable)

        val resource = DataLayerTestModelFactory.buildPerson()
        val actual = builder.build(resource)

        val parentPerson = personConverter.unconvert(parentPersonTableResult)
        val enrichedSubject = subject.copy(parentPerson = parentPerson)
        assertThat(actual)
            .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
            .usingRecursiveComparison()
            .isEqualTo(enrichedSubject)
    }

    @Test
    fun `#build should return enriched subject when has Beneficiary and dependents`() = runBlocking<Unit> {
        val personToken = personTokenService.createForPersonId(personId).get()
        val personTable = TestTableFactory.buildPersonTable(
            personToken.personPiiToken,
            nationalId = "113.750.950-30",
            email = "<EMAIL>"
        )
        personRepository.add(personTable)

        val memberTable = TestTableFactory.buildMemberTable(personId = personToken.personPiiToken)
            .copy(status = MemberStatus.ACTIVE)
        val memberTableResult = memberRepository.add(memberTable).get()
        val member = memberConverter.unconvert(memberTableResult)

        val beneficiaryTable = TestTableFactory.buildBeneficiaryTable(
            personId = personToken.personPiiToken,
            memberId = member.id
        )
        beneficiaryRepository.add(beneficiaryTable)

        val dependentPersonId = PersonId()
        val dependentPersonToken = personTokenService.createForPersonId(dependentPersonId).get()
        val dependentPersonTable = TestTableFactory.buildPersonTable(
            dependentPersonToken.personPiiToken,
            nationalId = "360.765.957-57",
            email = "<EMAIL>"
        )
        val dependentPersonTableResult = personRepository.add(dependentPersonTable).get()

        val dependentMemberTable = TestTableFactory.buildMemberTable(personId = dependentPersonToken.personPiiToken)
        val dependentMemberTableResult = memberRepository.add(dependentMemberTable).get()
        val dependentMember = memberConverter.unconvert(dependentMemberTableResult)

        val dependentBeneficiaryTable = TestTableFactory.buildBeneficiaryTable(
            personId = dependentPersonToken.personPiiToken,
            parentBeneficiary = beneficiaryTable.id,
            memberId = dependentMember.id
        )
        beneficiaryRepository.add(dependentBeneficiaryTable)

        val dependentPerson = personConverter.unconvert(dependentPersonTableResult)

        val resource = DataLayerTestModelFactory.buildPerson()
        val actual = builder.build(resource)

        val enrichedSubject = subject.copy(dependentPersons = listOf(dependentPerson))
        assertThat(actual)
            .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
            .usingRecursiveComparison()
            .isEqualTo(enrichedSubject)
    }

    @Test
    fun `#build should return enriched subject when has Beneficiary, dependents and parent PersonModel`() = runBlocking<Unit> {
        val parentPersonToken = personTokenService.createForPersonId(PersonId()).get()
        val parentPersonTable = TestTableFactory.buildPersonTable(
            parentPersonToken.personPiiToken,
            nationalId = "733.905.120-12"
        )
        val parentPersonTableResult = personRepository.add(parentPersonTable).get()

        val personToken = personTokenService.createForPersonId(personId).get()
        val personTable = TestTableFactory.buildPersonTable(
            personToken.personPiiToken,
            nationalId = "113.750.950-30",
            email = "<EMAIL>"
        )
        personRepository.add(personTable)

        val memberTable = TestTableFactory.buildMemberTable(
            personId = personToken.personPiiToken,
            parentPerson = parentPersonToken.personPiiToken
        ).copy(status = MemberStatus.ACTIVE)
        val memberTableResult = memberRepository.add(memberTable).get()
        val member = memberConverter.unconvert(memberTableResult)

        val beneficiaryTable = TestTableFactory.buildBeneficiaryTable(
            personId = personToken.personPiiToken,
            memberId = member.id,
            parentPerson = parentPersonToken.personPiiToken
        )
        beneficiaryRepository.add(beneficiaryTable)

        val dependentPersonId = PersonId()
        val dependentPersonToken = personTokenService.createForPersonId(dependentPersonId).get()
        val dependentPersonTable = TestTableFactory.buildPersonTable(
            dependentPersonToken.personPiiToken,
            nationalId = "360.765.957-57",
            email = "<EMAIL>"
        )
        val dependentPersonTableResult = personRepository.add(dependentPersonTable).get()

        val dependentMemberTable = TestTableFactory.buildMemberTable(personId = dependentPersonToken.personPiiToken)
        val dependentMemberTableResult = memberRepository.add(dependentMemberTable).get()
        val dependentMember = memberConverter.unconvert(dependentMemberTableResult)

        val dependentBeneficiaryTable = TestTableFactory.buildBeneficiaryTable(
            personId = dependentPersonToken.personPiiToken,
            parentBeneficiary = beneficiaryTable.id,
            memberId = dependentMember.id
        )
        beneficiaryRepository.add(dependentBeneficiaryTable)

        val dependentPerson = personConverter.unconvert(dependentPersonTableResult)

        val resource = DataLayerTestModelFactory.buildPerson()
        val actual = builder.build(resource)

        val parentPerson = personConverter.unconvert(parentPersonTableResult)
        val enrichedSubject = subject.copy(parentPerson = parentPerson, dependentPersons = listOf(dependentPerson))
        assertThat(actual)
            .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
            .usingRecursiveComparison()
            .isEqualTo(enrichedSubject)
    }

    @Test
    fun `#build should return enriched subject when has Beneficiary and dependents related to newest`() =
        runBlocking<Unit> {
            val personToken = personTokenService.createForPersonId(personId).get()
            val personTable = TestTableFactory.buildPersonTable(
                personToken.personPiiToken,
                nationalId = "111.222.333-44",
                email = "<EMAIL>"
            )
            personRepository.add(personTable)

            val memberTable = TestTableFactory.buildMemberTable(personId = personToken.personPiiToken)
                .copy(status = MemberStatus.CANCELED)
            val memberTableResult = memberRepository.add(memberTable).get()
            val member = memberConverter.unconvert(memberTableResult)

            val beneficiaryTable = TestTableFactory.buildBeneficiaryTable(
                personId = personToken.personPiiToken,
                memberId = member.id
            )
            beneficiaryRepository.add(beneficiaryTable)

            val newMemberTable = TestTableFactory.buildMemberTable(personId = personToken.personPiiToken)
                .copy(status = MemberStatus.ACTIVE)
            val newMemberTableResult = memberRepository.add(newMemberTable).get()
            val newMember = memberConverter.unconvert(newMemberTableResult)

            val newBeneficiaryTable = TestTableFactory.buildBeneficiaryTable(
                personId = personToken.personPiiToken,
                memberId = newMember.id
            )
            beneficiaryRepository.add(newBeneficiaryTable)

            val dependentPersonId = PersonId()
            val dependentPersonToken = personTokenService.createForPersonId(dependentPersonId).get()
            val dependentPersonTable = TestTableFactory.buildPersonTable(
                dependentPersonToken.personPiiToken,
                nationalId = "555.666.777-88",
                email = "<EMAIL>"
            )
            val dependentPersonTableResult = personRepository.add(dependentPersonTable).get()

            val dependentMemberTable = TestTableFactory.buildMemberTable(personId = dependentPersonToken.personPiiToken)
            val dependentMemberTableResult = memberRepository.add(dependentMemberTable).get()
            val dependentMember = memberConverter.unconvert(dependentMemberTableResult)

            val dependentBeneficiaryTable = TestTableFactory.buildBeneficiaryTable(
                personId = dependentPersonToken.personPiiToken,
                parentBeneficiary = newBeneficiaryTable.id,
                memberId = dependentMember.id
            )
            beneficiaryRepository.add(dependentBeneficiaryTable)

            val dependentPerson = personConverter.unconvert(dependentPersonTableResult)

            val resource = DataLayerTestModelFactory.buildPerson()
            val actual = builder.build(resource)

            val enrichedSubject = subject.copy(dependentPersons = listOf(dependentPerson))
            assertThat(actual)
                .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
                .usingRecursiveComparison()
                .isEqualTo(enrichedSubject)
        }

    @Test
    fun `#build should return same subject when PersonModel has no MemberModel`() = runBlocking<Unit> {
        val personToken = personTokenService.createForPersonId(personId).get()
        val personTable = TestTableFactory.buildPersonTable(personToken.personPiiToken)
        personRepository.add(personTable)

        val resource = TestModelFactory.buildCassiMember()
        val actual = builder.build(resource)

        assertThat(actual).isEqualTo(subject)
    }

    @Test
    fun `#build should return enriched subject when resource is MemberReference and PersonModel has a MemberModel`() =
        runBlocking<Unit> {
            val personToken = personTokenService.createForPersonId(personId).get()
            val personTable = TestTableFactory.buildPersonTable(personToken.personPiiToken)
            personRepository.add(personTable)

            val memberTable1 = TestTableFactory.buildMemberTable(personId = personToken.personPiiToken)
            val memberTable1Result = memberRepository.add(memberTable1).get()
            val member1 = memberConverter.unconvert(memberTable1Result)

            val memberTable2 = TestTableFactory.buildMemberTable(personId = personToken.personPiiToken)
            val memberTable2Result = memberRepository.add(memberTable2).get()
            val member2 = memberConverter.unconvert(memberTable2Result)

            val resource = TestModelFactory.buildCassiMember(memberId = memberTable2.id)
            val actual = builder.build(resource)

            val members = listOf(member1, member2)
            val enrichedSubject = subject.copy(members = members)

            assertThat(actual)
                .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
                .usingRecursiveComparison()
                .isEqualTo(enrichedSubject)
        }

    @Test
    fun `#build should return enriched subject when resource is LegalGuardianAssociationReference and PersonModel has a LegalGuardianAssociation`() =
        runBlocking<Unit> {
            val personToken = personTokenService.createForPersonId(personId).get()
            val personTable = TestTableFactory.buildPersonTable(
                id = personToken.personPiiToken,
                nationalId = "06576248964"
            )
            personRepository.add(personTable)

            val guardianPersonId = PersonId()
            val guardianToken = personTokenService.createForPersonId(guardianPersonId).get()
            val guardianPersonTable = TestTableFactory.buildPersonTable(
                id = guardianToken.personPiiToken,
                nationalId = "06576248965"
            )
            val guardianPersonTableResult = personRepository.add(guardianPersonTable).get()

            val legalGuardianTable = TestTableFactory.buildLegalGuardianAssociationTable(
                personId = personToken.personPiiToken,
                guardianId = guardianToken.personPiiToken
            )
            legalGuardianAssociationRepository.add(legalGuardianTable).get()

            val resource = FinancialDataModel(
                personId = PersonId(),
                bankCode = "001",
                bankName = null,
                bankAgency = "0001",
                accountNumber = "000001",
                nationalId = "***********",
                accountNickname = null,
                active = true
            )
            val actual = builder.build(resource)

            val legalGuardianPerson = personConverter.unconvert(guardianPersonTableResult)
            val legalGuardians = listOf(legalGuardianPerson)

            val enrichedSubject = subject.copy(legalGuardianPersons = legalGuardians)

            assertThat(actual)
                .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
                .usingRecursiveComparison()
                .isEqualTo(enrichedSubject)
        }

    @Test
    fun `#build enriches subject when Beneficiary and dependents exist and resource is DependentInformation`() = runBlocking<Unit> {
        val parentPersonToken = personTokenService.createForPersonId(PersonId()).get()
        val parentPersonTable = TestTableFactory.buildPersonTable(
            parentPersonToken.personPiiToken,
            nationalId = "733.905.120-12"
        )
        val parentPersonTableResult = personRepository.add(parentPersonTable).get()

        val personToken = personTokenService.createForPersonId(personId).get()
        val personTable = TestTableFactory.buildPersonTable(
            personToken.personPiiToken,
            nationalId = "113.750.950-30",
            email = "<EMAIL>"
        )
        personRepository.add(personTable)

        val memberTable = TestTableFactory.buildMemberTable(
            personId = personToken.personPiiToken,
            parentPerson = parentPersonToken.personPiiToken
        ).copy(status = MemberStatus.ACTIVE)
        val memberTableResult = memberRepository.add(memberTable).get()
        val member = memberConverter.unconvert(memberTableResult)

        val beneficiaryTable = TestTableFactory.buildBeneficiaryTable(
            personId = personToken.personPiiToken,
            memberId = member.id,
            parentPerson = parentPersonToken.personPiiToken
        )
        beneficiaryRepository.add(beneficiaryTable)

        val dependentPersonId = PersonId()
        val dependentPersonToken = personTokenService.createForPersonId(dependentPersonId).get()
        val dependentPersonTable = TestTableFactory.buildPersonTable(
            dependentPersonToken.personPiiToken,
            nationalId = "360.765.957-57",
            email = "<EMAIL>"
        )
        val dependentPersonTableResult = personRepository.add(dependentPersonTable).get()

        val dependentMemberTable = TestTableFactory.buildMemberTable(personId = dependentPersonToken.personPiiToken)
        val dependentMemberTableResult = memberRepository.add(dependentMemberTable).get()
        val dependentMember = memberConverter.unconvert(dependentMemberTableResult)

        val dependentBeneficiaryTable = TestTableFactory.buildBeneficiaryTable(
            personId = dependentPersonToken.personPiiToken,
            parentBeneficiary = beneficiaryTable.id,
            memberId = dependentMember.id
        )
        beneficiaryRepository.add(dependentBeneficiaryTable)

        val dependentPerson = personConverter.unconvert(dependentPersonTableResult)

        val resource = TestModelFactory.buildHealthDeclaration()
        val actual = builder.build(resource)

        val parentPerson = personConverter.unconvert(parentPersonTableResult)
        val enrichedSubject = subject.copy(parentPerson = parentPerson, dependentPersons = listOf(dependentPerson))
        assertThat(actual)
            .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
            .usingRecursiveComparison()
            .isEqualTo(enrichedSubject)
    }

    @Test
    fun `build returns same subject when resource is not DependentInformation`() = runBlocking<Unit> {
        val personToken = personTokenService.createForPersonId(personId).get()
        val personTable = TestTableFactory.buildPersonTable(personToken.personPiiToken)
        personRepository.add(personTable)

        val beneficiaryTable = TestTableFactory.buildBeneficiaryTable(personId = personToken.personPiiToken)
        beneficiaryRepository.add(beneficiaryTable)

        val resource = FinancialDataModel(
            personId = PersonId(),
            bankCode = "001",
            bankName = null,
            bankAgency = "0001",
            accountNumber = "000001",
            nationalId = "***********",
            accountNickname = null,
            active = true
        )
        val actual = builder.build(resource)

        assertThat(actual).isEqualTo(subject).usingRecursiveComparison()
    }

    @Test
    fun `#build should return enriched subject when resource is MemberReference and DependentInfo, person has MemberModel and dependents too`() =
        runBlocking<Unit> {
            val personToken = personTokenService.createForPersonId(personId).get()
            val personTable = TestTableFactory.buildPersonTable(personToken.personPiiToken)
            personRepository.add(personTable)

            val parentMemberTable = TestTableFactory.buildMemberTable(personId = personToken.personPiiToken)
            val parentMemberTableResult = memberRepository.add(parentMemberTable).get()
            val parentMember = memberConverter.unconvert(parentMemberTableResult)

            val beneficiaryTable = TestTableFactory.buildBeneficiaryTable(
                personId = personToken.personPiiToken,
                memberId = parentMember.id
            )
            beneficiaryRepository.add(beneficiaryTable)

            val dependentPersonId = PersonId()
            val dependentPersonToken = personTokenService.createForPersonId(dependentPersonId).get()
            val dependentPersonTable = TestTableFactory.buildPersonTable(
                id = dependentPersonToken.personPiiToken,
                nationalId = "587.205.050-00"
            )
            val dependentPersonTableResult = personRepository.add(dependentPersonTable).get()
            val dependentPerson = personConverter.unconvert(dependentPersonTableResult)

            val dependentMemberTable = TestTableFactory.buildMemberTable(personId = dependentPersonToken.personPiiToken)
            val dependentMemberTableResult = memberRepository.add(dependentMemberTable).get()
            val dependentMember = memberConverter.unconvert(dependentMemberTableResult)

            val dependentBeneficiaryTable = TestTableFactory.buildBeneficiaryTable(
                personId = dependentPersonToken.personPiiToken,
                parentBeneficiary = beneficiaryTable.id,
                memberId = dependentMember.id
            )
            beneficiaryRepository.add(dependentBeneficiaryTable)

            val resource = TestModelFactory.buildMemberContract(memberId = dependentMember.id)
            val actual = builder.build(resource)

            val members = listOf(parentMember)
            val dependentMembers = listOf(dependentMember)
            val enrichedSubject = subject.copy(members = members, dependentPersons = listOf(dependentPerson), dependentMembers = dependentMembers)

            assertThat(actual)
                .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
                .usingRecursiveComparison()
                .isEqualTo(enrichedSubject)
        }

    @Test
    fun `#build should return enriched subject when resource is MemberReference but not DependentInformation`() =
        runBlocking<Unit> {
            val personToken = personTokenService.createForPersonId(personId).get()
            val personTable = TestTableFactory.buildPersonTable(personToken.personPiiToken)
            personRepository.add(personTable)

            val memberTable1 = TestTableFactory.buildMemberTable(personId = personToken.personPiiToken)
            val memberTable1Result = memberRepository.add(memberTable1).get()
            val member1 = memberConverter.unconvert(memberTable1Result)

            val memberTable2 = TestTableFactory.buildMemberTable(personId = personToken.personPiiToken)
            val memberTable2Result = memberRepository.add(memberTable2).get()
            val member2 = memberConverter.unconvert(memberTable2Result)

            val resource = TestModelFactory.buildCassiMember(memberId = memberTable2.id)
            val actual = builder.build(resource)

            val members = listOf(member1, member2)
            val enrichedSubject = subject.copy(members = members)

            assertThat(actual)
                .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
                .usingRecursiveComparison()
                .isEqualTo(enrichedSubject)
        }
}
