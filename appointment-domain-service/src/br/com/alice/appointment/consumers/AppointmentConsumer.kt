package br.com.alice.appointment.consumers

import br.com.alice.appointment.client.AppointmentEvolutionService
import br.com.alice.appointment.client.TimelineFilter
import br.com.alice.appointment.client.TimelineService
import br.com.alice.appointment.converters.toTimeline
import br.com.alice.appointment.event.AppointmentCompletedEvent
import br.com.alice.appointment.event.DraftAppointmentDeletedEvent
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.AppointmentDiscardedType
import br.com.alice.data.layer.models.AppointmentStatus
import br.com.alice.data.layer.models.CaseRecordDetails
import br.com.alice.data.layer.models.PersonCase
import br.com.alice.data.layer.models.Timeline
import br.com.alice.data.layer.models.TimelineAppendage
import br.com.alice.data.layer.models.TimelineAppendagesCategory
import br.com.alice.data.layer.models.TimelineAppendagesType
import br.com.alice.data.layer.models.TimelineReferenceModel.APPOINTMENT
import br.com.alice.healthcondition.client.PersonCaseService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class AppointmentConsumer(
    private val timelineService: TimelineService,
    private val appointmentEvolutionService: AppointmentEvolutionService,
    private val personCaseService: PersonCaseService,
    private val healthProfessionalService: HealthProfessionalService,
) : Consumer() {
    suspend fun consumerAppointmentDeleted(event: DraftAppointmentDeletedEvent) = withSubscribersEnvironment {
        val payload = event.payload

        logger.info(
            "AppointmentConsumer::consumerAppointmentDeleted to timeline",
            "appointment_id" to payload.appointmentId,
            "person_id" to payload.personId,
            "type" to payload.type,
            "description" to payload.description,
            "staff_id" to payload.staffId,
        )

        val appointmentId = payload.appointmentId
        val reasonType = payload.discardedType

        if (reasonType != AppointmentDiscardedType.NO_SHOW) {
            logger.info(
                "AppointmentConsumer::consumerAppointmentDeleted is not no show",
                "reason_type" to reasonType,
                "appointment" to appointmentId
            )
            return@withSubscribersEnvironment false.success()
        }
        val timelineType = AppointmentStatus.DISCARDED.toTimeline(reasonType)

        timelineService.findOneBy(
            TimelineFilter(
                referencedModelIds = listOf(appointmentId),
                referencedModelClasses = listOf(APPOINTMENT)
            )
        ).flatMap { timeline ->
            timelineService.updatedAndNotify(
                timeline.copy(
                    status = timelineType,
                    description = payload.description,
                )
            )
        }.coFoldNotFound {
            val timeline = Timeline(
                personId = payload.personId,
                type = payload.type.toTimeline(),
                title = payload.type.description,
                status = AppointmentStatus.DISCARDED.toTimeline(reasonType),
                referencedModelId = appointmentId,
                referencedModelClass = APPOINTMENT,
                referencedModelDate = payload.createdAt,
                staffId = payload.staffId,
                description = payload.description,
                specialtyId = getHealthProfessionalSpecialtyId(payload.staffId, payload.appointmentId)
            )
            timelineService.addAndNotify(timeline)
        }

    }

    suspend fun consumerAppointmentCompleted(event: AppointmentCompletedEvent) = withSubscribersEnvironment {
        val appointment = event.payload.appointment
        logAppointmentEvent("consumerAppointmentCompleted to timeline", appointment)
        val evolutions = appointmentEvolutionService.getByAppointment(appointment.id).get().map { it.toTimeline() }

        timelineService.findOneBy(
            TimelineFilter(
                referencedModelIds = listOf(appointment.id),
                referencedModelClasses = listOf(APPOINTMENT)
            )
        ).flatMap { timeline ->
            logTimelineEvent("consumerAppointmentCompleted appointment to timeline", timeline)
            timelineService.updatedAndNotify(
                timeline.copy(
                    status = appointment.status.toTimeline(appointment.discardedType),
                    description = appointment.content,
                    evolutions = evolutions,
                    draftGroup = appointment.draftGroupStaffIds.orEmpty()
                ).let {
                    getTimelineToSave(it, appointment)
                }
            )
        }.coFoldNotFound {
            appointment.toTimeline(getHealthProfessionalSpecialtyId(appointment.staffId, appointment.id)).copy(evolutions = evolutions).let { timeline ->
                logTimelineEvent("consumerAppointmentCompleted appointment to timeline", timeline)
                getTimelineToSave(timeline, appointment)
            }.let {
                timelineService.addAndNotify(it)
            }
        }

    }

    private suspend fun getTimelineToSave(timeline: Timeline, appointment: Appointment) =
        appointment.caseRecordDetails?.let { caseRecords -> getTimeline(timeline, caseRecords).get() } ?: timeline

    private suspend fun getTimeline(currentTimeline: Timeline, caseRecordDetails: List<CaseRecordDetails>) =
        currentTimeline.let { timeline ->
            caseRecordDetails.mapNotNull { it.caseId }.let { caseIds ->
                personCaseService.findByIds(caseIds).mapEach { personCase ->
                    getTimelineAppendagesForUpdate(personCase)
                }.map { appendages ->
                    timeline.copy(
                        appendages = timeline.appendages.plus(appendages)
                    )
                }
            }
        }

    private fun getTimelineAppendagesForUpdate(personCase: PersonCase) =
        TimelineAppendage(
            title = "${personCase.codeValue} - ${personCase.codeDescription}",
            id = personCase.id,
            category = TimelineAppendagesCategory.valueOf(personCase.codeType.name),
            type = TimelineAppendagesType.PERSON_CASE
        )

    private fun logTimelineEvent(message: String, timeline: Timeline) {
        logger.info(
            "AppointmentConsumer::$message",
            "timeline_id" to timeline.id,
            "person_id" to timeline.personId,
            "title" to timeline.title,
            "referenced_model_id" to timeline.referencedModelId,
            "referenced_model_class" to timeline.referencedModelClass,
            "type" to timeline.type,
            "description" to timeline.description,
            "staff_id" to timeline.staffId,
        )
    }

    private fun logAppointmentEvent(message: String, appointment: Appointment) {
        logger.info(
            "AppointmentConsumer::$message",
            "appointment_id" to appointment.id,
            "person_id" to appointment.personId,
            "type" to appointment.type,
            "description" to appointment.description,
            "staff_id" to appointment.staffId,
        )
    }

    private suspend fun getHealthProfessionalSpecialtyId(staffId: UUID, appointmentId: UUID) =
        healthProfessionalService.findByStaffId(staffId).fold({ it.specialtyId }, {
            logger.info(
                "AppointmentConsumer::getHealthProfessionalSpecialtyId error getting health professional",
                "appointment_id" to appointmentId,
                "staff_id" to staffId,
            )
            null
        })

}
