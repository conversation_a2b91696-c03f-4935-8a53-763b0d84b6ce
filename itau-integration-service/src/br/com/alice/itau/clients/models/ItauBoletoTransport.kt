package br.com.alice.itau.clients.models

import com.google.gson.annotations.SerializedName

data class ItauBoletoCreateRequest(
    @SerializedName("data")
    val data: ItauBoletoDataCreateRequest,
)

data class ItauBoletoDataCreateRequest(
    @SerializedName("etapa_processo_boleto")
    val processStage: String = "efetivacao",
    @SerializedName("beneficiario")
    val beneficiary: ItauBoletoAliceBankAccountCreateRequest,
    @SerializedName("dado_boleto")
    val boletoData: ItauBoletoBoletoDataCreateRequest,
)

data class ItauBoletoAliceBankAccountCreateRequest(
    @SerializedName("id_beneficiario")
    val aliceBankAccount: String,
)

data class ItauBoletoBoletoDataCreateRequest(
    @SerializedName("descricao_instrumento_cobranca")
    val descriptionCharge: String = ItauBoletoDescriptionCharge.BOLETO.code,
    @SerializedName("tipo_boleto")
    val type: String = "a vista",
    @SerializedName("desconto_expresso")
    val discountExpress: Boolean = false,
    @SerializedName("codigo_carteira")
    val walletCode: Int = 109,
    @SerializedName("codigo_especie")
    val speciesCode: String = "08",
    @SerializedName("recebimento_divergente")
    val divergentReceipt: ItauBoletoConfigDivergentReceipt,
    @SerializedName("pagador")
    val payer: ItauBoletoPayerCreateRequest,
    @SerializedName("juros")
    val interest: ItauBoletoInterestCreateRequest?,
    @SerializedName("multa")
    val fine: ItauBoletoFineCreateRequest?,
    @SerializedName("dados_individuais_boleto")
    val individualBoleto: List<ItauBoletoIndividualCreateRequest>,
)

data class ItauBoletoConfigDivergentReceipt(
    @SerializedName("codigo_tipo_autorizacao")
    val authorizationType: String = "03",
)

data class ItauBoletoIndividualCreateRequest(
    @SerializedName("numero_nosso_numero")
    val ourNumber: String,
    @SerializedName("data_vencimento")
    val dueDate: String,
    @SerializedName("valor_titulo")
    val titleValue: String,
)

data class ItauBoletoFineCreateRequest(
    @SerializedName("codigo_tipo_multa")
    val fineType: String = "02",
    @SerializedName("percentual_multa")
    val finePercentage: String = "000000200000"
)

data class ItauBoletoInterestCreateRequest(
    @SerializedName("codigo_tipo_juros")
    val interestType: String = "90",
    @SerializedName("percentual_juros")
    val interestPercentage: String = "000000100000"
)

data class ItauBoletoPayerCreateRequest(
    @SerializedName("pessoa")
    val person: ItauBoletoPayerPersonCreateRequest,
)

data class ItauBoletoPayerPersonCreateRequest(
    @SerializedName("nome_pessoa")
    val name: String,
    @SerializedName("tipo_pessoa")
    val personType: ItauBoletoPayerTypeCreateRequest
)

enum class ItauBoletoPayerPersonCode(val code: String) {
    FISICA("F"),
    JURIDICA("J")
}

enum class ItauBoletoDescriptionCharge(val code: String) {
    BOLETO("boleto"),
    BOLECODE("boleto_pix")
}

enum class ItauBoletoStatus {
    @SerializedName("Em Aberto")
    PENDING,

    @SerializedName("Paga")
    PAID,

    @SerializedName("Baixada")
    CANCELED
}

data class ItauBoletoPayerTypeCreateRequest(
    @SerializedName("codigo_tipo_pessoa")
    val personCode: String,
    @SerializedName("numero_cadastro_pessoa_fisica")
    val cpf: String?,
    @SerializedName("numero_cadastro_nacional_pessoa_juridica")
    val cnpj: String?,
)

data class ItauBoletoCreateResponse(
    @SerializedName("data")
    val data: ItauBoletoDataCreateResponse,
)

data class ItauBoletoDataCreateResponse(
    @SerializedName("dado_boleto")
    val boletoData: ItauBoletoDataBoletoCreateResponse
)

data class ItauBoletoDataBoletoCreateResponse(
    @SerializedName("dados_individuais_boleto")
    val individualBoleto: List<ItauBoletoIndividualDataBoletoCreateResponse>
)


data class ItauBoletoIndividualDataBoletoCreateResponse(
    @SerializedName("id_boleto_individual")
    val id: String,
    @SerializedName("numero_linha_digitavel")
    val digitableLine: String,
    @SerializedName("codigo_barras")
    val barcodeData: String,
)

data class ItauBoletoGetResponse(
    @SerializedName("data")
    val data: List<ItauBoletoDataGetResponse>,
)

data class ItauBoletoDataGetResponse(
    @SerializedName("id_boleto")
    val id: String,
    @SerializedName("dado_boleto")
    val boletoData: ItauBoletoDataBoletoGetResponse,
)

data class ItauBoletoDataBoletoGetResponse(
    @SerializedName("dados_individuais_boleto")
    val individualBoleto: List<ItauBoletoDataBoletoIndividualGetResponse>,
    @SerializedName("qrcode_pix")
    val pixData: ItauBoletoDataQrCodeResponse?
)

data class ItauBoletoDataBoletoIndividualGetResponse(
    @SerializedName("situacao_geral_boleto")
    val status: ItauBoletoStatus,
    @SerializedName("codigo_barras")
    val barcodeData: String,
    @SerializedName("numero_linha_digitavel")
    val digitableLine: String,
    @SerializedName("data_vencimento")
    val dueDate: String,
    @SerializedName("valor_titulo")
    val titleValue: String,
)

data class ItauBoletoDataQrCodeResponse(
    @SerializedName("emv")
    val pixCopyAndPaste: String,
)

