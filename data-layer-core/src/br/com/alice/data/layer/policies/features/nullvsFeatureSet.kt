package br.com.alice.data.layer.policies.features

import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.BolepixPaymentDetailModel
import br.com.alice.data.layer.models.BoletoPaymentDetailModel
import br.com.alice.data.layer.models.CancelPaymentOnAcquirerScheduleModel
import br.com.alice.data.layer.models.InvoiceItemModel
import br.com.alice.data.layer.models.InvoiceLiquidationModel
import br.com.alice.data.layer.models.InvoicePaymentModel
import br.com.alice.data.layer.models.ItauPaymentModel
import br.com.alice.data.layer.models.MemberInvoiceGroupModel
import br.com.alice.data.layer.models.MemberInvoiceModel
import br.com.alice.data.layer.models.NullvsIntegrationLogModel
import br.com.alice.data.layer.models.NullvsIntegrationRecordModel
import br.com.alice.data.layer.models.PaymentDetailModel
import br.com.alice.data.layer.models.PixPaymentDetailModel
import br.com.alice.data.layer.models.ResourceSignTokenModel
import br.com.alice.data.layer.models.SimpleCreditCardPaymentDetailModel

val createViewUpdateRecordAndLog = policySet {
    can(View, Create, Update, Count) {
        resources(
            NullvsIntegrationLogModel::class,
            NullvsIntegrationRecordModel::class
        )
    }
}

val nullvsInvoiceSet = policySet {
    can(View, Create, Update, Count) {
        resources(
            MemberInvoiceGroupModel::class,
            InvoiceLiquidationModel::class,
            MemberInvoiceModel::class,
            InvoicePaymentModel::class,
            CancelPaymentOnAcquirerScheduleModel::class,
            ResourceSignTokenModel::class,
            ItauPaymentModel::class,
            PaymentDetailModel::class,
            BoletoPaymentDetailModel::class,
            SimpleCreditCardPaymentDetailModel::class,
            PixPaymentDetailModel::class,
            BolepixPaymentDetailModel::class,
            InvoiceItemModel::class,
        )
    }
}
