package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.FeatureType
import java.time.LocalDateTime
import java.util.UUID

internal data class FeatureConfigTable(
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val namespace: FeatureNamespace,
    val key: String,
    val type: FeatureType,
    val value: String,
    val description: String,
    val active: Boolean,
    val isPublic: Boolean = false
) : Table<FeatureConfigTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
