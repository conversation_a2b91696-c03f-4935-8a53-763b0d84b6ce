plugins {
    kotlin
    application
    id("com.github.johnrengelman.shadow")
    id("com.google.cloud.tools.jib")
    id("org.sonarqube")
}

group = "br.com.alice.app-content-domain-service"
version = aliceAppContentDomainServiceVersion

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    main {
        kotlin.sourceDirs = files("src", "${layout.buildDirectory}/generated/source/kotlin")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:app-content-domain-service")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

tasks {
    shadowJar {
        isZip64 = true
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":common-kafka"))
    implementation(project(":common-redis"))
    implementation(project(":communication"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:person-domain-service-data-package"))
    implementation(project(":data-packages:product-domain-service-data-package"))
	implementation(project(":data-packages:questionnaire-domain-service-data-package"))
    implementation(project(":data-packages:membership-domain-service-data-package"))
	implementation(project(":data-packages:member-onboarding-domain-service-data-package"))
	implementation(project(":data-packages:app-content-domain-service-data-package"))
	implementation(project(":data-packages:action-plan-domain-service-data-package"))
	implementation(project(":data-packages:health-logic-domain-service-data-package"))
	implementation(project(":data-packages:duquesa-domain-service-data-package"))
	implementation(project(":data-packages:exec-indicator-domain-service-data-package"))
    implementation(project(":data-packages:ehr-domain-service-data-package"))
    implementation(project(":data-packages:wanda-domain-service-data-package"))
    implementation(project(":data-packages:refund-domain-service-data-package"))
    implementation(project(":data-packages:screening-domain-service-data-package"))
    implementation(project(":data-packages:fhir-domain-service-data-package"))
    implementation(project(":data-packages:channel-domain-service-data-package"))
    implementation(project(":data-packages:staff-domain-service-data-package"))
    implementation(project(":data-packages:provider-domain-service-data-package"))
    implementation(project(":data-packages:schedule-domain-service-data-package"))
    implementation(project(":data-packages:clinical-account-domain-service-data-package"))
    implementation(project(":feature-config-domain-client"))
    implementation(project(":app-content-domain-client"))
    implementation(project(":person-domain-client"))
    implementation(project(":questionnaire-domain-client"))
    implementation(project(":action-plan-domain-client"))
    implementation(project(":membership-domain-client"))
    implementation(project(":member-onboarding-domain-client"))
    implementation(project(":wanda-domain-client"))
    implementation(project(":product-domain-client"))
    implementation(project(":health-logic-domain-client"))
    implementation(project(":test-result-domain-client"))
    implementation(project(":ehr-domain-client"))
    implementation(project(":duquesa-domain-client"))
    implementation(project(":refund-domain-client"))
    implementation(project(":screening-domain-client"))
    implementation(project(":schedule-domain-client"))
    implementation(project(":provider-domain-client"))
    implementation(project(":sorting-hat-domain-client"))
    implementation(project(":clinical-account-domain-client"))

    ktor2Dependencies()

    testImplementation(project(":data-layer-common-tests"))
    testImplementation(project(":common-tests"))
    test2Dependencies()
}
