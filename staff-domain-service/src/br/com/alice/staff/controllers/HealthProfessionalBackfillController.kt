package br.com.alice.staff.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.models.CouncilType
import br.com.alice.common.notification.NotificationEventAction
import br.com.alice.common.toResponse
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.STAFF_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.HealthProfessionalModel
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.services.HealthProfessionalModelDataService
import br.com.alice.staff.converters.toTransport
import br.com.alice.staff.event.HealthProfessionalChangedEvent
import br.com.alice.staff.event.HealthProfessionalUpdatedEvent
import br.com.alice.staff.models.BackfillResponse
import br.com.alice.staff.models.SetCouncilRequest
import br.com.alice.staff.models.SetSpecialtiesRequest
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.isFailure
import com.github.kittinunf.result.isSuccess
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.opentelemetry.api.trace.Span

class HealthProfessionalBackfillController(
    private val healthProfessionalDataService: HealthProfessionalModelDataService,
    private val kafkaProducerService: KafkaProducerService
) : Controller() {

    suspend fun setSpecialties(request: SetSpecialtiesRequest) = span("setSpecialties") { span ->
        withBackfillEnvironment {
            span.setSpecialtiesRequest(request)

            val result = mutableListOf<Result<HealthProfessional, Throwable>>()
            request.staffIds
                .chunked(20)
                .pmap { chunk ->
                    healthProfessionalDataService
                        .find { where { this.staffId.inList(chunk) } }
                        .map { healthProfessionals ->
                            healthProfessionals
                                .pmap { healthProfessional ->
                                    coResultOf<HealthProfessional, Throwable> {
                                        healthProfessional.copyWithNewSpecialties(request).let {
                                            healthProfessionalDataService.update(it)
                                                .then { updated -> updated.sendEvents() }
                                                .get()
                                        }.toTransport()
                                    }.let { result.add(it) }
                                }
                        }
                }

            BackfillResponse(
                quantitySuccess = result.count { it.isSuccess() },
                quantityFailure = result.count { it.isFailure() }
            ).let { response ->
                span.setBackfillResponse(response)
                response.toResponse()
            }
        }
    }

    suspend fun setCouncilTypeBasedOnSpecialty(request: SetCouncilRequest) =
        span("setCouncilTypeBasedOnSpecialty") { span ->
            withBackfillEnvironment {

                val result = mutableListOf<Result<HealthProfessional, Throwable>>()
                healthProfessionalDataService.find {
                    limit { request.limit }
                        .offset { request.offset }
                        .orderBy { this.createdAt }
                        .sortOrder { desc }
                }.pmapEach { hp ->
                    if (hp.council.type != null) return@pmapEach true.success()

                    coResultOf<HealthProfessional, Throwable> {
                        hp.copyWithCouncilType().let {
                            healthProfessionalDataService.update(it)
                                .then { updated -> updated.sendEvents() }
                                .get()
                        }.toTransport()
                    }.let { result.add(it) }
                }

                BackfillResponse(
                    quantitySuccess = result.count { it.isSuccess() },
                    quantityFailure = result.count { it.isFailure() }
                ).let { response ->
                    span.setBackfillResponse(response)
                    response.toResponse()
                }
            }
        }

    private fun HealthProfessionalModel.copyWithNewSpecialties(request: SetSpecialtiesRequest) = this.copy(
        specialtyId = request.specialtyId,
        subSpecialtyIds = request.subSpecialtyIds,
        internalSpecialtyId = null,
        internalSubSpecialtyIds = emptyList()
    )

    private fun HealthProfessionalModel.copyWithCouncilType(): HealthProfessionalModel {
        if (this.specialtyId != null && specialtyCouncilMapping.containsKey(this.specialtyId.toString())) {
            val specialtyCouncil = specialtyCouncilMapping[this.specialtyId.toString()]
            val councilType = specialtyCouncil?.councilType

            return this.copy(
                council = this.council.copy(type = councilType)
            )
        }

        return this.copy(
            council = this.council.copy(type = CouncilType.CRM)
        )
    }

    private suspend fun HealthProfessionalModel.sendEvents() {
        val healthProfessionalUpdatedEvent = HealthProfessionalUpdatedEvent(this.id)
        val healthProfessionalChangedEvent =
            HealthProfessionalChangedEvent(this.toTransport(), NotificationEventAction.UPDATED)
        kafkaProducerService.produce(healthProfessionalUpdatedEvent)
        kafkaProducerService.produce(healthProfessionalChangedEvent)
    }

    private fun Span.setSpecialtiesRequest(request: SetSpecialtiesRequest) {
        setAttribute("staff_ids", request.staffIds.joinToString(","))
        setAttribute("specialty_id", request.specialtyId.toString())
        setAttribute("sub_specialty_ids", request.subSpecialtyIds.joinToString(","))
    }

    private fun Span.setBackfillResponse(response: BackfillResponse) {
        setAttribute("quantity_success", response.quantitySuccess.toString())
        setAttribute("quantity_failure", response.quantityFailure.toString())
    }

    private suspend fun withBackfillEnvironment(func: suspend () -> Response) =
        withRootServicePolicy(STAFF_DOMAIN_ROOT_SERVICE_NAME) {
            withUnauthenticatedTokenWithKey(STAFF_DOMAIN_ROOT_SERVICE_NAME) {
                func.invoke()
            }
        }

    private val specialtyCouncilMapping = mapOf(
        "8605b051-ebdb-4b74-85c9-a030b5a98100" to SpecialtyCouncil("Acupuntura", CouncilType.CRM),
        "3bc096ea-fc85-496a-9abf-455bc835ef00" to SpecialtyCouncil("Agendamento", CouncilType.CRM),
        "78d6214e-5a9b-44b4-8d84-5a3295add100" to SpecialtyCouncil("Alergologia", CouncilType.CRM),
        "03322cd5-d7e7-4226-9571-91bd3bf4d100" to SpecialtyCouncil("Anestesiologia", CouncilType.CRM),
        "6b2cae7f-3691-431d-a806-0258a7a18f00" to SpecialtyCouncil("Assistente Social", CouncilType.CRESS),
        "e372330f-d777-4606-a83f-aa67a8e58e00" to SpecialtyCouncil("Cardiologia", CouncilType.CRM),
        "3ceefc35-d056-4a0e-916b-e04910aac700" to SpecialtyCouncil(
            "Cardiologia Pediátrica",
            CouncilType.CRM
        ),
        "0ca9baf7-ef25-4bdc-b08e-1fc0563e4600" to SpecialtyCouncil("Cirurgia Buco-maxilo", CouncilType.CRO),
        "d8e3c4a7-7ba3-4a08-9dea-38eae13f5800" to SpecialtyCouncil(
            "Cirurgia Cabeça e Pescoço",
            CouncilType.CRM
        ),
        "6b7cbde7-37fb-4d8b-bc28-be5e261d8b00" to SpecialtyCouncil(
            "Cirurgia Cardiovascular",
            CouncilType.CRM
        ),
        "44d302d7-90f8-40d2-8b91-97f190cdc800" to SpecialtyCouncil(
            "Cirurgia Geral (Aparelho Digestivo)",
            CouncilType.CRM
        ),
        "f7536dc2-39fd-494e-9b99-eeae85040000" to SpecialtyCouncil("Cirurgia Pediátrica", CouncilType.CRM),
        "54ea5289-583d-4e1e-b446-3386d0880b00" to SpecialtyCouncil("Cirurgia Plástica", CouncilType.CRM),
        "ea917faf-9fda-441c-a524-ba0564bf6200" to SpecialtyCouncil("Cirurgia Torácica", CouncilType.CRM),
        "24a80d81-4025-44b5-8712-5ebe57e8c600" to SpecialtyCouncil("Cirurgia Vascular", CouncilType.CRM),
        "71f143ad-62d4-4879-bf87-672614f59500" to SpecialtyCouncil(
            "Clínica Multidisciplinar",
            CouncilType.OUT
        ),
        "210a8b7c-15d8-4b22-b7b1-a0615ec89900" to SpecialtyCouncil("Cuidados de Suporte", CouncilType.CRM),
        "c640a2b7-1540-42d1-94d0-ae7647eb7e00" to SpecialtyCouncil("Dermatologia", CouncilType.CRM),
        "3639a622-7b97-4ac8-af54-35a119c5a400" to SpecialtyCouncil(
            "Encontros de Saúde Mental",
            CouncilType.CRP
        ),
        "d8eb14ff-b79c-46b2-b770-0a2fb0b5d900" to SpecialtyCouncil(
            "Endocrinologia e Metabologia",
            CouncilType.CRM
        ),
        "443d8386-af04-467d-9403-e231d3479600" to SpecialtyCouncil(
            "Endocrinologia Pediátrica",
            CouncilType.CRM
        ),
        "24294b3e-949a-4395-902f-4437cb84c400" to SpecialtyCouncil(
            "Enfermagem de Família",
            CouncilType.COREN
        ),
        "8bdda9e8-1258-42b4-b5eb-b4134ac02100" to SpecialtyCouncil("Fisiatria", CouncilType.CRM),
        "5aa155a2-d654-4e1b-b842-4a380dbb6300" to SpecialtyCouncil("Fisioterapia", CouncilType.CREFITO),
        "192f250a-e6b4-4294-bca1-0249178eb500" to SpecialtyCouncil("Fonoaudiologia", CouncilType.CREFONO),
        "b4b507df-4d25-4a45-9314-3f70a226d600" to SpecialtyCouncil(
            "Gastroenterologia clínica",
            CouncilType.CRM
        ),
        "2508d7e4-9e44-4403-9564-a6dcd5e85d00" to SpecialtyCouncil(
            "Gastroenterologia Pediátrica",
            CouncilType.CRM
        ),
        "71709aa7-0a0a-493a-aa6c-6391803aaf00" to SpecialtyCouncil("Genética", CouncilType.CRM),
        "2ffe1b15-7fb5-4fc5-9b46-41e1d451a300" to SpecialtyCouncil("Geriatria", CouncilType.CRM),
        "cc0e16ca-cddf-4bb6-8138-9afdf31dbb00" to SpecialtyCouncil(
            "Ginecologia e Obstetrícia",
            CouncilType.CRM
        ),
        "bb2d8aa3-9276-438f-b0d5-4e1fb2d19700" to SpecialtyCouncil("Hematologia", CouncilType.CRM),
        "46ac2208-1aa1-4732-bea6-e49b8d5d5b00" to SpecialtyCouncil(
            "Hematologia e Hemoterapia Pediátrica",
            CouncilType.CRM
        ),
        "a6a80ddf-c7b8-4cb7-bc88-b3bf8e027300" to SpecialtyCouncil("Infectologia", CouncilType.CRM),
        "12e2da05-e342-4da1-9deb-9ce6f3a19400" to SpecialtyCouncil(
            "Infectologia Pediátrica",
            CouncilType.CRM
        ),
        "5e4a5238-8c7c-4246-8585-22b25d398600" to SpecialtyCouncil(
            "Medicina de Família e Comunidade",
            CouncilType.CRM
        ),
        "a2eda372-59c4-4ac4-acfe-6bd2b9455f00" to SpecialtyCouncil("Medicina do Esporte", CouncilType.CRM),
        "821ef7ac-e6e2-4771-aaf7-dcd7777c5400" to SpecialtyCouncil("Nefrologia", CouncilType.CRM),
        "7d2b5214-258f-4477-a71b-64486dc28900" to SpecialtyCouncil(
            "Nefrologia Pediátrica",
            CouncilType.CRM
        ),
        "780530ef-bf3d-4d22-88e0-06c8681b8900" to SpecialtyCouncil("Neurocirurgia", CouncilType.CRM),
        "58636df5-e6a6-4198-b703-6fb2247f7100" to SpecialtyCouncil("Neurologia", CouncilType.CRM),
        "1f7f1550-370c-49ea-a389-97f9942aae00" to SpecialtyCouncil(
            "Neurologia Pediátrica",
            CouncilType.CRM
        ),
        "c79e7ee4-2b76-4214-9181-d62171476a00" to SpecialtyCouncil("Nutrição", CouncilType.CRN),
        "df0022ec-0483-4bff-847a-aa2f216a8000" to SpecialtyCouncil("Nutrologia", CouncilType.CRM),
        "6845d98f-aa19-468a-8c44-a83273808000" to SpecialtyCouncil("Obstetriz", CouncilType.COREN),
        "c7f26335-61b7-4a56-acfe-97529ac6b300" to SpecialtyCouncil("Oftalmologia", CouncilType.CRM),
        "07bdde53-5860-4761-83f7-b35d08dfd500" to SpecialtyCouncil(
            "Onco Hematologia Pediátrica",
            CouncilType.CRM
        ),
        "4319ca83-e727-4656-a967-0aa894e5eb00" to SpecialtyCouncil("Oncologia", CouncilType.CRM),
        "a41fdbb1-d266-4d24-a909-9d8bd28c7700" to SpecialtyCouncil(
            "Ortopedia e Traumatologia",
            CouncilType.CRM
        ),
        "423de6d3-7bc4-4396-ac70-174695aa1500" to SpecialtyCouncil("Ortopedia Pediátrica", CouncilType.CRM),
        "567bd790-c414-4ef0-baea-ffa02a2c5100" to SpecialtyCouncil("Otorrinolaringologia", CouncilType.CRM),
        "9e323fad-7f24-4668-a302-6f7d71e20c00" to SpecialtyCouncil("Pediatria Geral", CouncilType.CRM),
        "20e92589-ad8e-4c3e-8503-6b5e23487d00" to SpecialtyCouncil("Pneumologia", CouncilType.CRM),
        "f960c694-f036-4aae-b8af-20e05f122b00" to SpecialtyCouncil(
            "Pneumologia Pediátrica",
            CouncilType.CRM
        ),
        "5234bd82-cfcd-404c-ad96-a380285f6300" to SpecialtyCouncil(
            "Psicologia Cognitivo Comportamental",
            CouncilType.CRP
        ),
        "44fb4c1b-ef17-4770-ac2d-52b902e32300" to SpecialtyCouncil("Psicologia TEA", CouncilType.CRP),
        "6a6e4298-2001-4966-937c-d2aef2dac100" to SpecialtyCouncil("Psiquiatria", CouncilType.CRM),
        "668094d2-5400-4b32-97a6-c0d6be300200" to SpecialtyCouncil(
            "Radiologia Intervencionista",
            CouncilType.CRM
        ),
        "3390ce53-5b14-4c17-a54e-c55f92589800" to SpecialtyCouncil("Retaguarda Clínica", CouncilType.CRM),
        "ceeca1f4-d77b-4c18-abe0-e2d8e690f100" to SpecialtyCouncil(
            "Retaguarda Pediátrica",
            CouncilType.CRM
        ),
        "8b0f2e62-21e7-4b7a-98a1-25163f76fc00" to SpecialtyCouncil("Reumatologia", CouncilType.CRM),
        "135ceb82-f6a8-4dfe-b02b-924edf863f00" to SpecialtyCouncil(
            "Reumatologia Pediátrica",
            CouncilType.CRM
        ),
        "d5b2eeb5-e3b9-4dbc-a0b3-e6810d80a200" to SpecialtyCouncil(
            "Terapia Ocupacional",
            CouncilType.CREFITO
        ),
        "684ccfe2-0752-4bce-a441-2c44b845e700" to SpecialtyCouncil("Urologia", CouncilType.CRM),
    )

    data class SpecialtyCouncil(val name: String, val councilType: CouncilType)
}
