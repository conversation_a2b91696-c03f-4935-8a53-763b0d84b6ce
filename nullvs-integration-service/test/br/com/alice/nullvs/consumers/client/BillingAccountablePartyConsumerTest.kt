package br.com.alice.nullvs.consumers.client

import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.common.kafka.internals.LocalProducer
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.NullvsIntegrationLog
import br.com.alice.data.layer.models.NullvsIntegrationRecord
import br.com.alice.data.layer.models.Person
import br.com.alice.moneyin.event.BillingAccountablePartyAssignedEvent
import br.com.alice.moneyin.event.BillingAccountablePartyCreatedEvent
import br.com.alice.moneyin.event.BillingAccountablePartyUpdatedEvent
import br.com.alice.nullvs.client.exceptions.TotvsClientGetException
import br.com.alice.nullvs.clients.TotvsClientClient
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.consumers.ConsumerTest
import br.com.alice.nullvs.converters.NullvsClientConverterService
import br.com.alice.nullvs.events.NullvsClientActivatedEvent
import br.com.alice.nullvs.events.NullvsClientBatchRequestEvent
import br.com.alice.nullvs.events.NullvsMemberBatchRequestEvent
import br.com.alice.nullvs.exceptions.TotvsClientNotFoundByInternalIdException
import br.com.alice.nullvs.exceptions.TotvsMemberNotFoundByInternalIdException
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.TotvsActionClient
import br.com.alice.nullvs.models.TotvsCityCode
import br.com.alice.nullvs.models.TotvsClientGetWebServiceResponse
import br.com.alice.nullvs.models.TotvsGroupCompany
import br.com.alice.nullvs.models.TotvsRelationType
import br.com.alice.nullvs.models.TotvsUser
import br.com.alice.nullvs.models.client.NullvsClientBatchRequest
import br.com.alice.nullvs.models.client.TotvsClientRequest
import br.com.alice.nullvs.models.member.GracePeriodType
import br.com.alice.nullvs.models.member.NullvsMemberBatchRequest
import br.com.alice.nullvs.models.member.TotvsMemberRequest
import br.com.alice.nullvs.services.internals.NullvsIntegrationRecordService
import br.com.alice.nullvs.services.internals.TotvsMemberIntegrationService
import br.com.alice.nullvs.services.internals.dependency.NullvsDependencyService
import br.com.alice.person.client.MemberService
import br.com.alice.person.model.events.PersonUpdatedEvent
import com.github.kittinunf.result.failure
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class BillingAccountablePartyConsumerTest : ConsumerTest() {
    private val nullvsClientConverterService: NullvsClientConverterService = mockk()
    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService = mockk()
    private val totvsMemberIntegrationService: TotvsMemberIntegrationService = mockk()
    private val memberService: MemberService = mockk()
    private val totvsClientClient: TotvsClientClient = mockk()
    private val nullvsDependencyService: NullvsDependencyService = mockk()

    private val consumer =
        BillingAccountablePartyConsumer(
            nullvsClientConverterService,
            nullvsIntegrationRecordService,
            totvsMemberIntegrationService,
            memberService,
            LocalProducer,
            totvsClientClient,
            nullvsDependencyService,
        )

    private val meta = Meta(
        eventId = RangeUUID.generate(),
        eventName = "event01",
        internalId = RangeUUID.generate(),
        internalModelName = InternalModelType.BILLING_ACCOUNTABLE_PARTY,
        integrationEventName = "integration01",
        externalId = null,
        externalModelName = ExternalModelType.CLIENT,
        integratedAt = LocalDateTime.now().minusDays(1),
        originalTopic = "original01",
    )

    private val billingAccountableParty = TestModelFactory.buildBillingAccountableParty(
        type = BillingAccountablePartyType.NATURAL_PERSON
    )

    private val totvsClientRequest = TotvsClientRequest(
        branch = "ABC",
        type = "customer",
        name = "Jose Cardoso",
        legalName = "Jose Cardoso Corp.",
        address = "123 Alameda",
        totvType = "type1",
        nationalId = "*********",
        addressState = "SP",
        addressCityCode = "12345",
        addressNeighborhood = "Perdizes",
        addressPostalCode = "12345",
        phoneNumberDDD = "555",
        phoneNumber = "************",
        birthDate = "14/09/1989",
        email = "<EMAIL>",
        identityDocument = "**********",
        isBlocked = "false",
        billingAccountablePartyIdSoc = "7890",
        product = "product1",
        contract = "contract1",
        blockDate = null,
        beneficiaryOrPerson = "person"
    )

    private fun buildNullvsMemberBatchRequest(person: Person, member: Member, meta: Meta) = NullvsMemberBatchRequest(
        meta,
        NullvsActionType.UPDATE,
        TotvsMemberRequest(
            company = TotvsGroupCompany.ALICE_INDIVIDUAL,
            client = "billing-accountable-party",
            createdAt = LocalDateTime.now(),
            ANSProductId = "ans-product-id",
            idPayload = 1,
            beneficiaries = listOf(
                TotvsMemberRequest.TotvsBeneficiary(
                    userType = TotvsUser.HOLDER,
                    email = person.email,
                    fullName = person.fullRegisterName,
                    fullSocialName = person.fullSocialName,
                    aliceId = member.id,
                    nationalId = person.nationalId.onlyNumbers(),
                    identityDocument = person.identityDocument,
                    mothersName = person.mothersName!!,
                    addressPostalCode = person.mainAddress!!.postalCode!!.onlyDigits(),
                    addressStreet = person.mainAddress!!.street,
                    addressNumber = person.mainAddress!!.number,
                    addressComplement = person.mainAddress!!.complement,
                    addressNeighborhood = person.mainAddress!!.neighbourhood!!,
                    addressCity = TotvsCityCode("3550308", "SAO PAULO", "SP"),
                    addressState = person.mainAddress!!.state.toString(),
                    phoneNumber = person.phoneNumber!!,
                    dateOfBirth = person.dateOfBirth!!,
                    createdAt = person.createdAt,
                    parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                    sex = Sex.FEMALE,
                    cnsNumber = person.cnsNumber,
                    ccoCodeANS = null,
                    ANSProductId = "ans-id",
                    canceledReason = null,
                    canceledAt = null,
                    cpts = listOf(
                        TotvsMemberRequest.TotvsBeneficiary.CPT(
                            cid = "XPTO",
                            startedAt = LocalDate.of(2023, 6, 1),
                            periodInDays = 730L
                        )
                    ),
                    gracePeriods = listOf(
                        TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                            type = GracePeriodType.BIRTH,
                            startedAt = LocalDate.of(2023, 6, 1),
                            periodInDays = 300L
                        )
                    ),
                ),
            ),
        ),
    )

    @BeforeTest
    fun setup() {
        LocalProducer.clearMessages()
    }

    @Nested
    inner class SyncBillingAccountablePartyFromCreate {
        private val nullvsClientBatchRequest =
            NullvsClientBatchRequest(meta, TotvsActionClient.INSERT, totvsClientRequest)

        @Test
        fun `should generate NullvsClientBatchRequestEvent when BillingAccountablePartyCreatedEvent is consumed by Nullvs`(): Unit =
            runBlocking {
                val event = BillingAccountablePartyCreatedEvent(billingAccountableParty)

                coEvery {
                    nullvsClientConverterService.generateCreationRequest(any(), any())
                } returns nullvsClientBatchRequest

                val result = consumer.syncBillingAccountablePartyFromCreate(event)

                ResultAssert.assertThat(result).isSuccessWithData(true)
                Assertions.assertThat(LocalProducer.hasEvent(NullvsClientBatchRequestEvent.name)).isTrue
            }

    }

    @Nested
    inner class SyncBillingAccountablePartyFromUpdate {

        private val nullvsClientBatchRequest =
            NullvsClientBatchRequest(meta, TotvsActionClient.INSERT, totvsClientRequest)

        @Test
        fun `should generate NullvsClientBatchRequestEvent when BillingAccountablePartyUpdatedEvent is consumed by Nullvs`(): Unit =
            runBlocking {
                val event = BillingAccountablePartyUpdatedEvent(billingAccountableParty)
                val integrationRecord = TestModelFactory.buildNullvsIntegrationRecord()

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY
                    )
                } returns integrationRecord
                coEvery {
                    nullvsClientConverterService.generateUpdateRequest(
                        any(),
                        any()
                    )
                } returns nullvsClientBatchRequest

                val result = consumer.syncBillingAccountablePartyFromUpdate(event)

                ResultAssert.assertThat(result).isSuccess()
                Assertions.assertThat(LocalProducer.hasEvent(NullvsClientBatchRequestEvent.name)).isTrue
            }

        @Test
        fun `should generate create event on not find previous record and client in totvs`(): Unit =
            runBlocking {
                val event = BillingAccountablePartyUpdatedEvent(billingAccountableParty)

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        any()
                    )
                } returns NotFoundException().failure()

                coEvery {
                    totvsClientClient.getInfoByBillingAccountablePartyId(billingAccountablePartyId = billingAccountableParty.id)
                } returns TotvsClientGetException(billingAccountableParty.id).failure()

                coEvery {
                    nullvsClientConverterService.generateCreationRequest(
                        any(),
                        any()
                    )
                } returns nullvsClientBatchRequest

                val result = consumer.syncBillingAccountablePartyFromUpdate(event)

                ResultAssert.assertThat(result).isSuccess()
                Assertions.assertThat(LocalProducer.hasEvent(NullvsClientBatchRequestEvent.name)).isTrue()
            }

        @Test
        fun `should generate update event when record is not generate by default (fallback to nullvs record)`(): Unit =
            runBlocking {
                val event = BillingAccountablePartyUpdatedEvent(billingAccountableParty)
                val response = TotvsClientGetWebServiceResponse(
                    internalId = billingAccountableParty.id.toString(),
                    clientCode = "0001",
                    store = "01",
                    cgc = "********",
                    clientName = "Client Name",
                    type = "F"
                )

                val record = NullvsIntegrationRecord(
                    internalId = billingAccountableParty.id,
                    internalModelName = InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    externalId = response.clientCode,
                    externalModelName = ExternalModelType.CLIENT,
                    integratedAt = LocalDateTime.now()
                )

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        any()
                    )
                } returns NotFoundException().failure()

                coEvery {
                    totvsClientClient.getInfoByBillingAccountablePartyId(billingAccountablePartyId = billingAccountableParty.id)
                } returns response

                coEvery {
                    nullvsIntegrationRecordService.add(
                        any()
                    )
                } returns record

                coEvery {
                    nullvsClientConverterService.generateUpdateRequest(
                        any(),
                        any()
                    )
                } returns nullvsClientBatchRequest

                val result = consumer.syncBillingAccountablePartyFromUpdate(event)

                ResultAssert.assertThat(result).isSuccess()
                Assertions.assertThat(LocalProducer.hasEvent(NullvsClientBatchRequestEvent.name)).isTrue()
            }

    }

    @Nested
    inner class UpdateBeneficiaryTotvs {
        private val person = TestModelFactory.buildPerson(
            mothersName = "Mother's name",
            phoneNumber = "phone",
            dateOfBirth = LocalDateTime.of(1994, 10, 17, 14, 0),
        )
        private val member = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.ACTIVE)
        private val personBillingAccountableParty = TestModelFactory.buildPersonBillingAccountableParty(
            personId = person.id,
            billingAccountablePartyId = billingAccountableParty.id
        )
        private val event = BillingAccountablePartyAssignedEvent(personBillingAccountableParty)
        private val meta = Meta(
            eventId = event.messageId,
            eventName = event.name,
            internalModelName = InternalModelType.MEMBER,
            internalId = member.id,
            externalModelName = ExternalModelType.BENEFICIARY,
            integratedAt = event.eventDate,
            originalTopic = PersonUpdatedEvent.name,
            integrationEventName = NullvsMemberBatchRequestEvent.name,
        )

        private val requestMemberBatchRequest = buildNullvsMemberBatchRequest(person, member, meta)

        @Test
        fun `#should return an exception when try to get member is wrong`() = runBlocking {
            coEvery { memberService.getCurrent(person.id) } returns Exception("")
            coEvery {
                totvsMemberIntegrationService.updateBeneficiaryTotvs(
                    meta,
                    member
                )
            } returns Exception("")

            val result = consumer.updateBeneficiaryTotvs(event)

            ResultAssert.assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce { memberService.getCurrent(person.id) }
            coVerifyNone { totvsMemberIntegrationService.updateBeneficiaryTotvs(meta, member) }
        }

        @Test
        fun `#should ignore the NotFoundException when the membership is not found`() = runBlocking {
            coEvery { memberService.getCurrent(person.id) } returns NotFoundException()

            val result = consumer.updateBeneficiaryTotvs(event)

            ResultAssert.assertThat(result).isSuccessWithData(false)

            coVerifyOnce { memberService.getCurrent(person.id) }
            coVerifyNone { totvsMemberIntegrationService.updateBeneficiaryTotvs(meta, member) }
        }

        @Test
        fun `#should ignore the TotvsMemberNotFoundByInternalIdException`() = runBlocking {
            coEvery { memberService.getCurrent(person.id) } returns member
            coEvery {
                totvsMemberIntegrationService.updateBeneficiaryTotvs(
                    meta,
                    member
                )
            } returns TotvsMemberNotFoundByInternalIdException(member.id)

            val result = consumer.updateBeneficiaryTotvs(event)

            ResultAssert.assertThat(result).isSuccessWithData(true)

            coVerifyOnce { memberService.getCurrent(person.id) }
            coVerifyOnce { totvsMemberIntegrationService.updateBeneficiaryTotvs(meta, member) }
        }

        @Test
        fun `#should return an AutoRetryableException with TotvsMemberNotFoundByInternalIdException when member id is not itself`() =
            runBlocking {
                val parentMemberId = RangeUUID.generate()
                coEvery { memberService.getCurrent(person.id) } returns member
                coEvery {
                    totvsMemberIntegrationService.updateBeneficiaryTotvs(
                        meta,
                        member
                    )
                } returns TotvsMemberNotFoundByInternalIdException(parentMemberId)

                val result = consumer.updateBeneficiaryTotvs(event)

                ResultAssert.assertThat(result).isFailureOfType(AutoRetryableException::class)
                assert(result.component2()!!.cause is TotvsMemberNotFoundByInternalIdException)

                coVerifyOnce { memberService.getCurrent(person.id) }
                coVerifyOnce { totvsMemberIntegrationService.updateBeneficiaryTotvs(meta, member) }
            }

        @Test
        fun `#should return an AutoRetryableException in case of TotvsClientNotFoundByInternalIdException`() =
            runBlocking {
                val parentMemberId = RangeUUID.generate()
                coEvery { memberService.getCurrent(person.id) } returns member
                coEvery {
                    totvsMemberIntegrationService.updateBeneficiaryTotvs(
                        meta,
                        member
                    )
                } returns TotvsClientNotFoundByInternalIdException(parentMemberId)

                val result = consumer.updateBeneficiaryTotvs(event)

                ResultAssert.assertThat(result).isFailureOfType(AutoRetryableException::class)
                assert(result.component2()!!.cause is TotvsClientNotFoundByInternalIdException)

                coVerifyOnce { memberService.getCurrent(person.id) }
                coVerifyOnce { totvsMemberIntegrationService.updateBeneficiaryTotvs(meta, member) }
            }

        @Test
        fun `#should return an exception when update the beneficiary is wrong`() = runBlocking {
            coEvery { memberService.getCurrent(person.id) } returns member
            coEvery {
                totvsMemberIntegrationService.updateBeneficiaryTotvs(
                    meta,
                    member
                )
            } returns Exception("")

            val result = consumer.updateBeneficiaryTotvs(event)

            ResultAssert.assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce { totvsMemberIntegrationService.updateBeneficiaryTotvs(meta, member) }
        }

        @Test
        fun `#should produce NullvsMemberBatchRequestEvent`() =
            runBlocking {
                coEvery { memberService.getCurrent(person.id) } returns member
                coEvery {
                    totvsMemberIntegrationService.updateBeneficiaryTotvs(
                        meta,
                        member
                    )
                } returns requestMemberBatchRequest

                val result = consumer.updateBeneficiaryTotvs(event)

                ResultAssert.assertThat(result).isSuccessWithData(true)

                Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchRequestEvent.name)).isTrue

                coVerifyOnce { memberService.getCurrent(person.id) }
                coVerifyOnce { totvsMemberIntegrationService.updateBeneficiaryTotvs(meta, member) }
            }

        @Test
        fun `#should ignore event when member status is PENDING`() =
            runBlocking {
                val pendingMember = member.copy(status = MemberStatus.PENDING)
                coEvery { memberService.getCurrent(person.id) } returns pendingMember
                coEvery {
                    totvsMemberIntegrationService.updateBeneficiaryTotvs(
                        meta,
                        pendingMember
                    )
                } returns requestMemberBatchRequest

                val result = consumer.updateBeneficiaryTotvs(event)

                ResultAssert.assertThat(result).isSuccessWithData(false)

                Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchRequestEvent.name)).isFalse

                coVerifyOnce { memberService.getCurrent(person.id) }
                coVerifyNone { totvsMemberIntegrationService.updateBeneficiaryTotvs(any(), any()) }
            }

        @Test
        fun `#should produce NullvsMemberBatchRequestEvent even when the member is DUQUESA when the skip FF is false`() =
            runBlocking {
                withFeatureFlags(
                    FeatureNamespace.NULLVS,
                    mapOf("should_skip_duquesa_member_nullvs" to false)
                ) {
                    val member = member.copy(brand = Brand.DUQUESA)

                    coEvery { memberService.getCurrent(person.id) } returns member
                    coEvery {
                        totvsMemberIntegrationService.updateBeneficiaryTotvs(
                            meta,
                            member
                        )
                    } returns requestMemberBatchRequest

                    val result = consumer.updateBeneficiaryTotvs(event)

                    ResultAssert.assertThat(result).isSuccessWithData(true)

                    Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchRequestEvent.name)).isTrue

                    coVerifyOnce { memberService.getCurrent(person.id) }
                    coVerifyOnce { totvsMemberIntegrationService.updateBeneficiaryTotvs(meta, member) }
                }
            }
    }

    @Nested
    inner class ReprocessClientDependencies {
        @Test
        fun `#should reprocess client dependencies`() = runBlocking {
            val event = NullvsClientActivatedEvent(RangeUUID.generate(), "001100")

            coEvery {
                nullvsDependencyService.reprocessItsDependents(
                    event.payload.billingAccountablePartyId,
                    InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                )
            } returns emptyList()

            val result = consumer.reprocessClientDependencies(event)

            ResultAssert.assertThat(result).isSuccessWithData(emptyList<NullvsIntegrationLog>())

            coVerifyOnce {
                nullvsDependencyService.reprocessItsDependents(
                    event.payload.billingAccountablePartyId,
                    InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                )
            }
        }

        @Test
        fun `#should not reprocess contract dependencies when FF is disabled`() = runBlocking {
            val event = NullvsClientActivatedEvent(RangeUUID.generate(), "001100")

            withFeatureFlag(FeatureNamespace.NULLVS, "use_dependency_service", false) {
                val result = consumer.reprocessClientDependencies(event)

                ResultAssert.assertThat(result).isSuccessWithData(true)

                coVerifyNone {
                    nullvsDependencyService.reprocessItsDependents(
                        any(),
                        any(),
                    )
                }
            }
        }
    }
}
