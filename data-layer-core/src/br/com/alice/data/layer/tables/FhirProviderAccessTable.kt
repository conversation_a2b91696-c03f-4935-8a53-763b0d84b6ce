package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.ProviderIntegration
import java.time.LocalDateTime
import java.util.UUID

internal data class FhirProviderAccessTable(
    val provider: ProviderIntegration,
    val clientId: UUID,
    val clientSecret: UUID,
    val active: Boolean,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int,
    override val createdAt: LocalDateTime,
    override val updatedAt: LocalDateTime,
) : Table<FhirProviderAccessTable> {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
