package br.com.alice.amas.consumers

import br.com.alice.amas.services.internal.HealthProfessionalTierHistoryService
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.notification.NotificationEventAction
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.staff.event.HealthProfessionalChangedEvent
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class HealthProfessionalChangedConsumerTest: ConsumerTest() {
    private val healthProfessionalTierHistoryService = mockk<HealthProfessionalTierHistoryService>()

    private val consumer = HealthProfessionalChangedConsumer(healthProfessionalTierHistoryService)

    private val hp = TestModelFactory.buildHealthProfessional(
        tier = SpecialistTier.EXPERT,
        theoristTier = SpecialistTier.SUPER_EXPERT
    )

    private val hpth = TestModelFactory.buildHealthProfessionalTierHistory(
        createdAt = hp.updatedAt,
        staffId = hp.staffId,
        tier = hp.tier!!,
        theoristTier = hp.theoristTier!!
    )

    private val event = HealthProfessionalChangedEvent(hp, NotificationEventAction.UPDATED)

    @Test
    fun `#createTierHistory should call tier history service and try to create it`() = runBlocking {
        coEvery {
            healthProfessionalTierHistoryService.createFromHealthProfessionalIfNecessary(hp)
        } returns hpth.success()

        val res = consumer.createTierHistory(event)

        ResultAssert.assertThat(res).isSuccess()

        coVerifyOnce {
            healthProfessionalTierHistoryService.createFromHealthProfessionalIfNecessary(hp)
        }
    }

    @Test
    fun `#createTierHistory should ignora if HealthProfessional is inactive`() = runBlocking {
        val newHp = hp.copy(status = SpecialistStatus.INACTIVE)
        val newEvent = HealthProfessionalChangedEvent(newHp, NotificationEventAction.UPDATED)

        val res = consumer.createTierHistory(newEvent)

        ResultAssert.assertThat(res).isSuccess()

        coVerifyNone {
            healthProfessionalTierHistoryService.createFromHealthProfessionalIfNecessary(any())
        }
    }

    @Test
    fun `#createTierHistory should fail when create tier history fails`() = runBlocking {
        coEvery {
            healthProfessionalTierHistoryService.createFromHealthProfessionalIfNecessary(hp)
        } returns IllegalArgumentException("error").failure()

        val res = consumer.createTierHistory(event)

        ResultAssert.assertThat(res).isFailure()

        coVerifyOnce {
            healthProfessionalTierHistoryService.createFromHealthProfessionalIfNecessary(hp)
        }
    }

}
