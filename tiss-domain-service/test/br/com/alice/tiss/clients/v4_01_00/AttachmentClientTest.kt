package br.com.alice.tiss.clients.v4_01_00

import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.tiss.models.AttachmentHeader
import br.com.alice.tiss.models.OPMERequestAttachment
import br.com.alice.tiss.models.RequestedOPME
import br.com.alice.tiss.totvs.v4_01_00.commom.CtMotivoGlosa
import br.com.alice.tiss.totvs.v4_01_00.commom.DmTipoTransacao
import br.com.alice.tiss.totvs.v4_01_00.commom.ProtocoloRecebimentoAnexoWS
import br.com.alice.tiss.totvs.v4_01_00.guia.TissLoteAnexo
import br.com.alice.tiss.utils.TissTransactionTypeEnum
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.math.BigInteger
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

class AttachmentClientTest {

    private val createAttachmentWebService: TissLoteAnexo = mockk()
    private val client = AttachmentClient(createAttachmentWebService)

    @AfterTest
    fun setup() {
        clearAllMocks()
    }

    private val opmeRequest = OPMERequestAttachment(
        requestedOPMEs = listOf(
            RequestedOPME(
                opmeIdentification = GuiaProcedure(
                    table = "22",
                    code = "123",
                    quantity = 1,
                    description = "CAGE CERVICAL - 03-090145 Cage Cervical 14x5mm"
                ),
                manufacturerOption = "1",
                requestedQuantity = BigInteger.valueOf(2),
                requestedValue = 10.25.toBigDecimal()
            ),
            RequestedOPME(
                opmeIdentification = GuiaProcedure(table = "22", code = "653", quantity = 1, description = ""),
                manufacturerOption = "1",
                requestedQuantity = BigInteger.valueOf(1),
                requestedValue = 2.25.toBigDecimal()
            ),
            RequestedOPME(
                opmeIdentification = GuiaProcedure(table = "22", code = "653", quantity = 1, description = "TESTE"),
                manufacturerOption = "1",
                requestedQuantity = BigInteger.valueOf(1),
                requestedValue = 2.25.toBigDecimal()
            )
        ),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE,
            phoneNumber = "123",
            email = "<EMAIL>"
        ),
        beneficiaryData = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste", externalId = "000120240600000262"),
        origin = GuiaOrigin.EHR,
        attachmentHeader = AttachmentHeader(
            ansCode = "1234",
            attachmentGuideNumber = "************",
            referencedGuideNumber = "123",
            requestDate = LocalDate.now()
        ),
        providerCnpj = "00000000000",
        observation = "Teste",
        materialSpecification = "Teste",
        technicalJustification = "Teste"
    )

    @Test
    fun `#createOpme - should call createAttachmentWebService endpoint correctly`() = runBlocking {

        val expectedResponse = ProtocoloRecebimentoAnexoWS()

        coEvery {
            createAttachmentWebService.tissLoteAnexoPort.tissLoteAnexoOperation(any())
        } returns expectedResponse

        client.createOpme(opmeRequest)

        val transactionType = DmTipoTransacao.valueOf(TissTransactionTypeEnum.ENVIO_ANEXO.toString())
        coVerifyOnce {
            createAttachmentWebService.tissLoteAnexoPort.tissLoteAnexoOperation(
                match {
                    it.cabecalho.identificacaoTransacao.tipoTransacao == transactionType &&
                            it.loteAnexo.anexosGuiasTISS.anexoSolicitacaoOPME.opmeSolicitadas.opmeSolicitada.first().quantidadeSolicitada == opmeRequest.requestedOPMEs[0].requestedQuantity &&
                            it.loteAnexo.anexosGuiasTISS.anexoSolicitacaoOPME.opmeSolicitadas.opmeSolicitada.first().valorSolicitado == opmeRequest.requestedOPMEs[0].requestedValue &&
                            it.loteAnexo.anexosGuiasTISS.anexoSolicitacaoOPME.opmeSolicitadas.opmeSolicitada.first().identificacaoOPME.codigoProcedimento == opmeRequest.requestedOPMEs[0].opmeIdentification.code &&
                            it.loteAnexo.anexosGuiasTISS.anexoSolicitacaoOPME.dadosBeneficiario.numeroCarteira == opmeRequest.beneficiaryData.externalId &&
                            it.loteAnexo.anexosGuiasTISS.anexoSolicitacaoOPME.profissionalSolicitante.nomeProfissional == opmeRequest.professional.name
                }
            )
        }
    }

    @Test
    fun `#createOpme - returns with errors`() = runBlocking {
        val glosa = CtMotivoGlosa()
        glosa.codigoGlosa = "1001"
        glosa.descricaoGlosa = "Error"

        val loteAnexo = ProtocoloRecebimentoAnexoWS.LoteAnexo()
        loteAnexo.mensagemErro = glosa

        val expectedResponse = ProtocoloRecebimentoAnexoWS()
        expectedResponse.loteAnexo = loteAnexo

        coEvery {
            createAttachmentWebService.tissLoteAnexoPort.tissLoteAnexoOperation(any())
        } returns expectedResponse

        client.createOpme(opmeRequest)

        coVerifyOnce { createAttachmentWebService.tissLoteAnexoPort.tissLoteAnexoOperation(any()) }
    }
}
