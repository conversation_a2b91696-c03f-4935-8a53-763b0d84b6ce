package br.com.alice.data.layer.policies.features

import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.*

val createBeneficiaryOnboarding = policySet {
    can(View) {
        resources(CompanyModel::class, CompanyContractModel::class)
    }

    can(View, Create) {
        resources(BeneficiaryOnboardingModel::class, BeneficiaryOnboardingPhaseModel::class)
    }
}

val viewBeneficiaryCompiledView = policySet {
    match("can view", { action is View || action is Count }) {
        match("any BeneficiaryCompiledView") { resourceIs(BeneficiaryCompiledViewModel::class) }
    }
}

val createBeneficiaryCompiledView = policySet {
    match("can view", { action is View || action is Count }) {
        match("any Beneficiary") { resourceIs(BeneficiaryModel::class) }
        match("any MemberModel") { resourceIs(MemberModel::class) }
        match("any PersonModel") { resourceIs(PersonModel::class) }
        match("any PersonClinicalAccount") { resourceIs(PersonClinicalAccount::class) }
        match("any BeneficiaryOnboardingPhase") { resourceIs(BeneficiaryOnboardingPhaseModel::class) }
        match("any BeneficiaryOnboarding") { resourceIs(BeneficiaryOnboardingModel::class) }
        match("any Product") { resourceIs(ProductModel::class) }
        match("any ProductPriceListing") { resourceIs(ProductPriceListingModel::class) }
    }

    match("can view and create", { action is View || action is Count || action is Create || action is Update }) {
        match("any BeneficiaryCompiledView") { resourceIs(BeneficiaryCompiledViewModel::class) }
    }
}

val deleteBeneficiaryCompiledView = policySet {
    match("can view and delete", { action is View || action is Count || action is Delete }) {
        match("any BeneficiaryCompiledView") { resourceIs(BeneficiaryCompiledViewModel::class) }
    }
}

val createMemberFromBeneficiary = policySet {
    match("can view", { action is View || action is Count }) {
        match("any Company") { resourceIs(CompanyModel::class) }
        match("any Product") { resourceIs(ProductModel::class) }
        match("any PriceListing") { resourceIs(PriceListingModel::class) }
        match("any ProductPriceListing") { resourceIs(ProductPriceListingModel::class) }
    }
    match("can view and create", { action is View || action is Count || action is Create || action is Update }) {
        match("any MemberModel") { resourceIs(MemberModel::class) }
        match("any MemberProductPriceModel") { resourceIs(MemberProductPriceModel::class) }
        match("any CassiMember") { resourceIs(CassiMemberModel::class) }
    }
}

val createViewUpdateCassiMember = policySet {
    can(View, Count, Create, Update) {
        resources(CassiMemberModel::class)
    }
}

val createViewBillingAccountableParty = policySet {
    can(View, Create) {
        resources(BillingAccountablePartyModel::class)
    }
}

val createViewUpdateCompanyContractAndSubContract = policySet {
    allows(CompanyContractModel::class, View, Update, Count)
    allows(CompanySubContractModel::class, View, Update, Count)
    allows(CompanyModel::class, View, Update, Count)
}

val deleteCassiMember = policySet {
    can(Delete) { resources(CassiMemberModel::class) }
}

val createPersonLoginFromBeneficiary = policySet {
    match("can view", { action is View }) {
        match("any PersonModel") { resourceIs(PersonModel::class) }
    }
    match("can create", { action is Create }) {
        match("any PersonLoginModel") { resourceIs(PersonLoginModel::class) }
    }
}

val moveToFinishedPhaseFromAppointmentSchedule = policySet {
    match("can view", { action is View }) {
        match("any Beneficiary") { resourceIs(BeneficiaryModel::class) }
    }
    match("can view and create", { action is View || action is Create }) {
        match("any BeneficiaryOnboarding") { resourceIs(BeneficiaryOnboardingModel::class) }
        match("any BeneficiaryOnboardingPhase") { resourceIs(BeneficiaryOnboardingPhaseModel::class) }
    }
}

val moveToSpecificPhase = policySet {
    match("can view", { action is View }) {
        match("any Beneficiary") { resourceIs(BeneficiaryModel::class) }
    }
    match("can view and create", { action is View || action is Create }) {
        match("any BeneficiaryOnboarding") { resourceIs(BeneficiaryOnboardingModel::class) }
        match("any BeneficiaryOnboardingPhase") { resourceIs(BeneficiaryOnboardingPhaseModel::class) }
    }
}

val createOnboardingForBeneficiaryWithoutOnboarding = policySet {
    match("can view", { action is View }) {
        match("any Company") { resourceIs(CompanyModel::class) }
    }
    match("can view and create", { action is View || action is Create }) {
        match("any BeneficiaryOnboarding") { resourceIs(BeneficiaryOnboardingModel::class) }
        match("any BeneficiaryOnboardingPhase") { resourceIs(BeneficiaryOnboardingPhaseModel::class) }
    }
}

val createAndViewAndUpdateBeneficiaryHubspot = policySet {
    match("can view, update and create", { action is View || action is Update || action is Create }) {
        match("any BeneficiaryHubspot") { resourceIs(BeneficiaryHubspotModel::class) }
    }
}

val createB2bBatchInvoiceReport = policySet {
    match("can view", { action is View }) {
        match("any Company") { resourceIs(CompanyModel::class) }
    }
    match(
        "can view, count, update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any B2bBatchInvoiceReport") { resourceIs(B2bBatchInvoiceReportModel::class) }
    }
}

val deleteCompanyContract = policySet {
    match("can delete", { action is Delete }) {
        match("any CompanyContract") { resourceIs(CompanyContractModel::class) }
    }
}

val deleteCompanySubContract = policySet {
    match("can delete", { action is Delete }) {
        match("any CompanySubContract") { resourceIs(CompanySubContractModel::class) }
    }
}

val deleteBeneficiaryOnboarding = policySet {
    match("can delete", { action is Delete }) {
        match("any BeneficiaryOnboarding") { resourceIs(BeneficiaryOnboardingModel::class) }
    }
}

val deleteBeneficiaryOnboardingPhase = policySet {
    match("can delete", { action is Delete }) {
        match("any BeneficiaryOnboardingPhase") { resourceIs(BeneficiaryOnboardingPhaseModel::class) }
    }
}

val deleteBeneficiary = policySet {
    match("can delete", { action is Delete }) {
        match("any Beneficiary") { resourceIs(BeneficiaryModel::class) }
    }
}

val viewCompanyProductPriceListing = policySet {
    can(View, Count) {
        resources(CompanyProductPriceListingModel::class)
    }
}

val viewAndUpdateCompanyProductPriceListing = policySet {
    can(View, Create, Update) {
        resources(CompanyProductPriceListingModel::class)
    }
}

val deleteCompanyProductPriceListing = policySet {
    can(Delete) {
        resources(CompanyProductPriceListingModel::class)
    }
}

val viewAndUpdateMemberProductChangeSchedule = policySet {
    can(View, Create, Update) {
        resources(MemberProductChangeScheduleModel::class)
    }
}

val viewAndCountItsOwnCompanyInvoices = policySet {
    match("can view", { action is View }) {
        match("Company's MemberInvoiceGroup") {
            resource is MemberInvoiceGroupModel &&
                    subject is CompanyStaff &&
                    (resource.companyId == subject.companyId)
        }
        match("Company's PreActivationPaymentModel") {
            resource is PreActivationPaymentModel &&
                    subject is CompanyStaff &&
                    (resource.companyId == subject.companyId)
        }
    }

    match("can count", { action is Count }) {
        match("any MemberInvoiceGroup") { resourceIs(MemberInvoiceGroupModel::class) }
    }
}

val createInvoicePayment = policySet {
    can(View, Count) {
        match("any InvoicePayment") { resourceIs(InvoicePaymentModel::class) }
        match("any CancelPaymentOnAcquirerScheduleModel") { resourceIs(CancelPaymentOnAcquirerScheduleModel::class) }
        match("any BoletoPaymentDetail") { resourceIs(BoletoPaymentDetailModel::class) }
        match("any PixPaymentDetail") { resourceIs(PixPaymentDetailModel::class) }
        match("any MemberInvoice") { resourceIs(MemberInvoiceModel::class) }
        match("any BillingAccountableParty") { resourceIs(BillingAccountablePartyModel::class) }
        match("any BolepixPaymentDetail") { resourceIs(BolepixPaymentDetailModel::class) }
        match("any InvoiceGroupTaxReceipt") { resourceIs(InvoiceGroupTaxReceiptModel::class) }
        match("any InvoiceLiquidation") { resourceIs(InvoiceLiquidationModel::class) }
        match("any ResourceSignTokenModel") { resourceIs(ResourceSignTokenModel::class) }
    }

    can(Create, Update) {
        match("any InvoicePayment") { resourceIs(InvoicePaymentModel::class) }
        match("any CancelPaymentOnAcquirerScheduleModel") { resourceIs(CancelPaymentOnAcquirerScheduleModel::class) }
        match("any BoletoPaymentDetail") { resourceIs(BoletoPaymentDetailModel::class) }
        match("any PixPaymentDetail") { resourceIs(PixPaymentDetailModel::class) }
        match("any MemberInvoice") { resourceIs(MemberInvoiceModel::class) }
        match("any BolepixPaymentDetail") { resourceIs(BolepixPaymentDetailModel::class) }
        match("any ResourceSignTokenModel") { resourceIs(ResourceSignTokenModel::class) }
    }
}

val viewAndCreateInvoiceGroupTaxReceipt = policySet {
    allows(InvoiceGroupTaxReceiptModel::class, View, Create, Count)
}

val viewCreateAndUpdateMemberTelegramTracking = policySet {
    allows(MemberTelegramTrackingModel::class, View, Create, Update)
}

val viewConsolidatedHRCompanyReport = policySet {
    allows(ConsolidatedHRCompanyReport::class, View, Count)
}

val viewAndCountCompanyScoreMagenta = policySet {
    allows(CompanyScoreMagenta::class, View, Count)
}

val viewPreActivationPayment = policySet {
    can(View, Count) {
        resources(PreActivationPaymentModel::class)
    }
}

val viewPersonGracePeriod = policySet {
    can(View, Count) {
        resources(PersonGracePeriod::class)
    }
}
