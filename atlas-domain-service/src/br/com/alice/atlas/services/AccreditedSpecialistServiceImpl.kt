package br.com.alice.atlas.services

import br.com.alice.atlas.converters.SpecialistConverter.cassiSpecialistToSpecialist
import br.com.alice.atlas.converters.SpecialistConverter.healthProfessionalToSpecialist
import br.com.alice.atlas.converters.SpecialistConverter.providerUnitToSpecialist
import br.com.alice.atlas.model.SpecialistTransport
import br.com.alice.common.core.exceptions.InternalServiceErrorException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.extensions.mapEachNotNull
import br.com.alice.common.extensions.resultOf
import br.com.alice.coverage.client.ConsolidatedAccreditedNetworkService
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetwork
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.client.CassiSpecialistService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.HealthProfessionalService.FindOptions
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.getOrElse
import java.util.UUID

class AccreditedSpecialistServiceImpl(
    private val consolidatedAccreditedNetworkService: ConsolidatedAccreditedNetworkService,
    private val healthProfessionalService: HealthProfessionalService,
    private val providerUnitService: ProviderUnitService,
    private val cassiSpecialistService: CassiSpecialistService,
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val siteAccreditedNetworkService: SiteAccreditedNetworkService,
) : AccreditedSpecialistService {
    override suspend fun getSpecialistDetails(referenceId: UUID): Result<SpecialistTransport, Throwable> {
        val accreditedNetworks = consolidatedAccreditedNetworkService.getByReferencedId(referenceId).fold(
            success = { it },
            failure = { return it.failure() }
        )

        val includedPlans = accreditedNetworks.groupBy { it.referencedId }[referenceId]?.let {
            getIncludedPlans(it).getOrElse { err -> return err.failure() }
        } ?: return NotFoundException("Specialist was not found").failure()


        return getNetworkType(accreditedNetworks).fold(
            success = { networkType ->
                when (networkType) {
                    ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL, ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL ->
                        handleHealthProfessional(includedPlans, referenceId)

                    ConsolidatedAccreditedNetworkType.PARTNER_HEALTH_PROFESSIONAL ->
                        handleHealthProfessional(includedPlans, referenceId)

                    ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY ->
                        handleClinicalCommunity(includedPlans, referenceId)

                    ConsolidatedAccreditedNetworkType.CASSI_SPECIALIST ->
                        handleCassiSpecialist(includedPlans, referenceId)

                    else -> InternalServiceErrorException("Accredited network type not supported").failure()
                }
            },
            failure = { return it.failure() }
        )
    }

    private suspend fun getIncludedPlans(accreditedNetworks: List<ConsolidatedAccreditedNetwork>) =
        (accreditedNetworks.map { it.bundleIds }.flatten().distinct().ifEmpty { null }?.let { bundleIds ->
            siteAccreditedNetworkService.getByBundleIdsAndBrands(bundleIds, listOf(Brand.ALICE))
        } ?: siteAccreditedNetworkService.getActives()).mapEachNotNull { it.title }

    private fun getNetworkType(accreditedNetworks: List<ConsolidatedAccreditedNetwork>) =
        resultOf<ConsolidatedAccreditedNetworkType, Throwable> { accreditedNetworks.groupBy { it.type }.keys.first() }

    private suspend fun handleHealthProfessional(
        includedPlans: List<String>?,
        referenceId: UUID
    ): Result<SpecialistTransport, Throwable> {
        val healthProfessional = healthProfessionalService.get(
            referenceId,
            FindOptions(withStaff = false, withContact = true),
        ).fold(
            success = { it },
            failure = { return it.failure() }
        )
        val specialty = healthProfessional.specialtyId?.let { getMedicalSpecialtyName(it) }
        val subSpecialties =
            if (healthProfessional.subSpecialtyIds.isNotEmpty()) getMedicalSpecialtyNames(healthProfessional.subSpecialtyIds) else null
        return healthProfessionalToSpecialist(
            healthProfessional,
            includedPlans,
            specialty,
            subSpecialties
        ).let { Result.success(it) }
    }

    private suspend fun handleClinicalCommunity(
        includedPlans: List<String>?,
        referenceId: UUID
    ): Result<SpecialistTransport, Throwable> {
        val providerUnit = providerUnitService.get(referenceId).fold(
            success = { it },
            failure = { return it.failure() }
        )
        val specialty = providerUnit.medicalSpecialtyProfile?.let {
            getMedicalSpecialtyNames(it.map { it.specialtyId }).joinToString(", ")
        }
        val subSpecialties = if (providerUnit.medicalSpecialtyProfile.isNotNullOrEmpty())
            getMedicalSpecialtyNames(
                providerUnit.medicalSpecialtyProfile!!.map { it.subSpecialtyIds }.flatten().distinct()
            )
        else null

        return providerUnitToSpecialist(
            providerUnit,
            includedPlans,
            specialty,
            subSpecialties
        ).let { Result.success(it) }
    }

    private suspend fun handleCassiSpecialist(
        includedPlans: List<String>?,
        referenceId: UUID
    ): Result<SpecialistTransport, Throwable> {
        val cassiSpecialist = cassiSpecialistService.get(referenceId).fold(
            success = { it },
            failure = { return it.failure() }
        )
        val specialty = getMedicalSpecialtyName(cassiSpecialist.specialtyId)
        val subSpecialties =
            if (cassiSpecialist.subSpecialtyIds.isNotEmpty()) getMedicalSpecialtyNames(cassiSpecialist.subSpecialtyIds) else null
        return cassiSpecialistToSpecialist(
            cassiSpecialist,
            includedPlans,
            specialty,
            subSpecialties
        ).let { Result.success(it) }
    }

    private suspend fun getMedicalSpecialtyName(id: UUID): String {
        return medicalSpecialtyService.getById(id).fold(
            success = { it.name },
            failure = { throw it }
        )
    }

    private suspend fun getMedicalSpecialtyNames(ids: List<UUID>): List<String> {
        return medicalSpecialtyService.getByIds(ids).fold(
            success = { it.map { it.name } },
            failure = { throw it }
        )
    }
}

