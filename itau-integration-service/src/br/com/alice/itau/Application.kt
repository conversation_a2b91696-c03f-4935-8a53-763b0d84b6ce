package br.com.alice.itau

import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.authentication.firebase
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.data.layer.ITAU_INTEGRATION_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.itau.ioc.DataLayerServiceModule
import br.com.alice.itau.ioc.ServiceModule
import br.com.alice.itau.routes.controllerRoutes
import br.com.alice.itau.routes.kafkaRoutes
import br.com.alice.itau.routes.webhookRoutes
import com.typesafe.config.ConfigFactory
import io.ktor.server.application.Application
import io.ktor.server.auth.Authentication
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    val dependencyInjectionModules = listOf(
        FeatureConfigDomainClientModule,
        KafkaProducerModule,
        ServiceModule,
        DataLayerServiceModule,
        module(createdAtStart = true) {
            single { config }
        })
}

@JvmOverloads
fun Application.module(dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules) {
    setupDomainService(dependencyInjectionModules) {
        install(Authentication) {
            <EMAIL>()
            firebase()
        }

        routing {
            application.attributes.put(PolicyRootServiceKey, ITAU_INTEGRATION_ROOT_SERVICE_NAME)

            webhookRoutes()
            controllerRoutes()
        }

        kafkaConsumer {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }

        featureConfigBootstrap(FeatureNamespace.ITAU)
    }
}
