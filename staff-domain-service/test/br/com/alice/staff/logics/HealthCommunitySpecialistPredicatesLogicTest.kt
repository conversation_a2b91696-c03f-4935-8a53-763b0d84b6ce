package br.com.alice.staff.logics

import br.com.alice.common.RangeUUID
import br.com.alice.common.models.SpecialistTier.EXPERT
import br.com.alice.common.models.SpecialistTier.TALENTED
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Predicate.Companion.ContainsAnyPredicateUsage
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.data.layer.models.SpecialistAppointmentType.PRESENTIAL
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.models.SpecialtyTiers
import br.com.alice.data.layer.services.HealthCommunitySpecialistModelDataService.FieldOptions
import br.com.alice.data.layer.services.HealthCommunitySpecialistModelDataService.OrderingOptions
import org.assertj.core.api.Assertions
import kotlin.test.Test

@Suppress("OPT_IN_IS_NOT_ENABLED")
class HealthCommunitySpecialistPredicatesLogicTest {
    @OptIn(QueryAllUsage::class)
    @Test
    fun `#buildFilterQuery - if all are null`() {
        val expected = QueryBuilder(
            FieldOptions(), OrderingOptions()
        ).all()

        val result = HealthCommunitySpecialistPredicatesLogic.buildFilterQuery(
            null, null, null, null, null, null
        )

        Assertions.assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildFilterQuery - if status is passed`() {
        val status = SpecialistStatus.ACTIVE
        val expected = QueryBuilder(
            FieldOptions(), OrderingOptions()
        ).where {
            Predicate.eq(FieldOptions().status, status)
        }

        val result = HealthCommunitySpecialistPredicatesLogic.buildFilterQuery(
            status, null, null, null, null, null
        )

        Assertions.assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildFilterQuery - if specialtiesIds are passed`() {
        val specialties = listOf(RangeUUID.generate())
        val expected = QueryBuilder(
            FieldOptions(), OrderingOptions()
        ).where {
            Predicate.inList(FieldOptions().specialty, specialties)
        }

        val result = HealthCommunitySpecialistPredicatesLogic.buildFilterQuery(
            null,specialties, null, null, null, null, null
        )

        Assertions.assertThat(result).isEqualTo(expected)
    }

    @OptIn(ContainsAnyPredicateUsage::class)
    @Test
    fun `#buildFilterQuery - if appointmentsTypes are passed`() {
        val appointmentTypes = listOf(PRESENTIAL)
        val expected = QueryBuilder(
            FieldOptions(), OrderingOptions()
        ).where {
            Predicate.containsAny(FieldOptions().appointmentTypes, appointmentTypes)
        }

        val result = HealthCommunitySpecialistPredicatesLogic.buildFilterQuery(
            null,null, appointmentTypes, null, null, null, null
        )

        Assertions.assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildFilterQuery - if namePrefix is passed`() {
        val namePrefix = "zé"
        val expected = QueryBuilder(
            FieldOptions(), OrderingOptions()
        ).where {
            Predicate.search(FieldOptions().searchTokens, namePrefix)
        }

        val result = HealthCommunitySpecialistPredicatesLogic.buildFilterQuery(
            null,null, null, namePrefix, null, null,  null
        )

        Assertions.assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildFilterQuery - if showOnApp is passed`() {
        val showOnApp = true
        val expected = QueryBuilder(
            FieldOptions(), OrderingOptions()
        ).where {
            Predicate.eq(FieldOptions().showOnApp, showOnApp)
        }

        val result = HealthCommunitySpecialistPredicatesLogic.buildFilterQuery(
            null,null, null, null, showOnApp, null, null
        )

        Assertions.assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildFilterQuery - if ids are passed`() {
        val ids = listOf(RangeUUID.generate())
        val expected = QueryBuilder(
            FieldOptions(), OrderingOptions()
        ).where {
            Predicate.inList(FieldOptions().id, ids)
        }

        val result = HealthCommunitySpecialistPredicatesLogic.buildFilterQuery(
            null,null, null, null, null, ids, null
        )

        Assertions.assertThat(result).isEqualTo(expected)
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#buildFilterQuery - if specialtyTiers are passed`() {
        val specialtyTiers = listOf(
            SpecialtyTiers(specialtyId = RangeUUID.generate(), tiers = listOf(TALENTED) )
        )
        val expected = QueryBuilder(
            FieldOptions(), OrderingOptions()
        ).where {
            scope(
                specialtyTiers.fold(scope(Predicate.False)) { oldPredicate, specialtyTiers ->
                    oldPredicate.or(
                        scope(
                            FieldOptions().tier.inList(specialtyTiers.tiers).and(
                                FieldOptions().specialty.eq(specialtyTiers.specialtyId)
                            )
                        )
                    )
                }
            )
        }

        val result = HealthCommunitySpecialistPredicatesLogic.buildFilterQuery(
            null, null, null, null, null, null, specialtyTiers
        )

        Assertions.assertThat(result).isEqualTo(expected)
    }

    @OptIn(OrPredicateUsage::class, ContainsAnyPredicateUsage::class)
    @Test
    fun `#buildFilterQuery - if all arguments are passed`() {
        val status = SpecialistStatus.ACTIVE
        val specialties = listOf(RangeUUID.generate())
        val appointmentTypes = listOf(PRESENTIAL)
        val namePrefix = "zé"
        val showOnApp = true
        val ids = listOf(RangeUUID.generate())
        val specialtyTiers = listOf(
            SpecialtyTiers(specialtyId = RangeUUID.generate(), tiers = listOf(TALENTED) ),
            SpecialtyTiers(specialtyId = RangeUUID.generate(), tiers = listOf(TALENTED, EXPERT) )
        )
        val expected = QueryBuilder(
            FieldOptions(), OrderingOptions()
        ).where {
            Predicate.eq(FieldOptions().status, status) and
                    Predicate.inList(FieldOptions().specialty, specialties) and
                    Predicate.containsAny(FieldOptions().appointmentTypes, appointmentTypes) and
                    Predicate.search(FieldOptions().searchTokens, namePrefix) and
                    Predicate.eq(FieldOptions().showOnApp, showOnApp) and
                    Predicate.inList(FieldOptions().id, ids) and
                    scope(
                        specialtyTiers.fold(scope(Predicate.False)) { oldPredicate, specialtyTiers ->
                            oldPredicate.or(
                                scope(
                                    FieldOptions().tier.inList(specialtyTiers.tiers).and(
                                        FieldOptions().specialty.eq(specialtyTiers.specialtyId)
                                    )
                                )
                            )
                        }
                    )
        }

        val result = HealthCommunitySpecialistPredicatesLogic.buildFilterQuery(
            status, specialties, appointmentTypes, namePrefix, showOnApp, ids, specialtyTiers
        )

        Assertions.assertThat(result).isEqualTo(expected)
    }

    @OptIn(OrPredicateUsage::class, ContainsAnyPredicateUsage::class)
    @Test
    fun `#buildFilterQuery - if range is passed`() {
        val status = SpecialistStatus.ACTIVE
        val specialties = listOf(RangeUUID.generate())
        val appointmentTypes = listOf(PRESENTIAL)
        val namePrefix = "zé"
        val showOnApp = true
        val ids = listOf(RangeUUID.generate())
        val range = IntRange(1, 20)
        val specialtyTiers = listOf(
            SpecialtyTiers(specialtyId = RangeUUID.generate(), tiers = listOf(TALENTED) ),
            SpecialtyTiers(specialtyId = RangeUUID.generate(), tiers = listOf(TALENTED, EXPERT) )
        )
        val expected = QueryBuilder(
            FieldOptions(), OrderingOptions()
        ).where {
            Predicate.eq(FieldOptions().status, status) and
                    Predicate.inList(FieldOptions().specialty, specialties) and
                    Predicate.containsAny(FieldOptions().appointmentTypes, appointmentTypes) and
                    Predicate.search(FieldOptions().searchTokens, namePrefix) and
                    Predicate.eq(FieldOptions().showOnApp, showOnApp) and
                    Predicate.inList(FieldOptions().id, ids) and
                    scope(
                        specialtyTiers.fold(scope(Predicate.False)) { oldPredicate, specialtyTiers ->
                            oldPredicate.or(
                                scope(
                                    FieldOptions().tier.inList(specialtyTiers.tiers).and(
                                        FieldOptions().specialty.eq(specialtyTiers.specialtyId)
                                    )
                                )
                            )
                        }
                    )
        }.orderBy { name }.offset { range.first }.limit { range.count() }

        val result = HealthCommunitySpecialistPredicatesLogic.buildFilterQuery(
            range, status, specialties, appointmentTypes, namePrefix, showOnApp, ids, specialtyTiers
        )

        Assertions.assertThat(result).isEqualTo(expected)
    }
}

