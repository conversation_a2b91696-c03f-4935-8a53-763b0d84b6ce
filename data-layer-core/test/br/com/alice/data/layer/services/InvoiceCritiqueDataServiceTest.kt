package br.com.alice.data.layer.services

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.models.InvoiceCritiqueModel
import br.com.alice.data.layer.models.InvoiceCritiqueProcedureModel
import br.com.alice.data.layer.models.InvoiceCritiqueReason
import br.com.alice.data.layer.models.InvoiceCritiqueStatus
import br.com.alice.data.layer.models.TissInvoiceStatus
import br.com.alice.data.layer.repositories.JdbiRepositoryFactory
import br.com.alice.data.layer.tables.GuiaTable
import br.com.alice.data.layer.tables.InvoiceTable
import com.github.kittinunf.result.Result
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test

class InvoiceCritiqueDataServiceImplTest : DataServiceTestHelper() {

    private val repoFactory = JdbiRepositoryFactory(mainJdbi)
    private val invoiceRepo = repoFactory.get(InvoiceTable::class)
    private val guiaRepo = repoFactory.get(GuiaTable::class)

    private val dataService = InvoiceCritiqueModelDataServiceClient(httpInvoker)

    @Test
    fun `should add invoice critique and get successfully`() = runBlocking {
        val invoice = InvoiceTable(
            code = "123456",
            userEmail = "<EMAIL>",
            status = TissInvoiceStatus.DRAFT,
            searchTokens = TsVector("search"),
        )

        invoiceRepo.add(invoice)

        val guia = GuiaTable(
            number = "2312",
            tissBatchId = UUID.randomUUID(),
            tissBatchNumber = "123",
            valueTotal = BigDecimal(123.0),
            memberNewBorn = "N",
            requestedAt = LocalDate.now(),
        )

        guiaRepo.add(guia)

        val critique = InvoiceCritiqueModel(
            invoiceId = invoice.id,
            guiaId = guia.id,
            reason = InvoiceCritiqueReason.INCORRECT_CODE,
            description = "description",
            status = InvoiceCritiqueStatus.PENDING,
            procedures = listOf(
                InvoiceCritiqueProcedureModel("code", "description")
            ),
        )

        val res = dataService.add(critique)
        Assertions.assertThat(res is Result.Success).isTrue()

        val queryResult = dataService.get(critique.id)

        assertThat(queryResult).isSuccessWithDataIgnoringGivenFields(
            critique,
            "version",
            "createdAt",
            "updatedAt",
        )
    }
}
