package br.com.alice.exec.indicator.client

import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.AdderList
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.client.UpdaterList
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.HealthSpecialistScoreEnum
import br.com.alice.data.layer.models.TierType
import br.com.alice.data.layer.models.TussProcedureSpecialty
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface TussProcedureSpecialtyService : Service,
    Adder<TussProcedureSpecialty>,
    Get<PERSON><TussProcedureSpecialty>,
    Updater<TussProcedureSpecialty>,
    UpdaterList<TussProcedureSpecialty>,
    AdderList<TussProcedureSpecialty> {

    override val namespace get() = "exec_indicator"
    override val serviceName get() = "tuss_procedure_specialty"
    override suspend fun addList(models: List<TussProcedureSpecialty>): Result<List<TussProcedureSpecialty>, Throwable>

    override suspend fun updateList(
        models: List<TussProcedureSpecialty>,
        returnOnFailure: Boolean
    ): Result<List<TussProcedureSpecialty>, Throwable>

    override suspend fun get(id: UUID): Result<TussProcedureSpecialty, Throwable>
    override suspend fun add(model: TussProcedureSpecialty): Result<TussProcedureSpecialty, Throwable>
    override suspend fun update(model: TussProcedureSpecialty): Result<TussProcedureSpecialty, Throwable>

    suspend fun getByIds(ids: List<UUID>): Result<List<TussProcedureSpecialty>, Throwable>

    suspend fun findByAliceCodes(aliceCodes: List<String>): Result<List<TussProcedureSpecialty>, Throwable>

    suspend fun findByTierAndScoreAndSpecialtyAndTussCode(
        tier: SpecialistTier,
        score: HealthSpecialistScoreEnum?,
        specialtyId: UUID,
        tussCode: String
    ): Result<List<TussProcedureSpecialty>, Throwable>

    suspend fun findByAliceCodeScoreTierSpecialtyIdBrandAndProductTier(
        params: FindExistingTussProcedureSpecialtyParams
    ): Result<List<TussProcedureSpecialty>, Throwable>

    suspend fun findByEndAtNullAndQuery(tussProcedureSpecialtyFilters: TussProcedureSpecialtyFilters): Result<List<TussProcedureSpecialty>, Throwable>

    suspend fun count(tussProcedureSpecialtyFilters: TussProcedureSpecialtyFilters): Result<Int, Throwable>

    suspend fun findActiveByAliceCodesAndSpeciality(
        specialityId: UUID,
        aliceCodes: List<String>
    ): Result<List<TussProcedureSpecialty>, Throwable>

    suspend fun findActiveByHealthSpecialistResourceBundleIds(
        healthSpecialistResourceBundleIds: List<UUID>,
    ): Result<List<TussProcedureSpecialty>, Throwable>
}

data class TussProcedureSpecialtyFilters(
    val aliceCode: String?,
    val tussCode: String?,
    val range: IntRange? = null
)

data class FindExistingTussProcedureSpecialtyParams(
    val aliceCode: String,
    val score: HealthSpecialistScoreEnum?,
    val tier: SpecialistTier,
    val specialtyId: UUID,
    val brand: Brand,
    val productTier: TierType
)
