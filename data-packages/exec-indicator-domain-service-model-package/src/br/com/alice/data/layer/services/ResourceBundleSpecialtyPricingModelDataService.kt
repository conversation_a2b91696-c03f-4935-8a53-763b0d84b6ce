package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.AdderList
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.DeleterList
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.client.UpdaterList
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingModel
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ResourceBundleSpecialtyPricingModelDataService :
    Service,
    Finder<ResourceBundleSpecialtyPricingModelDataService.FieldOptions, ResourceBundleSpecialtyPricingModelDataService.OrderingOptions, ResourceBundleSpecialtyPricingModel>,
    Counter<ResourceBundleSpecialtyPricingModelDataService.FieldOptions, ResourceBundleSpecialtyPricingModelDataService.OrderingOptions, ResourceBundleSpecialtyPricingModel>,
    Getter<ResourceBundleSpecialtyPricingModel>,
    Adder<ResourceBundleSpecialtyPricingModel>,
    Updater<ResourceBundleSpecialtyPricingModel>,
    UpdaterList<ResourceBundleSpecialtyPricingModel>,
    AdderList<ResourceBundleSpecialtyPricingModel>,
    DeleterList<ResourceBundleSpecialtyPricingModel> {

    override val namespace get() = "exec_indicator"
    override val serviceName get() = "resource_bundle_specialty_pricing"

    class Id : Field.UUIDField(ResourceBundleSpecialtyPricingModel::id) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ResourceBundleSpecialtyId : Field.UUIDField(ResourceBundleSpecialtyPricingModel::resourceBundleSpecialtyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class UpdatedAt : Field.DateTimeField(ResourceBundleSpecialtyPricingModel::updatedAt)
    class CreatedAt : Field.DateTimeField(ResourceBundleSpecialtyPricingModel::createdAt)

    class FieldOptions {
        val id = Id()
        val resourceBundleSpecialtyId = ResourceBundleSpecialtyId()
    }

    class OrderingOptions {
        val updatedAt = UpdatedAt()
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<ResourceBundleSpecialtyPricingModel, Throwable>
    override suspend fun add(model: ResourceBundleSpecialtyPricingModel): Result<ResourceBundleSpecialtyPricingModel, Throwable>
    override suspend fun update(model: ResourceBundleSpecialtyPricingModel): Result<ResourceBundleSpecialtyPricingModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ResourceBundleSpecialtyPricingModel>, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun updateList(models: List<ResourceBundleSpecialtyPricingModel>, returnOnFailure: Boolean): Result<List<ResourceBundleSpecialtyPricingModel>, Throwable>
    override suspend fun addList(models: List<ResourceBundleSpecialtyPricingModel>): Result<List<ResourceBundleSpecialtyPricingModel>, Throwable>
    override suspend fun deleteList(models: List<ResourceBundleSpecialtyPricingModel>): Result<List<Boolean>, Throwable>
}
