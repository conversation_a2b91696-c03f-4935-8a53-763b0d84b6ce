{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["NullvsIntegrationLogModel", "NullvsIntegrationRecordModel", "MemberInvoiceGroupModel", "InvoiceLiquidationModel", "MemberInvoiceModel", "InvoicePaymentModel", "PaymentDetailModel", "BoletoPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "PixPaymentDetailModel", "BolepixPaymentDetailModel", "InvoiceItemModel", "ResourceSignTokenModel"], "actions": ["view", "create", "update", "count"]}, {"resources": ["MemberModel"], "actions": ["view"]}, {"resources": ["TrackPersonABModel"], "actions": ["view", "create"]}, {"resources": ["CompanyContractModel", "CompanySubContractModel", "CompanyModel", "Generic<PERSON><PERSON><PERSON><PERSON>", "BillingAccountablePartyModel"], "actions": ["view", "count"]}]}]}