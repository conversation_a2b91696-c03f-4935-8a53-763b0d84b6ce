package br.com.alice.tiss.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.events.GuiaWithRadiotherapyCreatedEvent
import br.com.alice.exec.indicator.events.GuiaWithRadiotherapyCreatedPayload
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.exec.indicator.models.RequesterData
import br.com.alice.tiss.consumers.converters.GuiaWithRadiotherapyCreatedRequestBuilder
import br.com.alice.tiss.models.AttachmentHeader
import br.com.alice.tiss.models.BeneficiaryAdditionalData
import br.com.alice.tiss.models.CreateGuiaRequest
import br.com.alice.tiss.models.OncologicalDiagnosis
import br.com.alice.tiss.models.OncologicalRadioDiagnosis
import br.com.alice.tiss.models.RadioRequestAttachment
import br.com.alice.tiss.services.internal.GuiaWithAttachmentService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GuiaWithRadiotherapyCreatedConsumerTest : ConsumerTest() {

    private val guiaWithRadiotherapyCreatedRequestBuilder: GuiaWithRadiotherapyCreatedRequestBuilder = mockk()
    private val guiaWithAttachmentService: GuiaWithAttachmentService = mockk()
    private val totvsGuiaService: TotvsGuiaService = mockk()


    private val consumer = GuiaWithRadiotherapyCreatedConsumer(
        guiaWithRadiotherapyCreatedRequestBuilder,
        guiaWithAttachmentService,
        totvsGuiaService
    )

    private val person = TestModelFactory.buildPerson()
    private val attachment = TestModelFactory.buildAttachmentRadiotherapy()
    private val totvsGuia = TestModelFactory.buildTotvsGuia()
    private val radiotherapyTotvsGuia = TestModelFactory.buildTotvsGuia()

    private val payload = GuiaWithRadiotherapyCreatedPayload(
        attachment = attachment,
        totvsGuia = totvsGuia,
        radiotherapyTotvsGuia = radiotherapyTotvsGuia,
        beneficiary = Beneficiary(nationalId = "12345678900", newBornAttendance = false, name = "Batata frita"),
        origin = GuiaOrigin.EITA,
        person = person,
        referenceGuiaProcedures = emptyList(),
    )

    private val radioyRequestAttachment = RadioRequestAttachment(
        providerCnpj = "",
        origin = GuiaOrigin.EITA,
        attachmentHeader = AttachmentHeader(
            ansCode = "1234",
            attachmentGuideNumber = "************",
            referencedGuideNumber = "123",
            requestDate = LocalDate.now()
        ),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE,
            phoneNumber = "(11) 1111-1111",
            email = "<EMAIL>"
        ),
        beneficiaryData = Beneficiary(nationalId = "12345678900", newBornAttendance = false, name = "Batata frita"),
        beneficiaryAdditionalData = BeneficiaryAdditionalData(
            age = 60.toBigInteger(),
            sex = "1",
        ),
        oncologicalDiagnosisRadio = OncologicalRadioDiagnosis(
            imageDiagnosis = null,
            radioDiagnosis = OncologicalDiagnosis(
                diagnosisDate = LocalDate.now(),
                diagnosisCID = listOf("C50"),
                local = "1",
                purpose = "1",
                ecog = "1",
                histopathologicalDiagnosis = "1",
                relevantInfo = "1"
            )
        ),
        numberFields = 1.toBigInteger(),
        fieldDose = 4.toBigInteger(),
        totalDose = 8.toBigInteger(),
        numberDays = 2.toBigInteger(),
        expectedStartDate = LocalDate.now(),
        observation = null
    )

    private val createGuiaRequest = CreateGuiaRequest(
        procedures = listOf(
            GuiaProcedure(table = "98", code = "123", quantity = 1, description = ""),
            GuiaProcedure(table = "98", code = "545", quantity = 1, description = "")
        ),
        requester = RequesterData(name = "Alice Operadora", providerCode = "00000000000"),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE,
            phoneNumber = "123",
            email = "<EMAIL>"
        ),
        guiaNumber = "************",
        requestDate = LocalDate.now(),
        beneficiary = Beneficiary(nationalId = "12345678900", newBornAttendance = false, name = "Batata frita"),
        ansCode = "1234",
        authorizationType = "1",
        attendanceCharacter = "1",
        origin = GuiaOrigin.EITA,
        attendanceRegime = "1",
        accidentIndication = "1",
    )

    private val event = GuiaWithRadiotherapyCreatedEvent(payload)

    @AfterTest
    fun setup() = clearAllMocks()

    @Test
    fun `#handle GuiaWithRadiotherapyCreatedEvent should process event`() = runBlocking {
        coEvery {
            guiaWithRadiotherapyCreatedRequestBuilder.build(
                attachment = attachment,
                totvsGuia = totvsGuia,
                radiotherapyTotvsGuia = radiotherapyTotvsGuia,
                beneficiary = payload.beneficiary,
                origin = payload.origin,
                person = person,
                referenceGuiaProcedures = emptyList(),
            )
        } returns Pair(radioyRequestAttachment, createGuiaRequest).success()

        coEvery {
            totvsGuiaService.get(totvsGuia.id)
        } returns totvsGuia.success()

        coEvery {
            guiaWithAttachmentService.createGuiaWithRadiotherapy(
                createGuiaRequest,
                radioyRequestAttachment
            )
        } returns true.success()

        val result = consumer.handle(event)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { guiaWithAttachmentService.createGuiaWithRadiotherapy(any(), any()) }
    }

    @Test
    fun `#handle GuiaWithRadiotherapyCreatedEvent should not process event when guia already has external code`() = runBlocking {
        coEvery {
            guiaWithRadiotherapyCreatedRequestBuilder.build(
                attachment = attachment,
                totvsGuia = totvsGuia,
                radiotherapyTotvsGuia = radiotherapyTotvsGuia,
                beneficiary = payload.beneficiary,
                origin = payload.origin,
                person = person,
                referenceGuiaProcedures = emptyList(),
            )
        } returns Pair(radioyRequestAttachment, createGuiaRequest).success()

        coEvery {
            totvsGuiaService.get(totvsGuia.id)
        } returns totvsGuia.copy(externalCode = "hasExternalCode").success()

        coEvery {
            guiaWithAttachmentService.createGuiaWithRadiotherapy(
                createGuiaRequest,
                radioyRequestAttachment
            )
        } returns false.success()

        val result = consumer.handle(event)

        ResultAssert.assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyNone { guiaWithAttachmentService.createGuiaWithRadiotherapy(any(), any()) }
    }
}
