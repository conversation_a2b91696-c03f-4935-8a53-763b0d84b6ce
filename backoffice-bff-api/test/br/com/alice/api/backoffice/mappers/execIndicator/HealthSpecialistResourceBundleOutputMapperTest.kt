package br.com.alice.api.backoffice.mappers.execIndicator

import br.com.alice.api.backoffice.transfers.Colors
import br.com.alice.api.backoffice.transfers.FriendlyEnumResponse
import br.com.alice.api.backoffice.transfers.execIndicator.HealthSpecialistResourceBundleIndexResponse
import br.com.alice.api.backoffice.transfers.execIndicator.HealthSpecialistResourceBundleResponse
import br.com.alice.api.backoffice.transfers.execIndicator.HealthSpecialistResourceBundleType
import br.com.alice.api.backoffice.transfers.execIndicator.SecondaryResourcesResponseTransport
import br.com.alice.common.mappers.CommonOutputMapper
import br.com.alice.common.transfers.ListPaginatedResponse
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePricingStatus
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundleWithPricingData
import io.ktor.http.Parameters
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class HealthSpecialistResourceBundleOutputMapperTest {

    private val resourceBundle = TestModelFactory.buildHealthSpecialistResourceBundle()
    private val resources = TestModelFactory.buildHealthcareResource()
    private val resourceBundleWithPricing = HealthSpecialistResourceBundleWithPricingData(
        healthSpecialistResourceBundle = resourceBundle,
        pricingStatus = HealthSpecialistResourceBundlePricingStatus.PRICED,
        specialtiesCount = 1,
        allSpecialtiesCount = 1
    )

    @Test
    fun `#toResponse returns correctly`() {
        val expected = HealthSpecialistResourceBundleResponse(
            id = resourceBundle.id,
            primaryTuss = resourceBundle.primaryTuss,
            secondaryResources = listOf(
                SecondaryResourcesResponseTransport(
                    id = resources.id,
                    code = resources.code,
                    tableType = resources.tableType ?: "",
                    tussCode = resources.tussCode ?: "",
                    description = resources.description
                )
            ),
            executionAmount = resourceBundle.executionAmount,
            executionEnvironment = resourceBundle.executionEnvironment,
            aliceDescription = resourceBundle.description,
            status = resourceBundle.status,
            serviceType = resourceBundle.serviceType,
            aliceCode = resourceBundle.code
        )

        val result = HealthSpecialistResourceBundleOutputMapper.toResponse(resourceBundle, listOf(resources))
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#toPaginatedResponse returns correctly`() {
        val total = 10
        val params = Parameters.build {
            append("page", "1")
            append("pageSize", "3")
        }

        val response = HealthSpecialistResourceBundleIndexResponse(
            id = resourceBundle.id,
            aliceCode = resourceBundle.code,
            primaryTuss = resourceBundle.primaryTuss,
            aliceDescription = resourceBundle.description,
            type = FriendlyEnumResponse(
                value = HealthSpecialistResourceBundleType.SINGLE,
                friendlyName = HealthSpecialistResourceBundleType.SINGLE.description,
                color = Colors.GRAY
            ),
            serviceType = FriendlyEnumResponse(
                value = resourceBundle.serviceType,
                friendlyName = resourceBundle.serviceType.description,
                color = Colors.BLUE
            ),
            status = resourceBundle.status,
            pricingStatus = FriendlyEnumResponse(
                value = resourceBundleWithPricing.pricingStatus,
                friendlyName = resourceBundleWithPricing.pricingStatus.description,
                color = Colors.GREEN
            ),
            specialtiesText = "Todas especialidades"
        )

        val expected = ListPaginatedResponse(
            pagination = CommonOutputMapper.toPaginationResponse(params, total),
            results = listOf(response, response, response)
        )

        val result = HealthSpecialistResourceBundleOutputMapper.toPaginatedResponse(
            listOf(resourceBundleWithPricing, resourceBundleWithPricing, resourceBundleWithPricing),
            total,
            params
        )
        assertThat(result).isEqualTo(expected)
    }


}
