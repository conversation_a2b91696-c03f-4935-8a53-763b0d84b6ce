package br.com.alice.api.backoffice.mappers.execIndicator

import br.com.alice.api.backoffice.transfers.execIndicator.HealthSpecialistResourceBundleUpdateRequest
import br.com.alice.data.layer.models.HealthSpecialistResourceBundle

object HealthSpecialistResourceBundleInputMapper {

    fun toUpdate(
        healthSpecialistResourceBundle: HealthSpecialistResourceBundle,
        request: HealthSpecialistResourceBundleUpdateRequest
    ): HealthSpecialistResourceBundle =
        healthSpecialistResourceBundle.copy(
            secondaryResources = request.secondaryResources?.map { it.id } ?: emptyList(),
            executionAmount = request.executionAmount,
            executionEnvironment = request.executionEnvironment,
            description = request.aliceDescription,
            status = request.status,
            serviceType = request.serviceType,
        )
}
