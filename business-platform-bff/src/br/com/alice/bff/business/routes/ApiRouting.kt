package br.com.alice.bff.business.routes

import br.com.alice.bff.business.controllers.CacheController
import br.com.alice.bff.business.controllers.v1.CompanySubcontractController
import br.com.alice.bff.business.controllers.v2.AuthController
import br.com.alice.bff.business.controllers.v2.BeneficiaryController
import br.com.alice.bff.business.controllers.v2.BeneficiaryListController
import br.com.alice.bff.business.controllers.v2.CompanyController
import br.com.alice.bff.business.controllers.v2.CompanyStaffController
import br.com.alice.bff.business.controllers.v2.CopayController
import br.com.alice.bff.business.controllers.v2.DashboardController
import br.com.alice.bff.business.controllers.v2.GracePeriodController
import br.com.alice.bff.business.controllers.v2.InvoiceController
import br.com.alice.bff.business.controllers.v2.LiquidationController
import br.com.alice.bff.business.controllers.v2.NotificationController
import br.com.alice.bff.business.controllers.v2.PaymentController
import br.com.alice.bff.business.controllers.v2.ProductController
import br.com.alice.bff.business.controllers.v2.RefundController
import br.com.alice.bff.business.controllers.v2.ScreenController
import br.com.alice.bff.business.controllers.v2.WhatsNewController
import br.com.alice.common.coHandler
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.extensions.inject
import br.com.alice.common.multipartHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.plugins.swagger.swaggerUI
import io.ktor.server.routing.Routing
import io.ktor.server.routing.delete
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route

fun Routing.apiRoutes() {
    val cacheController by inject<CacheController>()
    val healthController by inject<HealthController>()
    val authController by inject<AuthController>()

    get("/") { coHandler(healthController::checkHealthWithAuth) }
    post("/cache/clear_cache") { coHandler(cacheController::clearAllCaches) }
    post("/send_magic_link") { coHandler(authController::sendMagicLink) }
    post("/sign_in") { coHandler(authController::signIn) }

    swaggerUI(path = "/v2/swagger", swaggerFile = "documentation/openapi.yaml")

    authenticate {
        val companySubcontractController by inject<CompanySubcontractController>()
        val beneficiaryController by inject<br.com.alice.bff.business.controllers.v1.BeneficiaryController>()

        route("/company/{companyId}") {
            get("/subcontracts") { coHandler("companyId", companySubcontractController::index) }
            route("/beneficiaries") {
                get("/basic_infos") { coHandler("companyId", beneficiaryController::basicInfos) }
                post("/holders") { coHandler("companyId", beneficiaryController::addBeneficiaryHolder) }
                post("/dependents") { coHandler("companyId", beneficiaryController::addBeneficiaryDependent) }
                post("/{id}/schedule_cancellation") {
                    coHandler(
                        "companyId",
                        "id",
                        beneficiaryController::scheduleCancellation
                    )
                }
                post("/validate_person_entry") { coHandler("companyId", beneficiaryController::validatePersonEntry) }
            }
        }

        route("/v2") {
            val invoiceController by inject<InvoiceController>()
            val beneficiaryListController by inject<BeneficiaryListController>()
            val v2BeneficiaryController by inject<BeneficiaryController>()
            val paymentController by inject<PaymentController>()
            val productController by inject<ProductController>()
            val notificationController by inject<NotificationController>()
            val gracePeriodController by inject<GracePeriodController>()
            val copayController by inject<CopayController>()
            val refundController by inject<RefundController>()
            val screenController by inject<ScreenController>()
            val companyController by inject<CompanyController>()
            val dashboardController by inject<DashboardController>()
            val liquidationController by inject<LiquidationController>()
            val whatsNewController by inject<WhatsNewController>()
            val companyStaffController by inject<CompanyStaffController>()

            get("/me") { coHandler(authController::getLogged) }
            get("/whats_new") { coHandler(whatsNewController::getWhatsNewAnnouncements) }

            route("/companies") {
                post("/data-request") { coHandler(companyController::requestCompanyMembersData) }

                route("/{companyId}") {
                    get("/") { coHandler("companyId", companyController::getCompany) }
                    put("/") { coHandler("companyId", companyController::updateCompany) }

                    route("/invoices") {
                        get { coHandler("companyId", invoiceController::listInvoices) }
                        route("/{invoiceId}") {
                            get("/available_files") { coHandler("invoiceId", invoiceController::availableFiles) }
                            get("/download_file") { invoiceController.downloadFiles(this) }
                        }
                    }
                    route("/liquidations") {
                        route("/{liquidationId}") {
                            get("/available_files") { coHandler("liquidationId", liquidationController::availableFiles) }
                            get("/download_file") { liquidationController.downloadFiles(this) }
                        }
                    }

                    route("/beneficiaries") {
                        post("/dependents") { coHandler("companyId", v2BeneficiaryController::addBeneficiaryDependent) }
                        get("/") { coHandler("companyId", beneficiaryListController::listBeneficiaries) }
                        get("/status_quantities") { coHandler("companyId",beneficiaryListController::insuranceStatusQuantities)}
                        get("/sheet/template") { coHandler("companyId", v2BeneficiaryController::downloadBeneficiarySheetTemplate) }
                    }

                    get("/notifications") { coHandler("companyId", notificationController::listNotifications) }
                    get("/change_product_next_date") {
                        coHandler(
                            "companyId",
                            v2BeneficiaryController::getProductChangeNextDate
                        )
                    }
                    route("/subcontracts") {
                        route("/{subcontractId}") {
                            route("/products") {
                                get { coHandler("companyId", "subcontractId", productController::productsBySubcontract) }
                                get("/count") { coHandler("subcontractId", productController::countProductsBySubcontract) }
                            }
                            get("/available_products") { coHandler("companyId", "subcontractId", productController::availableProducts) }
                            get("/grace_period") {
                                coHandler(
                                    "companyId",
                                    "subcontractId",
                                    gracePeriodController::getBySubcontract
                                )
                            }
                            get("/copay") { coHandler("companyId", "subcontractId", copayController::getBySubcontract) }
                            get("/refund") {
                                coHandler(
                                    "companyId",
                                    "subcontractId",
                                    refundController::getBySubcontract
                                )
                            }
                            get("/screen/dynamic_tabs") {
                                coHandler(
                                    "companyId",
                                    "subcontractId",
                                    screenController::getDynamicTabs
                                )
                            }
                            get("/relations") {
                                coHandler(
                                    "companyId",
                                    "subcontractId",
                                    v2BeneficiaryController::getRelationTypes
                                )
                            }
                        }
                    }

                    route("/dashboards") {
                        get("/account_overview") { coHandler("companyId", dashboardController::getAccountOverview) }
                        get("/utilization_journey") { coHandler("companyId", dashboardController::getUtilizationJourney) }
                        get("/score_magenta") { coHandler("companyId", dashboardController::getScoreMagenta) }
                    }

                    route("staffs") {
                        get("/") { coHandler("companyId", companyStaffController::getActiveCompanyStaffs) }
                        post("/") { coHandler("companyId", companyStaffController::addCompanyStaff) }

                        route("/{staffId}") {
                            delete("/") { coHandler("companyId", "staffId", companyStaffController::deleteStaff) }
                            put("/") { coHandler("companyId", "staffId", companyStaffController::changeAccessLevel) }
                        }
                    }
                }
            }

            route("/beneficiaries") {
                route("/{beneficiaryId}") {
                    get { coHandler("beneficiaryId", v2BeneficiaryController::getBeneficiary) }
                    post("/product_change_requests") {
                        coHandler(
                            "beneficiaryId",
                            v2BeneficiaryController::changeProduct
                        )
                    }
                    delete("/product_change_requests") {
                        coHandler(
                            "beneficiaryId",
                            v2BeneficiaryController::cancelProductChange
                        )
                    }
                    get("/products") { coHandler("beneficiaryId", productController::getBeneficiaryProduct) }
                    delete("/schedule_cancellation") {
                        coHandler(
                            "beneficiaryId",
                            v2BeneficiaryController::abortBeneficiaryCancel
                        )
                    }
                    get("/is_cancelable") { coHandler("beneficiaryId", v2BeneficiaryController::isCancelable) }
                }
            }

            route("/persons") {
                route("/{personId}") {
                    route("/file") {
                        post("/") { multipartHandler("personId", v2BeneficiaryController::uploadFile) }
                        delete("/{fileId}") { coHandler("personId", "fileId", v2BeneficiaryController::deleteFile) }
                    }
                }
            }

            route("/invoices/{invoiceId}") {
                get("/payments") { coHandler("invoiceId", paymentController::getInvoicePayment) }
            }

            get("/product_groups") { coHandler(productController::listProductGroups) }
        }
    }
}
