package br.com.alice.tiss.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.events.GuiaHospitalizationExtensionCreatedEvent
import br.com.alice.exec.indicator.events.GuiaHospitalizationExtensionCreatedPayload
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.exec.indicator.models.RequesterData
import br.com.alice.tiss.consumers.converters.GuiaHospitalizationExtensionCreatedRequestBuilder
import br.com.alice.tiss.models.CreateGuiaHospitalizationExtensionRequest
import br.com.alice.tiss.services.internal.GuiaService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

class GuiaHospitalizationExtensionCreatedConsumerTest : ConsumerTest() {
    @AfterTest
    fun setup() = clearAllMocks()
    private val guiaService: GuiaService = mockk()
    private val guiaExtensionBuilder: GuiaHospitalizationExtensionCreatedRequestBuilder = mockk()
    private val totvsGuiaService: TotvsGuiaService = mockk()

    private val consumer = GuiaHospitalizationExtensionCreatedConsumer(guiaService, guiaExtensionBuilder, totvsGuiaService)

    private val referenceTotvsGuia = TestModelFactory.buildTotvsGuia()
    private val totvsGuia = TestModelFactory.buildTotvsGuia(referenceTotvsGuiaId = referenceTotvsGuia.id)

    private val payload = GuiaHospitalizationExtensionCreatedPayload(
        totvsGuia = totvsGuia,
        referenceTotvsGuia = referenceTotvsGuia,
        procedures = emptyList(),
        beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        origin = GuiaOrigin.EITA
    )

    private val createGuiaHospitalizationRequest = CreateGuiaHospitalizationExtensionRequest(
        procedures = listOf(
            GuiaProcedure(table = "98", code = "123", quantity = 1, description = ""),
            GuiaProcedure(table = "98", code = "545", quantity = 1, description = "")
        ),
        requester = RequesterData(name = "Alice Operadora", providerCode = "00000000000"),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE,
            phoneNumber = "(11) 1111-1111",
        ),
        guiaNumber = "983732412324",
        requestDate = LocalDate.now(),
        beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        ansCode = "1234",
        origin = GuiaOrigin.EHR,
        clinicalIndication = "1",
        additionalDays = 1,
        referenceGuiaNumber = "123"
    )

    private val event = GuiaHospitalizationExtensionCreatedEvent(payload)

    @Test
    fun `#handle GuiaHospitalizationExtensionCreatedConsumer should process event as expected`() = runBlocking {
        coEvery {
            guiaService.createGuiaHospitalizationExtension(createGuiaHospitalizationRequest)
        } returns true.success()

        coEvery {
            guiaExtensionBuilder.build(any(), any(), any(), any(), any())
        } returns createGuiaHospitalizationRequest.success()

        coEvery {
            totvsGuiaService.get(totvsGuia.id)
        } returns totvsGuia.success()

        val result = consumer.handle(event)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { guiaService.createGuiaHospitalizationExtension(any()) }
    }

    @Test
    fun `#handle GuiaHospitalizationExtensionCreatedConsumer should process not event when guia already has external code`() = runBlocking {
        coEvery {
            guiaService.createGuiaHospitalizationExtension(createGuiaHospitalizationRequest)
        } returns true.success()

        coEvery {
            guiaExtensionBuilder.build(any(), any(), any(), any(), any())
        } returns createGuiaHospitalizationRequest.success()

        coEvery {
            totvsGuiaService.get(totvsGuia.id)
        } returns totvsGuia.copy(externalCode = "haveExternalCode").success()

        val result = consumer.handle(event)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyNone { guiaService.createGuiaHospitalizationExtension(any()) }
    }
}
