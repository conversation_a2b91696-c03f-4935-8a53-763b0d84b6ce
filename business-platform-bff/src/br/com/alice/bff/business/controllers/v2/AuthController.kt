package br.com.alice.bff.business.controllers.v2

import br.com.alice.authentication.currentEmail
import br.com.alice.bff.business.converters.v2.toResponse
import br.com.alice.bff.business.models.v2.SendMagicLinkRequest
import br.com.alice.bff.business.models.v2.SignInRequest
import br.com.alice.bff.business.models.v2.StaffResponse
import br.com.alice.bff.business.services.AuthService
import br.com.alice.bff.business.services.CompanyTraits
import br.com.alice.bff.business.services.ModuleService
import br.com.alice.bff.business.services.Modules
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanyStaffService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.foldEmptyResponse
import br.com.alice.common.foldResponse
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.BUSINESS_PLATFORM_BFF_NAME
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyStaffAccessLevel
import io.ktor.http.HttpStatusCode

class AuthController(
    private val authService: AuthService,
    private val companyStaffService: CompanyStaffService,
    private val companyService: CompanyService,
    private val moduleService: ModuleService,
    private val beneficiaryService: BeneficiaryService,
) : Controller() {

    suspend fun sendMagicLink(signInRequest: SendMagicLinkRequest): Response =
        withUnauthenticatedTokenWithKey(BUSINESS_PLATFORM_BFF_NAME) {
            authService
                .sendSignInLink(
                    email = signInRequest.email.trim().lowercase(),
                    url = signInRequest.url,
                )
                .foldEmptyResponse(HttpStatusCode.OK)
        }

    suspend fun signIn(signInRequest: SignInRequest) =
        withUnauthenticatedTokenWithKey(BUSINESS_PLATFORM_BFF_NAME) {
            val isAuthorized = authService.validateSignIn(
                signInRequest.idToken,
            )

            when (isAuthorized) {
                true -> Response(HttpStatusCode.OK)
                false -> Response(HttpStatusCode.Unauthorized)
            }
        }

    suspend fun getLogged() =
        coResultOf<StaffResponse, Throwable> {
            val staff = companyStaffService.getLatestByEmail(currentEmail()).get()
            val company = companyService.get(staff.companyId).get()
            val beneficiaryCount = beneficiaryService.countUniquePerPersonByCompanyId(company.id).get()

            staff.toResponse(
                company = company,
                beneficiaryCount = beneficiaryCount,
                modules = getCompanyModules(company, staff.accessLevel),
            )
        }.foldResponse()


    private suspend fun getCompanyModules(
        company: Company,
        accessLevel: CompanyStaffAccessLevel = CompanyStaffAccessLevel.ADMIN,
    ): List<Modules> =
        moduleService.getActiveModules(
            CompanyTraits(
                userMail = currentEmail(),
                companySize = company.companySize,
                companyCnpj = company.cnpj,
                companyId = company.id
            ),
            accessLevelStaff = accessLevel,
        )
}
