package br.com.alice.data.layer.pipelines.subjects

import br.com.alice.authentication.Claims
import br.com.alice.authentication.RootService
import br.com.alice.common.core.Model
import br.com.alice.common.core.Role
import br.com.alice.common.core.Subject
import br.com.alice.common.core.exceptions.AccessForbiddenException
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.data.layer.EHR_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.FILE_VAULT_ROOT_SERVICE_NAME
import br.com.alice.data.layer.MEMBERSHIP_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.CompanyStaff
import br.com.alice.data.layer.models.HealthCommunitySpecialistModel
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.models.SalesAgent
import br.com.alice.data.layer.models.SalesFirmStaff
import br.com.alice.data.layer.models.StaffModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.pipelines.subjects.builders.BatchExternalHealthProfessionalSubjectBuilder
import br.com.alice.data.layer.pipelines.subjects.builders.PersonSubjectBuilder
import br.com.alice.data.layer.subjects.EmailDomain
import br.com.alice.data.layer.subjects.Service
import br.com.alice.data.layer.subjects.Unauthenticated
import br.com.alice.data.layer.tables.CompanyStaffTable
import br.com.alice.data.layer.tables.HealthCommunitySpecialistTable
import br.com.alice.data.layer.tables.PersonTable
import br.com.alice.data.layer.tables.SalesAgentTable
import br.com.alice.data.layer.tables.SalesFirmStaffTable
import br.com.alice.data.layer.tables.StaffTable

internal object SubjectBuilder {

    suspend fun subject(factory: DatabasePipelineFactory, claims: Claims): Subject {
        val id = claims.aliceId

        return when (claims.type) {
            "Person" -> {
                val localPipeline = factory.get<PersonModel, PersonTable>(withAuthorizationPipeline = false)
                val person = localPipeline.get(id.toPersonId()).get()

                PersonSubject(
                    id = person.id,
                    person = person,
                )
            }

            "Staff" -> {
                val localPipeline = factory.get<StaffModel, StaffTable>(withAuthorizationPipeline = false)
                localPipeline.get(id.toUUID()).get().takeIf { it.active } ?: throwException(claims)
            }

            CompanyStaff::class.simpleName -> {
                val localPipeline = factory.get<CompanyStaff, CompanyStaffTable>(withAuthorizationPipeline = false)
                localPipeline.get(id.toUUID()).get().takeIf { it.active } ?: throwException(claims)
            }

            "HealthCommunitySpecialist" -> {
                val localPipeline =
                    factory.get<HealthCommunitySpecialistModel, HealthCommunitySpecialistTable>(
                        withAuthorizationPipeline = false
                    )
                localPipeline.get(id.toUUID()).get().takeIf { it.active } ?: throwException(claims)
            }

            Unauthenticated::class.simpleName -> {
                Unauthenticated(id)
            }

            Service::class.simpleName -> {
                Service(id)
            }

            EmailDomain::class.simpleName -> {
                EmailDomain(id)
            }

            SalesFirmStaff::class.simpleName -> {
                val localPipeline =
                    factory.get<SalesFirmStaff, SalesFirmStaffTable>(withAuthorizationPipeline = false)
                localPipeline.get(id.toUUID()).get().takeIf { it.active } ?: throwException(claims)
            }

            SalesAgent::class.simpleName -> {
                val localPipeline =
                    factory.get<SalesAgent, SalesAgentTable>(withAuthorizationPipeline = false)
                localPipeline.get(id.toUUID()).get().takeIf { it.additionalInfo?.hasPortalAccess == true }
                    ?: throwException(claims)
            }

            else -> throwException(claims)
        }
    }

    suspend fun <M : Model> enrichSubject(
        factory: DatabasePipelineFactory,
        subject: Subject,
        resource: M?,
        rootService: RootService
    ): Subject =
        when (rootService.name) {
            EHR_API_ROOT_SERVICE_NAME -> enrichEhrSubject(factory, subject, resource, rootService)
            MEMBERSHIP_ROOT_SERVICE_NAME,
            FILE_VAULT_ROOT_SERVICE_NAME -> enrichMembershipSubject(factory, subject, resource, rootService)
            else -> subject
        }

    suspend fun <M : Model> enrichSubjects(
        factory: DatabasePipelineFactory,
        subject: Subject,
        resources: List<M>,
        rootService: RootService
    ): List<Pair<M, Subject>> =
        when (rootService.name) {
            EHR_API_ROOT_SERVICE_NAME -> enrichEhrSubjects(factory, subject, resources, rootService)
            MEMBERSHIP_ROOT_SERVICE_NAME,
            FILE_VAULT_ROOT_SERVICE_NAME -> enrichMembershipSubjects(factory, subject, resources, rootService)
            else -> resources.map { it to subject }
        }

    private suspend fun <M : Model> enrichEhrSubject(
        factory: DatabasePipelineFactory,
        subject: Subject,
        resource: M?,
        rootService: RootService
    ): Subject {
        if (rootService.name != EHR_API_ROOT_SERVICE_NAME) return subject
        if (subject !is StaffModel) return subject

        val staffSubject = StaffSubject(subject)

        return when {
            subject.isAssistanceCare() || subject.isDigitalScreeningNurse() -> DigitalCareProfessionalSubject(subject)
            subject.isCareCoord() -> CareCoordinationSubject(subject)
            subject.isOnSiteProfessional() -> OnSiteProfessionalSubject(subject)
            subject.isFromMultiTeam() -> multiTeamSubject(factory, staffSubject, resource)
            subject.isChiefRisk() -> ChiefRiskSubject(subject)
            subject.isHealthProfessionalOrNavigator() -> InternalHealthProfessionalSubject(subject)
            subject.isProductTechHealth() -> ProdTechHealthSubject(subject)
            subject.role == Role.HEALTH_OPS -> HealthOpsSubject(subject)
            subject.isMedEx() -> HealthAuditSubject(subject)
            else -> StaffSubject(subject)
        }
    }

    private suspend fun <M : Model> multiTeamSubject(
        factory: DatabasePipelineFactory,
        staffSubject: StaffSubject,
        resource: M?
    ): Subject = BatchExternalHealthProfessionalSubjectBuilder(factory, staffSubject)
        .build(listOf(resource)).first().second

    private suspend fun <M : Model> enrichMembershipSubject(
        factory: DatabasePipelineFactory,
        subject: Subject,
        resource: M?,
        rootService: RootService
    ): Subject {
        val shouldUseBuilder =
            (rootService.name == MEMBERSHIP_ROOT_SERVICE_NAME || rootService.name == FILE_VAULT_ROOT_SERVICE_NAME)
        if (!shouldUseBuilder) return subject
        if (subject !is PersonSubject) return subject

        return PersonSubjectBuilder(factory, subject).build(resource)
    }

    private suspend fun <M : Model> enrichEhrSubjects(
        factory: DatabasePipelineFactory,
        subject: Subject,
        resources: List<M>,
        rootService: RootService
    ): List<Pair<M, Subject>> =
        if (rootService.name != EHR_API_ROOT_SERVICE_NAME) resources.map { it to subject }
        else if (subject !is StaffModel) resources.map { it to subject }
        else resources.map { resource ->
            resource to enrichSubject(factory, subject, resource, rootService)
        }

    private suspend fun <M : Model> enrichMembershipSubjects(
        factory: DatabasePipelineFactory,
        subject: Subject, resources: List<M>, rootService: RootService
    ): List<Pair<M, Subject>> {
        val shouldUseBuilder =
            (rootService.name == MEMBERSHIP_ROOT_SERVICE_NAME || rootService.name == FILE_VAULT_ROOT_SERVICE_NAME)

        return if (resources.isEmpty()) emptyList()
        else if (!shouldUseBuilder) resources.map { it to subject }
        else if (subject !is PersonSubject) resources.map { it to subject }
        else {
            val builder = PersonSubjectBuilder(factory, subject)
            val personSubject = builder.build(resources.first())

            resources.map { resource -> resource to personSubject }
        }
    }

    private fun throwException(claims: Claims): Subject =
        throw AccessForbiddenException(
            code = "invalid_claims",
            message = "Unexpected claim of type ${claims.type}"
        )
}
