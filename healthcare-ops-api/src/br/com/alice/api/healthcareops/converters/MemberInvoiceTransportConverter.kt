package br.com.alice.api.healthcareops.converters

import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.yearMonthFormatter
import br.com.alice.data.layer.models.CreateMemberInvoiceRequest
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.InvoicePaymentResponse
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoiceResponse
import br.com.alice.data.layer.models.MemberInvoicesResponse
import java.time.LocalDate


object MemberInvoiceResponseConverter {
    fun convert(invoicePayment: InvoicePayment) = InvoicePaymentResponse(
        amount = invoicePayment.amount,
        approvedAt = invoicePayment.approvedAt,
        status = invoicePayment.status,
        method = invoicePayment.method,
        canceledReason = invoicePayment.canceledReason,
        externalId = invoicePayment.externalId,
        source = invoicePayment.source,
        failReason = invoicePayment.failReason,
        id = invoicePayment.id,
        paymentDetail = invoicePayment.paymentDetail
    )

    fun convert(memberInvoices: List<MemberInvoice>): MemberInvoicesResponse {
        val invoicesResponse =
            memberInvoices.map { memberInvoice -> convert(memberInvoice) }.sortedBy { it.referenceDate }
        return MemberInvoicesResponse(invoicesResponse)
    }

    fun convert(memberInvoice: MemberInvoice) = MemberInvoiceResponse(
        id = memberInvoice.id.toString(),
        referenceDate = memberInvoice.referenceDate,
        status = memberInvoice.status,
        paidAt = memberInvoice.paidAt,
        totalAmount = memberInvoice.totalAmount,
        dueDate = memberInvoice.dueDate.toString(),
        payments = memberInvoice.invoicePayments.orEmpty().map { convert(it) },
        invoiceItems = memberInvoice.invoiceItems?.map { InvoiceItemsConverter.convert(it) },
        type = memberInvoice.type,

        )
}

object MemberInvoiceRequestConverter {
    fun convert(createMemberInvoiceRequest: CreateMemberInvoiceRequest, member: Member) =
        MemberInvoice(
            personId = member.personId,
            memberId = member.id,
            totalAmount = createMemberInvoiceRequest.totalAmount,
            referenceDate = LocalDate.parse(createMemberInvoiceRequest.referenceDate, yearMonthFormatter),
            dueDate = LocalDate.parse(createMemberInvoiceRequest.dueDate).atEndOfTheDay()
        )
}

