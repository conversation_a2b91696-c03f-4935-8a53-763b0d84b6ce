package br.com.alice.business.metrics

import br.com.alice.business.metrics.BeneficiaryMetric.BeneficiaryCreationFlow.AOS
import br.com.alice.business.metrics.BeneficiaryMetric.BeneficiaryCreationFlow.HR_PORTAL
import br.com.alice.common.RangeUUID
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.metrics.Metric
import io.micrometer.core.instrument.Counter
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class BeneficiaryMetricTest {

    @BeforeTest
    fun setup() {
        mockkObject(logger)
        Metric.meterRegistry.meters.map { meter -> Metric.meterRegistry.remove(meter.id) }
    }

    @AfterTest
    fun clear() {
        unmockkAll()
    }

    @Test
    fun `#beneficiaryCreatedIncrement counts metric with expected correct label`() {
        val beneficiaryId = RangeUUID.generate()
        BeneficiaryMetric.beneficiaryCreatedIncrement(beneficiaryId, HR_PORTAL)
        BeneficiaryMetric.beneficiaryCreatedIncrement(beneficiaryId, AOS)
        BeneficiaryMetric.beneficiaryCreatedIncrement(beneficiaryId, AOS)

        val counterAos = Metric.meterRegistry.meters.find {
            it.id.name == "alice.beneficiary_created"
                    && it.id.getTag("flow") == "aos"
        } as Counter

        val counterHrPortal = Metric.meterRegistry.meters.find {
            it.id.name == "alice.beneficiary_created"
                    && it.id.getTag("flow") == "hr_portal"
        } as Counter

        assertThat(counterAos.count()).isEqualTo(2.0)
        assertThat(counterHrPortal.count()).isEqualTo(1.0)

        verify(exactly = 2) { logger.info("BENEFICIARY_CREATED_AOS_LOG_METRIC", any()) }
        verify(exactly = 1) { logger.info("BENEFICIARY_CREATED_HR_PORTAL_LOG_METRIC", any()) }
    }
}
