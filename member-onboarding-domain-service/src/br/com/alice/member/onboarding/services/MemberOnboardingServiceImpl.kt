package br.com.alice.member.onboarding.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MemberOnboarding
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType
import br.com.alice.data.layer.models.MemberOnboardingReferencedLink
import br.com.alice.data.layer.models.MemberOnboardingReferencedLinkModel
import br.com.alice.data.layer.services.MemberOnboardingDataService
import br.com.alice.featureconfig.client.FeatureConfigService
import br.com.alice.featureconfig.client.TrackPersonABService
import br.com.alice.member.onboarding.client.MemberOnboardingService
import br.com.alice.member.onboarding.model.OnboardingVersion
import br.com.alice.member.onboarding.notifier.MemberOnboardingCompletedEvent
import br.com.alice.member.onboarding.notifier.MemberOnboardingStepUpdatedEvent
import br.com.alice.member.onboarding.services.internal.MemberOnboardingCreationService
import br.com.alice.membership.model.events.UpdateAppStateRequestedEvent
import br.com.alice.membership.model.events.UpdateAppStateRequestedPayload
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.onFailure
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

const val MEMBER_ONBOARDING_APP_STATE = "MEMBER_ONBOARDING_V2"
const val REDESIGN_UNIFIED_HEALTH_APP_STATE = "REDESIGN_UNIFIED_HEALTH"

class MemberOnboardingServiceImpl(
   private val memberOnboardingDataService: MemberOnboardingDataService,
   private val memberService: MemberService,
   private val memberOnboardingCreationService: MemberOnboardingCreationService,
   private val kafkaProducerService: KafkaProducerService,
   private val trackPersonABService: TrackPersonABService,
   private val featureConfigService: FeatureConfigService
): MemberOnboardingService {

   override suspend fun get(id: UUID) =
      memberOnboardingDataService.get(id)

   override suspend fun getByPersonId(personId: PersonId): Result<MemberOnboarding, Throwable> =
      memberOnboardingDataService.findOne {
         where { this.personId.eq(personId) }
            .orderBy { this.createdAt }
            .sortOrder { SortOrder.Descending }
      }

   override suspend fun getByPersonIdAndCompleted(personId: PersonId, completed: Boolean): Result<MemberOnboarding, Throwable> =
      memberOnboardingDataService.findOne {
         where {
            this.personId.eq(personId)
               .and(this.completed.eq(completed))
         }
            .orderBy { this.createdAt }
            .sortOrder { SortOrder.Descending }
      }

   override suspend fun getByPersonIdCheckingSteps(personId: PersonId, isMemberActive: Boolean): Result<MemberOnboarding, Throwable> =
      getByPersonId(personId).flatMap { memberOnboarding ->
         unlockStepOrCompleteOnboarding(memberOnboarding, isMemberActive)
      }

   override suspend fun updateStepStatus(
      memberOnboardingId: UUID,
      stepType: MemberOnboardingStepType,
      stepStatus: MemberOnboardingStepStatus
   ) =
      get(memberOnboardingId).flatMap { memberOnboarding ->
         setStepStatus(memberOnboarding, stepType, stepStatus).let { stepsToUpdate ->
            memberOnboarding.copy(steps = stepsToUpdate).let {
               if (it.canUnblockFinalStep) {
                  unblockFinalStep(it)
               } else {
                  updateOrFinish(it)
               }
            }
         }
      }

   private suspend fun updateOrFinish(memberOnboarding: MemberOnboarding) =
      if (memberOnboarding.isCompleted) {
         memberService.getCurrent(memberOnboarding.personId)
            .flatMap { member ->
               if (member.active) {
                  finishOnboarding(memberOnboarding)
               } else {
                  updateAndEmitEvent(memberOnboarding)
               }
            }
      } else {
         updateAndEmitEvent(memberOnboarding)
      }

   private suspend fun updateAndEmitEvent(memberOnboarding: MemberOnboarding) =
      update(memberOnboarding).then {
         kafkaProducerService.produce(MemberOnboardingStepUpdatedEvent(it))
      }

   override suspend fun createMemberOnboardingIfNecessary(
      personId: PersonId
   ): Result<Boolean, Throwable> {
         val memberOnboarding = getByPersonId(personId).getOrNull()

         return memberOnboardingCreationService.createIfNecessary(personId, memberOnboarding)
      }

   override suspend fun addReferencedLink(
      memberOnboardingId: UUID,
      referencedLinks: List<MemberOnboardingReferencedLink>,
      shouldOverride: Boolean
   ): Result<MemberOnboarding, Throwable> =
      get(memberOnboardingId)
         .flatMap { saved ->
            update(
               saved.copy(
                  referencedLinks = referencedLinks.getReferencedLinks(
                     previousLinks = saved.referencedLinks,
                     shouldOverride = shouldOverride
                  )
               )
            )
         }

   override suspend fun findAllPaginatedByCompleted(completed: Boolean, offset: Int, limit: Int) =
      memberOnboardingDataService.find {
         where {
            this.completed.eq(completed)
         }
            .orderBy { this.createdAt }
            .sortOrder { SortOrder.Ascending }
            .offset { offset }.limit { limit }
      }

   override suspend fun findAllByReferencedLinkModelPaginated(offset: Int, limit: Int, model: MemberOnboardingReferencedLinkModel) =
      memberOnboardingDataService.find {
         where {
            this.referencedLinkModel.eq(model)
         }
            .orderBy { this.createdAt }
            .sortOrder { SortOrder.Ascending }
            .offset { offset }.limit { limit }
      }

   override suspend fun findAllPaginated(offset: Int, limit: Int) =
      memberOnboardingDataService.find {
         orderBy { this.createdAt }
            .sortOrder { SortOrder.Ascending }
            .offset { offset }.limit { limit }
      }

   override suspend fun findByIds(ids: List<UUID>) =
      memberOnboardingDataService.find {
         where {
            this.id.inList(ids)
         }
      }

   override suspend fun delete(model: MemberOnboarding) =
      memberOnboardingDataService.delete(model)

   override suspend fun deleteList(models: List<MemberOnboarding>) =
      memberOnboardingDataService.deleteList(models)

   override suspend fun getOnboardingVersion(personId: PersonId): Result<OnboardingVersion, Throwable> =
      getFeatureConfigABTest("onboarding_version_ab_test").flatMap { featureConfig ->
         trackPersonABService.findOne(
            personId = personId,
            featureFlagId = featureConfig.id
         ).map {
            OnboardingVersion.valueOf(it.abPath)
         }.coFoldNotFound {
            OnboardingVersion.V1.success()
         }
      }

   override suspend fun getAliceInfoVersion(personId: PersonId): Result<Int, Throwable> =
      getFeatureConfigABTest("alice_info_onboarding_version_ab_test")
         .flatMap { featureConfig ->
            trackPersonABService.findOne(personId, featureConfig.id)
               .map { it.abPath.toInt() }
         }.onFailure {
            logger.error("Error getting Alice Info onboarding version", it)
         }

   override suspend fun getByPersonIds(personIds: List<PersonId>): Result<List<MemberOnboarding>, Throwable> =
      memberOnboardingDataService.find {
         where {
            this.personId.inList(personIds)
         }
      }

   suspend fun unlockStepOrCompleteOnboarding(
      memberOnboarding: MemberOnboarding,
      isMemberActive: Boolean
   ): Result<MemberOnboarding, Throwable> {
      if (!memberOnboarding.completed) {
         if (memberOnboarding.canUnblockFinalStep) {
            return unblockFinalStep(memberOnboarding)
         } else if(memberOnboarding.isCompleted && isMemberActive) {
            return finishOnboarding(memberOnboarding)
         }
      }

      return memberOnboarding.success()
   }

   private suspend fun update(memberOnboarding: MemberOnboarding) =
      memberOnboardingDataService.update(memberOnboarding).then {
         kafkaProducerService.produce(
            UpdateAppStateRequestedEvent(
               UpdateAppStateRequestedPayload(
                  personId = memberOnboarding.personId,
                  appState = MEMBER_ONBOARDING_APP_STATE
               )
            )
         )
         kafkaProducerService.produce(
            UpdateAppStateRequestedEvent(
               UpdateAppStateRequestedPayload(
                  personId = memberOnboarding.personId,
                  appState = REDESIGN_UNIFIED_HEALTH_APP_STATE
               )
            )
         )
      }

   private suspend fun unblockFinalStep(memberOnboarding: MemberOnboarding) =
      setStepStatus(
         memberOnboarding = memberOnboarding,
         stepType = MemberOnboardingStepType.CONCLUSION,
         stepStatus = MemberOnboardingStepStatus.PENDING
      ).let { steps ->
         update(memberOnboarding.copy(steps = steps)).then { onboarding ->
            kafkaProducerService.produce(MemberOnboardingStepUpdatedEvent(onboarding))
         }
      }

   private suspend fun finishOnboarding(memberOnboarding: MemberOnboarding) =
      update(memberOnboarding.copy(completed = true)).then {
         kafkaProducerService.produce(MemberOnboardingCompletedEvent(it))
      }

   private suspend fun getFeatureConfigABTest(abKey: String) =
      featureConfigService.getByNamespaceAndKey(FeatureNamespace.MEMBER_ONBOARDING, abKey)


   private fun setStepStatus(
      memberOnboarding: MemberOnboarding,
      stepType: MemberOnboardingStepType,
      stepStatus: MemberOnboardingStepStatus
   ) =
      memberOnboarding.steps.map { step ->
         if (step.templateType == stepType) {
            step.copy(status = stepStatus, updatedAt = LocalDateTime.now())
         } else step
      }
   
   private fun List<MemberOnboardingReferencedLink>.getReferencedLinks(
      previousLinks: List<MemberOnboardingReferencedLink>,
      shouldOverride: Boolean
   ) =
      if(!shouldOverride) {
         val newLinks = this.filter { link ->  previousLinks.none { it.id == link.id } }
         previousLinks + newLinks
      } else {
         this
      }
}
