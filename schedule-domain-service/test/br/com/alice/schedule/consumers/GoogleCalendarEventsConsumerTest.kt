package br.com.alice.schedule.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ExternalEventTransparency
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.schedule.model.events.FetchInstancesForRecurrentEventsType
import br.com.alice.schedule.model.events.GoogleCalendarEventCreationRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarEventNotificationEvent
import br.com.alice.schedule.model.events.GoogleCalendarEventsFromRecurrenceRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarFetchInstancesForRecurrentEventsEvent
import br.com.alice.schedule.model.events.GoogleCalendarFetchInstancesForRecurrentEventsForPageEvent
import br.com.alice.schedule.model.events.GoogleCalendarFinishedEventsQueryEvent
import br.com.alice.schedule.model.events.GoogleCalendarQueryEventsEvent
import br.com.alice.schedule.model.events.GoogleCalendarRecurringEventCreationRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarSynchronizationRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarWebhookSubscriptionRequestedEvent
import br.com.alice.schedule.model.events.RenewGoogleCalendarWebhooksRequestedEvent
import br.com.alice.schedule.model.googlecalendar.GoogleCalendarEventPayload
import br.com.alice.schedule.model.googlecalendar.GoogleCalendarEventWebhookNotification
import br.com.alice.schedule.model.googlecalendar.GoogleCalendarEventsQueryFilters
import br.com.alice.schedule.services.GoogleCalendarEventServiceImpl
import br.com.alice.schedule.services.SchedulePreferenceServiceImpl
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test

class GoogleCalendarEventsConsumerTest : ConsumerTest() {

    private val googleCalendarEventService: GoogleCalendarEventServiceImpl = mockk()
    private val schedulePreferenceService: SchedulePreferenceServiceImpl = mockk()

    private val consumer = GoogleCalendarEventsConsumer(
        googleCalendarEventService,
        schedulePreferenceService
    )

    private val staffId = RangeUUID.generate()
    private val schedulePreference =
        TestModelFactory.buildSchedulePreference(staffId = staffId, weeklyHours = 40, intervalBetweenEvents = 10)

    private val queryFilters = GoogleCalendarEventsQueryFilters(
        calendarId = "primary",
        showDeleted = true,
        maxResults = 10,
        singleEvents = false,
        syncToken = null,
        pageToken = null,
    )

    @Test
    fun `#createGoogleCalendarEvent should call google calendar event service create or update event`() =
        runBlocking {
            withFeatureFlag(
                FeatureNamespace.SCHEDULE,
                "should_use_temp_consumer_to_process_calendar_events",
                false
            ) {
                val eventPayload = GoogleCalendarEventPayload(
                    status = "confirmed",
                    startTime = LocalDateTime.now(),
                    endTime = LocalDateTime.now(),
                    id = "id",
                    externalUpdatedAt = LocalDateTime.now(),
                    transparency = ExternalEventTransparency.BUSY,
                )

                val event = GoogleCalendarEventCreationRequestedEvent(
                    eventPayload, staffId
                )
                val schedulePreference =
                    TestModelFactory.buildSchedulePreference(staffId = event.payload.staffId)
                        .copy(hasStaffSchedules = true)

                coEvery { schedulePreferenceService.getByStaffId(event.payload.staffId) } returns schedulePreference

                coEvery {
                    googleCalendarEventService.createOrUpdateEvent(
                        event.payload.event,
                        event.payload.staffId
                    )
                } returns TestModelFactory.buildExternalCalendarEvent()

                val result = consumer.createGoogleCalendarEvent(event)

                assertThat(result).isSuccess()

                coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
                coVerifyOnce { googleCalendarEventService.createOrUpdateEvent(any(), any()) }
            }
        }

    @Test
    fun `#createGoogleCalendarEvent should not call google calendar event service create or update event when staff does not have staff schedules`() =
        runBlocking {
            withFeatureFlag(
                FeatureNamespace.SCHEDULE,
                "should_use_temp_consumer_to_process_calendar_events",
                false
            ) {
                val eventPayload = GoogleCalendarEventPayload(
                    status = "confirmed",
                    startTime = LocalDateTime.now(),
                    endTime = LocalDateTime.now(),
                    id = "id",
                    externalUpdatedAt = LocalDateTime.now(),
                    transparency = ExternalEventTransparency.BUSY,
                )

                val event = GoogleCalendarEventCreationRequestedEvent(
                    eventPayload, staffId
                )
                val schedulePreference =
                    TestModelFactory.buildSchedulePreference(staffId = event.payload.staffId)

                coEvery { schedulePreferenceService.getByStaffId(event.payload.staffId) } returns schedulePreference

                val result = consumer.createGoogleCalendarEvent(event)

                assertThat(result).isSuccess()

                coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
                coVerifyNone { googleCalendarEventService.createOrUpdateEvent(any(), any()) }
            }
        }

    @Test
    fun `#createGoogleCalendarRecurringEvent should call google calendar event service create or update event`() =
        runBlocking {
            withFeatureFlag(
                FeatureNamespace.SCHEDULE,
                "should_use_temp_consumer_to_process_calendar_recurring_events",
                false
            ) {
                val eventPayload = GoogleCalendarEventPayload(
                    status = "confirmed",
                    startTime = LocalDateTime.now(),
                    endTime = LocalDateTime.now(),
                    id = "id",
                    externalUpdatedAt = LocalDateTime.now(),
                    transparency = ExternalEventTransparency.BUSY,
                )
                val event = GoogleCalendarRecurringEventCreationRequestedEvent(eventPayload, staffId)
                val schedulePreference =
                    TestModelFactory.buildSchedulePreference(staffId = event.payload.staffId)
                        .copy(hasStaffSchedules = false, isDoingFullSync = true)

                coEvery { schedulePreferenceService.getByStaffId(event.payload.staffId) } returns schedulePreference

                coEvery {
                    googleCalendarEventService.createOrUpdateEvent(
                        event.payload.event,
                        event.payload.staffId
                    )
                } returns TestModelFactory.buildExternalCalendarEvent()

                val result = consumer.createGoogleRecurringCalendarEvent(event)

                assertThat(result).isSuccess()

                coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
                coVerifyOnce { googleCalendarEventService.createOrUpdateEvent(any(), any()) }
            }
        }

    @Test
    fun `#createGoogleCalendarRecurringEvent should not call google calendar event service create or update event when staff does not have staff schedule`() =
        runBlocking {
            withFeatureFlag(
                FeatureNamespace.SCHEDULE,
                "should_use_temp_consumer_to_process_calendar_recurring_events",
                false
            ) {
                val eventPayload = GoogleCalendarEventPayload(
                    status = "confirmed",
                    startTime = LocalDateTime.now(),
                    endTime = LocalDateTime.now(),
                    id = "id",
                    externalUpdatedAt = LocalDateTime.now(),
                    transparency = ExternalEventTransparency.BUSY,
                )
                val event = GoogleCalendarRecurringEventCreationRequestedEvent(eventPayload, staffId)
                val schedulePreference =
                    TestModelFactory.buildSchedulePreference(staffId = event.payload.staffId)

                coEvery { schedulePreferenceService.getByStaffId(event.payload.staffId) } returns schedulePreference

                val result = consumer.createGoogleRecurringCalendarEvent(event)

                assertThat(result).isSuccess()

                coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
                coVerifyNone { googleCalendarEventService.createOrUpdateEvent(any(), any()) }
            }
        }

    @Test
    fun `#synchronizeGoogleCalendar should call google calendar event service synchronizeGoogleCalendar`() =
        runBlocking<Unit> {
            val event = GoogleCalendarSynchronizationRequestedEvent(
                schedulePreference
            )

            coEvery {
                googleCalendarEventService.synchronizeGoogleCalendar(
                    schedulePreference
                )
            } returns true

            val result = consumer.synchronizeGoogleCalendar(event)

            assertThat(result).isSuccess()
        }

    @Test
    fun `#createOrUpdateEventFromWebhookNotification should call google calendar event service createOrUpdateEventFromCalendarWebhook`() =
        runBlocking<Unit> {
            val eventPayload = GoogleCalendarEventWebhookNotification(
                channelId = staffId,
                resourceId = "eventId",
                resourceUri = "google.com/events",
            )

            val event = GoogleCalendarEventNotificationEvent(
                eventPayload,
            )

            coEvery {
                googleCalendarEventService.createOrUpdateEventFromCalendarWebhook(
                    eventPayload
                )
            } returns true

            val result = consumer.createOrUpdateEventFromWebhookNotification(event)

            assertThat(result).isSuccess()
        }

    @Test
    fun `#createWebhookChannelToReceiveEventUpdates should call google calendar event service registerWebhookChannel`() =
        runBlocking<Unit> {
            val event = GoogleCalendarWebhookSubscriptionRequestedEvent(
                schedulePreference
            )

            coEvery {
                googleCalendarEventService.registerWebhookChannel(
                    schedulePreference
                )
            } returns schedulePreference

            val result = consumer.createWebhookChannelToReceiveEventUpdates(event)

            assertThat(result).isSuccess()
        }

    @Test
    fun `#processGoogleCalendarFinishedEventsQueryEvent should call google calendar event service updateSchedulePreferenceWithLastQueryInformation`() =
        runBlocking<Unit> {
            val date = LocalDateTime.now()
            val event = GoogleCalendarFinishedEventsQueryEvent(
                staffId = staffId,
                lastUpdatedTime = date,
                nextSyncToken = "test"
            )

            coEvery {
                googleCalendarEventService.updateSchedulePreferenceWithLastQueryInformation(
                    staffId,
                    date,
                    "test"
                )
            } returns true

            val result = consumer.processGoogleCalendarFinishedEventsQueryEvent(event)

            assertThat(result).isSuccess()
        }

    @Test
    fun `#queryGoogleCalendarEvents should call google calendar event service queryGoogleCalendarEvents`() =
        runBlocking<Unit> {
            val event = GoogleCalendarQueryEventsEvent(
                staffId = staffId,
                queryFilters = queryFilters,
                refreshToken = "test"
            )

            coEvery {
                googleCalendarEventService.queryGoogleCalendarEvents(
                    queryFilters,
                    staffId,
                    "test"
                )
            } returns true

            val result = consumer.queryGoogleCalendarEvents(event)

            assertThat(result).isSuccess()
        }

    @Test
    fun `#renewWebhookChannelsCloseToExpiration should call google calendar event service renewWebhookChannelsCloseToExpiration`() =
        runBlocking<Unit> {
            val date = LocalDate.now()
            val event = RenewGoogleCalendarWebhooksRequestedEvent(
                date
            )

            coEvery {
                googleCalendarEventService.renewWebhookChannelsCloseToExpiration(
                    date,
                )
            } returns true

            val result = consumer.renewWebhookChannelsCloseToExpiration(event)

            assertThat(result).isSuccess()
        }

    @Test
    fun `#queryGoogleCalendarEventsForRecurrence should call google calendar event service queryGoogleCalendarEventsForRecurrence`() =
        runBlocking<Unit> {
            val event = GoogleCalendarEventsFromRecurrenceRequestedEvent(
                staffId = staffId,
                queryFilters = queryFilters,
            )

            coEvery {
                googleCalendarEventService.queryGoogleCalendarEventsForRecurrence(
                    queryFilters,
                    staffId,
                )
            } returns true

            val result = consumer.queryGoogleCalendarEventsForRecurrence(event)

            assertThat(result).isSuccess()
        }

    @Test
    fun `#fetchInstancesForRecurrentEvents should call google calendar event service getFutureGoogleCalendarEventsActiveRecurrencesForOneDay`() =
        runBlocking<Unit> {
            val date = LocalDate.now()
            val event = GoogleCalendarFetchInstancesForRecurrentEventsEvent(
                date
            )

            coEvery {
                googleCalendarEventService.getFutureGoogleCalendarEventsActiveRecurrencesForOneDay(
                    date,
                )
            } returns true

            val result = consumer.fetchInstancesForRecurrentEvents(event)

            assertThat(result).isSuccess()
        }

    @Test
    fun `#fetchInstancesForRecurrentEventsForPage should call google calendar event service getFutureGoogleCalendarEventsActiveRecurrencesForOneDayForPage`() =
        runBlocking<Unit> {
            val date = LocalDate.now()
            val event = GoogleCalendarFetchInstancesForRecurrentEventsForPageEvent(
                date,
                0,
                FetchInstancesForRecurrentEventsType.FOR_WEEKDAY_WITH_UNTIL_DATE,
            )

            coEvery {
                googleCalendarEventService.getFutureGoogleCalendarEventsActiveRecurrencesForOneDayForPage(
                    date,
                    0,
                    FetchInstancesForRecurrentEventsType.FOR_WEEKDAY_WITH_UNTIL_DATE,
                )
            } returns true

            val result = consumer.fetchInstancesForRecurrentEventsForPage(event)

            assertThat(result).isSuccess()
        }
}
