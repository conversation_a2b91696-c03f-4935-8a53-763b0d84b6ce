package br.com.alice.member.onboarding.services.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType
import br.com.alice.data.layer.models.MemberOnboardingFlowType
import br.com.alice.data.layer.models.Step
import br.com.alice.data.layer.models.TrackPersonAB
import br.com.alice.data.layer.services.MemberOnboardingDataService
import br.com.alice.featureconfig.client.TrackPersonABService
import br.com.alice.healthlogic.client.ClinicalOutcomeService
import br.com.alice.member.onboarding.notifier.MemberOnboardingCreatedEvent
import br.com.alice.membership.client.onboarding.HealthDeclarationForm
import br.com.alice.membership.model.events.UpdateAppStateRequestedEvent
import br.com.alice.membership.model.events.UpdateAppStateRequestedPayload
import br.com.alice.person.br.com.alice.person.model.MemberCurrentAndPrevious
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class MemberOnboardingCreationServiceTest {
    private val memberOnboardingDataService: MemberOnboardingDataService = mockk()
    private val memberService: MemberService = mockk()
    private val personService: PersonService = mockk()
    private val healthDeclarationService: HealthDeclarationForm = mockk()
    private val trackPersonABService: TrackPersonABService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val aliceInfoABTestService: AliceInfoABTestService = mockk()
    private val clinicalOutcomeService: ClinicalOutcomeService = mockk()

    private val memberOnboardingCreationService = MemberOnboardingCreationService(
        memberOnboardingDataService,
        memberService,
        personService,
        healthDeclarationService,
        trackPersonABService,
        kafkaProducerService,
        aliceInfoABTestService,
        clinicalOutcomeService
    )

    private val mockNow = LocalDateTime.parse("2024-08-20T10:00:00")
    private val personId = PersonId()
    private val person = TestModelFactory.buildPerson(personId = personId, dateOfBirth = LocalDateTime.parse("2022-01-01T11:00:00"))
    private val childSteps = listOf(
        Step(
            templateType = MemberOnboardingStepType.COVER,
            status = MemberOnboardingStepStatus.PENDING,
            updatedAt = mockNow
        ),
        Step(
            templateType = MemberOnboardingStepType.CONCLUSION,
            status = MemberOnboardingStepStatus.BLOCKED,
            updatedAt = mockNow
        )
    )
    private val memberOnboarding = TestModelFactory.buildMemberOnboarding(
        personId = personId,
        steps = childSteps,
        createdAt = mockNow,
        updatedAt = mockNow,
        referencedLinks = emptyList()
    )
    private val member = TestModelFactory.buildMember(personId = personId, createdAt = LocalDateTime.parse("2024-08-19T11:00:00"))
    private val healthDeclaration = TestModelFactory.buildHealthDeclaration(personId = personId, finishedAt = LocalDateTime.parse("2023-08-19T11:00:00"))
    private val memberCurrentAndPrevious = MemberCurrentAndPrevious(current = member)
    private val trackPerson = TrackPersonAB(
        personId = personId,
        featureConfigId = RangeUUID.generate(),
        abPath = "V1",
    )
    private val appStateOnboardingEvent = UpdateAppStateRequestedEvent(
        UpdateAppStateRequestedPayload(personId = personId, appState = "MEMBER_ONBOARDING_V2")
    )
    private val appStateRedesignUnifiedHealthEvent = UpdateAppStateRequestedEvent(
        UpdateAppStateRequestedPayload(personId = personId, appState = "REDESIGN_UNIFIED_HEALTH")
    )
    private val clinicalOutcomeRecord = TestModelFactory.buildClinicalOutcomeRecord(
        personId = personId,
        outcomeConfId = "bec90d96-4d94-4f66-9cb3-5a8956062600".toUUID(),
        addedAt = LocalDateTime.parse("2023-08-19T11:00:00")
    )

    @BeforeTest
    fun setup() {
        clearAllMocks()
    }

    @Test
    fun `#createIfNecessary create MemberOnboarding when is reactivation and was canceled after 1 year and has no previous healthDeclaration`() =
        mockRangeUuidAndDateTime(memberOnboarding.id, mockNow) {
            val memberCanceled = member.copy(canceledAt = LocalDateTime.parse("2021-01-01T10:00:00"))
            val memberCurrentAndPrevious = memberCurrentAndPrevious.copy(previous = memberCanceled)
            val event = MemberOnboardingCreatedEvent(memberOnboarding)

            coEvery { memberService.findMemberCurrentAndPrevious(personId) } returns memberCurrentAndPrevious.success()
            coEvery { healthDeclarationService.findFinishedByPerson(personId) } returns NotFoundException().failure()
            coEvery { personService.get(personId) } returns person.success()
            coEvery { memberOnboardingDataService.add(memberOnboarding) } returns memberOnboarding.success()
            coEvery { aliceInfoABTestService.createIfNecessary(personId) } returns Unit
            coEvery { kafkaProducerService.produce(event) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateOnboardingEvent) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()
            coEvery {
                trackPersonABService.findOrStartAb(
                    personId = personId,
                    namespace = FeatureNamespace.MEMBER_ONBOARDING,
                    key = "onboarding_version_ab_test",
                    defaultPath = "V1"
                )
            } returns trackPerson.success()

            val result = memberOnboardingCreationService.createIfNecessary(personId)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { memberService.findMemberCurrentAndPrevious(any()) }
            coVerifyOnce { healthDeclarationService.findFinishedByPerson(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { memberOnboardingDataService.add(any()) }
            coVerifyOnce { aliceInfoABTestService.createIfNecessary(any()) }
            coVerifyOnce { trackPersonABService.findOrStartAb(any(), any(), any(), any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#createIfNecessary create MemberOnboarding with completed steps when is reactivation and was canceled after 1 year and has previous healthDeclaration`() =
        mockRangeUuidAndDateTime(memberOnboarding.id, mockNow) {
            val memberCanceled = member.copy(canceledAt = LocalDateTime.parse("2021-01-01T10:00:00"))
            val memberCurrentAndPrevious = memberCurrentAndPrevious.copy(previous = memberCanceled)
            val person = person.copy(dateOfBirth = LocalDateTime.parse("1995-01-01T11:00:00"))
            val steps = listOf(
                Step(
                    templateType = MemberOnboardingStepType.VIDEO,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.COVER,
                    status = MemberOnboardingStepStatus.COMPLETED
                ),
                Step(
                    templateType = MemberOnboardingStepType.SCORE_MAGENTA,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.ALICE_INFO,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.CONCLUSION,
                    status = MemberOnboardingStepStatus.BLOCKED
                )
            )
            val memberOnboarding = memberOnboarding.copy(steps = steps, flowType = MemberOnboardingFlowType.ADULT)
            val event = MemberOnboardingCreatedEvent(memberOnboarding)

            coEvery { memberService.findMemberCurrentAndPrevious(personId) } returns memberCurrentAndPrevious.success()
            coEvery { healthDeclarationService.findFinishedByPerson(personId) } returns healthDeclaration.success()
            coEvery { personService.get(personId) } returns person.success()
            coEvery { memberOnboardingDataService.add(memberOnboarding) } returns memberOnboarding.success()
            coEvery { aliceInfoABTestService.createIfNecessary(personId) } returns Unit
            coEvery { kafkaProducerService.produce(event) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateOnboardingEvent) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()
            coEvery {
                trackPersonABService.findOrStartAb(
                    personId = personId,
                    namespace = FeatureNamespace.MEMBER_ONBOARDING,
                    key = "onboarding_version_ab_test",
                    defaultPath = "V1"
                )
            } returns trackPerson.success()

            val result = memberOnboardingCreationService.createIfNecessary(personId)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { memberService.findMemberCurrentAndPrevious(any()) }
            coVerifyOnce { healthDeclarationService.findFinishedByPerson(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { memberOnboardingDataService.add(any()) }
            coVerifyOnce { aliceInfoABTestService.createIfNecessary(any()) }
            coVerifyOnce { trackPersonABService.findOrStartAb(any(), any(), any(), any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#createIfNecessary create MemberOnboarding with completed steps when is reactivation and was canceled before 1 year and has previous healthDeclaration and score magenta`() =
        mockRangeUuidAndDateTime(memberOnboarding.id, mockNow) {
            val memberCanceled = member.copy(canceledAt = LocalDateTime.parse("2024-01-01T10:00:00"))
            val memberCurrentAndPrevious = memberCurrentAndPrevious.copy(previous = memberCanceled)
            val person = person.copy(dateOfBirth = LocalDateTime.parse("1995-01-01T11:00:00"))
            val steps = listOf(
                Step(
                    templateType = MemberOnboardingStepType.VIDEO,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.COVER,
                    status = MemberOnboardingStepStatus.COMPLETED
                ),
                Step(
                    templateType = MemberOnboardingStepType.SCORE_MAGENTA,
                    status = MemberOnboardingStepStatus.COMPLETED
                ),
                Step(
                    templateType = MemberOnboardingStepType.ALICE_INFO,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.CONCLUSION,
                    status = MemberOnboardingStepStatus.BLOCKED
                )
            )
            val memberOnboarding = memberOnboarding.copy(steps = steps, flowType = MemberOnboardingFlowType.ADULT)
            val event = MemberOnboardingCreatedEvent(memberOnboarding)

            coEvery { memberService.findMemberCurrentAndPrevious(personId) } returns memberCurrentAndPrevious.success()
            coEvery {
                clinicalOutcomeService.getByPersonIdAndOutcomeConfIdBeforeDate(
                    personId = personId,
                    outcomeConfId = clinicalOutcomeRecord.outcomeConfId,
                    date = mockNow
                )
            } returns clinicalOutcomeRecord.success()
            coEvery { healthDeclarationService.findFinishedByPerson(personId) } returns healthDeclaration.success()
            coEvery { personService.get(personId) } returns person.success()
            coEvery { memberOnboardingDataService.add(memberOnboarding) } returns memberOnboarding.success()
            coEvery { aliceInfoABTestService.createIfNecessary(personId) } returns Unit
            coEvery { kafkaProducerService.produce(event) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateOnboardingEvent) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()
            coEvery {
                trackPersonABService.findOrStartAb(
                    personId = personId,
                    namespace = FeatureNamespace.MEMBER_ONBOARDING,
                    key = "onboarding_version_ab_test",
                    defaultPath = "V1"
                )
            } returns trackPerson.success()

            val result = memberOnboardingCreationService.createIfNecessary(personId)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { memberService.findMemberCurrentAndPrevious(any()) }
            coVerifyOnce { clinicalOutcomeService.getByPersonIdAndOutcomeConfIdBeforeDate(any(), any(), any()) }
            coVerifyOnce { healthDeclarationService.findFinishedByPerson(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { memberOnboardingDataService.add(any()) }
            coVerifyOnce { aliceInfoABTestService.createIfNecessary(any()) }
            coVerifyOnce { trackPersonABService.findOrStartAb(any(), any(), any(), any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#createIfNecessary create MemberOnboarding when is reactivation and was canceled before 1 year and has previous healthDeclaration and score magenta with recent date`() =
        mockRangeUuidAndDateTime(memberOnboarding.id, mockNow) {
            val memberCanceled = member.copy(canceledAt = LocalDateTime.parse("2024-01-01T10:00:00"))
            val memberCurrentAndPrevious = memberCurrentAndPrevious.copy(previous = memberCanceled)
            val person = person.copy(dateOfBirth = LocalDateTime.parse("1995-01-01T11:00:00"))
            val steps = listOf(
                Step(
                    templateType = MemberOnboardingStepType.VIDEO,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.COVER,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.SCORE_MAGENTA,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.ALICE_INFO,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.CONCLUSION,
                    status = MemberOnboardingStepStatus.BLOCKED
                )
            )
            val memberOnboarding = memberOnboarding.copy(steps = steps, flowType = MemberOnboardingFlowType.ADULT)
            val event = MemberOnboardingCreatedEvent(memberOnboarding)
            val healthDeclaration = healthDeclaration.copy(finishedAt = LocalDateTime.parse("2024-08-01T11:00:00"))
            val clinicalOutcomeRecord = clinicalOutcomeRecord.copy(addedAt = LocalDateTime.parse("2024-08-01T11:00:00"))

            coEvery { memberService.findMemberCurrentAndPrevious(personId) } returns memberCurrentAndPrevious.success()
            coEvery {
                clinicalOutcomeService.getByPersonIdAndOutcomeConfIdBeforeDate(
                    personId = personId,
                    outcomeConfId = clinicalOutcomeRecord.outcomeConfId,
                    date = mockNow
                )
            } returns clinicalOutcomeRecord.success()
            coEvery { healthDeclarationService.findFinishedByPerson(personId) } returns healthDeclaration.success()
            coEvery { personService.get(personId) } returns person.success()
            coEvery { memberOnboardingDataService.add(memberOnboarding) } returns memberOnboarding.success()
            coEvery { aliceInfoABTestService.createIfNecessary(personId) } returns Unit
            coEvery { kafkaProducerService.produce(event) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateOnboardingEvent) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()
            coEvery {
                trackPersonABService.findOrStartAb(
                    personId = personId,
                    namespace = FeatureNamespace.MEMBER_ONBOARDING,
                    key = "onboarding_version_ab_test",
                    defaultPath = "V1"
                )
            } returns trackPerson.success()

            val result = memberOnboardingCreationService.createIfNecessary(personId)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { memberService.findMemberCurrentAndPrevious(any()) }
            coVerifyOnce { clinicalOutcomeService.getByPersonIdAndOutcomeConfIdBeforeDate(any(), any(), any()) }
            coVerifyOnce { healthDeclarationService.findFinishedByPerson(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { memberOnboardingDataService.add(any()) }
            coVerifyOnce { aliceInfoABTestService.createIfNecessary(any()) }
            coVerifyOnce { trackPersonABService.findOrStartAb(any(), any(), any(), any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#createIfNecessary create MemberOnboarding when is reactivation and was canceled before 1 year and has no previous healthDeclaration and score magenta`() =
        mockRangeUuidAndDateTime(memberOnboarding.id, mockNow) {
            val memberCanceled = member.copy(canceledAt = LocalDateTime.parse("2024-01-01T10:00:00"))
            val memberCurrentAndPrevious = memberCurrentAndPrevious.copy(previous = memberCanceled)
            val person = person.copy(dateOfBirth = LocalDateTime.parse("1995-01-01T11:00:00"))
            val steps = listOf(
                Step(
                    templateType = MemberOnboardingStepType.VIDEO,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.COVER,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.SCORE_MAGENTA,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.ALICE_INFO,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.CONCLUSION,
                    status = MemberOnboardingStepStatus.BLOCKED
                )
            )
            val memberOnboarding = memberOnboarding.copy(steps = steps, flowType = MemberOnboardingFlowType.ADULT)
            val event = MemberOnboardingCreatedEvent(memberOnboarding)

            coEvery { memberService.findMemberCurrentAndPrevious(personId) } returns memberCurrentAndPrevious.success()
            coEvery {
                clinicalOutcomeService.getByPersonIdAndOutcomeConfIdBeforeDate(
                    personId = personId,
                    outcomeConfId = clinicalOutcomeRecord.outcomeConfId,
                    date = mockNow
                )
            } returns NotFoundException().failure()
            coEvery { healthDeclarationService.findFinishedByPerson(personId) } returns NotFoundException().failure()
            coEvery { personService.get(personId) } returns person.success()
            coEvery { memberOnboardingDataService.add(memberOnboarding) } returns memberOnboarding.success()
            coEvery { aliceInfoABTestService.createIfNecessary(personId) } returns Unit
            coEvery { kafkaProducerService.produce(event) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateOnboardingEvent) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()
            coEvery {
                trackPersonABService.findOrStartAb(
                    personId = personId,
                    namespace = FeatureNamespace.MEMBER_ONBOARDING,
                    key = "onboarding_version_ab_test",
                    defaultPath = "V1"
                )
            } returns trackPerson.success()

            val result = memberOnboardingCreationService.createIfNecessary(personId)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { memberService.findMemberCurrentAndPrevious(any()) }
            coVerifyOnce { clinicalOutcomeService.getByPersonIdAndOutcomeConfIdBeforeDate(any(), any(), any()) }
            coVerifyOnce { healthDeclarationService.findFinishedByPerson(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { memberOnboardingDataService.add(any()) }
            coVerifyOnce { aliceInfoABTestService.createIfNecessary(any()) }
            coVerifyOnce { trackPersonABService.findOrStartAb(any(), any(), any(), any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#createIfNecessary does not create MemberOnboarding when is reactivation and has previous MemberOnboarding`() =
        mockRangeUuidAndDateTime(memberOnboarding.id, mockNow) {
            val memberCanceled = member.copy(canceledAt = LocalDateTime.parse("2024-01-01T10:00:00"))
            val memberCurrentAndPrevious = memberCurrentAndPrevious.copy(previous = memberCanceled)

            coEvery { memberService.findMemberCurrentAndPrevious(personId) } returns memberCurrentAndPrevious.success()

            val result = memberOnboardingCreationService.createIfNecessary(personId, memberOnboarding)

            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { memberService.findMemberCurrentAndPrevious(any()) }
            coVerifyNone { clinicalOutcomeService.getByPersonIdAndOutcomeConfIdBeforeDate(any(), any(), any()) }
            coVerifyNone { healthDeclarationService.findFinishedByPerson(any()) }
            coVerifyNone { aliceInfoABTestService.createIfNecessary(any()) }
            coVerifyNone { personService.get(any()) }
            coVerifyNone { memberOnboardingDataService.add(any()) }
        }

    @Test
    fun `#createIfNecessary create MemberOnboarding when is not reactivation and has no previous healthDeclaration`() =
        mockRangeUuidAndDateTime(memberOnboarding.id, mockNow) {
            val event = MemberOnboardingCreatedEvent(memberOnboarding)

            coEvery { memberService.findMemberCurrentAndPrevious(personId) } returns memberCurrentAndPrevious.success()
            coEvery { healthDeclarationService.findFinishedByPerson(personId) } returns NotFoundException().failure()
            coEvery { personService.get(personId) } returns person.success()
            coEvery { memberOnboardingDataService.add(memberOnboarding) } returns memberOnboarding.success()
            coEvery { aliceInfoABTestService.createIfNecessary(personId) } returns Unit
            coEvery { kafkaProducerService.produce(event) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateOnboardingEvent) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()
            coEvery {
                trackPersonABService.findOrStartAb(
                    personId = personId,
                    namespace = FeatureNamespace.MEMBER_ONBOARDING,
                    key = "onboarding_version_ab_test",
                    defaultPath = "V1"
                )
            } returns trackPerson.success()

            val result = memberOnboardingCreationService.createIfNecessary(personId)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { memberService.findMemberCurrentAndPrevious(any()) }
            coVerifyOnce { healthDeclarationService.findFinishedByPerson(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { memberOnboardingDataService.add(any()) }
            coVerifyOnce { aliceInfoABTestService.createIfNecessary(any()) }
            coVerifyOnce { trackPersonABService.findOrStartAb(any(), any(), any(), any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#createIfNecessary create MemberOnboarding with completed steps when is not reactivation and has previous healthDeclaration`() =
        mockRangeUuidAndDateTime(memberOnboarding.id, mockNow) {
            val steps = listOf(
                Step(
                    templateType = MemberOnboardingStepType.COVER,
                    status = MemberOnboardingStepStatus.COMPLETED
                ),
                Step(
                    templateType = MemberOnboardingStepType.CONCLUSION,
                    status = MemberOnboardingStepStatus.PENDING
                )
            )
            val memberOnboarding = memberOnboarding.copy(steps = steps)
            val event = MemberOnboardingCreatedEvent(memberOnboarding)

            coEvery { memberService.findMemberCurrentAndPrevious(personId) } returns memberCurrentAndPrevious.success()
            coEvery { healthDeclarationService.findFinishedByPerson(personId) } returns healthDeclaration.success()
            coEvery { personService.get(personId) } returns person.success()
            coEvery { memberOnboardingDataService.add(memberOnboarding) } returns memberOnboarding.success()
            coEvery { aliceInfoABTestService.createIfNecessary(personId) } returns Unit
            coEvery { kafkaProducerService.produce(event) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateOnboardingEvent) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()
            coEvery {
                trackPersonABService.findOrStartAb(
                    personId = personId,
                    namespace = FeatureNamespace.MEMBER_ONBOARDING,
                    key = "onboarding_version_ab_test",
                    defaultPath = "V1"
                )
            } returns trackPerson.success()

            val result = memberOnboardingCreationService.createIfNecessary(personId)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { memberService.findMemberCurrentAndPrevious(any()) }
            coVerifyOnce { healthDeclarationService.findFinishedByPerson(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { memberOnboardingDataService.add(any()) }
            coVerifyOnce { aliceInfoABTestService.createIfNecessary(any()) }
            coVerifyOnce { trackPersonABService.findOrStartAb(any(), any(), any(), any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#createIfNecessary create onboarding when isn't reactivation and has previous onboarding with date minor then member creation`() =
        mockRangeUuidAndDateTime(memberOnboarding.id, mockNow) {
            val memberCurrentAndPrevious = memberCurrentAndPrevious.copy(
                current = member.copy(createdAt = LocalDateTime.parse("2024-08-21T10:00:00"))
            )
            val event = MemberOnboardingCreatedEvent(memberOnboarding)

            coEvery { memberService.findMemberCurrentAndPrevious(personId) } returns memberCurrentAndPrevious.success()
            coEvery { healthDeclarationService.findFinishedByPerson(personId) } returns NotFoundException().failure()
            coEvery { personService.get(personId) } returns person.success()
            coEvery { memberOnboardingDataService.add(memberOnboarding) } returns memberOnboarding.success()
            coEvery { aliceInfoABTestService.createIfNecessary(personId) } returns Unit
            coEvery { kafkaProducerService.produce(event) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateOnboardingEvent) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()
            coEvery {
                trackPersonABService.findOrStartAb(
                    personId = personId,
                    namespace = FeatureNamespace.MEMBER_ONBOARDING,
                    key = "onboarding_version_ab_test",
                    defaultPath = "V1"
                )
            } returns trackPerson.success()

            val result = memberOnboardingCreationService.createIfNecessary(personId, memberOnboarding)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { memberService.findMemberCurrentAndPrevious(any()) }
            coVerifyOnce { healthDeclarationService.findFinishedByPerson(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { memberOnboardingDataService.add(any()) }
            coVerifyOnce { aliceInfoABTestService.createIfNecessary(any()) }
            coVerifyOnce { trackPersonABService.findOrStartAb(any(), any(), any(), any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#createIfNecessary create MemberOnboarding with completed steps when is reactivation, was canceled after 1 year and has previous onboarding with creation date minor then member creation`() =
        mockRangeUuidAndDateTime(memberOnboarding.id, mockNow) {
            val memberCanceled = member.copy(canceledAt = LocalDateTime.parse("2021-01-01T10:00:00"))
            val memberCurrentAndPrevious = memberCurrentAndPrevious.copy(
                current = member.copy(createdAt = LocalDateTime.parse("2024-08-21T10:00:00")),
                previous = memberCanceled
            )
            val person = person.copy(dateOfBirth = LocalDateTime.parse("1995-01-01T11:00:00"))
            val steps = listOf(
                Step(
                    templateType = MemberOnboardingStepType.VIDEO,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.COVER,
                    status = MemberOnboardingStepStatus.COMPLETED
                ),
                Step(
                    templateType = MemberOnboardingStepType.SCORE_MAGENTA,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.ALICE_INFO,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.CONCLUSION,
                    status = MemberOnboardingStepStatus.BLOCKED
                )
            )
            val memberOnboarding = memberOnboarding.copy(steps = steps, flowType = MemberOnboardingFlowType.ADULT)
            val event = MemberOnboardingCreatedEvent(memberOnboarding)

            coEvery { memberService.findMemberCurrentAndPrevious(personId) } returns memberCurrentAndPrevious.success()
            coEvery { healthDeclarationService.findFinishedByPerson(personId) } returns healthDeclaration.success()
            coEvery { personService.get(personId) } returns person.success()
            coEvery { memberOnboardingDataService.add(memberOnboarding) } returns memberOnboarding.success()
            coEvery { aliceInfoABTestService.createIfNecessary(personId) } returns Unit
            coEvery { kafkaProducerService.produce(event) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateOnboardingEvent) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()
            coEvery {
                trackPersonABService.findOrStartAb(
                    personId = personId,
                    namespace = FeatureNamespace.MEMBER_ONBOARDING,
                    key = "onboarding_version_ab_test",
                    defaultPath = "V1"
                )
            } returns trackPerson.success()

            val result = memberOnboardingCreationService.createIfNecessary(personId, memberOnboarding)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { memberService.findMemberCurrentAndPrevious(any()) }
            coVerifyOnce { healthDeclarationService.findFinishedByPerson(any()) }
            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { memberOnboardingDataService.add(any()) }
            coVerifyOnce { aliceInfoABTestService.createIfNecessary(any()) }
            coVerifyOnce { trackPersonABService.findOrStartAb(any(), any(), any(), any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#createIfNecessary does not create MemberOnboarding when has previous MemberOnboarding with date bigger than member creation`() =
        mockRangeUuidAndDateTime(memberOnboarding.id, mockNow) {
            coEvery { memberService.findMemberCurrentAndPrevious(personId) } returns memberCurrentAndPrevious.success()

            val result = memberOnboardingCreationService.createIfNecessary(personId, memberOnboarding)

            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { memberService.findMemberCurrentAndPrevious(any()) }
            coVerifyNone { healthDeclarationService.findFinishedByPerson(any()) }
            coVerifyNone { aliceInfoABTestService.createIfNecessary(any()) }
            coVerifyNone { personService.get(any()) }
            coVerifyNone { memberOnboardingDataService.add(any()) }
        }

    @Test
    fun `#createIfNecessary does not create MemberOnboarding when member was created before MemberOnboarding feature init date`() =
        mockRangeUuidAndDateTime(memberOnboarding.id, mockNow) {
            val memberCurrentAndPrevious = memberCurrentAndPrevious.copy(
                current = member.copy(createdAt = LocalDateTime.parse("2024-07-01T10:00:00"))
            )

            coEvery { memberService.findMemberCurrentAndPrevious(personId) } returns memberCurrentAndPrevious.success()

            val result = memberOnboardingCreationService.createIfNecessary(personId)

            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { memberService.findMemberCurrentAndPrevious(any()) }
            coVerifyNone {
                healthDeclarationService.findFinishedByPerson(any())
                personService.get(any())
                memberOnboardingDataService.add(any())
                kafkaProducerService.produce(any())
                aliceInfoABTestService.createIfNecessary(any())
            }
        }

    @Test
    fun `#createIfNecessary does not create MemberOnboarding when member brand is Duquesa`() =
        mockRangeUuidAndDateTime(memberOnboarding.id, mockNow) {
            val memberCurrentAndPrevious = memberCurrentAndPrevious.copy(
                current = member.copy(createdAt = LocalDateTime.parse("2024-07-01T10:00:00"), brand = Brand.DUQUESA)
            )

            coEvery { memberService.findMemberCurrentAndPrevious(personId) } returns memberCurrentAndPrevious.success()

            val result = memberOnboardingCreationService.createIfNecessary(personId)

            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { memberService.findMemberCurrentAndPrevious(any()) }
            coVerifyNone {
                healthDeclarationService.findFinishedByPerson(any())
                personService.get(any())
                memberOnboardingDataService.add(any())
                kafkaProducerService.produce(any())
                aliceInfoABTestService.createIfNecessary(any())
            }
        }
}
