package br.com.alice.hr.core.ioc

import br.com.alice.hr.core.ServiceConfig
import br.com.alice.hr.core.clients.interfaces.DataAPIClient
import br.com.alice.hr.core.clients.interfaces.AmazonS3Client
import br.com.alice.hr.core.clients.DataAPIClientDev
import br.com.alice.hr.core.clients.DataAPIClientImpl
import br.com.alice.hr.core.clients.AmazonS3ClientImpl
import org.koin.dsl.module


val ClientsModule = module(createdAtStart = true) {
    single<DataAPIClient> {
        if(ServiceConfig.isProduction)
            DataAPIClientImpl()
        else DataAPIClientDev()
    }

    single<AmazonS3Client> {AmazonS3ClientImpl()}
}