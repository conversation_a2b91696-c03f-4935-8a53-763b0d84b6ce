package br.com.alice.bff.business.controllers.v2

import br.com.alice.bff.business.ControllerTestHelper
import br.com.alice.bff.business.services.ModuleService
import br.com.alice.bff.business.services.Modules
import br.com.alice.bff.business.services.ProductCachedService
import br.com.alice.business.client.BeneficiaryCompiledViewService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanyStaffService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CompanyStaffAccessLevel
import br.com.alice.data.layer.models.FeatureNamespace
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class WhatsNewControllerTest: ControllerTestHelper() {
    private val companyStaffService: CompanyStaffService = mockk()
    private val companyService: CompanyService = mockk()
    private val modulesService: ModuleService = mockk()
    private val beneficiaryViewService: BeneficiaryCompiledViewService = mockk()
    private val subContractService: CompanySubContractService = mockk()
    private val productCachedService: ProductCachedService = mockk()

    val controller: WhatsNewController = WhatsNewController(
        companyStaffService,
        companyService,
        modulesService,
        beneficiaryViewService,
        subContractService,
        productCachedService
    )

    @BeforeTest
    override fun setup() {
        super.setup()

        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns LocalDate.of(2025, 9, 30)

        module.single { controller }
        coEvery { companyStaffService.getLatestByEmail(staff.email) } returns companyStaff
    }

    @AfterTest
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `#getWhatsNewAnnouncements should return 200 with ULTRA, SCORE MAGENTA and UTILIZATION JOURNEY`() = runBlocking {
        withFeatureFlag(FeatureNamespace.BUSINESS, "SHOW_ULTRA_MODAL", true) {
            val expected = listOf(
                WhatsNewAnnouncements.PRODUCT_ULTRA,
                WhatsNewAnnouncements.FEATURE_MAGENTA_TAB,
                WhatsNewAnnouncements.FEATURE_UTILIZATION_JOURNEY_TAB,
                WhatsNewAnnouncements.TOP_OF_MIND
            )
            val modulesEnabled = listOf(
                Modules.DATA_MODULE_MAGENTA_TAB,
                Modules.DATA_MODULE_JOURNEY_TAB
            )

            val products = listOf(
                TestModelFactory.buildProduct(title = "Ultra"),
                TestModelFactory.buildProduct(title = "Conforto")
            )

            coEvery { companyService.get(companyStaff.companyId) } returns company.success()
            coEvery { companyStaffService.getLatestByEmail(companyStaff.email) } returns companyStaff.success()
            coEvery { modulesService.getActiveModules(any()) } returns modulesEnabled

            coEvery { beneficiaryViewService.countByFilters(any()) } returns 35.success()
            coEvery { subContractService.findByCompanyId(company.id) } returns subContracts.success()
            coEvery { productCachedService.listProductsByCompanyAndSubcontract(company.id, subContracts.first().id) } returns products

            authenticatedAsCompanyStaff("id-token", companyStaff) {
                get(url = "/v2/whats_new") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expected)
                }
            }

        }
    }

    @Test
    fun `#getWhatsNewAnnouncements should return 200 without tabs announcements when staff is finance`() = runBlocking {
        withFeatureFlag(FeatureNamespace.BUSINESS, "SHOW_ULTRA_MODAL", true) {
            val staff = companyStaff.copy(accessLevel = CompanyStaffAccessLevel.FINANCE)

            val expected = listOf(
                WhatsNewAnnouncements.PRODUCT_ULTRA,
                WhatsNewAnnouncements.TOP_OF_MIND
            )

            val modulesEnabled = listOf(
                Modules.DATA_MODULE_MAGENTA_TAB,
                Modules.DATA_MODULE_JOURNEY_TAB
            )

            val products = listOf(
                TestModelFactory.buildProduct(title = "Ultra"),
                TestModelFactory.buildProduct(title = "Conforto")
            )

            coEvery { companyService.get(staff.companyId) } returns company.success()
            coEvery { companyStaffService.getLatestByEmail(staff.email) } returns staff.success()
            coEvery { modulesService.getActiveModules(any()) } returns modulesEnabled

            coEvery { beneficiaryViewService.countByFilters(any()) } returns 35.success()
            coEvery { subContractService.findByCompanyId(company.id) } returns subContracts.success()
            coEvery { productCachedService.listProductsByCompanyAndSubcontract(company.id, subContracts.first().id) } returns products

            authenticatedAsCompanyStaff("id-token", staff) {
                get(url = "/v2/whats_new") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expected)
                }
            }

        }
    }

    @Test
    fun `#getWhatsNewAnnouncements should return 200 without magenta`() = runBlocking {
        withFeatureFlag(FeatureNamespace.BUSINESS, "SHOW_ULTRA_MODAL", true) {
            val expected = listOf(
                WhatsNewAnnouncements.PRODUCT_ULTRA,
                WhatsNewAnnouncements.TOP_OF_MIND
            )
            val modulesEnabled = listOf(
                Modules.DATA_MODULE,
            )

            val products = listOf(
                TestModelFactory.buildProduct(title = "Ultra"),
                TestModelFactory.buildProduct(title = "Conforto")
            )

            coEvery { companyService.get(companyStaff.companyId) } returns company.success()
            coEvery { companyStaffService.getLatestByEmail(companyStaff.email) } returns companyStaff.success()
            coEvery { modulesService.getActiveModules(any()) } returns modulesEnabled

            coEvery { beneficiaryViewService.countByFilters(any()) } returns 35.success()
            coEvery { subContractService.findByCompanyId(company.id) } returns subContracts.success()
            coEvery { productCachedService.listProductsByCompanyAndSubcontract(company.id, subContracts.first().id) } returns products

            authenticatedAsCompanyStaff("id-token", companyStaff) {
                get(url = "/v2/whats_new") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expected)
                }
            }
        }
    }

    @Test
    fun `#getWhatsNewAnnouncements should return 200 without utilization journey`() = runBlocking {
        withFeatureFlag(FeatureNamespace.BUSINESS, "SHOW_ULTRA_MODAL", true) {
            val expected = listOf(
                WhatsNewAnnouncements.PRODUCT_ULTRA,
                WhatsNewAnnouncements.FEATURE_MAGENTA_TAB,
                WhatsNewAnnouncements.TOP_OF_MIND
            )
            val modulesEnabled = listOf(
                Modules.DATA_MODULE,
                Modules.DATA_MODULE_MAGENTA_TAB
            )

            val products = listOf(
                TestModelFactory.buildProduct(title = "Ultra"),
                TestModelFactory.buildProduct(title = "Conforto")
            )

            coEvery { companyService.get(companyStaff.companyId) } returns company.success()
            coEvery { companyStaffService.getLatestByEmail(companyStaff.email) } returns companyStaff.success()
            coEvery { modulesService.getActiveModules(any()) } returns modulesEnabled

            coEvery { beneficiaryViewService.countByFilters(any()) } returns 35.success()
            coEvery { subContractService.findByCompanyId(company.id) } returns subContracts.success()
            coEvery { productCachedService.listProductsByCompanyAndSubcontract(company.id, subContracts.first().id) } returns products

            authenticatedAsCompanyStaff("id-token", companyStaff) {
                get(url = "/v2/whats_new") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expected)
                }
            }
        }
    }

    @Test
    fun `#getWhatsNewAnnouncements should return 200 without ULTRA if ff is turned of`() = runBlocking {
        withFeatureFlag(FeatureNamespace.BUSINESS, "SHOW_ULTRA_MODAL", false) {
            val expected = listOf(
                WhatsNewAnnouncements.FEATURE_MAGENTA_TAB,
                WhatsNewAnnouncements.TOP_OF_MIND
            )
            val modulesEnabled = listOf(
                Modules.DATA_MODULE_MAGENTA_TAB,
            )

            val products = listOf(
                TestModelFactory.buildProduct(title = "Ultra"),
                TestModelFactory.buildProduct(title = "Conforto")
            )

            coEvery { companyService.get(companyStaff.companyId) } returns company.success()
            coEvery { companyStaffService.getLatestByEmail(companyStaff.email) } returns companyStaff.success()
            coEvery { modulesService.getActiveModules(any()) } returns modulesEnabled

            coEvery { beneficiaryViewService.countByFilters(any()) } returns 35.success()
            coEvery { subContractService.findByCompanyId(company.id) } returns subContracts.success()
            coEvery { productCachedService.listProductsByCompanyAndSubcontract(company.id, subContracts.first().id) } returns products

            authenticatedAsCompanyStaff("id-token", companyStaff) {
                get(url = "/v2/whats_new") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expected)
                }
            }

        }
    }

    @Test
    fun `#getWhatsNewAnnouncements should return 200 without ULTRA if company has less than 35 active members`() = runBlocking {
        withFeatureFlag(FeatureNamespace.BUSINESS, "SHOW_ULTRA_MODAL", true) {
            val expected = listOf(
                WhatsNewAnnouncements.FEATURE_MAGENTA_TAB,
                WhatsNewAnnouncements.TOP_OF_MIND
            )
            val modulesEnabled = listOf(
                Modules.DATA_MODULE_MAGENTA_TAB,
            )

            val products = listOf(
                TestModelFactory.buildProduct(title = "Ultra"),
                TestModelFactory.buildProduct(title = "Conforto")
            )

            coEvery { companyService.get(companyStaff.companyId) } returns company.success()
            coEvery { companyStaffService.getLatestByEmail(companyStaff.email) } returns companyStaff.success()
            coEvery { modulesService.getActiveModules(any()) } returns modulesEnabled

            coEvery { beneficiaryViewService.countByFilters(any()) } returns 30.success()
            coEvery { subContractService.findByCompanyId(company.id) } returns subContracts.success()
            coEvery { productCachedService.listProductsByCompanyAndSubcontract(company.id, subContracts.first().id) } returns products

            authenticatedAsCompanyStaff("id-token", companyStaff) {
                get(url = "/v2/whats_new") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expected)
                }
            }

        }
    }

    @Test
    fun `#getWhatsNewAnnouncements should return 200 without ULTRA if product is not new portfolio`() = runBlocking {
        withFeatureFlag(FeatureNamespace.BUSINESS, "SHOW_ULTRA_MODAL", true) {
            val expected = listOf(
                WhatsNewAnnouncements.FEATURE_MAGENTA_TAB,
                WhatsNewAnnouncements.TOP_OF_MIND
            )
            val modulesEnabled = listOf(
                Modules.DATA_MODULE_MAGENTA_TAB,
            )

            val products = listOf(
                TestModelFactory.buildProduct(title = "Apartamento", displayName = ""),
                TestModelFactory.buildProduct(title = "Conforto")
            )

            coEvery { companyService.get(companyStaff.companyId) } returns company.success()
            coEvery { companyStaffService.getLatestByEmail(companyStaff.email) } returns companyStaff.success()
            coEvery { modulesService.getActiveModules(any()) } returns modulesEnabled

            coEvery { beneficiaryViewService.countByFilters(any()) } returns 40.success()
            coEvery { subContractService.findByCompanyId(company.id) } returns subContracts.success()
            coEvery { productCachedService.listProductsByCompanyAndSubcontract(company.id, subContracts.first().id) } returns products

            authenticatedAsCompanyStaff("id-token", companyStaff) {
                get(url = "/v2/whats_new") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expected)
                }
            }
        }
    }

    @Test
    fun `#getWhatsNewAnnouncements should return SETTINGS_FEATURE news if has SETTINGS_MODULE module and flag is enabled`() = runBlocking {
        withFeatureFlag(FeatureNamespace.BUSINESS, "is_settings_feature_enabled", true) {
            val expected = listOf(
                WhatsNewAnnouncements.SETTINGS_FEATURE,
                WhatsNewAnnouncements.TOP_OF_MIND
            )
            val modulesEnabled = listOf(
                Modules.SETTINGS_MODULE
            )

            coEvery { companyService.get(companyStaff.companyId) } returns company.success()
            coEvery { companyStaffService.getLatestByEmail(companyStaff.email) } returns companyStaff.success()
            coEvery { modulesService.getActiveModules(any()) } returns modulesEnabled


            authenticatedAsCompanyStaff("id-token", companyStaff) {
                get(url = "/v2/whats_new") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expected)
                }
            }
        }
    }

    @Test
    fun `#getWhatsNewAnnouncements should return SETTINGS_FEATURE news if has SETTINGS_MODULE module and flag is disabled`() = runBlocking {
        withFeatureFlag(FeatureNamespace.BUSINESS, "is_settings_feature_enabled", false) {
            val expected = listOf(WhatsNewAnnouncements.TOP_OF_MIND)
            val modulesEnabled = listOf(
                Modules.SETTINGS_MODULE
            )

            coEvery { companyService.get(companyStaff.companyId) } returns company.success()
            coEvery { companyStaffService.getLatestByEmail(companyStaff.email) } returns companyStaff.success()
            coEvery { modulesService.getActiveModules(any()) } returns modulesEnabled
            authenticatedAsCompanyStaff("id-token", companyStaff) {
                get(url = "/v2/whats_new") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expected)
                }
            }
        }
    }

    @Test
    fun `#getWhatsNewAnnouncements should not return SETTINGS_FEATURE news if has not SETTINGS_MODULE module and flag is enabled`() = runBlocking {
        withFeatureFlag(FeatureNamespace.BUSINESS, "is_settings_feature_enabled", false) {
            val expected = listOf(WhatsNewAnnouncements.TOP_OF_MIND)
            val modulesEnabled = listOf<Modules>()

            coEvery { companyService.get(companyStaff.companyId) } returns company.success()
            coEvery { companyStaffService.getLatestByEmail(companyStaff.email) } returns companyStaff.success()
            coEvery { modulesService.getActiveModules(any()) } returns modulesEnabled
            authenticatedAsCompanyStaff("id-token", companyStaff) {
                get(url = "/v2/whats_new") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expected)
                }
            }
        }
    }

    @Test
    fun `#getWhatsNewAnnouncements should not show TOP_OF_MIND announcement if date is overdue`() = runBlocking {
        val expected = emptyList<WhatsNewAnnouncements>()
        val modulesEnabled = listOf<Modules>()

        every { LocalDate.now() } returns LocalDate.of(2025, 10, 1)
        coEvery { companyService.get(companyStaff.companyId) } returns company.success()
        coEvery { companyStaffService.getLatestByEmail(companyStaff.email) } returns companyStaff.success()
        coEvery { modulesService.getActiveModules(any()) } returns modulesEnabled

        authenticatedAsCompanyStaff("id-token", companyStaff) {
            get(url = "/v2/whats_new") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)
            }
        }
    }
}
