package br.com.alice.atlas.usecase

import br.com.alice.atlas.model.Accredited
import br.com.alice.atlas.model.AccreditedCategory
import br.com.alice.atlas.model.AccreditedFilterParams
import br.com.alice.atlas.model.AccreditedSearchArea
import br.com.alice.atlas.usecases.BuildFilterUseCase
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import kotlin.test.AfterTest
import kotlin.test.assertEquals

class BuildFilterUseCaseTest: MockedTestHelper() {
    private val useCase: BuildFilterUseCase = BuildFilterUseCase()

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    private val siteAccreditedNetwork = TestModelFactory.buildSiteAccreditedNetwork()
    private val filterParams = AccreditedFilterParams(
        siteAccreditedNetworkId = siteAccreditedNetwork.id,
        categories = listOf(AccreditedCategory.HOSPITAL),
        specialtyId = RangeUUID.generate(),
        providerName = "providerName",
        range = IntRange(0, 8)
    )
    private val searchArea = AccreditedSearchArea(
        latitude = "**********",
        longitude = "**********",
        radiusInMeters = 1000,
        type = null,
        placeId = null
    )
    private val flagship = listOf(
        Accredited(
            id = RangeUUID.generate(),
            name = "Flagship 1",
            category = AccreditedCategory.HOSPITAL,
            referencedId = RangeUUID.generate(),
        )
    )

    @Test
    fun `#buildFilter should return a filter`():Unit = runBlocking {
        val result = useCase.buildFilter(filterParams, searchArea, siteAccreditedNetwork, flagship)

        assertThat(result).isSuccess()
    }

    @Test
    fun `#buildFilter should return a filter with limit equals to 9`():Unit = runBlocking {
        val params = filterParams.copy(
            range = IntRange(0, 8)
        )
        val flagship = listOf(
            Accredited(
                id = RangeUUID.generate(),
                name = "Flagship 1",
                category = AccreditedCategory.HOSPITAL,
                referencedId = RangeUUID.generate(),
            ),
            Accredited(
                id = RangeUUID.generate(),
                name = "Flagship 1",
                category = AccreditedCategory.HOSPITAL,
                referencedId = RangeUUID.generate(),
            ),
        )

        val result = useCase.buildFilter(params, searchArea, siteAccreditedNetwork, flagship)

        assertThat(result).isSuccess()
        assertEquals(7, result.get().limit)
        assertEquals(0, result.get().offset)
    }

    @Test
    fun `#buildFilter should return a filter with offset correct when is not first page`():Unit = runBlocking {
        val params = filterParams.copy(
            range = IntRange(10, 18)
        )
        val flagship = listOf(
            Accredited(
                id = RangeUUID.generate(),
                name = "Flagship 1",
                category = AccreditedCategory.HOSPITAL,
                referencedId = RangeUUID.generate(),
            ),
            Accredited(
                id = RangeUUID.generate(),
                name = "Flagship 1",
                category = AccreditedCategory.HOSPITAL,
                referencedId = RangeUUID.generate(),
            ),
        )

        val result = useCase.buildFilter(params, searchArea, siteAccreditedNetwork, flagship)

        assertThat(result).isSuccess()
        assertEquals(7, result.get().limit)
        assertEquals(8, result.get().offset)
    }

    @Test
    fun `#buildFilter should return a filter with limit equals to 9 when there is no flagship`():Unit = runBlocking {
        val params = filterParams.copy(
            range = IntRange(0, 8)
        )

        val result = useCase.buildFilter(params, searchArea, siteAccreditedNetwork)

        assertThat(result).isSuccess()
        assertEquals(9, result.get().limit)
        assertEquals(0, result.get().offset)
    }

    @Test
    fun `#buildFilter should return a filter with offset correct when is not first page and there is no flagship`():Unit = runBlocking {
        val params = filterParams.copy(
            range = IntRange(10, 18)
        )

        val result = useCase.buildFilter(params, searchArea, siteAccreditedNetwork)

        assertThat(result).isSuccess()
        assertEquals(9, result.get().limit)
        assertEquals(10, result.get().offset)
    }

    @Test
    fun `#buildFilter should return a filter when siteAccreditedNetworkId is null`():Unit = runBlocking {
        withFeatureFlag(
            namespace = FeatureNamespace.MEMBER_WANNABE,
            key = "site_accredited_network_id_default_filter",
            value = siteAccreditedNetwork.id.toString(),
        ) {
            val filterParams = filterParams.copy(siteAccreditedNetworkId = null)

            val result = useCase.buildFilter(filterParams, searchArea, siteAccreditedNetwork, flagship)

            assertThat(result).isSuccess()
        }
    }

    @Test
    fun `#buildFilter should return a filter with limit equals to 0 when flagship size bigger than range`():Unit = runBlocking {
        val params = filterParams.copy(
            range = IntRange(0, 1)
        )
        val flagship = listOf(
            Accredited(
                id = RangeUUID.generate(),
                name = "Flagship 1",
                category = AccreditedCategory.HOSPITAL,
                referencedId = RangeUUID.generate(),
            ),
            Accredited(
                id = RangeUUID.generate(),
                name = "Flagship 1",
                category = AccreditedCategory.HOSPITAL,
                referencedId = RangeUUID.generate(),
            ),
            Accredited(
                id = RangeUUID.generate(),
                name = "Flagship 1",
                category = AccreditedCategory.HOSPITAL,
                referencedId = RangeUUID.generate(),
            ),
        )

        val result = useCase.buildFilter(params, searchArea, siteAccreditedNetwork, flagship)

        assertThat(result).isSuccess()
        assertEquals(0, result.get().limit)
        assertEquals(0, result.get().offset)
    }
}
