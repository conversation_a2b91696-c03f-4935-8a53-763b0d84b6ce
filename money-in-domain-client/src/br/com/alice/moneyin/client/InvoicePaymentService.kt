package br.com.alice.moneyin.client

import br.com.alice.common.PaymentMethod
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.BillingAccountableParty
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoiceLiquidation
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.InvoicePaymentSource
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.models.PreActivationPayment
import com.github.kittinunf.result.Result
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface InvoicePaymentService : Service {

    override val namespace get() = "money_in"
    override val serviceName get() = "invoice_payments"

    suspend fun get(paymentId: UUID, withPaymentDetails: Boolean = true): Result<InvoicePayment, Throwable>

    suspend fun getLastByCompanyId(
        companyId: UUID,
        withPaymentDetails: Boolean = true
    ): Result<InvoicePayment, Throwable>

    suspend fun getByExternalId(
        externalId: String,
        withPaymentDetails: Boolean = false
    ): Result<InvoicePayment, Throwable>

    suspend fun getByExternalIdAndSource(
        externalId: String,
        source: InvoicePaymentSource,
        withPaymentDetails: Boolean = false
    ): Result<InvoicePayment, Throwable>

    suspend fun getByInvoiceGroupIds(
        memberInvoiceGroupIds: List<UUID>,
        withPaymentDetails: Boolean = false
    ): Result<List<InvoicePayment>, Throwable>

    suspend fun getLastByInvoiceGroupId(
        memberInvoiceGroupId: UUID,
        withPaymentDetails: Boolean
    ): Result<InvoicePayment, Throwable>

    suspend fun getByInvoiceLiquidationIds(
        invoiceLiquidationIds: List<UUID>,
        withPaymentDetails: Boolean = false
    ): Result<List<InvoicePayment>, Throwable>

    suspend fun getByInvoicePreActivationPayment(
        preActivationPaymentIds: List<UUID>,
        withPaymentDetails: Boolean = false
    ): Result<List<InvoicePayment>, Throwable>

    suspend fun approve(
        paymentId: UUID,
        approvedAt: LocalDateTime? = null,
        amountPaid: BigDecimal? = null,
        interest: BigDecimal? = null,
        fine: BigDecimal? = null,
        approvedByStaffId: UUID? = null,
    ):
            Result<InvoicePayment, Throwable>

    suspend fun secondCopy(paymentId: UUID, personId: PersonId): Result<Boolean, Throwable>

    suspend fun cancel(paymentId: UUID, reason: CancellationReason): Result<InvoicePayment, Throwable>

    suspend fun cancelByInvoiceGroupId(
        memberInvoiceGroupId: UUID,
        reason: CancellationReason
    ): Result<List<InvoicePayment>, Throwable>

    suspend fun cancelByPreActivationPaymentId(
        preActivationPaymentId: UUID,
        reason: CancellationReason
    ): Result<List<InvoicePayment>, Throwable>

    suspend fun cancelByInvoiceLiquidationId(
        invoiceLiquidationId: UUID,
        reason: CancellationReason
    ): Result<List<InvoicePayment>, Throwable>

    suspend fun decline(paymentId: UUID): Result<InvoicePayment, Throwable>

    suspend fun fail(paymentId: UUID, failReason: String): Result<InvoicePayment, Throwable>

    suspend fun expire(paymentId: UUID): Result<InvoicePayment, Throwable>

    suspend fun listInvoicePayments(
        memberInvoiceId: UUID,
        withPaymentDetails: Boolean = true
    ): Result<List<InvoicePayment>, Throwable>

    suspend fun listInvoicePaymentsById(invoicePaymentIds: List<UUID>): Result<List<InvoicePayment>, Throwable>

    suspend fun listInvoicePaymentsByInvoicesIds(
        memberInvoiceIds: List<UUID>,
        withPaymentDetails: Boolean = true
    ): Result<List<InvoicePayment>, Throwable>

    suspend fun getPendingInvoicePayment(
        memberInvoiceId: UUID,
        method: PaymentMethod,
        withPaymentDetails: Boolean = true
    ): Result<InvoicePayment, Throwable>

    suspend fun createExternalPaymentForInvoicePayment(
        invoicePayment: InvoicePayment,
        billingAccountableParty: BillingAccountableParty,
        personId: PersonId? = null
    ): Result<InvoicePayment, Throwable>

    suspend fun createInvoicePayment(
        invoicePayment: InvoicePayment,
        member: Member,
    ): Result<InvoicePayment, Throwable>

    suspend fun createInvoicePaymentForMemberInvoices(
        payload: CreateInvoicePaymentForMemberInvoicesPayload
    ): Result<InvoicePayment, Throwable>

    data class CreateInvoicePaymentForMemberInvoicesPayload(
        val paymentMethod: PaymentMethod,
        val memberInvoices: List<MemberInvoice>,
        val dueDate: LocalDateTime,
        val billingAccountableParty: BillingAccountableParty,
        val reason: PaymentReason = PaymentReason.REGULAR_PAYMENT,
        val memberInvoiceGroup: MemberInvoiceGroup? = null,
        val personId: PersonId? = null,
        val origin: InvoicePaymentOrigin = InvoicePaymentOrigin.UNDEFINED,
        val paymentUrl: String? = null,
        val externalId: String? = null,
        val sendEmail: Boolean? = null,
        val syncProcess: Boolean? = false
    )

    suspend fun createInvoicePaymentForInvoiceGroup(
        memberInvoiceGroup: MemberInvoiceGroup,
        paymentMethod: PaymentMethod,
        reason: PaymentReason = PaymentReason.REGULAR_PAYMENT,
        origin: InvoicePaymentOrigin = InvoicePaymentOrigin.UNDEFINED,
        dueDate: LocalDate? = null,
        syncProcess: Boolean? = false
    ): Result<InvoicePayment, Throwable>

    suspend fun createFromMemberInvoiceGroup(
        memberInvoiceGroup: MemberInvoiceGroup,
        paymentMethod: PaymentMethod,
        reason: PaymentReason = PaymentReason.REGULAR_PAYMENT,
        origin: InvoicePaymentOrigin = InvoicePaymentOrigin.UNDEFINED,
        dueDate: LocalDate? = null,
        sendEmail: Boolean = true,
        copyMemberInvoiceIds: Boolean = false,
        syncProcess: Boolean = false
    ): Result<InvoicePayment, Throwable>

    suspend fun createFromPreActivationPayment(
        preActivationPayment: PreActivationPayment,
        paymentMethod: PaymentMethod,
        origin: InvoicePaymentOrigin = InvoicePaymentOrigin.UNDEFINED,
        dueDate: LocalDate? = null,
        sendEmail: Boolean = true,
        copyMemberInvoiceIds: Boolean = false,
        syncProcess: Boolean = false
    ): Result<InvoicePayment, Throwable>

    suspend fun reissueBoleto(
        invoicePayment: InvoicePayment,
        billingAccountableParty: BillingAccountableParty,
        dueDate: LocalDateTime
    ): Result<InvoicePayment, Throwable>

    suspend fun listInvoicePaymentsByBillingAccountablePartyId(
        billingAccountablePartyId: UUID,
        withPaymentDetails: Boolean = false
    ): Result<List<InvoicePayment>, Throwable>

    suspend fun createPaymentForLiquidation(
        invoiceLiquidation: InvoiceLiquidation,
        paymentMethod: PaymentMethod,
        reason: PaymentReason,
        origin: InvoicePaymentOrigin,
        dueDate: LocalDate? = null
    ): Result<InvoicePayment, Throwable>

    suspend fun listInvoicePaymentsByBillingAccountablePartyIds(
        billingAccountablePartyIds: List<UUID>,
        withPaymentDetails: Boolean
    ): Result<List<InvoicePayment>, Throwable>

    suspend fun update(invoicePayment: InvoicePayment): Result<InvoicePayment, Throwable>

    suspend fun findInvoicePaymentInCreatedAtRangeAndBillingAccountablePartyId(
        startDate: LocalDateTime,
        endDate: LocalDateTime,
        billingAccountablePartyId: UUID
    ): Result<List<InvoicePayment>, Throwable>

    suspend fun findForApprovedWithoutAmountPaidDefined(
        range: IntRange = IntRange(0, 15)
    ): Result<List<InvoicePayment>, Throwable>
}

class InvoicePaymentCanceledException(
    message: String,
    code: String = "invoice_payment_canceled",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(invoicePaymentId: UUID) : this(
        message = "Invoice payment '$invoicePaymentId' was cancelled already"
    )
}

class PendingInvoicePaymentException(
    message: String,
    code: String = "pending_invoice_payment",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(invoiceId: UUID) : this(
        message = "There is already an pending payment for invoice '$invoiceId'"
    )
}

class InvoicePaymentAlreadyApprovedException(
    message: String,
    code: String = "invoice_payment_already_approved",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(invoicePaymentId: UUID) : this(
        message = "Invoice Payment '$invoicePaymentId' is already approved"
    )
}

class InvoicePaymentAmountNotAllowed(
    message: String,
    code: String = "invoice_payment_amount_not_allowed",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(amount: BigDecimal, memberInvoiceId: UUID) : this(
        message = "Invoice payment amount = $amount is not allowed for member invoice $memberInvoiceId"
    )
}

class InvoicePaymentInvalidDueDate(
    message: String,
    code: String = "invoice_payment_invalid_due_date",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(invoiceId: UUID, dueDate: LocalDateTime) : this(
        message = "Payment due date $dueDate invalid for invoice '$invoiceId'"
    )
}

class InvoicePaymentDetailsNotFoundException(
    message: String,
    code: String = "invoice_payment_details_not_found_exception",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(invoicePaymentId: UUID) : this(
        message = "No paymentDetail information found for invoicePayment $invoicePaymentId"
    )
}

class InvoicePaymentInvalidAmountPaid(
    message: String,
    code: String = "invoice_payment_invalid_amount_paid",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(amountPaid: BigDecimal, totalAmount: BigDecimal) : this(
        message = "Invoice payment amount paid = $amountPaid is less than the invoice payment total amount $totalAmount"
    )
}

