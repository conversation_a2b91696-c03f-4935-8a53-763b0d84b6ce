package br.com.alice.business.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.events.CompanyContractCreatedEvent
import br.com.alice.business.events.CompanyContractUpdatedEvent
import br.com.alice.common.PaymentMethod
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.isBeforeEq
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.CompanyContract
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.moneyin.client.PreActivationCompanyInvoiceService
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate

class CompanyContractConsumer(
    private val subcontractService: CompanySubContractService,
    private val beneficiaryService: BeneficiaryService,
    private val preActivationCompanyInvoiceService: PreActivationCompanyInvoiceService,
    private val memberService: MemberService,
    private val companyService: CompanyService,
) : Consumer() {


    suspend fun handleCreateCompanyFirstPayment(event: CompanyContractCreatedEvent) = withSubscribersEnvironment {
        createFirstPaymentForSubcontract(event.payload.contract)
    }

    suspend fun handleUpdateCompanyFirstPayment(event: CompanyContractUpdatedEvent) = withSubscribersEnvironment {
        createFirstPaymentForSubcontract(event.payload.contract)
    }

    private fun shouldCreateFirstInvoiceGroupPayment(groupCompany: String) =
        FeatureService.inList(
            FeatureNamespace.BUSINESS,
            "should_create_first_invoice_group_payment",
            groupCompany,
            false
        ).also {
            logger.info(
                "The FF should_create_first_invoice_group_payment", "values" to FeatureService.get(
                    FeatureNamespace.BUSINESS,
                    "should_create_first_invoice_group_payment", emptyList<String>()
                )
            )
        }

    private fun shouldSkipCreation(cnpj: String) = FeatureService.inList(
        FeatureNamespace.BUSINESS,
        "skip_creation_first_invoice_group_payment",
        cnpj,
        false
    ).also {
        logger.info(
            "The FF should_create_first_invoice_group_payment", "values" to FeatureService.get(
                FeatureNamespace.BUSINESS,
                "skip_creation_first_invoice_group_payment", emptyList<String>()
            )
        )
    }

    private suspend fun fallbackBeneficiariesUpdateSubContractId(
        companySubContract: CompanySubContract
    ): Result<List<Beneficiary>, Throwable> = beneficiaryService.findByCompanyId(companySubContract.companyId)
        .map { beneficiaries ->
            beneficiaries.filter { it.companySubContractId == null }
                .map { it.copy(companySubContractId = companySubContract.id) }
        }
        .flatMap {
            if (it.isEmpty()) return it.success()
            else {
                beneficiaryService.updateInBatch(it)
            }
        }

    private suspend fun createFirstPaymentForSubcontract(companyContract: CompanyContract) = coroutineScope {
        if (
            companyContract.contractFileIds.isNotEmpty() &&
            companyContract.groupCompany?.let { shouldCreateFirstInvoiceGroupPayment(it) } == true
        ) {
            subcontractService.findByContractId(companyContract.id)
                .andThen { subcontracts ->
                    if (subcontracts.isEmpty() || subcontracts.size > 1) {
                        subcontracts.success()
                    } else {
                        fallbackBeneficiariesUpdateSubContractId(subcontracts.first())
                    }
                }
                .pmapEach {
                    val companyDeferred = async { companyService.get(it.companyId).get() }
                    val preActivationCompanyInvoiceDef = async {
                        preActivationCompanyInvoiceService.get(
                            it.id,
                            companyContract.groupCompany,
                        ).getOrNullIfNotFound()
                    }

                    val company = companyDeferred.await()
                    val preActivationCompanyInvoice = preActivationCompanyInvoiceDef.await()

                    if (!shouldSkipCreation(company.cnpj) && preActivationCompanyInvoice == null) {
                        logger.info(
                            "Creating the first payment for the subcontract",
                            "id" to it.id,
                            "contract_id" to it.contractId,
                            "company_id" to it.companyId,
                        )
                        createPayment(companyContract, it).get()
                    } else {
                        logger.info(
                            "Skipping the first payment creation for the subcontract once it already has a first payment created",
                            "subcontract_id" to it.id,
                            "contract_id" to it.contractId,
                            "company_id" to it.companyId,
                            "subcontract_id" to it.externalId,
                            "contract_id" to companyContract.externalId,
                            "pre_activation_company_invoice_id" to preActivationCompanyInvoice?.id,
                            "pre_activation_company_type" to preActivationCompanyInvoice?.type,
                        )
                    }
                }
        } else {
            logger.info(
                "Skipping the first payment creation for the contract",
                "contract_id" to companyContract.id,
                "ff" to FeatureService.get(
                    FeatureNamespace.BUSINESS,
                    "should_create_first_invoice_group_payment", emptyList<String>()
                )
            )

            false.success()
        }
    }

    private suspend fun createPayment(
        companyContract: CompanyContract,
        companySubContract: CompanySubContract
    ) = beneficiaryService
        .findByCompanySubContractId(companySubContract.id)
        .mapEach { it.memberId }
        .flatMap { memberIds ->
            memberService.findByIdsAndStatus(memberIds, listOf(MemberStatus.ACTIVE, MemberStatus.PENDING))
                .then { members ->
                    logger.info(
                        "Members found",
                        "size" to members.size,
                        "members" to members.map {
                            mapOf(
                                "member_id" to it.id,
                                "person_id" to it.personId,
                            )
                        }
                    )
                }
        }.flatMap { members ->
            if (members.isEmpty()) {
                logger.info("The first invoice group payment was not created because no member was found.")
                return@flatMap false.success()
            }

            val dueDate = companyContract.startedAt?.let {
                val tomorrow = LocalDate.now().plusDays(1)
                if (it.isBeforeEq(tomorrow)) {
                    tomorrow
                } else {
                    it.minusDays(1)
                }
            } ?: LocalDate.now().plusDays(1)

            val startedAt =
                companyContract.startedAt ?: LocalDate.now()

            preActivationCompanyInvoiceService.create(
                PreActivationCompanyInvoiceService.CreatePayload(
                    companyId = companySubContract.companyId,
                    companySubContractId = companySubContract.id,
                    billingAccountablePartyId = companySubContract.billingAccountablePartyId!!,
                    members = members,
                    paymentMethod = PaymentMethod.BOLEPIX,
                    dueDate = dueDate,
                    referenceDate = startedAt.atBeginningOfTheMonth(),
                    origin = InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS,
                    groupCompany = companyContract.groupCompany,
                )
            )
        }
}
