package br.com.alice.app.content.model.section

import br.com.alice.app.content.model.Alignment
import br.com.alice.app.content.model.BackgroundColor
import br.com.alice.app.content.model.Button
import br.com.alice.app.content.model.CardColorScheme
import br.com.alice.app.content.model.CardHeader
import br.com.alice.app.content.model.CardSize
import br.com.alice.app.content.model.CardState
import br.com.alice.app.content.model.CardType
import br.com.alice.app.content.model.RemoteAction

data class CardSection(
    val type: CardType? = null,
    val state: CardState? = null,
    val header: CardHeader? = null,
    val title: String? = null,
    val subtitle: String? = null,
    val description: String? = null,
    val backgroundImage: String? = null,
    val accessoryImage: String? = null,
    val accessoryIcon: String? = null,
    val alignment: Alignment? = null,
    val backgroundColor: BackgroundColor? = null,
    val contentAction: RemoteAction? = null,
    val onCardClick: RemoteAction? = null,
    val onTapAction: RemoteAction? = null,
    val mainAction: Button? = null,
    val buttonPrimary: Button? = null,
    val buttonSecondary: Button? = null,
    val size: CardSize? = null,
    val colorScheme: CardColorScheme? = null,
    val expandableCardVariant: ExpandableCardVariant? = null
) : SectionDataBase()
