package br.com.alice.hr.core.clients

import br.com.alice.common.observability.Spannable
import br.com.alice.hr.core.clients.interfaces.AmazonS3Client
import com.amazonaws.auth.WebIdentityTokenCredentialsProvider
import com.amazonaws.regions.Regions
import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.AmazonS3ClientBuilder
import com.amazonaws.services.s3.AmazonS3URI
import com.amazonaws.services.s3.model.GetObjectRequest
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success

class AmazonS3ClientImpl(
    private val s3: AmazonS3 = AmazonS3ClientBuilder
        .standard()
        .withCredentials(WebIdentityTokenCredentialsProvider.create())
        .withPathStyleAccessEnabled(true)
        .withRegion(Regions.US_EAST_1)
        .build()
) : AmazonS3Client, Spannable {
    override suspend fun getS3ObjectContent(path: String): Result<ByteArray, Throwable> = span("getS3Object") {
        val s3Uri = AmazonS3URI(path)
        val getObjectRequest = GetObjectRequest(s3Uri.getBucket(), s3Uri.getKey())

        try {
            s3.getObject(getObjectRequest)
            .getObjectContent()
            .readBytes()
            .success()
        } catch (e: Exception) {
            e.failure()
        }
    }
}
