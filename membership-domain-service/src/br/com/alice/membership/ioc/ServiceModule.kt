package br.com.alice.membership.ioc

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.core.RunningMode.PRODUCTION
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.common.storage.FileStorage
import br.com.alice.common.storage.FileVaultStorage
import br.com.alice.common.storage.LocalFileStorage
import br.com.alice.common.storage.S3FileStorage
import br.com.alice.communication.partner_campaigns.PartnerCampaignSignUp
import br.com.alice.communication.partner_campaigns.medipreco.MediPrecoClient
import br.com.alice.communication.partner_campaigns.medipreco.MediPrecoIntegrator
import br.com.alice.membership.SERVICE_NAME
import br.com.alice.membership.ServiceConfig
import br.com.alice.membership.client.*
import br.com.alice.membership.client.onboarding.GasProcessService
import br.com.alice.membership.client.onboarding.GracePeriodService
import br.com.alice.membership.client.onboarding.HealthDeclarationAppointmentScheduler
import br.com.alice.membership.client.onboarding.HealthDeclarationForm
import br.com.alice.membership.client.onboarding.HealthDeclarationService
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.membership.client.onboarding.OperationOnboardingService
import br.com.alice.membership.client.onboarding.PersonRegistrationService
import br.com.alice.membership.client.onboarding.ProductOrderService
import br.com.alice.membership.client.onboarding.ShoppingCartService
import br.com.alice.membership.client.onboarding.ShoppingService
import br.com.alice.membership.communication.Mailer
import br.com.alice.membership.communication.PushNotifier
import br.com.alice.membership.communication.SMSService
import br.com.alice.membership.consumers.*
import br.com.alice.membership.controllers.BackfillController
import br.com.alice.membership.controllers.ProductChangeScheduleJobController
import br.com.alice.membership.services.*
import br.com.alice.membership.services.contactInfo.UpdatedPersonContactInfoTempServiceImpl
import br.com.alice.membership.services.contract.ContractTermBuilder
import br.com.alice.membership.services.growth.PromoCodeServiceImpl
import br.com.alice.membership.services.growth.PromoCodeValidationFactory
import br.com.alice.membership.services.growth.validation.DefaultPromoCodeValidator
import br.com.alice.membership.services.growth.validation.MemberGetMemberValidator
import br.com.alice.membership.services.onboarding.ContractRegistryImpl
import br.com.alice.membership.services.onboarding.GasProcessServiceImpl
import br.com.alice.membership.services.onboarding.GracePeriodServiceImpl
import br.com.alice.membership.services.onboarding.HealthDeclarationAppointmentSchedulerImpl
import br.com.alice.membership.services.onboarding.HealthDeclarationFormImpl
import br.com.alice.membership.services.onboarding.HealthDeclarationServiceImpl
import br.com.alice.membership.services.onboarding.OnboardingServiceImpl
import br.com.alice.membership.services.onboarding.OperationOnboardingServiceImpl
import br.com.alice.membership.services.onboarding.PersonRegistrationServiceImpl
import br.com.alice.membership.services.onboarding.ProductOrderServiceImpl
import br.com.alice.membership.services.onboarding.ShoppingCartServiceImpl
import br.com.alice.membership.services.onboarding.ShoppingServiceImpl
import br.com.alice.membership.storage.MemberDocuments
import br.com.alice.membership.storage.MemberVaultDocuments
import br.com.alice.membership.webhooks.IdwallWebhooksReceiver
import io.ktor.client.engine.apache.Apache
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.bind
import org.koin.dsl.module

val ServiceModule = module(createdAtStart = true) {

    when (ServiceConfig.runningMode) {
        PRODUCTION -> {
            single<FileStorage> { S3FileStorage() }
        }

        else -> {
            single<FileStorage> { LocalFileStorage() }
        }
    }

    // MediPreco config
    single<PartnerCampaignSignUp> {
        val config = ServiceConfig.PartnerCampaign.mediPrecoConfig()
        val client = MediPrecoClient(config, Apache.create {
            customizeClient {
                setMaxConnTotal(2000)
                setMaxConnPerRoute(200)
            }
        })

        MediPrecoIntegrator(client)
    }

    // Internal
    single { Mailer(get(), get(), ServiceConfig.Crm.hubspotConfig()) }
    single { PromoCodeValidationFactory(getKoin()) }
    singleOf(::PushNotifier)
    singleOf(::SMSService)
    singleOf(::MemberDocuments)
    singleOf(::ContractCertifier)
    singleOf(::ContractTermBuilder)
    singleOf(::TermCertifier)
    singleOf(::MemberVaultDocuments)

    // Growth
    singleOf(::MemberGetMemberValidator)
    singleOf(::DefaultPromoCodeValidator)

    // Exposed Services
    single<SessionsService> { SessionsServiceImpl() }

    singleOf(::DeviceServiceImpl) bind DeviceService::class
    singleOf(::PersonLoginServiceImpl) bind PersonLoginService::class
    singleOf(::ContractRegistryImpl) bind ContractRegistry::class
    singleOf(::ContractGeneratorImpl) bind ContractGenerator::class
    singleOf(::HealthDeclarationFormImpl) bind HealthDeclarationForm::class
    singleOf(::OnboardingServiceImpl) bind OnboardingService::class
    singleOf(::ShoppingServiceImpl) bind ShoppingService::class
    singleOf(::UpdateAppRuleServiceImpl) bind UpdateAppRuleService::class
    singleOf(::PersonRegistrationServiceImpl) bind PersonRegistrationService::class
    singleOf(::GasProcessServiceImpl) bind GasProcessService::class
    singleOf(::MembershipPersonServiceImpl) bind MembershipPersonService::class
    singleOf(::HealthDeclarationServiceImpl) bind HealthDeclarationService::class
    singleOf(::OperationOnboardingServiceImpl) bind OperationOnboardingService::class
    singleOf(::HealthDeclarationAppointmentSchedulerImpl) bind HealthDeclarationAppointmentScheduler::class
    singleOf(::LeadAnonymizationServiceImpl) bind LeadAnonymizationService::class
    singleOf(::HealthGoalServiceImpl) bind HealthGoalService::class
    singleOf(::PersonTaskServiceImpl) bind PersonTaskService::class
    singleOf(::PersonTestResultFileServiceImpl) bind PersonTestResultFileService::class
    singleOf(::PromoCodeServiceImpl) bind PromoCodeService::class
    singleOf(::PersonHealthcareTeamRecommendationServiceImpl) bind PersonHealthcareTeamRecommendationService::class
    singleOf(::ProductOrderServiceImpl) bind ProductOrderService::class
    singleOf(::ShoppingCartServiceImpl) bind ShoppingCartService::class
    singleOf(::GracePeriodServiceImpl) bind GracePeriodService::class
    singleOf(::InvoicePriceServiceImpl) bind InvoicePriceService::class
    singleOf(::HealthcareTeamRecommendationServiceImpl) bind HealthcareTeamRecommendationService::class
    singleOf(::CptsServiceImpl) bind CptsService::class
    singleOf(::FaqGroupServiceImpl) bind FaqGroupService::class
    singleOf(::FaqFeedbackServiceImpl) bind FaqFeedbackService::class
    singleOf(::FaqContentServiceImpl) bind FaqContentService::class
    singleOf(::HealthcareTeamRecommendationRuleServiceImpl) bind HealthcareTeamRecommendationRuleService::class
    singleOf(::HealthcareTeamRecommendationRuleToRecommendationServiceImpl) bind HealthcareTeamRecommendationRuleToRecommendationService::class
    singleOf(::DocumentServiceImpl) bind DocumentService::class
    singleOf(::PersonPreferencesServiceImpl) bind PersonPreferencesService::class
    singleOf(::EmailCommunicationServiceImpl) bind EmailCommunicationService::class
    singleOf(::PersonDocumentsUploadServiceImpl) bind PersonDocumentsUploadService::class
    singleOf(::PersonBenefitServiceImpl) bind PersonBenefitService::class
    singleOf(::MemberPriceServiceImpl) bind MemberPriceService::class
    singleOf(::MemberContractTermServiceImpl) bind MemberContractTermService::class
    singleOf(::UpdatedPersonContactInfoTempServiceImpl) bind UpdatedPersonContactInfoTempService::class
    singleOf(::MemberLifeCycleEventsServiceImpl) bind MemberLifeCycleEventsService::class
    single {
        FileVaultStorage(DefaultHttpClient({
            install(ContentNegotiation) {
                gsonSnakeCase()
            }
        }, timeoutInMillis = 15_000))
    }
    singleOf(::MemberProductChangeScheduleServiceImpl) bind MemberProductChangeScheduleService::class

    // Servers
    loadServiceServers("br.com.alice.membership.services")


    // Controllers
    single { HealthController(SERVICE_NAME) }
    singleOf(::BackfillController)
    singleOf(::ProductChangeScheduleJobController)

    // Webhooks
    singleOf(::IdwallWebhooksReceiver)

    // Consumers
    singleOf(::ClinicalBackgroundUpdatedConsumer)
    singleOf(::HealthDeclarationConsumer)
    singleOf(::ContractConsumer)
    singleOf(::AcquisitionAnalyticsConsumer)
    singleOf(::ContractSignedConsumer)
    singleOf(::AppointmentScheduleEventsConsumer)
    singleOf(::AppointmentScheduleAnalyticsConsumer)
    singleOf(::NewRegistrationConsumer)
    singleOf(::InvoiceConsumer)
    singleOf(::MemberCancelledConsumer)
    singleOf(::AccessCodeCreatedConsumer)
    singleOf(::UpdatedPersonContactInfoTempConsumer)
    singleOf(::PersonCreatedInLeadCentralConsumer)
    singleOf(::EngagementAnalyticsConsumer)
    singleOf(::PersonBenefitUpdatedConsumer)
    singleOf(::CaseRecordConsumer)
    singleOf(::BeneficiaryTermAcceptedConsumer)
    singleOf(::OnboardingArchivedConsumer)
    singleOf(::MemberAccreditedNetworkTrackerConsumer)
    singleOf(::MemberProductChangeRequestConsumer)
}
