package br.com.alice.atlas.model

data class SpecialistTransport(
    val name: String,
    val photo: String?,
    val council: String?,
    val specialty: String?,
    val subSpecialties: List<String>?,
    val includedPlans: List<String>?,
    val phones: List<AccreditedPhone>?,
    val address: List<String>?,
    val remote: Boolean,
    val about: String? = null,
    val contractOrigin: String? = null,
)
