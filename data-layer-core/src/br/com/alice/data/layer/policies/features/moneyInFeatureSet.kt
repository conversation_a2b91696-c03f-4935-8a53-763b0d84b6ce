package br.com.alice.data.layer.policies.features

import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.CancelPaymentOnAcquirerScheduleModel
import br.com.alice.data.layer.models.InvoiceItemModel
import br.com.alice.data.layer.models.InvoicePaymentModel
import br.com.alice.data.layer.models.ItauPaymentModel
import br.com.alice.data.layer.models.MemberInvoiceGroupModel
import br.com.alice.data.layer.models.MemberInvoiceModel
import br.com.alice.data.layer.models.PaymentDetailModel
import br.com.alice.data.layer.models.PreActivationPaymentModel
import br.com.alice.data.layer.models.ResourceSignTokenModel

val viewAndUpdateMemberInvoiceGroup = policySet {
    can(View, Update, Create) {
        resources(
            MemberInvoiceModel::class,
            InvoicePaymentModel::class,
            ResourceSignTokenModel::class,
            ItauPaymentModel::class,
            CancelPaymentOnAcquirerScheduleModel::class,
            PaymentDetailModel::class,
            InvoiceItemModel::class,
            MemberInvoiceGroupModel::class,
        )
    }
}

val viewAndUpdatePreActivationPayment = policySet {
    can(View, Update, Create) {
        resources(
            MemberInvoiceModel::class,
            InvoicePaymentModel::class,
            ResourceSignTokenModel::class,
            ItauPaymentModel::class,
            CancelPaymentOnAcquirerScheduleModel::class,
            PaymentDetailModel::class,
            InvoiceItemModel::class,
            PreActivationPaymentModel::class,
        )
    }
}
