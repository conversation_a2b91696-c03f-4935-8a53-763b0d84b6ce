package br.com.alice.hr.core.services.useCases

import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.common.core.extensions.isAfterEq
import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanySize
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDateTime

class GetBeneficiaryRelationTypeOptionsUseCase(
    private val companyProductPriceListingService: CompanyProductPriceListingService
) : Spannable {

    companion object {
        val OLD_PORFOLIO_PRODUCT_ANS_NUMBERS = setOf("*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********")
    }

    suspend fun run(company: Company, subContract: CompanySubContract): Result<List<ParentBeneficiaryRelationType>, Throwable> {
        val numbers = companyProductPriceListingService.findCurrentBySubContractId(subContract.id)
            .map { priceListings -> priceListings.map { it.ansNumber } }
            .get()

        val last30Days = LocalDateTime.now().minusDays(30)

        return when {
            numbers.intersect(OLD_PORFOLIO_PRODUCT_ANS_NUMBERS).toList().isNotEmpty() -> buildOldPortfolioRelationTypes()
            company.companySize == CompanySize.MLA -> buildMLARelationTypes()
            subContract.createdAt.isAfterEq(last30Days) -> buildUnder30Days()
            else -> buildAbove30Days()
        }.success()
    }

    private fun buildOldPortfolioRelationTypes() = listOf(
        ParentBeneficiaryRelationType.SPOUSE,
        ParentBeneficiaryRelationType.PARTNER,
        ParentBeneficiaryRelationType.STEPCHILD,
        ParentBeneficiaryRelationType.CHILD,
        ParentBeneficiaryRelationType.FOSTER_CHILD,
        ParentBeneficiaryRelationType.GRANDCHILD,
        ParentBeneficiaryRelationType.GREATGRANDCHILD
    )

    private fun buildMLARelationTypes() = listOf(
        ParentBeneficiaryRelationType.SPOUSE,
        ParentBeneficiaryRelationType.PARTNER,
        ParentBeneficiaryRelationType.STEPCHILD,
        ParentBeneficiaryRelationType.CHILD,
        ParentBeneficiaryRelationType.FOSTER_CHILD
    )

    private fun buildUnder30Days() = listOf(
        ParentBeneficiaryRelationType.CHILD,
        ParentBeneficiaryRelationType.PARTNER,
        ParentBeneficiaryRelationType.SPOUSE,
        ParentBeneficiaryRelationType.STEPCHILD,
        ParentBeneficiaryRelationType.FOSTER_CHILD,
        ParentBeneficiaryRelationType.BROTHER_SISTER,
        ParentBeneficiaryRelationType.MOTHER_FATHER,
        ParentBeneficiaryRelationType.NIECE_NEPHEW,
        ParentBeneficiaryRelationType.GRANDCHILD,
        ParentBeneficiaryRelationType.SON_DAUGHTER_IN_LAW,
        ParentBeneficiaryRelationType.STEP_MOTHER_FATHER
    )

    private fun buildAbove30Days() = listOf(
        ParentBeneficiaryRelationType.CHILD,
        ParentBeneficiaryRelationType.PARTNER,
        ParentBeneficiaryRelationType.SPOUSE,
        ParentBeneficiaryRelationType.STEPCHILD,
        ParentBeneficiaryRelationType.FOSTER_CHILD
    )
}
