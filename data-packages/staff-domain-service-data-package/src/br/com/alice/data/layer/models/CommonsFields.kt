package br.com.alice.data.layer.models

import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.common.service.serialization.gsonCompleteSerializer

data class SpecialistBankAccountInfo(
    val bankCode: String,
    val agencyNumber: String,
    val accountNumber: String
)

enum class HealthProfessionalCouncil {
    CRBM, CREF, COREN, CREFITO, CREFONO, CRM, CRN, CRO, CRP, CRMV, CRF, CRBIO
}

enum class SpecialistAppointmentType(val description: String) {
    REMOTE("Remoto"),
    PRESENTIAL("Presencial")
}

data class Council(
    val number: String,
    val state: State,
    val type: CouncilType? = null
) : JsonSerializable {
    override fun toString() = type?.let { "$it $number/$state" } ?: "CRM $number/$state"

    fun toJson(): String = gsonCompleteSerializer.toJson(this)
}

enum class SpecialistStatus {
    ACTIVE,
    INACTIVE;
}

enum class HealthSpecialistScoreEnum(val description: String) {
    NEED_TO_RAISE_THE_BAR("Precisa subir a barra"),
    IS_RAISING_THE_BAR("Tá subindo a barra"),
    DOMINATING("Dominando a parada"),
    LIVING_THE_IMPOSSIBLE("Vivendo o impossível"),
    DOES_NOT_APPLY("Não se aplica")
}
