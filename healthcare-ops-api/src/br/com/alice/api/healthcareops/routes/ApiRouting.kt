package br.com.alice.api.healthcareops.routes

import br.com.alice.api.healthcareops.controllers.*
import br.com.alice.api.healthcareops.controllers.business.BeneficiaryController
import br.com.alice.api.healthcareops.controllers.business.BeneficiaryOnboardingController
import br.com.alice.api.healthcareops.controllers.business.CompanyActivationFilesController
import br.com.alice.api.healthcareops.controllers.business.CompanyBeneficiaryController
import br.com.alice.api.healthcareops.controllers.business.CompanyContractController
import br.com.alice.api.healthcareops.controllers.business.CompanyContractualRiskController
import br.com.alice.api.healthcareops.controllers.business.CompanyController
import br.com.alice.api.healthcareops.controllers.business.CompanyInvoiceController
import br.com.alice.api.healthcareops.controllers.business.CompanySubContractController
import br.com.alice.api.healthcareops.controllers.moneyin.InvoiceItemsController
import br.com.alice.api.healthcareops.controllers.moneyin.InvoicesController
import br.com.alice.common.coHandler
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.extensions.inject
import br.com.alice.common.handler
import br.com.alice.common.multipartHandler
import br.com.alice.common.multipleFileMultipartHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.plugins.swagger.swaggerUI
import io.ktor.server.routing.Routing
import io.ktor.server.routing.delete
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route

fun Routing.apiRoutes() {
    val healthController by inject<HealthController>()

    swaggerUI(path = "swagger", swaggerFile = "openapi/documentation.yaml")

    get("/") { handler(healthController::checkHealth) }

    val authController by inject<AuthController>()
    post("/sign_in") { coHandler(authController::signIn) }

    memberTelegramRouting()
    nullvsLogRouting()
    billingAccountablePartyRouting()
    invoiceRouting()
    companyProductPriceListingRouting()
    contractualRiskRouting()

    authenticate {
        route("/products") {
            val productsController by inject<ProductsController>()
            get { coHandler(productsController::searchByQuery) }

            get("/{productId}") { coHandler("productId", productsController::getProductInfo) }

            route("/{product_id}/price_listing") {
                val productPriceListingController by inject<ProductPriceListingController>()
                get { coHandler("product_id", productPriceListingController::findByProductId) }
                post("/{price_listing_id}") {
                    coHandler(
                        "product_id",
                        "price_listing_id",
                        productPriceListingController::add
                    )
                }
            }
        }

        route("/search") {
            val searchController by inject<SearchController>()
            get { coHandler("searchTerm", searchController::search) }
        }



        route("/tax_documents") {
            val taxDocumentController by inject<TaxDocumentController>()

            post { coHandler(taxDocumentController::sendTaxDocumentToMembers) }
            post("/{nationalId}") { coHandler("nationalId", taxDocumentController::sendTaxDocumentToMember) }
        }

        route("/feature/{featureKey}") {
            val featureController by inject<FeatureController>()
            get { coHandler("featureKey", featureController::getByKey) }
        }

        val onboardingController by inject<OnboardingController>()
        val memberContractController by inject<MemberContractController>()
        route("/person") {
            route("/{personId}") {
                get("/") { coHandler("personId", onboardingController::getByPersonId) }
                get("/member-contract-term") { coHandler("personId", memberContractController::getMemberContractTerms) }
            }
        }

        val personController by inject<PersonController>()
        get("/person") { coHandler(personController::searchPerson) }
        route("/people") {
            get("/") { coHandler(onboardingController::getByEmail) }

            route("/{personId}") {

                route("/legal_guardian") {
                    val legalGuardianAssociationController by inject<LegalGuardianAssociationController>()
                    val legalGuardianInfoTempController by inject<LegalGuardianInfoTempController>()

                    route("/association") {
                        get { coHandler("personId", legalGuardianAssociationController::findByPerson) }
                        post { coHandler("personId", legalGuardianAssociationController::createAssociation) }
                    }

                    get { coHandler("personId", legalGuardianInfoTempController::findByPersonId) }

                    post { coHandler(legalGuardianAssociationController::create) }

                    delete { coHandler("personId", legalGuardianAssociationController::delete) }
                    route("/sign_term") {
                        put { coHandler("personId", legalGuardianAssociationController::signTerm) }
                    }
                    route("/history") {
                        get { coHandler("personId", legalGuardianAssociationController::history) }
                    }
                    route("/valid") {
                        put { coHandler("personId", legalGuardianAssociationController::valid) }
                    }
                    route("/expire") {
                        put { coHandler("personId", legalGuardianAssociationController::expire) }
                    }
                    route("/archive") {
                        put { coHandler("personId", legalGuardianAssociationController::archive) }
                    }
                    route("/invalid") {
                        put { coHandler("personId", legalGuardianAssociationController::invalid) }
                    }
                }

                put("") { coHandler("personId", personController::updatePerson) }
                get("/document") { coHandler("personId", personController::getDocumentPicture) }

                route("/contract_signed/trigger") {
                    post { coHandler("personId", personController::triggerSignedContractEvent) }
                }

                route("/billing_accountable_parties") {
                    val billingAccountablePartyController by inject<BillingAccountablePartyController>()

                    get { coHandler("personId", billingAccountablePartyController::findByPerson) }
                    post { coHandler("personId", billingAccountablePartyController::assign) }
                }

                route("/payments") {
                    val invoicesController by inject<InvoicesController>()
                    val invoicePriceController by inject<InvoicePriceController>()
                    get { coHandler("personId", invoicesController::getPaymentsProfile) }
                    post("/calculate_invoice_price") {
                        coHandler(
                            "personId",
                            invoicePriceController::calculateInvoicePrice
                        )
                    }
                }

                route("/preferences") {
                    val personPaymentPreferencesController by inject<PersonPreferencesController>()
                    get { coHandler("personId", personPaymentPreferencesController::get) }
                    post { coHandler("personId", personPaymentPreferencesController::upsert) }
                }

                route("/health_declaration") {
                    val healthDeclarationController by inject<HealthDeclarationController>()
                    get { coHandler("personId", healthDeclarationController::findByPerson) }
                    delete { coHandler("personId", healthDeclarationController::archiveByPerson) }
                    post { coHandler("personId", healthDeclarationController::createToPerson) }
                }

                route("/onboarding") {
                    put("/finish") { coHandler("personId", onboardingController::finishProcess) }
                    post("/health_declaration_appointment_phase") {
                        coHandler(
                            "personId",
                            onboardingController::movePersonToHealthDeclarationAppointment
                        )
                    }
                    post("/move_phase") { coHandler("personId", onboardingController::movePersonToPhase) }
                }

                route("/product_order") {
                    val productOrderController by inject<ProductOrderController>()
                    put { coHandler("personId", productOrderController::changeProduct) }
                    get { coHandler("personId", productOrderController::getProductOrder) }
                }

                route("/person_registration") {
                    val personRegistrationController by inject<PersonRegistrationController>()
                    post { coHandler("personId", personRegistrationController::create) }
                }

                route("/member") {
                    val memberController by inject<MemberController>()
                    get { coHandler("personId", memberController::getMember) }
                    post("/product") { coHandler("personId", memberController::changeProduct) }
                    put("/cancel") { coHandler("personId", memberController::cancelActiveOrPendingMembership) }
                    put("/{memberId}/assign_parent") {
                        coHandler("memberId", memberController::assignMemberParent)
                    }
                    post("/{memberId}/reactivate") {
                        coHandler("memberId", memberController::reactivateCanceledMember)
                    }
                    put("/{memberId}/reactivate") {
                        coHandler("memberId", memberController::reactivateCanceledMembership)
                    }
                    post("/activation_date") { coHandler("personId", memberController::changeActivationDate) }
                }

                route("/health_declaration_appointment") {
                    val appointmentScheduleController by inject<HealthDeclarationAppointmentScheduleController>()
                    get("/") {
                        coHandler(
                            "personId",
                            appointmentScheduleController::getScheduledHealthDeclarationAppointment
                        )
                    }
                }

                route("/background_check") {
                    val backgroundCheckController by inject<BackgroundCheckController>()
                    get { coHandler("personId", backgroundCheckController::getReport) }
                    post { coHandler("personId", backgroundCheckController::finishProcess) }
                    post("/process") { coHandler("personId", backgroundCheckController::startProcess) }
                }

                route("/contract") {
                    val contractController by inject<ContractController>()
                    get { coHandler("personId", contractController::getContract) }
                    post { coHandler("personId", contractController::generateContract) }

                    post("/update_document") { multipartHandler("personId", contractController::updateDocument) }
                }

                route("/auth") {
                    val personAuthController by inject<PersonAuthController>()
                    post("/generate_new_access_code") {
                        coHandler(
                            "personId",
                            personAuthController::generateNewAccessCode
                        )
                    }
                }

                route("/insurance_portability") {
                    val insurancePortabilityController by inject<InsurancePortabilityController>()
                    val insurancePortabilityV3Controller by inject<InsurancePortabilityV3Controller>()

                    get { coHandler("personId", insurancePortabilityController::findByPerson) }
                    get("/all") { coHandler("personId", insurancePortabilityController::listByPerson) }

                    post { coHandler("personId", insurancePortabilityController::requestPortability) }
                    post("/v2") { coHandler("personId", insurancePortabilityController::requestPortabilityV2) }
                    post("/v3") { coHandler("personId", insurancePortabilityV3Controller::create) }
                    put("/approve") { coHandler("personId", insurancePortabilityController::approve) }
                    put("/decline") { coHandler("personId", insurancePortabilityController::decline) }
                }

                route("/invoice_items") {
                    val invoiceItemsController by inject<InvoiceItemsController>()
                    get { coHandler("personId", invoiceItemsController::listInvoiceItems) }
                    post { coHandler("personId", invoiceItemsController::createInvoiceItems) }

                    route("/{invoice_item_id}") {
                        put("/cancel") { coHandler("invoice_item_id", invoiceItemsController::cancelInvoiceItem) }
                    }
                }

                post("/terminate_session") { coHandler("personId", personController::terminateSession) }

                route("/update_contact_info") {
                    val updatedPersonContactInfoTempController by inject<UpdatedPersonContactInfoTempController>()
                    post { coHandler("personId", updatedPersonContactInfoTempController::create) }
                    delete { coHandler("personId", updatedPersonContactInfoTempController::delete) }
                }
            }
        }

        route("/insurance_portability") {
            val insurancePortabilityV3Controller by inject<InsurancePortabilityV3Controller>()

            route("/{portability_request_id}") {
                put { coHandler("portability_request_id", insurancePortabilityV3Controller::updateRequirements) }
                put("/approve") { coHandler("portability_request_id", insurancePortabilityV3Controller::approve) }
                put("/specific/approve") {
                    coHandler(
                        "portability_request_id",
                        insurancePortabilityV3Controller::approveSpecific
                    )
                }
                put("/decline") { coHandler("portability_request_id", insurancePortabilityV3Controller::decline) }
                put("/cancel") { coHandler("portability_request_id", insurancePortabilityV3Controller::cancel) }
                get("/documents") {
                    coHandler(
                        "portability_request_id",
                        insurancePortabilityV3Controller::getDocuments
                    )
                }
                post("/documents") {
                    multipleFileMultipartHandler(
                        "portability_request_id",
                        insurancePortabilityV3Controller::uploadDocuments
                    )
                }
                put("/request_documents") {
                    coHandler(
                        "portability_request_id",
                        insurancePortabilityV3Controller::requestDocuments
                    )
                }
                put("/archive") { coHandler("portability_request_id", insurancePortabilityV3Controller::archive) }
                put("/unarchive") { coHandler("portability_request_id", insurancePortabilityV3Controller::unarchive) }
            }
        }
        route("/members/{member_id}") {
            val invoicesController by inject<InvoicesController>()

            route("/product_price_listings") {
                val memberProductPriceController by inject<MemberProductPriceController>()

                get { coHandler("member_id", memberProductPriceController::findMemberProductPrices) }
                put("/{product_price_listing_id}") {
                    coHandler(
                        "member_id",
                        "product_price_listing_id",
                        memberProductPriceController::updateMemberProductPrice
                    )
                }
            }

            route("/invoices") {
                get { coHandler("member_id", invoicesController::listInvoices) }
                post { coHandler("member_id", invoicesController::createInvoice) }

                route("/issue_invoice") {
                    post { coHandler("member_id", invoicesController::issueFirstInvoice) }
                }

                route("/issue_first_invoice") {
                    post { coHandler("member_id", invoicesController::issueFirstInvoice) }
                }

                route("/{invoice_id}") {
                    put("/cancel") { coHandler("invoice_id", invoicesController::cancelInvoice) }

                    route("/payments") {
                        post { coHandler("member_id", "invoice_id", invoicesController::createPayment) }

                        route("/{payment_id}") {
                            put("/approve") { coHandler("payment_id", invoicesController::approvePayment) }
                            put("/cancel") { coHandler("payment_id", invoicesController::cancelPayment) }
                        }
                    }
                }
            }
        }

        route("/leads") {
            val leadsController by inject<LeadsController>()

            get { coHandler(leadsController::findByEmail) }
            post { coHandler(leadsController::add) }

            route("/{id}") {
                get { coHandler("id", leadsController::get) }
            }

            post("/reprocess") { coHandler(leadsController::resendLeadsToCrm) }
        }

        route("/onboarding") {
            val operationOnboardingController by inject<OperationOnboardingController>()
            get("/phase/{phaseName}") {
                coHandler("phaseName", operationOnboardingController::getOperationOnboardingPersons)
            }

            route("/{id}") {
                put("/expire") { coHandler("id", onboardingController::expire) }
                put("/ignore-expiration") { coHandler("id", onboardingController::ignoreExpiration) }
                put("/remove-ignore-expiration") { coHandler("id", onboardingController::removeIgnoreExpiration) }
            }
        }

        route("/product_order") {
            val productOrderController by inject<ProductOrderController>()

            route("/{id}") {
                put("/unarchive") { coHandler("id", productOrderController::unarchive) }
            }
        }

        route("/onboarding_contract") {
            val contractController by inject<ContractController>()

            route("/{id}") {
                put("/unarchive") { coHandler("id", contractController::unarchive) }
            }
        }

        route("/person_registration") {
            val personRegistrationController by inject<PersonRegistrationController>()

            route("/{id}") {
                put("/unarchive") { coHandler("id", personRegistrationController::unarchive) }
                put("/archive") { coHandler("id", personRegistrationController::archive) }
            }
        }

        route("/anonymization") {
            val anonymizationController by inject<AnonymizationController>()
            post { coHandler(anonymizationController::anonymize) }
        }

        route("/health_declaration_appointment") {
            val healthDeclarationAppointmentScheduleController by inject<HealthDeclarationAppointmentScheduleController>()
            get("/schedule") { coHandler(healthDeclarationAppointmentScheduleController::listAll) }
            put("/{appointmentId}/no_show") {
                coHandler(
                    "appointmentId",
                    healthDeclarationAppointmentScheduleController::markScheduleAsNoShow
                )
            }
            put("/{appointmentId}/completed") {
                coHandler(
                    "appointmentId",
                    healthDeclarationAppointmentScheduleController::completeSchedule
                )
            }
        }

        route("/promotions") {
            route("/codes") {
                val promoCodeController by inject<PromoCodeController>()
                post { coHandler(promoCodeController::addNewPromoCode) }
            }
        }

        route("/price_listing") {
            val priceListingController by inject<PriceListingController>()

            get { coHandler(priceListingController::list) }
            post { coHandler(priceListingController::create) }

            route("/{price_listing_id}") {
                get { coHandler("price_listing_id", priceListingController::get) }
                delete { coHandler("price_listing_id", priceListingController::delete) }
            }
        }

        route("/backfill") {
            val priceListingBackfillController by inject<BackfillPriceListingController>()
            post("/member_product_price") { coHandler(priceListingBackfillController::backfillMemberProductPrice) }

            val paymentBackfillController by inject<PaymentBackfillController>()
            post("/reissue_boleto") { coHandler(paymentBackfillController::backfillBoletoPayment) }

            val backfillMemberProductcontroller by inject<BackfillMemberProductController>()
            post("/{member_id}/fix_member_selected_products") {
                coHandler(
                    "member_id",
                    backfillMemberProductcontroller::fixMemberSelectedProduct
                )
            }
        }

        route("/company") {
            val companyController by inject<CompanyController>()
            val companyContractController by inject<CompanyContractController>()
            val companySubContractController by inject<CompanySubContractController>()
            val companyActivationFilesController by inject<CompanyActivationFilesController>()
            val companyInvoiceItemsController by inject<CompanyInvoiceItemsController>()
            val companyDealController by inject<CompanyDealController>()
            val companyBeneficiaryController by inject<CompanyBeneficiaryController>()
            val companyContractualRiskController by inject<CompanyContractualRiskController>()
            val companyInvoiceController by inject<CompanyInvoiceController>()

            get("") { coHandler(companyController::findByQuery) }
            post("") { coHandler(companyController::createCompany) }

            route("/ongoing_company_deal") {
                route("/{deal_id}") {
                    put("/cancel") { coHandler("deal_id", companyDealController::cancelCompanyDeal) }
                    put("/reactivate") { coHandler("deal_id", companyDealController::reactivateCompanyDeal) }
                }
            }

            route("/contract") {
                get { coHandler(companyContractController::findByTitle) }
                post("/upload") { multipartHandler(companyContractController::uploadContractAndAdditions) }
                route("/{company_contract_id}") {
                    get("/") { coHandler("company_contract_id", companyContractController::getById) }
                    put("/") { coHandler("company_contract_id", companyContractController::updateCompanyContract) }
                }
            }

            route("subcontract/{sub_contract_id}") {
                put { coHandler("sub_contract_id", companySubContractController::updateCompanySubContract) }
                get { coHandler("sub_contract_id", companySubContractController::getById) }

                post("first_payment") {
                    coHandler(
                        "sub_contract_id",
                        companyInvoiceController::createPreActivationInvoice
                    )
                }

                route("pre_activation_invoice") {
                    post {
                        coHandler(
                            "sub_contract_id",
                            companyInvoiceController::createPreActivationInvoice
                        )
                    }

                    delete("/{invoice_id}") {
                        coHandler("sub_contract_id", "invoice_id", companyInvoiceController::cancelPreActivationInvoice)
                    }

                    post("/{invoice_id}/payment") {
                        coHandler(
                            "sub_contract_id",
                            "invoice_id",
                            companyInvoiceController::createPaymentForPreActivation
                        )
                    }
                }
                route("/invoice_info") {
                    get { coHandler("sub_contract_id", companySubContractController::listCompanySubContractInvoices) }
                }
            }

            route("/invoice_group/{member_invoice_group_id}") {
                put("cancel_first_payment") {
                    coHandler(
                        "member_invoice_group_id",
                        companyInvoiceController::cancelFirstInvoicePayment
                    )
                }
            }

            route("/invoice_group/{member_invoice_group_id}") {
                post("payment") { coHandler("member_invoice_group_id", companyInvoiceController::createInvoicePayment) }
            }

            route("/{company_id}") {
                get("") { coHandler("company_id", companyController::getById) }
                put("") { coHandler("company_id", companyController::updateCompany) }

                post("contract") { coHandler("company_id", companyContractController::createCompanyContract) }

                get("/invoice_info") { coHandler("company_id", companyInvoiceController::getInvoiceInfoByCompanyId) }

                get("/beneficiaries") {
                    coHandler(
                        "company_id",
                        companyBeneficiaryController::getBeneficiariesByCompanyId
                    )
                }

                route("/invoice_group/{member_invoice_group_id}") {
                    post("payment") {
                        coHandler(
                            "member_invoice_group_id",
                            companyInvoiceController::createInvoicePayment
                        )
                    }
                }

                route("/invoice_payment/{invoice_payment_id}") {
                    put("approve") { coHandler("invoice_payment_id", companyInvoiceController::approveInvoicePayment) }
                    put("cancel") { coHandler("invoice_payment_id", companyInvoiceController::cancelInvoicePayment) }
                }

                route("/subcontract") {
                    get { coHandler("company_id", companySubContractController::listCompanySubContractByCompanyId) }

                    route("{sub_contract_id}/invoice_items") {
                        get {
                            coHandler(
                                "company_id",
                                "sub_contract_id",
                                companyInvoiceItemsController::listInvoiceItems
                            )
                        }
                        post {
                            coHandler(
                                "company_id",
                                "sub_contract_id",
                                companyInvoiceItemsController::createInvoiceItems
                            )
                        }

                        route("/{invoice_item_id}") {
                            put {
                                coHandler(
                                    "invoice_item_id",
                                    companyInvoiceItemsController::updateInvoiceItem
                                )
                            }
                            put("/cancel") {
                                coHandler(
                                    "invoice_item_id",
                                    companyInvoiceItemsController::cancelInvoiceItem
                                )
                            }
                        }
                    }

                    route("{sub_contract_id}/company_product_price_listing") {

                        route("/{cppl_id}") {
                            delete {
                                coHandler("cppl_id", companySubContractController::deleteCompanyProductPriceListingById)
                            }
                        }

                        delete {
                            coHandler(
                                "sub_contract_id",
                                companySubContractController::deleteCompanyProductPriceListingBySubcontractId
                            )
                        }
                    }
                }

                route("/contract/{contract_id}/subcontract") {
                    post {
                        coHandler(
                            "company_id",
                            "contract_id",
                            companySubContractController::createCompanySubContract
                        )
                    }
                }

                route("/activation_files") {
                    get {
                        coHandler("company_id", companyActivationFilesController::getActivationFilesByCompanyId)
                    }
                }

                route("/contractual_risk") {
                    get {
                        coHandler("company_id", companyContractualRiskController::getCompanyContractualRisk)
                    }
                }
            }
        }

        route("company_deal") {
            val dealController by inject<OngoingCompanyDealController>()
            route("/{deal_id}") {
                put("/send_contract") { coHandler("deal_id", dealController::sendContract) }
            }
        }

        route("/beneficiary") {
            val beneficiaryController by inject<BeneficiaryController>()
            val beneficiaryOnboardingController by inject<BeneficiaryOnboardingController>()

            get("/list") { coHandler(beneficiaryController::index) }
            post { coHandler(beneficiaryController::create) }
            post("/batch") { coHandler(beneficiaryController::addBatch) }
            post("/move_to_next_phase") { coHandler(beneficiaryOnboardingController::moveToNextPhaseByBeneficiaryIds) }

            route("/{beneficiary_id}") {
                get { coHandler("beneficiary_id", beneficiaryController::getBeneficiaryDetails) }
                put { coHandler("beneficiary_id", beneficiaryController::update) }
                post("updateGracePeriodType") {
                    coHandler(
                        "beneficiary_id",
                        beneficiaryController::updateGracePeriodType
                    )
                }
                post("/onboarding") { coHandler("beneficiary_id", beneficiaryOnboardingController::createOnboarding) }
                get("/onboarding") { coHandler("beneficiary_id", beneficiaryOnboardingController::getOnboarding) }
            }
        }
    }
}
