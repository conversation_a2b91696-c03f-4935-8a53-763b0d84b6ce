package br.com.alice.staff.logics

import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Predicate.Companion.ContainsAnyPredicateUsage
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.data.layer.models.SpecialistAppointmentType
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.models.SpecialtyTiers
import br.com.alice.data.layer.services.HealthProfessionalModelDataService
import java.util.UUID

object HealthProfessionalPredicatesLogic {
    fun buildFilterQuery(
        range: IntRange,
        status: SpecialistStatus? = null,
        specialtiesIds: List<UUID>? = null,
        appointmentTypes: List<SpecialistAppointmentType>? = null,
        namePrefix: String? = null,
        showOnApp: Boolean? = null,
        ids: List<UUID>? = null,
        specialtyTiers: List<SpecialtyTiers>? = null,
    ): QueryBuilder <
        HealthProfessionalModelDataService.FieldOptions,
        HealthProfessionalModelDataService.OrderingOptions
        > =
        buildFilterBaseFilterQuery(status, specialtiesIds, appointmentTypes, namePrefix, showOnApp, ids, specialtyTiers)
            .orderBy { name }
            .offset { range.first }
            .limit { range.count() }

    fun buildFilterQuery(
        status: SpecialistStatus? = null,
        specialtiesIds: List<UUID>? = null,
        appointmentTypes: List<SpecialistAppointmentType>? = null,
        namePrefix: String? = null,
        showOnApp: Boolean? = null,
        ids: List<UUID>? = null,
        specialtyTiers: List<SpecialtyTiers>? = null,
    ): QueryBuilder <
        HealthProfessionalModelDataService.FieldOptions,
        HealthProfessionalModelDataService.OrderingOptions
        > =
        buildFilterBaseFilterQuery(status, specialtiesIds, appointmentTypes, namePrefix, showOnApp, ids, specialtyTiers)

    private fun buildFilterBaseFilterQuery(
        status: SpecialistStatus?,
        specialtiesIds: List<UUID>?,
        appointmentTypes: List<SpecialistAppointmentType>?,
        namePrefix: String?,
        showOnApp: Boolean?,
        ids: List<UUID>?,
        specialtyTiers: List<SpecialtyTiers>?
    ): QueryBuilder <
        HealthProfessionalModelDataService.FieldOptions,
        HealthProfessionalModelDataService.OrderingOptions
        > =
        if (
            status == null &&
            specialtiesIds == null &&
            appointmentTypes == null &&
            namePrefix == null &&
            showOnApp == null &&
            ids == null &&
            specialtyTiers == null
        )
            QueryBuilder(
                HealthProfessionalModelDataService.FieldOptions(),
                HealthProfessionalModelDataService.OrderingOptions()
            )
        else
            QueryBuilder(
                HealthProfessionalModelDataService.FieldOptions(),
                HealthProfessionalModelDataService.OrderingOptions()
            ).where { buildFilterPredicates(status, specialtiesIds, appointmentTypes, namePrefix, showOnApp, ids, specialtyTiers) !! }

    private fun buildFilterPredicates(
        status: SpecialistStatus?,
        specialtiesIds: List<UUID>?,
        appointmentTypes: List<SpecialistAppointmentType>?,
        namePrefix: String?,
        showOnApp: Boolean?,
        ids: List<UUID>?,
        specialtyTiers: List<SpecialtyTiers>?
    ): Predicate? =
        withStatus(status)
            .withSpecialties(specialtiesIds)
            .withAppointmentTypes(appointmentTypes)
            .withNamePrefix(namePrefix)
            .withShowOnApp(showOnApp)
            .withIds(ids)
            .withSpecialtyTiers(specialtyTiers)

    private fun withStatus(status: SpecialistStatus?): Predicate? =
        when (status) {
            null -> null
            else -> Predicate.eq(HealthProfessionalModelDataService.FieldOptions().status, status)
        }

    private fun Predicate?.withSpecialties(specialtiesIds: List<UUID>?): Predicate? =
        when {
            this == null && specialtiesIds != null ->
                Predicate.inList(HealthProfessionalModelDataService.FieldOptions().specialtyId, specialtiesIds)
            this != null && specialtiesIds != null ->
                this and Predicate.inList(HealthProfessionalModelDataService.FieldOptions().specialtyId, specialtiesIds)
            else -> this
        }

    private fun Predicate?.withNamePrefix(namePrefix: String?): Predicate? =
        when {
            this == null && namePrefix != null ->
                Predicate.search(HealthProfessionalModelDataService.FieldOptions().searchTokens, namePrefix)
            this != null && namePrefix != null ->
                this and Predicate.search(HealthProfessionalModelDataService.FieldOptions().searchTokens, namePrefix)
            else -> this
        }

    @OptIn(ContainsAnyPredicateUsage::class)
    private fun Predicate?.withAppointmentTypes(
        appointmentTypes: List<SpecialistAppointmentType>?,
    ): Predicate? =
        when {
            this == null && appointmentTypes != null ->
                Predicate.containsAny(
                    HealthProfessionalModelDataService.FieldOptions().appointmentTypes,
                    appointmentTypes
                )
            this != null && appointmentTypes != null ->
                this and Predicate.containsAny(
                    HealthProfessionalModelDataService.FieldOptions().appointmentTypes,
                    appointmentTypes
                )
            else -> this
        }

    private fun Predicate?.withShowOnApp(showOnApp: Boolean?): Predicate? =
        when {
            this == null && showOnApp != null ->
                Predicate.eq(HealthProfessionalModelDataService.FieldOptions().showOnApp, showOnApp)
            this != null && showOnApp != null ->
                this and Predicate.eq(HealthProfessionalModelDataService.FieldOptions().showOnApp, showOnApp)
            else -> this
        }

    private fun Predicate?.withIds(ids: List<UUID>?): Predicate? =
        when {
            this == null && ids != null ->
                Predicate.inList(HealthProfessionalModelDataService.FieldOptions().id, ids)
            this != null && ids != null ->
                this and Predicate.inList(HealthProfessionalModelDataService.FieldOptions().id, ids)
            else -> this
        }

    private fun Predicate?.withSpecialtyTiers(specialtyTiers: List<SpecialtyTiers>?): Predicate? =
        when {
            this == null && specialtyTiers != null ->
                buildSpecialtyFilters(specialtyTiers)
            this != null && specialtyTiers != null ->
                this and buildSpecialtyFilters(specialtyTiers)
            else -> this
        }

    @OptIn(OrPredicateUsage::class)
    private fun buildSpecialtyFilters(specialtyTiers: List<SpecialtyTiers>): Predicate =
        scope(
            specialtyTiers.fold(scope(Predicate.False)) { oldPredicate, specialtyTiers ->
                oldPredicate.or(
                    scope(
                        HealthProfessionalModelDataService.FieldOptions().tier.inList(specialtyTiers.tiers).and(
                            HealthProfessionalModelDataService.FieldOptions().specialtyId.eq(specialtyTiers.specialtyId)
                        )
                    )
                )
            }
        )
}
