package br.com.alice.itau.clients.models

import com.google.gson.annotations.SerializedName

data class ItauBolecodeCreateRequest(
    @SerializedName("etapa_processo_boleto")
    val requestType: String = "efetivacao",
    @SerializedName("beneficiario")
    val beneficiary: ItauBolecodeBeneficiaryCreateRequest,
    @SerializedName("dado_boleto")
    val boletoData: ItauBoletoCodeBoletoDataCreateRequest,
)

data class ItauBoletoCodeBoletoDataCreateRequest(
    @SerializedName("descricao_instrumento_cobranca")
    val descriptionCharge: String = ItauBoletoDescriptionCharge.BOLETO.code,
    @SerializedName("tipo_boleto")
    val type: String = "a vista",
    @SerializedName("codigo_carteira")
    val walletCode: Int = 109,
    @SerializedName("codigo_especie")
    val speciesCode: String = "08",
    @SerializedName("recebimento_divergente")
    val divergentReceipt: ItauBoletoConfigDivergentReceipt,
    @SerializedName("pagador")
    val payer: ItauBoletoPayerCreateRequest,
    @SerializedName("juros")
    val interest: ItauBoletoInterestCreateRequest?,
    @SerializedName("multa")
    val fine: ItauBoletoFineCreateRequest?,
    @SerializedName("dados_individuais_boleto")
    val individualBoleto: List<ItauBoletoIndividualCreateRequest>,
    @SerializedName("dados_qrcode")
    val qrCode: ItauBolecodeQrCodeCreateRequest,
)

data class ItauBolecodeBeneficiaryCreateRequest(
    @SerializedName("id_beneficiario")
    val aliceBankAccountId: String,
)

data class ItauBolecodeQrCodeCreateRequest(
    @SerializedName("chave")
    val alicePixKey: String,
)

data class ItauBolecodeCreateResponse(
    @SerializedName("data")
    val data: ItauBolecodeCreateDataResponse,
)

data class ItauBolecodeCreateDataResponse(
    @SerializedName("dado_boleto")
    val boleto: ItauBolecodeBoletoListCreateResponse,
    @SerializedName("dados_qrcode")
    val pix: ItauBolecodePixResponse
)

data class ItauBolecodeBoletoListCreateResponse(
    @SerializedName("dados_individuais_boleto")
    val individualBoleto: List<ItauBolecodeBoletoResponse>,
)

data class ItauBolecodeBoletoResponse(
    @SerializedName("id_boleto_individual")
    val id: String,
    @SerializedName("codigo_barras")
    val barcode: String,
    @SerializedName("numero_linha_digitavel")
    val digitableLine: String
)

data class ItauBolecodePixResponse(
    @SerializedName("txid")
    val id: String,
    @SerializedName("emv")
    val copyAndPaste: String,
    @SerializedName("base64")
    val qrCodeBase64: String
)
