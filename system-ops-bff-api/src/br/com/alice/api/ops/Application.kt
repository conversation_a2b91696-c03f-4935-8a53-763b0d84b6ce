package br.com.alice.api.ops

import br.com.alice.action.plan.ioc.ActionPlanDomainClientModule
import br.com.alice.amas.ioc.AmasDomainClientModule
import br.com.alice.api.ops.controllers.*
import br.com.alice.api.ops.controllers.channels.AliceAgoraWorkingHoursController
import br.com.alice.api.ops.controllers.channels.ChannelFupController
import br.com.alice.api.ops.controllers.channels.ChannelMacroController
import br.com.alice.api.ops.controllers.channels.ChannelStaffController
import br.com.alice.api.ops.controllers.channels.ChannelTagController
import br.com.alice.api.ops.controllers.fhir.ProviderAccessController
import br.com.alice.api.ops.controllers.healthcampaign.HealthCampaignController
import br.com.alice.api.ops.controllers.healthcare_resource.HealthcareBundleController
import br.com.alice.api.ops.controllers.healthcare_resource.HealthcareResourceController
import br.com.alice.api.ops.controllers.healthcare_resource.HealthcareResourceGroupController
import br.com.alice.api.ops.controllers.healthcondition.HealthConditionAxisController
import br.com.alice.api.ops.controllers.healthcondition.HealthConditionController
import br.com.alice.api.ops.controllers.healthcondition.HealthConditionRelatedController
import br.com.alice.api.ops.controllers.healthlogics.AutomaticHealthLogicsController
import br.com.alice.api.ops.controllers.healthlogics.ClinicalOutcomesConsolidatedCalculatorConfController
import br.com.alice.api.ops.controllers.healthlogics.HealthFormOutcomeCalculatorConfController
import br.com.alice.api.ops.controllers.healthlogics.HealthLogicsController
import br.com.alice.api.ops.controllers.protocol.ProtocolController
import br.com.alice.api.ops.controllers.riskCalculation.RiskCalculationConfController
import br.com.alice.api.ops.controllers.riskCalculation.RiskGroupCalculatorConfigController
import br.com.alice.api.ops.controllers.sales_channel.SalesAgentController
import br.com.alice.api.ops.controllers.sales_channel.SalesFirmController
import br.com.alice.api.ops.controllers.sales_channel.SalesFirmStaffController
import br.com.alice.api.ops.routes.apiRoutes
import br.com.alice.api.ops.services.AuthService
import br.com.alice.api.ops.services.HealthProfessionalOpsProfileInternalService
import br.com.alice.api.ops.services.ProcessTussProcedureSpecialtyFileService
import br.com.alice.api.ops.services.StaffInternalService
import br.com.alice.app.content.ioc.AppContentDomainClientModule
import br.com.alice.appointment.ioc.AppointmentDomainClientModule
import br.com.alice.atlas.ioc.AtlasDomainClientModule
import br.com.alice.bottini.ioc.BottiniClientModule
import br.com.alice.business.ioc.BusinessDomainClientModule
import br.com.alice.channel.ioc.ChannelDomainClientModule
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupBffApi
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.core.RunningMode.PRODUCTION
import br.com.alice.common.googlemaps.ioc.GoogleMapsModule
import br.com.alice.common.headerOpenTelemetryTraceId
import br.com.alice.common.headerTraceId
import br.com.alice.common.ioc.NotificationModule
import br.com.alice.common.notification.installNotificationSubscriptionAutoConfirm
import br.com.alice.common.rfc.HttpInvoker
import br.com.alice.common.rfc.Invoker
import br.com.alice.common.storage.FileStorage
import br.com.alice.common.storage.LocalFileStorage
import br.com.alice.common.storage.S3FileStorage
import br.com.alice.communication.ioc.CommunicationModule
import br.com.alice.coverage.ioc.CoverageDomainClientModule
import br.com.alice.data.layer.SYSTEM_OPS_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.services.PolicyDescriptionDataService
import br.com.alice.data.layer.services.PolicyDescriptionDataServiceClient
import br.com.alice.ehr.ioc.EhrDomainClientModule
import br.com.alice.exec.indicator.ioc.ExecIndicatorDomainClientModule
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.fhir.ioc.FhirDomainClientModule
import br.com.alice.filevault.ioc.FileVaultClientModule
import br.com.alice.haoc.ioc.HaocIntegrationClientModule
import br.com.alice.healthcondition.ioc.HealthConditionDomainClientModule
import br.com.alice.healthlogic.ioc.HealthLogicDomainClientModule
import br.com.alice.healthplan.ioc.HealthPlanDomainClientModule
import br.com.alice.limbo.ioc.LimboClientModule
import br.com.alice.marauders.map.ioc.MaraudersMapDomainClientModule
import br.com.alice.member.onboarding.ioc.MemberOnboardingDomainClientModule
import br.com.alice.membership.ioc.MembershipClientModule
import br.com.alice.moneyin.ioc.MoneyInClientModule
import br.com.alice.onboarding.ioc.OnboardingClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.product.ioc.ProductDomainClientModule
import br.com.alice.provider.ioc.ProviderDomainClientModule
import br.com.alice.questionnaire.ioc.QuestionnaireDomainClientModule
import br.com.alice.sales_channel.ioc.SalesChannelDomainClientModule
import br.com.alice.schedule.ioc.AppointmentScheduleDomainClientModule
import br.com.alice.screening.ioc.ScreeningDomainClientModule
import br.com.alice.secondary.attention.ioc.SecondaryAttentionDomainClientModule
import br.com.alice.sortinghat.ioc.SortingHatDomainClientModule
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.ioc.StaffDomainClientModule
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.server.application.Application
import io.ktor.server.plugins.cors.routing.CORS
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.core.qualifier.named
import org.koin.dsl.module
import java.time.Duration
import br.com.alice.api.ops.controllers.staff.StaffController as StaffControllerV2

private const val MAX_AGE_IN_SECONDS = 7L

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    fun dependencyInjectionModules() = listOf(
        NotificationModule,
        MembershipClientModule,
        EhrDomainClientModule,
        FeatureConfigDomainClientModule,
        ChannelDomainClientModule,
        ExecIndicatorDomainClientModule,
        ProviderDomainClientModule,
        HaocIntegrationClientModule,
        HealthConditionDomainClientModule,
        HealthLogicDomainClientModule,
        QuestionnaireDomainClientModule,
        PersonDomainClientModule,
        MoneyInClientModule,
        LimboClientModule,
        ProviderDomainClientModule,
        FhirDomainClientModule,
        BusinessDomainClientModule,
        OnboardingClientModule,
        StaffDomainClientModule,
        MaraudersMapDomainClientModule,
        CoverageDomainClientModule,
        AppointmentScheduleDomainClientModule,
        FileVaultClientModule,
        ActionPlanDomainClientModule,
        AtlasDomainClientModule,
        BottiniClientModule,
        HealthPlanDomainClientModule,
        ClinicalAccountDomainClientModule,
        AmasDomainClientModule,
        AppContentDomainClientModule,
        MemberOnboardingDomainClientModule,
        AppointmentDomainClientModule,
        SecondaryAttentionDomainClientModule,
        ProductDomainClientModule,
        ScreeningDomainClientModule,
        SortingHatDomainClientModule,
        SalesChannelDomainClientModule,
        CommunicationModule,
        GoogleMapsModule,

        module(createdAtStart = true) {

            single<Invoker> { HttpInvoker() }

            when (ServiceConfig.runningMode) {
                PRODUCTION -> {
                    single<FileStorage> { S3FileStorage() }
                }

                else -> {
                    single<FileStorage> { LocalFileStorage() }
                }
            }

            // Force StaffService sync all injections
            single<StaffService> { get(named("sync-staff-service")) }

            // Services
            single { AuthService(get()) }
            single { ProcessTussProcedureSpecialtyFileService(get(), get()) }

            single<PolicyDescriptionDataService> { PolicyDescriptionDataServiceClient(get()) }

            // Controllers
            single { AuthController(get()) }
            single { CassiSpecialistControllerV2(get()) }
            single { ClinicalOutcomesConsolidatedCalculatorConfController(get()) }
            single { GasB2cAllowedMembersController(get()) }
            single { HaocClaimController(get(), get()) }
            single { HealthcareTeamControllerV2(get(), get()) }
            single { HealthController(SYSTEM_OPS_ROOT_SERVICE_NAME) }
            single { PersonClinicalAccountController(get(), get(), get(), get(), get()) }
            single { PersonController(get()) }
            single { PersonDefaulterController() }
            single { PersonEhrProfileController(get(), get(), get(), get()) }
            single { PersonEhrProfileLightController(get(), get(), get()) }
            single { PersonInternalReferenceController(get(), get()) }
            single { ServiceScriptController(get(), get()) }
            single { AppointmentScheduleOptionsController(get(), get()) }
            single { TestCodeController(get()) }
            single { TestCodeAnalyteController(get()) }
            single { AliceAgoraWorkingHoursController(get()) }
            single { ChannelMacroController(get()) }
            single { ChannelFupController(get()) }
            single { ChannelTagController(get()) }
            single { ChannelStaffController(get(), get()) }
            single { PolicyDescriptionController(get()) }
            single { PrescriptionSentenceController(get(), get()) }
            single { ExecIndicatorAuthorizersController(get(), get()) }
            single { FaqContentController(get(), get()) }
            single { FaqGroupController(get()) }
            single { ProviderController(get()) }
            single { UpdateAppRuleController(get()) }
            single { ProviderUnitController(get(), get(), get()) }
            single { ProviderUnitGroupController(get(), get()) }
            single { ProviderTestCodeController(get(), get(), get()) }
            single { ProviderUnitTestCodeController(get(), get()) }
            single { MedicalSpecialtyController(get(), get(), get()) }
            single { CboCodeController(get()) }
            single { ClinicalStaffController(get()) }
            single { HealthConditionController(get(), get()) }
            single { HealthConditionAxisController(get()) }
            single { HealthConditionRelatedController(get(), get()) }
            single { TestCodePackageController(get()) }
            single { TestPreparationController(get()) }
            single { AutomaticTaskEngineController() }
            single { ProductBundleController(get(), get(), get()) }
            single { HealthFormController(get(), get(), get()) }
            single { HealthFormSectionController(get()) }
            single { HealthFormQuestionController(get(), get()) }
            single { ProductController(get(), get(), get()) }
            single { ABTestingController(get(), get()) }
            single { HealthFormAutomaticTaskController(get(), get()) }
            single { BillingAccountablePartyController(get()) }
            single { DeadletterController(get()) }
            single { ProviderAccessController(get()) }
            single {
                BeneficiaryController(
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get()
                )
            }
            single { InsurancePortabilityHealthInsuranceController(get()) }
            single { BeneficiaryOnboardingController() }
            single { RiskGroupCalculatorConfigController(get()) }
            single { ProcedureProviderController(get()) }
            single { AppointmentScheduleEventTypeController(get()) }
            single { MemberOnboardingTemplateController(get(), get(), get()) }
            single { AppointmentTemplateController(get()) }
            single { ProfessionalTierProcedureValueController(get()) }
            single { ProductRecommendationController(get()) }
            single { PromoCodeController(get()) }
            single { TussProcedureSpecialtyController(get(), get(), get(), get()) }
            single { AnalyteOutcomeMappingController(get(), get(), get()) }
            single { SpecialistOpinionController(get()) }
            single { RoutingRuleController(get()) }
            single { StructuredAddressController(get()) }
            single { MapsAddressController(get()) }
            single { LivanceProviderUnitController(get()) }

            // controllers v2
            single { AutomaticHealthLogicsController(get()) }
            single { AppointmentMacroController(get()) }
            single { CassiMemberController(get()) }
            single { CompanyController(get(), get()) }
            single { CompanySubcontractController(get()) }
            single { HealthPlanTaskTemplateController(get(), get(), get()) }
            single { HealthPlanTaskGroupTemplateController(get(), get()) }
            single { HealthMeasurementTypeController(get()) }
            single { HealthMeasurementCategoryController(get()) }
            single { HealthFormOutcomeCalculatorConfController(get(), get(), get()) }
            single { HealthCampaignController(get(), get(), get()) }
            single { HealthLogicsController(get()) }
            single { RiskCalculationConfController(get(), get(), get()) }
            single { OutcomeConfController(get()) }
            single { HealthDemandMonitoringController(get(), get(), get()) }
            single { ProtocolController(get(), get()) }
            single { HealthcareResourceController(get(), get(), get(), get()) }
            single { HealthcareResourceGroupController(get()) }
            single { HealthcareBundleController(get(), get(), get(), get()) }

            single { SalesFirmController(get()) }
            single { SalesFirmStaffController(get(), get()) }

            single { CoveredGeoRegionController(get()) }

            single { CsatTemplateController(get()) }
            single { SalesAgentController(get()) }
            single { VicProductOptionController(get(), get(), get()) }
            single { StaffControllerV2(get(), get()) }

            //Internal Service
            single { HealthProfessionalOpsProfileInternalService(get()) }
            single { StaffInternalService(get(), get(), get(), get()) }
        }
    )
}

@JvmOverloads
fun Application.module(
    dependencyInjectionModules: List<Module> =
        ApplicationModule.dependencyInjectionModules(),
) {
    setupBffApi(dependencyInjectionModules, withOriginalErrorMessage = true) {
        install(CORS) {
            allowMethod(HttpMethod.Get)
            allowMethod(HttpMethod.Post)
            allowMethod(HttpMethod.Put)
            allowMethod(HttpMethod.Delete)
            allowMethod(HttpMethod.Options)
            allowHeader(HttpHeaders.Range)
            allowHeader(HttpHeaders.Authorization)
            allowHeader(HttpHeaders.ContentType)
            allowHeader(HttpHeaders.ContentRange)
            allowHeader("Session-Id")
            allowHeadersPrefixed("X-Datadog-")
            allowHeader("traceparent")
            exposeHeader(HttpHeaders.ContentRange)
            exposeHeader(headerTraceId)
            exposeHeader(headerOpenTelemetryTraceId)
            // TODO: be more restrict on hosts
            anyHost()
            maxAgeInSeconds = Duration.ofDays(MAX_AGE_IN_SECONDS).seconds
        }

        routing {
            application.attributes.put(PolicyRootServiceKey, SYSTEM_OPS_ROOT_SERVICE_NAME)

            apiRoutes()
        }

        installNotificationSubscriptionAutoConfirm()
        featureConfigBootstrap(
            FeatureNamespace.AOS,
            FeatureNamespace.SYSTEM_OPS,
            FeatureNamespace.EXEC_INDICATOR
        )
    }
}
