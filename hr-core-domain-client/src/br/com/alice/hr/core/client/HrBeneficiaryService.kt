package br.com.alice.hr.core.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.hr.core.model.DependentTransport
import br.com.alice.hr.core.model.TemplateSheetResult
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HrBeneficiaryService : Service {

    override val namespace get() = "hr-core"
    override val serviceName get() = "beneficiary"

    suspend fun createDependent(dependent: DependentTransport, companyId: UUID): Result<Beneficiary, Throwable>
    suspend fun getRelationTypes(companyId: UUID, subcontractId: UUID): Result<List<ParentBeneficiaryRelationType>, Throwable>
    suspend fun getBeneficiarySheetTemplate(companyId: UUID): Result<TemplateSheetResult, Throwable>
}
