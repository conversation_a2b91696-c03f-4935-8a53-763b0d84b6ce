package br.com.alice.tiss.services.internal

import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.exec.indicator.models.RequesterData
import br.com.alice.tiss.clients.v4_01_00.GuiaClient
import br.com.alice.tiss.models.CreateGuiaHospitalizationExtensionRequest
import br.com.alice.tiss.models.CreateGuiaHospitalizationRequest
import br.com.alice.tiss.models.CreateGuiaRequest
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

class GuiaServiceTest {

    private val guiaClient: GuiaClient = mockk()

    private val service = GuiaService(guiaClient)

    private val createGuiaRequest = CreateGuiaRequest(
        procedures = listOf(
            GuiaProcedure(table = "22", code = "123", quantity = 1, description = ""),
            GuiaProcedure(table = "22", code = "545", quantity = 1, description = "")
        ),
        requester = RequesterData(name = "Alice Operadora", providerCode = "34266553000102"),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE
        ),
        guiaNumber = "983732412324",
        requestDate = LocalDate.now(),
        beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        ansCode = "1234",
        authorizationType = "1",
        attendanceCharacter = "1",
        origin = GuiaOrigin.EHR,
        attendanceRegime = "1",
        accidentIndication = "1"
    )

    private val createGuiaHospitalizationRequest = CreateGuiaHospitalizationRequest(
        procedures = listOf(
            GuiaProcedure(table = "22", code = "123", quantity = 1, description = ""),
            GuiaProcedure(table = "22", code = "545", quantity = 1, description = "")
        ),
        requester = RequesterData(name = "Alice Operadora", providerCode = "34266553000102"),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE
        ),
        guiaNumber = "983732412324",
        requestDate = LocalDate.now(),
        beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        ansCode = "1234",
        authorizationType = "1",
        attendanceCharacter = "1",
        origin = GuiaOrigin.EHR,
        attendanceRegime = "1",
        accidentIndication = "1",
        recommendedAdmissionDate = LocalDate.now(),
        admissionType = "1",
        admissionDaysRequested = 1.toBigInteger(),
        opmeIndicator = false,
        quimioterapyIndicator = false,
        clinicalIndication = "1"
    )

    private val createGuiaHospitalizationExtensionRequest = CreateGuiaHospitalizationExtensionRequest(
        procedures = listOf(
            GuiaProcedure(table = "22", code = "123", quantity = 1, description = ""),
            GuiaProcedure(table = "22", code = "545", quantity = 1, description = "")
        ),
        requester = RequesterData(name = "Alice Operadora", providerCode = "34266553000102"),
        professional = Professional(
            name = "Robson",
            council = CouncilType.CRM,
            councilNumber = "123",
            councilState = State.CE
        ),
        guiaNumber = "983732412324",
        requestDate = LocalDate.now(),
        beneficiary = Beneficiary(nationalId = "123", newBornAttendance = false, name = "Teste"),
        ansCode = "1234",
        origin = GuiaOrigin.EHR,
        clinicalIndication = "1",
        referenceGuiaNumber = "123",
        additionalDays = 1
    )

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    @Test
    fun `#createGuiaExam should throw error if procedures is empty`() = runBlocking {
        val result = service.createGuiaExam(createGuiaRequest.copy(procedures = emptyList()))

        ResultAssert.assertThat(result).isFailureOfType(NotFoundException::class)
        coVerifyNone { guiaClient.createGuiaExam(any()) }
    }

    @Test
    fun `#createGuiaExam should throw error if cnpj is invalid`() = runBlocking {
        val result = service.createGuiaExam(createGuiaRequest.copy(requester = RequesterData("123", "Teste")))

        ResultAssert.assertThat(result).isFailureOfType(InvalidArgumentException::class)
        coVerifyNone { guiaClient.createGuiaExam(any()) }
    }

    @Test
    fun `#createGuiaExam should call createGuiaExam correctly`() = runBlocking {
        coEvery {
            guiaClient.createGuiaExam(createGuiaRequest)
        } returns true.success()

        val result = service.createGuiaExam(createGuiaRequest)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { guiaClient.createGuiaExam(createGuiaRequest) }
    }

    @Test
    fun `#createGuiaHospitalization should throw error if cnpj is invalid`() = runBlocking {
        val result = service.createGuiaHospitalization(
            createGuiaHospitalizationRequest.copy(
                requester = RequesterData(
                    "123",
                    "Teste"
                )
            )
        )

        ResultAssert.assertThat(result).isFailureOfType(InvalidArgumentException::class)
        coVerifyNone { guiaClient.createGuiaExam(any()) }
    }

    @Test
    fun `#createGuiaHospitalization should call createGuiaHospitalization correctly`() = runBlocking {
        coEvery {
            guiaClient.createGuiaHospitalization(createGuiaHospitalizationRequest)
        } returns true.success()

        val result = service.createGuiaHospitalization(createGuiaHospitalizationRequest)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { guiaClient.createGuiaHospitalization(createGuiaHospitalizationRequest) }
    }

    @Test
    fun `#createGuiaHospitalizationExtension should throw error if cnpj is invalid`() = runBlocking {
        val result = service.createGuiaHospitalizationExtension(
            createGuiaHospitalizationExtensionRequest.copy(requester = RequesterData("123", "Teste"))
        )

        ResultAssert.assertThat(result).isFailureOfType(InvalidArgumentException::class)
        coVerifyNone { guiaClient.createGuiaHospitalizationExtension(any()) }
    }

    @Test
    fun `#createGuiaHospitalizationExtension should call createGuiaHospitalizationExtension correctly`() = runBlocking {
        coEvery {
            guiaClient.createGuiaHospitalizationExtension(createGuiaHospitalizationExtensionRequest)
        } returns true.success()

        val result = service.createGuiaHospitalizationExtension(createGuiaHospitalizationExtensionRequest)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { guiaClient.createGuiaHospitalizationExtension(createGuiaHospitalizationExtensionRequest) }
    }
}
