package br.com.alice.itau.consumers

import br.com.alice.common.consumer.Consumer
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.itau.SERVICE_NAME
import br.com.alice.itau.events.ItauBoletoPaymentWebhookReceivedEvent
import br.com.alice.itau.events.ItauPixPaymentWebhookReceivedEvent
import br.com.alice.itau.services.internal.ItauPaymentService
import br.com.alice.moneyin.event.AcquirerPaymentWebhookReceivedEvent
import br.com.alice.moneyin.event.AcquirerPaymentWebhookReceivedEventPayload
import com.github.kittinunf.result.success
import com.google.gson.Gson
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset

class ItauPaymentWebhookReceivedConsumer(
    private val kafkaProducerService: KafkaProducerService,
    private val itauPaymentService: ItauPaymentService,
) : Consumer(SERVICE_NAME) {

    suspend fun emitAcquirerPaymentWebhookReceivedEventForPix(event: ItauPixPaymentWebhookReceivedEvent) =
        withSubscribersEnvironment {
            logger.info("Consuming ItauPixPaymentWebhookReceivedEvent", "event" to event)
            val payload = event.payload
            val itauPayment = itauPaymentService.getByPixId(payload.pixId)
                .getOrNullIfNotFound()
            if (itauPayment == null) {
                logger.warn("Pix payment not found", "pixId" to event.payload.pixKey)
                return@withSubscribersEnvironment true.success()
            }
            itauPaymentService.update(
                itauPayment.copy(
                    paidResponse = Gson().toJson(event.payload)
                )
            ).get()

            kafkaProducerService.produce(
                AcquirerPaymentWebhookReceivedEvent(
                    AcquirerPaymentWebhookReceivedEventPayload(
                        invoicePaymentId = itauPayment.invoicePaymentId,
                        paidAt = LocalDateTime.ofInstant(Instant.parse(payload.paidAt), ZoneOffset.UTC),
                        amountPaid = payload.amountPaid.toBigDecimal(),
                    )
                )
            )
            true.success()
        }

    suspend fun emitAcquirerPaymentWebhookReceivedEventForBoleto(event: ItauBoletoPaymentWebhookReceivedEvent) =
        withSubscribersEnvironment {
            logger.info("Consuming ItauPixPaymentWebhookReceivedEvent", "event" to event)
            val payload = event.payload
            val itauPayment = itauPaymentService.getByBoletoId(payload.boletoId)
                .getOrNullIfNotFound()
            if (itauPayment == null) {
                logger.warn("Boleto payment not found", "boletoId" to event.payload.boletoId)
                return@withSubscribersEnvironment true.success()
            }
            itauPaymentService.update(
                itauPayment.copy(
                    paidResponse = Gson().toJson(event.payload)
                )
            ).get()

            kafkaProducerService.produce(
                AcquirerPaymentWebhookReceivedEvent(
                    AcquirerPaymentWebhookReceivedEventPayload(
                        invoicePaymentId = itauPayment.invoicePaymentId,
                        paidAt = LocalDate.parse(payload.paidAt).atTime(5, 0),
                        amountPaid = payload.amountPaid.toBigDecimal(),
                    )
                )
            )
            true.success()
        }
}
