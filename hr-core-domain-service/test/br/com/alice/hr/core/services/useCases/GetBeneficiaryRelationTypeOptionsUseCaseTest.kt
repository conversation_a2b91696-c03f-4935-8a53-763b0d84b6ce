package br.com.alice.hr.core.services.useCases

import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CompanySize
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import kotlin.test.assertEquals

class GetBeneficiaryRelationTypeOptionsUseCaseTest {
    private val companyProductPriceListingService = mockk<CompanyProductPriceListingService>()
    private val getBeneficiaryRelationTypeOptionsUseCase = GetBeneficiaryRelationTypeOptionsUseCase(companyProductPriceListingService)

    private val subcontract = TestModelFactory.buildCompanySubContract()
    private val mlaCompany = TestModelFactory.buildCompany(companySize = CompanySize.MLA)
    private val pCompany = TestModelFactory.buildCompany(companySize = CompanySize.SMALL)

    private val oldPortfolioPriceListing = TestModelFactory.buildCompanyProductPriceListing(ansNumber = "*********")
    private val priceListing = TestModelFactory.buildCompanyProductPriceListing()

    @Test
    fun `#run should return old portfolio relation types`() = runBlocking {
        val priceListings = listOf(oldPortfolioPriceListing)

        coEvery { companyProductPriceListingService.findCurrentBySubContractId(subcontract.id) } returns priceListings.success()

        val result = getBeneficiaryRelationTypeOptionsUseCase.run(mlaCompany, subcontract).get()

        assertEquals(
            listOf(
                ParentBeneficiaryRelationType.SPOUSE,
                ParentBeneficiaryRelationType.PARTNER,
                ParentBeneficiaryRelationType.STEPCHILD,
                ParentBeneficiaryRelationType.CHILD,
                ParentBeneficiaryRelationType.FOSTER_CHILD,
                ParentBeneficiaryRelationType.GRANDCHILD,
                ParentBeneficiaryRelationType.GREATGRANDCHILD
            ),
            result
        )
    }

    @Test
    fun `#run should return MLA relation types`() = runBlocking {
        val priceListings = listOf(priceListing)

        coEvery { companyProductPriceListingService.findCurrentBySubContractId(subcontract.id) } returns priceListings.success()

        val result = getBeneficiaryRelationTypeOptionsUseCase.run(mlaCompany, subcontract).get()

        assertEquals(
            listOf(
                ParentBeneficiaryRelationType.SPOUSE,
                ParentBeneficiaryRelationType.PARTNER,
                ParentBeneficiaryRelationType.STEPCHILD,
                ParentBeneficiaryRelationType.CHILD,
                ParentBeneficiaryRelationType.FOSTER_CHILD
            ),
            result
        )
    }

    @Test
    fun `#run should return P relation types under 30 days`() = runBlocking {
        val priceListings = listOf(priceListing)

        coEvery { companyProductPriceListingService.findCurrentBySubContractId(subcontract.id) } returns priceListings.success()

        val result = getBeneficiaryRelationTypeOptionsUseCase.run(pCompany, subcontract).get()

        assertEquals(
            listOf(
                ParentBeneficiaryRelationType.CHILD,
                ParentBeneficiaryRelationType.PARTNER,
                ParentBeneficiaryRelationType.SPOUSE,
                ParentBeneficiaryRelationType.STEPCHILD,
                ParentBeneficiaryRelationType.FOSTER_CHILD,
                ParentBeneficiaryRelationType.BROTHER_SISTER,
                ParentBeneficiaryRelationType.MOTHER_FATHER,
                ParentBeneficiaryRelationType.NIECE_NEPHEW,
                ParentBeneficiaryRelationType.GRANDCHILD,
                ParentBeneficiaryRelationType.SON_DAUGHTER_IN_LAW,
                ParentBeneficiaryRelationType.STEP_MOTHER_FATHER
            ),
            result
        )
    }

    @Test
    fun `#run should return P relation types above 30 days`() = runBlocking {
        val priceListings = listOf(priceListing)

        coEvery { companyProductPriceListingService.findCurrentBySubContractId(subcontract.id) } returns priceListings.success()

        val result = getBeneficiaryRelationTypeOptionsUseCase.run(
            pCompany,
            subcontract.copy(createdAt = LocalDateTime.now().minusDays(40))
        ).get()

        assertEquals(
            listOf(
                ParentBeneficiaryRelationType.CHILD,
                ParentBeneficiaryRelationType.PARTNER,
                ParentBeneficiaryRelationType.SPOUSE,
                ParentBeneficiaryRelationType.STEPCHILD,
                ParentBeneficiaryRelationType.FOSTER_CHILD
            ),
            result
        )
    }
}
