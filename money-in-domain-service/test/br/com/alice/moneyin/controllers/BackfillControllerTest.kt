package br.com.alice.moneyin.controllers

import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.extensions.money
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoiceBreakdown
import br.com.alice.data.layer.models.InvoiceItem
import br.com.alice.data.layer.models.InvoiceItemOperation
import br.com.alice.data.layer.models.InvoiceItemStatus
import br.com.alice.data.layer.models.InvoiceItemType
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.InvalidAmountException
import br.com.alice.moneyin.client.InvoiceAlreadyPaidException
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.communication.InvoiceMailer
import br.com.alice.moneyin.converters.InvoiceItemListConverter.toInvoiceBreakdown
import br.com.alice.moneyin.model.AssociateMemberInvoicesInMemberInvoiceGroupRequest
import br.com.alice.moneyin.model.BindCompanyAndSubcontractToMemberInvoiceGroupRequest
import br.com.alice.moneyin.model.ChangeMemberInvoiceGroupOwnerRequest
import br.com.alice.moneyin.model.CreateInvoicePixDetail
import br.com.alice.moneyin.model.IdsRequest
import br.com.alice.moneyin.model.InvoicePaymentSendEmailRequest
import br.com.alice.moneyin.model.MemberInvoiceRequest
import br.com.alice.moneyin.model.RecalculateInvoiceBreakdownRequest
import br.com.alice.moneyin.model.RequestReissueInvoicePaymentInBatch
import br.com.alice.moneyin.model.SendTemplateToEmail
import br.com.alice.moneyin.model.UpdateEmailRequest
import br.com.alice.moneyin.model.UpdateMemberInvoiceItemRequest
import br.com.alice.moneyin.services.MoneyInResourceSignTokenServiceImpl
import br.com.alice.moneyin.services.PaymentDetailService
import br.com.alice.person.client.MemberService
import io.ktor.client.statement.bodyAsText
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class BackfillControllerTest : ControllerTestHelper() {

    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val invoicesService: InvoicesService = mockk()
    private val memberInvoiceGroupService: MemberInvoiceGroupService = mockk()
    private val invoicePaymentService: InvoicePaymentService = mockk()
    private val invoiceMailer: InvoiceMailer = mockk()
    private val paymentDetailService: PaymentDetailService = mockk()
    private val memberService: MemberService = mockk()
    private val moneyInResourceSignTokenService: MoneyInResourceSignTokenServiceImpl = mockk()

    private val controller =
        BackfillController(
            billingAccountablePartyService,
            invoicesService,
            memberInvoiceGroupService,
            invoicePaymentService,
            invoiceMailer,
            paymentDetailService,
            memberService,
            moneyInResourceSignTokenService
        )
    private val authorizationHeader = mapOf("Authorization" to "Basic dGVzdDp0ZXN0")

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @Test
    fun `#should return 401 Unauthorized when Authorization header is not found`() {

        val request = UpdateEmailRequest(
            values = listOf(
                UpdateEmailRequest.Value(
                    national_id = "01",
                    email = "<EMAIL>",
                ),
                UpdateEmailRequest.Value(
                    national_id = "02",
                    email = "<EMAIL>",
                ),
                UpdateEmailRequest.Value(
                    national_id = "03",
                    email = "<EMAIL>",
                ),
                UpdateEmailRequest.Value(
                    national_id = "04",
                    email = "<EMAIL>",
                ),
            )
        )

        internalAuthentication {
            post(
                "/backfill/update_billing_accountable_party_id_by_national_id",
                body = request,
                headers = mapOf()
            ) { response ->
                ResponseAssert.assertThat(response).isUnauthorized()
            }
        }
    }

    @Nested
    inner class UpdateBillingAccountablePartyEmailByNationalId {

        @Test
        fun `#update from the billing accountable party using the national id as reference`() = runBlocking {
            val billingAccountableParty1 =
                TestModelFactory.buildBillingAccountableParty(nationalId = "01", email = "<EMAIL>")
            val billingAccountableParty2 =
                TestModelFactory.buildBillingAccountableParty(nationalId = "02", email = "<EMAIL>")
            val billingAccountableParty3 =
                TestModelFactory.buildBillingAccountableParty(nationalId = "03", email = "<EMAIL>")
            val updatedBillingAccountableParty1 = billingAccountableParty1.copy(email = "<EMAIL>")
            val updatedBillingAccountableParty2 = billingAccountableParty2.copy(email = "<EMAIL>")
            val updatedBillingAccountableParty3 = billingAccountableParty3.copy(email = "<EMAIL>")

            coEvery { billingAccountablePartyService.findByNationalIds(listOf("01", "02", "03", "04")) } returns listOf(
                billingAccountableParty1,
                billingAccountableParty2,
                billingAccountableParty3,
            )
            coEvery { billingAccountablePartyService.update(updatedBillingAccountableParty1) } returns updatedBillingAccountableParty1
            coEvery { billingAccountablePartyService.update(updatedBillingAccountableParty2) } returns updatedBillingAccountableParty2
            coEvery { billingAccountablePartyService.update(updatedBillingAccountableParty3) } returns Exception("Some error")

            val request = UpdateEmailRequest(
                values = listOf(
                    UpdateEmailRequest.Value(
                        national_id = "01",
                        email = "<EMAIL>",
                    ),
                    UpdateEmailRequest.Value(
                        national_id = "02",
                        email = "<EMAIL>",
                    ),
                    UpdateEmailRequest.Value(
                        national_id = "03",
                        email = "<EMAIL>",
                    ),
                    UpdateEmailRequest.Value(
                        national_id = "04",
                        email = "<EMAIL>",
                    ),
                )
            )

            internalAuthentication {
                post(
                    "/backfill/update_billing_accountable_party_id_by_national_id",
                    body = request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()
                    val content = gson.fromJson<List<Pair<String, String>>>(response.bodyAsText())
                    Assertions.assertThat(content[0]).isEqualTo("01" to "")
                    Assertions.assertThat(content[1]).isEqualTo("02" to "")
                    Assertions.assertThat(content[2]).isEqualTo("03" to "Some error")
                }
            }

            coVerifyOnce { billingAccountablePartyService.findByNationalIds(listOf("01", "02", "03", "04")) }
            coVerifyOnce { billingAccountablePartyService.update(updatedBillingAccountableParty1) }
            coVerifyOnce { billingAccountablePartyService.update(updatedBillingAccountableParty2) }
        }
    }

    @Nested
    inner class CancelMemberInvoice {

        @Test
        fun `#should cancel some member invoices by id`() = runBlocking {
            val memberInvoice1 = TestModelFactory.buildMemberInvoice()
            val memberInvoice2 = TestModelFactory.buildMemberInvoice()

            coEvery {
                invoicesService.cancel(
                    memberInvoice1.id,
                    CancellationReason.PAYMENT_PROCESSOR_CANCELED,
                    forceCancellation = true
                )
            } returns memberInvoice1.cancel(CancellationReason.PAYMENT_PROCESSOR_CANCELED)
            coEvery {
                invoicesService.cancel(
                    memberInvoice2.id,
                    CancellationReason.PAYMENT_PROCESSOR_CANCELED,
                    forceCancellation = true
                )
            } returns InvoiceAlreadyPaidException("Some error")

            val request = MemberInvoiceRequest(
                values = listOf(
                    memberInvoice1.id,
                    memberInvoice2.id,
                )
            )

            internalAuthentication {
                post("/backfill/cancel_member_invoice", body = request, headers = authorizationHeader) { response ->
                    ResponseAssert.assertThat(response).isOK()
                    val content = gson.fromJson<List<Pair<String, String>>>(response.bodyAsText())
                    Assertions.assertThat(content[0]).isEqualTo(memberInvoice1.id.toString() to "")
                    Assertions.assertThat(content[1]).isEqualTo(memberInvoice2.id.toString() to "Some error")
                }
            }

            coVerify(exactly = 2) {
                invoicesService.cancel(
                    any(),
                    CancellationReason.PAYMENT_PROCESSOR_CANCELED,
                    forceCancellation = true
                )
            }
        }

        @Test
        fun `#should cancel some member invoices groups by id`() = runBlocking {
            val memberInvoiceGroup1 = TestModelFactory.buildMemberInvoiceGroup()
            val memberInvoiceGroup2 = TestModelFactory.buildMemberInvoiceGroup()

            coEvery {
                memberInvoiceGroupService.cancelById(memberInvoiceGroup1.id)
            } returns memberInvoiceGroup1

            coEvery {
                memberInvoiceGroupService.cancelById(memberInvoiceGroup2.id)
            } returns InvalidAmountException("Some error")

            val request = MemberInvoiceRequest(
                values = listOf(
                    memberInvoiceGroup1.id,
                    memberInvoiceGroup2.id,
                )
            )

            internalAuthentication {
                post(
                    "/backfill/cancel_member_invoice_group",
                    body = request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()
                    val content = gson.fromJson<List<Pair<String, String>>>(response.bodyAsText())
                    Assertions.assertThat(content[0]).isEqualTo(memberInvoiceGroup1.id.toString() to "")
                    Assertions.assertThat(content[1]).isEqualTo(memberInvoiceGroup2.id.toString() to "Some error")
                }
            }

            coVerify(exactly = 2) { memberInvoiceGroupService.cancelById(any()) }
        }
    }

    @Nested
    inner class UpdateMemberInvoiceItems {
        @Test
        fun `#should update the member invoice by ids`() = runBlocking {
            val oldInvoiceItem =
                TestModelFactory.buildInvoiceItem(type = InvoiceItemType.SALES, notes = "ACRESCIMO AJT OPERACIONAL")
            val oldInvoiceItems = listOf(oldInvoiceItem)

            val newInvoiceItem = oldInvoiceItem.copy(type = InvoiceItemType.PLAN_READJUSTMENT)
            val newInvoiceItems = listOf(newInvoiceItem)


            val memberInvoice1 = TestModelFactory.buildMemberInvoice(
                invoiceItems = listOf(oldInvoiceItem),
                invoiceBreakdown = oldInvoiceItems.toInvoiceBreakdown().get()
            )

            val memberInvoiceUpdated = memberInvoice1.copy(
                invoiceItems = newInvoiceItems,
                invoiceBreakdown = oldInvoiceItems.toInvoiceBreakdown().get()
            )

            coEvery { invoicesService.findInvoicesByIds(listOf(memberInvoice1.id)) } returns listOf(memberInvoice1)
            coEvery { invoicesService.update(match { it.id == memberInvoice1.id }) } returns memberInvoiceUpdated

            val request = UpdateMemberInvoiceItemRequest(
                ids = listOf(
                    memberInvoice1.id,
                ),
                oldInvoiceType = oldInvoiceItem.type,
                oldNotes = oldInvoiceItem.notes!!,
                newInvoiceType = newInvoiceItem.type,
            )

            internalAuthentication {
                post(
                    "/backfill/update_member_invoice_items",
                    body = request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }
        }

        @Test
        fun `#should update the member invoice by ids changing notes`() = runBlocking {
            val oldInvoiceItem =
                TestModelFactory.buildInvoiceItem(type = InvoiceItemType.OTHERS, notes = "DESCONTO MGM")
            val oldInvoiceItems = listOf(oldInvoiceItem)

            val newInvoiceItem =
                oldInvoiceItem.copy(type = InvoiceItemType.PROMO_CODE_RESULT, notes = "Desconto Member get Member")
            val newInvoiceItems = listOf(newInvoiceItem)


            val memberInvoice1 = TestModelFactory.buildMemberInvoice(
                invoiceItems = listOf(oldInvoiceItem),
                invoiceBreakdown = oldInvoiceItems.toInvoiceBreakdown().get()
            )

            val memberInvoiceUpdated = memberInvoice1.copy(
                invoiceItems = newInvoiceItems,
                invoiceBreakdown = oldInvoiceItems.toInvoiceBreakdown().get()
            )

            coEvery { invoicesService.findInvoicesByIds(listOf(memberInvoice1.id)) } returns listOf(memberInvoice1)
            coEvery { invoicesService.update(match { it.id == memberInvoice1.id }) } returns memberInvoiceUpdated

            val request = UpdateMemberInvoiceItemRequest(
                ids = listOf(
                    memberInvoice1.id,
                ),
                oldInvoiceType = oldInvoiceItem.type,
                oldNotes = oldInvoiceItem.notes!!,
                newInvoiceType = newInvoiceItem.type,
                newNotes = newInvoiceItem.notes,
            )

            internalAuthentication {
                post(
                    "/backfill/update_member_invoice_items",
                    body = request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }
        }
    }

    @Nested
    inner class ResendInvoiceEmail {
        @Test
        fun `#should update the member invoice by ids`() = runBlocking {
            val request = InvoicePaymentSendEmailRequest(
                invoicePaymentId = RangeUUID.generate(),
                billingAccountablePartyId = null,
            )
            val invoicePayment = TestModelFactory.buildInvoicePayment(reason = PaymentReason.REGULAR_PAYMENT)

            coEvery { invoicePaymentService.get(request.invoicePaymentId, true) } returns invoicePayment
            coEvery { invoiceMailer.send(invoicePayment, invoicePayment.reason!!, null) } returns mockk()

            internalAuthentication {
                post(
                    "/backfill/invoice_payment/resend_invoice_email",
                    request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }
        }
    }

    @Nested
    inner class BindCompanySubcontractToMemberInvoiceGroup {
        @Test
        fun `#should update the member invoice by ids`() = runBlocking {
            val memberInvoiceGroupId = RangeUUID.generate()
            val companyId = RangeUUID.generate()
            val subcontractId = RangeUUID.generate()

            val request = BindCompanyAndSubcontractToMemberInvoiceGroupRequest(
                list = listOf(
                    BindCompanyAndSubcontractToMemberInvoiceGroupRequest.Payload(
                        memberInvoiceGroupId = memberInvoiceGroupId,
                        companyId = companyId,
                        subcontractId = subcontractId
                    )
                )
            )

            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(id = memberInvoiceGroupId)
            val memberInvoiceGroupUpdated = memberInvoiceGroup.copy(
                companyId = companyId,
                companySubcontractId = subcontractId,
            )

            coEvery { memberInvoiceGroupService.getByIds(listOf(memberInvoiceGroupId)) } returns listOf(
                memberInvoiceGroup
            )
            coEvery {
                memberInvoiceGroupService.update(match {
                    it.companyId == companyId && it.companySubcontractId == subcontractId
                })
            } returns memberInvoiceGroupUpdated

            internalAuthentication {
                post(
                    "/backfill/bind_company_subcontract_to_member_invoice_group",
                    request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }
        }
    }

    @Test
    fun `#recalculateInvoiceBreakdown - should recalculate invoice breakdown successfully`() = runBlocking {

        val breakdown = InvoiceBreakdown(
            totalAmount = 0.money,
            discount = 0.money,
            addition = 0.money,
            productPrice = 0.money,
            promoCode = 0.money,
            proRation = 0.money,
            copay = 0.money,
            productChange = 0.money,
            sales = 0.money,
            promoCodeResult = 0.money,
            salesResult = 0.money,
            others = 0.money,
            readjustment = 0.money,
            operationalAdjustment = 0.money,
            invoiceItems = listOf(
                InvoiceItem(
                    id = UUID.fromString("9d06463a-0f1e-4fa8-9f97-************"),
                    type = InvoiceItemType.OPERATIONAL_ADJUSTMENT,
                    notes = "DESCONTO AJT OPERACIONAL",
                    status = InvoiceItemStatus.ACTIVE,
                    version = 0,
                    operation = InvoiceItemOperation.DISCOUNT,
                    personId = PersonId(),
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now(),
                    absoluteValue = (41.88).money,
                    referenceDate = LocalDate.now()
                ),
                InvoiceItem(
                    id = UUID.fromString("9d06463a-0f1e-4fa8-9f97-************"),
                    type = InvoiceItemType.OPERATIONAL_ADJUSTMENT,
                    notes = "DESCONTO AJT OPERACIONAL",
                    status = InvoiceItemStatus.ACTIVE,
                    version = 0,
                    operation = InvoiceItemOperation.DISCOUNT,
                    personId = PersonId(),
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now(),
                    absoluteValue = (41.88).money,
                    referenceDate = LocalDate.now()
                ),
                InvoiceItem(
                    id = UUID.fromString("9d06463a-0f1e-4fa8-9f97-************"),
                    type = InvoiceItemType.PRODUCT_CHANGE,
                    notes = "DESCONTO AJT OPERACIONAL",
                    status = InvoiceItemStatus.ACTIVE,
                    version = 0,
                    operation = InvoiceItemOperation.CHARGE,
                    personId = PersonId(),
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now(),
                    absoluteValue = (3000).money,
                    referenceDate = LocalDate.now()
                )
            ),
        )

        val memberInvoices = listOf(TestModelFactory.buildMemberInvoice(invoiceBreakdown = breakdown))
        val ids = memberInvoices.map { it.id }


        val request = RecalculateInvoiceBreakdownRequest(
            ids = ids,
            type = listOf(InvoiceItemType.OPERATIONAL_ADJUSTMENT)
        )

        coEvery {
            invoicesService.findInvoicesByIds(ids)
        } returns memberInvoices

        coEvery {
            invoicesService.update(

                match {
                    it.totalAmount == breakdown.invoiceItems!!.sumOf { invoiceItem ->
                        if (invoiceItem.operation == InvoiceItemOperation.DISCOUNT)
                            invoiceItem.value.negate()
                        else invoiceItem.value
                    }
                }


            )
        } returns memberInvoices.first()

        internalAuthentication {
            post(
                "/backfill/member_invoice/recalculate_invoice_breakdown",
                request,
                headers = authorizationHeader
            ) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyOnce { invoicesService.findInvoicesByIds(ids) }
        coVerifyOnce { invoicesService.update(any()) }
    }

    @Test
    fun `#expireMoneyInInvoicePaymentLink success`() = runBlocking {
        val resourceSignTokenId = RangeUUID.generate()

        val request = IdsRequest(ids = listOf(resourceSignTokenId))

        coEvery {
            moneyInResourceSignTokenService.softDeleteSignToken(resourceSignTokenId)
        } returns true

        internalAuthentication {
            post(
                "/backfill/soft_delete_money_in_sign_token",
                request,
                headers = authorizationHeader
            ) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyOnce { moneyInResourceSignTokenService.softDeleteSignToken(any()) }
    }

    @Nested
    inner class ChangeMemberInvoiceGroupOwner {
        @Test
        fun `#should change the MIG owner`() = runBlocking {
            val migId = RangeUUID.generate()
            val companyId = RangeUUID.generate()
            val subcontractId = RangeUUID.generate()


            val mig = TestModelFactory.buildMemberInvoiceGroup(id = migId)
            val migUpdated = mig.copy(companyId = companyId, companySubcontractId = subcontractId)

            val request = ChangeMemberInvoiceGroupOwnerRequest(
                items = mapOf(
                    mig.id to ChangeMemberInvoiceGroupOwnerRequest.Item(
                        companyId, subcontractId
                    )
                )
            )

            coEvery {
                memberInvoiceGroupService.getByIds(listOf(migId))
            } returns listOf(mig)

            coEvery { memberInvoiceGroupService.update(migUpdated) } returns migUpdated

            internalAuthentication {
                post(
                    "/backfill/member_invoice_group/change_owner",
                    request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }
        }
    }

    @Nested
    inner class MemberInvoiceGroupUpdate {
        @Test
        fun `#backfillTriggerMemberInvoiceGroupUpdate should trigger update`() = runBlocking {
            val mig = TestModelFactory.buildMemberInvoiceGroup()
            val miId = RangeUUID.generate()
            val migUpdated = mig.copy(memberInvoiceIds = listOf(miId))
            val request = AssociateMemberInvoicesInMemberInvoiceGroupRequest(mig.id, listOf(miId))

            coEvery {
                memberInvoiceGroupService.get(mig.id)
            } returns mig

            coEvery {
                memberInvoiceGroupService.update(migUpdated)
            } returns migUpdated

            internalAuthentication {
                post(
                    "/backfill/triger_member_invoice_group_update",
                    request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }
        }
    }

    @Nested
    inner class CreateInvoicePaymentDetail {
        @Test
        fun `#createInvoicePixDetails should create detail succefully`() = runBlocking {
            val model = TestModelFactory.buildPixPaymentDetail(paymentId = UUID.randomUUID())

            coEvery {
                paymentDetailService.createPaymentDetail(any())
            } returns model

            val request = CreateInvoicePixDetail(
                paymentId = model.paymentId,
                paymentUrl = model.paymentUrl,
                paymentCode = model.paymentCode,
                dueDate = model.dueDate,
            )

            internalAuthentication {
                post(
                    "/backfill/invoice_payment/create_pix_invoice_details",
                    request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }
        }
    }

    @Nested
    inner class ReissueInvoicePaymentInBatch {
        @Test
        fun `#should create cancel and reissue successfully`() = runBlocking {
            val billing = TestModelFactory.buildBillingAccountableParty()
            val mig = TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = billing.id)
            val invoice = TestModelFactory.buildMemberInvoice(memberInvoiceGroupId = mig.id)
            val payment =
                TestModelFactory.buildInvoicePayment(
                    invoiceGroupId = mig.id,
                    billingAccountablePartyId = billing.id,
                    reason = PaymentReason.REGULAR_PAYMENT,
                )

            val dueDate = LocalDate.now()

            val newPayment =
                TestModelFactory.buildInvoicePayment(invoiceGroupId = mig.id, billingAccountablePartyId = billing.id)

            coEvery {
                invoicePaymentService.cancel(payment.id, CancellationReason.OVERDUE)
            } returns payment

            coEvery { invoicesService.listByMemberInvoiceGroupId(payment.invoiceGroupId!!) } returns listOf(
                invoice
            )

            coEvery { billingAccountablePartyService.get(payment.billingAccountablePartyId!!) } returns billing

            coEvery { memberInvoiceGroupService.get(payment.invoiceGroupId!!) } returns mig

            coEvery {
                invoicePaymentService.createInvoicePaymentForMemberInvoices(
                    InvoicePaymentService.CreateInvoicePaymentForMemberInvoicesPayload(
                        PaymentMethod.BOLETO,
                        listOf(invoice),
                        dueDate = dueDate.atEndOfTheDay(),
                        billing,
                        payment.reason!!,
                        mig.copy(type = null),
                        null,
                        InvoicePaymentOrigin.ISSUED_BY_BACKFILL,
                        paymentUrl = null,
                        externalId = null,
                    )
                )
            } returns newPayment

            val request = RequestReissueInvoicePaymentInBatch(
                paymentIds = listOf(payment.id),
                dueDate = dueDate,
                sendEmail = false,
            )

            internalAuthentication {
                post(
                    "/backfill/invoice_payment/reissue_in_batch",
                    request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }
        }

        @Test
        fun `#should create cancel and reissue successfully from the mig even when the member invoices does not belong to the mig`() =
            runBlocking {
                val billing = TestModelFactory.buildBillingAccountableParty()
                val mig = TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = billing.id)
                val invoice = TestModelFactory.buildMemberInvoice(memberInvoiceGroupId = mig.id)
                val payment =
                    TestModelFactory.buildInvoicePayment(
                        invoiceGroupId = mig.id,
                        billingAccountablePartyId = billing.id,
                        reason = PaymentReason.REGULAR_PAYMENT,
                    )

                val dueDate = LocalDate.now()

                val newPayment =
                    TestModelFactory.buildInvoicePayment(
                        invoiceGroupId = mig.id,
                        billingAccountablePartyId = billing.id
                    )

                coEvery {
                    invoicePaymentService.cancel(payment.id, CancellationReason.OVERDUE)
                } returns payment

                coEvery {
                    invoicesService.listByMemberInvoiceGroupId(payment.invoiceGroupId!!)
                } returns emptyList()

                coEvery { invoicesService.findInvoicesByIds(payment.memberInvoiceIds) } returns listOf(
                    invoice
                )

                coEvery {
                    billingAccountablePartyService.get(payment.billingAccountablePartyId!!)
                } returns billing

                coEvery { memberInvoiceGroupService.get(payment.invoiceGroupId!!) } returns mig

                coEvery {
                    invoicePaymentService.createInvoicePaymentForMemberInvoices(
                        InvoicePaymentService.CreateInvoicePaymentForMemberInvoicesPayload(
                            PaymentMethod.BOLETO,
                            listOf(invoice),
                            dueDate = dueDate.atEndOfTheDay(),
                            billing,
                            payment.reason!!,
                            mig.copy(type = null),
                            null,
                            InvoicePaymentOrigin.ISSUED_BY_BACKFILL,
                            paymentUrl = null,
                            externalId = null,
                        )
                    )
                } returns newPayment

                val request = RequestReissueInvoicePaymentInBatch(
                    paymentIds = listOf(payment.id),
                    dueDate = dueDate,
                    sendEmail = false,
                )

                internalAuthentication {
                    post(
                        "/backfill/invoice_payment/reissue_in_batch",
                        request,
                        headers = authorizationHeader
                    ) { response ->
                        ResponseAssert.assertThat(response).isOK()
                    }
                }
            }

        @Test
        fun `#should create cancel and reissue successfully even when the invoice payment does not belong to some member invoice group`() =
            runBlocking {
                val billing = TestModelFactory.buildBillingAccountableParty()
                val member = TestModelFactory.buildMember()
                val invoice = TestModelFactory.buildMemberInvoice(memberInvoiceGroupId = null, memberId = member.id)
                val payment =
                    TestModelFactory.buildInvoicePayment(
                        invoiceGroupId = null,
                        billingAccountablePartyId = billing.id,
                        reason = PaymentReason.REGULAR_PAYMENT,
                    )

                val dueDate = LocalDate.now()

                val newPayment =
                    TestModelFactory.buildInvoicePayment(billingAccountablePartyId = billing.id)

                coEvery {
                    invoicePaymentService.cancel(payment.id, CancellationReason.OVERDUE)
                } returns payment

                coEvery { invoicesService.findInvoicesByIds(payment.memberInvoiceIds) } returns listOf(
                    invoice
                )

                coEvery {
                    billingAccountablePartyService.get(payment.billingAccountablePartyId!!)
                } returns billing

                coEvery { memberService.get(invoice.memberId) } returns member

                coEvery {
                    invoicePaymentService.createInvoicePayment(
                        match {
                            it.method == payment.method &&
                                    it.origin == InvoicePaymentOrigin.ISSUED_BY_BACKFILL &&
                                    it.amount == payment.amount &&
                                    it.memberInvoiceIds == payment.memberInvoiceIds
                        },
                        member
                    )
                } returns newPayment

                val request = RequestReissueInvoicePaymentInBatch(
                    paymentIds = listOf(payment.id),
                    dueDate = dueDate,
                    sendEmail = false,
                )

                internalAuthentication {
                    post(
                        "/backfill/invoice_payment/reissue_in_batch",
                        request,
                        headers = authorizationHeader
                    ) { response ->
                        ResponseAssert.assertThat(response).isOK()
                    }
                }
            }
    }

    @Nested
    inner class TemplateEmail {
        @Test
        fun `#should send a pinpoint email to user successfully`() {

            val request = SendTemplateToEmail(
                email = "<EMAIL>",
                template = "Template_Boleto_Primeira_Mensalidade",
                params = mapOf(
                    "name" to "Teste",
                    "dueDate" to "2023-08-11",
                    "amount" to "R$ 100,00",
                    "paymentUrl" to "https://faturas.iugu.com/28e584b2-216a-43ef-bd9b-ea80d7dace6e-a825",
                )
            )

            coEvery {
                invoiceMailer.sendTemplateToEmail(
                    request.email,
                    request.template,
                    request.params,
                    any()
                )
            } returns mockk()

            internalAuthentication {
                post(
                    "/backfill/email/send_template_to_email",
                    body = request,
                    headers = authorizationHeader
                ) { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }
        }
    }


}
