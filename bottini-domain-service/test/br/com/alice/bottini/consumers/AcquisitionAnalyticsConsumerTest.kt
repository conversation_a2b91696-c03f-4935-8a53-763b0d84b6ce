package br.com.alice.bottini.consumers

import br.com.alice.bottini.analytics.Segment
import br.com.alice.bottini.client.LeadQualificationService
import br.com.alice.bottini.client.LeadService
import br.com.alice.bottini.events.LeadCreatedEvent
import br.com.alice.bottini.events.SimulationFinishedEvent
import br.com.alice.bottini.models.QualifiedResult
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.HealthProductSimulationType
import br.com.alice.data.layer.models.LeadSource
import br.com.alice.data.layer.models.TrackingOrigin
import br.com.alice.membership.model.events.SignedContractEvent
import br.com.alice.person.br.com.alice.person.model.events.FirstAccessEvent
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.person.model.events.MemberActivatedEvent
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import kotlin.test.BeforeTest
import kotlin.test.Test

class AcquisitionAnalyticsConsumerTest : ConsumerTest() {

    private val leadQualifierService: LeadQualificationService = mockk()
    private val personService: PersonService = mockk()
    private val leadService: LeadService = mockk()
    private val memberService: MemberService = mockk()
    private val consumer = AcquisitionAnalyticsConsumer(leadQualifierService, personService, leadService, memberService)

    @BeforeTest
    fun beforeTests() {
        mockkObject(Segment)
    }

    @Test
    fun `#leadCreated should send lead created event to segment`() = runBlocking<Unit> {
        val trackingInfo = TestModelFactory.buildTrackingInfo().copy(
            utmMedium = "medium",
            utmSource = "source",
            utmCampaign = "campaign",
            utmContent = "content",
            utmCampaignId = "campaignId",
            utmAdSetId = "adSetId",
            utmAdId = "adId",
            utmTerm = "term",
            origin = TrackingOrigin.PLAN_LIST,
            anonymousId = "anonymousId",
        )
        val lead = TestModelFactory.buildLead(trackingInfo = trackingInfo, source = LeadSource.SIMULATION)
        val qualifiedLead = QualifiedResult(isTarget = true, isSuperTarget = true, isIMP = true)
        coEvery { leadQualifierService.qualify(lead) } returns qualifiedLead.success()
        every {
            Segment.analytics.track("lead_created",
                buildJsonObject {
                    put("description", "Lead criado no back-end")
                    put("user_id", lead.id.toString())
                    put("lead_id", lead.id.toString())
                    put("banco_lead_created_at", lead.createdAt.toString())
                    put("is_target", qualifiedLead.isTarget)
                    put("is_super_target", qualifiedLead.isSuperTarget)
                    put("is_imp", qualifiedLead.isIMP)
                    put("is_anti_imp", qualifiedLead.isAntiIMP)
                    put("lead_utm_medium", lead.trackingInfo?.utmMedium)
                    put("lead_utm_source", lead.trackingInfo?.utmSource)
                    put("lead_utm_campaign", lead.trackingInfo?.utmCampaign)
                    put("lead_utm_content", lead.trackingInfo?.utmContent)
                    put("lead_utm_campaign_id", lead.trackingInfo?.utmCampaignId)
                    put("lead_utm_ad_set_id", lead.trackingInfo?.utmAdSetId)
                    put("lead_utm_ad_id", lead.trackingInfo?.utmAdId)
                    put("lead_utm_term", lead.trackingInfo?.utmTerm)
                    put("lead_origin", lead.trackingInfo?.origin.toString())
                    put("brand", Brand.ALICE.name)
                    put("anonymous_id", lead.trackingInfo?.anonymousId)
                }
            )
        }.returns(Unit)

        val response = consumer.leadCreated(LeadCreatedEvent(lead))

        ResultAssert.assertThat(response).isSuccess()
    }

    @Test
    fun `#simulationFinished should send simulationFinished event to segment`() = runBlocking<Unit> {
        val trackingInfo = TestModelFactory.buildTrackingInfo().copy(
            utmMedium = "medium",
            utmSource = "source",
            utmCampaign = "campaign",
            utmContent = "content",
            utmCampaignId = "campaignId",
            utmAdSetId = "adSetId",
            utmAdId = "adId",
            utmTerm = "term",
            origin = TrackingOrigin.PLAN_LIST,
            anonymousId = "anonymousId",
        )
        val lead = TestModelFactory.buildLead(trackingInfo = trackingInfo, source = LeadSource.SIMULATION)
        val simulation = TestModelFactory.buildHealthProductSimulation(leadId = lead.id, type = HealthProductSimulationType.MICRO_COMPANY)
        every {
            Segment.analytics.track("simulation_finished",
                buildJsonObject {
                    put("description", "Simulação finalizada no back-end")
                    put("user_id", lead.id.toString())
                    put("lead_id", lead.id.toString())
                    put("simulation_id", simulation.id.toString())
                    put("banco_simulação_created_at", simulation.createdAt.toString())
                    put("type", simulation.type.toString())
                    put("anonymous_id", lead.trackingInfo?.anonymousId)
                }
            )
        }.returns(Unit)

        val response = consumer.simulationFinished(SimulationFinishedEvent(lead, simulation))

        ResultAssert.assertThat(response).isSuccess()
    }

    @Test
    fun `#memberActivated should send member activated event to segment`() = runBlocking<Unit> {
        val leadId = RangeUUID.generate()
        val memberId = RangeUUID.generate()
        val person = TestModelFactory.buildPerson(leadId = leadId)
        val member = TestModelFactory.buildMember(personId = person.id, id = memberId)
        coEvery { personService.get(member.personId) } returns person.success()
        coEvery { memberService.getCurrent(person.id) } returns member.success()
        every {
            Segment.analytics.track("member_activated",
                buildJsonObject {
                    put("description", "Member ativado no back-end")
                    put("user_id", leadId.toString())
                    put("lead_id", leadId.toString())
                    put("person_id", person.id.toString())
                    put("member_id", member.id.toString())
                    put("brand", member.brand?.name)
                }
            )
        }.returns(Unit)

        val response = consumer.memberActivated(MemberActivatedEvent(member))

        ResultAssert.assertThat(response).isSuccess()
    }

    @Test
    fun `#memberActivated should not send member activated event to segment if member has no leadId`() =
        runBlocking<Unit> {
            val leadId = RangeUUID.generate()
            val person = TestModelFactory.buildPerson()
            val member = TestModelFactory.buildMember(personId = person.id)
            coEvery { personService.get(member.personId) } returns person.success()
            every {
                Segment.analytics.track("member_activated",
                    buildJsonObject {
                        put("description", "Member ativado no back-end")
                        put("user_id", leadId.toString())
                        put("lead_id", leadId.toString())
                        put("person_id", person.id.toString())
                        put("member_id", member.id.toString())
                    }
                )
            }.returns(Unit)

            val response = consumer.memberActivated(MemberActivatedEvent(member))

            ResultAssert.assertThat(response).isSuccess()
            coVerify(exactly = 0) { Segment.analytics.identify(any()) }
            coVerify(exactly = 0) { Segment.analytics.track(any(), any()) }
            coVerify(exactly = 0) { Segment.analytics.flush() }
            coVerify(exactly = 0) { Segment.analytics.reset() }
            coVerify(exactly = 0) { memberService.getCurrent(any()) wasNot called }
        }

    @Test
    fun `#firstAccess should send first access event to segment with no lead id`() = runBlocking<Unit> {
        val person = TestModelFactory.buildPerson()
        val personLogin = TestModelFactory.buildPersonLogin(personId = person.id)

        coEvery { personService.get(person.id) } returns person.success()

        every {
            Segment.analytics.track("first_access",
                buildJsonObject {
                    put("description", "Primeiro login no app")
                    put("person_id", person.id.toString())
                    put("user_id", person.leadId.toString())

                }
            )
        }.returns(Unit)

        val response = consumer.firstAccess(FirstAccessEvent(personLogin))

        ResultAssert.assertThat(response).isSuccess()
        coVerify(exactly = 0) { leadService.get(any()) wasNot called }
    }

    @Test
    fun `#firstAccess should send first access event to segment with lead id`() = runBlocking<Unit> {
        val lead = TestModelFactory.buildLead(source = LeadSource.APP)
        val person = TestModelFactory.buildPerson(leadId = lead.id)
        val personLogin = TestModelFactory.buildPersonLogin(personId = person.id)
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { leadService.get(lead.id) } returns lead.success()

        every {
            Segment.analytics.track("first_access",
                buildJsonObject {
                    put("description", "Primeiro login no app")
                    put("person_id", person.id.toString())
                    put("lead_id", person.leadId.toString())
                    put("user_id", person.leadId.toString())
                    put("brand", Brand.ALICE.name)
                }
            )
        }.returns(Unit)

        val response = consumer.firstAccess(FirstAccessEvent(personLogin))

        ResultAssert.assertThat(response).isSuccess()
    }
    @Test
    fun `#contractSigned should send contractSigned event to segment`() = runBlocking<Unit> {
        val contract = TestModelFactory.buildOnboardingContract()
        val person = TestModelFactory.buildPerson()

        coEvery {
            personService.get(contract.personId)
        } returns person.success()
        every {
            Segment.analytics.track("signed_contract",
                buildJsonObject {
                    put("description", "Contrato foi assinado")
                    put("contract_id", contract.id.toString())
                    put("person_id", contract.personId.toString())
                    put("lead_id", person.leadId.toString())
                    put("user_id", person.leadId.toString())
                }
            )
        }.returns(Unit)

        val response = consumer.contractSigned(SignedContractEvent(contract))

        ResultAssert.assertThat(response).isSuccess()
    }
}
