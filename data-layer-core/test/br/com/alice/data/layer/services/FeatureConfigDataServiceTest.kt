package br.com.alice.data.layer.services

import br.com.alice.common.convertTo
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureConfigModel
import br.com.alice.data.layer.models.FeatureNamespace
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class FeatureConfigDataServiceTest : DataServiceTestHelper() {

    private val featureConfigDataService = FeatureConfigModelDataServiceClient(httpInvoker)

    @Test
    fun `#add and #get`() = runBlocking {

        val featureConfig = TestModelFactory.buildFeatureConfig().convertTo(FeatureConfigModel::class)

        val resultAdd = featureConfigDataService.add(featureConfig)
        assertThat(resultAdd).isSuccessWithDataIgnoringGivenFields(featureConfig, "createdAt", "updatedAt")

        val resultGet = featureConfigDataService.get(featureConfig.id)
        assertThat(resultGet).isSuccessWithDataIgnoringGivenFields(featureConfig, "createdAt", "updatedAt")

        val resultFind = featureConfigDataService.find { where { this.namespace.eq(FeatureNamespace.EHR) } }
        assertThat(resultFind).isSuccessWithDataIgnoringGivenFields(listOf(featureConfig), "createdAt", "updatedAt")
    }
}
