package br.com.alice.itau.converters

import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.itau.ServiceConfig
import br.com.alice.itau.clients.models.ItauBoletoCreateResponse
import br.com.alice.itau.clients.models.ItauBoletoDataBoletoCreateResponse
import br.com.alice.itau.clients.models.ItauBoletoDataBoletoGetResponse
import br.com.alice.itau.clients.models.ItauBoletoDataBoletoIndividualGetResponse
import br.com.alice.itau.clients.models.ItauBoletoDataCreateResponse
import br.com.alice.itau.clients.models.ItauBoletoDataGetResponse
import br.com.alice.itau.clients.models.ItauBoletoDataQrCodeResponse
import br.com.alice.itau.clients.models.ItauBoletoGetResponse
import br.com.alice.itau.clients.models.ItauBoletoIndividualDataBoletoCreateResponse
import br.com.alice.itau.clients.models.ItauBoletoStatus
import br.com.alice.itau.converter.shouldIgnoreInterestAndFineIfFirstPayment
import br.com.alice.itau.converter.toAcquirerCreatePaymentResponse
import br.com.alice.itau.converter.toAcquirerGetPaymentResponse
import br.com.alice.itau.converter.toItauBoletoCreateRequest
import br.com.alice.itau.converter.toOurNumberString
import br.com.alice.moneyin.models.PaymentRequestInput
import br.com.alice.moneyin.models.PaymentStatus
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.LocalDate

class ItauBoletoConverterTest {

    private val itauPayment = TestModelFactory.buildItauPayment()
    private val payer = PaymentRequestInput.Payer(
        id = RangeUUID.generate(),
        nationalId = "**************",
        name = "ACME Ltda",
        address = null,
        email = "<EMAIL>",
        type = BillingAccountablePartyType.LEGAL_PERSON
    )
    private val paymentRequestInput = PaymentRequestInput(
        orderId = RangeUUID.generate(),
        referenceDates = emptyList(),
        dueDate = LocalDate.of(2025, 6, 10),
        payer = payer,
        people = emptyList(),
        isFirstPayment = false,
        method = PaymentMethod.BOLETO,
        installment = 0,
        totalInstallments = 1,
        totalAmount = BigDecimal("200.00")
    )

    @Test
    fun `#toAcquirerCreatePaymentResponse should convert ItauBoletoCreateResponse to AcquirerCreatePaymentResponse`() {
        val response = ItauBoletoCreateResponse(
            data = ItauBoletoDataCreateResponse(
                boletoData = ItauBoletoDataBoletoCreateResponse(
                    individualBoleto = listOf(
                        ItauBoletoIndividualDataBoletoCreateResponse(
                            id = "boletoId123",
                            digitableLine = "3419176********45678901234567890123456789012",
                            barcodeData = "34198765432109876543210987654321098765432109"
                        )
                    )
                )
            )
        )

        val (convertedResponse, externalIds) = response.toAcquirerCreatePaymentResponse(itauPayment)

        assertThat(convertedResponse.status).isEqualTo(PaymentStatus.PENDING)
        assertThat(convertedResponse.bankSlip?.barcodeData).isEqualTo("34198765432109876543210987654321098765432109")
        assertThat(convertedResponse.bankSlip?.digitableLine).isEqualTo("3419176********45678901234567890123456789012")
        assertThat(externalIds.pixId).isNull()
        assertThat(externalIds.boletoId).isEqualTo("boletoId123")
    }

    @Test
    fun `#toItauBoletoCreateRequest should convert PaymentRequestInput to ItauBoletoCreateRequest`() {
        val request = paymentRequestInput.toItauBoletoCreateRequest(itauPayment)

        assertThat(request.data.beneficiary.aliceBankAccount).isEqualTo(ServiceConfig.itauBoletoConfig.aliceAccountKey)
        assertThat(request.data.boletoData.payer.person.name).isEqualTo("ACME Ltda")
        assertThat(request.data.boletoData.payer.person.personType.cnpj).isEqualTo("**************")
        assertThat(request.data.boletoData.payer.person.personType.cpf).isNull()
        assertThat(request.data.boletoData.payer.person.personType.personCode).isEqualTo("J") // JURIDICA
        assertThat(request.data.boletoData.individualBoleto.first().dueDate).isEqualTo("2025-06-10")
        assertThat(request.data.boletoData.individualBoleto.first().titleValue).isEqualTo("*****************")
    }

    @Test
    fun `#toAcquirerGetPaymentResponse should convert ItauBoletoGetResponse to AcquirerGetPaymentResponse`() {
        val response = ItauBoletoGetResponse(
            data = listOf(
                ItauBoletoDataGetResponse(
                    id = "123",
                    boletoData = ItauBoletoDataBoletoGetResponse(
                        individualBoleto = listOf(
                            ItauBoletoDataBoletoIndividualGetResponse(
                                digitableLine = "3419176********45678901234567890123456789012",
                                barcodeData = "34198765432109876543210987654321098765432109",
                                status = ItauBoletoStatus.PENDING,
                                dueDate = "2023-10-10",
                                titleValue = "100.10"
                            )
                        ),
                        pixData = null
                    )
                )
            )
        )

        val convertedResponse = response.toAcquirerGetPaymentResponse()

        assertThat(convertedResponse.id).isEqualTo("123")
        assertThat(convertedResponse.status).isEqualTo(PaymentStatus.PENDING)
        assertThat(convertedResponse.bankSlip?.digitableLine).isEqualTo("3419176********45678901234567890123456789012")
        assertThat(convertedResponse.bankSlip?.barcodeData).isEqualTo("34198765432109876543210987654321098765432109")
        assertThat(convertedResponse.pix).isNull()
        assertThat(convertedResponse.totalCents).isEqualTo(10010)
        assertThat(convertedResponse.dueDate).isEqualTo(LocalDate.parse("2023-10-10"))
    }

    @Test
    fun `#toAcquirerGetPaymentResponse should convert ItauBoletoGetResponse to AcquirerGetPaymentResponse with pix`() {
        val response = ItauBoletoGetResponse(
            data = listOf(
                ItauBoletoDataGetResponse(
                    id = "123",
                    boletoData = ItauBoletoDataBoletoGetResponse(
                        individualBoleto = listOf(
                            ItauBoletoDataBoletoIndividualGetResponse(
                                digitableLine = "3419176********45678901234567890123456789012",
                                barcodeData = "34198765432109876543210987654321098765432109",
                                status = ItauBoletoStatus.PENDING,
                                dueDate = "2023-10-10",
                                titleValue = "100.10"
                            )
                        ),
                        pixData = ItauBoletoDataQrCodeResponse(
                            pixCopyAndPaste = "**********"
                        )
                    )
                )
            )
        )

        val convertedResponse = response.toAcquirerGetPaymentResponse()

        assertThat(convertedResponse.id).isEqualTo("123")
        assertThat(convertedResponse.status).isEqualTo(PaymentStatus.PENDING)
        assertThat(convertedResponse.bankSlip?.digitableLine).isEqualTo("3419176********45678901234567890123456789012")
        assertThat(convertedResponse.bankSlip?.barcodeData).isEqualTo("34198765432109876543210987654321098765432109")
        assertThat(convertedResponse.pix?.copyAndPaste).isEqualTo("**********")
        assertThat(convertedResponse.totalCents).isEqualTo(10010)
        assertThat(convertedResponse.dueDate).isEqualTo(LocalDate.parse("2023-10-10"))
    }

    @Test
    fun `toOurNumberString should convert Int to String with leading zeros`() {
        val ourNumber = 123
        val expected = "********"
        val result = ourNumber.toOurNumberString()
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `shouldIgnoreInterestAndFineIfFirstPayment should return true if first payment`() {
        val firstPayment = paymentRequestInput.copy(isFirstPayment = true)
        val expected = firstPayment.shouldIgnoreInterestAndFineIfFirstPayment()
        assertThat(expected).isTrue
    }

    @Test
    fun `shouldIgnoreInterestAndFineIfFirstPayment should return false if isn't first payment`() {
        val firstPayment = paymentRequestInput.copy(isFirstPayment = false)
        val expected = firstPayment.shouldIgnoreInterestAndFineIfFirstPayment()
        assertThat(expected).isFalse
    }

}
