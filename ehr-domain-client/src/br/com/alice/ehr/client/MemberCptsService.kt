package br.com.alice.ehr.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.GracePeriodType
import br.com.alice.data.layer.models.Person
import br.com.alice.ehr.model.MemberCpt
import com.github.kittinunf.result.Result

@RemoteService
interface MemberCptsService : Service {

    override val namespace get() = "ehr"
    override val serviceName get() = "cpts"

    suspend fun buildPersonCpts(person: Person): Result<MemberCpt, Throwable>

    suspend fun buildPersonCptsByPersonId(personId: PersonId): Result<MemberCpt, Throwable>

}

data class GracePeriods(
    val surgeryInDays: Long = 180,
    val therapyInDays: Long = 60,
    val pacInDays: Long = 180,
    val cptInDays: Long = 730,
    val birthInDays: Long = 300,
    val emergencyInDays: Long = 1,
    val especialExamsInDays: Long = 60,
    val hospitalizationDueToPersonalAccidentInDays: Long = 0,
    val hospitalizationInDays: Long = 0,
    val others: Long? = null
) {

    fun getCalculatedPeriodsDiscount(type: GracePeriodType?): GracePeriods =
        when (type) {
            null -> this
            GracePeriodType.TOTAL_GRACE_PERIOD -> this
            GracePeriodType.TOTAL_GRACE_PERIOD_202405 -> hospitalizationDueToPersonalAccidentInDays()
                .setHospitalizationInDays(90)

            GracePeriodType.PARTIAL_GRACE_PERIOD -> setSurgeryInDays(90)
                .setTherapyInDays(30)
                .setPacInDays(120)

            GracePeriodType.PARTIAL_GRACE_PERIOD_202405 -> getCalculatedPeriodsDiscount(GracePeriodType.PARTIAL_GRACE_PERIOD)
                .hospitalizationDueToPersonalAccidentInDays()
                .setHospitalizationInDays(60)

            GracePeriodType.GRACE_PERIOD_EXEMPTION -> setSurgeryInDays(0)
                .setTherapyInDays(0)
                .setPacInDays(0)
                .emptyEmergency()
                .emptyEspecialExams()
                .emptyOthers()

            GracePeriodType.TOTAL_EXEMPTION -> setSurgeryInDays(0)
                .setTherapyInDays(0)
                .setPacInDays(0)
                .emptyCpt()
                .emptyBirth()
                .emptyEmergency()
                .emptyEspecialExams()
                .emptyOthers()
        }

    private fun hospitalizationDueToPersonalAccidentInDays() =
        copy(hospitalizationDueToPersonalAccidentInDays = 1)

    private fun setHospitalizationInDays(hospitalizationInDays: Long) =
        copy(hospitalizationInDays = hospitalizationInDays)

    private fun setSurgeryInDays(surgeryInDays: Long) =
        copy(surgeryInDays = surgeryInDays)

    private fun setTherapyInDays(therapyInDays: Long) =
        copy(therapyInDays = therapyInDays)

    private fun setPacInDays(pacInDays: Long) =
        copy(pacInDays = pacInDays)

    private fun emptyCpt() =
        copy(cptInDays = 0)

    private fun emptyBirth() =
        copy(birthInDays = 0)

    private fun emptyEmergency() =
        copy(emergencyInDays = 0)

    private fun emptyEspecialExams() =
        copy(especialExamsInDays = 0)

    private fun emptyOthers() = copy(others = 0)
}
